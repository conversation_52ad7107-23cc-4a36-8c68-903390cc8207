"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[158],{5623:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},19946:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(12115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:d=2,absoluteStrokeWidth:l,className:s="",children:u,iconNode:c,...h}=e;return(0,n.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:r,strokeWidth:l?24*Number(d)/Number(o):d,className:a("lucide",s),...h},[...c.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),l=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:l,...s}=r;return(0,n.createElement)(d,{ref:i,iconNode:t,className:a("lucide-".concat(o(e)),l),...s})});return r.displayName="".concat(e),r}},20547:(e,t,r)=>{r.d(t,{UC:()=>U,ZL:()=>B,bL:()=>I,l9:()=>q});var n=r(12115),o=r(85185),a=r(6101),i=r(46081),d=r(19178),l=r(92293),s=r(25519),u=r(61285),c=r(35152),h=r(34378),p=r(28905),f=r(63655),m=r(99708),v=r(5845),g=r(38168),y=r(93795),w=r(95155),b="Popover",[k,x]=(0,i.A)(b,[c.Bk]),P=(0,c.Bk)(),[C,A]=k(b),M=e=>{let{__scopePopover:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:d=!1}=e,l=P(t),s=n.useRef(null),[h,p]=n.useState(!1),[f=!1,m]=(0,v.i)({prop:o,defaultProp:a,onChange:i});return(0,w.jsx)(c.bL,{...l,children:(0,w.jsx)(C,{scope:t,contentId:(0,u.B)(),triggerRef:s,open:f,onOpenChange:m,onOpenToggle:n.useCallback(()=>m(e=>!e),[m]),hasCustomAnchor:h,onCustomAnchorAdd:n.useCallback(()=>p(!0),[]),onCustomAnchorRemove:n.useCallback(()=>p(!1),[]),modal:d,children:r})})};M.displayName=b;var W="PopoverAnchor";n.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,a=A(W,r),i=P(r),{onCustomAnchorAdd:d,onCustomAnchorRemove:l}=a;return n.useEffect(()=>(d(),()=>l()),[d,l]),(0,w.jsx)(c.Mz,{...i,...o,ref:t})}).displayName=W;var j="PopoverTrigger",R=n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,i=A(j,r),d=P(r),l=(0,a.s)(t,i.triggerRef),s=(0,w.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":L(i.open),...n,ref:l,onClick:(0,o.m)(e.onClick,i.onOpenToggle)});return i.hasCustomAnchor?s:(0,w.jsx)(c.Mz,{asChild:!0,...d,children:s})});R.displayName=j;var E="PopoverPortal",[N,O]=k(E,{forceMount:void 0}),D=e=>{let{__scopePopover:t,forceMount:r,children:n,container:o}=e,a=A(E,t);return(0,w.jsx)(N,{scope:t,forceMount:r,children:(0,w.jsx)(p.C,{present:r||a.open,children:(0,w.jsx)(h.Z,{asChild:!0,container:o,children:n})})})};D.displayName=E;var z="PopoverContent",F=n.forwardRef((e,t)=>{let r=O(z,e.__scopePopover),{forceMount:n=r.forceMount,...o}=e,a=A(z,e.__scopePopover);return(0,w.jsx)(p.C,{present:n||a.open,children:a.modal?(0,w.jsx)(_,{...o,ref:t}):(0,w.jsx)(S,{...o,ref:t})})});F.displayName=z;var _=n.forwardRef((e,t)=>{let r=A(z,e.__scopePopover),i=n.useRef(null),d=(0,a.s)(t,i),l=n.useRef(!1);return n.useEffect(()=>{let e=i.current;if(e)return(0,g.Eq)(e)},[]),(0,w.jsx)(y.A,{as:m.DX,allowPinchZoom:!0,children:(0,w.jsx)(X,{...e,ref:d,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),l.current||null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;l.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),S=n.forwardRef((e,t)=>{let r=A(z,e.__scopePopover),o=n.useRef(!1),a=n.useRef(!1);return(0,w.jsx)(X,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(i=r.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,i;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let d=t.target;(null===(i=r.triggerRef.current)||void 0===i?void 0:i.contains(d))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),X=n.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:i,onEscapeKeyDown:u,onPointerDownOutside:h,onFocusOutside:p,onInteractOutside:f,...m}=e,v=A(z,r),g=P(r);return(0,l.Oh)(),(0,w.jsx)(s.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,w.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:f,onEscapeKeyDown:u,onPointerDownOutside:h,onFocusOutside:p,onDismiss:()=>v.onOpenChange(!1),children:(0,w.jsx)(c.UC,{"data-state":L(v.open),role:"dialog",id:v.contentId,...g,...m,ref:t,style:{...m.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),T="PopoverClose";function L(e){return e?"open":"closed"}n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,a=A(T,r);return(0,w.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})}).displayName=T,n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=P(r);return(0,w.jsx)(c.i3,{...o,...n,ref:t})}).displayName="PopoverArrow";var I=M,q=R,B=D,U=F},24122:(e,t,r)=>{r.d(t,{g:()=>h});let n={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}};var o=r(67356);let a={date:(0,o.k)({formats:{full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},defaultWidth:"full"}),time:(0,o.k)({formats:{full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},defaultWidth:"full"}),dateTime:(0,o.k)({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};var i=r(34548);function d(e,t,r){var n,o,a;let d="eeee p";return(n=e,o=t,a=r,+(0,i.k)(n,a)==+(0,i.k)(o,a))?d:e.getTime()>t.getTime()?"'下个'"+d:"'上个'"+d}let l={lastWeek:d,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:d,other:"PP p"};var s=r(58698);let u={ordinalNumber:(e,t)=>{let r=Number(e);switch(null==t?void 0:t.unit){case"date":return r.toString()+"日";case"hour":return r.toString()+"时";case"minute":return r.toString()+"分";case"second":return r.toString()+"秒";default:return"第 "+r.toString()}},era:(0,s.o)({values:{narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},defaultWidth:"wide"}),quarter:(0,s.o)({values:{narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,s.o)({values:{narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},defaultWidth:"wide"}),day:(0,s.o)({values:{narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},defaultWidth:"wide"}),dayPeriod:(0,s.o)({values:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultWidth:"wide",formattingValues:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultFormattingWidth:"wide"})};var c=r(44008);let h={code:"zh-CN",formatDistance:(e,t,r)=>{let o;let a=n[e];return(o="string"==typeof a?a:1===t?a.one:a.other.replace("{{count}}",String(t)),null==r?void 0:r.addSuffix)?r.comparison&&r.comparison>0?o+"内":o+"前":o},formatLong:a,formatRelative:(e,t,r,n)=>{let o=l[e];return"function"==typeof o?o(t,r,n):o},localize:u,match:{ordinalNumber:(0,r(40972).K)({matchPattern:/^(第\s*)?\d+(日|时|分|秒)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,c.A)({matchPatterns:{narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(前)/i,/^(公元)/i]},defaultParseWidth:"any"}),quarter:(0,c.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,c.A)({matchPatterns:{narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},defaultParseWidth:"any"}),day:(0,c.A)({matchPatterns:{narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},defaultParseWidth:"any"}),dayPeriod:(0,c.A)({matchPatterns:{any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}}},24357:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},47863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},49103:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},66474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},69074:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}}]);