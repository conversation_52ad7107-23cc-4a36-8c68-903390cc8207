(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8499],{2361:(s,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>r});var l=a(95155),c=a(68856);function r(){return(0,l.jsxs)("div",{className:"p-4 space-y-6",children:[(0,l.jsx)("div",{className:"flex space-x-4 border-b pb-2 mb-4",children:[,,].fill(0).map((s,e)=>(0,l.jsx)(c.E,{className:"h-10 w-24"},e))}),(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(c.<PERSON>,{className:"h-10 w-40"}),(0,l.jsx)(c.E,{className:"h-10 w-40"}),(0,l.jsx)(c.E,{className:"h-10 w-24"})]}),(0,l.jsx)("div",{className:"flex space-x-2",children:(0,l.jsx)(c.E,{className:"h-10 w-28"})})]}),(0,l.jsxs)("div",{className:"border rounded-md",children:[(0,l.jsx)("div",{className:"flex border-b p-3 bg-muted/30",children:[,,,,,].fill(0).map((s,e)=>(0,l.jsx)(c.E,{className:"h-6 flex-1 mx-2"},e))}),Array(6).fill(0).map((s,e)=>(0,l.jsx)("div",{className:"flex border-b p-3",children:[,,,,,].fill(0).map((s,e)=>(0,l.jsx)(c.E,{className:"h-6 flex-1 mx-2"},e))},e))]}),(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)(c.E,{className:"h-8 w-40"}),(0,l.jsx)(c.E,{className:"h-8 w-64"})]})]})}},20501:(s,e,a)=>{Promise.resolve().then(a.bind(a,2361))},59434:(s,e,a)=>{"use strict";a.d(e,{cn:()=>r});var l=a(52596),c=a(39688);function r(){for(var s=arguments.length,e=Array(s),a=0;a<s;a++)e[a]=arguments[a];return(0,c.QP)((0,l.$)(e))}},68856:(s,e,a)=>{"use strict";a.d(e,{E:()=>r});var l=a(95155),c=a(59434);function r(s){let{className:e,...a}=s;return(0,l.jsx)("div",{className:(0,c.cn)("animate-pulse rounded-md bg-muted",e),...a})}}},s=>{var e=e=>s(s.s=e);s.O(0,[4277,6315,7358],()=>e(20501)),_N_E=s.O()}]);