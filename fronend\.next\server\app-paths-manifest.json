{"/_not-found/page": "app/_not-found/page.js", "/api/salary/route": "app/api/salary/route.js", "/1/page": "app/1/page.js", "/features/attendance-management/page": "app/features/attendance-management/page.js", "/features/course-scheduling/page": "app/features/course-scheduling/page.js", "/features/financial-management/page": "app/features/financial-management/page.js", "/features/grade-management/page": "app/features/grade-management/page.js", "/features/student-management/page": "app/features/student-management/page.js", "/features/organization-management/page": "app/features/organization-management/page.js", "/features/page": "app/features/page.js", "/group/1/page": "app/group/1/page.js", "/group/2/page": "app/group/2/page.js", "/group/page": "app/group/page.js", "/group/3/page": "app/group/3/page.js", "/page": "app/page.js", "/login/page": "app/login/page.js", "/academic/attendance/page": "app/academic/attendance/page.js", "/academic/classes/page": "app/academic/classes/page.js", "/academic/classes/[classesId]/page": "app/academic/classes/[classesId]/page.js", "/academic/classes/new/page": "app/academic/classes/new/page.js", "/academic/products/page": "app/academic/products/page.js", "/academic/schedule/page": "app/academic/schedule/page.js", "/academic/courses/page": "app/academic/courses/page.js", "/academic/schedule/new/page": "app/academic/schedule/new/page.js", "/academic/schedule/[classesScheduleId]/page": "app/academic/schedule/[classesScheduleId]/page.js", "/data/education/page": "app/data/education/page.js", "/academic/students/page": "app/academic/students/page.js", "/enrollment/followUp/page": "app/enrollment/followUp/page.js", "/data/sales/page": "app/data/sales/page.js", "/dashboard/page": "app/dashboard/page.js", "/aiTools/page": "app/aiTools/page.js", "/notifications/[notificationsId]/page": "app/notifications/[notificationsId]/page.js", "/notifications/list/page": "app/notifications/list/page.js", "/notifications/create/page": "app/notifications/create/page.js", "/finance/cashier/page": "app/finance/cashier/page.js", "/finance/receipt/page": "app/finance/receipt/page.js", "/finance/wages/page": "app/finance/wages/page.js", "/organization/info/page": "app/organization/info/page.js", "/organization/classrooms/page": "app/organization/classrooms/page.js", "/organization/logs/page": "app/organization/logs/page.js", "/organization/staff/page": "app/organization/staff/page.js"}