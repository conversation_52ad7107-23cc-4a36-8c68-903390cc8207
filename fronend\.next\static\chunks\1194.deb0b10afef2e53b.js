"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1194],{1194:(e,t,d)=>{d.r(t),d.d(t,{default:()=>o});var a=d(95155),n=d(48436);let h=(0,d(19946).A)("ClipboardList",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]]);var s=d(12115),r=d(55028),u=d(57001),c=d(95728);let l=(0,r.default)(()=>d.e(4521).then(d.bind(d,86902)),{loadableGenerated:{webpack:()=>[86902]},ssr:!1}),o=function(e){let{studentProduct:t}=e,[d]=(0,c.Mk)(),[r,o]=(0,s.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.p,{icon:h,tooltipText:"调整产品",onClick:()=>o(!0)}),r&&(0,a.jsx)(l,{open:r,onOpenChange:o,studentProduct:t,handleAdjust:e=>{d({studentId:t.studentId,studentProductId:t.id,data:e}).then(e=>{n.l.success("调整成功.")}).catch(e=>{console.error("调整失败",e)}),o(!1)}})]})}}}]);