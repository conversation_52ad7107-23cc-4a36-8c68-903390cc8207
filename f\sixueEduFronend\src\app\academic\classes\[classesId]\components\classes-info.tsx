'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import ClassInfoEditDialog from './dialog/class-info-edit-dialog';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface ClassInfoProps {
  classData: any;
}

const ClassInfo: React.FC<ClassInfoProps> = ({ classData }) => {
  // console.log('classData:', classData);

  if (!classData) {
    return <>loading...</>;
  }

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editData, setEditData] = useState({});
  const [isSettingsExpanded, setIsSettingsExpanded] = useState(false);

  useEffect(() => {
    if (classData) {
      setEditData({
        name: classData.name,
        course: '',
        capacity: '1222',
        classroom: '122',
        teacher: '122'
      })
    }


  }, [classData]);
  const getStatusVariant = (status: string) => {
    switch (status) {
      case ' active':
        return 'secondary';
      case '已结束':
        return 'destructive';
      default:
        return 'default';
    }
  };

  const handleEditSubmit = () => {
    // 处理表单提交
    console.log('提交的数据:', editData);
    setIsEditDialogOpen(false);
  };

  const toggleSettings = () => {
    setIsSettingsExpanded(!isSettingsExpanded);
  };

  return (
    <>
      <div className="w-full">
        <CardHeader className="flex flex-row items-center justify-between p-2">
          <CardTitle className="text-lg font-semibold">班级信息</CardTitle>
          <div className="flex space-x-1">
            <Button variant="default" size="sm" onClick={() => setIsEditDialogOpen(true)}>编辑信息</Button>
            <Button variant="outline" size="sm">导出数据</Button>
          </div>
        </CardHeader>
        <CardContent className="p-2 pt-0">
          <div className="grid grid-cols-1 gap-2">
            <div className="grid grid-cols-3 ">
              <div className="flex flex-col">
                <p className="text-sm text-muted-foreground">班级名称</p>
                <div className="flex items-center space-x-1">
                  <div className="font-medium">{classData.name}</div>
                  <Badge variant={getStatusVariant(classData.status)} className={`${classData.status === 'active' ? 'bg-green-100 text-green-800 hover:bg-green-200' : ''} font-normal`}>
                    {classData.status === 'active' ? '进行中' : '已结束'}
                  </Badge>
                </div>
              </div>
              <div className="flex flex-col">
                <p className="text-sm text-muted-foreground">班级编号</p>
                <p className="font-medium">{classData.id}</p>
              </div>
              <div className="flex flex-col">
                <p className="text-sm text-muted-foreground">课程包</p>
                <div className="flex items-center space-x-1">
                  <Badge variant="secondary" className="font-normal">{classData.course.name}</Badge>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-3">
              <div className="flex flex-col">
                <p className="text-sm text-muted-foreground">开课时间</p>
                <p className="font-medium">{new Date(classData.startDate).toLocaleDateString()}</p>
              </div>
              <div className="flex flex-col">
                <p className="text-sm text-muted-foreground">主讲教师</p>
                <p className="font-medium">{classData.teacher.name}</p>
              </div>
            </div>

            <div className="grid grid-cols-1">
              <div className="flex flex-col">
                <p className="text-sm text-muted-foreground">备注</p>
                <p className="font-medium">{classData.remarks}</p>
              </div>
            </div>

            <div className="border-t pt-2 mt-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleSettings}
                className="flex items-center justify-center w-full text-sm text-muted-foreground"
              >
                班级设置 {isSettingsExpanded ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />}
              </Button>

              {isSettingsExpanded && (
                <div className="mt-2 space-y-2 bg-muted/30 p-2 rounded-md">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex flex-col p-2 bg-card rounded-md">
                      <div className="flex justify-between items-center">
                        <p className="text-sm font-medium">开放预约</p>
                        {classData.isReserve ? (
                          <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200">已开启</Badge>
                        ) : (
                          <Badge variant="secondary" className="opacity-70">未开启</Badge>
                        )}
                      </div>
                      {classData.isReserve && (
                        <div className="text-sm mt-2 space-y-1 text-muted-foreground">
                          <div className="flex items-center">
                            <span className="w-16 inline-block">开始时间:</span>
                            <span className="font-medium text-foreground">{classData.appointmentStartTime || '未设置'}</span>
                          </div>
                          <div className="flex items-center">
                            <span className="w-16 inline-block">截止时间:</span>
                            <span className="font-medium text-foreground">{classData.appointmentEndTime || '未设置'}</span>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col p-2 bg-card rounded-md">
                      <div className="flex justify-between items-center">
                        <p className="text-sm font-medium">学员扫码考勤</p>
                        {classData.isQRCodeAttendance ? (
                          <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200">已开启</Badge>
                        ) : (
                          <Badge variant="secondary" className="opacity-70">未开启</Badge>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col p-2 bg-card rounded-md ">
                      <div className="flex justify-between items-center">
                        <p className="text-sm font-medium">系统自动考勤</p>
                        {classData.isAutoCheckIn ? (
                          <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200">已开启</Badge>
                        ) : (
                          <Badge variant="secondary" className="opacity-70">未开启</Badge>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col p-2 bg-card rounded-md ">
                      <div className="flex justify-between items-center">
                        <p className="text-sm font-medium">开放请假</p>
                        {classData.isOnLeave ? (
                          <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200">已开启</Badge>
                        ) : (
                          <Badge variant="secondary" className="opacity-70">未开启</Badge>
                        )}
                      </div>
                      {classData.isOnLeave && (
                        <div className="text-sm mt-2 text-muted-foreground">
                          <div className="flex items-center">
                            <span className="w-16 inline-block">截止时间:</span>
                            <span className="font-medium text-foreground">{classData.leaveDeadline || '未设置'}</span>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col p-2 bg-card rounded-md ">
                      <div className="flex justify-between items-center">
                        <p className="text-sm font-medium">显示周期数</p>
                        {classData.isShowWeekCount ? (
                          <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200">已开启</Badge>
                        ) : (
                          <Badge variant="secondary" className="opacity-70">未开启</Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </div>
      <ClassInfoEditDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        classes={classData}
      />
    </>
  );
};

export default ClassInfo;