(()=>{var e={};e.id=9719,e.ids=[9719],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19659:(e,s,t)=>{Promise.resolve().then(t.bind(t,87289))},21820:e=>{"use strict";e.exports=require("os")},25648:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687),a=t(43210);let l=(0,t(62688).A)("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]]);function i({stats:e,audioUrl:s}){let t=(0,a.useRef)(null),[i,n]=(0,a.useState)(!1);return(0,r.jsxs)("div",{className:"flex items-center bg-gradient-to-r from-blue-50 to-pink-50 px-3 py-1.5 text-gray-800 rounded-xl shadow-md sticky top-0 left-0 w-full z-30 min-h-[44px] overflow-hidden",children:[(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute -right-8 -top-8 w-24 h-24 bg-pink-200 rounded-full opacity-20"}),(0,r.jsx)("div",{className:"absolute -left-4 -bottom-8 w-16 h-16 bg-blue-200 rounded-full opacity-20"})]}),(0,r.jsx)("button",{type:"button",className:`relative mr-3 flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 active:from-blue-500 active:to-purple-500 transition shadow-md z-10 ${i?"animate-spin-slow":""}`,onClick:()=>{t.current&&(i?t.current.pause():t.current.play())},"aria-label":"播放/暂停音频",children:(0,r.jsx)(l,{size:18,className:"text-white"})}),(0,r.jsx)("audio",{ref:t,src:s,onPlay:()=>n(!0),onPause:()=>n(!1),onEnded:()=>n(!1),className:"hidden"}),(0,r.jsx)("div",{className:"flex flex-1 justify-between space-x-3 relative z-10",children:e.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-pink-500 font-bold text-lg",children:e.value}),(0,r.jsx)("span",{className:"text-gray-600 text-sm ml-0.5",children:e.label})]},s))}),(0,r.jsx)("style",{children:`
          .animate-spin-slow {
            animation: spin 3s linear infinite;
          }
          @keyframes spin {
            100% { transform: rotate(360deg); }
          }
        `})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29099:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["group",{children:["2",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,68837)),"F:\\trae\\cardmees\\fronend\\src\\app\\group\\2\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["F:\\trae\\cardmees\\fronend\\src\\app\\group\\2\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/group/2/page",pathname:"/group/2",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30468:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687);t(43210);var a=t(30474),l=t(11860);function i({open:e,onClose:s,qrCode:t,shareTitle:i,shareDesc:n}){return e?(0,r.jsx)("div",{className:"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl w-full max-w-sm relative",children:[(0,r.jsx)("button",{onClick:s,className:"absolute top-2 right-2 text-gray-400 hover:text-gray-600 p-1",children:(0,r.jsx)(l.A,{size:24})}),(0,r.jsxs)("div",{className:"pt-6 pb-4 px-6 text-center border-b border-gray-100",children:[(0,r.jsx)("h3",{className:"text-xl font-bold",children:"分享给好友"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"邀请好友一起参与"})]}),(0,r.jsx)("div",{className:"grid grid-cols-4 gap-2 p-6",children:[{icon:"/icons/wechat.svg",name:"微信",color:"bg-green-500"},{icon:"/icons/moments.svg",name:"朋友圈",color:"bg-green-600"},{icon:"/icons/qq.svg",name:"QQ",color:"bg-blue-400"},{icon:"/icons/weibo.svg",name:"微博",color:"bg-red-500"}].map(e=>(0,r.jsxs)("button",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:`w-12 h-12 ${e.color} rounded-full flex items-center justify-center mb-1 text-white`,children:e.icon?(0,r.jsx)(a.default,{src:e.icon,alt:e.name,width:24,height:24}):e.name.charAt(0)}),(0,r.jsx)("span",{className:"text-xs",children:e.name})]},e.name))}),t&&(0,r.jsxs)("div",{className:"px-6 pb-6 flex flex-col items-center",children:[(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg p-2 mb-2",children:(0,r.jsx)("img",{src:t,alt:"二维码",className:"w-40 h-40 object-contain"})}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"扫描二维码参与活动"})]}),(0,r.jsx)("div",{className:"bg-gray-50 p-4 rounded-b-2xl border-t border-gray-100",children:(0,r.jsxs)("div",{className:"bg-white p-3 rounded-lg border border-gray-200",children:[(0,r.jsx)("h4",{className:"font-medium text-sm",children:i}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:n})]})})]})}):null}},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59411:(e,s,t)=>{Promise.resolve().then(t.bind(t,68837))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68837:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\group\\\\2\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\group\\2\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87289:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(60687),a=t(43210),l=t(97351),i=t(25648),n=t(30468);function d({course:e}){return(0,r.jsxs)("div",{className:"mx-4 my-4 bg-white rounded-xl shadow-md overflow-hidden",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-48 object-cover"}),(0,r.jsx)("div",{className:"absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium",children:e.category})]}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("h3",{className:"font-bold text-lg text-gray-800",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[(0,r.jsx)("span",{className:"text-yellow-500",children:"★"}),(0,r.jsx)("span",{className:"text-yellow-500",children:"★"}),(0,r.jsx)("span",{className:"text-yellow-500",children:"★"}),(0,r.jsx)("span",{className:"text-yellow-500",children:"★"}),(0,r.jsx)("span",{className:"text-yellow-500",children:"★"}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 ml-1",children:["(",e.reviewCount,"条评价)"]})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:e.description}),(0,r.jsxs)("div",{className:"mt-3 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("span",{className:"text-red-500 font-bold",children:["\xa5",e.price]}),(0,r.jsxs)("span",{className:"text-xs text-gray-400 line-through ml-1",children:["\xa5",e.originalPrice]})]}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:[e.studentCount,"人已学习"]})]})]})]})}function c({times:e,selectedTime:s,onSelectTime:t}){return(0,r.jsxs)("div",{className:"mx-4 my-4 bg-white rounded-xl shadow-md p-4",children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-3",children:"选择预约时间"}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-3",children:e.map(e=>(0,r.jsxs)("button",{disabled:!e.available,className:`p-3 rounded-lg border text-sm ${s===e.id?"bg-red-500 text-white border-red-500":e.available?"border-gray-200 hover:border-red-500":"bg-gray-100 text-gray-400 cursor-not-allowed"}`,onClick:()=>e.available&&t(e.id),children:[(0,r.jsx)("div",{className:"font-medium",children:e.date}),(0,r.jsx)("div",{className:`text-xs mt-1 ${s===e.id?"text-white":"text-gray-500"}`,children:e.time}),!e.available&&(0,r.jsx)("div",{className:"text-xs mt-1 text-gray-500",children:"已约满"})]},e.id))})]})}function o({teacher:e}){return(0,r.jsxs)("div",{className:"mx-4 my-4 bg-white rounded-xl shadow-md overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex p-4 items-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full overflow-hidden mr-4 border-2 border-gray-100",children:(0,r.jsx)("img",{src:e.avatar,alt:e.name,className:"w-full h-full object-cover"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-lg",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.title}),(0,r.jsx)("div",{className:"flex mt-1",children:e.tags.map((e,s)=>(0,r.jsx)("span",{className:"mr-2 text-xs px-2 py-0.5 bg-gray-100 rounded-full text-gray-500",children:e},s))})]})]}),(0,r.jsx)("div",{className:"px-4 pb-4",children:(0,r.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:e.bio})})]})}function x({open:e,onClose:s,onSubmit:t,selectedTime:l,times:i}){let[n,d]=(0,a.useState)({name:"",phone:"",remarks:""});if(!e)return null;let c=i.find(e=>e.id===l),o=e=>{let{name:s,value:t}=e.target;d(e=>({...e,[s]:t}))};return(0,r.jsx)("div",{className:"fixed inset-0 z-50 bg-black/30 flex items-center justify-center p-4",children:(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(n)},className:"bg-white rounded-2xl w-full max-w-md p-6 shadow-lg",children:[(0,r.jsx)("h2",{className:"text-center text-xl font-bold mb-5",children:"确认预约信息"}),c&&(0,r.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg mb-4",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"预约日期："}),(0,r.jsx)("span",{className:"font-medium",children:c.date})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm mt-1",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"预约时间："}),(0,r.jsx)("span",{className:"font-medium",children:c.time})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm text-gray-600 mb-1",children:"您的姓名"}),(0,r.jsx)("input",{name:"name",value:n.name,onChange:o,required:!0,placeholder:"请输入您的姓名",className:"w-full px-3 py-2.5 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-300"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm text-gray-600 mb-1",children:"联系电话"}),(0,r.jsx)("input",{name:"phone",value:n.phone,onChange:o,required:!0,placeholder:"请输入您的联系电话",className:"w-full px-3 py-2.5 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-300"})]}),(0,r.jsxs)("div",{className:"mb-5",children:[(0,r.jsx)("label",{className:"block text-sm text-gray-600 mb-1",children:"备注信息"}),(0,r.jsx)("textarea",{name:"remarks",value:n.remarks,onChange:o,placeholder:"有什么需要说明的，可以在这里备注",className:"w-full px-3 py-2.5 border border-gray-200 rounded-lg h-24 focus:outline-none focus:ring-2 focus:ring-red-300 resize-none"})]}),(0,r.jsx)("button",{type:"submit",className:"w-full py-3 rounded-full bg-gradient-to-r from-red-500 to-red-600 text-white text-base font-medium border-0 mb-2",children:"确认预约"}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("button",{type:"button",onClick:s,className:"text-gray-400 bg-transparent border-0 text-sm py-1 px-3 hover:text-gray-500",children:"取消"})})]})})}function m({currentPrice:e,originalPrice:s,endTime:t}){let[l,i]=(0,a.useState)({days:0,hours:0,minutes:0,seconds:0});return(0,r.jsx)("div",{className:"mx-3 my-3",children:(0,r.jsx)("div",{className:"bg-gradient-to-r from-red-500 to-orange-500 rounded-lg shadow-md overflow-hidden",children:(0,r.jsx)("div",{className:"p-3",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsx)("div",{children:"0.00"===e?(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-white text-2xl font-bold",children:"免费"}),(0,r.jsxs)("span",{className:"text-white/80 text-xs",children:["原价 \xa5",s]})]}):(0,r.jsxs)("div",{className:"flex items-baseline",children:[(0,r.jsx)("span",{className:"text-white text-sm font-medium",children:"\xa5"}),(0,r.jsx)("span",{className:"text-white text-2xl font-bold mx-1",children:e}),(0,r.jsxs)("span",{className:"text-white/80 text-xs line-through ml-1",children:["\xa5",s]})]})}),(0,r.jsxs)("div",{className:"flex flex-col items-end",children:[(0,r.jsx)("div",{className:"bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium mb-1",children:"限时优惠"}),(0,r.jsxs)("div",{className:"flex items-center text-white text-xs",children:[(0,r.jsx)("span",{className:"mr-1",children:"剩余"}),(0,r.jsx)("div",{className:"bg-black/30 px-1.5 py-0.5 rounded mx-0.5 font-mono",children:String(l.days).padStart(2,"0")}),(0,r.jsx)("span",{className:"mx-0.5",children:":"}),(0,r.jsx)("div",{className:"bg-black/30 px-1.5 py-0.5 rounded mx-0.5 font-mono",children:String(l.hours).padStart(2,"0")}),(0,r.jsx)("span",{className:"mx-0.5",children:":"}),(0,r.jsx)("div",{className:"bg-black/30 px-1.5 py-0.5 rounded mx-0.5 font-mono",children:String(l.minutes).padStart(2,"0")})]})]})]}),"0.00"===e&&(0,r.jsxs)("div",{className:"mt-1 bg-white/20 rounded-lg p-2 text-white text-xs leading-tight",children:[(0,r.jsx)("span",{className:"font-bold",children:"\uD83D\uDCE3 预约提醒："}),(0,r.jsx)("span",{children:"每人限1次免费试听机会，名额有限，预约从速！"})]})]})})})})}function u({title:e,list:s}){return(0,r.jsxs)("section",{className:"mx-4 my-4",children:[(0,r.jsxs)("div",{className:"flex items-center mb-3",children:[(0,r.jsx)("div",{className:"h-6 w-1 bg-red-500 rounded-full mr-2"}),(0,r.jsx)("h2",{className:"text-lg font-bold text-gray-800",children:e})]}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden",children:s.map((s,t)=>"text"===s.type?(0,r.jsx)("div",{className:"p-4",children:s.text?.split("\n").map((s,t)=>{if("预约说明"===e&&s.match(/^\d+\./)){let[e,...a]=s.split(/\.(.+)/);return r.jsxs("div",{className:"flex items-start mb-2",children:[r.jsx("div",{className:"bg-red-500 text-white w-5 h-5 rounded-full flex items-center justify-center text-xs mr-2 mt-0.5",children:e}),r.jsx("p",{className:"text-gray-700 flex-1",children:a.join(".")})]},t)}return r.jsx("p",{className:"text-gray-700 leading-relaxed mb-2",children:s},t)})},t):"img"===s.type?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("img",{src:s.img,alt:"",className:"w-full rounded-none",loading:"lazy"}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent py-8"})]},t):null)})]})}function h(){let[e,s]=(0,a.useState)(!1),[t,h]=(0,a.useState)(null),[p,b]=(0,a.useState)(!1),g={data:{html:{module:[{title:"机构介绍",list:[{text:"我们是一家专注于少儿艺术教育的专业机构，成立于2010年，拥有超过50位经验丰富的专业教师团队。我们致力于为6-18岁的青少年儿童提供高质量的艺术教育，课程涵盖素描、速写、水彩、油画等多个艺术领域。我们的教学场地配备专业设施，为学生提供舒适的学习环境。多年来，我们已培养了上千名学生，其中不少学生在各类艺术竞赛中获得优异成绩。",type:"text"},{img:"https://images.unsplash.com/photo-1577896851231-70ef18881754?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",type:"img"}]},{title:"课程介绍",list:[{text:"我们的速写课程采用全程小班教学，为不同年龄段的孩子提供从基础到进阶的系统化学习。通过观察、分析和表现，培养学生对物体结构、比例和空间关系的认知能力。特训教材由资深教师团队研发，紧贴少儿认知和艺术学习规律，引导学生掌握速写的核心技能。参与我们的课程将有效提升孩子的观察力、表现力和创造力，为艺术学习打下坚实基础。",type:"text"},{img:"https://image.cardmee.net/2/2021/05/image/1621906385171587078.png",type:"img"}]},{title:"预约说明",list:[{text:"1. 选择您方便的时间段进行预约\n2. 填写个人信息，提交预约申请\n3. 预约成功后，我们会短信通知确认\n4. 如需更改或取消预约，请提前24小时联系客服\n5. 试听课程请准时到达，迟到超过15分钟视为自动放弃\n6. 每人限预约1次免费试听课程",type:"text"}]}],audioUrl:"https://image.cardmee.net/540/2020/04/voice/0d3fb888-f41a-4021-8e2d-22a93b3150e5.mp3"},options:{cover:"https://images.unsplash.com/photo-1602934585418-f588a6c595ca?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",name:"少儿速写课程免费试听",shareDesc:"预约免费试听课，专业教师1对1指导",shareTitle:"少儿速写课程免费试听预约",buttons:[{text:"立即预约",type:"book",enabled:!0}],stats:[{value:283,label:"人查看"},{value:125,label:"人分享"},{value:68,label:"人预约"}],prices:{currentPrice:"19.99",originalPrice:"198.00",endTime:new Date().getTime()+6048e5},courses:[{name:"少儿速写基础入门课程",category:"美术类",description:"专为6-12岁儿童设计的速写入门课程，通过趣味教学激发孩子的艺术天赋。",image:"https://images.unsplash.com/photo-1543859147-380b036834c5?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",price:"0.00",originalPrice:"198.00",reviewCount:128,studentCount:1240}],teachers:[],availableTimes:[{id:"t1",date:"周六 (10月21日)",time:"10:00-11:30",available:!0},{id:"t2",date:"周六 (10月21日)",time:"14:00-15:30",available:!0},{id:"t3",date:"周日 (10月22日)",time:"10:00-11:30",available:!0},{id:"t4",date:"周日 (10月22日)",time:"14:00-15:30",available:!1},{id:"t5",date:"周六 (10月28日)",time:"10:00-11:30",available:!0},{id:"t6",date:"周六 (10月28日)",time:"14:00-15:30",available:!0}]}}},f=g.data.html,j=g.data.options,v=j.buttons||[];return(0,r.jsxs)("div",{className:"bg-gray-50 min-h-screen font-sans pb-20",children:[(0,r.jsx)("div",{className:"sticky top-0 z-50",children:(0,r.jsx)(i.A,{stats:j.stats,audioUrl:f.audioUrl})}),(0,r.jsx)("img",{src:j.cover,alt:"banner",className:"w-full h-48 object-cover"}),(0,r.jsxs)("div",{className:"flex items-start justify-between mt-2.5 px-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl text-red-500 font-bold",children:j.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-2.5",children:j.shareDesc})]}),(0,r.jsxs)("button",{onClick:()=>{s(!0)},className:"flex flex-col items-center text-gray-500 pt-1",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"}),(0,r.jsx)("polyline",{points:"16 6 12 2 8 6"}),(0,r.jsx)("line",{x1:"12",y1:"2",x2:"12",y2:"15"})]})}),(0,r.jsx)("span",{className:"text-xs mt-1",children:"分享"})]})]}),j.courses.map((e,s)=>(0,r.jsx)(d,{course:e},s)),(0,r.jsx)(c,{times:j.availableTimes,selectedTime:t,onSelectTime:e=>{h(e)}}),j.teachers&&j.teachers.length>0&&j.teachers.map((e,s)=>(0,r.jsx)(o,{teacher:e},s)),(0,r.jsx)(m,{currentPrice:j.prices.currentPrice,originalPrice:j.prices.originalPrice,endTime:j.prices.endTime}),f.module.map((e,s)=>(0,r.jsx)(u,{title:e.title,list:e.list},s)),(0,r.jsx)("div",{className:"fixed inset-x-0 bottom-0 bg-white shadow-lg flex justify-around py-2.5",children:(0,r.jsx)(l.A,{buttons:v,onAction:e=>{"book"===e&&(t?b(!0):alert("请先选择预约时间"))}})}),e&&(0,r.jsx)(n.A,{open:e,onClose:()=>s(!1),shareTitle:j.shareTitle,shareDesc:j.shareDesc,qrCode:`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(window.location.href)}`}),p&&(0,r.jsx)(x,{open:p,onClose:()=>b(!1),onSubmit:e=>{console.log("预约数据:",{...e,timeId:t}),alert("预约成功！我们会尽快与您联系确认。"),b(!1)},selectedTime:t,times:j.availableTimes})]})}},94735:e=>{"use strict";e.exports=require("events")},97351:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(60687);function a({buttons:e,onAction:s}){return(0,r.jsx)("div",{className:"flex justify-around my-4",children:e.map(e=>(0,r.jsx)("button",{disabled:!e.enabled,className:`bg-red-500 text-white rounded-full px-8 py-2.5 text-lg font-medium shadow-md transition-all hover:bg-red-600 active:bg-red-700 ${e.enabled?"":"opacity-50 cursor-not-allowed"}`,onClick:()=>e.enabled&&s(e.type),children:e.text},e.type))})}t(43210)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,7392,7125,3019],()=>t(29099));module.exports=r})();