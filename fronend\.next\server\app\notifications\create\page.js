(()=>{var e={};e.id=3130,e.ids=[3130],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13828:(e,r,t)=>{Promise.resolve().then(t.bind(t,31469))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21337:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d});var n=t(65239),o=t(48088),i=t(88170),l=t.n(i),s=t(30893),a={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>s[e]);t.d(r,a);let d={children:["",{children:["notifications",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,61850)),"F:\\trae\\cardmees\\fronend\\src\\app\\notifications\\create\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94764)),"F:\\trae\\cardmees\\fronend\\src\\app\\notifications\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["F:\\trae\\cardmees\\fronend\\src\\app\\notifications\\create\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/notifications/create/page",pathname:"/notifications/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31469:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>em});var n=t(60687),o=t(43210),i=t(29523),l=t(44493),s=t(89667),a=t(34729),d=t(69918),c=t(80013),u=t(56896);function f(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function p(...e){return o.useCallback(function(...e){return r=>{let t=!1,n=e.map(e=>{let n=f(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():f(e[r],null)}}}}(...e),e)}t(51215);var m=Symbol("radix.slottable");function h(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===m}var v=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=function(e){let r=function(e){let r=o.forwardRef((e,r)=>{var t;let n,i;let{children:l,...s}=e,a=p(o.isValidElement(l)?(t=l,(i=(n=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?t.ref:(i=(n=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?t.props.ref:t.props.ref||t.ref):void 0,r);if(o.isValidElement(l)){let e=function(e,r){let t={...r};for(let n in r){let o=e[n],i=r[n];/^on[A-Z]/.test(n)?o&&i?t[n]=(...e)=>{let r=i(...e);return o(...e),r}:o&&(t[n]=o):"style"===n?t[n]={...o,...i}:"className"===n&&(t[n]=[o,i].filter(Boolean).join(" "))}return{...e,...t}}(s,l.props);return l.type!==o.Fragment&&(e.ref=a),o.cloneElement(l,e)}return o.Children.count(l)>1?o.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=o.forwardRef((e,t)=>{let{children:i,...l}=e,s=o.Children.toArray(i),a=s.find(h);if(a){let e=a.props.children,i=s.map(r=>r!==a?r:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,n.jsx)(r,{...l,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,i):null})}return(0,n.jsx)(r,{...l,ref:t,children:i})});return t.displayName=`${e}.Slot`,t}(`Primitive.${r}`),i=o.forwardRef((e,o)=>{let{asChild:i,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(i?t:r,{...l,ref:o})});return i.displayName=`Primitive.${r}`,{...e,[r]:i}},{}),x=globalThis?.document?o.useLayoutEffect:()=>{},b=e=>{let{present:r,children:t}=e,n=function(e){var r,t;let[n,i]=o.useState(),l=o.useRef(null),s=o.useRef(e),a=o.useRef("none"),[d,c]=(r=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,r)=>t[e][r]??e,r));return o.useEffect(()=>{let e=w(l.current);a.current="mounted"===d?e:"none"},[d]),x(()=>{let r=l.current,t=s.current;if(t!==e){let n=a.current,o=w(r);e?c("MOUNT"):"none"===o||r?.display==="none"?c("UNMOUNT"):t&&n!==o?c("ANIMATION_OUT"):c("UNMOUNT"),s.current=e}},[e,c]),x(()=>{if(n){let e;let r=n.ownerDocument.defaultView??window,t=t=>{let o=w(l.current).includes(t.animationName);if(t.target===n&&o&&(c("ANIMATION_END"),!s.current)){let t=n.style.animationFillMode;n.style.animationFillMode="forwards",e=r.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=t)})}},o=e=>{e.target===n&&(a.current=w(l.current))};return n.addEventListener("animationstart",o),n.addEventListener("animationcancel",t),n.addEventListener("animationend",t),()=>{r.clearTimeout(e),n.removeEventListener("animationstart",o),n.removeEventListener("animationcancel",t),n.removeEventListener("animationend",t)}}c("ANIMATION_END")},[n,c]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:o.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(r),i="function"==typeof t?t({present:n.isPresent}):o.Children.only(t),l=p(n.ref,function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof t||n.isPresent?o.cloneElement(i,{ref:l}):null};function w(e){return e?.animationName||"none"}function g(e){let r=o.useRef(e);return o.useEffect(()=>{r.current=e}),o.useMemo(()=>(...e)=>r.current?.(...e),[])}b.displayName="Presence";var y=o.createContext(void 0);function j(e,r,{checkForDefaultPrevented:t=!0}={}){return function(n){if(e?.(n),!1===t||!n.defaultPrevented)return r?.(n)}}var N="ScrollArea",[C,R]=function(e,r=[]){let t=[],i=()=>{let r=t.map(e=>o.createContext(e));return function(t){let n=t?.[e]||r;return o.useMemo(()=>({[`__scope${e}`]:{...t,[e]:n}}),[t,n])}};return i.scopeName=e,[function(r,i){let l=o.createContext(i),s=t.length;t=[...t,i];let a=r=>{let{scope:t,children:i,...a}=r,d=t?.[e]?.[s]||l,c=o.useMemo(()=>a,Object.values(a));return(0,n.jsx)(d.Provider,{value:c,children:i})};return a.displayName=r+"Provider",[a,function(t,n){let a=n?.[e]?.[s]||l,d=o.useContext(a);if(d)return d;if(void 0!==i)return i;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=t.reduce((r,{useScope:t,scopeName:n})=>{let o=t(e)[`__scope${n}`];return{...r,...o}},{});return o.useMemo(()=>({[`__scope${r.scopeName}`]:n}),[n])}};return t.scopeName=r.scopeName,t}(i,...r)]}(N),[E,S]=C(N),P=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:i="hover",dir:l,scrollHideDelay:s=600,...a}=e,[d,c]=o.useState(null),[u,f]=o.useState(null),[m,h]=o.useState(null),[x,b]=o.useState(null),[w,g]=o.useState(null),[j,N]=o.useState(0),[C,R]=o.useState(0),[S,P]=o.useState(!1),[T,k]=o.useState(!1),L=p(r,e=>c(e)),_=function(e){let r=o.useContext(y);return e||r||"ltr"}(l);return(0,n.jsx)(E,{scope:t,type:i,dir:_,scrollHideDelay:s,scrollArea:d,viewport:u,onViewportChange:f,content:m,onContentChange:h,scrollbarX:x,onScrollbarXChange:b,scrollbarXEnabled:S,onScrollbarXEnabledChange:P,scrollbarY:w,onScrollbarYChange:g,scrollbarYEnabled:T,onScrollbarYEnabledChange:k,onCornerWidthChange:N,onCornerHeightChange:R,children:(0,n.jsx)(v.div,{dir:_,...a,ref:L,style:{position:"relative","--radix-scroll-area-corner-width":j+"px","--radix-scroll-area-corner-height":C+"px",...e.style}})})});P.displayName=N;var T="ScrollAreaViewport",k=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:i,nonce:l,...s}=e,a=S(T,t),d=p(r,o.useRef(null),a.onViewportChange);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,n.jsx)(v.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:a.scrollbarXEnabled?"scroll":"hidden",overflowY:a.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,n.jsx)("div",{ref:a.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});k.displayName=T;var L="ScrollAreaScrollbar",_=o.forwardRef((e,r)=>{let{forceMount:t,...i}=e,l=S(L,e.__scopeScrollArea),{onScrollbarXEnabledChange:s,onScrollbarYEnabledChange:a}=l,d="horizontal"===e.orientation;return o.useEffect(()=>(d?s(!0):a(!0),()=>{d?s(!1):a(!1)}),[d,s,a]),"hover"===l.type?(0,n.jsx)(A,{...i,ref:r,forceMount:t}):"scroll"===l.type?(0,n.jsx)(I,{...i,ref:r,forceMount:t}):"auto"===l.type?(0,n.jsx)(D,{...i,ref:r,forceMount:t}):"always"===l.type?(0,n.jsx)(O,{...i,ref:r}):null});_.displayName=L;var A=o.forwardRef((e,r)=>{let{forceMount:t,...i}=e,l=S(L,e.__scopeScrollArea),[s,a]=o.useState(!1);return o.useEffect(()=>{let e=l.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),a(!0)},n=()=>{r=window.setTimeout(()=>a(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,n.jsx)(b,{present:t||s,children:(0,n.jsx)(D,{"data-state":s?"visible":"hidden",...i,ref:r})})}),I=o.forwardRef((e,r)=>{var t;let{forceMount:i,...l}=e,s=S(L,e.__scopeScrollArea),a="horizontal"===e.orientation,d=ee(()=>u("SCROLL_END"),100),[c,u]=(t={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},o.useReducer((e,r)=>t[e][r]??e,"hidden"));return o.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>u("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,s.scrollHideDelay,u]),o.useEffect(()=>{let e=s.viewport,r=a?"scrollLeft":"scrollTop";if(e){let t=e[r],n=()=>{let n=e[r];t!==n&&(u("SCROLL"),d()),t=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[s.viewport,a,u,d]),(0,n.jsx)(b,{present:i||"hidden"!==c,children:(0,n.jsx)(O,{"data-state":"hidden"===c?"hidden":"visible",...l,ref:r,onPointerEnter:j(e.onPointerEnter,()=>u("POINTER_ENTER")),onPointerLeave:j(e.onPointerLeave,()=>u("POINTER_LEAVE"))})})}),D=o.forwardRef((e,r)=>{let t=S(L,e.__scopeScrollArea),{forceMount:i,...l}=e,[s,a]=o.useState(!1),d="horizontal"===e.orientation,c=ee(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;a(d?e:r)}},10);return er(t.viewport,c),er(t.content,c),(0,n.jsx)(b,{present:i||s,children:(0,n.jsx)(O,{"data-state":s?"visible":"hidden",...l,ref:r})})}),O=o.forwardRef((e,r)=>{let{orientation:t="vertical",...i}=e,l=S(L,e.__scopeScrollArea),s=o.useRef(null),a=o.useRef(0),[d,c]=o.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=Y(d.viewport,d.content),f={...i,sizes:d,onSizesChange:c,hasThumb:!!(u>0&&u<1),onThumbChange:e=>s.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function p(e,r){return function(e,r,t,n="ltr"){let o=J(t),i=r||o/2,l=t.scrollbar.paddingStart+i,s=t.scrollbar.size-t.scrollbar.paddingEnd-(o-i),a=t.content-t.viewport;return K([l,s],"ltr"===n?[0,a]:[-1*a,0])(e)}(e,a.current,d,r)}return"horizontal"===t?(0,n.jsx)(M,{...f,ref:r,onThumbPositionChange:()=>{if(l.viewport&&s.current){let e=Z(l.viewport.scrollLeft,d,l.dir);s.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===t?(0,n.jsx)(F,{...f,ref:r,onThumbPositionChange:()=>{if(l.viewport&&s.current){let e=Z(l.viewport.scrollTop,d);s.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),M=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:i,...l}=e,s=S(L,e.__scopeScrollArea),[a,d]=o.useState(),c=o.useRef(null),u=p(r,c,s.onScrollbarXChange);return o.useEffect(()=>{c.current&&d(getComputedStyle(c.current))},[c]),(0,n.jsx)(W,{"data-orientation":"horizontal",...l,ref:u,sizes:t,style:{bottom:0,left:"rtl"===s.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===s.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":J(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(s.viewport){let n=s.viewport.scrollLeft+r.deltaX;e.onWheelScroll(n),function(e,r){return e>0&&e<r}(n,t)&&r.preventDefault()}},onResize:()=>{c.current&&s.viewport&&a&&i({content:s.viewport.scrollWidth,viewport:s.viewport.offsetWidth,scrollbar:{size:c.current.clientWidth,paddingStart:B(a.paddingLeft),paddingEnd:B(a.paddingRight)}})}})}),F=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:i,...l}=e,s=S(L,e.__scopeScrollArea),[a,d]=o.useState(),c=o.useRef(null),u=p(r,c,s.onScrollbarYChange);return o.useEffect(()=>{c.current&&d(getComputedStyle(c.current))},[c]),(0,n.jsx)(W,{"data-orientation":"vertical",...l,ref:u,sizes:t,style:{top:0,right:"ltr"===s.dir?0:void 0,left:"rtl"===s.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":J(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(s.viewport){let n=s.viewport.scrollTop+r.deltaY;e.onWheelScroll(n),function(e,r){return e>0&&e<r}(n,t)&&r.preventDefault()}},onResize:()=>{c.current&&s.viewport&&a&&i({content:s.viewport.scrollHeight,viewport:s.viewport.offsetHeight,scrollbar:{size:c.current.clientHeight,paddingStart:B(a.paddingTop),paddingEnd:B(a.paddingBottom)}})}})}),[q,z]=C(L),W=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:i,hasThumb:l,onThumbChange:s,onThumbPointerUp:a,onThumbPointerDown:d,onThumbPositionChange:c,onDragScroll:u,onWheelScroll:f,onResize:m,...h}=e,x=S(L,t),[b,w]=o.useState(null),y=p(r,e=>w(e)),N=o.useRef(null),C=o.useRef(""),R=x.viewport,E=i.content-i.viewport,P=g(f),T=g(c),k=ee(m,10);function _(e){N.current&&u({x:e.clientX-N.current.left,y:e.clientY-N.current.top})}return o.useEffect(()=>{let e=e=>{let r=e.target;b?.contains(r)&&P(e,E)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[R,b,E,P]),o.useEffect(T,[i,T]),er(b,k),er(x.content,k),(0,n.jsx)(q,{scope:t,scrollbar:b,hasThumb:l,onThumbChange:g(s),onThumbPointerUp:g(a),onThumbPositionChange:T,onThumbPointerDown:g(d),children:(0,n.jsx)(v.div,{...h,ref:y,style:{position:"absolute",...h.style},onPointerDown:j(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),N.current=b.getBoundingClientRect(),C.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",x.viewport&&(x.viewport.style.scrollBehavior="auto"),_(e))}),onPointerMove:j(e.onPointerMove,_),onPointerUp:j(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=C.current,x.viewport&&(x.viewport.style.scrollBehavior=""),N.current=null})})})}),U="ScrollAreaThumb",V=o.forwardRef((e,r)=>{let{forceMount:t,...o}=e,i=z(U,e.__scopeScrollArea);return(0,n.jsx)(b,{present:t||i.hasThumb,children:(0,n.jsx)($,{ref:r,...o})})}),$=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:i,...l}=e,s=S(U,t),a=z(U,t),{onThumbPositionChange:d}=a,c=p(r,e=>a.onThumbChange(e)),u=o.useRef(void 0),f=ee(()=>{u.current&&(u.current(),u.current=void 0)},100);return o.useEffect(()=>{let e=s.viewport;if(e){let r=()=>{f(),u.current||(u.current=Q(e,d),d())};return d(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[s.viewport,f,d]),(0,n.jsx)(v.div,{"data-state":a.hasThumb?"visible":"hidden",...l,ref:c,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:j(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,n=e.clientY-r.top;a.onThumbPointerDown({x:t,y:n})}),onPointerUp:j(e.onPointerUp,a.onThumbPointerUp)})});V.displayName=U;var H="ScrollAreaCorner",X=o.forwardRef((e,r)=>{let t=S(H,e.__scopeScrollArea),o=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&o?(0,n.jsx)(G,{...e,ref:r}):null});X.displayName=H;var G=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,...i}=e,l=S(H,t),[s,a]=o.useState(0),[d,c]=o.useState(0),u=!!(s&&d);return er(l.scrollbarX,()=>{let e=l.scrollbarX?.offsetHeight||0;l.onCornerHeightChange(e),c(e)}),er(l.scrollbarY,()=>{let e=l.scrollbarY?.offsetWidth||0;l.onCornerWidthChange(e),a(e)}),u?(0,n.jsx)(v.div,{...i,ref:r,style:{width:s,height:d,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function B(e){return e?parseInt(e,10):0}function Y(e,r){let t=e/r;return isNaN(t)?0:t}function J(e){let r=Y(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function Z(e,r,t="ltr"){let n=J(r),o=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,i=r.scrollbar.size-o,l=r.content-r.viewport,s=function(e,[r,t]){return Math.min(t,Math.max(r,e))}(e,"ltr"===t?[0,l]:[-1*l,0]);return K([0,l],[0,i-n])(s)}function K(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let n=(r[1]-r[0])/(e[1]-e[0]);return r[0]+n*(t-e[0])}}var Q=(e,r=()=>{})=>{let t={left:e.scrollLeft,top:e.scrollTop},n=0;return function o(){let i={left:e.scrollLeft,top:e.scrollTop},l=t.left!==i.left,s=t.top!==i.top;(l||s)&&r(),t=i,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function ee(e,r){let t=g(e),n=o.useRef(0);return o.useEffect(()=>()=>window.clearTimeout(n.current),[]),o.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(t,r)},[t,r])}function er(e,r){let t=g(r);x(()=>{let r=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return n.observe(e),()=>{window.cancelAnimationFrame(r),n.unobserve(e)}}},[e,t])}var et=t(4780);let en=o.forwardRef(({className:e,children:r,...t},o)=>(0,n.jsxs)(P,{ref:o,className:(0,et.cn)("relative overflow-hidden",e),...t,children:[(0,n.jsx)(k,{className:"h-full w-full rounded-[inherit]",children:r}),(0,n.jsx)(eo,{}),(0,n.jsx)(X,{})]}));en.displayName=P.displayName;let eo=o.forwardRef(({className:e,orientation:r="vertical",...t},o)=>(0,n.jsx)(_,{ref:o,orientation:r,className:(0,et.cn)("flex touch-none select-none transition-colors","vertical"===r&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...t,children:(0,n.jsx)(V,{className:"relative flex-1 rounded-full bg-border"})}));eo.displayName=_.displayName;var ei=t(27605),el=t(63442),es=t(45880),ea=t(71669),ed=t(84778),ec=t(16189),eu=t(51642),ef=t(58876);let ep=es.Ik({title:es.Yj().min(1,"标题不能为空"),content:es.Yj().min(1,"内容不能为空"),recipientType:es.k5(["all","individual"]),recipientIds:es.YO(es.Yj()).optional()}),em=function(){let e=(0,ec.useRouter)(),{data:r,isLoading:t}=(0,eu.X)(),[o,{isLoading:f}]=(0,ef.Gw)(),p=(0,ei.mN)({resolver:(0,el.u)(ep),defaultValues:{title:"",content:"",recipientType:"all",recipientIds:[]}}),m=async r=>{try{let t={title:r.title,content:r.content,recipientType:r.recipientType,recipientIds:"individual"===r.recipientType?r.recipientIds:void 0};await o(t).unwrap(),ed.l.success("通知发送成功"),e.push("/notifications/list")}catch(e){console.error("发送通知失败:",e),ed.l.error("发送通知失败")}};return(0,n.jsx)("div",{className:"container mx-auto py-6",children:(0,n.jsxs)(l.Zp,{children:[(0,n.jsxs)(l.aR,{children:[(0,n.jsx)(l.ZB,{children:"创建通知"}),(0,n.jsx)(l.BT,{children:"创建一条新的通知消息"})]}),(0,n.jsx)(ea.lV,{...p,children:(0,n.jsxs)("form",{onSubmit:p.handleSubmit(m),children:[(0,n.jsxs)(l.Wu,{className:"space-y-4",children:[(0,n.jsx)(ea.zB,{control:p.control,name:"title",render:({field:e})=>(0,n.jsxs)(ea.eI,{children:[(0,n.jsx)(ea.lR,{children:"通知标题"}),(0,n.jsx)(ea.MJ,{children:(0,n.jsx)(s.p,{placeholder:"请输入通知标题",...e})}),(0,n.jsx)(ea.C5,{})]})}),(0,n.jsx)(ea.zB,{control:p.control,name:"content",render:({field:e})=>(0,n.jsxs)(ea.eI,{children:[(0,n.jsx)(ea.lR,{children:"通知内容"}),(0,n.jsx)(ea.MJ,{children:(0,n.jsx)(a.T,{placeholder:"请输入通知内容",className:"min-h-[120px]",...e})}),(0,n.jsx)(ea.C5,{})]})}),(0,n.jsx)(ea.zB,{control:p.control,name:"recipientType",render:({field:e})=>(0,n.jsxs)(ea.eI,{className:"space-y-3",children:[(0,n.jsx)(ea.lR,{children:"接收者"}),(0,n.jsx)(ea.MJ,{children:(0,n.jsxs)(d.z,{onValueChange:e.onChange,defaultValue:e.value,className:"flex flex-col space-y-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(d.C,{value:"all",id:"all"}),(0,n.jsx)(c.J,{htmlFor:"all",children:"所有人"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(d.C,{value:"individual",id:"individual"}),(0,n.jsx)(c.J,{htmlFor:"individual",children:"指定人员"})]})]})}),(0,n.jsx)(ea.C5,{})]})}),"individual"===p.watch("recipientType")&&(0,n.jsx)(ea.zB,{control:p.control,name:"recipientIds",render:({field:e})=>(0,n.jsxs)(ea.eI,{children:[(0,n.jsx)(ea.lR,{children:"选择接收人"}),(0,n.jsx)(ea.MJ,{children:(0,n.jsx)(en,{className:"h-[200px] rounded-md border p-4",children:(0,n.jsx)("div",{className:"space-y-2",children:r?.map(r=>n.jsxs("div",{className:"flex items-center space-x-2",children:[n.jsx(u.S,{id:r.id,checked:e.value?.includes(r.id),onCheckedChange:t=>{let n=e.value||[];t?e.onChange([...n,r.id]):e.onChange(n.filter(e=>e!==r.id))}}),n.jsx(c.J,{htmlFor:r.id,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:r.name})]},r.id))})})}),(0,n.jsx)(ea.C5,{})]})})]}),(0,n.jsxs)(l.wL,{className:"flex justify-between",children:[(0,n.jsx)(i.$,{variant:"outline",onClick:()=>e.push("/notifications/list"),type:"button",children:"取消"}),(0,n.jsx)(i.$,{type:"submit",disabled:f||t,children:f?"发送中...":"发送通知"})]})]})})]})})}},33873:e=>{"use strict";e.exports=require("path")},34729:(e,r,t)=>{"use strict";t.d(r,{T:()=>l});var n=t(60687),o=t(43210),i=t(4780);let l=o.forwardRef(({className:e,...r},t)=>(0,n.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...r}));l.displayName="Textarea"},40211:(e,r,t)=>{"use strict";t.d(r,{C1:()=>R,bL:()=>C});var n=t(43210),o=t(98599),i=t(11273),l=t(70569),s=t(65551),a=t(83721),d=t(18853),c=t(46059),u=t(14163),f=t(60687),p="Checkbox",[m,h]=(0,i.A)(p),[v,x]=m(p),b=n.forwardRef((e,r)=>{let{__scopeCheckbox:t,name:i,checked:a,defaultChecked:d,required:c,disabled:p,value:m="on",onCheckedChange:h,form:x,...b}=e,[w,g]=n.useState(null),C=(0,o.s)(r,e=>g(e)),R=n.useRef(!1),E=!w||x||!!w.closest("form"),[S=!1,P]=(0,s.i)({prop:a,defaultProp:d,onChange:h}),T=n.useRef(S);return n.useEffect(()=>{let e=w?.form;if(e){let r=()=>P(T.current);return e.addEventListener("reset",r),()=>e.removeEventListener("reset",r)}},[w,P]),(0,f.jsxs)(v,{scope:t,state:S,disabled:p,children:[(0,f.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":j(S)?"mixed":S,"aria-required":c,"data-state":N(S),"data-disabled":p?"":void 0,disabled:p,value:m,...b,ref:C,onKeyDown:(0,l.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(e.onClick,e=>{P(e=>!!j(e)||!e),E&&(R.current=e.isPropagationStopped(),R.current||e.stopPropagation())})}),E&&(0,f.jsx)(y,{control:w,bubbles:!R.current,name:i,value:m,checked:S,required:c,disabled:p,form:x,style:{transform:"translateX(-100%)"},defaultChecked:!j(d)&&d})]})});b.displayName=p;var w="CheckboxIndicator",g=n.forwardRef((e,r)=>{let{__scopeCheckbox:t,forceMount:n,...o}=e,i=x(w,t);return(0,f.jsx)(c.C,{present:n||j(i.state)||!0===i.state,children:(0,f.jsx)(u.sG.span,{"data-state":N(i.state),"data-disabled":i.disabled?"":void 0,...o,ref:r,style:{pointerEvents:"none",...e.style}})})});g.displayName=w;var y=e=>{let{control:r,checked:t,bubbles:o=!0,defaultChecked:i,...l}=e,s=n.useRef(null),c=(0,a.Z)(t),u=(0,d.X)(r);n.useEffect(()=>{let e=s.current,r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==t&&r){let n=new Event("click",{bubbles:o});e.indeterminate=j(t),r.call(e,!j(t)&&t),e.dispatchEvent(n)}},[c,t,o]);let p=n.useRef(!j(t)&&t);return(0,f.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i??p.current,...l,tabIndex:-1,ref:s,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function j(e){return"indeterminate"===e}function N(e){return j(e)?"indeterminate":e?"checked":"unchecked"}var C=b,R=g},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>a,Zp:()=>l,aR:()=>s,wL:()=>u});var n=t(60687),o=t(43210),i=t(4780);let l=o.forwardRef(({className:e,...r},t)=>(0,n.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));l.displayName="Card";let s=o.forwardRef(({className:e,...r},t)=>(0,n.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));s.displayName="CardHeader";let a=o.forwardRef(({className:e,...r},t)=>(0,n.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));a.displayName="CardTitle";let d=o.forwardRef(({className:e,...r},t)=>(0,n.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=o.forwardRef(({className:e,...r},t)=>(0,n.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=o.forwardRef(({className:e,...r},t)=>(0,n.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56896:(e,r,t)=>{"use strict";t.d(r,{S:()=>a});var n=t(60687),o=t(43210),i=t(40211),l=t(13964),s=t(4780);let a=o.forwardRef(({className:e,...r},t)=>(0,n.jsx)(i.bL,{ref:t,className:(0,s.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...r,children:(0,n.jsx)(i.C1,{className:(0,s.cn)("flex items-center justify-center text-current"),children:(0,n.jsx)(l.A,{className:"h-4 w-4"})})}));a.displayName=i.bL.displayName},60676:(e,r,t)=>{Promise.resolve().then(t.bind(t,61850))},61850:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\notifications\\\\create\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\notifications\\create\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63442:(e,r,t)=>{"use strict";t.d(r,{u:()=>d});var n=t(27605);let o=(e,r,t)=>{if(e&&"reportValidity"in e){let o=(0,n.Jt)(t,r);e.setCustomValidity(o&&o.message||""),e.reportValidity()}},i=(e,r)=>{for(let t in r.fields){let n=r.fields[t];n&&n.ref&&"reportValidity"in n.ref?o(n.ref,t,e):n&&n.refs&&n.refs.forEach(r=>o(r,t,e))}},l=(e,r)=>{r.shouldUseNativeValidation&&i(e,r);let t={};for(let o in e){let i=(0,n.Jt)(r.fields,o),l=Object.assign(e[o]||{},{ref:i&&i.ref});if(s(r.names||Object.keys(e),o)){let e=Object.assign({},(0,n.Jt)(t,o));(0,n.hZ)(e,"root",l),(0,n.hZ)(t,o,e)}else(0,n.hZ)(t,o,l)}return t},s=(e,r)=>{let t=a(r);return e.some(e=>a(e).match(`^${t}\\.\\d+`))};function a(e){return e.replace(/\]|\[/g,"")}function d(e,r,t){return void 0===t&&(t={}),function(o,s,a){try{return Promise.resolve(function(n,l){try{var s=Promise.resolve(e["sync"===t.mode?"parse":"parseAsync"](o,r)).then(function(e){return a.shouldUseNativeValidation&&i({},a),{errors:{},values:t.raw?Object.assign({},o):e}})}catch(e){return l(e)}return s&&s.then?s.then(void 0,l):s}(0,function(e){if(Array.isArray(null==e?void 0:e.errors))return{values:{},errors:l(function(e,r){for(var t={};e.length;){var o=e[0],i=o.code,l=o.message,s=o.path.join(".");if(!t[s]){if("unionErrors"in o){var a=o.unionErrors[0].errors[0];t[s]={message:a.message,type:a.code}}else t[s]={message:l,type:i}}if("unionErrors"in o&&o.unionErrors.forEach(function(r){return r.errors.forEach(function(r){return e.push(r)})}),r){var d=t[s].types,c=d&&d[o.code];t[s]=(0,n.Gb)(s,r,t,i,c?[].concat(c,o.message):o.message)}e.shift()}return t}(e.errors,!a.shouldUseNativeValidation&&"all"===a.criteriaMode),a)};throw e}))}catch(e){return Promise.reject(e)}}}},69918:(e,r,t)=>{"use strict";t.d(r,{z:()=>q,C:()=>z});var n=t(60687),o=t(43210),i=t(70569),l=t(98599),s=t(11273),a=t(14163),d=t(72942),c=t(65551),u=t(43),f=t(18853),p=t(83721),m=t(46059),h="Radio",[v,x]=(0,s.A)(h),[b,w]=v(h),g=o.forwardRef((e,r)=>{let{__scopeRadio:t,name:s,checked:d=!1,required:c,disabled:u,value:f="on",onCheck:p,form:m,...h}=e,[v,x]=o.useState(null),w=(0,l.s)(r,e=>x(e)),g=o.useRef(!1),y=!v||m||!!v.closest("form");return(0,n.jsxs)(b,{scope:t,checked:d,disabled:u,children:[(0,n.jsx)(a.sG.button,{type:"button",role:"radio","aria-checked":d,"data-state":C(d),"data-disabled":u?"":void 0,disabled:u,value:f,...h,ref:w,onClick:(0,i.m)(e.onClick,e=>{d||p?.(),y&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),y&&(0,n.jsx)(N,{control:v,bubbles:!g.current,name:s,value:f,checked:d,required:c,disabled:u,form:m,style:{transform:"translateX(-100%)"}})]})});g.displayName=h;var y="RadioIndicator",j=o.forwardRef((e,r)=>{let{__scopeRadio:t,forceMount:o,...i}=e,l=w(y,t);return(0,n.jsx)(m.C,{present:o||l.checked,children:(0,n.jsx)(a.sG.span,{"data-state":C(l.checked),"data-disabled":l.disabled?"":void 0,...i,ref:r})})});j.displayName=y;var N=e=>{let{control:r,checked:t,bubbles:i=!0,...l}=e,s=o.useRef(null),a=(0,p.Z)(t),d=(0,f.X)(r);return o.useEffect(()=>{let e=s.current,r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(a!==t&&r){let n=new Event("click",{bubbles:i});r.call(e,t),e.dispatchEvent(n)}},[a,t,i]),(0,n.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:t,...l,tabIndex:-1,ref:s,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function C(e){return e?"checked":"unchecked"}var R=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],E="RadioGroup",[S,P]=(0,s.A)(E,[d.RG,x]),T=(0,d.RG)(),k=x(),[L,_]=S(E),A=o.forwardRef((e,r)=>{let{__scopeRadioGroup:t,name:o,defaultValue:i,value:l,required:s=!1,disabled:f=!1,orientation:p,dir:m,loop:h=!0,onValueChange:v,...x}=e,b=T(t),w=(0,u.jH)(m),[g,y]=(0,c.i)({prop:l,defaultProp:i,onChange:v});return(0,n.jsx)(L,{scope:t,name:o,required:s,disabled:f,value:g,onValueChange:y,children:(0,n.jsx)(d.bL,{asChild:!0,...b,orientation:p,dir:w,loop:h,children:(0,n.jsx)(a.sG.div,{role:"radiogroup","aria-required":s,"aria-orientation":p,"data-disabled":f?"":void 0,dir:w,...x,ref:r})})})});A.displayName=E;var I="RadioGroupItem",D=o.forwardRef((e,r)=>{let{__scopeRadioGroup:t,disabled:s,...a}=e,c=_(I,t),u=c.disabled||s,f=T(t),p=k(t),m=o.useRef(null),h=(0,l.s)(r,m),v=c.value===a.value,x=o.useRef(!1);return o.useEffect(()=>{let e=e=>{R.includes(e.key)&&(x.current=!0)},r=()=>x.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",r),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",r)}},[]),(0,n.jsx)(d.q7,{asChild:!0,...f,focusable:!u,active:v,children:(0,n.jsx)(g,{disabled:u,required:c.required,checked:v,...p,...a,name:c.name,ref:h,onCheck:()=>c.onValueChange(a.value),onKeyDown:(0,i.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,i.m)(a.onFocus,()=>{x.current&&m.current?.click()})})})});D.displayName=I;var O=o.forwardRef((e,r)=>{let{__scopeRadioGroup:t,...o}=e,i=k(t);return(0,n.jsx)(j,{...i,...o,ref:r})});O.displayName="RadioGroupIndicator";var M=t(65822),F=t(4780);let q=o.forwardRef(({className:e,...r},t)=>(0,n.jsx)(A,{className:(0,F.cn)("grid gap-2",e),...r,ref:t}));q.displayName=A.displayName;let z=o.forwardRef(({className:e,...r},t)=>(0,n.jsx)(D,{ref:t,className:(0,F.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:(0,n.jsx)(O,{className:"flex items-center justify-center",children:(0,n.jsx)(M.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));z.displayName=D.displayName},71669:(e,r,t)=>{"use strict";t.d(r,{C5:()=>b,MJ:()=>v,Rr:()=>x,eI:()=>m,lR:()=>h,lV:()=>d,zB:()=>u});var n=t(60687),o=t(43210),i=t(8730),l=t(27605),s=t(4780),a=t(80013);let d=l.Op,c=o.createContext({}),u=({...e})=>(0,n.jsx)(c.Provider,{value:{name:e.name},children:(0,n.jsx)(l.xI,{...e})}),f=()=>{let e=o.useContext(c),r=o.useContext(p),{getFieldState:t,formState:n}=(0,l.xW)(),i=t(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:s}=r;return{id:s,name:e.name,formItemId:`${s}-form-item`,formDescriptionId:`${s}-form-item-description`,formMessageId:`${s}-form-item-message`,...i}},p=o.createContext({}),m=o.forwardRef(({className:e,...r},t)=>{let i=o.useId();return(0,n.jsx)(p.Provider,{value:{id:i},children:(0,n.jsx)("div",{ref:t,className:(0,s.cn)("space-y-2",e),...r})})});m.displayName="FormItem";let h=o.forwardRef(({className:e,...r},t)=>{let{error:o,formItemId:i}=f();return(0,n.jsx)(a.J,{ref:t,className:(0,s.cn)(o&&"text-destructive",e),htmlFor:i,...r})});h.displayName="FormLabel";let v=o.forwardRef(({...e},r)=>{let{error:t,formItemId:o,formDescriptionId:l,formMessageId:s}=f();return(0,n.jsx)(i.DX,{ref:r,id:o,"aria-describedby":t?`${l} ${s}`:`${l}`,"aria-invalid":!!t,...e})});v.displayName="FormControl";let x=o.forwardRef(({className:e,...r},t)=>{let{formDescriptionId:o}=f();return(0,n.jsx)("p",{ref:t,id:o,className:(0,s.cn)("text-sm text-muted-foreground",e),...r})});x.displayName="FormDescription";let b=o.forwardRef(({className:e,children:r,...t},o)=>{let{error:i,formMessageId:l}=f(),a=i?String(i?.message??""):r;return a?(0,n.jsx)("p",{ref:o,id:l,className:(0,s.cn)("text-sm font-medium text-destructive",e),...t,children:a}):null});b.displayName="FormMessage"},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var n=t(60687),o=t(43210),i=t(78148),l=t(24224),s=t(4780);let a=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=o.forwardRef(({className:e,...r},t)=>(0,n.jsx)(i.b,{ref:t,className:(0,s.cn)(a(),e),...r}));d.displayName=i.b.displayName},81630:e=>{"use strict";e.exports=require("http")},83721:(e,r,t)=>{"use strict";t.d(r,{Z:()=>o});var n=t(43210);function o(e){let r=n.useRef({value:e,previous:e});return n.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>l});var n=t(60687),o=t(43210),i=t(4780);let l=o.forwardRef(({className:e,type:r,...t},o)=>(0,n.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:o,...t}));l.displayName="Input"},94735:e=>{"use strict";e.exports=require("events")},94764:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s,metadata:()=>l});var n=t(37413),o=t(24597),i=t(36733);let l={title:"CardMees",description:"CardMees Application"};function s({children:e}){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.default,{}),(0,n.jsxs)("div",{className:"flex h-[calc(100vh)]",children:[(0,n.jsx)(i.default,{}),(0,n.jsx)("main",{className:"flex-1 p-8 pt-16 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400",children:(0,n.jsx)("div",{className:"max-w-8xl mx-auto bg-white rounded-lg shadow-sm",children:e})})]})]})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4447,7392,5814,3928,3443,1991,7605,3019,9879],()=>t(21337));module.exports=n})();