(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7624],{15682:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>c});var l=a(95155),r=a(68856);function c(){return(0,l.jsxs)("div",{className:"p-4 space-y-6",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(r.E,{className:"h-10 w-48"}),(0,l.jsx)(r.E,{className:"h-10 w-24"})]}),(0,l.jsx)("div",{className:"flex space-x-2",children:(0,l.jsx)(r.<PERSON>,{className:"h-10 w-28"})})]}),(0,l.jsxs)("div",{className:"border rounded-md",children:[(0,l.jsx)("div",{className:"flex border-b p-3 bg-muted/30",children:[,,,,].fill(0).map((e,s)=>(0,l.jsx)(r.E,{className:"h-6 flex-1 mx-2"},s))}),Array(8).fill(0).map((e,s)=>(0,l.jsx)("div",{className:"flex border-b p-3",children:[,,,,].fill(0).map((e,s)=>(0,l.jsx)(r.E,{className:"h-6 flex-1 mx-2"},s))},s))]}),(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)(r.E,{className:"h-8 w-40"}),(0,l.jsx)(r.E,{className:"h-8 w-64"})]})]})}},59434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>c});var l=a(52596),r=a(39688);function c(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,l.$)(s))}},66186:(e,s,a)=>{Promise.resolve().then(a.bind(a,15682))},68856:(e,s,a)=>{"use strict";a.d(s,{E:()=>c});var l=a(95155),r=a(59434);function c(e){let{className:s,...a}=e;return(0,l.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",s),...a})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,6315,7358],()=>s(66186)),_N_E=e.O()}]);