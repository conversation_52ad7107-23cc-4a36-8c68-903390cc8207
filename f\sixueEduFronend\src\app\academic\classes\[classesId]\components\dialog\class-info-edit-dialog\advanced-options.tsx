"use client"
import { Collapsible, CollapsibleTrigger, CollapsibleContent } from "@/components/ui/collapsible"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { cn } from "@/lib/utils"
import { ChevronDown, Clock, UserCheck, CalendarCheck, BarChart3, Calendar } from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface AdvancedOptionsProps {
  isAdvancedOpen: boolean
  setIsAdvancedOpen: (open: boolean) => void
  formData: any
  setFormData: (data: any) => void
}

function AdvancedOptions({ isAdvancedOpen, setIsAdvancedOpen, formData, setFormData }: AdvancedOptionsProps) {
  return (
    <TooltipProvider>
      <Collapsible
        open={isAdvancedOpen}
        onOpenChange={setIsAdvancedOpen}
      >
        <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">高级选项</span>
            <span className="text-xs text-gray-500 dark:text-gray-400">配置额外的课程设置</span>
          </div>
          <ChevronDown
            className={cn(
              "h-4 w-4 text-gray-500 dark:text-gray-400 transition-transform duration-200",
              isAdvancedOpen ? "transform rotate-180" : "",
            )}
          />
        </CollapsibleTrigger>

        <CollapsibleContent className="p-4 bg-white dark:bg-gray-950 divide-y divide-gray-100 dark:divide-gray-800">
          {/* 开放预约选项 */}
          <div className="py-4 space-y-4 first:pt-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-primary" />
                <Label htmlFor="allow-reservation" className="font-medium">
                  开放预约
                </Label>
              </div>
              <Switch
                id="allow-reservation"
                checked={formData.isReserve}
                onCheckedChange={(checked) => setFormData({ ...formData, isReserve: checked })}
                className="data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
              />
            </div>

            {formData.isReserve && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pl-6 mt-2 animate-in fade-in slide-in-from-top-2 duration-200">
                <div className="space-y-2">
                  <Label htmlFor="start-time" className="text-sm text-gray-500 dark:text-gray-400">
                    开始时间 (小时)
                  </Label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="start-time"
                      type="text"
                      placeholder="例如: 24"
                      className="pl-10"
                      value={formData.appointmentStartTime || ""}
                      onChange={(e) => setFormData({ ...formData, appointmentStartTime: e.target.value })}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end-time" className="text-sm text-gray-500 dark:text-gray-400">
                    截止时间 (小时)
                  </Label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="end-time"
                      type="text"
                      placeholder="例如: 2"
                      className="pl-10"
                      value={formData.appointmentEndTime || ""}
                      onChange={(e) => setFormData({ ...formData, appointmentEndTime: e.target.value })}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 学员扫码考勤 */}
          <div className="py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <UserCheck className="h-4 w-4 text-primary" />
                <Label htmlFor="student-scan" className="font-medium">
                  学员扫码考勤
                </Label>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 text-xs text-gray-500 dark:text-gray-400 cursor-help">
                      ?
                    </span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">允许学员通过扫描二维码进行考勤签到</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Switch
                id="student-scan"
                checked={formData.isQRCodeAttendance}
                onCheckedChange={(checked) => setFormData({ ...formData, isQRCodeAttendance: checked })}
                className="data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
              />
            </div>
          </div>

          {/* 系统自动考勤 */}
          <div className="py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CalendarCheck className="h-4 w-4 text-primary" />
                <Label htmlFor="auto-attendance" className="font-medium">
                  系统自动考勤
                </Label>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 text-xs text-gray-500 dark:text-gray-400 cursor-help">
                      ?
                    </span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">系统将在课程开始时自动为学员记录考勤</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Switch
                id="auto-attendance"
                checked={formData.isAutoCheckIn}
                onCheckedChange={(checked) => setFormData({ ...formData, isAutoCheckIn: checked })}
                className="data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
              />
            </div>
          </div>

          {/* 开放请假 */}
          <div className="py-4 space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-primary" />
                <Label htmlFor="allow-leave" className="font-medium">
                  开放请假
                </Label>
              </div>
              <Switch
                id="allow-leave"
                checked={formData.isOnLeave}
                onCheckedChange={(checked) => setFormData({ ...formData, isOnLeave: checked })}
                className="data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
              />
            </div>

            {formData.isOnLeave && (
              <div className="pl-6 space-y-2 animate-in fade-in slide-in-from-top-2 duration-200">
                <Label htmlFor="leave-deadline" className="text-sm text-gray-500 dark:text-gray-400">
                  截止时间 (小时)
                </Label>
                <div className="relative max-w-xs">
                  <Clock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="leave-deadline"
                    type="text"
                    placeholder="例如: 2"
                    className="pl-10"
                    value={formData.leaveDeadline || ""}
                    onChange={(e) => setFormData({ ...formData, leaveDeadline: e.target.value })}
                  />
                </div>
              </div>
            )}
          </div>

          {/* 显示周期数 */}
          <div className="py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-primary" />
                <Label htmlFor="show-cycle" className="font-medium">
                  显示周期数
                </Label>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 text-xs text-gray-500 dark:text-gray-400 cursor-help">
                      ?
                    </span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">在课程信息中显示当前周期数</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Switch
                id="show-cycle"
                className="data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                checked={formData.isShowWeekCount}
                onCheckedChange={(checked) => setFormData({ ...formData, isShowWeekCount: checked })}
              />
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </TooltipProvider>
  )
}

export default AdvancedOptions

