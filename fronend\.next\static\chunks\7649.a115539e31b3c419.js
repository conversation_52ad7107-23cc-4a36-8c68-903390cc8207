"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7649],{15452:(e,t,r)=>{r.d(t,{G$:()=>Y,Hs:()=>b,UC:()=>et,VY:()=>en,ZL:()=>Q,bL:()=>X,bm:()=>eo,hE:()=>er,hJ:()=>ee,l9:()=>z});var n=r(12115),o=r(85185),a=r(6101),i=r(46081),l=r(61285),s=r(5845),d=r(19178),u=r(25519),c=r(34378),p=r(28905),f=r(63655),g=r(92293),m=r(93795),v=r(38168),h=r(99708),y=r(95155),D="Dialog",[x,b]=(0,i.A)(D),[j,w]=x(D),R=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[p=!1,f]=(0,s.i)({prop:o,defaultProp:a,onChange:i});return(0,y.jsx)(j,{scope:t,triggerRef:u,contentRef:c,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};R.displayName=D;var C="DialogTrigger",N=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=w(C,r),l=(0,a.s)(t,i.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":U(i.open),...n,ref:l,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});N.displayName=C;var A="DialogPortal",[I,O]=x(A,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,i=w(A,t);return(0,y.jsx)(I,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,y.jsx)(p.C,{present:r||i.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=A;var F="DialogOverlay",_=n.forwardRef((e,t)=>{let r=O(F,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=w(F,e.__scopeDialog);return a.modal?(0,y.jsx)(p.C,{present:n||a.open,children:(0,y.jsx)(P,{...o,ref:t})}):null});_.displayName=F;var P=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(F,r);return(0,y.jsx)(m.A,{as:h.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":U(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),k="DialogContent",G=n.forwardRef((e,t)=>{let r=O(k,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=w(k,e.__scopeDialog);return(0,y.jsx)(p.C,{present:n||a.open,children:a.modal?(0,y.jsx)(T,{...o,ref:t}):(0,y.jsx)(B,{...o,ref:t})})});G.displayName=k;var T=n.forwardRef((e,t)=>{let r=w(k,e.__scopeDialog),i=n.useRef(null),l=(0,a.s)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(L,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=n.forwardRef((e,t)=>{let r=w(k,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,y.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(i=r.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,i;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let l=t.target;(null===(i=r.triggerRef.current)||void 0===i?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),L=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...s}=e,c=w(k,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,y.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":U(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)($,{titleId:c.titleId}),(0,y.jsx)(K,{contentRef:p,descriptionId:c.descriptionId})]})]})}),Z="DialogTitle",q=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(Z,r);return(0,y.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});q.displayName=Z;var M="DialogDescription",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(M,r);return(0,y.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});V.displayName=M;var H="DialogClose",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=w(H,r);return(0,y.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function U(e){return e?"open":"closed"}S.displayName=H;var W="DialogTitleWarning",[Y,J]=(0,i.q)(W,{contentName:k,titleName:Z,docsSlug:"dialog"}),$=e=>{let{titleId:t}=e,r=J(W),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},K=e=>{let{contentRef:t,descriptionId:r}=e,o=J("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(a)},[a,t,r]),null},X=R,z=N,Q=E,ee=_,et=G,er=q,en=V,eo=S},17649:(e,t,r)=>{r.d(t,{UC:()=>k,VY:()=>L,ZD:()=>T,ZL:()=>_,bL:()=>E,hE:()=>B,hJ:()=>P,l9:()=>F,rc:()=>G});var n=r(12115),o=r(46081),a=r(6101),i=r(15452),l=r(85185),s=r(99708),d=r(95155),u="AlertDialog",[c,p]=(0,o.A)(u,[i.Hs]),f=(0,i.Hs)(),g=e=>{let{__scopeAlertDialog:t,...r}=e,n=f(t);return(0,d.jsx)(i.bL,{...n,...r,modal:!0})};g.displayName=u;var m=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=f(r);return(0,d.jsx)(i.l9,{...o,...n,ref:t})});m.displayName="AlertDialogTrigger";var v=e=>{let{__scopeAlertDialog:t,...r}=e,n=f(t);return(0,d.jsx)(i.ZL,{...n,...r})};v.displayName="AlertDialogPortal";var h=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=f(r);return(0,d.jsx)(i.hJ,{...o,...n,ref:t})});h.displayName="AlertDialogOverlay";var y="AlertDialogContent",[D,x]=c(y),b=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:o,...u}=e,c=f(r),p=n.useRef(null),g=(0,a.s)(t,p),m=n.useRef(null);return(0,d.jsx)(i.G$,{contentName:y,titleName:j,docsSlug:"alert-dialog",children:(0,d.jsx)(D,{scope:r,cancelRef:m,children:(0,d.jsxs)(i.UC,{role:"alertdialog",...c,...u,ref:g,onOpenAutoFocus:(0,l.m)(u.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=m.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(s.xV,{children:o}),(0,d.jsx)(O,{contentRef:p})]})})})});b.displayName=y;var j="AlertDialogTitle",w=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=f(r);return(0,d.jsx)(i.hE,{...o,...n,ref:t})});w.displayName=j;var R="AlertDialogDescription",C=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=f(r);return(0,d.jsx)(i.VY,{...o,...n,ref:t})});C.displayName=R;var N=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=f(r);return(0,d.jsx)(i.bm,{...o,...n,ref:t})});N.displayName="AlertDialogAction";var A="AlertDialogCancel",I=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:o}=x(A,r),l=f(r),s=(0,a.s)(t,o);return(0,d.jsx)(i.bm,{...l,...n,ref:s})});I.displayName=A;var O=e=>{let{contentRef:t}=e,r="`".concat(y,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(y,"` by passing a `").concat(R,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(y,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return n.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},E=g,F=m,_=v,P=h,k=b,G=N,T=I,B=w,L=C}}]);