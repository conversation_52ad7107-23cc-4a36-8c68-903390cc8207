(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9808],{59434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>d});var r=a(52596),l=a(39688);function d(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,l.QP)((0,r.$)(s))}},66695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>n,Wu:()=>m,ZB:()=>i,Zp:()=>c,aR:()=>t,wL:()=>x});var r=a(95155),l=a(12115),d=a(59434);let c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...l})});c.displayName="Card";let t=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...l})});t.displayName="CardHeader";let i=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...l})});i.displayName="CardTitle";let n=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,d.cn)("text-sm text-muted-foreground",a),...l})});n.displayName="CardDescription";let m=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,d.cn)("p-6 pt-0",a),...l})});m.displayName="CardContent";let x=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,d.cn)("flex items-center p-6 pt-0",a),...l})});x.displayName="CardFooter"},68856:(e,s,a)=>{"use strict";a.d(s,{E:()=>d});var r=a(95155),l=a(59434);function d(e){let{className:s,...a}=e;return(0,r.jsx)("div",{className:(0,l.cn)("animate-pulse rounded-md bg-muted",s),...a})}},78642:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>c});var r=a(95155),l=a(68856),d=a(66695);function c(){return(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsx)(d.Zp,{children:(0,r.jsx)(d.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.E,{className:"h-8 w-48"}),(0,r.jsx)(l.E,{className:"h-4 w-64"})]}),(0,r.jsx)(l.E,{className:"h-10 w-24"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4",children:Array(6).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.E,{className:"h-4 w-24"}),(0,r.jsx)(l.E,{className:"h-6 w-full"})]},s))})]})})}),(0,r.jsx)(d.Zp,{children:(0,r.jsx)(d.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"flex space-x-4 border-b",children:[,,,].fill(0).map((e,s)=>(0,r.jsx)(l.E,{className:"h-10 w-24"},s))}),(0,r.jsx)("div",{className:"space-y-4 mt-4",children:(0,r.jsxs)("div",{className:"border rounded-md",children:[(0,r.jsx)("div",{className:"flex border-b p-3 bg-muted/30",children:[,,,,,].fill(0).map((e,s)=>(0,r.jsx)(l.E,{className:"h-6 flex-1 mx-2"},s))}),Array(6).fill(0).map((e,s)=>(0,r.jsx)("div",{className:"flex border-b p-3",children:[,,,,,].fill(0).map((e,s)=>(0,r.jsx)(l.E,{className:"h-6 flex-1 mx-2"},s))},s))]})})]})})})]})}},97175:(e,s,a)=>{Promise.resolve().then(a.bind(a,78642))}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,6315,7358],()=>s(97175)),_N_E=e.O()}]);