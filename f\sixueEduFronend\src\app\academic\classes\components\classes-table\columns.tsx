
import { ColumnDef } from "@tanstack/react-table";
import { Pencil } from "lucide-react";
import { cn } from "@/lib/utils";

import { ClassesTableType } from "./type";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { TooltipIconButton } from "@/components/ui/tooltip-icon-button";
import { formatDate } from "@/utils/table/formatDate";

export const classesTableColumns: ColumnDef<ClassesTableType>[] = [
  {
    accessorKey: "name",
    header: '班级名称',
    cell: ({ row }) => <div className="font-medium text-slate-800">{row.original.name}</div>,
  },
  {
    accessorKey: "teacherName",
    header: "上课老师",
    cell: ({ row }) => <div className="text-slate-700">{row.original.teacher?.name}</div>,
  },
  {
    accessorKey: "times",
    header: "已上/总次数",
    cell: ({ row }) => (
      <div className="text-slate-600 flex items-center">
        <span className="font-medium text-emerald-600">{row.original.currentWeeks}</span>
        <span className="mx-0.5 text-slate-400">/</span>
        <span>{row.original.totalWeeks}</span>
      </div>
    ),
  },
  {
    accessorKey: "startDate",
    header: "开班日期",
    cell: ({ row }) => {
      const startDate = formatDate(row.original.startDate as any, "yyyy-MM-dd (EEEE)")
      return (
        <div className="text-slate-700 flex items-center">
          <span className="text-slate-800 font-medium">{startDate}</span>
        </div>
      )
    },
  },

  {
    accessorKey: "courseName",
    header: "当前课程",
    cell: ({ row }) => (
      <Badge className="bg-slate-100 text-slate-700 hover:bg-slate-200 transition-colors rounded-md px-2 py-1 font-normal">
        {row.original.course?.name}
      </Badge>
    ),
  },
  {
    accessorKey: "classesStatus",
    header: "状态",
    cell: ({ row }) => {
      const currentDate = new Date()
      const startDate = new Date(row.original.startDate as any)
      const endDate = new Date(row.original.endDate as any)

      // 使用枚举式对象定义状态类型
      const STATUS = {
        ACTIVE: 'active',
        INACTIVE: 'inactive',
        END: 'end'
      }

      // 使用更清晰的条件判断
      let status
      if (currentDate > startDate && currentDate < endDate) {
        status = STATUS.ACTIVE
      } else if (currentDate < startDate) {
        status = STATUS.INACTIVE
      } else {
        status = STATUS.END
      }
      // 状态样式和文本映射 - 更新样式为更现代的风格
      const statusConfig = {
        [STATUS.ACTIVE]: {
          badgeClass: "bg-emerald-50 text-emerald-700 hover:bg-emerald-100 border-emerald-200",
          dotClass: "bg-emerald-500",
          text: "进行中"
        },
        [STATUS.INACTIVE]: {
          badgeClass: "bg-slate-50 text-slate-700 hover:bg-slate-100 border-slate-200",
          dotClass: "bg-slate-500",
          text: "未开始"
        },
        [STATUS.END]: {
          badgeClass: "bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200",
          dotClass: "bg-amber-500",
          text: "已结束"
        }
      }

      const { badgeClass, dotClass, text } = statusConfig[status]

      return (
        <Badge className={cn("font-medium border py-1 px-2.5 rounded-md transition-all", badgeClass)}>
          <div className="flex items-center gap-1.5">
            <div className={cn("w-2 h-2 rounded-full animate-pulse", dotClass)} />
            {text}
          </div>
        </Badge>
      )
    },
  },
  {
    id: "actions",
    header: "操作",
    cell: ({ row }) => {
      const classes = row.original;
      return (
        <>

          <Link href={`/academic/classes/${classes.id}`}>
            <TooltipIconButton
              icon={Pencil}
              tooltipText="班级详细"
            />
          </Link>

        </>
      )
    },
  },
]