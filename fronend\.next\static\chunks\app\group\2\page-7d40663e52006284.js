(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9719],{14791:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var a=t(95155),l=t(12115),r=t(29733),i=t(66358),n=t(56904);function d(e){let{course:s}=e;return(0,a.jsxs)("div",{className:"mx-4 my-4 bg-white rounded-xl shadow-md overflow-hidden",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("img",{src:s.image,alt:s.name,className:"w-full h-48 object-cover"}),(0,a.jsx)("div",{className:"absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium",children:s.category})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h3",{className:"font-bold text-lg text-gray-800",children:s.name}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)("span",{className:"text-yellow-500",children:"★"}),(0,a.jsx)("span",{className:"text-yellow-500",children:"★"}),(0,a.jsx)("span",{className:"text-yellow-500",children:"★"}),(0,a.jsx)("span",{className:"text-yellow-500",children:"★"}),(0,a.jsx)("span",{className:"text-yellow-500",children:"★"}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 ml-1",children:["(",s.reviewCount,"条评价)"]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:s.description}),(0,a.jsxs)("div",{className:"mt-3 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"text-red-500 font-bold",children:["\xa5",s.price]}),(0,a.jsxs)("span",{className:"text-xs text-gray-400 line-through ml-1",children:["\xa5",s.originalPrice]})]}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[s.studentCount,"人已学习"]})]})]})]})}function c(e){let{times:s,selectedTime:t,onSelectTime:l}=e;return(0,a.jsxs)("div",{className:"mx-4 my-4 bg-white rounded-xl shadow-md p-4",children:[(0,a.jsx)("h3",{className:"font-bold text-gray-800 mb-3",children:"选择预约时间"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-3",children:s.map(e=>(0,a.jsxs)("button",{disabled:!e.available,className:"p-3 rounded-lg border text-sm ".concat(t===e.id?"bg-red-500 text-white border-red-500":e.available?"border-gray-200 hover:border-red-500":"bg-gray-100 text-gray-400 cursor-not-allowed"),onClick:()=>e.available&&l(e.id),children:[(0,a.jsx)("div",{className:"font-medium",children:e.date}),(0,a.jsx)("div",{className:"text-xs mt-1 ".concat(t===e.id?"text-white":"text-gray-500"),children:e.time}),!e.available&&(0,a.jsx)("div",{className:"text-xs mt-1 text-gray-500",children:"已约满"})]},e.id))})]})}function o(e){let{teacher:s}=e;return(0,a.jsxs)("div",{className:"mx-4 my-4 bg-white rounded-xl shadow-md overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex p-4 items-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full overflow-hidden mr-4 border-2 border-gray-100",children:(0,a.jsx)("img",{src:s.avatar,alt:s.name,className:"w-full h-full object-cover"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-bold text-lg",children:s.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:s.title}),(0,a.jsx)("div",{className:"flex mt-1",children:s.tags.map((e,s)=>(0,a.jsx)("span",{className:"mr-2 text-xs px-2 py-0.5 bg-gray-100 rounded-full text-gray-500",children:e},s))})]})]}),(0,a.jsx)("div",{className:"px-4 pb-4",children:(0,a.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:s.bio})})]})}function m(e){let{open:s,onClose:t,onSubmit:r,selectedTime:i,times:n}=e,[d,c]=(0,l.useState)({name:"",phone:"",remarks:""});if(!s)return null;let o=n.find(e=>e.id===i),m=e=>{let{name:s,value:t}=e.target;c(e=>({...e,[s]:t}))};return(0,a.jsx)("div",{className:"fixed inset-0 z-50 bg-black/30 flex items-center justify-center p-4",children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),r(d)},className:"bg-white rounded-2xl w-full max-w-md p-6 shadow-lg",children:[(0,a.jsx)("h2",{className:"text-center text-xl font-bold mb-5",children:"确认预约信息"}),o&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"预约日期："}),(0,a.jsx)("span",{className:"font-medium",children:o.date})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm mt-1",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"预约时间："}),(0,a.jsx)("span",{className:"font-medium",children:o.time})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm text-gray-600 mb-1",children:"您的姓名"}),(0,a.jsx)("input",{name:"name",value:d.name,onChange:m,required:!0,placeholder:"请输入您的姓名",className:"w-full px-3 py-2.5 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-300"})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm text-gray-600 mb-1",children:"联系电话"}),(0,a.jsx)("input",{name:"phone",value:d.phone,onChange:m,required:!0,placeholder:"请输入您的联系电话",className:"w-full px-3 py-2.5 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-300"})]}),(0,a.jsxs)("div",{className:"mb-5",children:[(0,a.jsx)("label",{className:"block text-sm text-gray-600 mb-1",children:"备注信息"}),(0,a.jsx)("textarea",{name:"remarks",value:d.remarks,onChange:m,placeholder:"有什么需要说明的，可以在这里备注",className:"w-full px-3 py-2.5 border border-gray-200 rounded-lg h-24 focus:outline-none focus:ring-2 focus:ring-red-300 resize-none"})]}),(0,a.jsx)("button",{type:"submit",className:"w-full py-3 rounded-full bg-gradient-to-r from-red-500 to-red-600 text-white text-base font-medium border-0 mb-2",children:"确认预约"}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("button",{type:"button",onClick:t,className:"text-gray-400 bg-transparent border-0 text-sm py-1 px-3 hover:text-gray-500",children:"取消"})})]})})}function x(e){let{currentPrice:s,originalPrice:t,endTime:r}=e,[i,n]=(0,l.useState)({days:0,hours:0,minutes:0,seconds:0});return(0,l.useEffect)(()=>{let e=setInterval(()=>{let s=r-new Date().getTime();if(s<0){clearInterval(e),n({days:0,hours:0,minutes:0,seconds:0});return}let t=Math.floor(s/864e5),a=Math.floor(s%864e5/36e5);n({days:t,hours:a,minutes:Math.floor(s%36e5/6e4),seconds:Math.floor(s%6e4/1e3)})},1e3);return()=>clearInterval(e)},[r]),(0,a.jsx)("div",{className:"mx-3 my-3",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-red-500 to-orange-500 rounded-lg shadow-md overflow-hidden",children:(0,a.jsx)("div",{className:"p-3",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsx)("div",{children:"0.00"===s?(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-white text-2xl font-bold",children:"免费"}),(0,a.jsxs)("span",{className:"text-white/80 text-xs",children:["原价 \xa5",t]})]}):(0,a.jsxs)("div",{className:"flex items-baseline",children:[(0,a.jsx)("span",{className:"text-white text-sm font-medium",children:"\xa5"}),(0,a.jsx)("span",{className:"text-white text-2xl font-bold mx-1",children:s}),(0,a.jsxs)("span",{className:"text-white/80 text-xs line-through ml-1",children:["\xa5",t]})]})}),(0,a.jsxs)("div",{className:"flex flex-col items-end",children:[(0,a.jsx)("div",{className:"bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium mb-1",children:"限时优惠"}),(0,a.jsxs)("div",{className:"flex items-center text-white text-xs",children:[(0,a.jsx)("span",{className:"mr-1",children:"剩余"}),(0,a.jsx)("div",{className:"bg-black/30 px-1.5 py-0.5 rounded mx-0.5 font-mono",children:String(i.days).padStart(2,"0")}),(0,a.jsx)("span",{className:"mx-0.5",children:":"}),(0,a.jsx)("div",{className:"bg-black/30 px-1.5 py-0.5 rounded mx-0.5 font-mono",children:String(i.hours).padStart(2,"0")}),(0,a.jsx)("span",{className:"mx-0.5",children:":"}),(0,a.jsx)("div",{className:"bg-black/30 px-1.5 py-0.5 rounded mx-0.5 font-mono",children:String(i.minutes).padStart(2,"0")})]})]})]}),"0.00"===s&&(0,a.jsxs)("div",{className:"mt-1 bg-white/20 rounded-lg p-2 text-white text-xs leading-tight",children:[(0,a.jsx)("span",{className:"font-bold",children:"\uD83D\uDCE3 预约提醒："}),(0,a.jsx)("span",{children:"每人限1次免费试听机会，名额有限，预约从速！"})]})]})})})})}function h(e){let{title:s,list:t}=e;return(0,a.jsxs)("section",{className:"mx-4 my-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-3",children:[(0,a.jsx)("div",{className:"h-6 w-1 bg-red-500 rounded-full mr-2"}),(0,a.jsx)("h2",{className:"text-lg font-bold text-gray-800",children:s})]}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden",children:t.map((e,t)=>{if("text"===e.type){var l;return(0,a.jsx)("div",{className:"p-4",children:null===(l=e.text)||void 0===l?void 0:l.split("\n").map((e,t)=>{if("预约说明"===s&&e.match(/^\d+\./)){let[s,...l]=e.split(/\.(.+)/);return(0,a.jsxs)("div",{className:"flex items-start mb-2",children:[(0,a.jsx)("div",{className:"bg-red-500 text-white w-5 h-5 rounded-full flex items-center justify-center text-xs mr-2 mt-0.5",children:s}),(0,a.jsx)("p",{className:"text-gray-700 flex-1",children:l.join(".")})]},t)}return(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-2",children:e},t)})},t)}return"img"===e.type?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("img",{src:e.img,alt:"",className:"w-full rounded-none",loading:"lazy"}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent py-8"})]},t):null})})]})}function u(){let[e,s]=(0,l.useState)(!1),[t,u]=(0,l.useState)(null),[p,b]=(0,l.useState)(!1),g={data:{html:{module:[{title:"机构介绍",list:[{text:"我们是一家专注于少儿艺术教育的专业机构，成立于2010年，拥有超过50位经验丰富的专业教师团队。我们致力于为6-18岁的青少年儿童提供高质量的艺术教育，课程涵盖素描、速写、水彩、油画等多个艺术领域。我们的教学场地配备专业设施，为学生提供舒适的学习环境。多年来，我们已培养了上千名学生，其中不少学生在各类艺术竞赛中获得优异成绩。",type:"text"},{img:"https://images.unsplash.com/photo-1577896851231-70ef18881754?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",type:"img"}]},{title:"课程介绍",list:[{text:"我们的速写课程采用全程小班教学，为不同年龄段的孩子提供从基础到进阶的系统化学习。通过观察、分析和表现，培养学生对物体结构、比例和空间关系的认知能力。特训教材由资深教师团队研发，紧贴少儿认知和艺术学习规律，引导学生掌握速写的核心技能。参与我们的课程将有效提升孩子的观察力、表现力和创造力，为艺术学习打下坚实基础。",type:"text"},{img:"https://image.cardmee.net/2/2021/05/image/1621906385171587078.png",type:"img"}]},{title:"预约说明",list:[{text:"1. 选择您方便的时间段进行预约\n2. 填写个人信息，提交预约申请\n3. 预约成功后，我们会短信通知确认\n4. 如需更改或取消预约，请提前24小时联系客服\n5. 试听课程请准时到达，迟到超过15分钟视为自动放弃\n6. 每人限预约1次免费试听课程",type:"text"}]}],audioUrl:"https://image.cardmee.net/540/2020/04/voice/0d3fb888-f41a-4021-8e2d-22a93b3150e5.mp3"},options:{cover:"https://images.unsplash.com/photo-1602934585418-f588a6c595ca?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",name:"少儿速写课程免费试听",shareDesc:"预约免费试听课，专业教师1对1指导",shareTitle:"少儿速写课程免费试听预约",buttons:[{text:"立即预约",type:"book",enabled:!0}],stats:[{value:283,label:"人查看"},{value:125,label:"人分享"},{value:68,label:"人预约"}],prices:{currentPrice:"19.99",originalPrice:"198.00",endTime:new Date().getTime()+6048e5},courses:[{name:"少儿速写基础入门课程",category:"美术类",description:"专为6-12岁儿童设计的速写入门课程，通过趣味教学激发孩子的艺术天赋。",image:"https://images.unsplash.com/photo-1543859147-380b036834c5?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",price:"0.00",originalPrice:"198.00",reviewCount:128,studentCount:1240}],teachers:[],availableTimes:[{id:"t1",date:"周六 (10月21日)",time:"10:00-11:30",available:!0},{id:"t2",date:"周六 (10月21日)",time:"14:00-15:30",available:!0},{id:"t3",date:"周日 (10月22日)",time:"10:00-11:30",available:!0},{id:"t4",date:"周日 (10月22日)",time:"14:00-15:30",available:!1},{id:"t5",date:"周六 (10月28日)",time:"10:00-11:30",available:!0},{id:"t6",date:"周六 (10月28日)",time:"14:00-15:30",available:!0}]}}},j=g.data.html,f=g.data.options,v=f.buttons||[];return(0,a.jsxs)("div",{className:"bg-gray-50 min-h-screen font-sans pb-20",children:[(0,a.jsx)("div",{className:"sticky top-0 z-50",children:(0,a.jsx)(i.A,{stats:f.stats,audioUrl:j.audioUrl})}),(0,a.jsx)("img",{src:f.cover,alt:"banner",className:"w-full h-48 object-cover"}),(0,a.jsxs)("div",{className:"flex items-start justify-between mt-2.5 px-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl text-red-500 font-bold",children:f.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-2.5",children:f.shareDesc})]}),(0,a.jsxs)("button",{onClick:()=>{s(!0)},className:"flex flex-col items-center text-gray-500 pt-1",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,a.jsx)("path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"}),(0,a.jsx)("polyline",{points:"16 6 12 2 8 6"}),(0,a.jsx)("line",{x1:"12",y1:"2",x2:"12",y2:"15"})]})}),(0,a.jsx)("span",{className:"text-xs mt-1",children:"分享"})]})]}),f.courses.map((e,s)=>(0,a.jsx)(d,{course:e},s)),(0,a.jsx)(c,{times:f.availableTimes,selectedTime:t,onSelectTime:e=>{u(e)}}),f.teachers&&f.teachers.length>0&&f.teachers.map((e,s)=>(0,a.jsx)(o,{teacher:e},s)),(0,a.jsx)(x,{currentPrice:f.prices.currentPrice,originalPrice:f.prices.originalPrice,endTime:f.prices.endTime}),j.module.map((e,s)=>(0,a.jsx)(h,{title:e.title,list:e.list},s)),(0,a.jsx)("div",{className:"fixed inset-x-0 bottom-0 bg-white shadow-lg flex justify-around py-2.5",children:(0,a.jsx)(r.A,{buttons:v,onAction:e=>{"book"===e&&(t?b(!0):alert("请先选择预约时间"))}})}),e&&(0,a.jsx)(n.A,{open:e,onClose:()=>s(!1),shareTitle:f.shareTitle,shareDesc:f.shareDesc,qrCode:"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=".concat(encodeURIComponent(window.location.href))}),p&&(0,a.jsx)(m,{open:p,onClose:()=>b(!1),onSubmit:e=>{console.log("预约数据:",{...e,timeId:t}),alert("预约成功！我们会尽快与您联系确认。"),b(!1)},selectedTime:t,times:f.availableTimes})]})}},29733:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var a=t(95155);function l(e){let{buttons:s,onAction:t}=e;return(0,a.jsx)("div",{className:"flex justify-around my-4",children:s.map(e=>(0,a.jsx)("button",{disabled:!e.enabled,className:"bg-red-500 text-white rounded-full px-8 py-2.5 text-lg font-medium shadow-md transition-all hover:bg-red-600 active:bg-red-700 ".concat(e.enabled?"":"opacity-50 cursor-not-allowed"),onClick:()=>e.enabled&&t(e.type),children:e.text},e.type))})}t(12115)},56904:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(95155);t(12115);var l=t(66766),r=t(54416);function i(e){let{open:s,onClose:t,qrCode:i,shareTitle:n,shareDesc:d}=e;return s?(0,a.jsx)("div",{className:"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl w-full max-w-sm relative",children:[(0,a.jsx)("button",{onClick:t,className:"absolute top-2 right-2 text-gray-400 hover:text-gray-600 p-1",children:(0,a.jsx)(r.A,{size:24})}),(0,a.jsxs)("div",{className:"pt-6 pb-4 px-6 text-center border-b border-gray-100",children:[(0,a.jsx)("h3",{className:"text-xl font-bold",children:"分享给好友"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"邀请好友一起参与"})]}),(0,a.jsx)("div",{className:"grid grid-cols-4 gap-2 p-6",children:[{icon:"/icons/wechat.svg",name:"微信",color:"bg-green-500"},{icon:"/icons/moments.svg",name:"朋友圈",color:"bg-green-600"},{icon:"/icons/qq.svg",name:"QQ",color:"bg-blue-400"},{icon:"/icons/weibo.svg",name:"微博",color:"bg-red-500"}].map(e=>(0,a.jsxs)("button",{className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 ".concat(e.color," rounded-full flex items-center justify-center mb-1 text-white"),children:e.icon?(0,a.jsx)(l.default,{src:e.icon,alt:e.name,width:24,height:24}):e.name.charAt(0)}),(0,a.jsx)("span",{className:"text-xs",children:e.name})]},e.name))}),i&&(0,a.jsxs)("div",{className:"px-6 pb-6 flex flex-col items-center",children:[(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-2 mb-2",children:(0,a.jsx)("img",{src:i,alt:"二维码",className:"w-40 h-40 object-contain"})}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"扫描二维码参与活动"})]}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-b-2xl border-t border-gray-100",children:(0,a.jsxs)("div",{className:"bg-white p-3 rounded-lg border border-gray-200",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:n}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:d})]})})]})}):null}},63061:(e,s,t)=>{Promise.resolve().then(t.bind(t,14791))},66358:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(95155),l=t(12115);let r=(0,t(19946).A)("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]]);function i(e){let{stats:s,audioUrl:t}=e,i=(0,l.useRef)(null),[n,d]=(0,l.useState)(!1);return(0,a.jsxs)("div",{className:"flex items-center bg-gradient-to-r from-blue-50 to-pink-50 px-3 py-1.5 text-gray-800 rounded-xl shadow-md sticky top-0 left-0 w-full z-30 min-h-[44px] overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute -right-8 -top-8 w-24 h-24 bg-pink-200 rounded-full opacity-20"}),(0,a.jsx)("div",{className:"absolute -left-4 -bottom-8 w-16 h-16 bg-blue-200 rounded-full opacity-20"})]}),(0,a.jsx)("button",{type:"button",className:"relative mr-3 flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 active:from-blue-500 active:to-purple-500 transition shadow-md z-10 ".concat(n?"animate-spin-slow":""),onClick:()=>{i.current&&(n?i.current.pause():i.current.play())},"aria-label":"播放/暂停音频",children:(0,a.jsx)(r,{size:18,className:"text-white"})}),(0,a.jsx)("audio",{ref:i,src:t,onPlay:()=>d(!0),onPause:()=>d(!1),onEnded:()=>d(!1),className:"hidden"}),(0,a.jsx)("div",{className:"flex flex-1 justify-between space-x-3 relative z-10",children:s.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-pink-500 font-bold text-lg",children:e.value}),(0,a.jsx)("span",{className:"text-gray-600 text-sm ml-0.5",children:e.label})]},s))}),(0,a.jsx)("style",{children:"\n          .animate-spin-slow {\n            animation: spin 3s linear infinite;\n          }\n          @keyframes spin {\n            100% { transform: rotate(360deg); }\n          }\n        "})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[9209,6315,7358],()=>s(63061)),_N_E=e.O()}]);