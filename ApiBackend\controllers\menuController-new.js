import { INTERNAL_ERROR, AUTH_ERROR, NOT_FOUND_ERROR, FORBIDDEN_ERROR } from '../errors/index.js';
import { menuService } from '../services/menuService-new.js';

/**
 * Menu Controller
 * Handles HTTP requests and responses for menu-related endpoints
 */
export const menuController = {
    /**
     * Get user menus
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async getUserMenus(request, reply) {
        try {
            const user = request.user;
            
            // Call service to get user menus
            const menuTree = await menuService.getUserMenus({
                user,
                fastify: request.server
            });
       
            // Return success response
            return reply.success({
                data: menuTree,
                message: '获取用户菜单成功'
            });
        } catch (error) {
            request.log.error({
                msg: '获取用户菜单失败',
                error: error.message,
                stack: error.stack,
                userId: request.user?.id
            });
            
            if (error instanceof AUTH_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('获取用户菜单失败');
        }
    },
    
    /**
     * Get all menus
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async getAllMenus(request, reply) {
        try {
            const user = request.user;
            
            // Call service to get all menus
            const menuTree = await menuService.getAllMenus({
                user,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: menuTree,
                message: '获取菜单成功'
            });
        } catch (error) {
            request.log.error({
                msg: '获取菜单失败',
                error: error.message,
                stack: error.stack,
                userId: request.user?.id
            });
            
            if (error instanceof AUTH_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('获取菜单失败');
        }
    },
    
    /**
     * Get role menus
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async getRoleMenus(request, reply) {
        try {
            const { roleId } = request.params;
            const user = request.user;
            
            // Call service to get role menus
            const menuTree = await menuService.getRoleMenus({
                roleId,
                user,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: menuTree,
                message: '获取角色菜单成功'
            });
        } catch (error) {
            request.log.error({
                msg: '获取角色菜单失败',
                error: error.message,
                stack: error.stack,
                params: request.params,
                userId: request.user?.id
            });
            
            if (error instanceof AUTH_ERROR) {
                throw error;
            }
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('获取角色菜单失败');
        }
    },
    
    /**
     * Update role menus
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async updateRoleMenus(request, reply) {
        try {
            const { roleId } = request.params;
            const { menuIds } = request.body;
            const user = request.user;
            
            // Call service to update role menus
            await menuService.updateRoleMenus({
                roleId,
                menuIds,
                user,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                message: '更新角色菜单成功'
            });
        } catch (error) {
            request.log.error({
                msg: '更新角色菜单失败',
                error: error.message,
                stack: error.stack,
                params: request.params,
                body: request.body,
                userId: request.user?.id
            });
            
            if (error instanceof AUTH_ERROR) {
                throw error;
            }
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('更新角色菜单失败');
        }
    },
    
    /**
     * Create menu
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async createMenu(request, reply) {
        try {
            const menuData = request.body;
            const user = request.user;
            
            // Call service to create menu
            const result = await menuService.createMenu({
                menuData,
                user,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: result,
                message: '创建菜单成功'
            });
        } catch (error) {
            request.log.error({
                msg: '创建菜单失败',
                error: error.message,
                stack: error.stack,
                body: request.body,
                userId: request.user?.id
            });
            
            if (error instanceof AUTH_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('创建菜单失败');
        }
    },
    
    /**
     * Update menu
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async updateMenu(request, reply) {
        try {
            const { menuId } = request.params;
            const menuData = request.body;
            const user = request.user;
            
            // Call service to update menu
            await menuService.updateMenu({
                menuId,
                menuData,
                user,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                message: '更新菜单成功'
            });
        } catch (error) {
            request.log.error({
                msg: '更新菜单失败',
                error: error.message,
                stack: error.stack,
                params: request.params,
                body: request.body,
                userId: request.user?.id
            });
            
            if (error instanceof AUTH_ERROR) {
                throw error;
            }
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('更新菜单失败');
        }
    },
    
    /**
     * Delete menu
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async deleteMenu(request, reply) {
        try {
            const { menuId } = request.params;
            const user = request.user;
            
            // Call service to delete menu
            await menuService.deleteMenu({
                menuId,
                user,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                message: '删除菜单成功'
            });
        } catch (error) {
            request.log.error({
                msg: '删除菜单失败',
                error: error.message,
                stack: error.stack,
                params: request.params,
                userId: request.user?.id
            });
            
            if (error instanceof AUTH_ERROR) {
                throw error;
            }
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof FORBIDDEN_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('删除菜单失败');
        }
    }
};

export default menuController;
