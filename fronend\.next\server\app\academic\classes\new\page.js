(()=>{var e={};e.id=950,e.ids=[950],e.modules={1793:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(60687),r=t(85726),l=t(44493);function n(){return(0,a.jsx)("div",{children:(0,a.jsxs)(l.Zp,{className:"border border-slate-200 shadow-sm overflow-hidden rounded-md",children:[(0,a.jsxs)(l.aR,{className:"bg-slate-50 px-6 py-4 border-b border-slate-200",children:[(0,a.jsx)(r.E,{className:"h-6 w-32"}),(0,a.jsx)(r.E,{className:"h-4 w-64 mt-1"})]}),(0,a.jsxs)(l<PERSON><PERSON>,{className:"p-6 space-y-8",children:[(0,a.jsxs)("section",{className:"space-y-4",children:[(0,a.jsx)(r.E,{className:"h-5 w-40 mb-3"}),(0,a.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-2",children:[(0,a.jsx)(r.E,{className:"h-10"}),(0,a.jsx)(r.E,{className:"h-10"}),(0,a.jsx)(r.E,{className:"h-10"}),(0,a.jsx)(r.E,{className:"h-10"})]})]}),(0,a.jsxs)("section",{className:"space-y-4",children:[(0,a.jsx)(r.E,{className:"h-5 w-40 mb-3"}),(0,a.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-2",children:[(0,a.jsx)(r.E,{className:"h-10"}),(0,a.jsx)(r.E,{className:"h-10"})]}),(0,a.jsx)(r.E,{className:"h-24"})]}),(0,a.jsxs)("section",{className:"space-y-4",children:[(0,a.jsx)(r.E,{className:"h-5 w-40 mb-3"}),(0,a.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-2",children:[(0,a.jsx)(r.E,{className:"h-10"}),(0,a.jsx)(r.E,{className:"h-10"})]})]}),(0,a.jsxs)("section",{className:"space-y-4",children:[(0,a.jsx)(r.E,{className:"h-5 w-40 mb-3"}),(0,a.jsx)(r.E,{className:"h-10"})]}),(0,a.jsxs)("section",{className:"space-y-4",children:[(0,a.jsx)(r.E,{className:"h-5 w-40 mb-3"}),(0,a.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-2",children:[(0,a.jsx)(r.E,{className:"h-10"}),(0,a.jsx)(r.E,{className:"h-10"}),(0,a.jsx)(r.E,{className:"h-10"})]})]}),(0,a.jsx)(r.E,{className:"h-10 w-full mt-4"})]})]})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13720:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\academic\\\\classes\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\classes\\loading.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21050:(e,s,t)=>{Promise.resolve().then(t.bind(t,1793))},21508:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\academic\\\\classes\\\\new\\\\components\\\\NewClassForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\classes\\new\\components\\NewClassForm.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32082:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,metadata:()=>l});var a=t(37413),r=t(21508);let l={title:`新增班级 - 蜜卡`};function n(){return(0,a.jsx)(r.default,{})}},33873:e=>{"use strict";e.exports=require("path")},36914:(e,s,t)=>{Promise.resolve().then(t.bind(t,93890))},42505:(e,s,t)=>{Promise.resolve().then(t.bind(t,74930))},48754:(e,s,t)=>{"use strict";t.d(s,{V:()=>o});var a=t(60687);t(43210);var r=t(47033),l=t(14952),n=t(13971),c=t(4780),d=t(29523);function o({className:e,classNames:s,showOutsideDays:t=!0,...o}){return(0,a.jsx)(n.hv,{showOutsideDays:t,className:(0,c.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,c.cn)((0,d.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,c.cn)((0,d.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...s},components:{IconLeft:({className:e,...s})=>(0,a.jsx)(r.A,{className:(0,c.cn)("h-4 w-4",e),...s}),IconRight:({className:e,...s})=>(0,a.jsx)(l.A,{className:(0,c.cn)("h-4 w-4",e),...s})},...o})}o.displayName="Calendar"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57842:(e,s,t)=>{Promise.resolve().then(t.bind(t,85147))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69918:(e,s,t)=>{"use strict";t.d(s,{z:()=>F,C:()=>V});var a=t(60687),r=t(43210),l=t(70569),n=t(98599),c=t(11273),d=t(14163),o=t(72942),i=t(65551),m=t(43),x=t(18853),u=t(83721),h=t(46059),p="Radio",[j,f]=(0,c.A)(p),[b,v]=j(p),N=r.forwardRef((e,s)=>{let{__scopeRadio:t,name:c,checked:o=!1,required:i,disabled:m,value:x="on",onCheck:u,form:h,...p}=e,[j,f]=r.useState(null),v=(0,n.s)(s,e=>f(e)),N=r.useRef(!1),g=!j||h||!!j.closest("form");return(0,a.jsxs)(b,{scope:t,checked:o,disabled:m,children:[(0,a.jsx)(d.sG.button,{type:"button",role:"radio","aria-checked":o,"data-state":C(o),"data-disabled":m?"":void 0,disabled:m,value:x,...p,ref:v,onClick:(0,l.m)(e.onClick,e=>{o||u?.(),g&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})}),g&&(0,a.jsx)(w,{control:j,bubbles:!N.current,name:c,value:x,checked:o,required:i,disabled:m,form:h,style:{transform:"translateX(-100%)"}})]})});N.displayName=p;var g="RadioIndicator",y=r.forwardRef((e,s)=>{let{__scopeRadio:t,forceMount:r,...l}=e,n=v(g,t);return(0,a.jsx)(h.C,{present:r||n.checked,children:(0,a.jsx)(d.sG.span,{"data-state":C(n.checked),"data-disabled":n.disabled?"":void 0,...l,ref:s})})});y.displayName=g;var w=e=>{let{control:s,checked:t,bubbles:l=!0,...n}=e,c=r.useRef(null),d=(0,u.Z)(t),o=(0,x.X)(s);return r.useEffect(()=>{let e=c.current,s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==t&&s){let a=new Event("click",{bubbles:l});s.call(e,t),e.dispatchEvent(a)}},[d,t,l]),(0,a.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:t,...n,tabIndex:-1,ref:c,style:{...e.style,...o,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function C(e){return e?"checked":"unchecked"}var R=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],k="RadioGroup",[z,E]=(0,c.A)(k,[o.RG,f]),I=(0,o.RG)(),_=f(),[M,T]=z(k),A=r.forwardRef((e,s)=>{let{__scopeRadioGroup:t,name:r,defaultValue:l,value:n,required:c=!1,disabled:x=!1,orientation:u,dir:h,loop:p=!0,onValueChange:j,...f}=e,b=I(t),v=(0,m.jH)(h),[N,g]=(0,i.i)({prop:n,defaultProp:l,onChange:j});return(0,a.jsx)(M,{scope:t,name:r,required:c,disabled:x,value:N,onValueChange:g,children:(0,a.jsx)(o.bL,{asChild:!0,...b,orientation:u,dir:v,loop:p,children:(0,a.jsx)(d.sG.div,{role:"radiogroup","aria-required":c,"aria-orientation":u,"data-disabled":x?"":void 0,dir:v,...f,ref:s})})})});A.displayName=k;var S="RadioGroupItem",P=r.forwardRef((e,s)=>{let{__scopeRadioGroup:t,disabled:c,...d}=e,i=T(S,t),m=i.disabled||c,x=I(t),u=_(t),h=r.useRef(null),p=(0,n.s)(s,h),j=i.value===d.value,f=r.useRef(!1);return r.useEffect(()=>{let e=e=>{R.includes(e.key)&&(f.current=!0)},s=()=>f.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",s),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",s)}},[]),(0,a.jsx)(o.q7,{asChild:!0,...x,focusable:!m,active:j,children:(0,a.jsx)(N,{disabled:m,required:i.required,checked:j,...u,...d,name:i.name,ref:p,onCheck:()=>i.onValueChange(d.value),onKeyDown:(0,l.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,l.m)(d.onFocus,()=>{f.current&&h.current?.click()})})})});P.displayName=S;var J=r.forwardRef((e,s)=>{let{__scopeRadioGroup:t,...r}=e,l=_(t);return(0,a.jsx)(y,{...l,...r,ref:s})});J.displayName="RadioGroupIndicator";var q=t(65822),B=t(4780);let F=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(A,{className:(0,B.cn)("grid gap-2",e),...s,ref:t}));F.displayName=A.displayName;let V=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(P,{ref:t,className:(0,B.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:(0,a.jsx)(J,{className:"flex items-center justify-center",children:(0,a.jsx)(q.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));V.displayName=P.displayName},73866:(e,s,t)=>{Promise.resolve().then(t.bind(t,13720))},74075:e=>{"use strict";e.exports=require("zlib")},74930:(e,s,t)=>{"use strict";t.d(s,{default:()=>F});var a=t(60687),r=t(27605),l=t(63442),n=t(71669),c=t(29523),d=t(44493),o=t(84778),i=t(89667),m=t(83066),x=t(60811);function u({form:e}){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(n.zB,{control:e.control,name:"name",render:({field:e})=>(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"班级名称"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(i.p,{placeholder:"请输入班级名称",className:"h-10 border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 text-sm bg-slate-50 hover:bg-white transition-colors",...e})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(n.zB,{control:e.control,name:"courseId",render:({field:e})=>(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"课程"}),(0,a.jsx)(m.A,{value:e.value,onChange:e.onChange}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}),(0,a.jsx)(n.zB,{control:e.control,name:"maxStudentCount",render:({field:e})=>(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"最多人数"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(i.p,{placeholder:"班级最多学员人数",className:"h-10 border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 text-sm bg-slate-50 hover:bg-white transition-colors",...e})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})})]}),(0,a.jsx)(n.zB,{control:e.control,name:"classroom",render:({field:e})=>(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"教室选择"}),(0,a.jsx)(x.A,{value:e.value||"",onChange:e.onChange}),(0,a.jsx)(n.C5,{className:"text-xs"})]})})]})}var h=t(69918),p=t(40988),j=t(48754),f=t(40228),b=t(4780),v=t(76869),N=t(52595),g=t(80013),y=t(96474),w=t(43210);let C=({value:e=[],onChange:s})=>{let t=[{label:"周一",value:1},{label:"周二",value:2},{label:"周三",value:3},{label:"周四",value:4},{label:"周五",value:5},{label:"周六",value:6},{label:"周日",value:0}],[r,l]=(0,w.useState)(null),[n,d]=(0,w.useState)(!1),o=s=>e.some(e=>e.day===s),i=s=>e.find(e=>e.day===s),m=e=>{l(e),d(!0)},x=(t,a,r)=>{let l=[...e],n=l.findIndex(e=>e.day===t);-1!==n?l[n]={day:t,startTime:a,endTime:r}:l.push({day:t,startTime:a,endTime:r}),s?.(l),d(!1)},u=t=>{let a=e.filter(e=>e.day!==t);s?.(a)};return(0,a.jsxs)("div",{className:"rounded-md overflow-hidden border border-slate-200",children:[(0,a.jsx)("div",{className:"grid grid-cols-7",children:t.map(e=>(0,a.jsx)("div",{className:"text-center py-2.5 bg-slate-50 text-slate-600 font-medium text-sm border-b border-slate-200",children:e.label},e.value))}),(0,a.jsx)("div",{className:"grid grid-cols-7",children:t.map(e=>{let s=i(e.value),t=o(e.value);return(0,a.jsxs)("div",{className:(0,b.cn)("flex flex-col items-center py-4 transition-colors",t?"bg-slate-50":"hover:bg-slate-50/50"),children:[(0,a.jsxs)(p.AM,{open:n&&r===e.value,onOpenChange:e=>{e||d(!1)},children:[(0,a.jsx)(p.Wv,{asChild:!0,children:(0,a.jsx)("div",{onClick:()=>m(e.value),className:(0,b.cn)("h-8 w-8 rounded-full flex items-center justify-center cursor-pointer transition-colors",t?"bg-slate-700 text-white hover:bg-slate-600":"border border-slate-300 text-slate-500 hover:border-slate-400 hover:text-slate-600"),children:t?(0,a.jsx)("span",{className:"text-xs font-medium",children:s?.startTime?.substring(0,2)}):(0,a.jsx)(y.A,{className:"h-3.5 w-3.5"})})}),(0,a.jsx)(p.hl,{className:"w-72 p-3 shadow-sm",align:"center",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h4",{className:"font-medium text-sm text-slate-700",children:[e.label,"课程时间"]}),(0,a.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",className:"h-7 px-2 text-xs text-slate-500 hover:text-slate-700 hover:bg-slate-100",onClick:()=>u(e.value),children:"删除"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsx)(g.J,{htmlFor:`start-time-${e.value}`,className:"text-xs text-slate-500",children:"开始时间"}),(0,a.jsx)("input",{id:`start-time-${e.value}`,type:"time",defaultValue:s?.startTime||"08:00",className:"w-full h-8 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"})]}),(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsx)(g.J,{htmlFor:`end-time-${e.value}`,className:"text-xs text-slate-500",children:"结束时间"}),(0,a.jsx)("input",{id:`end-time-${e.value}`,type:"time",defaultValue:s?.endTime||"09:30",className:"w-full h-8 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"})]})]}),(0,a.jsx)(c.$,{type:"button",className:"w-full h-8 text-sm bg-slate-800 hover:bg-slate-700",onClick:()=>{let s=document.getElementById(`start-time-${e.value}`),t=document.getElementById(`end-time-${e.value}`);x(e.value,s.value,t.value)},children:"确定"})]})})]}),s&&(0,a.jsxs)("div",{className:"text-xs mt-2 text-slate-600",children:[s.startTime," - ",s.endTime]})]},e.value)})})]})};function R({form:e}){let s=e.watch("recurrenceType"),t=e.watch("endType");return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(n.zB,{control:e.control,name:"recurrenceType",render:({field:e})=>(0,a.jsxs)(n.eI,{className:"space-y-3",children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"循环排课"}),(0,a.jsx)(n.MJ,{children:(0,a.jsxs)(h.z,{onValueChange:e.onChange,defaultValue:e.value,className:"flex flex-col space-y-2",children:[(0,a.jsxs)(n.eI,{className:"flex items-center space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors",children:[(0,a.jsx)(n.MJ,{children:(0,a.jsx)(h.C,{value:"weekly",className:"text-slate-700"})}),(0,a.jsx)(n.lR,{className:"font-normal text-slate-700 cursor-pointer w-full text-sm",children:"每周重复"})]}),(0,a.jsxs)(n.eI,{className:"flex items-center space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors",children:[(0,a.jsx)(n.MJ,{children:(0,a.jsx)(h.C,{value:"daily",className:"text-slate-700"})}),(0,a.jsx)(n.lR,{className:"font-normal text-slate-700 cursor-pointer w-full text-sm",children:"每天重复"})]})]})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}),"weekly"===s&&(0,a.jsx)("div",{className:"p-4 border rounded-md border-slate-200",children:(0,a.jsx)(n.zB,{control:e.control,name:"weekdays",render:({field:e})=>(0,a.jsxs)(n.eI,{className:"pt-0",children:[(0,a.jsx)(n.lR,{className:"text-sm text-slate-700 mb-3 block font-medium",children:"选择上课日期"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(C,{value:e.value||[],onChange:e.onChange})}),(0,a.jsx)(n.Rr,{className:"mt-3 text-slate-500 text-xs",children:"请选择每周上课的日期并设置时间"}),(0,a.jsx)(n.C5,{className:"text-xs"})]})})}),"daily"===s&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(n.zB,{control:e.control,name:"daily.startTime",render:({field:e})=>(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开始时间"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"time",placeholder:"选择开始时间",className:"w-full h-10 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:e.value||"",onChange:s=>e.onChange(s.target.value)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}),(0,a.jsx)(n.zB,{control:e.control,name:"daily.endTime",render:({field:e})=>(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"结束时间"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"time",placeholder:"选择结束时间",className:"w-full h-10 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:e.value||"",onChange:s=>e.onChange(s.target.value)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})})]}),(0,a.jsx)("div",{className:"pt-2",children:(0,a.jsx)(n.zB,{control:e.control,name:"endType",render:({field:s})=>(0,a.jsxs)(n.eI,{className:"space-y-3",children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"结束方式"}),(0,a.jsx)(n.MJ,{children:(0,a.jsxs)(h.z,{onValueChange:t=>{"number_of_times"===t&&e.setValue("endDate",void 0),s.onChange(t)},defaultValue:s.value,className:"flex flex-col space-y-2",children:[(0,a.jsxs)(n.eI,{className:"flex items-center space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors",children:[(0,a.jsx)(n.MJ,{children:(0,a.jsx)(h.C,{value:"times",className:"text-slate-700"})}),(0,a.jsx)(n.lR,{className:"font-normal text-slate-700 cursor-pointer w-full text-sm",children:"按时间"})]}),(0,a.jsxs)(n.eI,{className:"flex items-center space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors",children:[(0,a.jsx)(n.MJ,{children:(0,a.jsx)(h.C,{value:"number_of_times",className:"text-slate-700"})}),(0,a.jsx)(n.lR,{className:"font-normal text-slate-700 cursor-pointer w-full text-sm",children:"按次数"})]})]})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 pt-1",children:[(0,a.jsx)(n.zB,{control:e.control,name:"startDate",render:({field:e})=>(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开始日期"}),(0,a.jsxs)(p.AM,{children:[(0,a.jsx)(p.Wv,{asChild:!0,children:(0,a.jsx)(n.MJ,{children:(0,a.jsxs)("div",{className:(0,b.cn)("w-full flex items-center justify-between text-sm font-normal","border-b border-slate-200 py-2 text-slate-700","hover:border-slate-300 transition-colors cursor-pointer",!e.value&&"text-slate-500"),children:[e.value?(0,v.GP)(e.value,"yyyy年MM月dd日"):(0,a.jsx)("span",{children:"选择日期"}),(0,a.jsx)(f.A,{className:"h-4 w-4 opacity-50"})]})})}),(0,a.jsx)(p.hl,{className:"w-auto p-0 shadow-sm rounded-md",align:"start",children:(0,a.jsx)(j.V,{locale:N.g,mode:"single",selected:e.value,onSelect:s=>e.onChange(s),initialFocus:!0,className:"border-0"})})]}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}),"times"===t&&(0,a.jsx)(n.zB,{control:e.control,name:"endDate",render:({field:e})=>(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"结束日期"}),(0,a.jsxs)(p.AM,{children:[(0,a.jsx)(p.Wv,{asChild:!0,children:(0,a.jsx)(n.MJ,{children:(0,a.jsxs)("div",{className:(0,b.cn)("w-full flex items-center justify-between text-sm font-normal","border-b border-slate-200 py-2 text-slate-700","hover:border-slate-300 transition-colors cursor-pointer",!e.value&&"text-slate-500"),children:[e.value?(0,v.GP)(e.value,"yyyy年MM月dd日"):(0,a.jsx)("span",{children:"选择日期"}),(0,a.jsx)(f.A,{className:"h-4 w-4 opacity-50"})]})})}),(0,a.jsx)(p.hl,{className:"w-auto p-0 shadow-sm rounded-md",align:"start",children:(0,a.jsx)(j.V,{locale:N.g,mode:"single",selected:e.value,onSelect:s=>e.onChange(s),initialFocus:!0,className:"border-0"})})]}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}),"number_of_times"===t&&(0,a.jsx)(n.zB,{control:e.control,name:"times",render:({field:e})=>(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"课程次数"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"number",placeholder:"请输入课程次数",className:"w-full h-10 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:e.value??"",onChange:s=>e.onChange(Number.parseInt(s.target.value)||0)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})})]})]})}function k({form:e}){return(0,a.jsx)(n.zB,{control:e.control,name:"type",render:({field:e})=>(0,a.jsxs)(n.eI,{className:"space-y-3",children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"班级类型"}),(0,a.jsx)(n.MJ,{children:(0,a.jsxs)(h.z,{onValueChange:e.onChange,defaultValue:e.value,className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)(n.eI,{className:"flex items-start space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors",children:[(0,a.jsx)(n.MJ,{children:(0,a.jsx)(h.C,{value:"temporary",className:"mt-0.5 text-slate-700"})}),(0,a.jsxs)(n.lR,{className:"font-normal text-slate-700 cursor-pointer w-full text-sm",children:["临时班级",(0,a.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"适用于临时组织的短期课程"})]})]}),(0,a.jsxs)(n.eI,{className:"flex items-start space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors",children:[(0,a.jsx)(n.MJ,{children:(0,a.jsx)(h.C,{value:"fixed",className:"mt-0.5 text-slate-700"})}),(0,a.jsxs)(n.lR,{className:"font-normal text-slate-700 cursor-pointer w-full text-sm",children:["固定班级",(0,a.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"适用于长期固定的常规课程"})]})]})]})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})})}var z=t(27479),E=t(39907),I=t(295),_=t(84027),M=t(3589),T=t(78272);function A({form:e}){let[s,t]=(0,w.useState)(!1),r=e.watch("reservation.enabled"),l=e.watch("leave.enabled");return(0,a.jsxs)(I.Nt,{open:s,onOpenChange:t,className:"border border-slate-200 rounded-md shadow-sm overflow-hidden",children:[(0,a.jsxs)(I.R6,{className:"flex w-full items-center justify-between p-3 text-left hover:bg-slate-50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 text-slate-500"}),(0,a.jsx)("h3",{className:"text-sm font-medium text-slate-700",children:"高级选项"})]}),(0,a.jsx)("div",{className:"h-6 w-6 flex items-center justify-center text-slate-400",children:s?(0,a.jsx)(M.A,{className:"h-4 w-4"}):(0,a.jsx)(T.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)(I.Ke,{className:"px-4 pb-4 pt-2 space-y-5 border-t border-slate-200",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700 mt-2",children:"预约设置"}),(0,a.jsx)(n.zB,{control:e.control,name:"reservation.enabled",render:({field:e})=>(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between p-3 rounded-md border border-slate-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开放预约"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"允许学员预约此班级的课程"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(z.d,{checked:e.value,onCheckedChange:e.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}),r&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-3 pl-4 border-l border-slate-200",children:[(0,a.jsx)(n.zB,{control:e.control,name:"reservation.appointmentStartTime",render:({field:e})=>(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开放预约时间（小时）"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"课程开始前多少小时开放预约"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"number",placeholder:"如：24小时",className:"w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:e.value??"",onChange:s=>e.onChange(Number(s.target.value)||0)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}),(0,a.jsx)(n.zB,{control:e.control,name:"reservation.appointmentEndTime",render:({field:e})=>(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"截止预约时间（小时）"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"课程开始前多少小时停止预约"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"number",placeholder:"如：1小时",className:"w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:e.value??"",onChange:s=>e.onChange(Number(s.target.value)||0)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})})]})]}),(0,a.jsx)(E.w,{className:"bg-slate-200"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:"考勤选择"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,a.jsx)(n.zB,{control:e.control,name:"attendance.studentScan",render:({field:e})=>(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"学员扫码考勤"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"允许学员通过扫码进行课程签到"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(z.d,{checked:e.value,onCheckedChange:e.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}),(0,a.jsx)(n.zB,{control:e.control,name:"attendance.autoSystem",render:({field:e})=>(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"系统自动考勤"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"系统自动完成考勤流程"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(z.d,{checked:e.value,onCheckedChange:e.onChange,className:"data-[state=checked]:bg-slate-700"})})]})})]})]}),(0,a.jsx)(E.w,{className:"bg-slate-200"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:"请假选项"}),(0,a.jsx)(n.zB,{control:e.control,name:"leave.enabled",render:({field:e})=>(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between p-3 rounded-md border border-slate-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开放请假"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"允许学员请假"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(z.d,{checked:e.value,onCheckedChange:e.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}),l&&(0,a.jsx)("div",{className:"pl-4 border-l border-slate-200",children:(0,a.jsx)(n.zB,{control:e.control,name:"leave.leaveDeadline",render:({field:e})=>(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"请假截止时间（小时）"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"课程开始前多少小时停止请假"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"number",placeholder:"如：2小时",className:"w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:e.value??"",onChange:s=>e.onChange(Number(s.target.value)||0)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})})})]}),(0,a.jsx)(E.w,{className:"bg-slate-200"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:"周期设置"}),(0,a.jsx)(n.zB,{control:e.control,name:"isShowWeekCount",render:({field:e})=>(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"启用周期数"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"按周期划分课程，例如：第一周期、第二周期等"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(z.d,{checked:e.value,onCheckedChange:e.onChange,className:"data-[state=checked]:bg-slate-700"})})]})})]})]})]})}var S=t(45880);let P=S.z.object({name:S.z.string().min(1,"请输入班级名称"),courseId:S.z.string().min(1,"请选择课程包"),teacherId:S.z.string().min(1,"请选择主讲老师"),classroom:S.z.null().optional(),recurrenceType:S.z.enum(["weekly","daily"]),weekdays:S.z.array(S.z.object({day:S.z.number(),startTime:S.z.string(),endTime:S.z.string()})).optional(),daily:S.z.object({startTime:S.z.string().optional(),endTime:S.z.string().optional()}).optional(),endType:S.z.enum(["times","number_of_times"]),startDate:S.z.date().optional(),endDate:S.z.date().optional(),times:S.z.number().optional(),maxStudentCount:S.z.string().min(1,"请输入最大人数"),type:S.z.enum(["temporary","fixed"]),reservation:S.z.object({enabled:S.z.boolean(),appointmentStartTime:S.z.number().min(0,"不能小于0"),appointmentEndTime:S.z.number().min(0,"不能小于0")}),attendance:S.z.object({studentScan:S.z.boolean(),autoSystem:S.z.boolean()}),leave:S.z.object({enabled:S.z.boolean(),leaveDeadline:S.z.number().min(0,"不能小于0")}),isShowWeekCount:S.z.boolean()});var J=t(15079),q=t(88397);let B=function({form:e}){let[s,t]=(0,w.useState)(!1),[r,l]=(0,w.useState)([]),[c]=(0,q.Kp)();return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:" pb-2 space-y-3",children:[(0,a.jsx)(n.lR,{className:"text-sm",children:"主讲老师"}),(0,a.jsxs)(J.l6,{onValueChange:s=>e.setValue("teacherId",s),children:[(0,a.jsx)(J.bq,{className:"w-full h-10 text-sm border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 bg-transparent",children:(0,a.jsx)(J.yv,{placeholder:"选择主讲老师"})}),(0,a.jsx)(J.gC,{children:0===r.length?(0,a.jsx)("div",{className:"py-6 text-center text-sm text-slate-500",children:"暂无教师数据"}):r.map(e=>(0,a.jsxs)(J.eb,{value:e.id,className:"text-sm",children:[e.name," ",e.freeTime?(0,a.jsx)("span",{className:"text-xs text-green-500",children:"(有空)"}):(0,a.jsx)("span",{className:"text-xs text-red-500",children:"(无空)"})]},e.id))})]})]})})};function F(){let[e]=(0,q.Zj)(),s=(0,r.mN)({resolver:(0,l.u)(P),defaultValues:{name:"",recurrenceType:"weekly",weekdays:[],endType:"number_of_times",maxStudentCount:"",type:"fixed",reservation:{enabled:!1,appointmentStartTime:24,appointmentEndTime:1},attendance:{studentScan:!1,autoSystem:!1},leave:{enabled:!1,leaveDeadline:2},isShowWeekCount:!1}});return(0,a.jsx)("div",{children:(0,a.jsxs)(d.Zp,{className:"border border-slate-200 shadow-sm overflow-hidden rounded-md",children:[(0,a.jsxs)(d.aR,{className:"bg-slate-50 px-6 py-4 border-b border-slate-200",children:[(0,a.jsx)(d.ZB,{className:"text-lg font-medium text-slate-800",children:"班级排课"}),(0,a.jsx)(d.BT,{className:"text-slate-500 mt-1 text-sm",children:"创建新的班级并安排课程时间"})]}),(0,a.jsx)(d.Wu,{className:"p-6 space-y-8",children:(0,a.jsx)(n.lV,{...s,children:(0,a.jsxs)("form",{onSubmit:s.handleSubmit(function(s){e({endType:s.endType,startDate:s.startDate?.getTime(),endDate:s.endDate?.getTime(),name:s.name,courseId:s.courseId,daily:s.daily,teacherId:s.teacherId,classroomId:s.classroom&&"none"!==s.classroom?s.classroom:null,recurrenceType:s.recurrenceType,weekdays:s.weekdays,times:s.times,type:s.type,maxStudentCount:s.maxStudentCount,reservation:s.reservation,attendance:s.attendance,leave:s.leave,isShowWeekCount:s.isShowWeekCount}).then(e=>{console.log(e),o.l.success("班级创建成功.")})}),className:"space-y-8",children:[(0,a.jsx)("section",{children:(0,a.jsx)(u,{form:s})}),(0,a.jsx)("section",{children:(0,a.jsx)(R,{form:s})}),(0,a.jsx)("section",{children:(0,a.jsx)(k,{form:s})}),(0,a.jsx)("section",{children:(0,a.jsx)(B,{form:s})}),(0,a.jsx)("section",{children:(0,a.jsx)(A,{form:s})}),(0,a.jsx)("div",{className:"pt-2",children:(0,a.jsx)(c.$,{type:"submit",className:"w-full h-10 text-sm font-medium bg-slate-800 hover:bg-slate-700 transition-colors rounded-md",children:"创建班级"})})]})})})]})})}},77353:(e,s,t)=>{Promise.resolve().then(t.bind(t,21508))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85147:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\academic\\\\classes\\\\new\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\classes\\new\\loading.tsx","default")},85473:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>i,routeModule:()=>x,tree:()=>o});var a=t(65239),r=t(48088),l=t(88170),n=t.n(l),c=t(30893),d={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);t.d(s,d);let o={children:["",{children:["academic",{children:["classes",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,32082)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\classes\\new\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,85147)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\classes\\new\\loading.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,13720)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\classes\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21843)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,i=["F:\\trae\\cardmees\\fronend\\src\\app\\academic\\classes\\new\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/academic/classes/new/page",pathname:"/academic/classes/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},93890:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(60687),r=t(85726);function l(){return(0,a.jsxs)("div",{className:"space-y-4 p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)(r.E,{className:"h-8 w-32"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(r.E,{className:"h-10 w-28"}),(0,a.jsx)(r.E,{className:"h-10 w-28"})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-3 mb-4",children:[(0,a.jsx)(r.E,{className:"h-10 w-48"}),(0,a.jsx)(r.E,{className:"h-10 w-48"}),(0,a.jsx)(r.E,{className:"h-10 w-24"})]}),(0,a.jsxs)("div",{className:"border rounded-md",children:[(0,a.jsx)("div",{className:"flex border-b p-2 bg-muted/30",children:Array(6).fill(0).map((e,s)=>(0,a.jsx)(r.E,{className:"h-6 flex-1 mx-2"},s))}),Array(8).fill(0).map((e,s)=>(0,a.jsx)("div",{className:"flex border-b p-3",children:Array(6).fill(0).map((e,s)=>(0,a.jsx)(r.E,{className:"h-5 flex-1 mx-2"},s))},s))]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,a.jsx)(r.E,{className:"h-8 w-40"}),(0,a.jsx)(r.E,{className:"h-8 w-64"})]})]})}},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,7392,5814,3928,3443,2951,6869,1991,7605,5114,3019,9879,1530],()=>t(85473));module.exports=a})();