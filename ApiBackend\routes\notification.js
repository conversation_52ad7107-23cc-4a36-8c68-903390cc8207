import { NotificationController } from '../controllers/notification.js';
import notificationSchemas from '../schemas/notification.js';

export default async function (fastify, options) {
  // 创建通知
  fastify.post('/notifications', {
    schema: notificationSchemas.createNotificationSchema,
    onRequest: [fastify.auth.authenticate],
    handler: NotificationController.createNotification
  });

  // 获取通知列表
  fastify.get('/notifications', {
    schema: notificationSchemas.getNotificationsQuerySchema,
    onRequest: [fastify.auth.authenticate],
    handler: NotificationController.getNotifications
  });

  // 获取通知详情
  fastify.get('/notifications/:id', {
    schema: notificationSchemas.getNotificationParamsSchema,
    onRequest: [fastify.auth.authenticate],
    handler: NotificationController.getNotificationDetail
  });

  // 获取未读通知数量
  fastify.get('/notifications/unreadCount', {
    schema: notificationSchemas.getUnreadCountSchema,
    onRequest: [fastify.auth.authenticate],
    handler: NotificationController.getUnreadCount
  });

  // 批量标记通知为已读
  fastify.post('/notifications/read', {
    schema: notificationSchemas.readNotificationsSchema,
    onRequest: [fastify.auth.authenticate],
    handler: NotificationController.markAsRead
  });

  // 标记所有通知为已读
  fastify.post('/notifications/readAll', {
    schema: notificationSchemas.readAllNotificationsSchema,
    onRequest: [fastify.auth.authenticate],
    handler: NotificationController.markAllAsRead
  });

  // 批量删除通知
  fastify.post('/notifications/delete', {
    schema: notificationSchemas.deleteNotificationsSchema,
    onRequest: [fastify.auth.authenticate],
    handler: NotificationController.deleteNotifications
  });
}