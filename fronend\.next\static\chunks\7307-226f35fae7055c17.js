"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7307],{381:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4884:(e,t,r)=>{r.d(t,{bL:()=>N,zi:()=>w});var n=r(12115),o=r(85185),a=r(6101),i=r(46081),l=r(5845),s=r(45503),u=r(11275),d=r(63655),c=r(95155),p="Switch",[f,v]=(0,i.A)(p),[h,m]=f(p),g=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:s,defaultChecked:u,required:p,disabled:f,value:v="on",onCheckedChange:m,form:g,...y}=e,[b,N]=n.useState(null),w=(0,a.s)(t,e=>N(e)),R=n.useRef(!1),j=!b||g||!!b.closest("form"),[O=!1,A]=(0,l.i)({prop:s,defaultProp:u,onChange:m});return(0,c.jsxs)(h,{scope:r,checked:O,disabled:f,children:[(0,c.jsx)(d.sG.button,{type:"button",role:"switch","aria-checked":O,"aria-required":p,"data-state":C(O),"data-disabled":f?"":void 0,disabled:f,value:v,...y,ref:w,onClick:(0,o.m)(e.onClick,e=>{A(e=>!e),j&&(R.current=e.isPropagationStopped(),R.current||e.stopPropagation())})}),j&&(0,c.jsx)(x,{control:b,bubbles:!R.current,name:i,value:v,checked:O,required:p,disabled:f,form:g,style:{transform:"translateX(-100%)"}})]})});g.displayName=p;var y="SwitchThumb",b=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=m(y,r);return(0,c.jsx)(d.sG.span,{"data-state":C(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});b.displayName=y;var x=e=>{let{control:t,checked:r,bubbles:o=!0,...a}=e,i=n.useRef(null),l=(0,s.Z)(r),d=(0,u.X)(t);return n.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(l!==r&&t){let n=new Event("click",{bubbles:o});t.call(e,r),e.dispatchEvent(n)}},[l,r,o]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...a,tabIndex:-1,ref:i,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function C(e){return e?"checked":"unchecked"}var N=g,w=b},20547:(e,t,r)=>{r.d(t,{UC:()=>q,ZL:()=>Z,bL:()=>V,l9:()=>z});var n=r(12115),o=r(85185),a=r(6101),i=r(46081),l=r(19178),s=r(92293),u=r(25519),d=r(61285),c=r(35152),p=r(34378),f=r(28905),v=r(63655),h=r(99708),m=r(5845),g=r(38168),y=r(93795),b=r(95155),x="Popover",[C,N]=(0,i.A)(x,[c.Bk]),w=(0,c.Bk)(),[R,j]=C(x),O=e=>{let{__scopePopover:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:l=!1}=e,s=w(t),u=n.useRef(null),[p,f]=n.useState(!1),[v=!1,h]=(0,m.i)({prop:o,defaultProp:a,onChange:i});return(0,b.jsx)(c.bL,{...s,children:(0,b.jsx)(R,{scope:t,contentId:(0,d.B)(),triggerRef:u,open:v,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:p,onCustomAnchorAdd:n.useCallback(()=>f(!0),[]),onCustomAnchorRemove:n.useCallback(()=>f(!1),[]),modal:l,children:r})})};O.displayName=x;var A="PopoverAnchor";n.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,a=j(A,r),i=w(r),{onCustomAnchorAdd:l,onCustomAnchorRemove:s}=a;return n.useEffect(()=>(l(),()=>s()),[l,s]),(0,b.jsx)(c.Mz,{...i,...o,ref:t})}).displayName=A;var P="PopoverTrigger",k=n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,i=j(P,r),l=w(r),s=(0,a.s)(t,i.triggerRef),u=(0,b.jsx)(v.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":G(i.open),...n,ref:s,onClick:(0,o.m)(e.onClick,i.onOpenToggle)});return i.hasCustomAnchor?u:(0,b.jsx)(c.Mz,{asChild:!0,...l,children:u})});k.displayName=P;var E="PopoverPortal",[D,M]=C(E,{forceMount:void 0}),T=e=>{let{__scopePopover:t,forceMount:r,children:n,container:o}=e,a=j(E,t);return(0,b.jsx)(D,{scope:t,forceMount:r,children:(0,b.jsx)(f.C,{present:r||a.open,children:(0,b.jsx)(p.Z,{asChild:!0,container:o,children:n})})})};T.displayName=E;var I="PopoverContent",_=n.forwardRef((e,t)=>{let r=M(I,e.__scopePopover),{forceMount:n=r.forceMount,...o}=e,a=j(I,e.__scopePopover);return(0,b.jsx)(f.C,{present:n||a.open,children:a.modal?(0,b.jsx)(F,{...o,ref:t}):(0,b.jsx)(U,{...o,ref:t})})});_.displayName=I;var F=n.forwardRef((e,t)=>{let r=j(I,e.__scopePopover),i=n.useRef(null),l=(0,a.s)(t,i),s=n.useRef(!1);return n.useEffect(()=>{let e=i.current;if(e)return(0,g.Eq)(e)},[]),(0,b.jsx)(y.A,{as:h.DX,allowPinchZoom:!0,children:(0,b.jsx)(S,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),s.current||null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;s.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),U=n.forwardRef((e,t)=>{let r=j(I,e.__scopePopover),o=n.useRef(!1),a=n.useRef(!1);return(0,b.jsx)(S,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(i=r.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,i;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let l=t.target;(null===(i=r.triggerRef.current)||void 0===i?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),S=n.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:i,onEscapeKeyDown:d,onPointerDownOutside:p,onFocusOutside:f,onInteractOutside:v,...h}=e,m=j(I,r),g=w(r);return(0,s.Oh)(),(0,b.jsx)(u.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,b.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:v,onEscapeKeyDown:d,onPointerDownOutside:p,onFocusOutside:f,onDismiss:()=>m.onOpenChange(!1),children:(0,b.jsx)(c.UC,{"data-state":G(m.open),role:"dialog",id:m.contentId,...g,...h,ref:t,style:{...h.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),L="PopoverClose";function G(e){return e?"open":"closed"}n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,a=j(L,r);return(0,b.jsx)(v.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})}).displayName=L,n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=w(r);return(0,b.jsx)(c.i3,{...o,...n,ref:t})}).displayName="PopoverArrow";var V=O,z=k,Z=T,q=_},28905:(e,t,r)=>{r.d(t,{C:()=>i});var n=r(12115),o=r(6101),a=r(52712),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[o,i]=n.useState(),s=n.useRef({}),u=n.useRef(e),d=n.useRef("none"),[c,p]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=l(s.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let t=s.current,r=u.current;if(r!==e){let n=d.current,o=l(t);e?p("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):r&&n!==o?p("ANIMATION_OUT"):p("UNMOUNT"),u.current=e}},[e,p]),(0,a.N)(()=>{if(o){var e;let t;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=l(s.current).includes(e.animationName);if(e.target===o&&n&&(p("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=l(s.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{e&&(s.current=getComputedStyle(e)),i(e)},[])}}(t),s="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),u=(0,o.s)(i.ref,function(e){var t,r;let n=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof r||i.isPresent?n.cloneElement(s,{ref:u}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},47863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},63655:(e,t,r)=>{r.d(t,{hO:()=>s,sG:()=>l});var n=r(12115),o=r(47650),a=r(99708),i=r(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...o}=e,l=n?a.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},66474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},84616:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},87489:(e,t,r)=>{r.d(t,{b:()=>u});var n=r(12115),o=r(63655),a=r(95155),i="horizontal",l=["horizontal","vertical"],s=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:s=i,...u}=e,d=(r=s,l.includes(r))?s:i;return(0,a.jsx)(o.sG.div,{"data-orientation":d,...n?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...u,ref:t})});s.displayName="Separator";var u=s},88106:(e,t,r)=>{r.d(t,{Ke:()=>N,R6:()=>x,bL:()=>j});var n=r(12115),o=r(85185),a=r(46081),i=r(5845),l=r(52712),s=r(6101),u=r(63655),d=r(28905),c=r(61285),p=r(95155),f="Collapsible",[v,h]=(0,a.A)(f),[m,g]=v(f),y=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:o,defaultOpen:a,disabled:l,onOpenChange:s,...d}=e,[f=!1,v]=(0,i.i)({prop:o,defaultProp:a,onChange:s});return(0,p.jsx)(m,{scope:r,disabled:l,contentId:(0,c.B)(),open:f,onOpenToggle:n.useCallback(()=>v(e=>!e),[v]),children:(0,p.jsx)(u.sG.div,{"data-state":R(f),"data-disabled":l?"":void 0,...d,ref:t})})});y.displayName=f;var b="CollapsibleTrigger",x=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,a=g(b,r);return(0,p.jsx)(u.sG.button,{type:"button","aria-controls":a.contentId,"aria-expanded":a.open||!1,"data-state":R(a.open),"data-disabled":a.disabled?"":void 0,disabled:a.disabled,...n,ref:t,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});x.displayName=b;var C="CollapsibleContent",N=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=g(C,e.__scopeCollapsible);return(0,p.jsx)(d.C,{present:r||o.open,children:e=>{let{present:r}=e;return(0,p.jsx)(w,{...n,ref:t,present:r})}})});N.displayName=C;var w=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:o,children:a,...i}=e,d=g(C,r),[c,f]=n.useState(o),v=n.useRef(null),h=(0,s.s)(t,v),m=n.useRef(0),y=m.current,b=n.useRef(0),x=b.current,N=d.open||c,w=n.useRef(N),j=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>w.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,l.N)(()=>{let e=v.current;if(e){j.current=j.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();m.current=t.height,b.current=t.width,w.current||(e.style.transitionDuration=j.current.transitionDuration,e.style.animationName=j.current.animationName),f(o)}},[d.open,o]),(0,p.jsx)(u.sG.div,{"data-state":R(d.open),"data-disabled":d.disabled?"":void 0,id:d.contentId,hidden:!N,...i,ref:h,style:{"--radix-collapsible-content-height":y?"".concat(y,"px"):void 0,"--radix-collapsible-content-width":x?"".concat(x,"px"):void 0,...e.style},children:N&&a})});function R(e){return e?"open":"closed"}var j=y},90221:(e,t,r)=>{r.d(t,{u:()=>u});var n=r(62177);let o=(e,t,r)=>{if(e&&"reportValidity"in e){let o=(0,n.Jt)(r,t);e.setCustomValidity(o&&o.message||""),e.reportValidity()}},a=(e,t)=>{for(let r in t.fields){let n=t.fields[r];n&&n.ref&&"reportValidity"in n.ref?o(n.ref,r,e):n&&n.refs&&n.refs.forEach(t=>o(t,r,e))}},i=(e,t)=>{t.shouldUseNativeValidation&&a(e,t);let r={};for(let o in e){let a=(0,n.Jt)(t.fields,o),i=Object.assign(e[o]||{},{ref:a&&a.ref});if(l(t.names||Object.keys(e),o)){let e=Object.assign({},(0,n.Jt)(r,o));(0,n.hZ)(e,"root",i),(0,n.hZ)(r,o,e)}else(0,n.hZ)(r,o,i)}return r},l=(e,t)=>{let r=s(t);return e.some(e=>s(e).match(`^${r}\\.\\d+`))};function s(e){return e.replace(/\]|\[/g,"")}function u(e,t,r){return void 0===r&&(r={}),function(o,l,s){try{return Promise.resolve(function(n,i){try{var l=Promise.resolve(e["sync"===r.mode?"parse":"parseAsync"](o,t)).then(function(e){return s.shouldUseNativeValidation&&a({},s),{errors:{},values:r.raw?Object.assign({},o):e}})}catch(e){return i(e)}return l&&l.then?l.then(void 0,i):l}(0,function(e){if(Array.isArray(null==e?void 0:e.errors))return{values:{},errors:i(function(e,t){for(var r={};e.length;){var o=e[0],a=o.code,i=o.message,l=o.path.join(".");if(!r[l]){if("unionErrors"in o){var s=o.unionErrors[0].errors[0];r[l]={message:s.message,type:s.code}}else r[l]={message:i,type:a}}if("unionErrors"in o&&o.unionErrors.forEach(function(t){return t.errors.forEach(function(t){return e.push(t)})}),t){var u=r[l].types,d=u&&u[o.code];r[l]=(0,n.Gb)(l,t,r,a,d?[].concat(d,o.message):o.message)}e.shift()}return r}(e.errors,!s.shouldUseNativeValidation&&"all"===s.criteriaMode),s)};throw e}))}catch(e){return Promise.reject(e)}}}}}]);