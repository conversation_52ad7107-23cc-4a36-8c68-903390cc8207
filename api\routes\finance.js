import {
  parseISO,
  fromUnixTime,
  format,
  startOfDay,
  endOfDay,
  addDays,
  differenceInDays,
  startOfMonth,
  endOfMonth,
  addMonths,
  isBefore,
  isValid,
  getTime,
  setHours,
  setMinutes,
  setSeconds,
  setMilliseconds
} from 'date-fns';

export default async function finance(fastify, opts) {
  // 购买记录
  fastify.get('/finance/buying-record', {
    schema: {
      tags: ['finance'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'number', default: 1 },
          pageSize: { type: 'number', default: 10 },
          teacher: { type: 'string' },
          startTime: { type: 'string' },
          endTime: { type: 'string' },
          search: { type: 'string' },
        },
      }
    },
    onRequest: [fastify.auth.authenticate],
    handler: async (request, reply) => {
      const user = request.user
      const { teacher, page = 1, pageSize = 10, startTime, endTime, search } = request.query
      try {
        const where = {
          student: {
            ...(search && {
              OR: [
                {
                  name: {
                    contains: search,
                    mode: 'insensitive',
                  }
                },
                {
                  phone: {
                    contains: search,
                    mode: 'insensitive',
                  }
                },
              ]

            }),
          },
          ...(teacher && {
            salesRepresentativeId: teacher,
          }),
          institutionId: user.institutionId,
        }
        if (startTime && endTime) {
          where.paymentTime = {
            gte: Number.parseInt(startTime),
            lte: Number.parseInt(endTime),
          }
        }

        const skip = (page - 1) * pageSize
        const take = pageSize
        const orderBy = {
          paymentTime: 'desc',
        }

        // ====== 新 获取学生购买记录 ======
        const [studentProductRecords, total] = await Promise.all([

        fastify.prisma.studentProductRecord.findMany({
          where,
          select: {
            id: true,
            amount: true,
            amountPaid: true,
            amountUnpaid: true,
            giftCount: true,
            giftDays: true,
            paymentMethod: true,
            paymentTime: true,
            status: true,
            remarks: true,
            product: {
              select: {
                id:  true,
                name: true,

              }
            },
            student: {
              select: {
                id: true,
                name: true,
                phone: true,
              },
            },
            salesRepresentative: {
              select: {
                id: true,
                name: true,
              },
            },
            operator: {
              select: {
                id: true,
                name: true,
              },
            },
            studentProduct: {
              select: {
                id: true,
                remainingBalance: true,
              }
            }
          },
          orderBy,
          skip,
          take,
        }),
        fastify.prisma.studentProductRecord.count({
          where,
        })
      ])

        const transformedStudentProductRecords = studentProductRecords.map((item) => {
          return {
            ...item,
            paymentTime: Number.parseInt(item.paymentTime),
          }
        })

        // ====== 结束 获取学生购买记录 ====


        reply.success({
          message: 'success',
          data: {
            total,
            page,
            pageSize,
            list: transformedStudentProductRecords,
          },
        })


      } catch (error) {
        fastify.log.error(error)
        reply.code(500).send({ message: 'Internal Server Error' })
      }

    }
  })

  // 授课记录
  fastify.get('/finance/teaching-record', {
    schema: {
      tags: ['finance'],
      querystring: {
        type: 'object',
        properties: {
          startTime: { type: 'string' },
          endTime: { type: 'string' },
          search: { type: 'string' },
        },
      }
    },
    onRequest: [fastify.auth.authenticate],
    handler: async (request, reply) => {
      const user = request.user
      const { startTime, endTime, search } = request.query
      try {
        const where = {
          ...(search && {
            teacher: {
              OR: [
                {
                  name: { contains: search, mode: 'insensitive' },
                },
                {
                  phone: { contains: search, mode: 'insensitive' },
                },
              ],
            },
          }),
          institutionId: user.institutionId,
        }
        if (startTime && endTime) {
          where.StudentWeeklySchedule = {
            some: {
              operatorTime: {
                gte: Number.parseInt(startTime),
                lte: Number.parseInt(endTime),
              }
            }
          }
        }
        const result = await fastify.prisma.classesSchedule.findMany({
          where,
          select: {
            id: true,
            teacherId: true,
            teacher: {
              select: {
                id: true,
                name: true,
              },
            },
            StudentWeeklySchedule: {
              select: {
                status: true,
                operatorTime: true,
                attendanceCount: true,
                attendanceAmount: true,

              }
            }
          }
        })

        let resultMap = new Map()
        result.forEach(item => {
          const teacherId = item.teacher.id
          if (!resultMap.has(teacherId)) {
            resultMap.set(teacherId, {
              teacher: item.teacher,
              attendanceCount: 0,
              attendanceAmount: 0,
              classesCountCount: 0,
              unattendedCount: 0,
              leaveCount: 0,
              absentCount: 0,
            })
          }

          const teacherData = resultMap.get(teacherId)
          teacherData.classesCountCount += 1
          item.StudentWeeklySchedule.forEach(items => {
            if (items.status === 'unattended') {
              teacherData.unattendedCount += 1
            } else if (items.status === 'leave') {
              teacherData.leaveCount += 1
            } else if (items.status === 'absent') {
              teacherData.absentCount += 1
            }
            teacherData.attendanceCount = Number.parseFloat(teacherData.attendanceCount) + Number.parseFloat(items.attendanceCount || 0)
            teacherData.attendanceAmount = Number.parseFloat(teacherData.attendanceAmount) + Number.parseFloat(items.attendanceAmount || 0)
          })
        })

        const resultArray = Array.from(resultMap.values())
        resultArray.sort((a, b) => b.attendanceAmount - a.attendanceAmount)

        reply.success({
          message: 'success',
          data: resultArray,
        })
      } catch (error) {
        fastify.log.error(error)
        reply.code(500).send({ message: 'Internal Server Error' })
      }
    }
  })

  // 财务报表


  // 销售概览
  fastify.get('/finance/sales-overview', {
    schema: {
      tags: ['finance'],
      querystring: {
        type: 'object',
        properties: {
          startTime: { type: 'string' },
          endTime: { type: 'string' },
          checkType: {
            type: 'string',
            default: 'daily',
            enum: ['daily', 'month'],
            description: 'daily: 日报表， month: 月报表',
          }
        }
      }
    },
    onRequest: [fastify.auth.authenticate],
    handler: async (request, reply) => {
      const user = request.user;
      const { startTime, endTime, checkType = 'daily' } = request.query;

      // Prepare query conditions
      const where = {
        institutionId: user.institutionId,
      };

      // Handle date ranges
      let queryStartTime, queryEndTime;

      if (!startTime || !endTime) {
        // Default to last 7 days
        queryEndTime = new Date();
        queryStartTime = new Date();
        queryStartTime.setDate(queryStartTime.getDate() - 7);
      } else {
        // Use provided range
        try {
          queryStartTime = new Date(parseInt(startTime, 10));
          queryEndTime = new Date(parseInt(endTime, 10));

          if (isNaN(queryStartTime.getTime()) || isNaN(queryEndTime.getTime())) {
            throw new Error('Invalid time range');
          }
        } catch (err) {
          return reply.code(400).send({
            success: false,
            error: 'Invalid time parameters'
          });
        }
      }

      where.paymentTime = {
        gte: queryStartTime.getTime(),
        lte: queryEndTime.getTime(),
      };

      
      const [studentProductRefund, studentProductRecord] = await Promise.all([
        fastify.prisma.studentProductRefund.findMany({
          where,
          select: {
          status: true,
          amount: true,
          paymentTime: true,
        }
      }),
      fastify.prisma.studentProductRecord.findMany({
        where,
        select: {
          id: true,
          amount: true,
          amountPaid: true,
          amountUnpaid: true,
          paymentTime: true,
          status: true,
          // remainingBalance: true,
          }
        }),
      ]);

    

      // Create date formatters based on report type
      const formatDate = (timestamp) => {
        const date = new Date(parseInt(timestamp, 10));
        return checkType === 'daily'
          ? date.toLocaleDateString()
          : date.toLocaleDateString('default', { year: 'numeric', month: '2-digit' });
      };

      
      // 创建日期数据
      const dates = new Map();
      if (checkType === 'daily') {
        // Generate all dates between start and end dates for daily reports
        const daysDiff = Math.ceil((queryEndTime - queryStartTime) / (1000 * 60 * 60 * 24));
        const daysToGenerate = Math.min(daysDiff + 1, 31); // Limit to 31 days max to prevent excessive data

        for (let i = 0; i < daysToGenerate; i++) {
          const date = new Date(queryStartTime);
          date.setDate(date.getDate() + i);
          const dateKey = date.toLocaleDateString();
          dates.set(dateKey, {
            numberOfRefund: 0, // 退款次数
            refundAmount: 0, // 退款金额
            
            paidAmount: 0, // 支付金额
            numberOfPaid: 0, // 支付次数
            unpaidAmount: 0, // 未支付金额
          });
        }
      } else {
        // Generate all months between start and end dates for monthly reports
        const startYear = queryStartTime.getFullYear();
        const startMonth = queryStartTime.getMonth();
        const endYear = queryEndTime.getFullYear();
        const endMonth = queryEndTime.getMonth();

        // Calculate months difference
        const monthsDiff = (endYear - startYear) * 12 + (endMonth - startMonth);
        const monthsToGenerate = Math.min(monthsDiff + 1, 12); // Limit to 12 months max

        for (let i = 0; i < monthsToGenerate; i++) {
          const date = new Date(queryStartTime);
          date.setMonth(date.getMonth() + i);
          const dateKey = date.toLocaleDateString('default', { year: 'numeric', month: '2-digit' });

          dates.set(dateKey, {
            numberOfRefund: 0, // 退款次数
            refundAmount: 0, // 退款金额
            
            paidAmount: 0, // 支付金额
            numberOfPaid: 0, // 支付次数
            unpaidAmount: 0, // 未支付金额
          });
        }
      }


      // 处理退款数据
      // studentProductRefund, studentProductRecord
      for(const item of studentProductRefund) {
        const dateKey = formatDate(item.paymentTime);
        if(!dates.has(dateKey)) {
          dates.set(dateKey, {
            numberOfRefund: 0, // 退款次数
            refundAmount: 0, // 退款金额
            
            paidAmount: 0, // 支付金额
            numberOfPaid: 0, // 支付次数

            unpaidAmount: 0, // 未支付金额
          })
        }

        const dateData = dates.get(dateKey);
        if(item.status === 'approved') {
          dateData.numberOfRefund += 1;
          dateData.refundAmount += Number.parseFloat(item.amount || 0);
        }
      }

      for(const item of studentProductRecord) {
        const dateKey = formatDate(item.paymentTime);
        if(!dates.has(dateKey)) {
          dates.set(dateKey, {
            numberOfRefund: 0, // 退款次数
            refundAmount: 0, // 退款金额
            paidAmount: 0, // 支付金额
            numberOfPaid: 0, // 支付次数
            unpaidAmount: 0, // 未支付金额

          })
        }
        const dateData = dates.get(dateKey);
        if(item.status === 'done') {
          dateData.numberOfPaid += 1;
          dateData.paidAmount += Number.parseFloat(item.amountPaid || 0);
        } else if(item.status === 'arrears') {
          dateData.unpaidAmount += Number.parseFloat(item.amountUnpaid || 0);
        }
      } 



      // Convert to array and sort
      const transformedDates = Array.from(dates.entries()).map(([date, values]) => ({
        date,
        ...values
      }));

      // 根据日期排序
      transformedDates.sort((a, b) => {
        if (checkType === 'daily') {
          return new Date(a.date) - new Date(b.date);
        } else if (checkType === 'month') {
          // For monthly reports, parse the date properly before comparing
          const [aYear, aMonth] = a.date.split('/');
          const [bYear, bMonth] = b.date.split('/');
          return new Date(aYear, aMonth - 1) - new Date(bYear, bMonth - 1);
        }
        return 0;
      });


      const dateLabels = [];
      const numberOfRefundList = [];  // 退款次数
      const refundAmountList = [];  // 退款金额
      const paidAmountList = [];  // 支付金额
      const numberOfPaidList = [];  // 支付次数
      const unpaidAmountList = [];  // 未支付金额

      for(const item of transformedDates) {
        if(checkType === 'daily') {
          dateLabels.push(item.date);
        } else if(checkType === 'month') {
          dateLabels.push(item.date.split('年')[1]);
        }
        numberOfRefundList.push(item.numberOfRefund);
        refundAmountList.push(item.refundAmount);
        paidAmountList.push(item.paidAmount);
        numberOfPaidList.push(item.numberOfPaid);
        unpaidAmountList.push(item.unpaidAmount);
      }

      const totalPaidAmount = paidAmountList.reduce((acc, curr) => acc + curr, 0);
      const totalNumberOfPaid = numberOfPaidList.reduce((acc, curr) => acc + curr, 0);

      const totalRefundAmount = refundAmountList.reduce((acc, curr) => acc + curr, 0);
      const totalNumberOfRefund = numberOfRefundList.reduce((acc, curr) => acc + curr, 0);

      const totalUnpaidAmount = unpaidAmountList.reduce((acc, curr) => acc + curr, 0);

      // Send response
      reply.success({
        data: {
          datasets: {
            dateLabel: dateLabels,
            numberOfRefund: numberOfRefundList,
            refundAmount: refundAmountList,
            paidAmount: paidAmountList,
            numberOfPaid: numberOfPaidList,
            // unpaidAmount: unpaidAmountList
          },
          totals: {
            totalPaidAmount,
            totalNumberOfPaid,
            totalRefundAmount,
            totalNumberOfRefund,
            totalUnpaidAmount
          }
        }
      });
    }
  });

  // 根据销售人获取数据
  fastify.get('/finance/salesrep-overview', {
    schema: {
      tags: ['finance'],
      querystring: {
        type: 'object',
        properties: {
          page: {
            type: 'number',
            default: 1,
          },
          pageSize: {
            type: 'number',
            default: 10,
          },
          startTime: { type: 'string' },
          endTime: { type: 'string' },
          teacher: {
            type: 'string',
            description: 'Filter by salesRep ID'
          }
        }
      }
    },
    onRequest: [fastify.auth.authenticate],
    handler: async (request, reply) => {
      try {
        const user = request.user;
        const { teacher, startTime, endTime, page = 1, pageSize = 10 } = request.query;

        // 构建查询条件
        const where = {
          institutionId: user.institutionId,
          ...(teacher && { salesRepresentativeId: teacher }),
        };

        // 添加时间范围过滤
        if (startTime && endTime) {
          where.paymentTime = {
            gte: parseInt(startTime, 10),
            lte: parseInt(endTime, 10)
          };
        } else {
          let queryEndTime = new Date();
          let queryStartTime = new Date();
          queryStartTime.setDate(queryStartTime.getDate() - 7);
          where.paymentTime = {
            gte: queryStartTime.getTime(),
            lte: queryEndTime.getTime()
          }
        }

        // 查询数据库所有所需字段
        const result = await fastify.prisma.studentProductRecord.findMany({
          where,
          select: {
            amount: true,
            amountPaid: true,
            amountUnpaid: true,
            paymentTime: true,
            status: true,          // Added missing field
            salesRepresentative: {
              select: {
                id: true,
                name: true
              }
            }
          }
        });

        // 查询退款
        const refund = await fastify.prisma.studentProductRefund.findMany({
          where: {
            institutionId: user.institutionId,
            ...(teacher && { salesRepresentativeId: teacher }),
          },
          select: {
            amount: true,
            paymentTime: true,
            status: true,
            operator: {
              select: {
                name: true
            }
            }
          }
        })

        // // 安全地解析和转换数据
        const transactions = result.map((item) => ({
          ...item,
          // 安全地解析paymentTime为数字，如果解析失败则默认为0
          paymentTime: parseInt(item.paymentTime, 10) || 0,
          // 确保数值用于计算
          amountPaid: parseFloat(item.amountPaid || 0), // 已支付金额
          // amountUnpaid: parseFloat(item.amountUnpaid || 0) // 未支付金额
        }));

        const refundTransactions = refund.map((item) => ({
          ...item,
          paymentTime: parseInt(item.paymentTime, 10) || 0,
          amount: parseFloat(item.amount || 0),
        }))

        // // 按销售代表名称分组数据
        const newData = new Map();
        for(const item of transactions) {
          const key = item.salesRepresentative.name;
          if(!newData.has(key)) {
            newData.set(key, {
              amountPaid: 0,
              amountPaidCount: 0,
              refundAmount: 0,
              refundCount: 0,
            })
          }
          const getData = newData.get(key);
          if(item.status === 'done') {
            getData.amountPaid += item.amountPaid;
            getData.amountPaidCount += 1;
          }
        }
        // 处理退款
        for(const item of refundTransactions) {
          const key = item.operator.name;
          if(!newData.has(key)) {
            newData.set(key, {
              amountPaid: 0,
              amountPaidCount: 0,
              refundAmount: 0,
              refundCount: 0,
            })
          }
          const getData = newData.get(key);
          if(item.status === 'approved') {  
            getData.refundAmount += item.amount;
            getData.refundCount += 1;
          }
        }
        const sortedEntries = Array.from(newData.entries())
          .sort((a, b) => b[1].amountPaid - a[1].amountPaid);
        // 格式化数据以响应

        const dataLabels = [];
        const amountPaid = [];
        const refundAmount = [];
        const amountPaidCount = [];
        const refundCount = [];

        for(const [name, data] of sortedEntries) {
          dataLabels.push(name);
          amountPaid.push(Number(data.amountPaid.toFixed(2)));
          refundAmount.push(Number(data.refundAmount.toFixed(2)));
          amountPaidCount.push(data.amountPaidCount);
          refundCount.push(data.refundCount);
        }
        

        // 返回成功响应并格式化数据
        reply.success({
          data: {
            datasets: {
              dataLabels,
              amountPaid,
              refundAmount,
              amountPaidCount,
              refundCount
            }
          }
        });
      } catch (error) {
        request.log.error(error);
        reply.code(500).send({
          success: false,
          error: 'Failed to retrieve salesrep overview'
        });
      }
    }
  });

  //学员概览(获取学员年龄(当前时间-birthday)段范围(人数)、性别(人数), 学员课程(人数))
  fastify.get('/finance/student-overview', {
    schema: {
      tags: ['finance'],
    },
    onRequest: [fastify.auth.authenticate],
    handler: async (request, reply) => {
      const user = request.user
      try {
        const result = await fastify.prisma.student.findMany({
          where: {
            institutionId: user.institutionId
          },
          select: {
            birthday: true,
            gender: true,
            StudentProduct: {
              select: {
                StudentWeeklySchedule: {
                  select: {
                    classesSchedule: {
                      select: {
                        courses: {
                          select: {
                            name: true
                          }
                        }
                      }
                    }
                  }
                },
                product: {
                  select: {
                    name: true
                  }
                }
              },
            }
          }
        })
        const ageMap = new Map()
        const genderMap = new Map()
        const productMap = new Map()
        const courseMap = new Map()
        result.forEach(item => {
          const birthday = item.birthday ? new Date(Number(item.birthday)) : null
          const age = birthday ? Number(new Date().getFullYear() - birthday.getFullYear()) : 0
          ageMap.set(age, (ageMap.get(age) || 0) + 1)
          if (item.gender) {
            const gender = item.gender === 'male' ? '男' : item.gender === 'female' ? '女' : '保密'
            genderMap.set(gender, (genderMap.get(gender) || 0) + 1)
          }
          item.StudentProduct.forEach(item => {
            productMap.set(item.product.name, (productMap.get(item.product.name) || 0) + 1)
          })
          item.StudentProduct.forEach(item => {
            item.StudentWeeklySchedule.forEach(item => {
              if (item.classesSchedule && item.classesSchedule.courses) {
                courseMap.set(item.classesSchedule.courses.name, (courseMap.get(item.classesSchedule.courses.name) || 0) + 1)
              }
            })
          })
        })
        const ageArray = Array.from(ageMap.entries())
        const genderArray = Array.from(genderMap.entries())
        const productArray = Array.from(productMap.entries())
        const courseArray = Array.from(courseMap.entries())
        ageArray.sort((a, b) => a[0] - b[0])
        genderArray.sort((a, b) => a[0] - b[0])
        productArray.sort((a, b) => a[0] - b[0])
        courseArray.sort((a, b) => a[0] - b[0])

        const ageArrays = ageArray.map(item => {
          return {
            age: item[0],
            count: item[1]
          }
        })
        const genderArrays = genderArray.map(item => {
          return {
            gender: item[0],
            count: item[1]
          }
        })
        const productArrays = productArray.map(item => {
          return {
            product: item[0],
            count: item[1]
          }
        })
        const courseArrays = courseArray.map(item => {
          return {
            course: item[0],
            count: item[1]
          }
        })
        console.log(ageArrays, genderArrays, productArrays, courseArrays)

        reply.success({
          data: {
            age: ageArrays,
            gender: genderArrays,
            product: productArrays,
            course: courseArrays
          }
        })
      } catch (error) {
        fastify.log.error(error)
        reply.code(500).send({ message: 'Internal Server Error' })
      }
    }
  })

  //EducationOverview 整体概览


  fastify.get('/finance/education-overview', {
    schema: {
      tags: ['finance'],
      querystring: {
        type: 'object',
        properties: {
          startTime: { type: 'string' },
          endTime: { type: 'string' },
          checkType: {
            type: 'string',
            default: 'daily',
            enum: ['daily', 'monthly']
          }
        }
      }
    },
    onRequest: [fastify.auth.authenticate],
    handler: async (request, reply) => {
      const user = request.user;
      const { startTime, endTime, checkType = 'daily' } = request.query;

      try {
        // =================== 基础数据统计 ===================

        // 获取学生人数和课程人数
        const [studentCount, courseCount] = await Promise.all([
          fastify.prisma.student.count({
            where: { institutionId: user.institutionId }
          }),
          fastify.prisma.course.count({
            where: { institutionId: user.institutionId }
          })
        ]);

        // 获取今日上课人数、考勤人数
        const today = startOfDay(new Date());
        const todayStart = getTime(today);
        const todayEnd = getTime(endOfDay(today));

        // 课程统计辅助函数
        const countSchedules = async (startDate, endDate, status = null) => {
          const where = {
            institutionId: user.institutionId,
            classesSchedule: {
              startDate: {
                gte: startDate,
                lte: endDate
              }
            }
          };

          if (status) {
            where.status = status;
          }

          return fastify.prisma.studentWeeklySchedule.count({ where });
        };

        // 获取今日统计数据
        const [todayCount, todayAttendanceCount] = await Promise.all([
          countSchedules(todayStart, todayEnd),
          countSchedules(todayStart, todayEnd, 'attendance')
        ]);

        // =================== 日期解析与处理 ===================

        // 解析时间戳为日期对象
        const parseTimestamp = (timestamp) => {
          if (!timestamp) return new Date();

          const numTimestamp = Number(timestamp);
          if (isNaN(numTimestamp)) return new Date();

          // Handle milliseconds (13 digits) or seconds (10 digits)
          const date = String(numTimestamp).length <= 10
            ? fromUnixTime(numTimestamp)
            : new Date(numTimestamp);

          return isValid(date) ? date : new Date();
        };

        // 解析输入的起止日期
        let startDate = parseTimestamp(startTime);
        let endDate = parseTimestamp(endTime);

        // 确保日期有效性
        if (isBefore(endDate, startDate)) {
          [startDate, endDate] = [endDate, startDate];
        }

        // =================== 时间范围数据生成 ===================

        let timeRangeData = [];

        // 日视图数据生成
        const generateDailyData = async (start, end) => {
          const result = [];
          const startDay = startOfDay(start);
          const endDay = endOfDay(end);

          // 计算天数差
          const daysDiff = differenceInDays(endDay, startDay) + 1;

          for (let i = 0; i < daysDiff; i++) {
            const currentDate = addDays(startDay, i);
            const dayStart = getTime(startOfDay(currentDate));
            const dayEnd = getTime(endOfDay(currentDate));

            const [count, attendanceCount] = await Promise.all([
              countSchedules(dayStart, dayEnd),
              countSchedules(dayStart, dayEnd, 'attendance')
            ]);

            // 格式化日期为 YYYY-MM-DD
            const dateStr = format(currentDate, 'yyyy-MM-dd');

            result.push({
              date: dateStr,
              count,
              attendanceCount
            });
          }

          return result;
        };

        // 月视图数据生成
        const generateMonthlyData = async (start, end) => {
          const result = [];
          const months = [];

          // 获取起止月份列表
          let current = startOfMonth(start);
          const lastMonth = startOfMonth(end);

          // 生成月份列表
          while (!isBefore(lastMonth, current)) {
            const monthStart = getTime(startOfMonth(current));
            const monthEnd = getTime(endOfMonth(current));

            months.push({
              start: monthStart,
              end: monthEnd,
              label: format(current, 'yyyy-MM')
            });

            // 移至下月
            current = addMonths(current, 1);
          }

          // 获取各月统计数据
          for (const monthData of months) {
            const [count, attendanceCount] = await Promise.all([
              countSchedules(monthData.start, monthData.end),
              countSchedules(monthData.start, monthData.end, 'attendance')
            ]);

            result.push({
              date: monthData.label,
              count,
              attendanceCount
            });
          }

          return result;
        };

        // 根据视图类型生成时间范围数据
        if (checkType === 'daily') {
          timeRangeData = await generateDailyData(startDate, endDate);
        } else if (checkType === 'monthly') {
          timeRangeData = await generateMonthlyData(startDate, endDate);
        }

        // =================== 课程数据统计 ===================

        // 获取日期范围内课程人数
        const classesScheduleResult = await fastify.prisma.classesSchedule.findMany({
          where: {
            institutionId: user.institutionId,
            startDate: {
              gte: getTime(startDate),
              lte: getTime(endDate)
            }
          },
          select: {
            StudentWeeklySchedule: {
              select: {
                id: true,
              }
            },
            courses: {
              select: {
                name: true,
              }
            }
          }
        });

        // 汇总课程数据
        const courseMap = new Map();
        classesScheduleResult.forEach(item => {
          if (item.courses) {
            const dateKey = item.courses.name;
            if (!courseMap.has(dateKey)) {
              courseMap.set(dateKey, {
                course: item.courses.name,
                count: 0
              });
            }
            courseMap.set(dateKey, {
              course: item.courses.name,
              count: Number(courseMap.get(dateKey).count || 0) + Number(item.StudentWeeklySchedule.length)
            });
          }
        });
        const courseArray = Array.from(courseMap.values());

        // 返回结果
        reply.success({
          data: {
            count: {
              student: studentCount,
              course: courseCount,
              today: todayCount,
              todayAttendance: todayAttendanceCount,
            },
            timeRange: timeRangeData,
            course: courseArray
          }
        });

      } catch (error) {
        fastify.log.error(error);
        reply.code(500).send({ message: 'Internal Server Error' });
      }
    }
  });

  // 学员费用
  fastify.get('/finance/student-fees', {
    schema: {
      tags: ['finance'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'number', default: 1 },
          pageSize: { type: 'number', default: 10 },
          search: { type: 'string' },
        }
      },
    },
    onRequest: [fastify.auth.authenticate],
    handler: async (request, reply) => {
      const user = request.user;
      const { page = 1, pageSize = 10, search } = request.query;
      try {
        const where = {
          institutionId: user.institutionId,
        }
        if (search) {
          where.student = {
            name: {
              contains: search,
            }
          }
        }
        const skip = (page - 1) * pageSize
        const take = pageSize
        const orderBy = {
          payTime: 'desc',
        }


        const [total, studentProducts, studentProduct] = await Promise.all([
          fastify.prisma.studentProduct.count({
            where,
          }),
          fastify.prisma.studentProduct.findMany({
            where,
            select: {
              id: true,
              amount: true,
              totalCount: true,
              amountPaid: true,
              amountUnpaid: true,
              remainingCount: true,
              remainingAmount: true,
              payTime: true,
              type: true,
              remarks: true,
              product: {
                select: {
                  id: true,
                  name: true,
                  packageType: true,
                }
              },
              student: {
                select: {
                  id: true,
                  name: true,
                  phone: true,
                }
              },
            },
            skip, take, orderBy
          }),
          fastify.prisma.studentProduct.findMany({
            where: {
              institutionId: user.institutionId,
            },
            select: {
              remainingCount: true,
              remainingAmount: true
            }
          })
        ])
        const transformedStudentProducts = studentProducts.map((item) => {
          return {
            ...item,
            payTime: Number.parseInt(item.payTime),
          }
        })

        const remainingCount = studentProduct.reduce((acc, curr) => {
          acc.remainingCount += Number(curr.remainingCount) || 0
          acc.remainingAmount += Number(curr.remainingAmount) || 0
          return acc
        }, { remainingCount: 0, remainingAmount: 0 })



        reply.success({
          message: 'success',
          data: {
            total,
            page,
            pageSize,
            list: transformedStudentProducts,
            remainingCount,
          }
        })
      } catch (error) {
        fastify.log.error(error)
        reply.code(500).send({ message: 'Internal Server Error' })
      }

    }
  })

  // 学员消课汇总
  fastify.get('/finance/student-class-summary', {
    schema: {
      tags: ['finance'],
      querystring: {
        type: 'object',
        properties: {
          startTime: { type: 'string' },
          endTime: { type: 'string' },
          checkType: {
            type: 'string',
            default: 'daily',
            enum: ['daily', 'monthly']
          }
        }
      },
    },
    onRequest: [fastify.auth.authenticate],
    handler: async (request, reply) => {
      const user = request.user;
      const { startTime, endTime, checkType } = request.query;
      try {
        const where = {
          institutionId: user.institutionId,
        }
        if (startTime && endTime) {
          where.startDate = {
            gte: startTime,
            lte: endTime
          }
        } else {
          where.startDate = {
            gte: new Date().getTime() - 1000 * 60 * 60 * 24 * 30,
            lte: new Date().getTime()
          }
        }



        // =================== 时间范围数据生成 ===================
        const parseTimestamp = (timestamp) => {
          if (!timestamp) return new Date();

          const numTimestamp = Number(timestamp);
          if (isNaN(numTimestamp)) return new Date();

          // Handle milliseconds (13 digits) or seconds (10 digits)
          const date = String(numTimestamp).length <= 10
            ? fromUnixTime(numTimestamp)
            : new Date(numTimestamp);

          return isValid(date) ? date : new Date();
        };

        // 解析输入的起止日期
        let startDate = parseTimestamp(startTime);
        let endDate = parseTimestamp(endTime);

        // 确保日期有效性
        if (isBefore(endDate, startDate)) {
          [startDate, endDate] = [endDate, startDate];
        }

        const countSchedules = async (startDate, endDate, status = null) => {
          const where = {
            institutionId: user.institutionId,
            startDate: {
              gte: startDate,
              lte: endDate
            }
          };

          return fastify.prisma.classesSchedule.count({ where });
        };

        // =================== 时间范围数据生成 ===================

        let timeRangeData = [];

        // 日视图数据生成
        const generateDailyData = async (start, end) => {
          const result = [];
          const startDay = startOfDay(start);
          const endDay = endOfDay(end);

          // 计算天数差
          const daysDiff = differenceInDays(endDay, startDay) + 1;

          for (let i = 0; i < daysDiff; i++) {
            const currentDate = addDays(startDay, i);
            // const dayStart = getTime(startOfDay(currentDate));
            // const dayEnd = getTime(endOfDay(currentDate));

            // const [count, attendanceCount] = await Promise.all([
            //   countSchedules(dayStart, dayEnd),
            //   countSchedules(dayStart, dayEnd, 'attendance')
            // ]);

            // 格式化日期为 YYYY-MM-DD
            const dateStr = format(currentDate, 'yyyy-MM-dd');

            result.push({
              date: dateStr,
              // count,
              // attendanceCount
            });
          }

          return result;
        };

        // 月视图数据生成
        const generateMonthlyData = async (start, end) => {
          const result = [];
          const months = [];

          // 获取起止月份列表
          let current = startOfMonth(start);
          const lastMonth = startOfMonth(end);

          // 生成月份列表
          while (!isBefore(lastMonth, current)) {
            const monthStart = getTime(startOfMonth(current));
            const monthEnd = getTime(endOfMonth(current));

            months.push({
              start: monthStart,
              end: monthEnd,
              label: format(current, 'yyyy-MM')
            });

            // 移至下月
            current = addMonths(current, 1);
          }

          // 获取各月统计数据
          for (const monthData of months) {
            const [count, attendanceCount] = await Promise.all([
              countSchedules(monthData.start, monthData.end),
              countSchedules(monthData.start, monthData.end, 'attendance')
            ]);

            result.push({
              date: monthData.label,
              count,
              attendanceCount
            });
          }

          return result;
        };

        // 根据视图类型生成时间范围数据
        if (checkType === 'daily') {
          timeRangeData = await generateDailyData(startDate, endDate);
        } else if (checkType === 'monthly') {
          timeRangeData = await generateMonthlyData(startDate, endDate);
        }

        const result = await fastify.prisma.classesSchedule.findMany({
          where,
          select: {
            id: true,
            startDate: true,
            StudentWeeklySchedule: {
              select: {
                status: true,
                attendanceCount: true,
                attendanceAmount: true,
              }
            }
          }
        })



        const dataMap = new Map()

        timeRangeData.forEach((item) => {
          const dateKey = item.date
          if (!dataMap.has(dateKey)) {
            dataMap.set(dateKey, {
              count: 0,
              attendanceCount: 0,
              attendanceAmount: 0
            })
          }
        })

        result.forEach((item) => {
          const dateKey = item.startDate
          const date = new Date(Number(dateKey))
          let dateStr
          if (checkType === 'daily') {
            dateStr = format(date, 'yyyy-MM-dd')
          } else if (checkType === 'monthly') {
            dateStr = format(date, 'yyyy-MM')
          }
          if (!dataMap.has(dateStr)) {
            dataMap.set(dateStr, {
              count: 0,
              attendanceCount: 0,
              attendanceAmount: 0
            })
          }
          const data = dataMap.get(dateStr)
          data.count = item.StudentWeeklySchedule.length
          data.attendanceCount = item.StudentWeeklySchedule.reduce((acc, curr) => acc + Number(curr.attendanceCount || 0), 0)
          data.attendanceAmount = item.StudentWeeklySchedule.reduce((acc, curr) => acc + Number(curr.attendanceAmount || 0), 0)
          dataMap.set(dateStr, data)
        })

        // console.log(dataMap)
        const dataArray = Array.from(dataMap.entries())
        dataArray.sort((a, b) => a[0] - b[0])
        const transformedData = dataArray.map(([date, data]) => ({
          date,
          ...data
        }))

        reply.success({
          message: 'success',
          data: {
            timeRangeData,
            list: transformedData
          }
        })


      } catch (error) {
        fastify.log.error(error)
        reply.code(500).send({ message: 'Internal Server Error' })
      }
    }
  })

  // 获取退款记录
  fastify.get('/finance/refund-records', {
    schema: {
      tags: ['finance'],
      querystring: {
        type: 'object',
        properties: {
          startTime: { type: 'string' },
          endTime: { type: 'string' },
          page: { type: 'number' },
          pageSize: { type: 'number' },
          search: { type: 'string' },
          operator: { type: 'string' }
        }
      }
    },
    onRequest: [fastify.auth.authenticate],
    handler: async (request, reply) => {
      const user = request.user
      const { startTime, endTime, page = 1, pageSize = 10, operator, search } = request.query
      try {
        const where = {
          institutionId: user.institutionId,
          ...(startTime && endTime ? { paymentTime: { gte: startTime, lte: endTime } } : {}),
          ...(operator ? { operatorId: operator } : {}),
          ...(search ? { student: {
            OR:[
              {
                name: {
                  contains: search,
                  mode: 'insensitive'
                }
              },
              {
                phone: {
                  contains: search,
                  mode: 'insensitive'
                }
              }
            ]
           } } : {})
        }

        const skip = (page - 1) * pageSize
        const take = pageSize

        const [total, list] = await Promise.all([
          fastify.prisma.studentProductRefund.count({ where }),
          fastify.prisma.studentProductRefund.findMany({
            where,
            select: {
              id: true,
              product: {
                select: {
                  id: true,
                  name: true
                }
              },
              student: {
                select: {
                  id: true,
                  name: true
                }
              },
              amount: true,
              reason: true,
              paymentTime: true,
              paymentMethod: true,
              operator: {
                select: {
                  id: true,
                  name: true
                }
              },
              status: true,
            },
            skip,
            take
          })
        ])
        const serializedList = list.map(item => ({
          ...item,
          paymentTime: Number(item.paymentTime),
          amount: Number(item.amount),
        }))
        reply.success({
          message: 'success',
          data: {
            total,
            page,
            pageSize,
            list: serializedList
          }
        })
      } catch (error) {
        fastify.log.error(error)
        reply.code(500).send({ message: 'Internal Server Error' })
      }
    }
  })


  // 机构收入
  fastify.get('/finance/institution-income', {
    schema: {
      tags: ['finance'],
      querystring: {
        type: 'object',
        properties: {
          startTime: { type: 'string' },
          endTime: { type: 'string' },
          checkType: {
            type: 'string',
            default: 'daily',
            enum: ['daily', 'monthly']
          }
        }
      }
    },
    onRequest: [fastify.auth.authenticate],
    handler: async (request, reply) => {
      const user = request.user
      const { startTime, endTime, checkType = 'daily' } = request.query
      try { 
        const where = {
          institutionId: user.institutionId,
          ...(startTime && endTime ? { paymentTime: { gte: startTime, lte: endTime } } : {}),
        }

        const [total, list] = await Promise.all([
          fastify.prisma.bill.count({ where }),
          fastify.prisma.bill.findMany({ where, select: {
            amount: true,
            paymentTime: true,
            source: true,
            billType: true,
            status: true,
            paymentMethod: true,
            
          } })
        ])
        
      } catch (error) {
        fastify.log.error(error)
        reply.code(500).send({ message: 'Internal Server Error' })
      }
    }

  })
}