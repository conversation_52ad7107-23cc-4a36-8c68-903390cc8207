"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3101],{22346:(e,a,t)=>{t.d(a,{w:()=>o});var l=t(95155),s=t(12115),r=t(87489),d=t(59434);let o=s.forwardRef((e,a)=>{let{className:t,orientation:s="horizontal",decorative:o=!0,...n}=e;return(0,l.jsx)(r.b,{ref:a,decorative:o,orientation:s,className:(0,d.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",t),...n})});o.displayName=r.b.displayName},53101:(e,a,t)=>{t.r(a),t.d(a,{default:()=>A});var l=t(95155),s=t(12115),r=t(54165),d=t(59409),o=t(85057),n=t(30285),i=t(81586),c=t(46308),m=t(39785),u=t(71007),x=t(69074),b=t(59434),g=t(85511),p=t(14636),h=t(73168),f=t(24122),v=t(62523),y=t(26126),N=t(22346),j=t(48436),w=t(57141),k=t(63375),C=t(95728),L=t(70639);let A=function(e){let{open:a,onOpenChange:t,student:A,onPaymentSuccess:_}=e,[E,F]=(0,s.useState)(!1),[z,R]=(0,s.useState)({}),[J,M]=(0,s.useState)(G()),{data:D=[]}=(0,L.r3)({},{skip:!a}),[I]=(0,C.GZ)(),S=(0,s.useMemo)(()=>((J.amount||0)-(J.prepaidAmount||0)).toFixed(2),[J.amount,J.prepaidAmount]);function G(){let e=new Date;return{package:"",usageLimit:0,bonusLessons:0,amount:0,prepaidAmount:0,payment:"",salesRep:"",date:e,time:(0,h.GP)(e,"HH:mm")}}(0,s.useEffect)(()=>{a||(M(G()),R({}))},[a]),(0,s.useEffect)(()=>{if(J.package&&D.length)try{let e=Number.parseInt(J.package);if(isNaN(e)||e<0||e>=D.length)return;let a=D[e];M(e=>({...e,usageLimit:(null==a?void 0:a.usageLimit)||0,amount:(null==a?void 0:a.price)||0})),(z.package||z.usageLimit||z.amount)&&R(e=>({...e,package:void 0,usageLimit:void 0,amount:void 0}))}catch(e){console.error("解析套餐信息出错:",e)}},[J.package,D,z]);let V=(e,a)=>{M(t=>({...t,[e]:a})),z[e]&&R(a=>({...a,[e]:void 0}))},q=()=>{let e={};return J.package||(e.package="请选择套餐"),(!J.usageLimit||J.usageLimit<=0)&&(e.usageLimit="课时次数必须大于0"),(!J.amount||J.amount<=0)&&(e.amount="实收金额必须大于0"),J.payment||(e.payment="请选择收款方式"),J.date||(e.date="请选择收款日期"),R(e),0===Object.keys(e).length},H=async()=>{if(!q()){j.l.error("表单有误","请检查并填写所有必填项");return}try{F(!0);let e=Number.parseInt(J.package);if(isNaN(e)||e<0||e>=D.length)throw Error("无效的套餐选择");let a=J.date?"".concat((0,h.GP)(J.date,"yyyy-MM-dd")," ").concat(J.time):(0,h.GP)(new Date,"yyyy-MM-dd HH:mm"),l={productId:D[e].id,usageLimit:J.usageLimit,bonusLessons:J.bonusLessons,amount:J.amount,prepaidAmount:J.prepaidAmount,balance:Number(S),payment:J.payment,salesRep:J.salesRep,dateTime:new Date(a).getTime()};await I({studentId:A.id,productData:l}).unwrap(),_&&_(l),j.l.success("缴费成功","已成功为学员 ".concat(A.name," 完成缴费")),t(!1)}catch(e){console.error("缴费处理失败:",e),j.l.error("缴费失败",(null==e?void 0:e.message)||"处理缴费时出现错误，请重试")}finally{F(!1)}};return a?(0,l.jsx)(r.lG,{open:a,onOpenChange:t,children:(0,l.jsxs)(r.Cf,{className:"sm:max-w-[650px] p-0 gap-0 overflow-hidden rounded-lg",children:[(0,l.jsx)(r.c7,{className:"py-3 px-6 border-b bg-background flex flex-row items-center justify-between",children:(0,l.jsxs)(r.L3,{className:"text-base font-medium flex items-center gap-2 text-primary",children:[(0,l.jsx)(i.A,{className:"h-4 w-4"}),(0,l.jsxs)("span",{children:["学员缴费 - ",null==A?void 0:A.name]})]})}),(0,l.jsx)("div",{className:"p-6 max-h-[70vh] overflow-y-auto",children:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-muted-foreground",children:[(0,l.jsx)(c.A,{className:"h-3.5 w-3.5"}),(0,l.jsx)("h3",{children:"套餐信息"})]}),(0,l.jsxs)("div",{className:"grid gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)(o.J,{htmlFor:"package",className:"text-xs font-medium flex items-center gap-1.5",children:["学员套餐",(0,l.jsx)(y.E,{variant:"destructive",className:"font-normal text-[10px] px-1 py-0 h-4",children:"必填"})]}),(0,l.jsxs)(d.l6,{value:J.package,onValueChange:e=>V("package",e),children:[(0,l.jsx)(d.bq,{className:(0,b.cn)("w-full h-9",z.package&&"border-destructive"),id:"package",children:(0,l.jsx)(d.yv,{placeholder:"选择套餐"})}),(0,l.jsx)(d.gC,{children:D.length?D.map((e,a)=>(0,l.jsx)(d.eb,{value:String(a),children:e.name},e.id)):(0,l.jsx)(d.eb,{value:"loading",disabled:!0,children:"加载中..."})})]}),z.package&&(0,l.jsx)("p",{className:"text-xs text-destructive mt-1",children:z.package})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)(o.J,{htmlFor:"usageLimit",className:"text-xs font-medium flex items-center gap-1.5",children:["课时次数",(0,l.jsx)(y.E,{variant:"destructive",className:"font-normal text-[10px] px-1 py-0 h-4",children:"必填"})]}),(0,l.jsx)(v.p,{type:"number",id:"usageLimit",value:J.usageLimit,onChange:e=>V("usageLimit",Number(e.target.value)),placeholder:"输入课时次数",min:"1",className:(0,b.cn)("h-9",z.usageLimit&&"border-destructive")}),z.usageLimit&&(0,l.jsx)("p",{className:"text-xs text-destructive mt-1",children:z.usageLimit})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"bonusLessons",className:"text-xs font-medium",children:"赠送次数"}),(0,l.jsx)(v.p,{type:"number",id:"bonusLessons",value:J.bonusLessons,onChange:e=>V("bonusLessons",Number(e.target.value)),placeholder:"输入赠送次数",min:"0",className:"h-9"})]})]})]})]}),(0,l.jsx)(N.w,{className:"my-1"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-muted-foreground",children:[(0,l.jsx)(m.A,{className:"h-3.5 w-3.5"}),(0,l.jsx)("h3",{children:"支付信息"})]}),(0,l.jsxs)("div",{className:"grid gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)(o.J,{htmlFor:"amount",className:"text-xs font-medium flex items-center gap-1.5",children:["实收金额",(0,l.jsx)(y.E,{variant:"destructive",className:"font-normal text-[10px] px-1 py-0 h-4",children:"必填"}),(0,l.jsx)("span",{className:"text-muted-foreground text-[10px]",children:"(元)"})]}),(0,l.jsx)(v.p,{type:"number",id:"amount",value:J.amount,onChange:e=>V("amount",Number(e.target.value)),placeholder:"请输入实收金额",min:"0.01",step:"0.01",required:!0,className:(0,b.cn)("h-9 focus-visible:ring-primary",z.amount&&"border-destructive")}),z.amount&&(0,l.jsx)("p",{className:"text-xs text-destructive mt-1",children:z.amount})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)(o.J,{htmlFor:"prepaidAmount",className:"text-xs font-medium flex items-center gap-1.5",children:["预收款",(0,l.jsx)("span",{className:"text-muted-foreground text-[10px]",children:"(元)"})]}),(0,l.jsx)(v.p,{type:"number",id:"prepaidAmount",value:J.prepaidAmount,onChange:e=>V("prepaidAmount",Number(e.target.value)),placeholder:"请输入预收款",min:"0",step:"0.01",className:"h-9"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)(o.J,{className:"text-xs font-medium flex items-center gap-1.5",children:["欠费",(0,l.jsx)("span",{className:"text-muted-foreground text-[10px]",children:"(元)"})]}),(0,l.jsx)("div",{className:(0,b.cn)("flex h-9 w-full items-center justify-end rounded-md border px-3 font-medium text-sm",Number.parseFloat(S)>0?"text-destructive border-destructive/20 bg-destructive/5":0>Number.parseFloat(S)?"text-green-600 border-green-600/20 bg-green-50":"text-muted-foreground bg-muted"),children:S})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)(o.J,{htmlFor:"payment",className:"text-xs font-medium flex items-center gap-1.5",children:["收款方式",(0,l.jsx)(y.E,{variant:"destructive",className:"font-normal text-[10px] px-1 py-0 h-4",children:"必填"})]}),(0,l.jsxs)(d.l6,{value:J.payment,onValueChange:e=>V("payment",e),children:[(0,l.jsx)(d.bq,{className:(0,b.cn)("w-full h-9",z.payment&&"border-destructive"),id:"payment",children:(0,l.jsx)(d.yv,{placeholder:"选择收款方式"})}),(0,l.jsx)(d.gC,{children:w.uq.map(e=>(0,l.jsx)(d.eb,{value:e.id,children:e.label},e.id))})]}),z.payment&&(0,l.jsx)("p",{className:"text-xs text-destructive mt-1",children:z.payment})]})]})]}),(0,l.jsx)(N.w,{className:"my-1"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-muted-foreground",children:[(0,l.jsx)(u.A,{className:"h-3.5 w-3.5"}),(0,l.jsx)("h3",{children:"其他信息"})]}),(0,l.jsxs)("div",{className:"grid gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"salesRep",className:"text-xs font-medium",children:"销售员"}),(0,l.jsx)(k.A,{teacher:J.salesRep,placeholder:"选择销售员",setTeacher:e=>V("salesRep",e)})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)(o.J,{htmlFor:"date",className:"text-xs font-medium flex items-center gap-1.5",children:["收款时间",(0,l.jsx)(y.E,{variant:"destructive",className:"font-normal text-[10px] px-1 py-0 h-4",children:"必填"})]}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsxs)(p.AM,{children:[(0,l.jsx)(p.Wv,{asChild:!0,children:(0,l.jsxs)(n.$,{variant:"outline",className:(0,b.cn)("flex-1 justify-start text-left font-normal h-9",!(null==J?void 0:J.date)&&"text-muted-foreground",z.date&&"border-destructive"),id:"date",children:[(0,l.jsx)(x.A,{className:"mr-2 h-3.5 w-3.5"}),(null==J?void 0:J.date)?(0,h.GP)(null==J?void 0:J.date,"yyyy年MM月dd日",{locale:f.g}):"选择日期"]})}),(0,l.jsx)(p.hl,{className:"w-auto p-0",children:(0,l.jsx)(g.V,{mode:"single",selected:J.date,onSelect:e=>V("date",e),locale:f.g,initialFocus:!0})})]}),(0,l.jsx)(v.p,{type:"time",value:J.time,onChange:e=>V("time",e.target.value),className:"w-[120px] h-9",id:"time"})]}),z.date&&(0,l.jsx)("p",{className:"text-xs text-destructive mt-1",children:z.date})]})]})]})]})}),(0,l.jsxs)(r.Es,{className:"px-6 py-3 border-t flex flex-row justify-end gap-2",children:[(0,l.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>t(!1),disabled:E,children:"取消"}),(0,l.jsx)(n.$,{size:"sm",onClick:H,disabled:E,className:E?"opacity-80 pointer-events-none":"",children:E?"处理中...":"确认缴费"})]})]})}):null}},54165:(e,a,t)=>{t.d(a,{Cf:()=>x,Es:()=>g,HM:()=>m,L3:()=>p,c7:()=>b,lG:()=>n,rr:()=>h,zM:()=>i});var l=t(95155),s=t(12115),r=t(15452),d=t(54416),o=t(59434);let n=r.bL,i=r.l9,c=r.ZL,m=r.bm,u=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)(r.hJ,{ref:a,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...s})});u.displayName=r.hJ.displayName;let x=s.forwardRef((e,a)=>{let{className:t,children:s,...n}=e;return(0,l.jsxs)(c,{children:[(0,l.jsx)(u,{}),(0,l.jsxs)(r.UC,{ref:a,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...n,children:[s,(0,l.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,l.jsx)(d.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});x.displayName=r.UC.displayName;let b=e=>{let{className:a,...t}=e;return(0,l.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...t})};b.displayName="DialogHeader";let g=e=>{let{className:a,...t}=e;return(0,l.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};g.displayName="DialogFooter";let p=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)(r.hE,{ref:a,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",t),...s})});p.displayName=r.hE.displayName;let h=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)(r.VY,{ref:a,className:(0,o.cn)("text-sm text-muted-foreground",t),...s})});h.displayName=r.VY.displayName},57141:(e,a,t)=>{t.d(a,{C9:()=>s,DT:()=>o,I2:()=>d,IC:()=>u,N4:()=>c,fb:()=>x,lc:()=>l,oD:()=>n,u7:()=>m,uq:()=>r,x9:()=>i});let l={"limited-sessions":{label:"限次",color:"capitalize font-normal bg-blue-50 text-blue-700 hover:bg-blue-100 px-2.5 py-0.5 rounded-md"},"limited-time-and-count":{label:"时次限",color:"capitalize font-normal bg-violet-50 text-violet-700 hover:bg-violet-100 px-2.5 py-0.5 rounded-md"},default:{label:"未知套餐",color:"capitalize font-normal bg-slate-100 text-slate-700 hover:bg-slate-200 px-2.5 py-0.5 rounded-md"}},s={cash:{label:"现金",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},alipay:{label:"支付宝",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},wechat:{label:"微信",color:"bg-green-100 text-green-800 hover:bg-green-200"},card:{label:"银行卡",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},other:{label:"其他",color:"bg-slate-100 text-slate-800 hover:bg-slate-200"}},r=[{id:"wechat",label:"微信"},{id:"alipay",label:"支付宝"},{id:"card",label:"银行转账"},{id:"cash",label:"现金"},{id:"other",label:"其他"}],d={done:{label:"完成",color:"bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400"},arrears:{label:"欠费",color:"bg-amber-100 text-amber-800 hover:bg-amber-200 dark:bg-amber-900/30 dark:text-amber-400"},refunded:{label:"退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400"},default:{label:"未知",color:"bg-slate-100 text-slate-800 hover:bg-slate-200 dark:bg-slate-800/50 dark:text-slate-400"}},o={active:{badgeClass:"bg-emerald-50 text-emerald-700 hover:bg-emerald-100 border-emerald-200",dotClass:"bg-emerald-500",text:"正常"},expired:{badgeClass:"bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200",dotClass:"bg-amber-500",text:"已到期"},refunded:{badgeClass:"bg-red-50 text-red-700 hover:bg-red-100 border-red-200",dotClass:"bg-red-500",text:"已退款"},frozen:{badgeClass:"bg-slate-50 text-slate-700 hover:bg-slate-100 border-slate-200",dotClass:"bg-slate-500",text:"已冻结"},default:{badgeClass:"bg-slate-100 text-slate-700 hover:bg-slate-200 border-slate-200",dotClass:"bg-slate-400",text:"未知状态"}},n={all:{className:"bg-slate-100 text-slate-800 hover:bg-slate-200",text:"全部状态"},attendance:{className:"bg-green-100 text-green-800 hover:bg-green-200",text:"已考勤"},leave:{className:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200",text:"请假"},unattended:{className:"bg-orange-100 text-orange-800 hover:bg-orange-200",text:"未考勤"},default:{className:"bg-red-100 text-red-800 hover:bg-red-200",text:"缺勤"}},i={A:{label:"非常感兴趣",color:"bg-green-100 text-green-800 hover:bg-green-200"},B:{label:"比较感兴趣",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},C:{label:"一般意向",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},D:{label:"了解意向",color:"bg-orange-100 text-orange-800 hover:bg-orange-200"},E:{label:"不感兴趣",color:"bg-red-100 text-red-800 hover:bg-red-200"},default:{label:"未知",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},c={productSales:{label:"产品销售",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},courseSales:{label:"课程销售",color:"bg-green-100 text-green-800 hover:bg-green-200"},OverduePaymentCollection:{label:"逾期款项催收",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},lifestyleAndOffice:{label:"生活和办公",color:"bg-red-100 text-red-800 hover:bg-red-200"},socialAndCatering:{label:"社交和餐饮",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},refunded:{label:"退款",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"其他",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},m={completed:{label:"已完成",color:"bg-green-100 text-green-800 hover:bg-green-200"},pending:{label:"待审核",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},refunded:{label:"已退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},failed:{label:"审核未通过",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"未知状态",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},u={formal:{label:"正式学员",color:"text-blue-500"},graduated:{label:"历史学员",color:"text-green-500"},public:{label:"公海池",color:"text-red-500"},intent:{label:"意向学员",color:"text-yellow-500"}},x={secret:{label:"保密",color:"text-gray-500"},male:{label:"男",color:"text-blue-500"},female:{label:"女",color:"text-pink-500"}}},63375:(e,a,t)=>{t.d(a,{A:()=>n});var l=t(95155),s=t(12115),r=t(59409),d=t(59434),o=t(6658);let n=(0,s.memo)(function(e){let{teacher:a,setTeacher:t,width:n="w-full",placeholder:i="选择人员",className:c="",showAllOption:m=!0,allOptionText:u="全部人员",allOptionValue:x="all",teacherList:b,disabled:g=!1}=e,{data:p,isLoading:h,error:f}=(0,o.X)(),v=(0,s.useCallback)(e=>{t(e)},[t]),y=(0,s.useCallback)(()=>(0,d.cn)("h-9 ".concat(n),c),[n,c]),N=b||p;return(0,l.jsxs)(r.l6,{value:a,onValueChange:v,disabled:g||h,children:[(0,l.jsx)(r.bq,{className:y(),children:(0,l.jsx)(r.yv,{placeholder:i})}),(0,l.jsxs)(r.gC,{children:[f&&(0,l.jsx)(r.eb,{value:"error",disabled:!0,children:String(f)}),h&&(0,l.jsx)(r.eb,{value:"loading",disabled:!0,children:"加载中..."}),!h&&!f&&(0,l.jsxs)(l.Fragment,{children:[m&&(0,l.jsx)(r.eb,{value:x,children:u}),null==N?void 0:N.map(e=>(0,l.jsx)(r.eb,{value:e.id,children:e.name},e.id))]})]})]})})},85057:(e,a,t)=>{t.d(a,{J:()=>i});var l=t(95155),s=t(12115),r=t(40968),d=t(74466),o=t(59434);let n=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)(r.b,{ref:a,className:(0,o.cn)(n(),t),...s})});i.displayName=r.b.displayName},85511:(e,a,t)=>{t.d(a,{V:()=>i});var l=t(95155);t(12115);var s=t(42355),r=t(13052),d=t(20081),o=t(59434),n=t(30285);function i(e){let{className:a,classNames:t,showOutsideDays:i=!0,...c}=e;return(0,l.jsx)(d.hv,{showOutsideDays:i,className:(0,o.cn)("p-3",a),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,o.cn)((0,n.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,o.cn)((0,n.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:e=>{let{className:a,...t}=e;return(0,l.jsx)(s.A,{className:(0,o.cn)("h-4 w-4",a),...t})},IconRight:e=>{let{className:a,...t}=e;return(0,l.jsx)(r.A,{className:(0,o.cn)("h-4 w-4",a),...t})}},...c})}i.displayName="Calendar"}}]);