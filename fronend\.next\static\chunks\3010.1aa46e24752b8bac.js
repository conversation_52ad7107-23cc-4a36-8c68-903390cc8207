"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3010],{61809:(e,a,t)=>{t.d(a,{Y:()=>d});var r=t(44861),l=t(73168),s=t(24122);let d=function(e){let a,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"yyyy-MM-dd",d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(null==e)return d;try{if("string"==typeof e)a=new Date(e);else if("number"==typeof e)a=new Date(e);else{if(!(e instanceof Date))return d;a=e}if(!(0,r.f)(a))return d;return(0,l.GP)(a,t,{locale:s.g})}catch(e){return console.error("Date formatting error:",e),d}}},66695:(e,a,t)=>{t.d(a,{BT:()=>o,Wu:()=>i,ZB:()=>c,Zp:()=>d,aR:()=>n,wL:()=>u});var r=t(95155),l=t(12115),s=t(59434);let d=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:a,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...l})});d.displayName="Card";let n=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:a,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",t),...l})});n.displayName="CardHeader";let c=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:a,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",t),...l})});c.displayName="CardTitle";let o=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:a,className:(0,s.cn)("text-sm text-muted-foreground",t),...l})});o.displayName="CardDescription";let i=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:a,className:(0,s.cn)("p-6 pt-0",t),...l})});i.displayName="CardContent";let u=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:a,className:(0,s.cn)("flex items-center p-6 pt-0",t),...l})});u.displayName="CardFooter"},73010:(e,a,t)=>{t.r(a),t.d(a,{default:()=>j});var r=t(95155),l=t(12115),s=t(47924),d=t(9110),n=t(26126),c=t(55028),o=t(45968),i=t(79181),u=t(50228),f=t(61809);let m=(0,c.default)(()=>t.e(1289).then(t.bind(t,33670)),{loadableGenerated:{webpack:()=>[33670]},ssr:!1}),p=(0,c.default)(()=>t.e(2248).then(t.bind(t,82248)),{loadableGenerated:{webpack:()=>[82248]},ssr:!1}),g=(0,c.default)(()=>t.e(8180).then(t.bind(t,55799)),{loadableGenerated:{webpack:()=>[55799]},ssr:!1}),h=[{accessorKey:"name",header:"学员名称",cell:e=>{let{row:a}=e;return(0,u.P)(a.getValue("name"))}},{accessorKey:"phone",header:"手机号码",cell:e=>{let{row:a}=e;return(0,u.P)(a.getValue("phone"))}},{accessorKey:"age",header:"年龄",cell:e=>{let{row:a}=e;return(0,u.P)((0,o.C)(a.original.birthday||""))}},{accessorKey:"gender",header:"性别",cell:e=>{let{row:a}=e;return(0,u.P)((0,i.$)(a.getValue("gender")))}},{accessorKey:"address",header:"家庭住址",cell:e=>{let{row:a}=e;return(0,u.s)(a.getValue("address"),4)}},{accessorKey:"birthday",header:"学员生日",cell:e=>{let{row:a}=e,t=a.original.birthday;return t?(0,u.P)((0,f.Y)(new Date(t),"MM-dd"),!0):(0,u.P)("-",!0)}},{accessorKey:"follower",header:"跟进人",cell:e=>{var a;let{row:t}=e;return(0,u.P)((0,r.jsx)(n.E,{variant:"outline",className:"font-normal text-xs",children:null===(a=t.original.follower)||void 0===a?void 0:a.name}))}},{accessorKey:"remarks",header:"备注",cell:e=>{let{row:a}=e;return(0,u.s)(a.getValue("remarks"))}},{accessorKey:"createdAt",header:"加入时间",cell:e=>{let{row:a}=e;return(0,u.s)((0,f.Y)(a.getValue("createdAt"),"yyyy-MM-dd HH:mm:ss"))}},{id:"actions",header:"操作",cell:e=>{let{row:a}=e,t=a.original;return(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m,{studentId:t.id},"studentInfoDialog"),(0,r.jsx)(p,{student:t}),(0,r.jsx)(g,{studentId:t.id,studentType:t.type})]})}}];var x=t(48432),y=t(62523),v=t(66695),b=t(95728),w=t(45964),N=t.n(w);let j=function(){let[e,a]=(0,l.useState)(1),[t,n]=(0,l.useState)(10),[c,o]=(0,l.useState)(""),i=(0,l.useCallback)(N()(e=>{o(e),a(1)},500),[]),u=(0,l.useMemo)(()=>({page:e,pageSize:t,search:c,type:"graduated"}),[e,t,c]),{data:f,isLoading:m}=(0,b.FQ)(u);return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(v.Zp,{className:"border-slate-200",children:(0,r.jsx)(v.Wu,{className:"p-4",children:(0,r.jsx)("div",{className:"flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-end md:justify-between",children:(0,r.jsx)("div",{className:"w-full md:w-auto md:flex-1 md:max-w-md",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(s.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,r.jsx)(y.p,{type:"text",placeholder:"搜索学员姓名",onChange:e=>i(e.target.value),className:"pl-10 bg-slate-50 border-slate-200 focus:bg-white transition-colors"})]})})})})}),(0,r.jsx)(d.b,{columns:h,data:(null==f?void 0:f.list)||[],loading:m,pagination:!1}),(0,r.jsx)(x.default,{currentPage:(null==f?void 0:f.page)||1,pageSize:(null==f?void 0:f.pageSize)||10,totalItems:(null==f?void 0:f.total)||0,onPageChange:a,onPageSizeChange:n})]})}}}]);