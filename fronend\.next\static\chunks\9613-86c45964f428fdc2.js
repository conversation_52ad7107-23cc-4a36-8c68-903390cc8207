"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9613],{89613:(e,t,r)=>{r.d(t,{Kq:()=>H,UC:()=>U,bL:()=>S,l9:()=>q});var n=r(12115),o=r(85185),i=r(6101),l=r(46081),a=r(19178),s=r(61285),u=r(35152),c=(r(34378),r(28905)),d=r(63655),p=r(99708),f=r(5845),x=r(2564),h=r(95155),[v,g]=(0,l.A)("Tooltip",[u.Bk]),y=(0,u.Bk)(),b="TooltipProvider",m="tooltip.open",[w,C]=v(b),T=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,[a,s]=n.useState(!0),u=n.useRef(!1),c=n.useRef(0);return n.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,h.jsx)(w,{scope:t,isOpenDelayed:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(c.current),s(!1)},[]),onClose:n.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>s(!0),o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:n.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:l})};T.displayName=b;var E="Tooltip",[k,L]=v(E),R=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:i=!1,onOpenChange:l,disableHoverableContent:a,delayDuration:c}=e,d=C(E,e.__scopeTooltip),p=y(t),[x,v]=n.useState(null),g=(0,s.B)(),b=n.useRef(0),w=null!=a?a:d.disableHoverableContent,T=null!=c?c:d.delayDuration,L=n.useRef(!1),[R=!1,j]=(0,f.i)({prop:o,defaultProp:i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(m))):d.onClose(),null==l||l(e)}}),_=n.useMemo(()=>R?L.current?"delayed-open":"instant-open":"closed",[R]),P=n.useCallback(()=>{window.clearTimeout(b.current),b.current=0,L.current=!1,j(!0)},[j]),M=n.useCallback(()=>{window.clearTimeout(b.current),b.current=0,j(!1)},[j]),D=n.useCallback(()=>{window.clearTimeout(b.current),b.current=window.setTimeout(()=>{L.current=!0,j(!0),b.current=0},T)},[T,j]);return n.useEffect(()=>()=>{b.current&&(window.clearTimeout(b.current),b.current=0)},[]),(0,h.jsx)(u.bL,{...p,children:(0,h.jsx)(k,{scope:t,contentId:g,open:R,stateAttribute:_,trigger:x,onTriggerChange:v,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayed?D():P()},[d.isOpenDelayed,D,P]),onTriggerLeave:n.useCallback(()=>{w?M():(window.clearTimeout(b.current),b.current=0)},[M,w]),onOpen:P,onClose:M,disableHoverableContent:w,children:r})})};R.displayName=E;var j="TooltipTrigger",_=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...l}=e,a=L(j,r),s=C(j,r),c=y(r),p=n.useRef(null),f=(0,i.s)(t,p,a.onTriggerChange),x=n.useRef(!1),v=n.useRef(!1),g=n.useCallback(()=>x.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,h.jsx)(u.Mz,{asChild:!0,...c,children:(0,h.jsx)(d.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"===e.pointerType||v.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),v.current=!0)}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),v.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{x.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{x.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});_.displayName=j;var[P,M]=v("TooltipPortal",{forceMount:void 0}),D="TooltipContent",O=n.forwardRef((e,t)=>{let r=M(D,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,l=L(D,e.__scopeTooltip);return(0,h.jsx)(c.C,{present:n||l.open,children:l.disableHoverableContent?(0,h.jsx)(A,{side:o,...i,ref:t}):(0,h.jsx)(B,{side:o,...i,ref:t})})}),B=n.forwardRef((e,t)=>{let r=L(D,e.__scopeTooltip),o=C(D,e.__scopeTooltip),l=n.useRef(null),a=(0,i.s)(t,l),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,p=l.current,{onPointerInTransitChange:f}=o,x=n.useCallback(()=>{u(null),f(!1)},[f]),v=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>x(),[x]),n.useEffect(()=>{if(c&&p){let e=e=>v(e,p),t=e=>v(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,v,x]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e].x,a=t[e].y,s=t[i].x,u=t[i].y;a>n!=u>n&&r<(s-l)*(n-a)/(u-a)+l&&(o=!o)}return o}(r,s);n?x():o&&(x(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,d,x]),(0,h.jsx)(A,{...e,ref:a})}),[I,N]=v(E,{isInside:!1}),A=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:s,...c}=e,d=L(D,r),f=y(r),{onClose:v}=d;return n.useEffect(()=>(document.addEventListener(m,v),()=>document.removeEventListener(m,v)),[v]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&v()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,v]),(0,h.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:v,children:(0,h.jsxs)(u.UC,{"data-state":d.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,h.jsx)(p.xV,{children:o}),(0,h.jsx)(I,{scope:r,isInside:!0,children:(0,h.jsx)(x.b,{id:d.contentId,role:"tooltip",children:i||o})})]})})});O.displayName=D;var F="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=y(r);return N(F,r).isInside?null:(0,h.jsx)(u.i3,{...o,...n,ref:t})}).displayName=F;var H=T,S=R,q=_,U=O}}]);