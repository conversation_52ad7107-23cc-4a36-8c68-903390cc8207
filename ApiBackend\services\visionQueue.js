import { Queue, Worker } from "bullmq";
import { redisTask, redisConfig } from "../config/redis.js";
import { analyzeImage, captionImage } from "./visionService.js";

// 队列配置
const queueOptions = {
  connection: redisConfig,
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 1000
    },
    removeOnComplete: {
      age: 3600,
      count: 1000
    },
    removeOnFail: {
      age: 3600 * 24 * 7
    },
    lockDuration: 300000,
    timeout: 300000,
  },
  limiter: {
    max: 5,
    duration: 1000,
    groupKey: 'vision-processing'
  },
  streams: {
    events: {
      maxLen: 10000
    }
  }
};

const visionQueue = new Queue('vision-processing', queueOptions);

// 添加错误处理
visionQueue.on('error', (error) => {
  console.error('视觉处理队列错误:', error);
});

visionQueue.on('failed', (job, error) => {
  console.error(`视觉处理任务 ${job.id} 失败:`, error);
});

// Worker配置
const workerOptions = {
  connection: redisConfig,
  concurrency: 5,
  lockDuration: 300000,
  stalledInterval: 30000,
  maxStalledCount: 2,
  drainDelay: 5,
  autorun: true,
  metrics: {
    maxDataPoints: 100
  }
};

// 创建Worker处理任务
const worker = new Worker(
  'vision-processing',
  async (job) => {
    const { taskId, type } = job.data;
    console.log(`开始处理视觉任务 ${job.id}, taskId: ${taskId}, type: ${type}`);

    const startTime = Date.now();

    try {
      // 更新任务状态为处理中
      await redisTask.multi()
        .set(
          `task:${taskId}`,
          JSON.stringify({ status: 'processing', taskId, progress: 0, type }),
          'EX',
          3600
        )
        .set(
          `task:${taskId}:start`,
          startTime.toString(),
          'EX',
          3600
        )
        .exec();

      // 定期更新进度
      const progressInterval = setInterval(async () => {
        try {
          const elapsed = Date.now() - startTime;
          const progressPercent = Math.min(Math.floor((elapsed / 60000) * 100), 95);

          await job.updateProgress(progressPercent);
        } catch (e) {
          console.error('更新进度失败:', e);
        }
      }, 3000);

      // 根据任务类型执行不同的处理
      let result;
      if (type === 'image-analysis') {
        const { imageUrl, analysisType } = job.data;
        result = await Promise.race([
          analyzeImage(imageUrl, analysisType),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('图像分析超时')), 270000) // 4.5分钟超时
          )
        ]);
      } else if (type === 'image-caption') {
        const { imageUrl, language, detailLevel } = job.data;
        result = await Promise.race([
          captionImage(imageUrl, language, detailLevel),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('图像描述超时')), 270000) // 4.5分钟超时
          )
        ]);
      } else {
        throw new Error(`未知的视觉处理类型: ${type}`);
      }

      // 清除进度更新定时器
      clearInterval(progressInterval);

      // 计算处理时间
      const processingTime = Date.now() - startTime;

      // 更新任务状态为完成
      await redisTask.multi()
        .set(
          `task:${taskId}`,
          JSON.stringify({
            status: 'completed',
            taskId,
            result,
            processingTime,
            type
          }),
          'EX',
          3600
        )
        .set(
          `task:${taskId}:end`,
          Date.now().toString(),
          'EX',
          3600
        )
        .exec();

      console.log(`视觉处理任务 ${job.id} 完成，处理时间: ${processingTime}ms`);
      return result;
    } catch (error) {
      console.error(`视觉处理任务 ${job.id} 失败:`, error);

      // 更新任务状态为失败
      await redisTask.set(
        `task:${taskId}`,
        JSON.stringify({
          status: 'failed',
          taskId,
          error: error.message,
          stack: error.stack,
          processingTime: Date.now() - startTime,
          type
        }),
        'EX',
        3600
      );

      // 重新抛出错误，让BullMQ处理重试
      throw error;
    }
  },
  workerOptions
);

// 添加Worker错误处理
worker.on('error', (error) => {
  console.error('视觉处理Worker错误:', error);
});

worker.on('failed', (job, error) => {
  console.error(`视觉处理Worker处理任务 ${job?.id} 失败:`, error);
});

worker.on('completed', (job) => {
  console.log(`视觉处理Worker完成任务 ${job.id}`);
});

export default visionQueue;
