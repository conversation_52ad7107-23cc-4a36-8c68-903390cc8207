"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5121,7502],{13052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19946:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(12115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:d=2,absoluteStrokeWidth:u,className:l="",children:s,iconNode:c,...h}=e;return(0,r.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:n,strokeWidth:u?24*Number(d)/Number(o):d,className:a("lucide",l),...h},[...c.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(s)?s:[s]])}),u=(e,t)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:u,...l}=n;return(0,r.createElement)(d,{ref:i,iconNode:t,className:a("lucide-".concat(o(e)),u),...l})});return n.displayName="".concat(e),n}},20547:(e,t,n)=>{n.d(t,{UC:()=>B,ZL:()=>q,bL:()=>z,l9:()=>X});var r=n(12115),o=n(85185),a=n(6101),i=n(46081),d=n(19178),u=n(92293),l=n(25519),s=n(61285),c=n(35152),h=n(34378),m=n(28905),p=n(63655),f=n(99708),v=n(5845),g=n(38168),w=n(93795),y=n(95155),b="Popover",[P,C]=(0,i.A)(b,[c.Bk]),x=(0,c.Bk)(),[N,k]=P(b),A=e=>{let{__scopePopover:t,children:n,open:o,defaultOpen:a,onOpenChange:i,modal:d=!1}=e,u=x(t),l=r.useRef(null),[h,m]=r.useState(!1),[p=!1,f]=(0,v.i)({prop:o,defaultProp:a,onChange:i});return(0,y.jsx)(c.bL,{...u,children:(0,y.jsx)(N,{scope:t,contentId:(0,s.B)(),triggerRef:l,open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),hasCustomAnchor:h,onCustomAnchorAdd:r.useCallback(()=>m(!0),[]),onCustomAnchorRemove:r.useCallback(()=>m(!1),[]),modal:d,children:n})})};A.displayName=b;var M="PopoverAnchor";r.forwardRef((e,t)=>{let{__scopePopover:n,...o}=e,a=k(M,n),i=x(n),{onCustomAnchorAdd:d,onCustomAnchorRemove:u}=a;return r.useEffect(()=>(d(),()=>u()),[d,u]),(0,y.jsx)(c.Mz,{...i,...o,ref:t})}).displayName=M;var O="PopoverTrigger",R=r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,i=k(O,n),d=x(n),u=(0,a.s)(t,i.triggerRef),l=(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":L(i.open),...r,ref:u,onClick:(0,o.m)(e.onClick,i.onOpenToggle)});return i.hasCustomAnchor?l:(0,y.jsx)(c.Mz,{asChild:!0,...d,children:l})});R.displayName=O;var W="PopoverPortal",[E,j]=P(W,{forceMount:void 0}),T=e=>{let{__scopePopover:t,forceMount:n,children:r,container:o}=e,a=k(W,t);return(0,y.jsx)(E,{scope:t,forceMount:n,children:(0,y.jsx)(m.C,{present:n||a.open,children:(0,y.jsx)(h.Z,{asChild:!0,container:o,children:r})})})};T.displayName=W;var D="PopoverContent",S=r.forwardRef((e,t)=>{let n=j(D,e.__scopePopover),{forceMount:r=n.forceMount,...o}=e,a=k(D,e.__scopePopover);return(0,y.jsx)(m.C,{present:r||a.open,children:a.modal?(0,y.jsx)(F,{...o,ref:t}):(0,y.jsx)(_,{...o,ref:t})})});S.displayName=D;var F=r.forwardRef((e,t)=>{let n=k(D,e.__scopePopover),i=r.useRef(null),d=(0,a.s)(t,i),u=r.useRef(!1);return r.useEffect(()=>{let e=i.current;if(e)return(0,g.Eq)(e)},[]),(0,y.jsx)(w.A,{as:f.DX,allowPinchZoom:!0,children:(0,y.jsx)(I,{...e,ref:d,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),u.current||null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;u.current=2===t.button||n},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),_=r.forwardRef((e,t)=>{let n=k(D,e.__scopePopover),o=r.useRef(!1),a=r.useRef(!1);return(0,y.jsx)(I,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,i;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,i;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let d=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(d))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),I=r.forwardRef((e,t)=>{let{__scopePopover:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:i,onEscapeKeyDown:s,onPointerDownOutside:h,onFocusOutside:m,onInteractOutside:p,...f}=e,v=k(D,n),g=x(n);return(0,u.Oh)(),(0,y.jsx)(l.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,y.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:p,onEscapeKeyDown:s,onPointerDownOutside:h,onFocusOutside:m,onDismiss:()=>v.onOpenChange(!1),children:(0,y.jsx)(c.UC,{"data-state":L(v.open),role:"dialog",id:v.contentId,...g,...f,ref:t,style:{...f.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),U="PopoverClose";function L(e){return e?"open":"closed"}r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,a=k(U,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})}).displayName=U,r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=x(n);return(0,y.jsx)(c.i3,{...o,...r,ref:t})}).displayName="PopoverArrow";var z=A,X=R,q=T,B=S},24122:(e,t,n)=>{n.d(t,{g:()=>h});let r={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}};var o=n(67356);let a={date:(0,o.k)({formats:{full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},defaultWidth:"full"}),time:(0,o.k)({formats:{full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},defaultWidth:"full"}),dateTime:(0,o.k)({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};var i=n(34548);function d(e,t,n){var r,o,a;let d="eeee p";return(r=e,o=t,a=n,+(0,i.k)(r,a)==+(0,i.k)(o,a))?d:e.getTime()>t.getTime()?"'下个'"+d:"'上个'"+d}let u={lastWeek:d,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:d,other:"PP p"};var l=n(58698);let s={ordinalNumber:(e,t)=>{let n=Number(e);switch(null==t?void 0:t.unit){case"date":return n.toString()+"日";case"hour":return n.toString()+"时";case"minute":return n.toString()+"分";case"second":return n.toString()+"秒";default:return"第 "+n.toString()}},era:(0,l.o)({values:{narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},defaultWidth:"wide"}),quarter:(0,l.o)({values:{narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,l.o)({values:{narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},defaultWidth:"wide"}),day:(0,l.o)({values:{narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},defaultWidth:"wide"}),dayPeriod:(0,l.o)({values:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultWidth:"wide",formattingValues:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultFormattingWidth:"wide"})};var c=n(44008);let h={code:"zh-CN",formatDistance:(e,t,n)=>{let o;let a=r[e];return(o="string"==typeof a?a:1===t?a.one:a.other.replace("{{count}}",String(t)),null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?o+"内":o+"前":o},formatLong:a,formatRelative:(e,t,n,r)=>{let o=u[e];return"function"==typeof o?o(t,n,r):o},localize:s,match:{ordinalNumber:(0,n(40972).K)({matchPattern:/^(第\s*)?\d+(日|时|分|秒)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,c.A)({matchPatterns:{narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(前)/i,/^(公元)/i]},defaultParseWidth:"any"}),quarter:(0,c.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,c.A)({matchPatterns:{narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},defaultParseWidth:"any"}),day:(0,c.A)({matchPatterns:{narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},defaultParseWidth:"any"}),dayPeriod:(0,c.A)({matchPatterns:{any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}}},28905:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(12115),o=n(6101),a=n(52712),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),u=r.useRef({}),l=r.useRef(e),s=r.useRef("none"),[c,h]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=d(u.current);s.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let t=u.current,n=l.current;if(n!==e){let r=s.current,o=d(t);e?h("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?h("UNMOUNT"):n&&r!==o?h("ANIMATION_OUT"):h("UNMOUNT"),l.current=e}},[e,h]),(0,a.N)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=d(u.current).includes(e.animationName);if(e.target===o&&r&&(h("ANIMATION_END"),!l.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(s.current=d(u.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}h("ANIMATION_END")},[o,h]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{e&&(u.current=getComputedStyle(e)),i(e)},[])}}(t),u="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),l=(0,o.s)(i.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||i.isPresent?r.cloneElement(u,{ref:l}):null};function d(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},42355:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},47863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},63655:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>d});var r=n(12115),o=n(47650),a=n(99708),i=n(95155),d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...o}=e,d=r?a.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(d,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},66474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},69074:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}}]);