[
    {
        id: 'a7e46980-6adc-4af3-8553-d419f110cc44',
        name: '仪表盘',
        path: '/dashboard',
        icon: 'ChartBarIcon',
        component: 'Dashboard',
        sort: 1,
        permissionCode: 'dashboard:access',
    },
    // 教务中心
    {
        id: 'b7e46980-6adc-4af3-8553-d419f110cc44',
        name: '教务中心',
        path: '#',
        icon: 'AcademicCapIcon',
        // permissionCode: 'academic:access',
        sort: 2
    },
    // 教务中心子菜单
    {
        name: '学员管理',
        path: '/academic/student',
        icon: 'UserGroupIcon',
        sort: 1,
        parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'student:read',
    },
    // 班级管理
    {
 
        name: '班级管理',
        path: '/academic/classes',
        sort: 2,
        icon: 'BuildingOfficeIcon',
        parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'class:read',
    },
    {
 
        name: '课表管理',
        path: '/academic/schedule',
        sort: 3,
        icon: 'ChartPieIcon',
        parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'schedule:read',
    },
    {
 
        name: '课程管理',
        path: '/academic/courses',
        sort: 4,
        icon: 'ChartBarIcon',
        parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'course:read',
    },
    {
 
        name: '套餐管理',
        path: '/academic/product',
        sort: 5,
        icon: 'ShoppingBagIcon',
        parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'course:read',
    },


    // 招生中心
    {
        id: 'c7e46980-6adc-4af3-8553-d419f110cc44',
        name: '招生中心',
        path: '#enrollment',
        icon: 'BriefcaseIcon',
        sort: 3
    },
    // 招生中心子菜单
    {
        name: '跟进管理',
        path: '/enrollment/followUp',
        icon: 'TagIcon',
        sort: 1,
        parentId: 'c7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'followUp:read',
    },

    // 财务中心
    {
        id: 'd7e46980-6adc-4af3-8553-d419f110cc44',
        name: '财务中心',
        path: '#finance',
        icon: 'CurrencyDollarIcon',
        sort: 4
    },
    // 财务中心子菜单
    {
        name: '销售记录',
        path: '/finance/receipt',
        icon: 'ShoppingBagIcon',
        sort: 1,
        parentId: 'd7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'finance:sales:read',
    },
    {
        name: '收银记账',
        path: '/finance/cashier',
        icon: 'CurrencyDollarIcon',
        sort: 2,
        parentId: 'd7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'finance:bill:read',
    },
    {
        name: '工资管理',
        path: '/finance/wages',
        icon: 'CurrencyDollarIcon',
        sort: 3,
        parentId: 'd7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'finance:salary:read',
    },


    // 数据中心
   
    {
        id: 'e7e46980-6adc-4af3-8553-d419f110cc44',
        name: '数据中心',
        path: '#data',
        icon: 'ChartPieIcon',
        sort: 5
    },
    // 数据中心子菜单
    {
        name: '教务数据',
        path: '/data/education',
        icon: 'ChartPieIcon',
        sort: 1,
        parentId: 'e7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'data:academic:read',
    },
    {
        name: '销售数据',
        path: '/data/sales',
        icon: 'CurrencyDollarIcon',
        sort: 2,
        parentId: 'e7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'data:sales:read',
    },    


    // 机构中心
    
    {
        id: 'f7e46980-6adc-4af3-8553-d419f110cc44',
        name: '机构中心',
        path: '#organization',
        icon: 'BuildingOfficeIcon',
        sort: 6
    },
    // 机构中心子菜单
    {
        name: '机构信息',
        path: '/organization/info',
        icon: 'InformationCircleIcon',
        sort: 1,
        parentId: 'f7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'org:info:read',
    },
    {
        name: '员工管理',
        path: '/organization/staff',
        icon: 'UserGroupIcon',
        sort: 2,
        parentId: 'f7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'org:staff:read',
    },
    {
        name: '教室管理',
        path: '/organization/classrooms',
        icon: 'BuildingOfficeIcon',
        sort: 3,
        parentId: 'f7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'org:classroom:read',
    },
    {
        name: '操作日志',
        path: '/organization/log',
        icon: 'InboxIcon',
        sort: 4,
        parentId: 'f7e46980-6adc-4af3-8553-d419f110cc44',
        permissionCode: 'org:log:read',
    },
]