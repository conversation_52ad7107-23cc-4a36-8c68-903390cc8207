(()=>{var e={};e.id=5344,e.ids=[5344],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,s)=>{let{createProxy:r}=s(39844);e.exports=r("F:\\trae\\cardmees\\fronend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17720:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26373:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim();var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:d="",children:n,iconNode:x,...c},o)=>(0,r.createElement)("svg",{ref:o,...l,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:i("lucide",d),...c},[...x.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(n)?n:[n]])),n=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...l},n)=>(0,r.createElement)(d,{ref:n,iconNode:t,className:i(`lucide-${a(e)}`,s),...l}));return s.displayName=`${e}`,s}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34653:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var r=s(37413);s(61120);var a=s(4536),i=s.n(a),l=s(26373);let d=(0,l.A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var n=s(83799),x=s(61227),c=s(90230);let o=(0,l.A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),m=(0,l.A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);var h=s(51465),p=s(75234),g=s(41382),u=s(91142);let b=function(){let e=[{icon:d,title:"学员档案管理",description:"建立完整的学员个人档案，包括基本信息、联系方式、家庭背景等",details:["个人基本信息录入","家庭联系方式管理","学员照片上传","特殊需求记录","档案信息导入导出"]},{icon:n.A,title:"学习进度跟踪",description:"实时监控学员学习状态，记录学习轨迹，分析学习效果",details:["课程进度实时更新","学习时长统计","知识点掌握情况","学习行为分析","进度可视化图表"]},{icon:x.A,title:"成绩记录统计",description:"多维度成绩管理，支持各类考试、作业、测验成绩录入与分析",details:["多类型成绩录入","成绩趋势分析","排名统计功能","成绩报告生成","家长成绩通知"]},{icon:c.A,title:"家长沟通记录",description:"记录与家长的沟通历史，建立良好的家校合作关系",details:["沟通记录管理","家长反馈收集","问题跟进处理","沟通提醒设置","家长满意度调查"]},{icon:o,title:"个性化学习计划",description:"根据学员特点制定个性化学习方案，提高学习效率",details:["学习能力评估","个性化计划制定","学习目标设定","计划执行监控","效果评估调整"]},{icon:m,title:"智能搜索查询",description:"快速检索学员信息，支持多条件组合搜索",details:["多字段搜索","高级筛选功能","搜索结果导出","常用搜索保存","批量操作支持"]}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50 backdrop-blur-sm bg-opacity-90 dark:bg-opacity-90",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(i(),{href:"/features",className:"flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors",children:[(0,r.jsx)(h.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"返回功能列表"})]})}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)(p.A,{className:"h-8 w-8 text-indigo-600 dark:text-indigo-400"}),(0,r.jsxs)("span",{className:"text-xl font-bold text-gray-800 dark:text-white",children:[(0,r.jsx)("span",{className:"text-indigo-600 dark:text-indigo-400",children:"Card"}),"Mees"]})]})}),(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsx)(i(),{href:"/dashboard",className:"px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-lg transition-colors",children:"进入系统"})})]})})}),(0,r.jsx)("div",{className:"bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-800 dark:via-gray-900 dark:to-gray-800 py-20",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(0,r.jsx)("div",{className:"w-20 h-20 bg-blue-100 dark:bg-blue-900/30 rounded-2xl flex items-center justify-center",children:(0,r.jsx)(g.A,{className:"w-10 h-10 text-blue-600 dark:text-blue-400"})})}),(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white sm:text-5xl mb-4",children:"学员管理系统"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"全方位的学员信息管理解决方案，从档案建立到学习跟踪，让每一位学员都得到个性化关注"})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"核心功能特性"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"六大核心模块，构建完整的学员管理体系"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map((e,t)=>{let s=e.icon;return(0,r.jsx)("div",{className:"group",children:(0,r.jsxs)("div",{className:"relative p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-700",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-300"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,r.jsx)(s,{className:"w-6 h-6 text-blue-600 dark:text-blue-400"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:e.description}),(0,r.jsx)("ul",{className:"space-y-2",children:e.details.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 text-green-500"}),(0,r.jsx)("span",{children:e})]},t))})]})]})},t)})})]}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 py-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"使用效果统计"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"数据证明学员管理系统的显著效果"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{title:"提高管理效率",description:"数字化管理减少人工操作，提升工作效率",percentage:"85%"},{title:"增强家校沟通",description:"及时的信息反馈，增进家长信任",percentage:"92%"},{title:"个性化教学",description:"基于数据的个性化教学方案",percentage:"78%"},{title:"学习效果提升",description:"科学的跟踪分析，提升学习成果",percentage:"89%"}].map((e,t)=>(0,r.jsx)("div",{className:"text-center group",children:(0,r.jsxs)("div",{className:"relative p-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-100 dark:border-blue-800/30 hover:shadow-lg transition-all duration-300",children:[(0,r.jsx)("div",{className:"text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 mb-2",children:e.percentage}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-sm",children:e.description})]})},t))})]})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"实际应用场景"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"看看学员管理系统如何在日常教学中发挥作用"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,r.jsx)("span",{className:"text-blue-600 dark:text-blue-400 font-semibold text-sm",children:"1"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"新生入学管理"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"快速建立新生档案，录入基本信息，设置学习计划，通知家长相关事宜，确保新生顺利融入学习环境。"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,r.jsx)("span",{className:"text-blue-600 dark:text-blue-400 font-semibold text-sm",children:"2"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"学习进度监控"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"实时跟踪学员课程进度，及时发现学习困难，调整教学策略，确保每位学员都能跟上学习节奏。"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,r.jsx)("span",{className:"text-blue-600 dark:text-blue-400 font-semibold text-sm",children:"3"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"家长沟通协调"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"定期与家长沟通学员表现，收集家长反馈，协调家校合作，共同促进学员成长发展。"})]})]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-8 border border-blue-100 dark:border-blue-800/30",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-6",children:"系统优势"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(u.A,{className:"w-5 h-5 text-green-500"}),(0,r.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"一站式学员信息管理"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(u.A,{className:"w-5 h-5 text-green-500"}),(0,r.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"智能化数据分析"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(u.A,{className:"w-5 h-5 text-green-500"}),(0,r.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"个性化学习方案"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(u.A,{className:"w-5 h-5 text-green-500"}),(0,r.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"高效家校沟通"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(u.A,{className:"w-5 h-5 text-green-500"}),(0,r.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"实时进度跟踪"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(u.A,{className:"w-5 h-5 text-green-500"}),(0,r.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"多维度成绩分析"})]})]})]})]})]}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-800 dark:to-indigo-900",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8 text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"开始使用学员管理系统"}),(0,r.jsx)("p",{className:"text-xl text-blue-200 mb-8",children:"让每一位学员都得到个性化的关注和管理"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(i(),{href:"/dashboard",className:"px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors",children:"立即体验"}),(0,r.jsx)(i(),{href:"/features",className:"px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors",children:"查看其他功能"})]})]})})]})}},40109:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>o,pages:()=>c,routeModule:()=>m,tree:()=>x});var r=s(65239),a=s(48088),i=s(88170),l=s.n(i),d=s(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);s.d(t,n);let x={children:["",{children:["features",{children:["student-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,34653)),"F:\\trae\\cardmees\\fronend\\src\\app\\features\\student-management\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["F:\\trae\\cardmees\\fronend\\src\\app\\features\\student-management\\page.tsx"],o={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/features/student-management/page",pathname:"/features/student-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:x}})},41382:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},51465:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},52568:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61227:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75234:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83799:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},83997:e=>{"use strict";e.exports=require("tty")},90230:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},91142:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7392,5814,3019],()=>s(40109));module.exports=r})();