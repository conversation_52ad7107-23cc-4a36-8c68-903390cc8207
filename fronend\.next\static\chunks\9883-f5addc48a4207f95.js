"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9883],{5623:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},6101:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>a});var n=r(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(a(...e),e)}},19946:(e,t,r)=>{r.d(t,{A:()=>d});var n=r(12115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:u="",children:s,iconNode:c,...h}=e;return(0,n.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:r,strokeWidth:d?24*Number(l)/Number(o):l,className:a("lucide",u),...h},[...c.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(s)?s:[s]])}),d=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:d,...u}=r;return(0,n.createElement)(l,{ref:i,iconNode:t,className:a("lucide-".concat(o(e)),d),...u})});return r.displayName="".concat(e),r}},20547:(e,t,r)=>{r.d(t,{UC:()=>B,ZL:()=>I,bL:()=>T,l9:()=>V});var n=r(12115),o=r(85185),a=r(6101),i=r(46081),l=r(19178),d=r(92293),u=r(25519),s=r(61285),c=r(35152),h=r(34378),p=r(28905),f=r(63655),m=r(99708),v=r(5845),g=r(38168),y=r(93795),w=r(95155),b="Popover",[x,k]=(0,i.A)(b,[c.Bk]),C=(0,c.Bk)(),[P,A]=x(b),j=e=>{let{__scopePopover:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:l=!1}=e,d=C(t),u=n.useRef(null),[h,p]=n.useState(!1),[f=!1,m]=(0,v.i)({prop:o,defaultProp:a,onChange:i});return(0,w.jsx)(c.bL,{...d,children:(0,w.jsx)(P,{scope:t,contentId:(0,s.B)(),triggerRef:u,open:f,onOpenChange:m,onOpenToggle:n.useCallback(()=>m(e=>!e),[m]),hasCustomAnchor:h,onCustomAnchorAdd:n.useCallback(()=>p(!0),[]),onCustomAnchorRemove:n.useCallback(()=>p(!1),[]),modal:l,children:r})})};j.displayName=b;var W="PopoverAnchor";n.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,a=A(W,r),i=C(r),{onCustomAnchorAdd:l,onCustomAnchorRemove:d}=a;return n.useEffect(()=>(l(),()=>d()),[l,d]),(0,w.jsx)(c.Mz,{...i,...o,ref:t})}).displayName=W;var M="PopoverTrigger",R=n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,i=A(M,r),l=C(r),d=(0,a.s)(t,i.triggerRef),u=(0,w.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":L(i.open),...n,ref:d,onClick:(0,o.m)(e.onClick,i.onOpenToggle)});return i.hasCustomAnchor?u:(0,w.jsx)(c.Mz,{asChild:!0,...l,children:u})});R.displayName=M;var E="PopoverPortal",[N,O]=x(E,{forceMount:void 0}),D=e=>{let{__scopePopover:t,forceMount:r,children:n,container:o}=e,a=A(E,t);return(0,w.jsx)(N,{scope:t,forceMount:r,children:(0,w.jsx)(p.C,{present:r||a.open,children:(0,w.jsx)(h.Z,{asChild:!0,container:o,children:n})})})};D.displayName=E;var F="PopoverContent",S=n.forwardRef((e,t)=>{let r=O(F,e.__scopePopover),{forceMount:n=r.forceMount,...o}=e,a=A(F,e.__scopePopover);return(0,w.jsx)(p.C,{present:n||a.open,children:a.modal?(0,w.jsx)(z,{...o,ref:t}):(0,w.jsx)(_,{...o,ref:t})})});S.displayName=F;var z=n.forwardRef((e,t)=>{let r=A(F,e.__scopePopover),i=n.useRef(null),l=(0,a.s)(t,i),d=n.useRef(!1);return n.useEffect(()=>{let e=i.current;if(e)return(0,g.Eq)(e)},[]),(0,w.jsx)(y.A,{as:m.DX,allowPinchZoom:!0,children:(0,w.jsx)(X,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),d.current||null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;d.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),_=n.forwardRef((e,t)=>{let r=A(F,e.__scopePopover),o=n.useRef(!1),a=n.useRef(!1);return(0,w.jsx)(X,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(i=r.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,i;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let l=t.target;(null===(i=r.triggerRef.current)||void 0===i?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),X=n.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:i,onEscapeKeyDown:s,onPointerDownOutside:h,onFocusOutside:p,onInteractOutside:f,...m}=e,v=A(F,r),g=C(r);return(0,d.Oh)(),(0,w.jsx)(u.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,w.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:f,onEscapeKeyDown:s,onPointerDownOutside:h,onFocusOutside:p,onDismiss:()=>v.onOpenChange(!1),children:(0,w.jsx)(c.UC,{"data-state":L(v.open),role:"dialog",id:v.contentId,...g,...m,ref:t,style:{...m.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),q="PopoverClose";function L(e){return e?"open":"closed"}n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,a=A(q,r);return(0,w.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})}).displayName=q,n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=C(r);return(0,w.jsx)(c.i3,{...o,...n,ref:t})}).displayName="PopoverArrow";var T=j,V=R,I=D,B=S},24122:(e,t,r)=>{r.d(t,{g:()=>h});let n={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}};var o=r(67356);let a={date:(0,o.k)({formats:{full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},defaultWidth:"full"}),time:(0,o.k)({formats:{full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},defaultWidth:"full"}),dateTime:(0,o.k)({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};var i=r(34548);function l(e,t,r){var n,o,a;let l="eeee p";return(n=e,o=t,a=r,+(0,i.k)(n,a)==+(0,i.k)(o,a))?l:e.getTime()>t.getTime()?"'下个'"+l:"'上个'"+l}let d={lastWeek:l,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:l,other:"PP p"};var u=r(58698);let s={ordinalNumber:(e,t)=>{let r=Number(e);switch(null==t?void 0:t.unit){case"date":return r.toString()+"日";case"hour":return r.toString()+"时";case"minute":return r.toString()+"分";case"second":return r.toString()+"秒";default:return"第 "+r.toString()}},era:(0,u.o)({values:{narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},defaultWidth:"wide"}),quarter:(0,u.o)({values:{narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,u.o)({values:{narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},defaultWidth:"wide"}),day:(0,u.o)({values:{narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},defaultWidth:"wide"}),dayPeriod:(0,u.o)({values:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultWidth:"wide",formattingValues:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultFormattingWidth:"wide"})};var c=r(44008);let h={code:"zh-CN",formatDistance:(e,t,r)=>{let o;let a=n[e];return(o="string"==typeof a?a:1===t?a.one:a.other.replace("{{count}}",String(t)),null==r?void 0:r.addSuffix)?r.comparison&&r.comparison>0?o+"内":o+"前":o},formatLong:a,formatRelative:(e,t,r,n)=>{let o=d[e];return"function"==typeof o?o(t,r,n):o},localize:s,match:{ordinalNumber:(0,r(40972).K)({matchPattern:/^(第\s*)?\d+(日|时|分|秒)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,c.A)({matchPatterns:{narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(前)/i,/^(公元)/i]},defaultParseWidth:"any"}),quarter:(0,c.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,c.A)({matchPatterns:{narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},defaultParseWidth:"any"}),day:(0,c.A)({matchPatterns:{narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},defaultParseWidth:"any"}),dayPeriod:(0,c.A)({matchPatterns:{any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}}},24357:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},29676:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},47863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},47924:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},66474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},69074:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},74466:(e,t,r)=>{r.d(t,{F:()=>i});var n=r(52596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,d=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let a=o(t)||o(n);return i[e][a]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,d,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...u}[t]):({...l,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},99708:(e,t,r)=>{r.d(t,{DX:()=>i,xV:()=>d});var n=r(12115),o=r(6101),a=r(95155),i=n.forwardRef((e,t)=>{let{children:r,...o}=e,i=n.Children.toArray(r),d=i.find(u);if(d){let e=d.props.children,r=i.map(t=>t!==d?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(l,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,a.jsx)(l,{...o,ref:t,children:r})});i.displayName="Slot";var l=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),i=function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...e)=>{a(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(i.ref=t?(0,o.t)(t,e):e),n.cloneElement(r,i)}return n.Children.count(r)>1?n.Children.only(null):null});l.displayName="SlotClone";var d=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function u(e){return n.isValidElement(e)&&e.type===d}}}]);