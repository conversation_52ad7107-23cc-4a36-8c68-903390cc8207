(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1244],{36799:(e,l,a)=>{"use strict";a.d(l,{default:()=>S});var t=a(95155),s=a(55733),r=a(9110),n=a(79181),d=a(73168),c=a(24122),o=a(50228),i=a(45968),u=a(55028);let h=(0,u.default)(()=>a.e(3554).then(a.bind(a,73554)),{loadableGenerated:{webpack:()=>[73554]}}),g=[{accessorKey:"name",header:"姓名",cell:e=>{let{row:l}=e;return(0,o.P)(l.getValue("name"))}},{accessorKey:"phone",header:"手机号码",cell:e=>{let{row:l}=e;return(0,o.P)(l.getValue("phone"))}},{accessorKey:"age",header:"年龄",cell:e=>{let{row:l}=e;return(0,o.P)((0,i.C)(l.getValue("age")))}},{accessorKey:"gender",header:"性别",cell:e=>{let{row:l}=e;return(0,o.P)((0,n.$)(l.getValue("gender")))}},{accessorKey:"source",header:"学员来源",cell:e=>{let{row:l}=e;return(0,o.P)(l.getValue("source"))}},{accessorKey:"sourceDesc",header:"来源描述",cell:e=>{let{row:l}=e;return(0,o.s)(l.getValue("sourceDesc"))}},{accessorKey:"createdAt",header:"加入时间",cell:e=>{let{row:l}=e;return(0,o.P)((0,d.GP)(l.getValue("createdAt"),"yyyy-MM-dd HH:mm:ss",{locale:c.g}))}},{accessorKey:"remark",header:"备注",cell:e=>{let{row:l}=e;return(0,o.s)(l.getValue("remark"))}},{id:"actions",header:"操作",cell:e=>{let{row:l}=e,a=l.original;return(0,t.jsx)("div",{className:"flex space-x-2",children:(0,t.jsx)(h,{studentId:a.id})})}}];var p=a(12115),m=a(62523),f=a(48432),x=a(47924),b=a(45964),y=a.n(b),w=a(95728);function v(){let[e,l]=(0,p.useState)(""),[a,s]=(0,p.useState)(1),[n,d]=(0,p.useState)(10),c=(0,p.useCallback)(y()(e=>{l(e.trim())},500),[]),o=(0,p.useCallback)(e=>{let l=e.target.value;e.target.value=l,c(l)},[c]),i=(0,p.useMemo)(()=>({page:a,pageSize:n,search:e,type:"public"}),[a,n,e]),{data:u,isLoading:h}=(0,w.FQ)(i);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-4",children:(0,t.jsx)("div",{className:"flex flex-col sm:flex-row sm:justify-between items-start sm:items-center gap-4",children:(0,t.jsxs)("div",{className:"w-full sm:w-56 relative",children:[(0,t.jsx)(m.p,{placeholder:"搜索姓名/手机号",className:"w-full pl-9",defaultValue:e,onChange:o,onKeyDown:e=>"Enter"===e.key&&c.flush()}),(0,t.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground absolute left-3 top-1/2 -translate-y-1/2"})]})})}),(0,t.jsx)(r.b,{columns:g,data:(null==u?void 0:u.list)||[],loading:h},"highSeasPoolDataTable"),(0,t.jsx)(f.default,{currentPage:(null==u?void 0:u.page)||1,pageSize:(null==u?void 0:u.pageSize)||10,totalItems:(null==u?void 0:u.total)||0,onPageChange:s,onPageSizeChange:d})]})}var j=a(91347),k=a(68056);let P=(0,u.default)(()=>a.e(9831).then(a.bind(a,79831)),{loadableGenerated:{webpack:()=>[79831]},ssr:!1}),N=(0,u.default)(()=>Promise.all([a.e(81),a.e(6234)]).then(a.bind(a,6234)),{loadableGenerated:{webpack:()=>[6234]},ssr:!1});function S(){var e;let{hasPermission:l}=(0,k.J)()||{hasPermission:()=>!1},a=[{id:"targetStudent",key:"targetStudent",label:"意向学员",content:(0,t.jsx)(j.LQ,{permission:"followup:target:read",children:(0,t.jsx)(P,{})}),hidden:!l("followup:target:read")},{id:"followUp-record",key:"followUp-record",label:"跟进记录",content:(0,t.jsx)(j.LQ,{permission:"followUp:target:follow:read",children:(0,t.jsx)(N,{})}),hidden:!l("followUp:target:follow:read")},{id:"3",key:"highseas",label:"公海池",content:(0,t.jsx)(j.LQ,{permission:"followup:highseas:read",children:(0,t.jsx)(v,{})}),hidden:!l("followup:highseas:read")}].filter(e=>!e.hidden);return 0===a.length?(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("p",{className:"text-gray-500",children:"您没有查看跟进管理的权限"})}):(0,t.jsx)("div",{className:"space-y-4 p-4",children:(0,t.jsx)(s.Q,{tabs:a,defaultTab:(null===(e=a[0])||void 0===e?void 0:e.key)||"targetStudent",variant:"underline"})})}},88387:(e,l,a)=>{Promise.resolve().then(a.bind(a,36799))}},e=>{var l=l=>e(e.s=l);e.O(0,[4277,8687,4201,8737,4540,4582,5620,3168,9613,7945,791,9624,9110,6639,3870,6315,7358],()=>l(88387)),_N_E=e.O()}]);