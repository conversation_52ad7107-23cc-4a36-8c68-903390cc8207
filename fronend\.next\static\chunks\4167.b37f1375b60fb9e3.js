"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4167],{24122:(e,t,a)=>{a.d(t,{g:()=>m});let r={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}};var n=a(67356);let i={date:(0,n.k)({formats:{full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},defaultWidth:"full"}),time:(0,n.k)({formats:{full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},defaultWidth:"full"}),dateTime:(0,n.k)({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};var o=a(34548);function l(e,t,a){var r,n,i;let l="eeee p";return(r=e,n=t,i=a,+(0,o.k)(r,i)==+(0,o.k)(n,i))?l:e.getTime()>t.getTime()?"'下个'"+l:"'上个'"+l}let s={lastWeek:l,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:l,other:"PP p"};var d=a(58698);let c={ordinalNumber:(e,t)=>{let a=Number(e);switch(null==t?void 0:t.unit){case"date":return a.toString()+"日";case"hour":return a.toString()+"时";case"minute":return a.toString()+"分";case"second":return a.toString()+"秒";default:return"第 "+a.toString()}},era:(0,d.o)({values:{narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},defaultWidth:"wide"}),quarter:(0,d.o)({values:{narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,d.o)({values:{narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},defaultWidth:"wide"}),day:(0,d.o)({values:{narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},defaultWidth:"wide"}),dayPeriod:(0,d.o)({values:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultWidth:"wide",formattingValues:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultFormattingWidth:"wide"})};var u=a(44008);let m={code:"zh-CN",formatDistance:(e,t,a)=>{let n;let i=r[e];return(n="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",String(t)),null==a?void 0:a.addSuffix)?a.comparison&&a.comparison>0?n+"内":n+"前":n},formatLong:i,formatRelative:(e,t,a,r)=>{let n=s[e];return"function"==typeof n?n(t,a,r):n},localize:c,match:{ordinalNumber:(0,a(40972).K)({matchPattern:/^(第\s*)?\d+(日|时|分|秒)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,u.A)({matchPatterns:{narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(前)/i,/^(公元)/i]},defaultParseWidth:"any"}),quarter:(0,u.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,u.A)({matchPatterns:{narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},defaultParseWidth:"any"}),day:(0,u.A)({matchPatterns:{narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},defaultParseWidth:"any"}),dayPeriod:(0,u.A)({matchPatterns:{any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}}},64365:(e,t,a)=>{a.d(t,{v:()=>r});let r=e=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY",minimumFractionDigits:2}).format(e)},74167:(e,t,a)=>{a.r(t),a.d(t,{default:()=>v});var r=a(95155);a(12115);var n=a(9110),i=a(26126),o=a(44861),l=a(73168),s=a(24122),d=a(57141),c=a(59434),u=a(64365);let m=(0,a(55028).default)(()=>a.e(3124).then(a.bind(a,63124)),{loadableGenerated:{webpack:()=>[63124]},ssr:!1}),h=e=>{if(!e)return(0,r.jsx)("span",{className:"text-slate-400 text-sm italic",children:"-"});try{let t=new Date(e);if(!(0,o.f)(t))throw Error("无效日期");return(0,r.jsx)("div",{children:(0,l.GP)(t,"yyyy-MM-dd",{locale:s.g})})}catch(e){return(0,r.jsx)(r.Fragment,{children:"-"})}},g=[{accessorKey:"name",header:"商品名称",cell:e=>{var t;let{row:a}=e,n=(null===(t=a.original)||void 0===t?void 0:t.productName)||"未命名商品";return(0,r.jsx)("div",{className:"font-medium text-slate-800",children:n})}},{accessorKey:"packageType",header:"套餐类型",cell:e=>{let{row:t}=e,a=t.original.productPackageType,{color:n,label:o}=d.lc[a]||d.lc.default;return(0,r.jsx)(i.E,{className:n,children:o})}},{accessorKey:"amount",header:"商品金额",cell:e=>{let{row:t}=e,a=Number(t.getValue("amount")||0);return(0,r.jsx)("div",{className:"font-medium text-slate-800",children:(0,u.v)(a)})}},{accessorKey:"amountPaid",header:"实收金额",cell:e=>{let{row:t}=e,a=Number(t.getValue("amountPaid")||0);return(0,r.jsx)("div",{className:"font-medium text-slate-800",children:(0,u.v)(a)})}},{accessorKey:"amountUnpaid",header:"欠款金额",cell:e=>{let{row:t}=e,a=Number(t.getValue("amountUnpaid")||0);return(0,r.jsx)("div",{className:"font-medium text-slate-800",children:(0,u.v)(a)})}},{accessorKey:"paymentMethod",header:"支付方式",cell:e=>{let{row:t}=e,a=String(t.getValue("paymentMethod")||"other").toLowerCase(),{label:n,color:o}=d.C9[a]||d.C9.other;return(0,r.jsx)(i.E,{className:(0,c.cn)("font-normal border py-0.5",o),children:n})}},{accessorKey:"purchaseQuantity",header:"商品数量",cell:e=>{let{row:t}=e;return(0,r.jsx)("div",{className:"font-medium text-slate-800",children:t.original.purchaseQuantity||0})}},{accessorKey:"discount",header:"折扣",cell:e=>{let{row:t}=e,a=Number(t.getValue("discount")||0);return(0,r.jsx)("div",{className:"text-slate-700",children:10===a||0===a?"无折扣":"".concat(a," 折")})}},{accessorKey:"paymentTime",header:"支付时间",cell:e=>{let{row:t}=e;return h(t.getValue("paymentTime"))}},{accessorKey:"totalSessionCount",header:"购买次数/总",cell:e=>{let{row:t}=e,a=Number(t.original.studentProductTotalSessionCount||0);return(0,r.jsx)("div",{className:"text-center font-medium",children:a>0?a:"不限"})}},{accessorKey:"giftCount",header:"赠送次数",cell:e=>{let{row:t}=e;return(0,r.jsx)("div",{className:"font-medium text-slate-800",children:t.original.giftCount||0})}},{accessorKey:"giftDays",header:"赠送天数",cell:e=>{let{row:t}=e;return(0,r.jsx)("div",{className:"font-medium text-slate-800",children:t.original.giftDays||0})}},{accessorKey:"salesRepName",header:"销售员",cell:e=>{let{row:t}=e,a=t.original.salesRepName||"未指定";return(0,r.jsx)("div",{className:"text-slate-700",children:a})}},{accessorKey:"operatorName",header:"操作员",cell:e=>{let{row:t}=e,a=t.original.operatorName||"未指定";return(0,r.jsx)("div",{className:"text-slate-700",children:a})}},{accessorKey:"paymentStatus",header:"状态",cell:e=>{let{row:t}=e,a=String(t.original.studentProductPaymentStatus||"other").toLowerCase(),{label:n,color:o}=d.I2[a]||d.I2.default;return(0,r.jsx)(i.E,{className:(0,c.cn)("font-normal border py-0.5",o),children:n})}},{accessorKey:"actions",header:"操作",cell:e=>{let{row:t}=e,a=t.original,n=a.studentProductPaymentStatus;return console.log(n,"1111",a),(0,r.jsx)("div",{className:"flex items-center gap-1",children:"refunded"!==n&&n?(0,r.jsx)(m,{studentProductId:a.studentProductId}):(0,r.jsx)(i.E,{className:"border py-0.5",children:"已退款"})})}}];var f=a(65436),y=a(95728);let v=function(){let{studentId:e}=(0,f.G)(e=>e.currentStudent),{data:t,isLoading:a}=(0,y.qc)(e);return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(n.b,{columns:g,data:t||[],loading:a},"purchaseRecordDataTable")})}}}]);