import fetch from 'node-fetch';

/**
 * 生成文本
 * @param {string} prompt - 提示词
 * @param {number} maxTokens - 最大生成长度
 * @param {number} temperature - 随机性参数
 * @param {string} model - 使用的模型
 * @returns {Promise<Object>} 生成结果
 */
export async function generateText(prompt, maxTokens = 1000, temperature = 0.7, model = 'qianfan-chinese') {
    try {
        const startTime = Date.now();
        
        // 根据选择的模型确定API端点和参数
        let apiEndpoint = 'https://qianfan.baidubce.com/v1/chat/completions';
        let apiModel = 'ERNIE-Bot-4';
        
        if (model === 'qianfan-english') {
            apiModel = 'ERNIE-Bot-8k';
        } else if (model === 'qianfan-code') {
            apiModel = 'ERNIE-Code';
        }
        
        const response = await fetch(apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.BAIDU_API_KEY}`
            },
            body: JSON.stringify({
                model: apiModel,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: maxTokens,
                temperature: temperature
            })
        });
        
        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (!data.choices || data.choices.length === 0) {
            throw new Error('未返回有效文本内容');
        }
        
        // 计算生成时间
        const generateTime = Date.now() - startTime;
        console.log(`文本生成完成，用时 ${generateTime} 毫秒`);
        
        return {
            id: data.id,
            created: data.created,
            text: data.choices[0].message.content,
            model: data.model,
            usage: data.usage
        };
    } catch (error) {
        throw new Error(`文本生成失败: ${error.message}`);
    }
}

/**
 * 生成代码
 * @param {string} prompt - 提示词
 * @param {number} maxTokens - 最大生成长度
 * @param {number} temperature - 随机性参数
 * @returns {Promise<Object>} 生成结果
 */
export async function generateCode(prompt, maxTokens = 2000, temperature = 0.3) {
    try {
        return await generateText(prompt, maxTokens, temperature, 'qianfan-code');
    } catch (error) {
        throw new Error(`代码生成失败: ${error.message}`);
    }
}

/**
 * 文本摘要生成
 * @param {string} text - 要摘要的文本
 * @param {number} maxLength - 摘要最大长度
 * @param {string} language - 摘要语言
 * @returns {Promise<Object>} 生成结果
 */
export async function generateSummary(text, maxLength = 200, language = 'zh') {
    try {
        const langPrompt = language === 'zh' ? '中文' : 'English';
        const prompt = `请对以下文本生成一个简洁的${langPrompt}摘要，不超过${maxLength}字：\n\n${text}`;
        
        return await generateText(prompt, maxLength, 0.5, language === 'zh' ? 'qianfan-chinese' : 'qianfan-english');
    } catch (error) {
        throw new Error(`摘要生成失败: ${error.message}`);
    }
}

/**
 * 文本翻译
 * @param {string} text - 要翻译的文本
 * @param {string} targetLanguage - 目标语言
 * @returns {Promise<Object>} 翻译结果
 */
export async function translateText(text, targetLanguage = 'zh') {
    try {
        const langMap = {
            'zh': '中文',
            'en': '英文',
            'ja': '日文',
            'ko': '韩文',
            'fr': '法文',
            'de': '德文',
            'es': '西班牙文',
            'ru': '俄文'
        };
        
        const targetLang = langMap[targetLanguage] || '中文';
        const prompt = `请将以下文本翻译成${targetLang}：\n\n${text}`;
        
        return await generateText(prompt, text.length * 2, 0.3, 'qianfan-chinese');
    } catch (error) {
        throw new Error(`文本翻译失败: ${error.message}`);
    }
}
