"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8161],{18161:(e,l,t)=>{t.r(l),t.d(l,{default:()=>N});var a=t(95155),r=t(12115),o=t(47924),s=t(1482),b=t(9110),d=t(26126),g=t(57141),n=t(61809),c=t(50228);let u=[{accessorKey:"studentName",header:"学员名称",cell:e=>{let{row:l}=e;return(0,c.P)(l.original.studentName)}},{accessorKey:"className",header:"班级名称",cell:e=>{let{row:l}=e;return(0,c.P)(l.original.className)}},{accessorKey:"startDate",header:"上课时间",cell:e=>{let{row:l}=e,{startDate:t,startTime:a,endTime:r}=l.original;return(0,c.P)("".concat((0,n.Y)(t,"yyyy-MM-dd (EEE)")," ").concat(a," - ").concat(r))}},{accessorKey:"courseName",header:"课程名称",cell:e=>{let{row:l}=e;return(0,c.P)(l.original.courseName)}},{accessorKey:"subject",header:"章节主题",cell:e=>{let{row:l}=e;return(0,c.s)(l.original.subject,4)}},{accessorKey:"teacherName",header:"授课老师",cell:e=>{let{row:l}=e;return(0,c.P)(l.original.teacherName)}},{accessorKey:"attended",header:"考勤状态",cell:e=>{let{row:l}=e,t=l.original.status,{className:r,text:o}=g.oD[t]||g.oD.default;return(0,a.jsx)(d.E,{className:r,children:o})}}];var i=t(48432),x=t(62523),h=t(58012),m=t(82007),v=t(59409),p=t(66695),f=t(95728),y=t(45964),w=t.n(y);let N=function(){let[e,l]=(0,r.useState)("today"),[t,d]=(0,r.useState)(void 0),[n,c]=(0,r.useState)("all"),[y,N]=(0,r.useState)(1),[j,C]=(0,r.useState)(10),[k,D]=(0,r.useState)(""),S=(0,r.useCallback)(w()(e=>{D(e),N(1)},500),[]),P=(0,r.useMemo)(()=>{let{startTime:l,endTime:a}=(0,m.A)(e,t);return{page:y,pageSize:j,search:k,status:n,startDate:l,endDate:a}},[y,j,k,n,e,t]),{data:E,isLoading:z}=(0,f.Er)(P);return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(p.Zp,{className:"border-slate-200",children:(0,a.jsx)(p.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-end md:justify-between",children:[(0,a.jsx)("div",{className:"w-full md:w-auto md:flex-1 md:max-w-md",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,a.jsx)(x.p,{type:"text",placeholder:"搜索学员姓名",onChange:e=>S(e.target.value),className:"pl-10 bg-slate-50 border-slate-200 focus:bg-white transition-colors"})]})}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s.A,{className:"h-4 w-4 text-slate-500"}),(0,a.jsx)("span",{className:"text-sm text-slate-500 hidden sm:inline",children:"筛选："})]}),(0,a.jsxs)(v.l6,{onValueChange:c,defaultValue:n,children:[(0,a.jsx)(v.bq,{className:"h-9 w-[120px] bg-white",children:(0,a.jsx)(v.yv,{placeholder:"考勤状态"})}),(0,a.jsx)(v.gC,{children:Object.entries(g.oD).map(e=>{let[l,t]=e;return(0,a.jsx)(v.eb,{value:l,children:t.text},l)})})]}),(0,a.jsx)(h.A,{timeRange:e,onTimeRangeChange:l,dateRange:t,onDateRangeChange:d})]})]})})}),(0,a.jsx)(b.b,{columns:u,data:(null==E?void 0:E.list)||[],loading:z,pagination:!1}),(0,a.jsx)(i.default,{currentPage:(null==E?void 0:E.page)||1,pageSize:(null==E?void 0:E.pageSize)||10,totalItems:(null==E?void 0:E.total)||0,onPageChange:N,onPageSizeChange:C})]})}},57141:(e,l,t)=>{t.d(l,{C9:()=>r,DT:()=>b,I2:()=>s,IC:()=>u,N4:()=>n,fb:()=>i,lc:()=>a,oD:()=>d,u7:()=>c,uq:()=>o,x9:()=>g});let a={"limited-sessions":{label:"限次",color:"capitalize font-normal bg-blue-50 text-blue-700 hover:bg-blue-100 px-2.5 py-0.5 rounded-md"},"limited-time-and-count":{label:"时次限",color:"capitalize font-normal bg-violet-50 text-violet-700 hover:bg-violet-100 px-2.5 py-0.5 rounded-md"},default:{label:"未知套餐",color:"capitalize font-normal bg-slate-100 text-slate-700 hover:bg-slate-200 px-2.5 py-0.5 rounded-md"}},r={cash:{label:"现金",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},alipay:{label:"支付宝",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},wechat:{label:"微信",color:"bg-green-100 text-green-800 hover:bg-green-200"},card:{label:"银行卡",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},other:{label:"其他",color:"bg-slate-100 text-slate-800 hover:bg-slate-200"}},o=[{id:"wechat",label:"微信"},{id:"alipay",label:"支付宝"},{id:"card",label:"银行转账"},{id:"cash",label:"现金"},{id:"other",label:"其他"}],s={done:{label:"完成",color:"bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400"},arrears:{label:"欠费",color:"bg-amber-100 text-amber-800 hover:bg-amber-200 dark:bg-amber-900/30 dark:text-amber-400"},refunded:{label:"退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400"},default:{label:"未知",color:"bg-slate-100 text-slate-800 hover:bg-slate-200 dark:bg-slate-800/50 dark:text-slate-400"}},b={active:{badgeClass:"bg-emerald-50 text-emerald-700 hover:bg-emerald-100 border-emerald-200",dotClass:"bg-emerald-500",text:"正常"},expired:{badgeClass:"bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200",dotClass:"bg-amber-500",text:"已到期"},refunded:{badgeClass:"bg-red-50 text-red-700 hover:bg-red-100 border-red-200",dotClass:"bg-red-500",text:"已退款"},frozen:{badgeClass:"bg-slate-50 text-slate-700 hover:bg-slate-100 border-slate-200",dotClass:"bg-slate-500",text:"已冻结"},default:{badgeClass:"bg-slate-100 text-slate-700 hover:bg-slate-200 border-slate-200",dotClass:"bg-slate-400",text:"未知状态"}},d={all:{className:"bg-slate-100 text-slate-800 hover:bg-slate-200",text:"全部状态"},attendance:{className:"bg-green-100 text-green-800 hover:bg-green-200",text:"已考勤"},leave:{className:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200",text:"请假"},unattended:{className:"bg-orange-100 text-orange-800 hover:bg-orange-200",text:"未考勤"},default:{className:"bg-red-100 text-red-800 hover:bg-red-200",text:"缺勤"}},g={A:{label:"非常感兴趣",color:"bg-green-100 text-green-800 hover:bg-green-200"},B:{label:"比较感兴趣",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},C:{label:"一般意向",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},D:{label:"了解意向",color:"bg-orange-100 text-orange-800 hover:bg-orange-200"},E:{label:"不感兴趣",color:"bg-red-100 text-red-800 hover:bg-red-200"},default:{label:"未知",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},n={productSales:{label:"产品销售",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},courseSales:{label:"课程销售",color:"bg-green-100 text-green-800 hover:bg-green-200"},OverduePaymentCollection:{label:"逾期款项催收",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},lifestyleAndOffice:{label:"生活和办公",color:"bg-red-100 text-red-800 hover:bg-red-200"},socialAndCatering:{label:"社交和餐饮",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},refunded:{label:"退款",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"其他",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},c={completed:{label:"已完成",color:"bg-green-100 text-green-800 hover:bg-green-200"},pending:{label:"待审核",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},refunded:{label:"已退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},failed:{label:"审核未通过",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"未知状态",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},u={formal:{label:"正式学员",color:"text-blue-500"},graduated:{label:"历史学员",color:"text-green-500"},public:{label:"公海池",color:"text-red-500"},intent:{label:"意向学员",color:"text-yellow-500"}},i={secret:{label:"保密",color:"text-gray-500"},male:{label:"男",color:"text-blue-500"},female:{label:"女",color:"text-pink-500"}}},61809:(e,l,t)=>{t.d(l,{Y:()=>s});var a=t(44861),r=t(73168),o=t(24122);let s=function(e){let l,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"yyyy-MM-dd",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(null==e)return s;try{if("string"==typeof e)l=new Date(e);else if("number"==typeof e)l=new Date(e);else{if(!(e instanceof Date))return s;l=e}if(!(0,a.f)(l))return s;return(0,r.GP)(l,t,{locale:o.g})}catch(e){return console.error("Date formatting error:",e),s}}}}]);