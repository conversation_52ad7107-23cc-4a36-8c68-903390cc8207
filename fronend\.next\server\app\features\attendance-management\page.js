(()=>{var e={};e.id=570,e.ids=[570],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,r,t)=>{let{createProxy:s}=t(39844);e.exports=s("F:\\trae\\cardmees\\fronend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17720:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26373:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:x,...c},o)=>(0,s.createElement)("svg",{ref:o,...d,width:r,height:r,stroke:e,strokeWidth:a?24*Number(t)/Number(r):t,className:l("lucide",i),...c},[...x.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(n)?n:[n]])),n=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...d},n)=>(0,s.createElement)(i,{ref:n,iconNode:r,className:l(`lucide-${a(e)}`,t),...d}));return t.displayName=`${e}`,t}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40918:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(26373).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41382:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(26373).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},51465:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(26373).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},52568:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85814,23))},53148:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(26373).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69353:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>d.a,__next_app__:()=>o,pages:()=>c,routeModule:()=>m,tree:()=>x});var s=t(65239),a=t(48088),l=t(88170),d=t.n(l),i=t(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(r,n);let x={children:["",{children:["features",{children:["attendance-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,71290)),"F:\\trae\\cardmees\\fronend\\src\\app\\features\\attendance-management\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["F:\\trae\\cardmees\\fronend\\src\\app\\features\\attendance-management\\page.tsx"],o={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/features/attendance-management/page",pathname:"/features/attendance-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:x}})},71290:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var s=t(37413);t(61120);var a=t(4536),l=t.n(a),d=t(51465),i=t(53148),n=t(91142),x=t(72845);let c=(0,t(26373).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var o=t(41382),m=t(40918);function h(){return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,s.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(l(),{href:"/",className:"flex items-center space-x-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 transition-colors",children:[(0,s.jsx)(d.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{className:"font-medium",children:"返回首页"})]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"考勤管理系统"})]})})}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-yellow-600 py-16",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-6",children:(0,s.jsx)("div",{className:"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center",children:(0,s.jsx)(i.A,{className:"w-10 h-10 text-white"})})}),(0,s.jsx)("h1",{className:"text-4xl font-bold text-white mb-4",children:"考勤管理"}),(0,s.jsx)("p",{className:"text-xl text-orange-100 max-w-3xl mx-auto",children:"智能化考勤记录与统计分析，实时掌握学员出勤情况，提高管理效率"})]})})}),(0,s.jsx)("div",{className:"py-16 bg-white dark:bg-gray-800",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"核心功能特色"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"全方位的考勤管理解决方案"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-br from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 p-6 rounded-xl border border-orange-200 dark:border-orange-800",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(n.A,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"实时考勤记录"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"支持多种考勤方式，实时记录学员到课情况，自动生成考勤报表"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(x.A,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"智能数据分析"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"自动分析出勤率趋势，生成详细的统计报告和可视化图表"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 p-6 rounded-xl border border-red-200 dark:border-red-800",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(c,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"异常情况提醒"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"自动识别缺勤、迟到等异常情况，及时通知相关人员"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-xl border border-green-200 dark:border-green-800",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"多维度统计"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"按班级、课程、时间等多个维度进行考勤统计分析"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 p-6 rounded-xl border border-purple-200 dark:border-purple-800",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(m.A,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"灵活考勤规则"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"支持自定义考勤规则，适应不同课程和班级的管理需求"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 p-6 rounded-xl border border-teal-200 dark:border-teal-800",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-teal-500 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(x.A,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"报表导出"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"支持多种格式的考勤报表导出，便于存档和分析"})]})]})]})}),(0,s.jsx)("div",{className:"py-16 bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"适用场景"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"满足各种教育机构的考勤管理需求"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"培训机构"}),(0,s.jsxs)("ul",{className:"space-y-3 text-gray-600 dark:text-gray-300",children:[(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(n.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"课程考勤自动记录"})]}),(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(n.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"学员出勤率统计"})]}),(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(n.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"家长出勤情况通知"})]})]})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"学校教育"}),(0,s.jsxs)("ul",{className:"space-y-3 text-gray-600 dark:text-gray-300",children:[(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(n.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"班级考勤管理"})]}),(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(n.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"缺勤异常预警"})]}),(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(n.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"考勤数据分析"})]})]})]})]})]})}),(0,s.jsx)("div",{className:"py-16 bg-gradient-to-r from-orange-500 to-yellow-600",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"开始使用考勤管理系统"}),(0,s.jsx)("p",{className:"text-xl text-orange-100 mb-8",children:"让考勤管理变得更加简单高效，提升教育机构管理水平"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(l(),{href:"/dashboard",className:"inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-orange-600 bg-white hover:bg-gray-50 transition-colors duration-200",children:"立即体验"}),(0,s.jsx)(l(),{href:"/features",className:"inline-flex items-center justify-center px-8 py-3 border border-white text-base font-medium rounded-md text-white hover:bg-white/10 transition-colors duration-200",children:"查看更多功能"})]})]})}),(0,s.jsx)("footer",{className:"bg-gray-800 dark:bg-gray-900",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"text-center text-gray-400",children:(0,s.jsx)("p",{children:"\xa9 2024 CardMees 教育管理平台. 保留所有权利。"})})})})]})}},72845:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(26373).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91142:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(26373).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7392,5814,3019],()=>t(69353));module.exports=s})();