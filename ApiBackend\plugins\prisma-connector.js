// src/plugins/prisma-connector.js
import fp from "fastify-plugin";
import { PrismaClient } from "@prisma/client";

async function prismaConnector(fastify, options) {
    const prisma = new PrismaClient({
        log: ['query', 'info', 'warn', 'error'],
    });

    try {
        // 确保连接成功
        await prisma.$connect();
        fastify.log.info('Prisma Client connected successfully');

        // 将 Prisma 客户端挂载到 Fastify 实例
        fastify.decorate("prisma", prisma);

        // 关闭时断开 Prisma 连接
        fastify.addHook("onClose", async (instance) => {
            try {
                await instance.prisma.$disconnect();
                fastify.log.info('Prisma Client disconnected successfully');
            } catch (err) {
                fastify.log.error('Error disconnecting Prisma Client:', err);
            }
        });
    } catch (err) {
        fastify.log.error('Error connecting Prisma Client:', err);
        throw err;
    }
}

export default fp(prismaConnector, {
    name: "prisma-connector",
});