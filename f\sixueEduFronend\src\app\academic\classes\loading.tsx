'use client';

import { Skeleton } from '@/components/ui/skeleton';

export default function ClassesLoading() {
  return (
    <div className="space-y-4 p-4">
      {/* 标题和操作按钮区域 */}
      <div className="flex justify-between items-center mb-4">
        <Skeleton className="h-8 w-32" />
        <div className="flex space-x-2">
          <Skeleton className="h-10 w-28" />
          <Skeleton className="h-10 w-28" />
        </div>
      </div>

      {/* 搜索和过滤区域 */}
      <div className="flex flex-wrap gap-3 mb-4">
        <Skeleton className="h-10 w-48" />
        <Skeleton className="h-10 w-48" />
        <Skeleton className="h-10 w-24" />
      </div>

      {/* 班级表格骨架屏 */}
      <div className="border rounded-md">
        {/* 表头 */}
        <div className="flex border-b p-2 bg-muted/30">
          {Array(6)
            .fill(0)
            .map((_, index) => (
              <Skeleton key={index} className="h-6 flex-1 mx-2" />
            ))}
        </div>

        {/* 表格内容 */}
        {Array(8)
          .fill(0)
          .map((_, rowIndex) => (
            <div key={rowIndex} className="flex border-b p-3">
              {Array(6)
                .fill(0)
                .map((_, colIndex) => (
                  <Skeleton key={colIndex} className="h-5 flex-1 mx-2" />
                ))}
            </div>
          ))}
      </div>

      {/* 分页骨架屏 */}
      <div className="flex justify-between items-center mt-4">
        <Skeleton className="h-8 w-40" />
        <Skeleton className="h-8 w-64" />
      </div>
    </div>
  );
}
