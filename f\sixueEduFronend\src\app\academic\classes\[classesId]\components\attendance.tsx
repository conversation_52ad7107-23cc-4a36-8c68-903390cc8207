"use client"

import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { ATTENDANCE_STATUS_MAP } from "@/constants"

interface Period {
  id: number
  title: string
  date: string
  time: string
}

export default function Attendance({ attendanceData }: { attendanceData: any }) {
  if (!attendanceData) {
    return <div>Loading...</div>;
  }

  const { header, students } = attendanceData;

  const periods: Period[] = header;

  const statusCounts = {
    attendance: 0,
    unattended: 0,
    leave: 0,
    absent: 0
  };

  students.forEach((student: any) => {
    student.attendance.forEach((status: any) => {
      if (status in statusCounts) {
        statusCounts[status as keyof typeof statusCounts]++;
      }
    });
  });

  return (
    <div >
      <div>
        <div className="my-2 bg-white p-3 rounded-md shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-gray-700 font-medium">出勤总计：</div>
            <div className="flex flex-wrap gap-1">
              <Badge variant="outline" className="bg-blue-500 text-white px-2 py-1 text-sm rounded-md">
                已到课: {statusCounts.attendance}
              </Badge>
              <Badge variant="outline" className="bg-emerald-500 text-white px-2 py-1 text-sm rounded-md">
                未考勤: {statusCounts.unattended}
              </Badge>
              <Badge variant="outline" className="bg-yellow-500 text-white px-2 py-1 text-sm rounded-md">
                请假: {statusCounts.leave}
              </Badge>
              <Badge variant="outline" className="bg-red-500 text-white px-2 py-1 text-sm rounded-md">
                缺勤: {statusCounts.absent}
              </Badge>
            </div>
          </div>
        </div>

        <div className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableBody>
                {/* 周期信息行 */}
                <TableRow className="bg-gray-50 dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 border-b">
                  <TableCell className="font-medium text-center border-r min-w-[100px]">
                    <span className="text-gray-700 dark:text-gray-300">学员姓名</span>
                  </TableCell>
                  {periods.map((period) => (
                    <TableCell key={period.id} className="text-center border-r min-w-[140px] p-3">
                      <div className="text-blue-600 dark:text-blue-400 font-medium">{period.title}</div>
                      <div className="text-blue-600 dark:text-blue-400 font-medium">
                        {period.date}
                      </div>
                      <div className="text-gray-500 dark:text-gray-400 text-sm">
                        {period.time}
                      </div>
                    </TableCell>
                  ))}
                </TableRow>

                {/* 学员行 */}
                {students.map((student: any, studentIndex: any) => (
                  <TableRow
                    key={student.id}
                    className={cn(
                      "hover:bg-blue-50 dark:hover:bg-blue-950/30 transition-colors",
                      studentIndex % 2 === 0 ? "bg-white dark:bg-gray-950" : "bg-gray-50 dark:bg-gray-900/50"
                    )}
                  >
                    <TableCell className="font-medium text-center border-r">
                      <span className="text-gray-800 dark:text-gray-200">{student.name}</span>
                    </TableCell>
                    {/* 学员出勤状态 */}
                    {Array.from({ length: periods.length }).map((_, index) => {
                      const status = student.attendance[index];
                      const statusKey = status as keyof typeof ATTENDANCE_STATUS_MAP;
                      const statusInfo = ATTENDANCE_STATUS_MAP[statusKey] || ATTENDANCE_STATUS_MAP.default;
                      
                      return (
                        <TableCell key={index} className="text-center p-2 border-r">
                          {status ? (
                            <div className={`${statusInfo.className}  py-1.5 px-4 rounded-md inline-block transition-colors shadow-sm`}>
                              {statusInfo.text}
                            </div>
                          ) : (
                            <div className="text-xl text-gray-400 dark:text-gray-500 font-light">×</div>
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  )
}
