import * as staffService from '../services/staffService.js';

/**
 * 上传员工头像
 * @param {Object} request - Fastify请求对象
 * @param {Object} reply - Fastify响应对象
 */
export async function uploadStaffAvatar(request, reply) {
  try {
    const data = await request.file();
    if (!data) {
      return reply.status(400).send({
        code: 400,
        message: '未找到上传的文件',
      });
    }

    const buffer = await data.toBuffer();
    const result = await staffService.uploadStaffAvatar(buffer);

    return reply.success({
      message: '上传成功',
      data: result
    });
  } catch (error) {
    console.error('文件上传错误:', error);
    return reply.status(500).send({
      code: 500,
      message: '文件上传失败',
      error: error.message
    });
  }
}

/**
 * 重置员工密码
 * @param {Object} request - Fastify请求对象
 * @param {Object} reply - Fastify响应对象
 */
export async function resetStaffPassword(request, reply) {
  const user = request.user;
  const { staffId } = request.params;
  const client = await request.server.pg.connect();
  
  try {
    await staffService.resetStaffPassword({
      staffId,
      institutionId: user.institutionId,
      client
    });

    reply.success({
      message: '重置密码成功'
    });
  } catch (error) {
    throw error;
  } finally {
    client.release();
  }
}

/**
 * 更新员工状态
 * @param {Object} request - Fastify请求对象
 * @param {Object} reply - Fastify响应对象
 */
export async function updateStaffStatus(request, reply) {
  const { staffId } = request.params;
  const user = request.user;
  const client = await request.server.pg.connect();
  
  try {
    await staffService.updateStaffStatus({
      staffId,
      institutionId: user.institutionId,
      client
    });

    reply.success({
      message: '更新员工状态成功'
    });
  } catch (error) {
    throw error;
  } finally {
    client.release();
  }
}

/**
 * 更新员工信息
 * @param {Object} request - Fastify请求对象
 * @param {Object} reply - Fastify响应对象
 */
export async function updateStaff(request, reply) {
  const { staffId } = request.params;
  const { name, account, roles, avatar, isShow } = request.body;
  const user = request.user;
  const client = await request.server.pg.connect();
  
  try {
    await staffService.updateStaff({
      staffId,
      institutionId: user.institutionId,
      client,
      updateData: { name, account, roles, avatar, isShow }
    });

    reply.success({
      message: '更新员工信息成功'
    });
  } catch (error) {
    throw error;
  } finally {
    client.release();
  }
}

/**
 * 删除员工
 * @param {Object} request - Fastify请求对象
 * @param {Object} reply - Fastify响应对象
 */
export async function deleteStaff(request, reply) {
  const { staffId } = request.params;
  const user = request.user;
  const client = await request.server.pg.connect();
  
  try {
    await staffService.deleteStaff({
      staffId,
      institutionId: user.institutionId,
      client
    });

    reply.success({
      message: '删除员工成功'
    });
  } catch (error) {
    throw error;
  } finally {
    client.release();
  }
}

/**
 * 获取员工信息
 * @param {Object} request - Fastify请求对象
 * @param {Object} reply - Fastify响应对象
 */
export async function getStaffInfo(request, reply) {
  const { staffId } = request.params;
  const user = request.user;
  const client = await request.server.pg.connect();
  
  try {
    const staffInfo = await staffService.getStaffInfo({
      staffId,
      institutionId: user.institutionId,
      client
    });

    reply.success({
      data: staffInfo
    });
  } catch (error) {
    throw error;
  } finally {
    client.release();
  }
}

/**
 * 获取员工列表
 * @param {Object} request - Fastify请求对象
 * @param {Object} reply - Fastify响应对象
 */
export async function getStaffList(request, reply) {
  const user = request.user;
  const { page, pageSize, search } = request.query;
  const client = await request.server.pg.connect();
  
  try {
    const result = await staffService.getStaffList({
      institutionId: user.institutionId,
      client,
      query: { page, pageSize, search }
    });

    reply.success({
      data: result
    });
  } catch (error) {
    throw error;
  } finally {
    client.release();
  }
}

/**
 * 创建员工
 * @param {Object} request - Fastify请求对象
 * @param {Object} reply - Fastify响应对象
 */
export async function createStaff(request, reply) {
  const { name, account, roleId, phone, avatar, description, password } = request.body;
  const user = request.user;
  const client = await request.server.pg.connect();
  
  try {
    await staffService.createStaff({
      institutionId: user.institutionId,
      client,
      userData: { name, account, roleId, phone, avatar, description, password }
    });

    reply.success({
      message: '创建员工成功'
    });
  } catch (error) {
    throw error;
  } finally {
    client.release();
  }
} 

export default {
  uploadStaffAvatar,
  resetStaffPassword,
  updateStaffStatus,
  updateStaff,
  deleteStaff,
  getStaffInfo,
  getStaffList,
  createStaff
}
