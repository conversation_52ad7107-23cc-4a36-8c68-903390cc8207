/**
 * This script helps identify files and folders that need to be renamed
 * to follow Google TypeScript style guidelines.
 * 
 * It scans the project and generates a report of files that don't follow
 * the naming conventions, along with suggested new names.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

// Directories to exclude from scanning
const EXCLUDED_DIRS = [
  'node_modules',
  '.next',
  'dist',
  'build',
  '.git',
  'scripts'
];

// File extensions to check
const INCLUDED_EXTENSIONS = [
  '.ts',
  '.tsx',
  '.js',
  '.jsx'
];

// Convert camelCase or PascalCase to kebab-case
function toKebabCase(str) {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
}

// Check if a file name follows kebab-case convention
function isKebabCase(str) {
  return /^[a-z0-9]+(-[a-z0-9]+)*$/.test(str);
}

// Check if a file should be renamed
function shouldRenameFile(fileName) {
  const baseName = path.basename(fileName, path.extname(fileName));
  return !isKebabCase(baseName);
}

// Get suggested new name for a file
function getSuggestedFileName(fileName) {
  const ext = path.extname(fileName);
  const baseName = path.basename(fileName, ext);
  return `${toKebabCase(baseName)}${ext}`;
}

// Scan directory recursively
async function scanDirectory(dir, results = { files: [], folders: [] }) {
  const entries = await readdir(dir);
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry);
    const stats = await stat(fullPath);
    
    if (stats.isDirectory()) {
      if (EXCLUDED_DIRS.includes(entry)) {
        continue;
      }
      
      // Check folder name
      if (!isKebabCase(entry)) {
        results.folders.push({
          path: fullPath,
          currentName: entry,
          suggestedName: toKebabCase(entry)
        });
      }
      
      // Recursively scan subdirectories
      await scanDirectory(fullPath, results);
    } else if (stats.isFile()) {
      const ext = path.extname(entry);
      
      if (INCLUDED_EXTENSIONS.includes(ext) && shouldRenameFile(entry)) {
        results.files.push({
          path: fullPath,
          currentName: entry,
          suggestedName: getSuggestedFileName(entry)
        });
      }
    }
  }
  
  return results;
}

// Generate rename commands
function generateRenameCommands(results) {
  const commands = [];
  
  // Generate folder rename commands (bottom-up to avoid path issues)
  results.folders
    .sort((a, b) => b.path.length - a.path.length)
    .forEach(folder => {
      const dir = path.dirname(folder.path);
      const newPath = path.join(dir, folder.suggestedName);
      commands.push(`rename-folder "${folder.path}" "${newPath}"`);
    });
  
  // Generate file rename commands
  results.files.forEach(file => {
    const dir = path.dirname(file.path);
    const newPath = path.join(dir, file.suggestedName);
    commands.push(`rename-file "${file.path}" "${newPath}"`);
  });
  
  return commands;
}

// Main function
async function main() {
  try {
    console.log('Scanning project for files and folders to rename...');
    const srcDir = path.join(process.cwd(), 'src');
    const results = await scanDirectory(srcDir);
    
    console.log('\nFiles to rename:');
    if (results.files.length === 0) {
      console.log('  None found');
    } else {
      results.files.forEach(file => {
        console.log(`  ${file.path} -> ${file.suggestedName}`);
      });
    }
    
    console.log('\nFolders to rename:');
    if (results.folders.length === 0) {
      console.log('  None found');
    } else {
      results.folders.forEach(folder => {
        console.log(`  ${folder.path} -> ${folder.suggestedName}`);
      });
    }
    
    console.log('\nTotal items to rename:', results.files.length + results.folders.length);
    
    if (results.files.length > 0 || results.folders.length > 0) {
      console.log('\nRename commands:');
      const commands = generateRenameCommands(results);
      commands.forEach(cmd => console.log(`  ${cmd}`));
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
