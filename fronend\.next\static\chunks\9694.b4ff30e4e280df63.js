"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9694],{24122:(e,t,a)=>{a.d(t,{g:()=>u});let n={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}};var r=a(67356);let i={date:(0,r.k)({formats:{full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},defaultWidth:"full"}),time:(0,r.k)({formats:{full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},defaultWidth:"full"}),dateTime:(0,r.k)({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};var o=a(34548);function d(e,t,a){var n,r,i;let d="eeee p";return(n=e,r=t,i=a,+(0,o.k)(n,i)==+(0,o.k)(r,i))?d:e.getTime()>t.getTime()?"'下个'"+d:"'上个'"+d}let s={lastWeek:d,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:d,other:"PP p"};var l=a(58698);let c={ordinalNumber:(e,t)=>{let a=Number(e);switch(null==t?void 0:t.unit){case"date":return a.toString()+"日";case"hour":return a.toString()+"时";case"minute":return a.toString()+"分";case"second":return a.toString()+"秒";default:return"第 "+a.toString()}},era:(0,l.o)({values:{narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},defaultWidth:"wide"}),quarter:(0,l.o)({values:{narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,l.o)({values:{narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},defaultWidth:"wide"}),day:(0,l.o)({values:{narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},defaultWidth:"wide"}),dayPeriod:(0,l.o)({values:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultWidth:"wide",formattingValues:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultFormattingWidth:"wide"})};var m=a(44008);let u={code:"zh-CN",formatDistance:(e,t,a)=>{let r;let i=n[e];return(r="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",String(t)),null==a?void 0:a.addSuffix)?a.comparison&&a.comparison>0?r+"内":r+"前":r},formatLong:i,formatRelative:(e,t,a,n)=>{let r=s[e];return"function"==typeof r?r(t,a,n):r},localize:c,match:{ordinalNumber:(0,a(40972).K)({matchPattern:/^(第\s*)?\d+(日|时|分|秒)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,m.A)({matchPatterns:{narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(前)/i,/^(公元)/i]},defaultParseWidth:"any"}),quarter:(0,m.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,m.A)({matchPatterns:{narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},defaultParseWidth:"any"}),day:(0,m.A)({matchPatterns:{narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},defaultParseWidth:"any"}),dayPeriod:(0,m.A)({matchPatterns:{any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}}},66695:(e,t,a)=>{a.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>s,Zp:()=>o,aR:()=>d,wL:()=>m});var n=a(95155),r=a(12115),i=a(59434);let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});o.displayName="Card";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",a),...r})});d.displayName="CardHeader";let s=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});s.displayName="CardTitle";let l=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...r})});l.displayName="CardDescription";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",a),...r})});c.displayName="CardContent";let m=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",a),...r})});m.displayName="CardFooter"},79694:(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var n=a(95155),r=a(59434),i=a(66695),o=a(26126);let d={default:"bg-primary",success:"bg-green-500",info:"bg-blue-500",warning:"bg-yellow-500",danger:"bg-red-500"};function s(e){let{date:t,title:a,description:s,icon:l,variant:c="default",isLast:m=!1,username:u}=e;return(0,n.jsxs)("div",{className:"relative flex gap-4 pb-8 last:pb-0",children:[(0,n.jsxs)("div",{className:"flex flex-col items-center",children:[(0,n.jsx)("div",{className:(0,r.cn)("z-10 flex h-8 w-8 items-center justify-center rounded-full border-4 border-background",d[c]),children:l?(0,n.jsx)("span",{className:"text-white",children:l}):(0,n.jsx)("span",{className:"h-2 w-2 rounded-full bg-white"})}),!m&&(0,n.jsx)("div",{className:"h-full w-0.5 bg-border"})]}),(0,n.jsxs)("div",{className:"flex-1 pt-1",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,n.jsx)(o.E,{variant:"outline",className:"font-normal",children:t}),u&&(0,n.jsx)(o.E,{variant:"secondary",className:"font-normal",children:u})]}),(0,n.jsxs)(i.Zp,{className:"p-4",children:[(0,n.jsx)("h3",{className:"font-medium",children:a}),s&&(0,n.jsx)("p",{className:"mt-1 text-sm text-muted-foreground",children:s})]})]})]})}function l(e){let{items:t,className:a}=e;return(0,n.jsx)("div",{className:(0,r.cn)("space-y-4",a),children:t.map((e,a)=>(0,n.jsx)(s,{...e,isLast:a===t.length-1},a))})}var c=a(12115),m=a(73168),u=a(24122),h=a(65436),f=a(95728);let g=function(){let{studentId:e}=(0,h.G)(e=>e.currentStudent),{data:t,isLoading:a,error:r}=(0,f.RC)(e,{skip:!e}),i=c.useMemo(()=>t?t.map(e=>{let t=e.nextFollowUpDate?(0,m.GP)(new Date(e.nextFollowUpDate),"yyyy-MM-dd",{locale:u.g}):null,a=(0,m.GP)(new Date(e.followUpDate),"yyyy-MM-dd",{locale:u.g}),n=e.followUpUserName;return{date:a,title:"".concat(n," 跟进记录 ").concat(t?"下次跟进日期: (".concat(t,")"):""),description:e.followUpContent,variant:t?"info":"default",username:n}}):[],[t]);return a?(0,n.jsx)("div",{className:"text-center py-4",children:"加载跟进记录中..."}):r?(0,n.jsx)("div",{className:"text-center py-4 text-red-500",children:"获取跟进记录失败，请稍后重试"}):i.length?(0,n.jsx)(l,{items:i}):(0,n.jsx)("div",{className:"text-center py-4",children:"暂无跟进记录"})}}}]);