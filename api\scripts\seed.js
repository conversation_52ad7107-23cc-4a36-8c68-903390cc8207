import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
    // 0. 删除所有数据
    console.log('删除所有数据...');
    await prisma.rolePermission.deleteMany();
    await prisma.userRole.deleteMany();
    // await prisma.user.deleteMany();
    await prisma.userInstitution.deleteMany();
    await prisma.role.deleteMany();
    await prisma.institution.deleteMany();
    await prisma.user.deleteMany();    
    await prisma.menu.deleteMany();
    await prisma.permission.deleteMany();
    console.log('删除所有数据完成...');

    // 1. 创建基础权限
    console.log('创建权限...');
    // 1.1 创建教务中心权限
    console.log('创建教务中心权限...');
    await prisma.permission.createMany({
        data: [
            // 教务管理中心
            { code: 'academic:access', name: '访问教务中心' },
            { code: 'student:create', name: '创建学员' },
            { code: 'student:delete', name: '删除学员' },
            { code: 'student:update', name: '更新学员' },
            { code: 'student:read', name: '查看学员' },
            // 学员管理
            { code: 'student', name: '学员管理' },
            // 学员管理，子项
            { code: 'student:read:all', name: '查看所有学员' },
            { code: 'student:read:active', name: '查看在读学员' },
            { code: 'student:package:read', name: '查询学员套餐' },
            { code: 'student:attendance:read', name: '查询考勤记录' },
            { code: 'student:product:adjustment:read', name: '查询产品调整记录' },
            { code: 'student:read:graduated', name: '查询毕业学员' },
            { code: 'student:class:read', name: '上课查询' },

            // 班级管理
            { code: 'class:create', name: '创建班级' },
            { code: 'class:delete', name: '删除班级' },
            { code: 'class:update', name: '更新班级' },
            { code: 'class:read', name: '查看班级' },

            // 课表管理
            { code: 'schedule:create', name: '创建课表' },
            { code: 'schedule:delete', name: '删除课表' },
            { code: 'schedule:update', name: '更新课表' },
            { code: 'schedule:read', name: '查看课表' },
            // 课程管理
            { code: 'course:create', name: '创建课程' },
            { code: 'course:delete', name: '删除课程' },
            { code: 'course:update', name: '更新课程' },
            { code: 'course:read', name: '查看课程' },

            // 套餐管理
            { code: 'package:create', name: '创建套餐' },
            { code: 'package:delete', name: '删除套餐' },
            { code: 'package:update', name: '更新套餐' },
            { code: 'package:read', name: '查看套餐' },

        ]
    });

    // 1.2 创建招生中心权限
    console.log('创建招生中心权限...');
    await prisma.permission.createMany({
        data: [
            // 招生中心
            { code: 'enrollment:access', name: '访问招生中心' },
            // 跟进管理
            { code: 'followUp:read', name: '跟进管理' },
            // 跟进管理子项
            { code: 'lead:read', name: '意向学员' },
            { code: 'lead:follow:read', name: '跟进记录' },
            { code: 'lead:pool:read', name: '公海池' },
        ]
    });
    // 1.3 创建财务中心权限
    console.log('创建财务中心权限...');
    await prisma.permission.createMany({
        data: [
            // 财务中心
            { code: 'finance:access', name: '访问财务中心' },
            // 销售记录
            { code: 'finance:sales:read', name: '销售记录' },
            // 销售记录子项
            { code: 'finance:purchase:read', name: '购买记录' },
            { code: 'finance:refund:read', name: '退款记录' },
            //收银记账
            // { code: 'finance:bill:read', name: '收银记账' },
            // 收银记账子项
            { code: 'finance:bill:read', name: '账单列表' },
            { code: 'finance:bill:create', name: '创建账单' },
            { code: 'finance:bill:stats:read', name: '账单统计' },
            // 工资管理
            // { code: 'finance:salary:read', name: '工资管理' },
            // 工资管理子项
            { code: 'finance:salary:read', name: '工资列表' },
            { code: 'finance:teaching:stats:read', name: '授课统计' },
            { code: 'finance:salary:create', name: '创建工资单' },
        ]
    })
    // 1.4 创建数据中心权限
    console.log('创建数据中心权限...');
    await prisma.permission.createMany({
        data: [
            // 数据中心
            { code: 'data:access', name: '访问数据中心' },

            // 教务数据
            { code: 'data:academic:read', name: '教务数据' },
            // 教务数据子项
            { code: 'data:academic:overview:read', name: '整体概览' },
            { code: 'data:academic:consumption:read', name: '消课汇总' },
            { code: 'data:academic:fees:read', name: '学员费用' },
            // 销售数据
            { code: 'data:sales:read', name: '销售数据' },
            // 销售数据子项
            { code: 'data:sales:overview:read', name: '整体概览' },
            { code: 'data:sales:staff:read', name: '销售员统计' },
            { code: 'data:sales:student:read', name: '学员概览' },

        ]
    })
    // 1.5 创建机构中心权限
    console.log('创建机构中心权限...');
    await prisma.permission.createMany({
        data: [
            // 机构信息
            { code: 'org:access', name: '访问机构中心' },
            // 机构信息子项
            { code: 'org:info:read', name: '机构信息' },
            { code: 'org:info:update', name: '更新机构' },
            { code: 'org:address:create', name: '创建地址' },
            // 员工管理
            // { code: 'org:staff:read', name: '员工管理' },
            // 员工管理子项
            { code: 'org:staff:read', name: '员工列表' },
            { code: 'org:staff:create', name: '创建员工' },
            { code: 'org:staff:update', name: '更新员工' },
            { code: 'org:staff:delete', name: '删除员工' },
            // 岗位管理
            // 岗位管理子项
            { code: 'org:position:read', name: '岗位列表' },
            { code: 'org:position:create', name: '创建岗位' },
            { code: 'org:position:update', name: '更新岗位' },
            { code: 'org:position:delete', name: '删除岗位' },
            { code: 'org:position:permission:read', name: '岗位权限' },
            // 考勤记录
            // 考勤记录子项
            { code: 'org:attendance:read', name: '考勤记录' },
            { code: 'org:attendance:stats:read', name: '考勤统计' },
            // 教室管理
            { code: 'org:classroom:read', name: '教室列表' },
            { code: 'org:classroom:create', name: '创建教室' },
            { code: 'org:classroom:update', name: '更新教室' },
            { code: 'org:classroom:delete', name: '删除教室' },
            // 操作日志
            { code: 'org:log:read', name: '操作日志' },
        ]
    })

    // 业务相关权限
    const dashboardAccess = await prisma.permission.create({
        data: { code: 'dashboard:access', name: '访问仪表盘' }
    });

    // 创建超级管理员角色
    console.log('创建超级管理员角色...');
    let superAdminRole;
    try {
        superAdminRole = await prisma.role.create({
            data: {
                name: '超级管理员',
                code: 'superadmin',
                description: '拥有所有权限的超级管理员',
            }
        });
        console.log('超级管理员角色创建成功');
    } catch (error) {
        if (error.code === 'P2002') {
            console.log('超级管理员角色已存在，获取现有角色');
            superAdminRole = await prisma.role.findUnique({
                where: { code: 'superadmin' }
            });
        } else {
            throw error;
        }
    }

    // 创建教师角色
    console.log('创建教师角色...');
    let teacherRole;
    try {
        teacherRole = await prisma.role.create({
            data: {
                id: 'b7e46980-6adc-4af3-8553-d419f110cc44',
                code: 'teacher',
                name: '教师',
                description: '负责教学的教师角色',
            }
        });
        console.log('教师角色创建成功');
    } catch (error) {
        if (error.code === 'P2002') {
            console.log('教师角色已存在，获取现有角色');
            teacherRole = await prisma.role.findUnique({
                where: { code: 'teacher' }
            });
        } else {
            throw error;
        }
    }

    // 教师分配权限
    console.log('分配教师角色权限...');
    const teacherPermissions = [
        'academic:access',
        'student:read',
        'class:read',
        'schedule:read',
        'course:read',
    ]

    // 机构管理员角色
    console.log('创建机构管理员角色...');
    let orgAdmin;
    try {
        orgAdmin = await prisma.role.create({
            data: {
                code: 'orgadmin',
                name: '机构管理员',
                description: '负责机构管理的管理员角色',
            }
        });
        console.log('机构管理员角色创建成功');
    } catch (error) {
        if (error.code === 'P2002') {
            console.log('机构管理员角色已存在，获取现有角色');
            orgAdmin = await prisma.role.findUnique({
                where: { code: 'orgadmin' }
            });
        } else {
            throw error;
        }
    }

    // 机构管理员分配权限
    console.log('分配机构管理员角色权限...');
    const orgAdminPermissions = [
        'org:access',
        'org:info:read',
        'org:info:update',
        'org:staff:read',
        'org:staff:create',
        'org:staff:update',
        'org:staff:delete',
        'org:position:read',
        'org:position:create',
        'org:position:update',
        'org:position:delete',
        'org:position:permission:read',
        'org:attendance:read',
        'org:attendance:stats:read',
        'org:classroom:read',
        'org:classroom:create',
        'org:classroom:update',
        'org:classroom:delete',
        'org:log:read',
    ]

    // 删除现有角色权限关联，避免重复
    console.log('清理角色权限关联...');
    if (teacherRole && teacherRole.id) {
        await prisma.rolePermission.deleteMany({
            where: { roleId: teacherRole.id }
        });
    }
    if (orgAdmin && orgAdmin.id) {
        await prisma.rolePermission.deleteMany({
            where: { roleId: orgAdmin.id }
        });
    }
    if (superAdminRole && superAdminRole.id) {
        await prisma.rolePermission.deleteMany({
            where: { roleId: superAdminRole.id }
        });
    }

    // 分配权限给机构管理员角色
    if (orgAdmin && orgAdmin.id) {
        for (const permissionCode of orgAdminPermissions) {
            try {
                const permission = await prisma.permission.findUnique({
                    where: { code: permissionCode }
                });
                
                if (permission) {
                    await prisma.rolePermission.create({
                        data: {
                            roleId: orgAdmin.id,
                            permissionId: permission.id
                        }
                    });
                } else {
                    console.warn(`权限不存在: ${permissionCode}`);
                }
            } catch (error) {
                console.error(`分配权限 ${permissionCode} 给机构管理员失败:`, error);
            }
        }
    }

    // 分配权限给教师角色
    if (teacherRole && teacherRole.id) {
        for (const permissionCode of teacherPermissions) {
            try {
                const permission = await prisma.permission.findUnique({
                    where: { code: permissionCode }
                });
                
                if (permission) {
                    await prisma.rolePermission.create({
                        data: {
                            roleId: teacherRole.id,
                            permissionId: permission.id
                        }
                    });
                } else {
                    console.warn(`权限不存在: ${permissionCode}`);
                }
            } catch (error) {
                console.error(`分配权限 ${permissionCode} 给教师失败:`, error);
            }
        }
    }

    // 超级管理员拥有所有权限
    const allPermissions = await prisma.permission.findMany();
    if (superAdminRole && superAdminRole.id) {
        for (const permission of allPermissions) {
            try {
                await prisma.rolePermission.create({
                    data: {
                        roleId: superAdminRole.id,
                        permissionId: permission.id
                    }
                });
            } catch (error) {
                console.error(`分配权限 ${permission.code} 给超级管理员失败:`, error);
            }
        }
    }

    // 创建用户
    console.log('创建用户...');
    const password = bcrypt.hashSync('123456', 10); // 使用 bcrypt 加密密码
    
    // 创建超级管理员用户
    let superAdminUser;
    try {
        superAdminUser = await prisma.user.upsert({
            where: { account: 'superadmin' },
            update: {},
            create: {
                id: 'super-admin-id',  // 提供一个固定ID避免重复
                name: '超级管理员',
                password: password.toString(),
                account: 'superadmin',
                phone: '***********',
            },
        });
    } catch (error) {
        console.error('创建超级管理员用户失败:', error);
        throw error;
    }

    // 创建机构管理员用户
    let orgAdminUser;
    try {
        orgAdminUser = await prisma.user.upsert({
            where: { account: 'orgadmin' },
            update: {},
            create: {
                id: 'org-admin-id',  // 提供一个固定ID避免重复
                name: '机构管理员',
                password: password.toString(),
                account: 'orgadmin',
                phone: '***********',
            },
        });
    } catch (error) {
        console.error('创建机构管理员用户失败:', error);
        throw error;
    }

    // 创建机构
    console.log('创建机构...');
    let institution;
    try {
        institution = await prisma.institution.upsert({
            where: { name: '测试机构' },
            update: { userId: orgAdminUser.id },
            create: {
                name: '测试机构',
                userId: orgAdminUser.id, // 关联机构管理员用户
            },
        });
    } catch (error) {
        console.error('创建机构失败:', error);
        throw error;
    }

    // 创建教师用户
    let teacherUser;
    try {
        teacherUser = await prisma.user.upsert({
            where: { account: 'teacher' },
            update: {},
            create: {
                id: 'teacher-user-id',  // 提供一个固定ID避免重复
                name: '教师用户',
                password: password.toString(),
                account: 'teacher',
                phone: '***********',
            },
        });
    } catch (error) {
        console.error('创建教师用户失败:', error);
        throw error;
    }

    // 创建用户机构关联
    try {
        // 首先检查是否已存在关联
        const existingUserInstitution = await prisma.userInstitution.findFirst({
            where: {
                userId: teacherUser.id,
                institutionId: institution.id,
            }
        });
        
        if (!existingUserInstitution) {
            // 如果不存在则创建
            await prisma.userInstitution.create({
                data: {
                    userId: teacherUser.id,
                    institutionId: institution.id,
                }
            });
            console.log('用户机构关联创建成功');
        } else {
            console.log('用户机构关联已存在，无需重复创建');
        }
    } catch (error) {
        console.error('处理用户机构关联失败:', error);
    }

    // 同样为机构管理员创建机构关联
    try {
        const existingOrgAdminInstitution = await prisma.userInstitution.findFirst({
            where: {
                userId: orgAdminUser.id,
                institutionId: institution.id,
            }
        });
        
        if (!existingOrgAdminInstitution) {
            await prisma.userInstitution.create({
                data: {
                    userId: orgAdminUser.id,
                    institutionId: institution.id,
                }
            });
            console.log('机构管理员机构关联创建成功');
        } else {
            console.log('机构管理员机构关联已存在，无需重复创建');
        }
    } catch (error) {
        console.error('处理机构管理员机构关联失败:', error);
    }

    // 删除现有用户角色关联，避免重复
    await prisma.userRole.deleteMany({
        where: {
            OR: [
                { userId: superAdminUser.id },
                { userId: teacherUser.id },
                { userId: orgAdminUser.id }
            ]
        }
    });

    // 分配角色给用户
    console.log('分配角色给用户...');
    try {
        await prisma.userRole.createMany({
            data: [
                {
                    userId: superAdminUser.id,
                    roleId: superAdminRole.id,
                },
                {
                    userId: teacherUser.id,
                    roleId: teacherRole.id,
                },
                {
                    userId: orgAdminUser.id,
                    roleId: orgAdmin.id,
                }
            ],
            skipDuplicates: true,
        });
    } catch (error) {
        console.error('分配角色给用户失败:', error);
        throw error;
    }

    // 2.创建主菜单
    console.log('创建主菜单...');
    await prisma.menu.createMany({
        data: [
            {
                id: 'a7e46980-6adc-4af3-8553-d419f110cc44',
                name: '仪表盘',
                path: '/dashboard',
                icon: 'ChartBarIcon',
                component: 'Dashboard',
                sort: 1,
                permissionCode: 'dashboard:access',
            },
            {
                id: 'b7e46980-6adc-4af3-8553-d419f110cc44',
                name: '教务中心',
                // path: '#academic',
                icon: 'AcademicCapIcon',
                permissionCode: 'academic:access',
                sort: 2
            },
            {
                id: 'c7e46980-6adc-4af3-8553-d419f110cc44',
                name: '招生中心',
                // path: '#enrollment',
                icon: 'BriefcaseIcon',
                permissionCode: 'enrollment:access',
                sort: 3
            },
            {
                id: 'd7e46980-6adc-4af3-8553-d419f110cc44',
                name: '财务中心',
                // path: '#finance',
                icon: 'CurrencyDollarIcon',
                permissionCode: 'finance:access',
                sort: 4
            },
            {
                id: 'e7e46980-6adc-4af3-8553-d419f110cc44',
                name: '数据中心',
                // path: '#data',
                icon: 'ChartPieIcon',
                permissionCode: 'data:access',
                sort: 5
            },
            {
                id: 'f7e46980-6adc-4af3-8553-d419f110cc44',
                name: '机构中心',
                // path: '#organization',
                icon: 'BuildingOfficeIcon',
                permissionCode: 'org:access',
                sort: 6
            },
        ]
    })

    // 3. 创建子菜单
    console.log('创建子菜单...');
    await prisma.menu.createMany({
        data: [
            {
                name: '学员管理',
                path: '/academic/students',
                icon: 'UserGroupIcon',
                sort: 1,
                parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'student:read',
            },
            // 班级管理
            {

                name: '班级管理',
                path: '/academic/classes',
                sort: 2,
                icon: 'BuildingOfficeIcon',
                parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'class:read',
            },
            {

                name: '课表管理',
                path: '/academic/schedule',
                sort: 3,
                icon: 'ChartPieIcon',
                parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'schedule:read',
            },
            {

                name: '课程管理',
                path: '/academic/courses',
                sort: 4,
                icon: 'ChartBarIcon',
                parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'course:read',
            },
            {

                name: '套餐管理',
                path: '/academic/product',
                sort: 5,
                icon: 'ShoppingBagIcon',
                parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'course:read',
            },

            // 招生中心子菜单
            {
                name: '跟进管理',
                path: '/enrollment/followUp',
                icon: 'TagIcon',
                sort: 1,
                parentId: 'c7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'followUp:read',
            },
            // 财务中心子菜单
            {
                name: '销售记录',
                path: '/finance/receipt',
                icon: 'ShoppingBagIcon',
                sort: 1,
                parentId: 'd7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'finance:sales:read',
            },
            {
                name: '收银记账',
                path: '/finance/cashier',
                icon: 'CurrencyDollarIcon',
                sort: 2,
                parentId: 'd7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'finance:bill:read',
            },
            {
                name: '工资管理',
                path: '/finance/wages',
                icon: 'CurrencyDollarIcon',
                sort: 3,
                parentId: 'd7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'finance:salary:read',
            },
            // 数据中心子菜单
            {
                name: '教务数据',
                path: '/data/education',
                icon: 'ChartPieIcon',
                sort: 1,
                parentId: 'e7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'data:academic:read',
            },
            {
                name: '销售数据',
                path: '/data/sales',
                icon: 'CurrencyDollarIcon',
                sort: 2,
                parentId: 'e7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'data:sales:read',
            },
            // 机构中心子菜单
            {
                name: '机构信息',
                path: '/organization/info',
                icon: 'InformationCircleIcon',
                sort: 1,
                parentId: 'f7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'org:info:read',
            },
            {
                name: '员工管理',
                path: '/organization/staff',
                icon: 'UserGroupIcon',
                sort: 2,
                parentId: 'f7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'org:staff:read',
            },
            {
                name: '教室管理',
                path: '/organization/classrooms',
                icon: 'BuildingOfficeIcon',
                sort: 3,
                parentId: 'f7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'org:classroom:read',
            },
            {
                name: '操作日志',
                path: '/organization/log',
                icon: 'InboxIcon',
                sort: 4,
                parentId: 'f7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'org:log:read',
            },

        ]
    })
}

main().catch(e => {
    console.error(e);
    process.exit(1);
}).finally(async () => {
    await prisma.$disconnect();
})