import React, { useEffect, useState } from 'react'
import type { UseFormReturn } from "react-hook-form"
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import type formSchema from "../schema/form"
import { z } from 'zod'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { cn } from '@/lib/utils'
import { useClasses } from '@/hooks/useClasses'

interface ScheduleSettingsFormProps {
  form: UseFormReturn<z.infer<typeof formSchema>>
}
interface SelectOption {
  id: string
  name: string
  freeTime: boolean
}

function TeacherSelectForm({ form }: ScheduleSettingsFormProps) {
  const { getTeacherFreeTime } = useClasses()
  const [teacherOpen, setTeacherOpen] = useState(false)
  const [teacherOptions, setTeacherOptions] = useState<SelectOption []>([])

  const getTeacherFreeTimeData = async (value: any) => {
    const data = {
      startDate: new Date(value.startDate).getTime(),
      endDate: new Date(value.endDate).getTime(),
      endType: value.endType,
      daily: value.daily,
      weekdays: value.weekdays,
      times: value.times,
    }

    try {
      const response = await getTeacherFreeTime(data)
      setTeacherOptions(response)
      // setIsTeacherSelectOpen(true)
    } catch (error) {
      console.error("获取教师空闲时间失败", error)
    }
  }

  useEffect(() => {
    // 监听表单值变化
    const subscription = form.watch((value) => {
      if (value.startDate && value.endType && value.startDate) {
        if (value.daily || value.weekdays?.length) {
          if (value.endDate || value.times) {
            // 现在可以获取教师了
            getTeacherFreeTimeData(value)
          }
        }
      }
    })

    return () => subscription.unsubscribe()
  }, [form])


  return (
    <>
      <div className=" pb-2 space-y-3">
        <FormLabel className="text-sm">主讲老师</FormLabel>

        <Select onValueChange={(value) => form.setValue("teacherId", value)}>
          <SelectTrigger className="w-full h-10 text-sm border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 bg-transparent">
            <SelectValue placeholder="选择主讲老师" />
          </SelectTrigger>
          <SelectContent>
            {teacherOptions.length === 0 ? (
              <div className="py-6 text-center text-sm text-slate-500">暂无教师数据</div>
            ) : (
              teacherOptions.map(
                (teacher: SelectOption) => (
                  <SelectItem key={teacher.id} value={teacher.id} className="text-sm">
                    {teacher.name} {teacher.freeTime ? (
                      <span className="text-xs text-green-500">(有空)</span>
                    ) : (
                      <span className="text-xs text-red-500">(无空)</span>
                    )}
                  </SelectItem>
                ),
              )
            )}
          </SelectContent>
        </Select>
      </div>
    </>

  )
}

export default TeacherSelectForm