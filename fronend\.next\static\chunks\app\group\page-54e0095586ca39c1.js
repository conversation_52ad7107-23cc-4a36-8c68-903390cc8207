(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4442],{24663:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var l=s(95155);function a(e){let{title:t,list:s}=e;return(0,l.jsxs)("section",{className:"mx-2.5 my-4",children:[(0,l.jsx)("div",{className:"flex justify-center mb-2",children:(0,l.jsxs)("div",{className:"relative inline-block",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-red-800 rounded-md translate-x-0.5 translate-y-0.5"}),(0,l.jsx)("div",{className:"relative bg-red-500 text-white px-6 py-1.5 rounded-md text-center font-medium border border-white",children:t})]})}),(0,l.jsx)("div",{className:"border-2 border-red-300 rounded-lg p-4 bg-white shadow-sm",children:s.map((e,t)=>"text"===e.type?(0,l.jsx)("p",{className:"text-gray-800 text-base leading-relaxed pl-2",children:e.text.split("\n").map((e,t)=>(0,l.jsx)("span",{className:"block mb-1",children:e},t))},t):"img"===e.type?(0,l.jsx)("img",{src:e.img,alt:"",className:"w-full rounded-md my-2.5",loading:"lazy"},t):null)})]})}s(12115)},29733:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var l=s(95155);function a(e){let{buttons:t,onAction:s}=e;return(0,l.jsx)("div",{className:"flex justify-around my-4",children:t.map(e=>(0,l.jsx)("button",{disabled:!e.enabled,className:"bg-red-500 text-white rounded-full px-8 py-2.5 text-lg font-medium shadow-md transition-all hover:bg-red-600 active:bg-red-700 ".concat(e.enabled?"":"opacity-50 cursor-not-allowed"),onClick:()=>e.enabled&&s(e.type),children:e.text},e.type))})}s(12115)},44425:(e,t,s)=>{Promise.resolve().then(s.bind(s,98449))},46873:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var l=s(95155),a=s(12115);function r(e){let{currentPrice:t,originalPrice:s,endTime:r}=e,[n,i]=(0,a.useState)({days:0,hours:0,minutes:0,seconds:0});(0,a.useEffect)(()=>{let e=setInterval(()=>{let t=r-new Date().getTime();if(t<0){clearInterval(e),i({days:0,hours:0,minutes:0,seconds:0});return}let s=Math.floor(t/864e5),l=Math.floor(t%864e5/36e5);i({days:s,hours:l,minutes:Math.floor(t%36e5/6e4),seconds:Math.floor(t%6e4/1e3)})},1e3);return()=>clearInterval(e)},[r]);let d=e=>{let{value:t,label:s}=e;return(0,l.jsxs)("div",{className:"flex flex-col items-center mx-0.5",children:[(0,l.jsx)("div",{className:"bg-white bg-opacity-20 text-white text-xs font-medium rounded px-1.5 py-0.5 min-w-[20px] text-center",children:t<10?"0".concat(t):t}),(0,l.jsx)("span",{className:"text-[10px] text-white mt-0.5",children:s})]})};return(0,l.jsx)("div",{className:"my-2 px-2",children:(0,l.jsx)("div",{className:"bg-gradient-to-r from-red-500 to-pink-500 rounded-lg shadow-md overflow-hidden",children:(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center p-3",children:[(0,l.jsxs)("div",{className:"flex items-end mb-2 sm:mb-0",children:[(0,l.jsxs)("div",{className:"flex items-baseline",children:[(0,l.jsx)("span",{className:"text-white text-sm font-medium",children:"\xa5"}),(0,l.jsx)("span",{className:"text-white text-2xl font-bold mx-1",children:t})]}),(0,l.jsxs)("div",{className:"flex items-center ml-3 bg-white bg-opacity-10 rounded px-2 py-0.5",children:[(0,l.jsx)("span",{className:"text-xs text-white opacity-80",children:"原价:"}),(0,l.jsxs)("span",{className:"text-xs text-white line-through opacity-80 ml-1",children:["\xa5",s]})]})]}),(0,l.jsx)("div",{className:"hidden sm:block sm:mx-4 sm:h-8 sm:w-px bg-white bg-opacity-20"}),(0,l.jsxs)("div",{className:"flex items-center mt-1 sm:mt-0 sm:ml-auto",children:[(0,l.jsx)("div",{className:"bg-gradient-to-r from-red-600 to-red-500 rounded-full px-3 py-1 text-xs text-white mr-2",children:"限时"}),(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsxs)("div",{className:"flex",children:[(0,l.jsx)(d,{value:n.days,label:"天"}),(0,l.jsx)(d,{value:n.hours,label:"时"}),(0,l.jsx)(d,{value:n.minutes,label:"分"}),(0,l.jsx)(d,{value:n.seconds,label:"秒"})]})})]})]})})})}},56904:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var l=s(95155);s(12115);var a=s(66766),r=s(54416);function n(e){let{open:t,onClose:s,qrCode:n,shareTitle:i,shareDesc:d}=e;return t?(0,l.jsx)("div",{className:"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4",children:(0,l.jsxs)("div",{className:"bg-white rounded-2xl w-full max-w-sm relative",children:[(0,l.jsx)("button",{onClick:s,className:"absolute top-2 right-2 text-gray-400 hover:text-gray-600 p-1",children:(0,l.jsx)(r.A,{size:24})}),(0,l.jsxs)("div",{className:"pt-6 pb-4 px-6 text-center border-b border-gray-100",children:[(0,l.jsx)("h3",{className:"text-xl font-bold",children:"分享给好友"}),(0,l.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"邀请好友一起参与"})]}),(0,l.jsx)("div",{className:"grid grid-cols-4 gap-2 p-6",children:[{icon:"/icons/wechat.svg",name:"微信",color:"bg-green-500"},{icon:"/icons/moments.svg",name:"朋友圈",color:"bg-green-600"},{icon:"/icons/qq.svg",name:"QQ",color:"bg-blue-400"},{icon:"/icons/weibo.svg",name:"微博",color:"bg-red-500"}].map(e=>(0,l.jsxs)("button",{className:"flex flex-col items-center",children:[(0,l.jsx)("div",{className:"w-12 h-12 ".concat(e.color," rounded-full flex items-center justify-center mb-1 text-white"),children:e.icon?(0,l.jsx)(a.default,{src:e.icon,alt:e.name,width:24,height:24}):e.name.charAt(0)}),(0,l.jsx)("span",{className:"text-xs",children:e.name})]},e.name))}),n&&(0,l.jsxs)("div",{className:"px-6 pb-6 flex flex-col items-center",children:[(0,l.jsx)("div",{className:"border border-gray-200 rounded-lg p-2 mb-2",children:(0,l.jsx)("img",{src:n,alt:"二维码",className:"w-40 h-40 object-contain"})}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"扫描二维码参与活动"})]}),(0,l.jsx)("div",{className:"bg-gray-50 p-4 rounded-b-2xl border-t border-gray-100",children:(0,l.jsxs)("div",{className:"bg-white p-3 rounded-lg border border-gray-200",children:[(0,l.jsx)("h4",{className:"font-medium text-sm",children:i}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:d})]})})]})}):null}},98449:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var l=s(95155),a=s(12115),r=s(29733);function n(e){let{open:t,onClose:s,formFields:r=[]}=e,[n,i]=(0,a.useState)({});if(!t)return null;let d=(e,t)=>{i(s=>({...s,[e.id]:t}))};return(0,l.jsx)("div",{className:"fixed inset-0 z-50 bg-black/30 flex items-center justify-center",children:(0,l.jsxs)("form",{onSubmit:e=>{e.preventDefault(),console.log("表单数据:",n),s()},className:"bg-white rounded-2xl w-[320px] p-6 shadow-lg",children:[(0,l.jsx)("h2",{className:"text-center text-2xl mb-6 font-bold",children:"完善报名信息"}),(0,l.jsx)("div",{className:"mb-5 space-y-4",children:r.map(e=>(0,l.jsxs)("div",{children:["text"===e.type&&(0,l.jsx)("input",{placeholder:e.placeholder||"请输入".concat(e.label),required:e.required,value:n[e.id]||"",onChange:t=>d(e,t.target.value),className:"w-full px-3 py-2.5 border border-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-300"}),"number"===e.type&&(0,l.jsx)("input",{type:"number",placeholder:e.placeholder||"请输入".concat(e.label),required:e.required,value:n[e.id]||"",onChange:t=>d(e,t.target.value),className:"w-full px-3 py-2.5 border border-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-300"}),"radio"===e.type&&e.options&&(0,l.jsxs)("div",{className:"w-full border border-gray-100 rounded-lg px-3 py-2",children:[(0,l.jsx)("label",{className:"block text-sm text-gray-600 mb-1",children:e.label}),(0,l.jsx)("div",{className:"flex space-x-4",children:e.options.map(t=>(0,l.jsxs)("label",{className:"flex items-center cursor-pointer",children:[(0,l.jsx)("input",{type:"radio",name:e.id,value:t.value,checked:n[e.id]===t.value,onChange:()=>d(e,t.value),className:"w-4 h-4 text-blue-400 focus:ring-2 focus:ring-blue-300 mr-1",required:e.required&&!n[e.id]}),(0,l.jsx)("span",{children:t.label})]},t.value))})]}),"select"===e.type&&e.options&&(0,l.jsxs)("div",{className:"w-full",children:[(0,l.jsx)("label",{className:"block text-sm text-gray-600 mb-1",children:e.label}),(0,l.jsxs)("select",{value:n[e.id]||"",onChange:t=>d(e,t.target.value),className:"w-full px-3 py-2.5 border border-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-300",required:e.required,children:[(0,l.jsx)("option",{value:"",children:e.placeholder||"请选择"}),e.options.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]})]})]},e.id))}),(0,l.jsx)("button",{type:"submit",className:"w-full py-3 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 text-white text-lg border-0 mb-2 transition hover:from-blue-500 hover:to-blue-600 active:from-blue-600 active:to-blue-700",children:"我要报名"}),(0,l.jsx)("div",{className:"text-center",children:(0,l.jsx)("button",{type:"button",onClick:s,className:"text-gray-400 bg-transparent border-0 text-base py-1 px-3 hover:text-gray-600 transition",children:"取消"})})]})})}var i=s(56904),d=s(46873),c=s(24663);function o(){let[e,t]=(0,a.useState)(!1),[s,o]=(0,a.useState)(!1),m={data:{html:{module:[{title:"活动介绍",list:[{text:"速写，很多人理解成是写，或者是就像中国画很写意的书写作画，这些只说对了一种表面，速写不仅仅是一种绘画方式，更是通过速写锻炼作画者对现实生活的观察能力，分析能力与表现能力，通过作画让我们学会提炼，总结。在入门阶段我们就要做好两点充分的准备，思想准备与材料准备，并且立下恒心，因为速写不是一朝一夕就能够达到一定水平的。",type:"text"},{img:"https://image.cardmee.net/2/2021/05/image/1621906385171587078.png",type:"img"}]},{title:"拼团说明",list:[{text:'1.点击按钮"我要开团"或者"我要参团"\n2.参团或者开团后分享给好友，邀他一起加入。\n3.发起拼团之后24小时之内没成团的为无效团，系统\n   将会自动取消该拼团。\n4.满足对应团人数，再有人参与进来则自动升级成下\n   一团。\n5.拼团成功后生成核销码。\n6.活动结束之后去线下机构核销兑换。\n',type:"text"}]},{title:"机构介绍",list:[{text:"成童成美艺术是英国纳斯达克上市公司达内教育集团旗下少儿艺术教育品牌，依托集团公司18年的教学经验，专注3-18 岁少儿教育及服务， 教学体系落实创新教育理念，强调学生综合能力的培养和实际解决问题能力的提升，在兴趣激发和思维锻炼的同时，传递前沿技术，帮助中国青少年打造迎接未来世界的能力和思维视野。 作为线上线下一体化的全国化的运营品牌，线下建立了250余家校区，流动课堂走进120多个中小学公立校。线上，依靠在线教学平台，可以实现覆盖全国各地。成童成美已成为中国最大的少儿艺术教育品牌之一，累计培养学员80000万多名。",type:"text"},{img:"https://image.cardmee.net/2/2021/05/image/1621906462458113710.png",type:"img"},{img:"https://image.cardmee.net/2/2021/05/image/1621906472776731239.png",type:"img"}]}],type:19,audioUrl:"https://image.cardmee.net/540/2020/04/voice/0d3fb888-f41a-4021-8e2d-22a93b3150e5.mp3",audioName:"My Wonderful Car.mp3",ele1:"https://image.cardmee.net/2/2021/11/image/1636430859349609527.png"},options:{cover:"https://image.cardmee.net/default/2019/10/image/5fe2d601-9cfb-4e4f-a94f-3f78173b612d.png",description:"",encryptionTemplateId:"gT8dKnIrvNtuzGMFTVCepQ",htmlUrl:"pages/user/front_singleGroup/",level:3,maxPersons:null,name:"不止5折，拼团啦！",seller:{openPay:1},shareDesc:"团拼来袭，疯狂抢不停",sharePic:"https://image.cardmee.net/default/2019/10/image/64657c6d-f3bf-4a0b-ba86-057fbc414f39.png",shareTitle:"一起来拼",type:5,buttons:[{text:"我要开团",type:"open",enabled:!0}],prices:{currentPrice:"0.01",originalPrice:"3.02",endTime:new Date().getTime()+2592e5},formFields:[{id:"name",label:"学员姓名",type:"text",required:!0,placeholder:"请输入学员姓名"},{id:"age",label:"学员年龄",type:"number",required:!0,placeholder:"请输入学员年龄"},{id:"phone",label:"联系电话",type:"text",required:!0,placeholder:"请输入联系电话"}]}},msg:"",code:0},x=m.data.html,u=m.data.options,h=u.buttons||[{text:"我要开团",type:"open",enabled:!0},{text:"我要参团",type:"join",enabled:!0}],p=e=>{"open"===e&&t(!0)};return(0,l.jsxs)("div",{className:"bg-white min-h-screen font-sans pb-20",children:[(0,l.jsx)("img",{src:u.cover,alt:"banner",className:"w-full"}),(0,l.jsx)(d.A,{currentPrice:u.prices.currentPrice,originalPrice:u.prices.originalPrice,endTime:u.prices.endTime}),(0,l.jsxs)("div",{className:"flex items-start justify-between mt-2.5 px-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-xl text-red-500",children:u.name}),(0,l.jsx)("p",{className:"text-sm text-gray-400 mb-2.5",children:u.shareDesc})]}),(0,l.jsxs)("button",{onClick:()=>{o(!0)},className:"flex flex-col items-center text-gray-500 pt-1",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,l.jsx)("path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"}),(0,l.jsx)("polyline",{points:"16 6 12 2 8 6"}),(0,l.jsx)("line",{x1:"12",y1:"2",x2:"12",y2:"15"})]})}),(0,l.jsx)("span",{className:"text-xs mt-1",children:"分享"})]})]}),(0,l.jsx)("div",{className:"flex justify-around my-4",children:(0,l.jsx)(r.A,{buttons:h,onAction:p})}),x.module.map((e,t)=>(0,l.jsx)(c.A,{title:e.title,list:e.list},t)),(0,l.jsx)("div",{className:"fixed inset-x-0 bottom-0 bg-white shadow-lg flex justify-around py-2.5",children:(0,l.jsx)(r.A,{buttons:h,onAction:p})}),e&&(0,l.jsx)(n,{open:e,onClose:()=>t(!1),formFields:u.formFields||[]}),s&&(0,l.jsx)(i.A,{open:s,onClose:()=>o(!1),shareTitle:u.shareTitle||u.name,shareDesc:u.shareDesc,qrCode:"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=".concat(encodeURIComponent(window.location.href))})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9209,6315,7358],()=>t(44425)),_N_E=e.O()}]);