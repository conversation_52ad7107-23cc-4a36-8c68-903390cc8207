import { Queue, Worker } from "bullmq";
import { redisTask, redisConfig } from "../config/redis.js";
import { generateText } from "./textGeneration.js";

// 队列配置
const queueOptions = {
  connection: redisConfig,
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 1000
    },
    removeOnComplete: {
      age: 3600,
      count: 1000
    },
    removeOnFail: {
      age: 3600 * 24 * 7
    },
    lockDuration: 300000,
    timeout: 300000,
  },
  limiter: {
    max: 5,
    duration: 1000,
    groupKey: 'text-generation'
  },
  streams: {
    events: {
      maxLen: 10000
    }
  }
};

const textQueue = new Queue('text-generation', queueOptions);

// 添加错误处理
textQueue.on('error', (error) => {
  console.error('文本生成队列错误:', error);
});

textQueue.on('failed', (job, error) => {
  console.error(`文本生成任务 ${job.id} 失败:`, error);
});

// Worker配置
const workerOptions = {
  connection: redisConfig,
  concurrency: 5,
  lockDuration: 300000,
  stalledInterval: 30000,
  maxStalledCount: 2,
  drainDelay: 5,
  autorun: true,
  metrics: {
    maxDataPoints: 100
  }
};

// 创建Worker处理任务
const worker = new Worker(
  'text-generation',
  async (job) => {
    const { taskId, prompt, maxTokens, temperature, model } = job.data;
    console.log(`开始处理文本生成任务 ${job.id}, taskId: ${taskId}`);

    const startTime = Date.now();

    try {
      // 更新任务状态为处理中
      await redisTask.multi()
        .set(
          `task:${taskId}`,
          JSON.stringify({ status: 'processing', taskId, prompt, progress: 0, type: 'text-generation' }),
          'EX',
          3600
        )
        .set(
          `task:${taskId}:start`,
          startTime.toString(),
          'EX',
          3600
        )
        .exec();

      // 定期更新进度
      const progressInterval = setInterval(async () => {
        try {
          const elapsed = Date.now() - startTime;
          const progressPercent = Math.min(Math.floor((elapsed / 60000) * 100), 95);

          await job.updateProgress(progressPercent);
        } catch (e) {
          console.error('更新进度失败:', e);
        }
      }, 3000);

      // 执行文本生成
      const result = await Promise.race([
        generateText(prompt, maxTokens, temperature, model),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('文本生成超时')), 270000) // 4.5分钟超时
        )
      ]);

      // 清除进度更新定时器
      clearInterval(progressInterval);

      // 计算处理时间
      const processingTime = Date.now() - startTime;

      // 更新任务状态为完成
      await redisTask.multi()
        .set(
          `task:${taskId}`,
          JSON.stringify({
            status: 'completed',
            taskId,
            result,
            processingTime,
            type: 'text-generation'
          }),
          'EX',
          3600
        )
        .set(
          `task:${taskId}:end`,
          Date.now().toString(),
          'EX',
          3600
        )
        .exec();

      console.log(`文本生成任务 ${job.id} 完成，处理时间: ${processingTime}ms`);
      return result;
    } catch (error) {
      console.error(`文本生成任务 ${job.id} 失败:`, error);

      // 更新任务状态为失败
      await redisTask.set(
        `task:${taskId}`,
        JSON.stringify({
          status: 'failed',
          taskId,
          error: error.message,
          stack: error.stack,
          processingTime: Date.now() - startTime,
          type: 'text-generation'
        }),
        'EX',
        3600
      );

      // 重新抛出错误，让BullMQ处理重试
      throw error;
    }
  },
  workerOptions
);

// 添加Worker错误处理
worker.on('error', (error) => {
  console.error('文本生成Worker错误:', error);
});

worker.on('failed', (job, error) => {
  console.error(`文本生成Worker处理任务 ${job?.id} 失败:`, error);
});

worker.on('completed', (job) => {
  console.log(`文本生成Worker完成任务 ${job.id}`);
});

export default textQueue;
