import { test } from 'node:test';
import { strict as assert } from 'node:assert';
import roleController from '../../controllers/roleController.js';

test('Role Controller', async (t) => {
  await t.test('getAllRoles should return roles list with pagination', async () => {
    // Mock request and reply
    const request = {
      query: {
        page: 1,
        pageSize: 10,
        search: 'test'
      },
      user: {
        id: 'test-user-id',
        institutionId: 'test-institution-id'
      },
      server: {
        prisma: {
          $transaction: async () => [
            [
              { id: 'role1', name: 'Test Role 1', description: 'Description 1' },
              { id: 'role2', name: 'Test Role 2', description: 'Description 2' }
            ],
            2
          ]
        }
      },
      log: {
        error: () => {}
      }
    };

    const reply = {
      success: (data) => {
        return {
          statusCode: 200,
          ...data
        };
      }
    };

    // Call the controller method
    const result = await roleController.getAllRoles(request, reply);
    
    // Assert the result
    assert.equal(result.statusCode, 200);
    assert.equal(result.message, '获取角色列表成功');
    assert.equal(result.data.list.length, 2);
    assert.equal(result.data.total, 2);
    assert.equal(result.data.page, 1);
    assert.equal(result.data.pageSize, 10);
  });
});
