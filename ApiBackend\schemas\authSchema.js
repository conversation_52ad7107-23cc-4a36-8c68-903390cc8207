/**
 * Authentication Schema
 * Defines validation schemas for authentication-related endpoints
 */
const authSchema = {
    /**
     * Schema for user login
     */
    loginSchema: {
        tags: ['auth'],
        summary: '用户登录',
        description: '用户登录接口',
        body: {
            type: 'object',
            required: ['account', 'password'],
            properties: {
                account: { 
                    type: 'string',
                    description: '用户账号'
                },
                password: { 
                    type: 'string',
                    description: '用户密码'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            user: {
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    account: { type: 'string' },
                                    name: { type: 'string' },
                                    email: { type: 'string' },
                                    phone: { type: 'string' },
                                    avatar: { type: 'string' },
                                    status: { type: 'string' },
                                    lastLoginAt: { type: 'string' },
                                    institutionId: { type: 'string' },
                                    institutionName: { type: 'string' },
                                    institutionLogo: { type: 'string' },
                                    institutionSubjectName: { type: 'string' },
                                    notificationCount: { type: 'number' }
                                }
                            },
                            accessToken: { type: 'string' },
                            refreshToken: { type: 'string' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for user logout
     */
    logoutSchema: {
        tags: ['auth'],
        summary: '用户登出',
        description: '用户登出接口',
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for getting current user info
     */
    getCurrentUserSchema: {
        tags: ['auth'],
        summary: '获取当前用户信息',
        description: '获取当前登录用户的详细信息',
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            account: { type: 'string' },
                            name: { type: 'string' },
                            email: { type: 'string' },
                            phone: { type: 'string' },
                            avatar: { type: 'string' },
                            status: { type: 'string' },
                            lastLoginAt: { type: 'string' },
                            createdAt: { type: 'string' },
                            updatedAt: { type: 'string' },
                            institutionId: { type: 'string' },
                            institutionName: { type: 'string' },
                            institutionLogo: { type: 'string' },
                            institutionSubjectName: { type: 'string' },
                            notificationCount: { type: 'number' },
                            roles: { 
                                type: 'array',
                                items: { type: 'string' }
                            },
                            permissions: { 
                                type: 'array',
                                items: { type: 'string' }
                            }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for updating current user info
     */
    updateCurrentUserSchema: {
        tags: ['auth'],
        summary: '更新当前用户信息',
        description: '更新当前登录用户的信息',
        body: {
            type: 'object',
            properties: {
                name: { 
                    type: 'string',
                    description: '用户姓名'
                },
                email: { 
                    type: 'string',
                    format: 'email',
                    description: '用户邮箱'
                },
                phone: { 
                    type: 'string',
                    description: '用户手机号'
                },
                avatar: { 
                    type: 'string',
                    description: '用户头像URL'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for changing password
     */
    changePasswordSchema: {
        tags: ['auth'],
        summary: '修改密码',
        description: '修改当前登录用户的密码',
        body: {
            type: 'object',
            required: ['oldPassword', 'newPassword'],
            properties: {
                oldPassword: { 
                    type: 'string',
                    description: '原密码'
                },
                newPassword: { 
                    type: 'string',
                    description: '新密码'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for resetting password
     */
    resetPasswordSchema: {
        tags: ['auth'],
        summary: '重置密码',
        description: '管理员重置用户密码',
        params: {
            type: 'object',
            required: ['userId'],
            properties: {
                userId: { 
                    type: 'string',
                    description: '用户ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            account: { type: 'string' },
                            password: { type: 'string' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for registering new user
     */
    registerSchema: {
        tags: ['auth'],
        summary: '注册用户',
        description: '管理员注册新用户',
        body: {
            type: 'object',
            required: ['account', 'password', 'name'],
            properties: {
                account: { 
                    type: 'string',
                    description: '用户账号'
                },
                password: { 
                    type: 'string',
                    description: '用户密码'
                },
                name: { 
                    type: 'string',
                    description: '用户姓名'
                },
                email: { 
                    type: 'string',
                    format: 'email',
                    description: '用户邮箱'
                },
                phone: { 
                    type: 'string',
                    description: '用户手机号'
                },
                institutionId: { 
                    type: 'string',
                    description: '机构ID'
                },
                roleIds: { 
                    type: 'array',
                    items: { type: 'string' },
                    description: '角色ID数组'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            account: { type: 'string' },
                            name: { type: 'string' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for refreshing token
     */
    refreshTokenSchema: {
        tags: ['auth'],
        summary: '刷新访问令牌',
        description: '使用刷新令牌获取新的访问令牌',
        body: {
            type: 'object',
            required: ['refreshToken'],
            properties: {
                refreshToken: { 
                    type: 'string',
                    description: '刷新令牌'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            accessToken: { type: 'string' },
                            refreshToken: { type: 'string' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    }
};

export default authSchema;
