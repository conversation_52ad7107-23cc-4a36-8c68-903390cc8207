"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5192],{15192:(e,l,t)=>{t.r(l),t.d(l,{default:()=>s});var r=t(95155),a=t(85127),o=t(26126),d=t(59434),b=t(57141);function s(e){let{attendanceData:l}=e;if(!l)return(0,r.jsx)("div",{children:"Loading..."});let{header:t,students:s}=l,n={attendance:0,unattended:0,leave:0,absent:0};return s.forEach(e=>{e.attendance.forEach(e=>{e in n&&n[e]++})}),(0,r.jsx)("div",{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"my-2 bg-white p-3 rounded-md shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"text-gray-700 font-medium",children:"出勤总计："}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,r.jsxs)(o.E,{variant:"outline",className:"bg-blue-500 text-white px-2 py-1 text-sm rounded-md",children:["已到课: ",n.attendance]}),(0,r.jsxs)(o.E,{variant:"outline",className:"bg-emerald-500 text-white px-2 py-1 text-sm rounded-md",children:["未考勤: ",n.unattended]}),(0,r.jsxs)(o.E,{variant:"outline",className:"bg-yellow-500 text-white px-2 py-1 text-sm rounded-md",children:["请假: ",n.leave]}),(0,r.jsxs)(o.E,{variant:"outline",className:"bg-red-500 text-white px-2 py-1 text-sm rounded-md",children:["缺勤: ",n.absent]})]})]})}),(0,r.jsx)("div",{className:"p-0",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsx)(a.XI,{children:(0,r.jsxs)(a.BF,{children:[(0,r.jsxs)(a.Hj,{className:"bg-gray-50 dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 border-b",children:[(0,r.jsx)(a.nA,{className:"font-medium text-center border-r min-w-[100px]",children:(0,r.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"学员姓名"})}),t.map(e=>(0,r.jsxs)(a.nA,{className:"text-center border-r min-w-[140px] p-3",children:[(0,r.jsx)("div",{className:"text-blue-600 dark:text-blue-400 font-medium",children:e.title}),(0,r.jsx)("div",{className:"text-blue-600 dark:text-blue-400 font-medium",children:e.date}),(0,r.jsx)("div",{className:"text-gray-500 dark:text-gray-400 text-sm",children:e.time})]},e.id))]}),s.map((e,l)=>(0,r.jsxs)(a.Hj,{className:(0,d.cn)("hover:bg-blue-50 dark:hover:bg-blue-950/30 transition-colors",l%2==0?"bg-white dark:bg-gray-950":"bg-gray-50 dark:bg-gray-900/50"),children:[(0,r.jsx)(a.nA,{className:"font-medium text-center border-r",children:(0,r.jsx)("span",{className:"text-gray-800 dark:text-gray-200",children:e.name})}),Array.from({length:t.length}).map((l,t)=>{let o=e.attendance[t],d=b.oD[o]||b.oD.default;return(0,r.jsx)(a.nA,{className:"text-center p-2 border-r",children:o?(0,r.jsx)("div",{className:"".concat(d.className,"  py-1.5 px-4 rounded-md inline-block transition-colors shadow-sm"),children:d.text}):(0,r.jsx)("div",{className:"text-xl text-gray-400 dark:text-gray-500 font-light",children:"\xd7"})},t)})]},e.id))]})})})})]})})}},57141:(e,l,t)=>{t.d(l,{C9:()=>a,DT:()=>b,I2:()=>d,IC:()=>x,N4:()=>g,fb:()=>i,lc:()=>r,oD:()=>s,u7:()=>c,uq:()=>o,x9:()=>n});let r={"limited-sessions":{label:"限次",color:"capitalize font-normal bg-blue-50 text-blue-700 hover:bg-blue-100 px-2.5 py-0.5 rounded-md"},"limited-time-and-count":{label:"时次限",color:"capitalize font-normal bg-violet-50 text-violet-700 hover:bg-violet-100 px-2.5 py-0.5 rounded-md"},default:{label:"未知套餐",color:"capitalize font-normal bg-slate-100 text-slate-700 hover:bg-slate-200 px-2.5 py-0.5 rounded-md"}},a={cash:{label:"现金",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},alipay:{label:"支付宝",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},wechat:{label:"微信",color:"bg-green-100 text-green-800 hover:bg-green-200"},card:{label:"银行卡",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},other:{label:"其他",color:"bg-slate-100 text-slate-800 hover:bg-slate-200"}},o=[{id:"wechat",label:"微信"},{id:"alipay",label:"支付宝"},{id:"card",label:"银行转账"},{id:"cash",label:"现金"},{id:"other",label:"其他"}],d={done:{label:"完成",color:"bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400"},arrears:{label:"欠费",color:"bg-amber-100 text-amber-800 hover:bg-amber-200 dark:bg-amber-900/30 dark:text-amber-400"},refunded:{label:"退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400"},default:{label:"未知",color:"bg-slate-100 text-slate-800 hover:bg-slate-200 dark:bg-slate-800/50 dark:text-slate-400"}},b={active:{badgeClass:"bg-emerald-50 text-emerald-700 hover:bg-emerald-100 border-emerald-200",dotClass:"bg-emerald-500",text:"正常"},expired:{badgeClass:"bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200",dotClass:"bg-amber-500",text:"已到期"},refunded:{badgeClass:"bg-red-50 text-red-700 hover:bg-red-100 border-red-200",dotClass:"bg-red-500",text:"已退款"},frozen:{badgeClass:"bg-slate-50 text-slate-700 hover:bg-slate-100 border-slate-200",dotClass:"bg-slate-500",text:"已冻结"},default:{badgeClass:"bg-slate-100 text-slate-700 hover:bg-slate-200 border-slate-200",dotClass:"bg-slate-400",text:"未知状态"}},s={all:{className:"bg-slate-100 text-slate-800 hover:bg-slate-200",text:"全部状态"},attendance:{className:"bg-green-100 text-green-800 hover:bg-green-200",text:"已考勤"},leave:{className:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200",text:"请假"},unattended:{className:"bg-orange-100 text-orange-800 hover:bg-orange-200",text:"未考勤"},default:{className:"bg-red-100 text-red-800 hover:bg-red-200",text:"缺勤"}},n={A:{label:"非常感兴趣",color:"bg-green-100 text-green-800 hover:bg-green-200"},B:{label:"比较感兴趣",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},C:{label:"一般意向",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},D:{label:"了解意向",color:"bg-orange-100 text-orange-800 hover:bg-orange-200"},E:{label:"不感兴趣",color:"bg-red-100 text-red-800 hover:bg-red-200"},default:{label:"未知",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},g={productSales:{label:"产品销售",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},courseSales:{label:"课程销售",color:"bg-green-100 text-green-800 hover:bg-green-200"},OverduePaymentCollection:{label:"逾期款项催收",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},lifestyleAndOffice:{label:"生活和办公",color:"bg-red-100 text-red-800 hover:bg-red-200"},socialAndCatering:{label:"社交和餐饮",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},refunded:{label:"退款",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"其他",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},c={completed:{label:"已完成",color:"bg-green-100 text-green-800 hover:bg-green-200"},pending:{label:"待审核",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},refunded:{label:"已退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},failed:{label:"审核未通过",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"未知状态",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},x={formal:{label:"正式学员",color:"text-blue-500"},graduated:{label:"历史学员",color:"text-green-500"},public:{label:"公海池",color:"text-red-500"},intent:{label:"意向学员",color:"text-yellow-500"}},i={secret:{label:"保密",color:"text-gray-500"},male:{label:"男",color:"text-blue-500"},female:{label:"女",color:"text-pink-500"}}},85127:(e,l,t)=>{t.d(l,{A0:()=>b,BF:()=>s,Hj:()=>n,XI:()=>d,nA:()=>c,nd:()=>g});var r=t(95155),a=t(12115),o=t(59434);let d=a.forwardRef((e,l)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:l,className:(0,o.cn)("w-full caption-bottom text-sm",t),...a})})});d.displayName="Table";let b=a.forwardRef((e,l)=>{let{className:t,...a}=e;return(0,r.jsx)("thead",{ref:l,className:(0,o.cn)("[&_tr]:border-b",t),...a})});b.displayName="TableHeader";let s=a.forwardRef((e,l)=>{let{className:t,...a}=e;return(0,r.jsx)("tbody",{ref:l,className:(0,o.cn)("[&_tr:last-child]:border-0",t),...a})});s.displayName="TableBody",a.forwardRef((e,l)=>{let{className:t,...a}=e;return(0,r.jsx)("tfoot",{ref:l,className:(0,o.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...a})}).displayName="TableFooter";let n=a.forwardRef((e,l)=>{let{className:t,...a}=e;return(0,r.jsx)("tr",{ref:l,className:(0,o.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...a})});n.displayName="TableRow";let g=a.forwardRef((e,l)=>{let{className:t,...a}=e;return(0,r.jsx)("th",{ref:l,className:(0,o.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...a})});g.displayName="TableHead";let c=a.forwardRef((e,l)=>{let{className:t,...a}=e;return(0,r.jsx)("td",{ref:l,className:(0,o.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...a})});c.displayName="TableCell",a.forwardRef((e,l)=>{let{className:t,...a}=e;return(0,r.jsx)("caption",{ref:l,className:(0,o.cn)("mt-4 text-sm text-muted-foreground",t),...a})}).displayName="TableCaption"}}]);