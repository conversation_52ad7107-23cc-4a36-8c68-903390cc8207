"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[380],{5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5845:(e,t,r)=>{r.d(t,{i:()=>i});var n=r(12115),o=r(39033);function i({prop:e,defaultProp:t,onChange:r=()=>{}}){let[i,l]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[i]=r,l=n.useRef(i),a=(0,o.c)(t);return n.useEffect(()=>{l.current!==i&&(a(i),l.current=i)},[i,l,a]),r}({defaultProp:t,onChange:r}),a=void 0!==e,u=a?e:i,s=(0,o.c)(r);return[u,n.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&s(r)}else l(t)},[a,e,l,s])]}},9428:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},11275:(e,t,r)=>{r.d(t,{X:()=>i});var n=r(12115),o=r(52712);function i(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},28905:(e,t,r)=>{r.d(t,{C:()=>l});var n=r(12115),o=r(6101),i=r(52712),l=e=>{let{present:t,children:r}=e,l=function(e){var t,r;let[o,l]=n.useState(),u=n.useRef({}),s=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=a(u.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,r=s.current;if(r!==e){let n=c.current,o=a(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let t;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=a(u.current).includes(e.animationName);if(e.target===o&&n&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=a(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{e&&(u.current=getComputedStyle(e)),l(e)},[])}}(t),u="function"==typeof r?r({present:l.isPresent}):n.Children.only(r),s=(0,o.s)(l.ref,function(e){var t,r;let n=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof r||l.isPresent?n.cloneElement(u,{ref:s}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},35695:(e,t,r)=>{var n=r(18999);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},39033:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(12115);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},45503:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(12115);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},46081:(e,t,r)=>{r.d(t,{A:()=>l,q:()=>i});var n=r(12115),o=r(95155);function i(e,t){let r=n.createContext(t),i=e=>{let{children:t,...i}=e,l=n.useMemo(()=>i,Object.values(i));return(0,o.jsx)(r.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=n.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,i){let l=n.createContext(i),a=r.length;r=[...r,i];let u=t=>{let{scope:r,children:i,...u}=t,s=r?.[e]?.[a]||l,c=n.useMemo(()=>u,Object.values(u));return(0,o.jsx)(s.Provider,{value:c,children:i})};return u.displayName=t+"Provider",[u,function(r,o){let u=o?.[e]?.[a]||l,s=n.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}},52712:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(12115),o=globalThis?.document?n.useLayoutEffect:()=>{}},54059:(e,t,r)=>{r.d(t,{C1:()=>F,bL:()=>I,q7:()=>U});var n=r(12115),o=r(85185),i=r(6101),l=r(46081),a=r(63655),u=r(89196),s=r(5845),c=r(94315),d=r(11275),f=r(45503),p=r(28905),v=r(95155),m="Radio",[h,w]=(0,l.A)(m),[y,g]=h(m),b=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:l,checked:u=!1,required:s,disabled:c,value:d="on",onCheck:f,form:p,...m}=e,[h,w]=n.useState(null),g=(0,i.s)(t,e=>w(e)),b=n.useRef(!1),E=!h||p||!!h.closest("form");return(0,v.jsxs)(y,{scope:r,checked:u,disabled:c,children:[(0,v.jsx)(a.sG.button,{type:"button",role:"radio","aria-checked":u,"data-state":S(u),"data-disabled":c?"":void 0,disabled:c,value:d,...m,ref:g,onClick:(0,o.m)(e.onClick,e=>{u||null==f||f(),E&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})}),E&&(0,v.jsx)(R,{control:h,bubbles:!b.current,name:l,value:d,checked:u,required:s,disabled:c,form:p,style:{transform:"translateX(-100%)"}})]})});b.displayName=m;var E="RadioIndicator",x=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...o}=e,i=g(E,r);return(0,v.jsx)(p.C,{present:n||i.checked,children:(0,v.jsx)(a.sG.span,{"data-state":S(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:t})})});x.displayName=E;var R=e=>{let{control:t,checked:r,bubbles:o=!0,...i}=e,l=n.useRef(null),a=(0,f.Z)(r),u=(0,d.X)(t);return n.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(a!==r&&t){let n=new Event("click",{bubbles:o});t.call(e,r),e.dispatchEvent(n)}},[a,r,o]),(0,v.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:r,...i,tabIndex:-1,ref:l,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function S(e){return e?"checked":"unchecked"}var N=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],C="RadioGroup",[T,P]=(0,l.A)(C,[u.RG,w]),j=(0,u.RG)(),A=w(),[L,O]=T(C),D=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:o,value:i,required:l=!1,disabled:d=!1,orientation:f,dir:p,loop:m=!0,onValueChange:h,...w}=e,y=j(r),g=(0,c.jH)(p),[b,E]=(0,s.i)({prop:i,defaultProp:o,onChange:h});return(0,v.jsx)(L,{scope:r,name:n,required:l,disabled:d,value:b,onValueChange:E,children:(0,v.jsx)(u.bL,{asChild:!0,...y,orientation:f,dir:g,loop:m,children:(0,v.jsx)(a.sG.div,{role:"radiogroup","aria-required":l,"aria-orientation":f,"data-disabled":d?"":void 0,dir:g,...w,ref:t})})})});D.displayName=C;var M="RadioGroupItem",_=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:l,...a}=e,s=O(M,r),c=s.disabled||l,d=j(r),f=A(r),p=n.useRef(null),m=(0,i.s)(t,p),h=s.value===a.value,w=n.useRef(!1);return n.useEffect(()=>{let e=e=>{N.includes(e.key)&&(w.current=!0)},t=()=>w.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,v.jsx)(u.q7,{asChild:!0,...d,focusable:!c,active:h,children:(0,v.jsx)(b,{disabled:c,required:s.required,checked:h,...f,...a,name:s.name,ref:m,onCheck:()=>s.onValueChange(a.value),onKeyDown:(0,o.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.m)(a.onFocus,()=>{var e;w.current&&(null===(e=p.current)||void 0===e||e.click())})})})});_.displayName=M;var k=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,o=A(r);return(0,v.jsx)(x,{...o,...n,ref:t})});k.displayName="RadioGroupIndicator";var I=D,U=_,F=k},61285:(e,t,r)=>{r.d(t,{B:()=>u});var n,o=r(12115),i=r(52712),l=(n||(n=r.t(o,2)))["useId".toString()]||(()=>void 0),a=0;function u(e){let[t,r]=o.useState(l());return(0,i.N)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},63655:(e,t,r)=>{r.d(t,{hO:()=>u,sG:()=>a});var n=r(12115),o=r(47650),i=r(99708),l=r(95155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...o}=e,a=n?i.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},76981:(e,t,r)=>{r.d(t,{C1:()=>N,bL:()=>S});var n=r(12115),o=r(6101),i=r(46081),l=r(85185),a=r(5845),u=r(45503),s=r(11275),c=r(28905),d=r(63655),f=r(95155),p="Checkbox",[v,m]=(0,i.A)(p),[h,w]=v(p),y=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:i,checked:u,defaultChecked:s,required:c,disabled:p,value:v="on",onCheckedChange:m,form:w,...y}=e,[g,b]=n.useState(null),S=(0,o.s)(t,e=>b(e)),N=n.useRef(!1),C=!g||w||!!g.closest("form"),[T=!1,P]=(0,a.i)({prop:u,defaultProp:s,onChange:m}),j=n.useRef(T);return n.useEffect(()=>{let e=null==g?void 0:g.form;if(e){let t=()=>P(j.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[g,P]),(0,f.jsxs)(h,{scope:r,state:T,disabled:p,children:[(0,f.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":x(T)?"mixed":T,"aria-required":c,"data-state":R(T),"data-disabled":p?"":void 0,disabled:p,value:v,...y,ref:S,onKeyDown:(0,l.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(e.onClick,e=>{P(e=>!!x(e)||!e),C&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})}),C&&(0,f.jsx)(E,{control:g,bubbles:!N.current,name:i,value:v,checked:T,required:c,disabled:p,form:w,style:{transform:"translateX(-100%)"},defaultChecked:!x(s)&&s})]})});y.displayName=p;var g="CheckboxIndicator",b=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,i=w(g,r);return(0,f.jsx)(c.C,{present:n||x(i.state)||!0===i.state,children:(0,f.jsx)(d.sG.span,{"data-state":R(i.state),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});b.displayName=g;var E=e=>{let{control:t,checked:r,bubbles:o=!0,defaultChecked:i,...l}=e,a=n.useRef(null),c=(0,u.Z)(r),d=(0,s.X)(t);n.useEffect(()=>{let e=a.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==r&&t){let n=new Event("click",{bubbles:o});e.indeterminate=x(r),t.call(e,!x(r)&&r),e.dispatchEvent(n)}},[c,r,o]);let p=n.useRef(!x(r)&&r);return(0,f.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=i?i:p.current,...l,tabIndex:-1,ref:a,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function x(e){return"indeterminate"===e}function R(e){return x(e)?"indeterminate":e?"checked":"unchecked"}var S=y,N=b},80244:(e,t,r)=>{r.d(t,{OK:()=>Z,bL:()=>K,VM:()=>N,lr:()=>k,LM:()=>Y});var n=r(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return n.useCallback(function(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}(...e),e)}r(47650);var l=r(95155),a=Symbol("radix.slottable");function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}var s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{var r,o,l;let a,u;let{children:s,...c}=e,d=i(n.isValidElement(s)?(u=(a=null===(o=Object.getOwnPropertyDescriptor((r=s).props,"ref"))||void 0===o?void 0:o.get)&&"isReactWarning"in a&&a.isReactWarning)?r.ref:(u=(a=null===(l=Object.getOwnPropertyDescriptor(r,"ref"))||void 0===l?void 0:l.get)&&"isReactWarning"in a&&a.isReactWarning)?r.props.ref:r.props.ref||r.ref:void 0,t);if(n.isValidElement(s)){let e=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=i(...t);return o(...t),n}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(c,s.props);return s.type!==n.Fragment&&(e.ref=d),n.cloneElement(s,e)}return n.Children.count(s)>1?n.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),r=n.forwardRef((e,r)=>{let{children:o,...i}=e,a=n.Children.toArray(o),s=a.find(u);if(s){let e=s.props.children,o=a.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,l.jsx)(t,{...i,ref:r,children:o})});return r.displayName="".concat(e,".Slot"),r}(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?r:t,{...i,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),c=globalThis?.document?n.useLayoutEffect:()=>{},d=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,i]=n.useState(),l=n.useRef(null),a=n.useRef(e),u=n.useRef("none"),[s,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=f(l.current);u.current="mounted"===s?e:"none"},[s]),c(()=>{let t=l.current,r=a.current;if(r!==e){let n=u.current,o=f(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):r&&n!==o?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),c(()=>{if(o){var e;let t;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=f(l.current).includes(e.animationName);if(e.target===o&&n&&(d("ANIMATION_END"),!a.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(u.current=f(l.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),l="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),a=i(o.ref,function(e){var t,r;let n=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||o.isPresent?n.cloneElement(l,{ref:a}):null};function f(e){return(null==e?void 0:e.animationName)||"none"}function p(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}d.displayName="Presence";var v=n.createContext(void 0);function m(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var h="ScrollArea",[w,y]=function(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(t,o){let i=n.createContext(o),a=r.length;r=[...r,o];let u=t=>{let{scope:r,children:o,...u}=t,s=r?.[e]?.[a]||i,c=n.useMemo(()=>u,Object.values(u));return(0,l.jsx)(s.Provider,{value:c,children:o})};return u.displayName=t+"Provider",[u,function(r,l){let u=l?.[e]?.[a]||i,s=n.useContext(u);if(s)return s;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(o,...t)]}(h),[g,b]=w(h),E=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:o="hover",dir:a,scrollHideDelay:u=600,...c}=e,[d,f]=n.useState(null),[p,m]=n.useState(null),[h,w]=n.useState(null),[y,b]=n.useState(null),[E,x]=n.useState(null),[R,S]=n.useState(0),[N,C]=n.useState(0),[T,P]=n.useState(!1),[j,A]=n.useState(!1),L=i(t,e=>f(e)),O=function(e){let t=n.useContext(v);return e||t||"ltr"}(a);return(0,l.jsx)(g,{scope:r,type:o,dir:O,scrollHideDelay:u,scrollArea:d,viewport:p,onViewportChange:m,content:h,onContentChange:w,scrollbarX:y,onScrollbarXChange:b,scrollbarXEnabled:T,onScrollbarXEnabledChange:P,scrollbarY:E,onScrollbarYChange:x,scrollbarYEnabled:j,onScrollbarYEnabledChange:A,onCornerWidthChange:S,onCornerHeightChange:C,children:(0,l.jsx)(s.div,{dir:O,...c,ref:L,style:{position:"relative","--radix-scroll-area-corner-width":R+"px","--radix-scroll-area-corner-height":N+"px",...e.style}})})});E.displayName=h;var x="ScrollAreaViewport",R=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:o,nonce:a,...u}=e,c=b(x,r),d=i(t,n.useRef(null),c.onViewportChange);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,l.jsx)(s.div,{"data-radix-scroll-area-viewport":"",...u,ref:d,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,l.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});R.displayName=x;var S="ScrollAreaScrollbar",N=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=b(S,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:u}=i,s="horizontal"===e.orientation;return n.useEffect(()=>(s?a(!0):u(!0),()=>{s?a(!1):u(!1)}),[s,a,u]),"hover"===i.type?(0,l.jsx)(C,{...o,ref:t,forceMount:r}):"scroll"===i.type?(0,l.jsx)(T,{...o,ref:t,forceMount:r}):"auto"===i.type?(0,l.jsx)(P,{...o,ref:t,forceMount:r}):"always"===i.type?(0,l.jsx)(j,{...o,ref:t}):null});N.displayName=S;var C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=b(S,e.__scopeScrollArea),[a,u]=n.useState(!1);return n.useEffect(()=>{let e=i.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),u(!0)},n=()=>{t=window.setTimeout(()=>u(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[i.scrollArea,i.scrollHideDelay]),(0,l.jsx)(d,{present:r||a,children:(0,l.jsx)(P,{"data-state":a?"visible":"hidden",...o,ref:t})})}),T=n.forwardRef((e,t)=>{var r;let{forceMount:o,...i}=e,a=b(S,e.__scopeScrollArea),u="horizontal"===e.orientation,s=B(()=>f("SCROLL_END"),100),[c,f]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},"hidden"));return n.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>f("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,a.scrollHideDelay,f]),n.useEffect(()=>{let e=a.viewport,t=u?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(f("SCROLL"),s()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[a.viewport,u,f,s]),(0,l.jsx)(d,{present:o||"hidden"!==c,children:(0,l.jsx)(j,{"data-state":"hidden"===c?"hidden":"visible",...i,ref:t,onPointerEnter:m(e.onPointerEnter,()=>f("POINTER_ENTER")),onPointerLeave:m(e.onPointerLeave,()=>f("POINTER_LEAVE"))})})}),P=n.forwardRef((e,t)=>{let r=b(S,e.__scopeScrollArea),{forceMount:o,...i}=e,[a,u]=n.useState(!1),s="horizontal"===e.orientation,c=B(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;u(s?e:t)}},10);return q(r.viewport,c),q(r.content,c),(0,l.jsx)(d,{present:o||a,children:(0,l.jsx)(j,{"data-state":a?"visible":"hidden",...i,ref:t})})}),j=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,i=b(S,e.__scopeScrollArea),a=n.useRef(null),u=n.useRef(0),[s,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=V(s.viewport,s.content),f={...o,sizes:s,onSizesChange:c,hasThumb:!!(d>0&&d<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>u.current=0,onThumbPointerDown:e=>u.current=e};function p(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=z(r),i=t||o/2,l=r.scrollbar.paddingStart+i,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-i),u=r.content-r.viewport;return X([l,a],"ltr"===n?[0,u]:[-1*u,0])(e)}(e,u.current,s,t)}return"horizontal"===r?(0,l.jsx)(A,{...f,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=G(i.viewport.scrollLeft,s,i.dir);a.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=p(e,i.dir))}}):"vertical"===r?(0,l.jsx)(L,{...f,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=G(i.viewport.scrollTop,s);a.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=p(e))}}):null}),A=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,u=b(S,e.__scopeScrollArea),[s,c]=n.useState(),d=n.useRef(null),f=i(t,d,u.onScrollbarXChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,l.jsx)(M,{"data-orientation":"horizontal",...a,ref:f,sizes:r,style:{bottom:0,left:"rtl"===u.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===u.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":z(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(u.viewport){let n=u.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&u.viewport&&s&&o({content:u.viewport.scrollWidth,viewport:u.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:H(s.paddingLeft),paddingEnd:H(s.paddingRight)}})}})}),L=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,u=b(S,e.__scopeScrollArea),[s,c]=n.useState(),d=n.useRef(null),f=i(t,d,u.onScrollbarYChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,l.jsx)(M,{"data-orientation":"vertical",...a,ref:f,sizes:r,style:{top:0,right:"ltr"===u.dir?0:void 0,left:"rtl"===u.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":z(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(u.viewport){let n=u.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&u.viewport&&s&&o({content:u.viewport.scrollHeight,viewport:u.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:H(s.paddingTop),paddingEnd:H(s.paddingBottom)}})}})}),[O,D]=w(S),M=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:o,hasThumb:a,onThumbChange:u,onThumbPointerUp:c,onThumbPointerDown:d,onThumbPositionChange:f,onDragScroll:v,onWheelScroll:h,onResize:w,...y}=e,g=b(S,r),[E,x]=n.useState(null),R=i(t,e=>x(e)),N=n.useRef(null),C=n.useRef(""),T=g.viewport,P=o.content-o.viewport,j=p(h),A=p(f),L=B(w,10);function D(e){N.current&&v({x:e.clientX-N.current.left,y:e.clientY-N.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==E?void 0:E.contains(t))&&j(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[T,E,P,j]),n.useEffect(A,[o,A]),q(E,L),q(g.content,L),(0,l.jsx)(O,{scope:r,scrollbar:E,hasThumb:a,onThumbChange:p(u),onThumbPointerUp:p(c),onThumbPositionChange:A,onThumbPointerDown:p(d),children:(0,l.jsx)(s.div,{...y,ref:R,style:{position:"absolute",...y.style},onPointerDown:m(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),N.current=E.getBoundingClientRect(),C.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",g.viewport&&(g.viewport.style.scrollBehavior="auto"),D(e))}),onPointerMove:m(e.onPointerMove,D),onPointerUp:m(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=C.current,g.viewport&&(g.viewport.style.scrollBehavior=""),N.current=null})})})}),_="ScrollAreaThumb",k=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=D(_,e.__scopeScrollArea);return(0,l.jsx)(d,{present:r||o.hasThumb,children:(0,l.jsx)(I,{ref:t,...n})})}),I=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:o,...a}=e,u=b(_,r),c=D(_,r),{onThumbPositionChange:d}=c,f=i(t,e=>c.onThumbChange(e)),p=n.useRef(void 0),v=B(()=>{p.current&&(p.current(),p.current=void 0)},100);return n.useEffect(()=>{let e=u.viewport;if(e){let t=()=>{v(),p.current||(p.current=$(e,d),d())};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[u.viewport,v,d]),(0,l.jsx)(s.div,{"data-state":c.hasThumb?"visible":"hidden",...a,ref:f,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:m(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;c.onThumbPointerDown({x:r,y:n})}),onPointerUp:m(e.onPointerUp,c.onThumbPointerUp)})});k.displayName=_;var U="ScrollAreaCorner",F=n.forwardRef((e,t)=>{let r=b(U,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,l.jsx)(W,{...e,ref:t}):null});F.displayName=U;var W=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...o}=e,i=b(U,r),[a,u]=n.useState(0),[c,d]=n.useState(0),f=!!(a&&c);return q(i.scrollbarX,()=>{var e;let t=(null===(e=i.scrollbarX)||void 0===e?void 0:e.offsetHeight)||0;i.onCornerHeightChange(t),d(t)}),q(i.scrollbarY,()=>{var e;let t=(null===(e=i.scrollbarY)||void 0===e?void 0:e.offsetWidth)||0;i.onCornerWidthChange(t),u(t)}),f?(0,l.jsx)(s.div,{...o,ref:t,style:{width:a,height:c,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function H(e){return e?parseInt(e,10):0}function V(e,t){let r=e/t;return isNaN(r)?0:r}function z(e){let t=V(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function G(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=z(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,l=t.content-t.viewport,a=function(e,[t,r]){return Math.min(r,Math.max(t,e))}(e,"ltr"===r?[0,l]:[-1*l,0]);return X([0,l],[0,i-n])(a)}function X(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var $=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let i={left:e.scrollLeft,top:e.scrollTop},l=r.left!==i.left,a=r.top!==i.top;(l||a)&&t(),r=i,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function B(e,t){let r=p(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function q(e,t){let r=p(t);c(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var K=E,Y=R,Z=F},82284:(e,t,r)=>{r.d(t,{N:()=>u});var n=r(12115),o=r(46081),i=r(6101),l=r(99708),a=r(95155);function u(e){let t=e+"CollectionProvider",[r,u]=(0,o.A)(t),[s,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,o=n.useRef(null),i=n.useRef(new Map).current;return(0,a.jsx)(s,{scope:t,itemMap:i,collectionRef:o,children:r})};d.displayName=t;let f=e+"CollectionSlot",p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=c(f,r),u=(0,i.s)(t,o.collectionRef);return(0,a.jsx)(l.DX,{ref:u,children:n})});p.displayName=f;let v=e+"CollectionItemSlot",m="data-radix-collection-item",h=n.forwardRef((e,t)=>{let{scope:r,children:o,...u}=e,s=n.useRef(null),d=(0,i.s)(t,s),f=c(v,r);return n.useEffect(()=>(f.itemMap.set(s,{ref:s,...u}),()=>void f.itemMap.delete(s))),(0,a.jsx)(l.DX,{[m]:"",ref:d,children:o})});return h.displayName=v,[{Provider:d,Slot:p,ItemSlot:h},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},u]}},85185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},89196:(e,t,r)=>{r.d(t,{RG:()=>E,bL:()=>A,q7:()=>L});var n=r(12115),o=r(85185),i=r(82284),l=r(6101),a=r(46081),u=r(61285),s=r(63655),c=r(39033),d=r(5845),f=r(94315),p=r(95155),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[w,y,g]=(0,i.N)(h),[b,E]=(0,a.A)(h,[g]),[x,R]=b(h),S=n.forwardRef((e,t)=>(0,p.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(N,{...e,ref:t})})}));S.displayName=h;var N=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:a=!1,dir:u,currentTabStopId:h,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:g,onEntryFocus:b,preventScrollOnEntryFocus:E=!1,...R}=e,S=n.useRef(null),N=(0,l.s)(t,S),C=(0,f.jH)(u),[T=null,P]=(0,d.i)({prop:h,defaultProp:w,onChange:g}),[A,L]=n.useState(!1),O=(0,c.c)(b),D=y(r),M=n.useRef(!1),[_,k]=n.useState(0);return n.useEffect(()=>{let e=S.current;if(e)return e.addEventListener(v,O),()=>e.removeEventListener(v,O)},[O]),(0,p.jsx)(x,{scope:r,orientation:i,dir:C,loop:a,currentTabStopId:T,onItemFocus:n.useCallback(e=>P(e),[P]),onItemShiftTab:n.useCallback(()=>L(!0),[]),onFocusableItemAdd:n.useCallback(()=>k(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>k(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:A||0===_?-1:0,"data-orientation":i,...R,ref:N,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{M.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!M.current;if(e.target===e.currentTarget&&t&&!A){let t=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);j([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),E)}}M.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>L(!1))})})}),C="RovingFocusGroupItem",T=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:l=!1,tabStopId:a,...c}=e,d=(0,u.B)(),f=a||d,v=R(C,r),m=v.currentTabStopId===f,h=y(r),{onFocusableItemAdd:g,onFocusableItemRemove:b}=v;return n.useEffect(()=>{if(i)return g(),()=>b()},[i,g,b]),(0,p.jsx)(w.ItemSlot,{scope:r,id:f,focusable:i,active:l,children:(0,p.jsx)(s.sG.span,{tabIndex:m?0:-1,"data-orientation":v.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?v.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return P[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=v.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>j(r))}})})})});T.displayName=C;var P={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function j(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var A=S,L=T},90221:(e,t,r)=>{r.d(t,{u:()=>s});var n=r(62177);let o=(e,t,r)=>{if(e&&"reportValidity"in e){let o=(0,n.Jt)(r,t);e.setCustomValidity(o&&o.message||""),e.reportValidity()}},i=(e,t)=>{for(let r in t.fields){let n=t.fields[r];n&&n.ref&&"reportValidity"in n.ref?o(n.ref,r,e):n&&n.refs&&n.refs.forEach(t=>o(t,r,e))}},l=(e,t)=>{t.shouldUseNativeValidation&&i(e,t);let r={};for(let o in e){let i=(0,n.Jt)(t.fields,o),l=Object.assign(e[o]||{},{ref:i&&i.ref});if(a(t.names||Object.keys(e),o)){let e=Object.assign({},(0,n.Jt)(r,o));(0,n.hZ)(e,"root",l),(0,n.hZ)(r,o,e)}else(0,n.hZ)(r,o,l)}return r},a=(e,t)=>{let r=u(t);return e.some(e=>u(e).match(`^${r}\\.\\d+`))};function u(e){return e.replace(/\]|\[/g,"")}function s(e,t,r){return void 0===r&&(r={}),function(o,a,u){try{return Promise.resolve(function(n,l){try{var a=Promise.resolve(e["sync"===r.mode?"parse":"parseAsync"](o,t)).then(function(e){return u.shouldUseNativeValidation&&i({},u),{errors:{},values:r.raw?Object.assign({},o):e}})}catch(e){return l(e)}return a&&a.then?a.then(void 0,l):a}(0,function(e){if(Array.isArray(null==e?void 0:e.errors))return{values:{},errors:l(function(e,t){for(var r={};e.length;){var o=e[0],i=o.code,l=o.message,a=o.path.join(".");if(!r[a]){if("unionErrors"in o){var u=o.unionErrors[0].errors[0];r[a]={message:u.message,type:u.code}}else r[a]={message:l,type:i}}if("unionErrors"in o&&o.unionErrors.forEach(function(t){return t.errors.forEach(function(t){return e.push(t)})}),t){var s=r[a].types,c=s&&s[o.code];r[a]=(0,n.Gb)(a,t,r,i,c?[].concat(c,o.message):o.message)}e.shift()}return r}(e.errors,!u.shouldUseNativeValidation&&"all"===u.criteriaMode),u)};throw e}))}catch(e){return Promise.reject(e)}}}},94315:(e,t,r)=>{r.d(t,{jH:()=>i});var n=r(12115);r(95155);var o=n.createContext(void 0);function i(e){let t=n.useContext(o);return e||t||"ltr"}}}]);