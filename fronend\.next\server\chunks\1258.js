"use strict";exports.id=1258,exports.ids=[1258],exports.modules={11749:(e,a,s)=>{s.d(a,{p:()=>r});var t=s(60687),l=s(29523),d=s(76242);function r({icon:e,tooltipText:a,tooltipSide:s="top",tooltipAlign:r="center",delayDuration:i=300,variant:n="ghost",size:o="icon",className:c="h-8 w-8 hover:bg-muted",...m}){return(0,t.jsx)(d.Bc,{delayDuration:i,children:(0,t.jsxs)(d.m_,{children:[(0,t.jsx)(d.k$,{asChild:!0,children:(0,t.jsx)(l.$,{variant:n,size:o,className:c,...m,children:(0,t.jsx)(e,{className:"h-4 w-4 text-muted-foreground"})})}),(0,t.jsx)(d.<PERSON>,{side:s,align:r,className:"font-medium text-xs px-3 py-1.5",children:(0,t.jsx)("p",{children:a})})]})})}},23026:(e,a,s)=>{s.d(a,{A:()=>t});let t=(0,s(62688).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},51258:(e,a,s)=>{s.r(a),s.d(a,{default:()=>c});var t=s(60687),l=s(43210),d=s(23026),r=s(93500),i=s(84778),n=s(11749),o=s(76320);let c=function({studentId:e}){let[a]=(0,o.nu)(),[s,c]=(0,l.useState)(!1),m=async()=>{try{await a({studentId:e}),i.l.success("分配学员成功."),c(!1)}catch(e){i.l.error(e.message||"分配学员失败!")}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.p,{icon:d.A,tooltipText:"分配给自己",onClick:()=>c(!0)}),(0,t.jsx)(r.Lt,{open:s,onOpenChange:c,children:(0,t.jsxs)(r.EO,{className:"sm:max-w-[425px]",children:[(0,t.jsxs)(r.wd,{children:[(0,t.jsx)(r.r7,{children:"确认操作"}),(0,t.jsx)(r.$v,{children:"确认将学员分配给自己？分配后该学员将从公海池中移除。"})]}),(0,t.jsxs)(r.ck,{children:[(0,t.jsx)(r.Zr,{className:"h-9",children:"取消"}),(0,t.jsx)(r.Rx,{onClick:m,className:"h-9 bg-primary hover:bg-primary/90",children:"确认"})]})]})})]})}},93500:(e,a,s)=>{s.d(a,{$v:()=>h,EO:()=>x,Lt:()=>n,Rx:()=>u,Zr:()=>j,ck:()=>p,r7:()=>y,tv:()=>o,wd:()=>f});var t=s(60687),l=s(43210),d=s(97895),r=s(4780),i=s(29523);let n=d.bL,o=d.l9,c=d.ZL,m=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)(d.hJ,{className:(0,r.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a,ref:s}));m.displayName=d.hJ.displayName;let x=l.forwardRef(({className:e,...a},s)=>(0,t.jsxs)(c,{children:[(0,t.jsx)(m,{}),(0,t.jsx)(d.UC,{ref:s,className:(0,r.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a})]}));x.displayName=d.UC.displayName;let f=({className:e,...a})=>(0,t.jsx)("div",{className:(0,r.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...a});f.displayName="AlertDialogHeader";let p=({className:e,...a})=>(0,t.jsx)("div",{className:(0,r.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});p.displayName="AlertDialogFooter";let y=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)(d.hE,{ref:s,className:(0,r.cn)("text-lg font-semibold",e),...a}));y.displayName=d.hE.displayName;let h=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)(d.VY,{ref:s,className:(0,r.cn)("text-sm text-muted-foreground",e),...a}));h.displayName=d.VY.displayName;let u=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)(d.rc,{ref:s,className:(0,r.cn)((0,i.r)(),e),...a}));u.displayName=d.rc.displayName;let j=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)(d.ZD,{ref:s,className:(0,r.cn)((0,i.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...a}));j.displayName=d.ZD.displayName}};