(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1769],{11356:(e,t,s)=>{"use strict";s.d(t,{default:()=>F});var a=s(95155),r=s(30285),l=s(12115),n=s(55863),d=s(59434);let i=l.forwardRef((e,t)=>{let{className:s,value:r,...l}=e;return(0,a.jsx)(n.bL,{ref:t,className:(0,d.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...l,children:(0,a.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});i.displayName=n.bL.displayName;var c=s(22346),o=s(73168),u=s(24122),x=s(55028);let m=(0,x.default)(()=>Promise.all([s.e(81),s.e(4546),s.e(2464),s.e(2158)]).then(s.bind(s,12158)),{loadableGenerated:{webpack:()=>[12158]},ssr:!1});function f(e){var t;let{schedules:s}=e,[n,d]=(0,l.useState)(!1);if(!s)return(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:"加载中..."});let x=s.StudentWeeklySchedule.length,f=s.StudentWeeklySchedule.filter(e=>"attendance"===e.status).length,h=s.StudentWeeklySchedule.filter(e=>"leave"===e.status).length,g=s.StudentWeeklySchedule.filter(e=>"absent"===e.status).length,p=s.StudentWeeklySchedule.filter(e=>"unattended"===e.status).length;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-xl font-medium text-gray-900",children:[(0,o.GP)(new Date(null==s?void 0:s.startDate),"yyyy-MM-dd (EEEE)",{locale:u.g})," ",null==s?void 0:s.startTime,"-",null==s?void 0:s.endTime]}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:[s.name||(null===(t=s.classes)||void 0===t?void 0:t.name),"\xb7 ",s.courses.name]})]}),(0,a.jsx)(r.$,{onClick:()=>d(!0),variant:"outline",size:"sm",className:"self-start sm:self-center",children:"编辑"})]}),(0,a.jsx)(c.w,{className:"my-6"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"容纳人数"}),(0,a.jsx)("div",{className:"text-2xl font-medium",children:s.maxStudentCount})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"上课老师"}),(0,a.jsx)("div",{className:"text-2xl font-medium",children:s.teacher.name})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"课程进度"}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(i,{value:s.currentWeeks/s.totalWeeks*100,className:"h-2"})}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[s.currentWeeks,"/",s.totalWeeks]})]})]})]})]}),(0,a.jsx)("div",{className:"border-t px-6 py-4 bg-gray-50 mb-2",children:(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-x-6 gap-y-2 text-sm",children:[(0,a.jsx)("div",{className:"text-gray-500",children:"考勤情况"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"共"})," ",(0,a.jsx)("span",{className:"font-medium",children:x})," ",(0,a.jsx)("span",{className:"text-gray-500",children:"名学员"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"到课"})," ",(0,a.jsx)("span",{className:"font-medium text-blue-600",children:f})," ",(0,a.jsx)("span",{className:"text-gray-500",children:"人"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"请假"})," ",(0,a.jsx)("span",{className:"font-medium text-amber-600",children:h})," ",(0,a.jsx)("span",{className:"text-gray-500",children:"人"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"缺勤"})," ",(0,a.jsx)("span",{className:"font-medium text-red-600",children:g})," ",(0,a.jsx)("span",{className:"text-gray-500",children:"人"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"未考勤"})," ",(0,a.jsx)("span",{className:"font-medium",children:p})," ",(0,a.jsx)("span",{className:"text-gray-500",children:"人"})]})]})}),n&&(0,a.jsx)(m,{open:n,onOpenChange:d,schedules:s})]})}var h=s(9110),g=s(48623),p=s(12318),v=s(75494),y=s(54165),j=s(62523),b=s(47924),N=s(84616),w=s(95728),k=s(88172);let S=(0,x.default)(()=>s.e(8432).then(s.bind(s,48432)),{loadableGenerated:{webpack:()=>[48432]}});function C(e){let{open:t,onOpenChange:s,students:n,handleSave:d}=e,[i,c]=(0,l.useState)(""),[o,u]=(0,l.useState)(""),[x,m]=(0,l.useState)(1),[f,h]=(0,l.useState)(10),[g,p]=(0,l.useState)([]),{data:v,isLoading:C}=(0,w.L5)({page:x,pageSize:f,search:i},{skip:!t}),z=(0,l.useCallback)((0,k.A)(e=>{c(e)},500),[]);(0,l.useEffect)(()=>{t&&(n&&n.length>0?p(n.map(e=>e.student.id)):p([]))},[t,n]),(0,l.useEffect)(()=>{t||(u(""),c(""),m(1))},[t]);let E=e=>n.find(t=>t.student.id===e)?"在班":"未在班",A=e=>{d(e)&&p(t=>[...t,e])};return(0,a.jsx)(y.lG,{open:t,onOpenChange:s,children:(0,a.jsxs)(y.Cf,{className:"sm:max-w-[700px] p-0 overflow-hidden bg-white rounded-md shadow-md antialiased",children:[(0,a.jsx)(y.c7,{className:"px-6 py-2.5 bg-gray-50 border-b",children:(0,a.jsx)(y.L3,{className:"text-sm font-medium text-gray-900",children:"新增学员"})}),(0,a.jsxs)("div",{className:"p-5 space-y-5",children:[(0,a.jsxs)("div",{className:"flex items-center relative",children:[(0,a.jsx)(b.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400"}),(0,a.jsx)(j.p,{placeholder:"搜索学员名称",value:o,onChange:e=>{let t=e.target.value;u(t),z(t)},className:"pl-8 h-9 text-sm rounded-md border-gray-200 focus:ring-1 focus:ring-gray-200 transition-none"})]}),(0,a.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"bg-gray-50 border-b border-gray-100",children:[(0,a.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-600",children:"学员名"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-600",children:"手机号码"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-600",children:"状态"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-600",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-100",children:(null==v?void 0:v.list)&&v.list.length>0?v.list.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50/60 ".concat(g.includes(e.id)?"bg-green-50/40":""),children:[(0,a.jsx)("td",{className:"px-4 py-3 font-medium text-gray-700",children:e.name}),(0,a.jsx)("td",{className:"px-4 py-3 text-gray-600",children:e.phone}),(0,a.jsx)("td",{className:"px-4 py-3",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs ".concat("在班"===E(e.id)?"bg-green-50 text-green-700 ring-1 ring-green-600/10":"bg-yellow-50 text-yellow-700 ring-1 ring-yellow-600/10"),children:E(e.id)})}),(0,a.jsx)("td",{className:"px-4 py-3",children:(0,a.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>A(e.id),title:"添加",children:(0,a.jsx)(N.A,{className:"h-3 w-3"})})})]},e.id)):(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:4,className:"px-4 py-8 text-center text-gray-500 text-sm",children:C?"加载中...":"没有找到匹配的学员"})})})]})}),(null==v?void 0:v.total)&&v.total>=10&&(0,a.jsx)(S,{totalItems:v.total,pageSize:f,currentPage:x,onPageChange:m,onPageSizeChange:h})]})]})})}var z=s(26126),E=s(57001),A=s(27893),P=s(48436),W=s(5196),$=s(14186),_=s(54416),R=s(35695);let D={fixed:{text:"固定学员",class:"bg-blue-50 text-blue-600 border-blue-200"},temporary:{text:"临时学员",class:"bg-purple-50 text-purple-600 border-purple-200"},trial:{text:"试听学员",class:"bg-orange-50 text-orange-600 border-orange-200"},default:{text:"未知",class:"bg-slate-50 text-slate-600 border-slate-200"}},L={attendance:{text:"已考勤",class:"bg-emerald-50 text-emerald-600"},absent:{text:"缺勤",class:"bg-rose-50 text-rose-600"},leave:{text:"请假",class:"bg-amber-50 text-amber-600"},unattended:{text:"未考勤",class:"bg-slate-100 text-slate-600"},default:{text:"未考勤",class:"bg-slate-100 text-slate-600"}},T=e=>{let{studentId:t}=e,s=function(){let[e]=(0,A.eZ)(),{classesScheduleId:t}=(0,R.useParams)();return async(s,a)=>{let r=await e({id:t,data:{studentId:s,status:a}});if(r.error){var l,n,d;let e=(null===(n=r.error.data)||void 0===n?void 0:null===(l=n.error)||void 0===l?void 0:l.message)||(null===(d=r.error.data)||void 0===d?void 0:d.message);P.l.error(e||"更新考勤状态失败.");return}P.l.success("更新考勤状态成功.")}}();return(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(E.p,{icon:W.A,tooltipText:"到课",onClick:()=>s(t,"attendance")}),(0,a.jsx)(E.p,{icon:$.A,tooltipText:"请假",onClick:()=>s(t,"leave")}),(0,a.jsx)(E.p,{icon:_.A,tooltipText:"缺勤",onClick:()=>s(t,"absent")})]})},I=[{header:"姓名",accessorKey:"name",cell:e=>{var t;let{row:s}=e;return(0,a.jsx)("div",{className:"font-medium text-foreground",children:null===(t=s.original.student)||void 0===t?void 0:t.name})}},{header:"手机号码",accessorKey:"phone",cell:e=>{var t;let{row:s}=e;return(0,a.jsx)("div",{className:"text-muted-foreground",children:null===(t=s.original.student)||void 0===t?void 0:t.phone})}},{header:"上课身份",accessorKey:"studentType",cell:e=>{let{row:t}=e,s=D[t.original.studentType]||D.default;return(0,a.jsx)(z.E,{variant:"outline",className:"font-normal text-xs ".concat(s.class),children:s.text})}},{header:"考勤状态",accessorKey:"status",cell:e=>{let{row:t}=e,s=L[t.getValue("status")]||L.default;return(0,a.jsx)(z.E,{variant:"secondary",className:"px-2.5 py-0.5 rounded-md text-xs font-medium ".concat(s.class),children:s.text})}},{header:"操作",accessorKey:"action",cell:e=>{var t,s;let{row:r}=e;return console.log(null===(t=r.original.student)||void 0===t?void 0:t.id),(0,a.jsx)(T,{studentId:null===(s=r.original.student)||void 0===s?void 0:s.id})}}];function O(e){let{studentData:t}=e,{classesScheduleId:s}=(0,R.useParams)(),[n]=(0,A.b)(),[d]=(0,A.OU)(),[i,c]=(0,l.useState)(!1),[o,u]=(0,l.useState)([]),[x,m]=(0,l.useState)("temporary"),f=(0,l.useCallback)(e=>{console.log(e,"选中的行"),u(t=>t.length===e.length&&t.every((t,s)=>{var a;return t.id===(null===(a=e[s])||void 0===a?void 0:a.id)})?t:e)},[]),y=async()=>{let e=o.map(e=>e.student.id);try{await d({scheduleId:s,studentIds:e}),P.l.success("删除学员成功."),u([])}catch(e){P.l.error(e.message||"删除学员失败!")}};return t?(0,a.jsxs)("div",{className:"px-4 pb-4 space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsxs)(r.$,{variant:"outline",disabled:0===o.length,children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"批量考勤"})]}),(0,a.jsxs)(r.$,{variant:"outline",onClick:()=>{c(!0),m("temporary")},children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"新增临时学员"})]}),(0,a.jsxs)(r.$,{variant:"outline",onClick:()=>{c(!0),m("trial")},children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"新增试听学员"})]}),(0,a.jsxs)(r.$,{onClick:y,disabled:0===o.length,className:"shadow-sm hover:shadow transition-all duration-200 rounded-lg flex items-center gap-2 px-5 py-2 font-medium ".concat(o.length>0?"bg-red-500 hover:bg-red-600 text-white":"bg-gray-200 text-gray-500 cursor-not-allowed"),children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"移除学员"})]})]}),(0,a.jsx)(h.b,{columns:I,data:t,selectable:!0,onSelectedRowsChange:f}),i&&(0,a.jsx)(C,{open:i,onOpenChange:c,students:t,handleSave:e=>{try{return n({scheduleId:s,data:{students:{studentId:e,type:x}}}),!0}catch(e){return P.l.error(e.message||"添加学员失败!"),!1}}})]}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:"加载中..."})}function F(){let{classesScheduleId:e}=(0,R.useParams)(),{data:t}=(0,A.rr)(e);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f,{schedules:t}),(0,a.jsx)(O,{studentData:null==t?void 0:t.StudentWeeklySchedule})]})}},22346:(e,t,s)=>{"use strict";s.d(t,{w:()=>d});var a=s(95155),r=s(12115),l=s(87489),n=s(59434);let d=r.forwardRef((e,t)=>{let{className:s,orientation:r="horizontal",decorative:d=!0,...i}=e;return(0,a.jsx)(l.b,{ref:t,decorative:d,orientation:r,className:(0,n.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",s),...i})});d.displayName=l.b.displayName},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var a=s(95155);s(12115);var r=s(74466),l=s(59434);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:s}),t),...r})}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>i});var a=s(95155),r=s(12115),l=s(99708),n=s(74466),d=s(59434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:s,variant:r,size:n,asChild:c=!1,...o}=e,u=c?l.DX:"button";return(0,a.jsx)(u,{className:(0,d.cn)(i({variant:r,size:n,className:s})),ref:t,...o})});c.displayName="Button"},54165:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>m,Es:()=>h,HM:()=>u,L3:()=>g,c7:()=>f,lG:()=>i,rr:()=>p,zM:()=>c});var a=s(95155),r=s(12115),l=s(15452),n=s(54416),d=s(59434);let i=l.bL,c=l.l9,o=l.ZL,u=l.bm,x=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.hJ,{ref:t,className:(0,d.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r})});x.displayName=l.hJ.displayName;let m=r.forwardRef((e,t)=>{let{className:s,children:r,...i}=e;return(0,a.jsxs)(o,{children:[(0,a.jsx)(x,{}),(0,a.jsxs)(l.UC,{ref:t,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...i,children:[r,(0,a.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=l.UC.displayName;let f=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};f.displayName="DialogHeader";let h=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,d.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};h.displayName="DialogFooter";let g=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.hE,{ref:t,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",s),...r})});g.displayName=l.hE.displayName;let p=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.VY,{ref:t,className:(0,d.cn)("text-sm text-muted-foreground",s),...r})});p.displayName=l.VY.displayName},57001:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(95155),r=s(30285),l=s(46102);function n(e){let{icon:t,tooltipText:s,tooltipSide:n="top",tooltipAlign:d="center",delayDuration:i=300,variant:c="ghost",size:o="icon",className:u="h-8 w-8 hover:bg-muted",...x}=e;return(0,a.jsx)(l.Bc,{delayDuration:i,children:(0,a.jsxs)(l.m_,{children:[(0,a.jsx)(l.k$,{asChild:!0,children:(0,a.jsx)(r.$,{variant:c,size:o,className:u,...x,children:(0,a.jsx)(t,{className:"h-4 w-4 text-muted-foreground"})})}),(0,a.jsx)(l.ZI,{side:n,align:d,className:"font-medium text-xs px-3 py-1.5",children:(0,a.jsx)("p",{children:s})})]})})}},59434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var a=s(52596),r=s(39688);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(95155),r=s(12115),l=s(59434);let n=r.forwardRef((e,t)=>{let{className:s,type:r,...n}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...n})});n.displayName="Input"},80245:(e,t,s)=>{Promise.resolve().then(s.bind(s,11356))}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8687,4201,8737,4540,4582,5620,3168,9613,7945,817,9624,9110,6315,7358],()=>t(80245)),_N_E=e.O()}]);