/**
 * Worker startup script
 * This script starts a specific worker based on the command line argument
 */

// Import all workers
import { 
  imageQueue, 
  textQueue, 
  speechQueue, 
  visionQueue 
} from './index.js';

// Get the worker type from command line arguments
const workerType = process.argv[2];

if (!workerType) {
  console.error('Worker type not specified. Usage: node startWorker.js [image|text|speech|vision|all]');
  process.exit(1);
}

console.log(`Starting ${workerType} worker...`);

// Start the specified worker
switch (workerType.toLowerCase()) {
  case 'image':
    console.log('Image generation worker started');
    // The worker is already initialized when imported
    break;
  
  case 'text':
    console.log('Text generation worker started');
    // The worker is already initialized when imported
    break;
  
  case 'speech':
    console.log('Speech processing worker started');
    // The worker is already initialized when imported
    break;
  
  case 'vision':
    console.log('Vision processing worker started');
    // The worker is already initialized when imported
    break;
  
  case 'all':
    console.log('All workers started');
    // All workers are already initialized when imported
    break;
  
  default:
    console.error(`Unknown worker type: ${workerType}`);
    console.error('Available worker types: image, text, speech, vision, all');
    process.exit(1);
}

// Handle process termination
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully...');
  await cleanupWorkers();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully...');
  await cleanupWorkers();
  process.exit(0);
});

// Cleanup function to close workers gracefully
async function cleanupWorkers() {
  try {
    if (workerType === 'image' || workerType === 'all') {
      await imageQueue.close();
      console.log('Image generation worker closed');
    }
    
    if (workerType === 'text' || workerType === 'all') {
      await textQueue.close();
      console.log('Text generation worker closed');
    }
    
    if (workerType === 'speech' || workerType === 'all') {
      await speechQueue.close();
      console.log('Speech processing worker closed');
    }
    
    if (workerType === 'vision' || workerType === 'all') {
      await visionQueue.close();
      console.log('Vision processing worker closed');
    }
    
    console.log('All workers closed successfully');
  } catch (error) {
    console.error('Error closing workers:', error);
  }
}
