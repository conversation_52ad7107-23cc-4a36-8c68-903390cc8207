/**
 * Common configuration for BullMQ workers
 * This file provides shared configuration and utility functions for all workers
 */

import { redisTask, redisConfig } from "../config/redis.js";

/**
 * Default worker options that can be used across all workers
 */
export const defaultWorkerOptions = {
  connection: redisConfig,
  concurrency: 5,
  lockDuration: 300000, // 5 minutes
  stalledInterval: 30000,
  maxStalledCount: 2,
  drainDelay: 5,
  autorun: true,
  metrics: {
    maxDataPoints: 100
  }
};

/**
 * Default queue options that can be used across all queues
 */
export const defaultQueueOptions = {
  connection: redisConfig,
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 1000
    },
    removeOnComplete: {
      age: 3600, // 1 hour
      count: 1000
    },
    removeOnFail: {
      age: 3600 * 24 * 7 // 7 days
    },
    lockDuration: 300000, // 5 minutes
    timeout: 300000, // 5 minutes
  },
  streams: {
    events: {
      maxLen: 10000
    }
  }
};

/**
 * Helper function to update task status in Redis
 * @param {string} taskId - The ID of the task
 * @param {object} data - The data to store
 * @param {number} expiration - Expiration time in seconds
 */
export const updateTaskStatus = async (taskId, data, expiration = 3600) => {
  await redisTask.set(
    `task:${taskId}`,
    JSON.stringify(data),
    'EX',
    expiration
  );
};

/**
 * Helper function to mark task as completed
 * @param {string} taskId - The ID of the task
 * @param {object} result - The result data
 * @param {string} type - The type of task
 * @param {number} processingTime - The time taken to process the task
 */
export const markTaskCompleted = async (taskId, result, type, processingTime) => {
  await redisTask.multi()
    .set(
      `task:${taskId}`,
      JSON.stringify({
        status: 'completed',
        taskId,
        result,
        processingTime,
        type
      }),
      'EX',
      3600
    )
    .set(
      `task:${taskId}:end`,
      Date.now().toString(),
      'EX',
      3600
    )
    .exec();
};

/**
 * Helper function to mark task as failed
 * @param {string} taskId - The ID of the task
 * @param {Error} error - The error that occurred
 * @param {string} type - The type of task
 * @param {number} processingTime - The time taken before failure
 */
export const markTaskFailed = async (taskId, error, type, processingTime) => {
  await redisTask.set(
    `task:${taskId}`,
    JSON.stringify({
      status: 'failed',
      taskId,
      error: error.message,
      stack: error.stack,
      processingTime,
      type
    }),
    'EX',
    3600
  );
};

/**
 * Setup progress reporting for a task
 * @param {string} taskId - The ID of the task
 * @param {number} interval - The interval in ms to report progress
 * @returns {object} - An object with the interval and a function to update progress
 */
export const setupProgressReporting = (taskId, interval = 2000) => {
  const updateProgress = async (progress) => {
    await redisTask.set(
      `task:${taskId}:progress`,
      JSON.stringify({ progress }),
      'EX',
      3600
    );
  };

  const progressInterval = setInterval(() => {
    updateProgress(Math.floor(Math.random() * 10) + 50);
  }, interval);

  return {
    progressInterval,
    updateProgress
  };
};
