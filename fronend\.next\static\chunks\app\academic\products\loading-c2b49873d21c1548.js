(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1424],{35318:(s,e,a)=>{Promise.resolve().then(a.bind(a,39650))},39650:(s,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>c});var l=a(95155),r=a(68856);function c(){return(0,l.jsx)("div",{className:"p-4 space-y-4",children:(0,l.jsxs)("div",{className:"space-y-6 rounded-lg bg-white p-5 shadow-sm transition-all",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-4 border-b border-gray-100",children:[(0,l.jsx)(r.E,{className:"h-8 w-48"}),(0,l.jsx)(r.E,{className:"h-10 w-28"})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:[(0,l.jsxs)("div",{className:"flex flex-wrap items-center gap-3 w-full sm:w-auto",children:[(0,l.jsx)(r.E,{className:"h-10 w-[220px]"}),(0,l.jsx)(r.E,{className:"h-10 w-[180px]"}),(0,l.jsx)(r.E,{className:"h-10 w-24"})]}),(0,l.jsx)(r.E,{className:"h-10 w-10"})]}),(0,l.jsxs)("div",{className:"border rounded-md",children:[(0,l.jsx)("div",{className:"flex border-b p-2 bg-muted/30",children:[,,,,,].fill(0).map((s,e)=>(0,l.jsx)(r.E,{className:"h-6 flex-1 mx-2"},e))}),Array(8).fill(0).map((s,e)=>(0,l.jsx)("div",{className:"flex border-b p-3",children:[,,,,,].fill(0).map((s,e)=>(0,l.jsx)(r.E,{className:"h-5 flex-1 mx-2"},e))},e))]}),(0,l.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,l.jsx)(r.E,{className:"h-8 w-40"}),(0,l.jsx)(r.E,{className:"h-8 w-64"})]})]})})}},59434:(s,e,a)=>{"use strict";a.d(e,{cn:()=>c});var l=a(52596),r=a(39688);function c(){for(var s=arguments.length,e=Array(s),a=0;a<s;a++)e[a]=arguments[a];return(0,r.QP)((0,l.$)(e))}},68856:(s,e,a)=>{"use strict";a.d(e,{E:()=>c});var l=a(95155),r=a(59434);function c(s){let{className:e,...a}=s;return(0,l.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",e),...a})}}},s=>{var e=e=>s(s.s=e);s.O(0,[4277,6315,7358],()=>e(35318)),_N_E=s.O()}]);