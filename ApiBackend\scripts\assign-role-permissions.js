import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

/**
 * Assign permissions to a role
 * This script assigns permissions to a specific role
 * 
 * Usage:
 * node scripts/assign-role-permissions.js <roleCode> <permissionCode1,permissionCode2,...>
 * 
 * Example:
 * node scripts/assign-role-permissions.js teacher course:view,class:view,student:view
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.error('Usage: node scripts/assign-role-permissions.js <roleCode> <permissionCode1,permissionCode2,...>');
    process.exit(1);
  }
  
  const roleCode = args[0];
  const permissionCodes = args[1].split(',');
  
  console.log(`Assigning permissions to role: ${roleCode}`);
  console.log(`Permissions to assign: ${permissionCodes.join(', ')}`);
  
  // Get the role
  const role = await prisma.role.findUnique({
    where: { code: roleCode }
  });
  
  if (!role) {
    console.error(`Role with code ${roleCode} not found`);
    process.exit(1);
  }
  
  // Get the permissions
  const permissions = await prisma.permission.findMany({
    where: {
      code: {
        in: permissionCodes
      }
    }
  });
  
  const foundPermissionCodes = permissions.map(p => p.code);
  const missingPermissionCodes = permissionCodes.filter(code => !foundPermissionCodes.includes(code));
  
  if (missingPermissionCodes.length > 0) {
    console.warn(`Warning: The following permissions were not found: ${missingPermissionCodes.join(', ')}`);
  }
  
  console.log(`Found ${permissions.length} permissions to assign`);
  
  // Check for existing role permissions
  const existingRolePermissions = await prisma.rolePermission.findMany({
    where: {
      roleId: role.id,
      permissionId: {
        in: permissions.map(p => p.id)
      }
    }
  });
  
  const existingPermissionIds = existingRolePermissions.map(rp => rp.permissionId);
  const permissionsToAssign = permissions.filter(p => !existingPermissionIds.includes(p.id));
  
  console.log(`${existingRolePermissions.length} permissions already assigned`);
  console.log(`${permissionsToAssign.length} new permissions to assign`);
  
  // Create role permissions
  const createdRolePermissions = [];
  
  for (const permission of permissionsToAssign) {
    try {
      const rolePermission = await prisma.rolePermission.create({
        data: {
          id: uuidv4(),
          roleId: role.id,
          permissionId: permission.id
        }
      });
      
      createdRolePermissions.push(rolePermission);
      console.log(`Assigned permission: ${permission.code}`);
    } catch (error) {
      console.error(`Error assigning permission ${permission.code}: ${error.message}`);
    }
  }
  
  console.log(`Successfully assigned ${createdRolePermissions.length} new permissions to role ${roleCode}`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
