import { INTERNAL_ERROR, NOT_FOUND_ERROR, VALIDATION_ERROR } from '../errors/index.js';
import { TwitterSnowflake } from "@sapphire/snowflake";
import { formatCourseData } from '../scripts/coursesUtils.js';

/**
 * Courses Service
 * Contains business logic for course-related operations
 */
export const coursesService = {
    /**
     * Get courses list
     * @param {Object} params - Parameters
     * @param {number} params.page - Page number
     * @param {number} params.pageSize - Page size
     * @param {string} params.search - Search term
     * @param {string} params.type - Course type
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} Courses list and total count
     */
    async getCoursesList({ page = 1, pageSize = 10, search, type, institutionId, fastify }) {
        try {
            // Convert page and pageSize to numbers
            const pageNum = Number(page);
            const pageSizeNum = Number(pageSize);
            const skip = (pageNum - 1) * pageSizeNum;
            const take = pageSizeNum;
            
            // Build where condition
            const where = {
                name: {
                    contains: search || '',
                    mode: 'insensitive'
                },
                ...(type ? { type } : {}),
                institutionId
            };
            
            // Get Prisma client
            const client = fastify.prisma;
            
            // Execute queries in parallel
            const [courses, total] = await Promise.all([
                client.course.findMany({
                    where,
                    select: {
                        id: true,
                        name: true,
                        type: true,
                        duration: true,
                        teacherId: true,
                        isDirectSale: true,
                        deductionPerClass: true,
                        status: true,
                        picture: true,
                        isShow: true,
                        description: true,
                        price: true,
                        isDeductOnAttendance: true,
                        isDeductOnLeave: true,
                        isDeductOnAbsence: true,
                        ProductCourse: {
                            select: {
                                productId: true,
                                product: {
                                    select: {
                                        id: true,
                                        name: true,
                                    }
                                }
                            }
                        }
                    },
                    skip,
                    take,
                    orderBy: {
                        createdAt: 'desc'
                    }
                }),
                client.course.count({
                    where
                })
            ]);
            
            // Format course data
            const formattedCourses = courses.map(course => formatCourseData(course));
            
            // Return result
            return {
                list: formattedCourses,
                total,
                page: pageNum,
                pageSize: pageSizeNum
            };
        } catch (error) {
            throw new INTERNAL_ERROR(`获取课程列表失败: ${error.message}`);
        }
    },
    
    /**
     * Get courses select list
     * @param {Object} params - Parameters
     * @param {boolean} params.id - Include ID
     * @param {boolean} params.name - Include name
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Array>} Courses select list
     */
    async getCoursesSelectList({ id = true, name = true, institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Get courses
            const courses = await client.course.findMany({
                where: {
                    institutionId
                },
                select: {
                    id,
                    name
                },
                orderBy: {
                    name: 'asc'
                }
            });
            
            return courses;
        } catch (error) {
            throw new INTERNAL_ERROR(`获取课程选择列表失败: ${error.message}`);
        }
    },
    
    /**
     * Create course
     * @param {Object} params - Parameters
     * @param {Object} params.courseData - Course data
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} Created course
     */
    async createCourse({ courseData, institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Extract course data
            const {
                name, type = 'group', picture = '', isShow = true, duration = 60,
                description, isDirectSale = false, price = 0, deductionPerClass = 1,
                isDeductOnAttendance = true, isDeductOnLeave = false, isDeductOnAbsence = false
            } = courseData;
            
            // Generate course ID
            const id = TwitterSnowflake.generate().toString();
            
            // Create course
            const course = await client.course.create({
                data: {
                    id,
                    name,
                    type,
                    picture,
                    isShow,
                    duration,
                    description,
                    isDirectSale,
                    price,
                    deductionPerClass,
                    isDeductOnAttendance,
                    isDeductOnLeave,
                    isDeductOnAbsence,
                    institutionId,
                    status: 'active'
                }
            });
            
            // Format and return course
            return formatCourseData(course);
        } catch (error) {
            throw new INTERNAL_ERROR(`创建课程失败: ${error.message}`);
        }
    },
    
    /**
     * Update course
     * @param {Object} params - Parameters
     * @param {string} params.courseId - Course ID
     * @param {Object} params.courseData - Course data
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} Updated course
     */
    async updateCourse({ courseId, courseData, institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Check if course exists
            const existingCourse = await client.course.findFirst({
                where: {
                    id: courseId,
                    institutionId
                }
            });
            
            if (!existingCourse) {
                throw new NOT_FOUND_ERROR('课程不存在');
            }
            
            // Extract course data
            const {
                name, type, status, picture, isShow, duration, description,
                isDirectSale, price, deductionPerClass, isDeductOnAttendance,
                isDeductOnLeave, isDeductOnAbsence
            } = courseData;
            
            // Update course
            const updatedCourse = await client.course.update({
                where: {
                    id: courseId
                },
                data: {
                    ...(name !== undefined && { name }),
                    ...(type !== undefined && { type }),
                    ...(status !== undefined && { status }),
                    ...(picture !== undefined && { picture }),
                    ...(isShow !== undefined && { isShow }),
                    ...(duration !== undefined && { duration }),
                    ...(description !== undefined && { description }),
                    ...(isDirectSale !== undefined && { isDirectSale }),
                    ...(price !== undefined && { price }),
                    ...(deductionPerClass !== undefined && { deductionPerClass }),
                    ...(isDeductOnAttendance !== undefined && { isDeductOnAttendance }),
                    ...(isDeductOnLeave !== undefined && { isDeductOnLeave }),
                    ...(isDeductOnAbsence !== undefined && { isDeductOnAbsence })
                }
            });
            
            // Format and return updated course
            return formatCourseData(updatedCourse);
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`更新课程失败: ${error.message}`);
        }
    },
    
    /**
     * Delete course
     * @param {Object} params - Parameters
     * @param {string} params.courseId - Course ID
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<void>}
     */
    async deleteCourse({ courseId, institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Check if course exists
            const existingCourse = await client.course.findFirst({
                where: {
                    id: courseId,
                    institutionId
                }
            });
            
            if (!existingCourse) {
                throw new NOT_FOUND_ERROR('课程不存在');
            }
            
            // Check if course is used in classes
            const classesCount = await client.class.count({
                where: {
                    courseId,
                    institutionId
                }
            });
            
            if (classesCount > 0) {
                throw new VALIDATION_ERROR('课程已被班级使用，无法删除');
            }
            
            // Delete course-product associations
            await client.productCourse.deleteMany({
                where: {
                    courseId
                }
            });
            
            // Delete course
            await client.course.delete({
                where: {
                    id: courseId
                }
            });
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR || error instanceof VALIDATION_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`删除课程失败: ${error.message}`);
        }
    },
    
    /**
     * Get course by ID
     * @param {Object} params - Parameters
     * @param {string} params.courseId - Course ID
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} Course details
     */
    async getCourseById({ courseId, institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Get course
            const course = await client.course.findFirst({
                where: {
                    id: courseId,
                    institutionId
                },
                include: {
                    ProductCourse: {
                        select: {
                            productId: true,
                            product: {
                                select: {
                                    id: true,
                                    name: true,
                                    price: true
                                }
                            }
                        }
                    }
                }
            });
            
            // Check if course exists
            if (!course) {
                throw new NOT_FOUND_ERROR('课程不存在');
            }
            
            // Format and return course
            return formatCourseData(course);
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`获取课程详情失败: ${error.message}`);
        }
    },
    
    /**
     * Associate course with product
     * @param {Object} params - Parameters
     * @param {string} params.courseId - Course ID
     * @param {string} params.productId - Product ID
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<void>}
     */
    async associateCourseWithProduct({ courseId, productId, institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Check if course exists
            const course = await client.course.findFirst({
                where: {
                    id: courseId,
                    institutionId
                }
            });
            
            if (!course) {
                throw new NOT_FOUND_ERROR('课程不存在');
            }
            
            // Check if product exists
            const product = await client.product.findFirst({
                where: {
                    id: productId,
                    institutionId
                }
            });
            
            if (!product) {
                throw new NOT_FOUND_ERROR('产品不存在');
            }
            
            // Check if association already exists
            const existingAssociation = await client.productCourse.findFirst({
                where: {
                    courseId,
                    productId
                }
            });
            
            if (existingAssociation) {
                return; // Association already exists, no need to create it again
            }
            
            // Create association
            await client.productCourse.create({
                data: {
                    courseId,
                    productId
                }
            });
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`关联课程与产品失败: ${error.message}`);
        }
    },
    
    /**
     * Dissociate course from product
     * @param {Object} params - Parameters
     * @param {string} params.courseId - Course ID
     * @param {string} params.productId - Product ID
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<void>}
     */
    async dissociateCourseFromProduct({ courseId, productId, institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Check if course exists
            const course = await client.course.findFirst({
                where: {
                    id: courseId,
                    institutionId
                }
            });
            
            if (!course) {
                throw new NOT_FOUND_ERROR('课程不存在');
            }
            
            // Check if product exists
            const product = await client.product.findFirst({
                where: {
                    id: productId,
                    institutionId
                }
            });
            
            if (!product) {
                throw new NOT_FOUND_ERROR('产品不存在');
            }
            
            // Delete association
            await client.productCourse.deleteMany({
                where: {
                    courseId,
                    productId
                }
            });
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`解除课程与产品关联失败: ${error.message}`);
        }
    }
};

export default coursesService;
