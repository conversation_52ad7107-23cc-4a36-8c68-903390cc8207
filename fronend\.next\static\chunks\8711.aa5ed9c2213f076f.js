"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8711],{47924:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},54165:(e,t,a)=>{a.d(t,{Cf:()=>m,Es:()=>x,HM:()=>u,L3:()=>g,c7:()=>p,lG:()=>o,rr:()=>h,zM:()=>d});var n=a(95155),s=a(12115),l=a(15452),r=a(54416),i=a(59434);let o=l.bL,d=l.l9,c=l.ZL,u=l.bm,f=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)(l.hJ,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...s})});f.displayName=l.hJ.displayName;let m=s.forwardRef((e,t)=>{let{className:a,children:s,...o}=e;return(0,n.jsxs)(c,{children:[(0,n.jsx)(f,{}),(0,n.jsxs)(l.UC,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...o,children:[s,(0,n.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(r.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=l.UC.displayName;let p=e=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};p.displayName="DialogHeader";let x=e=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};x.displayName="DialogFooter";let g=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)(l.hE,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",a),...s})});g.displayName=l.hE.displayName;let h=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)(l.VY,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...s})});h.displayName=l.VY.displayName},54416:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62523:(e,t,a)=>{a.d(t,{p:()=>r});var n=a(95155),s=a(12115),l=a(59434);let r=s.forwardRef((e,t)=>{let{className:a,type:s,...r}=e;return(0,n.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...r})});r.displayName="Input"},84616:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},88172:(e,t,a)=>{a.d(t,{A:()=>z});let n=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};var s="object"==typeof global&&global&&global.Object===Object&&global,l="object"==typeof self&&self&&self.Object===Object&&self,r=s||l||Function("return this")();let i=function(){return r.Date.now()};var o=/\s/;let d=function(e){for(var t=e.length;t--&&o.test(e.charAt(t)););return t};var c=/^\s+/,u=r.Symbol,f=Object.prototype,m=f.hasOwnProperty,p=f.toString,x=u?u.toStringTag:void 0;let g=function(e){var t=m.call(e,x),a=e[x];try{e[x]=void 0;var n=!0}catch(e){}var s=p.call(e);return n&&(t?e[x]=a:delete e[x]),s};var h=Object.prototype.toString,y=u?u.toStringTag:void 0;let b=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":y&&y in Object(e)?g(e):h.call(e)},v=function(e){return"symbol"==typeof e||null!=e&&"object"==typeof e&&"[object Symbol]"==b(e)};var j=0/0,N=/^[-+]0x[0-9a-f]+$/i,w=/^0b[01]+$/i,k=/^0o[0-7]+$/i,S=parseInt;let C=function(e){if("number"==typeof e)return e;if(v(e))return j;if(n(e)){var t,a="function"==typeof e.valueOf?e.valueOf():e;e=n(a)?a+"":a}if("string"!=typeof e)return 0===e?e:+e;e=(t=e)?t.slice(0,d(t)+1).replace(c,""):t;var s=w.test(e);return s||k.test(e)?S(e.slice(2),s?2:8):N.test(e)?j:+e};var A=Math.max,O=Math.min;let z=function(e,t,a){var s,l,r,o,d,c,u=0,f=!1,m=!1,p=!0;if("function"!=typeof e)throw TypeError("Expected a function");function x(t){var a=s,n=l;return s=l=void 0,u=t,o=e.apply(n,a)}function g(e){var a=e-c,n=e-u;return void 0===c||a>=t||a<0||m&&n>=r}function h(){var e,a,n,s=i();if(g(s))return y(s);d=setTimeout(h,(e=s-c,a=s-u,n=t-e,m?O(n,r-a):n))}function y(e){return(d=void 0,p&&s)?x(e):(s=l=void 0,o)}function b(){var e,a=i(),n=g(a);if(s=arguments,l=this,c=a,n){if(void 0===d)return u=e=c,d=setTimeout(h,t),f?x(e):o;if(m)return clearTimeout(d),d=setTimeout(h,t),x(c)}return void 0===d&&(d=setTimeout(h,t)),o}return t=C(t)||0,n(a)&&(f=!!a.leading,r=(m="maxWait"in a)?A(C(a.maxWait)||0,t):r,p="trailing"in a?!!a.trailing:p),b.cancel=function(){void 0!==d&&clearTimeout(d),u=0,s=c=l=d=void 0},b.flush=function(){return void 0===d?o:y(i())},b}},98711:(e,t,a)=>{a.r(t),a.d(t,{default:()=>p});var n=a(95155),s=a(12115),l=a(54165),r=a(62523),i=a(30285),o=a(47924),d=a(84616),c=a(88172),u=a(55028),f=a(95728);let m=(0,u.default)(()=>a.e(8432).then(a.bind(a,48432)),{loadableGenerated:{webpack:()=>[48432]}});function p(e){let{open:t,onOpenChange:a,students:u,handleSave:p}=e,[x,g]=(0,s.useState)(""),[h,y]=(0,s.useState)(""),[b,v]=(0,s.useState)(1),[j,N]=(0,s.useState)(10),[w,k]=(0,s.useState)([]),[S,C]=(0,s.useState)([]),A=(0,s.useCallback)((0,c.A)(e=>{g(e),v(1)},500),[]),O=(0,s.useMemo)(()=>({page:b,pageSize:j,search:x}),[b,j,x]),{data:z}=(0,f.L5)(O);(0,s.useEffect)(()=>{t||(y(""),g(""),v(1))},[t]),(0,s.useEffect)(()=>{u&&u.length>0?(k(u.map(e=>e.student.id)),C(u)):(k([]),C([]))},[u]);let E=e=>S.find(t=>t.student.id===e)?"在班":"未在班",T=async e=>{if(await p(e)){k(t=>[...t,e]);let t=null==z?void 0:z.list.find(t=>t.id===e);t&&C(e=>[...e,{student:t}])}};return(0,n.jsx)(l.lG,{open:t,onOpenChange:a,children:(0,n.jsxs)(l.Cf,{className:"sm:max-w-[700px] p-0 overflow-hidden bg-white rounded-md shadow-md antialiased",children:[(0,n.jsx)(l.c7,{className:"px-6 py-2.5 bg-gray-50 border-b",children:(0,n.jsx)(l.L3,{className:"text-sm font-medium text-gray-900",children:"新增学员"})}),(0,n.jsxs)("div",{className:"p-5 space-y-5",children:[(0,n.jsxs)("div",{className:"flex items-center relative",children:[(0,n.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400"}),(0,n.jsx)(r.p,{placeholder:"搜索学员名称",value:h,onChange:e=>{let t=e.target.value;y(t),A(t)},className:"pl-8 h-9 text-sm rounded-md border-gray-200 focus:ring-1 focus:ring-gray-200 transition-none"})]}),(0,n.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,n.jsxs)("table",{className:"w-full text-sm",children:[(0,n.jsx)("thead",{children:(0,n.jsxs)("tr",{className:"bg-gray-50 border-b border-gray-100",children:[(0,n.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-600",children:"学员名"}),(0,n.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-600",children:"手机号码"}),(0,n.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-600",children:"状态"}),(0,n.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-600",children:"操作"})]})}),(0,n.jsx)("tbody",{className:"divide-y divide-gray-100",children:(null==z?void 0:z.list)&&z.list.length>0?z.list.map(e=>(0,n.jsxs)("tr",{className:"hover:bg-gray-50/60 ".concat(w.includes(e.id)?"bg-green-50/40":""),children:[(0,n.jsx)("td",{className:"px-4 py-3 font-medium text-gray-700",children:e.name}),(0,n.jsx)("td",{className:"px-4 py-3 text-gray-600",children:e.phone}),(0,n.jsx)("td",{className:"px-4 py-3",children:(0,n.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs ".concat("在班"===E(e.id)?"bg-green-50 text-green-700 ring-1 ring-green-600/10":"bg-yellow-50 text-yellow-700 ring-1 ring-yellow-600/10"),children:E(e.id)})}),(0,n.jsx)("td",{className:"px-4 py-3",children:(0,n.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>T(e.id),title:"添加",disabled:w.includes(e.id),children:(0,n.jsx)(d.A,{className:"h-3 w-3"})})})]},e.id)):(0,n.jsx)("tr",{children:(0,n.jsx)("td",{colSpan:4,className:"px-4 py-8 text-center text-gray-500 text-sm",children:"没有找到匹配的学员"})})})]})}),(null==z?void 0:z.total)&&z.total>=10&&(0,n.jsx)(m,{totalItems:z.total||0,pageSize:z.pageSize||10,currentPage:z.page||1,onPageChange:v,onPageSizeChange:e=>{N(e),v(1)}})]})]})})}}}]);