import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';
import { AUTH_ERROR, INTERNAL_ERROR } from '../errors/index.js';
import { uploadAvatar } from '../libs/qiniu.js';
import { BASICCONSTANTS } from '../constants/index.js';

/**
 * 上传员工头像
 * @param {Buffer} buffer - 头像文件buffer
 * @returns {Promise<string>} - 返回头像URL
 */
export async function uploadStaffAvatar(buffer) {
  try {
    return await uploadAvatar(buffer);
  } catch (error) {
    console.error('文件上传错误:', error);
    throw new INTERNAL_ERROR('文件上传失败');
  }
}

/**
 * 重置员工密码
 * @param {Object} params - 参数对象
 * @param {string} params.staffId - 员工ID
 * @param {string} params.institutionId - 机构ID
 * @param {Object} params.client - 数据库客户端
 * @returns {Promise<void>}
 */
export async function resetStaffPassword({ staffId, institutionId, client }) {
  try {
    const [userInfo, userInstitution] = await Promise.all([
      client.query(
        `SELECT id, account, name, phone
         FROM users
         WHERE id = $1`,
        [staffId]
      ),
      client.query(
        `SELECT id, "userId", "institutionId"
         FROM "user_institution"
         WHERE "userId" = $1 AND "institutionId" = $2`,
        [staffId, institutionId]
      )
    ]);

    if (userInfo.rows.length === 0) {
      throw new AUTH_ERROR('该员工不存在');
    }
    if (userInstitution.rows.length === 0) {
      throw new AUTH_ERROR('该员工不属于该机构');
    }

    const hashedPassword = await bcrypt.hash(BASICCONSTANTS.RESETPASSWORD, BASICCONSTANTS.PASSWORDHASHSALT);

    await client.query(
      `UPDATE users
       SET password = $1
       WHERE id = $2`,
      [hashedPassword, staffId]
    );
  } catch (error) {
    throw new INTERNAL_ERROR(error.message || '重置密码失败');
  }
}

/**
 * 更新员工状态
 * @param {Object} params - 参数对象
 * @param {string} params.staffId - 员工ID
 * @param {string} params.institutionId - 机构ID
 * @param {Object} params.client - 数据库客户端
 * @returns {Promise<void>}
 */
export async function updateStaffStatus({ staffId, institutionId, client }) {
  try {
    const [userInfo, userInstitution] = await Promise.all([
      client.query(
        `SELECT id, account, name, phone
         FROM users
         WHERE id = $1`,
        [staffId]
      ),
      client.query(
        `SELECT id, status, "userId", "institutionId"
         FROM "user_institution"
         WHERE "userId" = $1 AND "institutionId" = $2`,
        [staffId, institutionId]
      )
    ]);

    if (userInfo.rows.length === 0 || userInstitution.rows.length === 0) {
      throw new AUTH_ERROR('该员工不存在');
    }

    await client.query(
      `UPDATE "user_institution"
       SET status = NOT status
       WHERE id = $1`,
      [userInstitution.rows[0].id]
    );
  } catch (error) {
    throw new INTERNAL_ERROR(error.message || '更新员工状态失败');
  }
}

/**
 * 更新员工信息
 * @param {Object} params - 参数对象
 * @param {string} params.staffId - 员工ID
 * @param {string} params.institutionId - 机构ID
 * @param {Object} params.client - 数据库客户端
 * @param {Object} params.updateData - 更新数据
 * @returns {Promise<void>}
 */
export async function updateStaff({ staffId, institutionId, client, updateData }) {
  const { name, account, roles, avatar, isShow } = updateData;
  
  try {
    // 验证用户是否属于该机构
    const userInstitution = await client.query(
      `SELECT id
       FROM "user_institution"
       WHERE "userId" = $1 AND "institutionId" = $2`,
      [staffId, institutionId]
    );

    if (userInstitution.rows.length === 0) {
      throw new AUTH_ERROR('该员工不存在');
    }

    // 开始事务
    await client.query('BEGIN');

    // 更新用户基本信息
    if (name || account || avatar || isShow !== undefined) {
      const updateFields = [];
      const updateValues = [];
      let paramCount = 1;

      if (name) {
        updateFields.push(`name = $${paramCount}`);
        updateValues.push(name);
        paramCount++;
      }
      if (account) {
        updateFields.push(`account = $${paramCount}`);
        updateValues.push(account);
        paramCount++;
      }
      if (avatar) {
        updateFields.push(`avatar = $${paramCount}`);
        updateValues.push(avatar);
        paramCount++;
      }
      if (isShow !== undefined) {
        updateFields.push(`"isShow" = $${paramCount}`);
        updateValues.push(isShow);
        paramCount++;
      }

      updateValues.push(staffId);
      await client.query(
        `UPDATE users
         SET ${updateFields.join(', ')}
         WHERE id = $${paramCount}`,
        updateValues
      );
    }

    // 更新用户角色
    if (roles && roles.length > 0) {
      // 查询现有角色
      const existingRoles = await client.query(
        `SELECT id, "roleId"
         FROM "user_roles"
         WHERE "userId" = $1`,
        [staffId]
      );
      const existingRoleIds = existingRoles.rows.map(row => row.roleId);
      // 判断现有角色不等于请求角色
      if (existingRoleIds.length !== roles.length || existingRoleIds.some(roleId => !roles.includes(roleId))) {
        // 找出需要删除的角色
        const rolesToDelete = existingRoleIds.filter(roleId => !roles.includes(roleId));

        // 找出需要添加的角色
        const rolesToAdd = roles.filter(roleId => !existingRoleIds.includes(roleId));

        // 删除需要删除的角色
        if (rolesToDelete.length > 0) {
          await client.query(
            `DELETE FROM "user_roles"
             WHERE "userId" = $1 AND "roleId" = ANY($2)`,
            [staffId, rolesToDelete]
          );
        }

        // 插入新角色
        if (rolesToAdd.length > 0) {
          const roleValues = rolesToAdd.map((roleId, index) =>
            `($${index * 2 + 1}, $${index * 2 + 2})`
          ).join(', ');
          const roleParams = rolesToAdd.reduce((acc, roleId, index) => {
            acc.push(uuidv4(), staffId, roleId);
            return acc;
          }, []);

          await client.query(
            `INSERT INTO "user_roles" ("id", "userId", "roleId")
             VALUES ${roleValues}`,
            roleParams
          );
        }
      }
    }

    // 提交事务
    await client.query('COMMIT');
  } catch (error) {
    // 回滚事务
    await client.query('ROLLBACK');
    throw new INTERNAL_ERROR(error.message || '更新员工信息失败');
  }
}

/**
 * 删除员工
 * @param {Object} params - 参数对象
 * @param {string} params.staffId - 员工ID
 * @param {string} params.institutionId - 机构ID
 * @param {Object} params.client - 数据库客户端
 * @returns {Promise<void>}
 */
export async function deleteStaff({ staffId, institutionId, client }) {
  try {
    const userInstitution = await client.query(
      `SELECT id
       FROM "user_institution"
       WHERE "userId" = $1 AND "institutionId" = $2`,
      [staffId, institutionId]
    );

    if (userInstitution.rows.length === 0) {
      throw new AUTH_ERROR('该员工不存在');
    }

    await client.query(
      `DELETE FROM users
       WHERE id = $1`,
      [staffId]
    );
  } catch (error) {
    throw new INTERNAL_ERROR(error.message || '删除员工失败');
  }
}

/**
 * 获取员工信息
 * @param {Object} params - 参数对象
 * @param {string} params.staffId - 员工ID
 * @param {string} params.institutionId - 机构ID
 * @param {Object} params.client - 数据库客户端
 * @returns {Promise<Object>} - 返回员工信息
 */
export async function getStaffInfo({ staffId, institutionId, client }) {
  try {
    const userInstitution = await client.query(
      `SELECT id
       FROM "user_institution"
       WHERE "userId" = $1 AND "institutionId" = $2`,
      [staffId, institutionId]
    );

    if (userInstitution.rows.length === 0) {
      throw new AUTH_ERROR('该员工不存在');
    }

    const userInfo = await client.query(
      `SELECT u.id, u.account, u.name, u.avatar, u."isShow",
              json_agg(json_build_object(
                  'id', r.id,
                  'name', r.name
              )) as roles
       FROM users u
       LEFT JOIN "user_roles" ur ON u.id = ur."userId"
       LEFT JOIN roles r ON ur."roleId" = r.id
       WHERE u.id = $1
       GROUP BY u.id, u.account, u.name, u.avatar, u."isShow"`,
      [staffId]
    );

    return userInfo.rows[0];
  } catch (error) {
    throw new INTERNAL_ERROR(error.message || '获取员工信息失败');
  }
}

/**
 * 获取员工列表
 * @param {Object} params - 参数对象
 * @param {string} params.institutionId - 机构ID
 * @param {Object} params.client - 数据库客户端
 * @param {Object} params.query - 查询参数
 * @returns {Promise<Object>} - 返回员工列表和分页信息
 */
export async function getStaffList({ institutionId, client, query }) {
  const { page, pageSize, search } = query;
  
  try {
    // 构建查询条件
    const conditions = ['ui."institutionId" = $1'];
    const queryParams = [institutionId];
    let paramIndex = 2;

    if (search) {
      conditions.push(`(u.name ILIKE $${paramIndex} OR u.account ILIKE $${paramIndex} OR u.phone ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    const whereClause = conditions.join(' AND ');

    // 构建计数查询
    const countQuery = `
      SELECT COUNT(DISTINCT u.id) as total
      FROM users u
      JOIN "user_institution" ui ON u.id = ui."userId"
      WHERE ${whereClause}
    `;

    // 构建数据查询
    const dataQuery = `
      SELECT 
        u.id,
        u.account,
        u.name,
        u.phone,
        u.avatar,
        u."isShow",
        u."createdAt",
        u."description",
        json_agg(
          json_build_object(
            'role', json_build_object(
              'id', r.id,
              'name', r.name
            )
          )
        ) FILTER (WHERE r.id IS NOT NULL) as "userRoles",
        json_agg(
          json_build_object(
            'status', ui2.status
          )
        ) FILTER (WHERE ui2.id IS NOT NULL) as "userInstitutions"
      FROM 
        users u
      JOIN 
        "user_institution" ui ON u.id = ui."userId"
      LEFT JOIN 
        "user_roles" ur ON u.id = ur."userId"
      LEFT JOIN 
        roles r ON ur."roleId" = r.id
      LEFT JOIN 
        "user_institution" ui2 ON u.id = ui2."userId"
      WHERE 
        ${whereClause}
      GROUP BY 
        u.id, u.account, u.name, u.phone, u.avatar, u."isShow", u."createdAt"
      ORDER BY 
        u."createdAt" DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    console.error(dataQuery,"dataQuery")

    // 执行查询
    const [totalResult, usersResult] = await Promise.all([
      client.query(countQuery, queryParams),
      client.query(dataQuery, [...queryParams, pageSize, (page - 1) * pageSize])
    ]);

    const total = parseInt(totalResult.rows[0].total);
    const users = usersResult.rows.map(user => ({
      ...user,
      userRoles: user.userRoles || [],
      userInstitutions: user.userInstitutions || []
    }));

    return {
      list: users,
      page,
      pageSize,
      total
    };
  } catch (error) {
    throw new INTERNAL_ERROR(error.message || '获取员工列表失败');
  }
}

/**
 * 创建员工
 * @param {Object} params - 参数对象
 * @param {string} params.institutionId - 机构ID
 * @param {Object} params.client - 数据库客户端
 * @param {Object} params.userData - 用户数据
 * @returns {Promise<void>}
 */
export async function createStaff({ institutionId, client, userData }) {
  const { name, account, roleId, phone, avatar, description, password, isShow } = userData;
  
  try {
    // 开始事务
    await client.query('BEGIN');
    
    // 检查账号是否已存在
    const userInfo = await client.query(
      `SELECT id FROM users WHERE account = $1`,
      [account]
    );
    
    if (userInfo.rows.length > 0) {
      throw new AUTH_ERROR("该账号已存在");
    }
    
    // 检查角色是否存在
    const institutionRoles = await client.query(
      `SELECT id FROM roles WHERE "institutionId" = $1`,
      [institutionId]
    );
    
    const roleExists = institutionRoles.rows.some(role => role.id === roleId);
    if (!roleExists) {
      throw new AUTH_ERROR('角色不存在');
    }

    // 设置默认密码
    const hashedPassword = await bcrypt.hash(password || BASICCONSTANTS.RESETPASSWORD, BASICCONSTANTS.PASSWORDHASHSALT);
    const id = uuidv4(); // 生成唯一 ID

    // 创建用户
    const createStaffResult = await client.query(
      `INSERT INTO users (id, account, name, password, phone, avatar, description, "isShow", "updatedAt")
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
       RETURNING id`,
      [id, account, name, hashedPassword, phone, avatar, description, isShow ?? false, new Date()]
    );
    
    const userId = createStaffResult.rows[0].id;
    
    // 创建用户机构关联
    await client.query(
      `INSERT INTO "user_institution" (id, "userId", "institutionId", "updatedAt")
       VALUES ($1, $2, $3, $4)`,
      [uuidv4(), userId, institutionId, new Date()]
    );
    
    // 创建用户角色关联
    await client.query(
      `INSERT INTO "user_roles" (id, "userId", "roleId", "updatedAt")
       VALUES ($1, $2, $3, $4)`,
      [uuidv4(), userId, roleId, new Date()]
    );
    
    // 提交事务
    await client.query('COMMIT');
  } catch (error) {
    // 回滚事务
    await client.query('ROLLBACK');
    throw new INTERNAL_ERROR(error.message || '创建员工失败!');
  }
} 