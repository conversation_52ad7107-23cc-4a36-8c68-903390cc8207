"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5236],{55236:(e,t,a)=>{a.r(t),a.d(t,{default:()=>u});var s=a(95155),l=a(57001),n=a(89917),d=a(12115),i=a(55028),r=a(48436),c=a(13515);let o=(0,i.default)(()=>Promise.all([a.e(5488),a.e(8679)]).then(a.bind(a,88679)),{loadableGenerated:{webpack:()=>[88679]},ssr:!1}),u=function(e){let{staff:t}=e,[a]=(0,c.Wt)(),[i,u]=(0,d.useState)(!1),h=async e=>{try{await a({id:(null==t?void 0:t.id)||"",data:{...e,roleId:e.roles[0]}}),r.l.success("更新成功"),u(!1)}catch(e){r.l.error((null==e?void 0:e.message)||"操作失败")}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.p,{onClick:()=>u(!0),icon:n.A,tooltipText:"编辑员工信息"}),i&&(0,s.jsx)(o,{open:i,onOpenChange:u,staff:t,mode:"edit",onSubmit:h})]})}},57001:(e,t,a)=>{a.d(t,{p:()=>d});var s=a(95155),l=a(30285),n=a(46102);function d(e){let{icon:t,tooltipText:a,tooltipSide:d="top",tooltipAlign:i="center",delayDuration:r=300,variant:c="ghost",size:o="icon",className:u="h-8 w-8 hover:bg-muted",...h}=e;return(0,s.jsx)(n.Bc,{delayDuration:r,children:(0,s.jsxs)(n.m_,{children:[(0,s.jsx)(n.k$,{asChild:!0,children:(0,s.jsx)(l.$,{variant:c,size:o,className:u,...h,children:(0,s.jsx)(t,{className:"h-4 w-4 text-muted-foreground"})})}),(0,s.jsx)(n.ZI,{side:d,align:i,className:"font-medium text-xs px-3 py-1.5",children:(0,s.jsx)("p",{children:a})})]})})}},89917:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])}}]);