"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6278],{12318:(e,n,s)=>{s.d(n,{A:()=>t});let t=(0,s(19946).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},57001:(e,n,s)=>{s.d(n,{p:()=>i});var t=s(95155),l=s(30285),a=s(46102);function i(e){let{icon:n,tooltipText:s,tooltipSide:i="top",tooltipAlign:c="center",delayDuration:r=300,variant:o="ghost",size:d="icon",className:x="h-8 w-8 hover:bg-muted",...h}=e;return(0,t.jsx)(a.Bc,{delayDuration:r,children:(0,t.jsxs)(a.m_,{children:[(0,t.jsx)(a.k$,{asChild:!0,children:(0,t.jsx)(l.$,{variant:o,size:d,className:x,...h,children:(0,t.jsx)(n,{className:"h-4 w-4 text-muted-foreground"})})}),(0,t.jsx)(a.ZI,{side:i,align:c,className:"font-medium text-xs px-3 py-1.5",children:(0,t.jsx)("p",{children:s})})]})})}},86278:(e,n,s)=>{s.r(n),s.d(n,{default:()=>o});var t=s(95155),l=s(57001),a=s(12318),i=s(55028),c=s(12115);let r=(0,i.default)(()=>s.e(7777).then(s.bind(s,37777)).then(e=>e.ClassSelectionDialog),{loadableGenerated:{webpack:()=>[37777]},ssr:!1}),o=function(){let[e,n]=c.useState(!1);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l.p,{icon:a.A,tooltipText:"选班",onClick:()=>n(!0)}),e&&(0,t.jsx)(r,{open:e,onOpenChange:n,title:"选班",onSelectClass:e=>console.log(e)})]})}}}]);