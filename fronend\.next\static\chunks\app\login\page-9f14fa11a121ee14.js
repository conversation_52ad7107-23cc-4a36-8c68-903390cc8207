(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>n,t:()=>o});var s=r(12115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}function n(...e){return s.useCallback(o(...e),e)}},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:c="",children:u,iconNode:d,...f}=e;return(0,s.createElement)("svg",{ref:t,...n,width:a,height:a,stroke:r,strokeWidth:i?24*Number(l)/Number(a):l,className:o("lucide",c),...f},[...d.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),i=(e,t)=>{let r=(0,s.forwardRef)((r,n)=>{let{className:i,...c}=r;return(0,s.createElement)(l,{ref:n,iconNode:t,className:o("lucide-".concat(a(e)),i),...c})});return r.displayName="".concat(e),r}},25731:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(23464),a=r(48436);let o=s.A.create({baseURL:"http://127.0.0.1:3001",timeout:1e4,headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let t=localStorage.getItem("token");return console.log(t," token"),t&&(e.headers=e.headers||{},e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e.data,async e=>{var t;if(e.config,null===(t=e.response)||void 0===t||!t.data)return a.l.error("网络错误，请检查您的网络连接"),Promise.reject(Error("网络错误，请检查您的网络连接"));{let t=e.response.data;if(!1===t.success&&t.error){let{message:e,code:r,status:s}=t.error;switch(s){case 401:a.l.error(e||"登录已过期","请重新登录"),localStorage.removeItem("userInfo"),localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("refreshToken"),setTimeout(()=>{window.location.href="/login"},1500);break;case 403:a.l.error(e||"没有权限访问该资源");break;case 404:a.l.error(e||"请求的资源不存在");break;case 409:a.l.error(e||"资源冲突");break;case 500:a.l.error(e||"服务器错误");break;default:a.l.error(e||"请求失败")}let o=Error(e);return Object.assign(o,t.error),Promise.reject(o)}}});let n=o},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>i});var s=r(95155),a=r(12115),o=r(99708),n=r(74466),l=r(59434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:n,asChild:c=!1,...u}=e,d=c?o.DX:"button";return(0,s.jsx)(d,{className:(0,l.cn)(i({variant:a,size:n,className:r})),ref:t,...u})});c.displayName="Button"},32919:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35695:(e,t,r)=>{"use strict";var s=r(18999);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var s=r(12115),a=r(63655),o=r(95155),n=s.forwardRef((e,t)=>(0,o.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n},48436:(e,t,r)=>{"use strict";r.d(t,{l:()=>a});var s=r(56671);let a={success:(e,t)=>{(0,s.oR)(e,{description:t,icon:"✅"})},error:(e,t)=>{(0,s.oR)(e,{description:t,icon:"❌"})},info:(e,t)=>{(0,s.oR)(e,{description:t,icon:"ℹ️"})},warning:(e,t)=>{(0,s.oR)(e,{description:t,icon:"⚠️"})}}},52991:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n,Hf:()=>o,iA:()=>a});let s=(0,r(5710).Z0)({name:"user",initialState:{id:"",name:"",account:"",avatar:void 0,institutionName:"",institutionLogo:"",institutionSubjectName:"",token:"",refresh_token:"",isAuthenticated:!1},reducers:{setUserInfo:(e,t)=>{e.id=t.payload.id,e.name=t.payload.name,e.account=t.payload.account,e.institutionName=t.payload.institutionName,e.institutionLogo=t.payload.institutionLogo,e.institutionSubjectName=t.payload.institutionSubjectName,e.token=t.payload.token,e.refresh_token=t.payload.refresh_token,e.isAuthenticated=!0},clearUserInfo:e=>{e.id="",e.name="",e.account="",e.institutionName="",e.institutionLogo="",e.institutionSubjectName="",e.token="",e.refresh_token="",e.isAuthenticated=!1}}}),{setUserInfo:a,clearUserInfo:o}=s.actions,n=s.reducer},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(52596),a=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},61325:(e,t,r)=>{Promise.resolve().then(r.bind(r,63444))},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(95155),a=r(12115),o=r(59434);let n=a.forwardRef((e,t)=>{let{className:r,type:a,...n}=e;return(0,s.jsx)("input",{type:a,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...n})});n.displayName="Input"},63444:(e,t,r)=>{"use strict";r.d(t,{default:()=>y});var s=r(95155),a=r(66695),o=r(12115),n=r(35695),l=r(62523),i=r(30285),c=r(85057),u=r(48436),d=r(71007),f=r(32919),m=r(92138),h=r(25731),p=r(34540),g=r(52991);function v(){let e=(0,n.useRouter)(),{login:t}={login:async(e,t)=>{try{let r=await h.A.post("/api/auth/login",{account:e,password:t}),{accessToken:s,refreshToken:a}=r.data;return localStorage.setItem("token",s),localStorage.setItem("refresh_token",a),r.data}catch(e){throw console.error(e),e}},logout:async()=>{try{await h.A.post("/api/auth/logout")}catch(e){console.error("登出API调用失败:",e)}finally{localStorage.removeItem("userInfo"),localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("refreshToken"),localStorage.removeItem("persist:root"),sessionStorage.removeItem("token"),sessionStorage.removeItem("refresh_token"),sessionStorage.removeItem("refreshToken")}}},r=(0,p.wA)(),[a,v]=(0,o.useState)(!1),[x,b]=(0,o.useState)({account:"",password:""}),y=async s=>{if(s.preventDefault(),!a){if(!x.account||!x.password){u.l.warning("请输入用户名和密码");return}v(!0);try{let s=await t(x.account,x.password);if(!s)return;localStorage.setItem("token",s.accessToken),localStorage.setItem("refresh_token",s.refreshToken),r((0,g.iA)({id:s.user.id,name:s.user.name,account:s.user.account,avatar:s.user.avatar,institutionName:s.user.institutionName,institutionLogo:s.user.institutionLogo,institutionSubjectName:s.user.institutionSubjectName,token:s.accessToken,refresh_token:s.refreshToken,isAuthenticated:!0})),u.l.success("登录成功."),e.push("/dashboard")}catch(e){u.l.error("登录失败,请检查用户名和密码!")}finally{v(!1)}}};return(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsxs)("div",{className:"mb-6 md:mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"欢迎回来"}),(0,s.jsx)("p",{className:"text-gray-500 mt-2 text-sm",children:"请登录您的账号以继续"})]}),(0,s.jsxs)("form",{onSubmit:y,className:"space-y-5 flex-1",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"username",className:"text-sm font-medium text-gray-700 mb-1.5 block",children:"用户名"}),(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)(l.p,{id:"username",type:"text",value:x.account,onChange:e=>b({...x,account:e.target.value}),placeholder:"请输入用户名",className:"h-12 pl-4 pr-4 py-3 rounded-lg border-gray-200 focus-visible:ring-1 focus-visible:ring-blue-500    focus-visible:border-blue-500 w-full transition-all"}),(0,s.jsx)("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400",children:(0,s.jsx)(d.A,{size:18})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1.5",children:[(0,s.jsx)(c.J,{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"密码"}),(0,s.jsx)("a",{href:"#",className:"text-xs text-blue-600 hover:text-blue-800 transition-colors",children:"忘记密码?"})]}),(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)(l.p,{id:"password",type:"password",value:x.password,onChange:e=>b({...x,password:e.target.value}),placeholder:"请输入密码",className:"h-12 pl-4 pr-4 py-3 rounded-lg border-gray-200 focus-visible:ring-1 focus-visible:ring-blue-500    focus-visible:border-blue-500 w-full transition-all"}),(0,s.jsx)("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400",children:(0,s.jsx)(f.A,{size:18})})]})]}),(0,s.jsx)("div",{className:"pt-2",children:(0,s.jsxs)(i.$,{type:"submit",disabled:a,className:"w-full h-12 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700    hover:to-indigo-800 text-white font-medium flex items-center justify-center gap-2 transition-all   focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-60",children:[a?"登录中...":"登录",!a&&(0,s.jsx)(m.A,{size:16})]})}),(0,s.jsxs)("div",{className:"mt-auto pt-6 text-center text-xs text-gray-500",children:["通过登录，您同意我们的 ",(0,s.jsx)("a",{href:"#",className:"text-blue-600 hover:underline",children:"服务条款"})," 和",(0,s.jsx)("a",{href:"#",className:"text-blue-600 hover:underline",children:" 隐私政策"})]})]})]})}let x=function(){return(0,s.jsxs)("div",{className:"h-full flex flex-col items-center justify-center py-10 px-6 relative overflow-hidden text-white",children:[(0,s.jsx)("div",{className:"absolute -bottom-16 -left-16 w-64 h-64 rounded-full bg-indigo-500 opacity-20 blur-3xl"}),(0,s.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 rounded-full bg-blue-400 opacity-20 blur-2xl"}),(0,s.jsx)("div",{className:"flex items-center justify-center w-16 h-16 bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl mb-8 relative z-10",children:(0,s.jsx)("span",{className:"text-white text-2xl font-bold",children:"SX"})}),(0,s.jsxs)("div",{className:"text-center space-y-4 relative z-10",children:[(0,s.jsx)("h1",{className:"text-2xl md:text-3xl font-bold leading-tight",children:"思学教育"}),(0,s.jsx)("div",{className:"h-1 w-12 bg-white opacity-50 mx-auto rounded-full"}),(0,s.jsx)("p",{className:"text-sm md:text-base font-light opacity-80 max-w-xs",children:"专业的教育机构管理解决方案"})]}),(0,s.jsx)("div",{className:"mt-10 space-y-3 relative z-10",children:["智能教务管理","高效课程排期","全方位学员服务"].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm opacity-80",children:[(0,s.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-white"}),(0,s.jsx)("span",{children:e})]},t))}),(0,s.jsxs)("div",{className:"absolute bottom-4 text-xs opacity-60 mt-auto",children:["\xa9 ",new Date().getFullYear()," 零点科技"]})]})};var b=r(65436);function y(){let e=(0,n.useRouter)(),t=(0,b.G)(e=>e.user);return(0,o.useEffect)(()=>{let r=localStorage.getItem("token");t.isAuthenticated&&t.token&&r&&e.replace("/dashboard")},[t,e]),(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4",children:(0,s.jsxs)(a.Zp,{className:"w-full max-w-4xl overflow-hidden rounded-xl shadow-lg border-0 flex flex-col md:flex-row",children:[(0,s.jsx)("div",{className:"w-full md:w-5/12 bg-gradient-to-br from-blue-600 to-indigo-800",children:(0,s.jsx)(x,{})}),(0,s.jsx)("div",{className:"w-full md:w-7/12 p-6 sm:p-8 bg-white",children:(0,s.jsx)(v,{})})]})})})}},63655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>i,sG:()=>l});var s=r(12115),a=r(47650),o=r(99708),n=r(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=s.forwardRef((e,r)=>{let{asChild:s,...a}=e,l=s?o.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(l,{...a,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function i(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},65436:(e,t,r)=>{"use strict";r.d(t,{G:()=>o,j:()=>a});var s=r(34540);let a=()=>(0,s.wA)(),o=s.d4},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>u,ZB:()=>i,Zp:()=>n,aR:()=>l,wL:()=>d});var s=r(95155),a=r(12115),o=r(59434);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});n.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...a})});l.displayName="CardHeader";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});i.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...a})});u.displayName="CardContent";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...a})});d.displayName="CardFooter"},71007:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var s=r(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=s.$,n=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:l}=t,i=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],s=null==l?void 0:l[e];if(null===t)return null;let o=a(t)||a(s);return n[e][o]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return o(e,i,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var s=r(95155),a=r(12115),o=r(40968),n=r(74466),l=r(59434);let i=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(o.b,{ref:t,className:(0,l.cn)(i(),r),...a})});c.displayName=o.b.displayName},92138:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>n,xV:()=>i});var s=r(12115),a=r(6101),o=r(95155),n=s.forwardRef((e,t)=>{let{children:r,...a}=e,n=s.Children.toArray(r),i=n.find(c);if(i){let e=i.props.children,r=n.map(t=>t!==i?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,o.jsx)(l,{...a,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,r):null})}return(0,o.jsx)(l,{...a,ref:t,children:r})});n.displayName="Slot";var l=s.forwardRef((e,t)=>{let{children:r,...o}=e;if(s.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),n=function(e,t){let r={...t};for(let s in t){let a=e[s],o=t[s];/^on[A-Z]/.test(s)?a&&o?r[s]=(...e)=>{o(...e),a(...e)}:a&&(r[s]=a):"style"===s?r[s]={...a,...o}:"className"===s&&(r[s]=[a,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==s.Fragment&&(n.ref=t?(0,a.t)(t,e):e),s.cloneElement(r,n)}return s.Children.count(r)>1?s.Children.only(null):null});l.displayName="SlotClone";var i=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});function c(e){return s.isValidElement(e)&&e.type===i}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8687,4540,6315,7358],()=>t(61325)),_N_E=e.O()}]);