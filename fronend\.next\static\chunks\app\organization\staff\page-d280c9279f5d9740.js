(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3121],{10255:(e,t,r)=>{"use strict";function a(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return a}}),r(95155),r(47650),r(85744),r(20589)},17828:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return a}});let a=(0,r(64054).createAsyncLocalStorage)()},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var a=r(95155);r(12115);var n=r(74466),l=r(59434);let s=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...n}=e;return(0,a.jsx)("div",{className:(0,l.cn)(s({variant:r}),t),...n})}},30034:(e,t,r)=>{Promise.resolve().then(r.bind(r,55956))},36645:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let a=r(88229)._(r(67357));function n(e,t){var r;let n={};"function"==typeof e&&(n.loader=e);let l={...n,...t};return(0,a.default)({...l,modules:null==(r=l.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55028:(e,t,r)=>{"use strict";r.d(t,{default:()=>n.a});var a=r(36645),n=r.n(a)},55733:(e,t,r)=>{"use strict";r.d(t,{Q:()=>v});var a=r(95155),n=r(12115),l=r(59434),s=r(26126),i=r(74466);let o=(0,i.F)("",{variants:{variant:{default:"border-b flex -mb-px space-x-6",underline:"relative flex overflow-x-auto",pill:"bg-muted p-1 rounded-lg flex mb-4",vertical:"w-48 shrink-0 border-r pr-4 flex flex-col space-y-1"}},defaultVariants:{variant:"default"}}),u=(0,i.F)("transition-colors",{variants:{variant:{default:"py-2 border-b-2 font-medium text-sm flex items-center gap-2",underline:"py-2 mr-8 font-medium text-sm transition-colors relative flex items-center gap-2",pill:"flex-1 py-1.5 px-3 text-sm font-medium rounded-md flex items-center justify-center gap-2",vertical:"flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md text-left"},state:{active:"",inactive:""}},compoundVariants:[{variant:"default",state:"active",className:"border-primary text-primary"},{variant:"default",state:"inactive",className:"border-transparent text-muted-foreground hover:text-foreground hover:border-border"},{variant:"underline",state:"active",className:"text-primary"},{variant:"underline",state:"inactive",className:"text-muted-foreground hover:text-foreground"},{variant:"pill",state:"active",className:"bg-background text-foreground shadow-sm"},{variant:"pill",state:"inactive",className:"text-muted-foreground hover:text-foreground"},{variant:"vertical",state:"active",className:"bg-accent text-accent-foreground"},{variant:"vertical",state:"inactive",className:"text-muted-foreground hover:bg-muted hover:text-foreground"}],defaultVariants:{variant:"default",state:"inactive"}}),d=(0,i.F)("",{variants:{variant:{default:"py-4",underline:"py-4",pill:"",vertical:"flex-1"}},defaultVariants:{variant:"default"}}),c=n.memo(e=>{let{tab:t,isActive:r,showBadges:n}=e;return r?t.content?(0,a.jsx)(a.Fragment,{children:t.content}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:t.label}),n&&void 0!==t.badge&&(0,a.jsx)(s.E,{variant:"default",children:t.badge})]}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:["这是 ",t.label," 标签页的内容区域。",n&&void 0!==t.badge&&" 您有 ".concat(t.badge," 个未读").concat(t.label,"。")]})]}):null},(e,t)=>!e.isActive&&!t.isActive||e.isActive===t.isActive&&e.tab.id===t.tab.id&&e.showBadges===t.showBadges);c.displayName="TabContent";let f=n.memo(e=>{let{tab:t,isActive:r,variant:n,showIcons:i,showBadges:o,onClick:d}=e;return(0,a.jsxs)("button",{onClick:d,disabled:t.disabled,className:(0,l.cn)(u({variant:n,state:r?"active":"inactive"}),t.disabled&&"opacity-50 cursor-not-allowed"),children:[i&&t.icon,(0,a.jsx)("span",{children:t.label}),o&&void 0!==t.badge&&(0,a.jsx)(s.E,{variant:r?"default":"secondary",className:"ml-1 px-1.5 py-0.5 h-5",children:t.badge}),"underline"===n&&r&&(0,a.jsx)("span",{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-primary"})]})});function v(e){let{tabs:t,defaultTab:r,onChange:s,className:i,variant:u="default",showIcons:v=!0,showBadges:b=!0}=e,m=n.useRef(!1),[p,g]=n.useState(""),x=n.useCallback(e=>{e!==p&&(g(e),null==s||s(e))},[p,s]);n.useEffect(()=>{if(!m.current){var e;m.current=!0,g(r||(null===(e=t[0])||void 0===e?void 0:e.id)||"")}},[r,t]);let y=n.useMemo(()=>t.reduce((e,t)=>(e.set(t.id,t),e),new Map),[t]),h=n.useMemo(()=>new Set(t.map(e=>e.id)),[t]),j=n.useRef(new Map);n.useEffect(()=>{Array.from(j.current.keys()).filter(e=>!h.has(e)).forEach(e=>j.current.delete(e)),t.forEach(e=>{j.current.has(e.id)||j.current.set(e.id,()=>x(e.id))})},[t,h,x]);let N=n.useCallback(e=>j.current.get(e)||(()=>x(e)),[x]),{isVertical:_,showUnderlineBorder:w}=n.useMemo(()=>({isVertical:"vertical"===u,showUnderlineBorder:"underline"===u}),[u]),O=n.useMemo(()=>{let e=y.get(p);return e?(0,a.jsx)(c,{tab:e,isActive:!0,showBadges:b},p):null},[p,y,b]);return n.useEffect(()=>{},[t.length,p]),(0,a.jsxs)("div",{className:(0,l.cn)(_?"flex gap-8":"w-full",i),children:[(0,a.jsxs)("div",{className:o({variant:u}),children:[w&&(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-border"}),t.map(e=>(0,a.jsx)(f,{tab:e,isActive:p===e.id,variant:u,showIcons:v,showBadges:b,onClick:N(e.id)},e.id))]}),(0,a.jsx)("div",{className:d({variant:u}),children:O})]})}f.displayName="TabButton"},55956:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(95155);r(12115);var n=r(55733),l=r(55028);let s=(0,l.default)(()=>Promise.all([r.e(8687),r.e(4201),r.e(8737),r.e(4540),r.e(4582),r.e(5620),r.e(9613),r.e(7945),r.e(5342),r.e(9624),r.e(9110),r.e(5753)]).then(r.bind(r,85753)),{loadableGenerated:{webpack:()=>[85753]},ssr:!1}),i=(0,l.default)(()=>Promise.all([r.e(8687),r.e(4201),r.e(8737),r.e(4540),r.e(4582),r.e(5620),r.e(9613),r.e(7945),r.e(776),r.e(9624),r.e(9110),r.e(1439)]).then(r.bind(r,1439)),{loadableGenerated:{webpack:()=>[1439]},ssr:!1});function o(){let e=[{id:"staff",label:"员工管理",content:(0,a.jsx)(s,{})},{id:"position",label:"岗位管理",content:(0,a.jsx)(i,{})}];return(0,a.jsx)(n.Q,{defaultTab:"staff",tabs:e})}},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var a=r(52596),n=r(39688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}},62146:(e,t,r)=>{"use strict";function a(e){let{reason:t,children:r}=e;return r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return a}}),r(45262)},64054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{bindSnapshot:function(){return s},createAsyncLocalStorage:function(){return l},createSnapshot:function(){return i}});let r=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class a{disable(){throw r}getStore(){}run(){throw r}exit(){throw r}enterWith(){throw r}static bind(e){return e}}let n="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function l(){return n?new n:new a}function s(e){return n?n.bind(e):a.bind(e)}function i(){return n?n.snapshot():function(e,...t){return e(...t)}}},67357:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let a=r(95155),n=r(12115),l=r(62146);function s(e){return{default:e&&"default"in e?e.default:e}}r(10255);let i={loader:()=>Promise.resolve(s(()=>null)),loading:null,ssr:!0},o=function(e){let t={...i,...e},r=(0,n.lazy)(()=>t.loader().then(s)),o=t.loading;function u(e){let s=o?(0,a.jsx)(o,{isLoading:!0,pastDelay:!0,error:null}):null,i=!t.ssr||!!t.loading,u=i?n.Suspense:n.Fragment,d=t.ssr?(0,a.jsxs)(a.Fragment,{children:[null,(0,a.jsx)(r,{...e})]}):(0,a.jsx)(l.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(r,{...e})});return(0,a.jsx)(u,{...i?{fallback:s}:{},children:d})}return u.displayName="LoadableComponent",u}},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});var a=r(52596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=a.$,s=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:i}=t,o=Object.keys(s).map(e=>{let t=null==r?void 0:r[e],a=null==i?void 0:i[e];if(null===t)return null;let l=n(t)||n(a);return s[e][l]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return l(e,o,null==t?void 0:null===(a=t.compoundVariants)||void 0===a?void 0:a.reduce((e,t)=>{let{class:r,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...u}[t]):({...i,...u})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},85744:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return a.workAsyncStorageInstance}});let a=r(17828)}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6315,7358],()=>t(30034)),_N_E=e.O()}]);