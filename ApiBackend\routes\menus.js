import menuController from '../controllers/menuController.js';
export default async function (fastify, opts) {
    // 获取用户菜单
    fastify.get('/menus/user-menus', {
        schema: {
            tags: ['menus'],
            summary: '获取用户菜单',
        },
        onRequest: [fastify.auth.authenticate],
        handler: menuController.getUserMenus
    });
    
    // 获取菜单
    fastify.get('/menus', {
        schema: {
            tags: ['menus'],
            summary: '获取菜单',
        },
        onRequest: [fastify.auth.authenticate],
        handler: menuController.getAllMenus
    });

    // 获取根据角色id获取菜单
    fastify.get('/menus/role-menus/:roleId', {
        schema: {
            tags: ['menus'],
            summary: '获取根据角色id获取菜单',
            params: {
                type: 'object',
                properties: {
                    roleId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: menuController.getRoleMenus
    });
}