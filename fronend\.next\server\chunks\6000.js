"use strict";exports.id=6e3,exports.ids=[6e3],exports.modules={53332:(e,t,r)=>{var l=r(43210),n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=l.useState,i=l.useEffect,s=l.useLayoutEffect,o=l.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!n(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),l=a({inst:{value:r,getSnapshot:t}}),n=l[0].inst,c=l[1];return s(function(){n.value=r,n.getSnapshot=t,u(n)&&c({inst:n})},[e,r,t]),i(function(){return u(n)&&c({inst:n}),e(function(){u(n)&&c({inst:n})})},[e]),o(r),r};t.useSyncExternalStore=void 0!==l.useSyncExternalStore?l.useSyncExternalStore:c},79760:(e,t,r)=>{e.exports=r(53332)},86e3:(e,t,r)=>{r.r(t),r.d(t,{default:()=>ec});var l=r(60687),n=r(63503),a=r(43210);let i=(0,r(62688).A)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]);var s=r(11860),o=r(13964),u=r(4780),c=r(29523),d=/[\\\/_+.#"@\[\(\{&]/,f=/[\\\/_+.#"@\[\(\{&]/g,m=/[\s-]/,p=/[\s-]/g;function h(e){return e.toLowerCase().replace(p," ")}var v=r(26134),g=r(14163),x=r(96963),b=r(79760),y='[cmdk-group=""]',w='[cmdk-group-items=""]',j='[cmdk-item=""]',E=`${j}:not([aria-disabled="true"])`,k="cmdk-item-select",N="data-value",S=(e,t,r)=>(function(e,t,r){return function e(t,r,l,n,a,i,s){if(i===r.length)return a===t.length?1:.99;var o=`${a},${i}`;if(void 0!==s[o])return s[o];for(var u,c,h,v,g=n.charAt(i),x=l.indexOf(g,a),b=0;x>=0;)(u=e(t,r,l,n,x+1,i+1,s))>b&&(x===a?u*=1:d.test(t.charAt(x-1))?(u*=.8,(h=t.slice(a,x-1).match(f))&&a>0&&(u*=Math.pow(.999,h.length))):m.test(t.charAt(x-1))?(u*=.9,(v=t.slice(a,x-1).match(p))&&a>0&&(u*=Math.pow(.999,v.length))):(u*=.17,a>0&&(u*=Math.pow(.999,x-a))),t.charAt(x)!==r.charAt(i)&&(u*=.9999)),(u<.1&&l.charAt(x-1)===n.charAt(i+1)||n.charAt(i+1)===n.charAt(i)&&l.charAt(x-1)!==n.charAt(i))&&.1*(c=e(t,r,l,n,x+1,i+2,s))>u&&(u=.1*c),u>b&&(b=u),x=l.indexOf(g,x+1);return s[o]=b,b}(e=r&&r.length>0?`${e+" "+r.join(" ")}`:e,t,h(e),h(t),0,0,{})})(e,t,r),C=a.createContext(void 0),A=()=>a.useContext(C),R=a.createContext(void 0),I=()=>a.useContext(R),M=a.createContext(void 0),$=a.forwardRef((e,t)=>{let r=V(()=>{var t,r;return{search:"",value:null!=(r=null!=(t=e.value)?t:e.defaultValue)?r:"",filtered:{count:0,items:new Map,groups:new Set}}}),l=V(()=>new Set),n=V(()=>new Map),i=V(()=>new Map),s=V(()=>new Set),o=_(e),{label:u,children:c,value:d,onValueChange:f,filter:m,shouldFilter:p,loop:h,disablePointerSelection:v=!1,vimBindings:b=!0,...A}=e,I=(0,x.B)(),M=(0,x.B)(),$=(0,x.B)(),D=a.useRef(null),L=H();K(()=>{if(void 0!==d){let e=d.trim();r.current.value=e,O.emit()}},[d]),K(()=>{L(6,z)},[]);let O=a.useMemo(()=>({subscribe:e=>(s.current.add(e),()=>s.current.delete(e)),snapshot:()=>r.current,setState:(e,t,l)=>{var n,a,i;if(!Object.is(r.current[e],t)){if(r.current[e]=t,"search"===e)B(),q(),L(1,F);else if("value"===e&&(l||L(5,z),(null==(n=o.current)?void 0:n.value)!==void 0)){null==(i=(a=o.current).onValueChange)||i.call(a,null!=t?t:"");return}O.emit()}},emit:()=>{s.current.forEach(e=>e())}}),[]),G=a.useMemo(()=>({value:(e,t,l)=>{var n;t!==(null==(n=i.current.get(e))?void 0:n.value)&&(i.current.set(e,{value:t,keywords:l}),r.current.filtered.items.set(e,P(t,l)),L(2,()=>{q(),O.emit()}))},item:(e,t)=>(l.current.add(e),t&&(n.current.has(t)?n.current.get(t).add(e):n.current.set(t,new Set([e]))),L(3,()=>{B(),q(),r.current.value||F(),O.emit()}),()=>{i.current.delete(e),l.current.delete(e),r.current.filtered.items.delete(e);let t=U();L(4,()=>{B(),(null==t?void 0:t.getAttribute("id"))===e&&F(),O.emit()})}),group:e=>(n.current.has(e)||n.current.set(e,new Set),()=>{i.current.delete(e),n.current.delete(e)}),filter:()=>o.current.shouldFilter,label:u||e["aria-label"],getDisablePointerSelection:()=>o.current.disablePointerSelection,listId:I,inputId:$,labelId:M,listInnerRef:D}),[]);function P(e,t){var l,n;let a=null!=(n=null==(l=o.current)?void 0:l.filter)?n:S;return e?a(e,r.current.search,t):0}function q(){if(!r.current.search||!1===o.current.shouldFilter)return;let e=r.current.filtered.items,t=[];r.current.filtered.groups.forEach(r=>{let l=n.current.get(r),a=0;l.forEach(t=>{a=Math.max(e.get(t),a)}),t.push([r,a])});let l=D.current;Q().sort((t,r)=>{var l,n;let a=t.getAttribute("id"),i=r.getAttribute("id");return(null!=(l=e.get(i))?l:0)-(null!=(n=e.get(a))?n:0)}).forEach(e=>{let t=e.closest(w);t?t.appendChild(e.parentElement===t?e:e.closest(`${w} > *`)):l.appendChild(e.parentElement===l?e:e.closest(`${w} > *`))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{var t;let r=null==(t=D.current)?void 0:t.querySelector(`${y}[${N}="${encodeURIComponent(e[0])}"]`);null==r||r.parentElement.appendChild(r)})}function F(){let e=Q().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(N);O.setState("value",t||void 0)}function B(){var e,t,a,s;if(!r.current.search||!1===o.current.shouldFilter){r.current.filtered.count=l.current.size;return}r.current.filtered.groups=new Set;let u=0;for(let n of l.current){let l=P(null!=(t=null==(e=i.current.get(n))?void 0:e.value)?t:"",null!=(s=null==(a=i.current.get(n))?void 0:a.keywords)?s:[]);r.current.filtered.items.set(n,l),l>0&&u++}for(let[e,t]of n.current)for(let l of t)if(r.current.filtered.items.get(l)>0){r.current.filtered.groups.add(e);break}r.current.filtered.count=u}function z(){var e,t,r;let l=U();l&&((null==(e=l.parentElement)?void 0:e.firstChild)===l&&(null==(r=null==(t=l.closest(y))?void 0:t.querySelector('[cmdk-group-heading=""]'))||r.scrollIntoView({block:"nearest"})),l.scrollIntoView({block:"nearest"}))}function U(){var e;return null==(e=D.current)?void 0:e.querySelector(`${j}[aria-selected="true"]`)}function Q(){var e;return Array.from((null==(e=D.current)?void 0:e.querySelectorAll(E))||[])}function Z(e){let t=Q()[e];t&&O.setState("value",t.getAttribute(N))}function T(e){var t;let r=U(),l=Q(),n=l.findIndex(e=>e===r),a=l[n+e];null!=(t=o.current)&&t.loop&&(a=n+e<0?l[l.length-1]:n+e===l.length?l[0]:l[n+e]),a&&O.setState("value",a.getAttribute(N))}function X(e){let t=U(),r=null==t?void 0:t.closest(y),l;for(;r&&!l;)l=null==(r=e>0?function(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}(r,y):function(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}(r,y))?void 0:r.querySelector(E);l?O.setState("value",l.getAttribute(N)):T(e)}let Y=()=>Z(Q().length-1),ee=e=>{e.preventDefault(),e.metaKey?Y():e.altKey?X(1):T(1)},et=e=>{e.preventDefault(),e.metaKey?Z(0):e.altKey?X(-1):T(-1)};return a.createElement(g.sG.div,{ref:t,tabIndex:-1,...A,"cmdk-root":"",onKeyDown:e=>{var t;if(null==(t=A.onKeyDown)||t.call(A,e),!e.defaultPrevented)switch(e.key){case"n":case"j":b&&e.ctrlKey&&ee(e);break;case"ArrowDown":ee(e);break;case"p":case"k":b&&e.ctrlKey&&et(e);break;case"ArrowUp":et(e);break;case"Home":e.preventDefault(),Z(0);break;case"End":e.preventDefault(),Y();break;case"Enter":if(!e.nativeEvent.isComposing&&229!==e.keyCode){e.preventDefault();let t=U();if(t){let e=new Event(k);t.dispatchEvent(e)}}}}},a.createElement("label",{"cmdk-label":"",htmlFor:G.inputId,id:G.labelId,style:W},u),J(e,e=>a.createElement(R.Provider,{value:O},a.createElement(C.Provider,{value:G},e))))}),D=a.forwardRef((e,t)=>{var r,l;let n=(0,x.B)(),i=a.useRef(null),s=a.useContext(M),o=A(),u=_(e),c=null!=(l=null==(r=u.current)?void 0:r.forceMount)?l:null==s?void 0:s.forceMount;K(()=>{if(!c)return o.item(n,null==s?void 0:s.id)},[c]);let d=U(n,i,[e.value,e.children,i],e.keywords),f=I(),m=z(e=>e.value&&e.value===d.current),p=z(e=>!!c||!1===o.filter()||!e.search||e.filtered.items.get(n)>0);function h(){var e,t;v(),null==(t=(e=u.current).onSelect)||t.call(e,d.current)}function v(){f.setState("value",d.current,!0)}if(a.useEffect(()=>{let t=i.current;if(!(!t||e.disabled))return t.addEventListener(k,h),()=>t.removeEventListener(k,h)},[p,e.onSelect,e.disabled]),!p)return null;let{disabled:b,value:y,onSelect:w,forceMount:j,keywords:E,...N}=e;return a.createElement(g.sG.div,{ref:B([i,t]),...N,id:n,"cmdk-item":"",role:"option","aria-disabled":!!b,"aria-selected":!!m,"data-disabled":!!b,"data-selected":!!m,onPointerMove:b||o.getDisablePointerSelection()?void 0:v,onClick:b?void 0:h},e.children)}),L=a.forwardRef((e,t)=>{let{heading:r,children:l,forceMount:n,...i}=e,s=(0,x.B)(),o=a.useRef(null),u=a.useRef(null),c=(0,x.B)(),d=A(),f=z(e=>!!n||!1===d.filter()||!e.search||e.filtered.groups.has(s));K(()=>d.group(s),[]),U(s,o,[e.value,e.heading,u]);let m=a.useMemo(()=>({id:s,forceMount:n}),[n]);return a.createElement(g.sG.div,{ref:B([o,t]),...i,"cmdk-group":"",role:"presentation",hidden:!f||void 0},r&&a.createElement("div",{ref:u,"cmdk-group-heading":"","aria-hidden":!0,id:c},r),J(e,e=>a.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?c:void 0},a.createElement(M.Provider,{value:m},e))))}),O=a.forwardRef((e,t)=>{let{alwaysRender:r,...l}=e,n=a.useRef(null),i=z(e=>!e.search);return r||i?a.createElement(g.sG.div,{ref:B([n,t]),...l,"cmdk-separator":"",role:"separator"}):null}),G=a.forwardRef((e,t)=>{let{onValueChange:r,...l}=e,n=null!=e.value,i=I(),s=z(e=>e.search),o=z(e=>e.value),u=A(),c=a.useMemo(()=>{var e;let t=null==(e=u.listInnerRef.current)?void 0:e.querySelector(`${j}[${N}="${encodeURIComponent(o)}"]`);return null==t?void 0:t.getAttribute("id")},[]);return a.useEffect(()=>{null!=e.value&&i.setState("search",e.value)},[e.value]),a.createElement(g.sG.input,{ref:t,...l,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":u.listId,"aria-labelledby":u.labelId,"aria-activedescendant":c,id:u.inputId,type:"text",value:n?e.value:s,onChange:e=>{n||i.setState("search",e.target.value),null==r||r(e.target.value)}})}),P=a.forwardRef((e,t)=>{let{children:r,label:l="Suggestions",...n}=e,i=a.useRef(null),s=a.useRef(null),o=A();return a.useEffect(()=>{if(s.current&&i.current){let e=s.current,t=i.current,r,l=new ResizeObserver(()=>{r=requestAnimationFrame(()=>{let r=e.offsetHeight;t.style.setProperty("--cmdk-list-height",r.toFixed(1)+"px")})});return l.observe(e),()=>{cancelAnimationFrame(r),l.unobserve(e)}}},[]),a.createElement(g.sG.div,{ref:B([i,t]),...n,"cmdk-list":"",role:"listbox","aria-label":l,id:o.listId},J(e,e=>a.createElement("div",{ref:B([s,o.listInnerRef]),"cmdk-list-sizer":""},e)))}),q=a.forwardRef((e,t)=>{let{open:r,onOpenChange:l,overlayClassName:n,contentClassName:i,container:s,...o}=e;return a.createElement(v.bL,{open:r,onOpenChange:l},a.createElement(v.ZL,{container:s},a.createElement(v.hJ,{"cmdk-overlay":"",className:n}),a.createElement(v.UC,{"aria-label":e.label,"cmdk-dialog":"",className:i},a.createElement($,{ref:t,...o}))))}),F=Object.assign($,{List:P,Item:D,Input:G,Group:L,Separator:O,Dialog:q,Empty:a.forwardRef((e,t)=>z(e=>0===e.filtered.count)?a.createElement(g.sG.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:a.forwardRef((e,t)=>{let{progress:r,children:l,label:n="Loading...",...i}=e;return a.createElement(g.sG.div,{ref:t,...i,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":n},J(e,e=>a.createElement("div",{"aria-hidden":!0},e)))})});function _(e){let t=a.useRef(e);return K(()=>{t.current=e}),t}var K=a.useEffect;function V(e){let t=a.useRef();return void 0===t.current&&(t.current=e()),t}function B(e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}}function z(e){let t=I(),r=()=>e(t.snapshot());return(0,b.useSyncExternalStore)(t.subscribe,r,r)}function U(e,t,r,l=[]){let n=a.useRef(),i=A();return K(()=>{var a;let s=(()=>{var e;for(let t of r){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():n.current}})(),o=l.map(e=>e.trim());i.value(e,s,o),null==(a=t.current)||a.setAttribute(N,s),n.current=s}),n}var H=()=>{let[e,t]=a.useState(),r=V(()=>new Map);return K(()=>{r.current.forEach(e=>e()),r.current=new Map},[e]),(e,l)=>{r.current.set(e,l),t({})}};function J({asChild:e,children:t},r){let l;return e&&a.isValidElement(t)?a.cloneElement("function"==typeof(l=t.type)?l(t.props):"render"in l?l.render(t.props):t,{ref:t.ref},r(t.props.children)):r(t)}var W={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"},Q=r(99270);let Z=a.forwardRef(({className:e,...t},r)=>(0,l.jsx)(F,{ref:r,className:(0,u.cn)("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",e),...t}));Z.displayName=F.displayName;let T=a.forwardRef(({className:e,...t},r)=>(0,l.jsxs)("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[(0,l.jsx)(Q.A,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),(0,l.jsx)(F.Input,{ref:r,className:(0,u.cn)("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",e),...t})]}));T.displayName=F.Input.displayName;let X=a.forwardRef(({className:e,...t},r)=>(0,l.jsx)(F.List,{ref:r,className:(0,u.cn)("max-h-[300px] overflow-y-auto overflow-x-hidden",e),...t}));X.displayName=F.List.displayName;let Y=a.forwardRef((e,t)=>(0,l.jsx)(F.Empty,{ref:t,className:"py-6 text-center text-sm",...e}));Y.displayName=F.Empty.displayName;let ee=a.forwardRef(({className:e,...t},r)=>(0,l.jsx)(F.Group,{ref:r,className:(0,u.cn)("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",e),...t}));ee.displayName=F.Group.displayName;let et=a.forwardRef(({className:e,...t},r)=>(0,l.jsx)(F.Separator,{ref:r,className:(0,u.cn)("-mx-1 h-px bg-border",e),...t}));et.displayName=F.Separator.displayName;let er=a.forwardRef(({className:e,...t},r)=>(0,l.jsx)(F.Item,{ref:r,className:(0,u.cn)("relative flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",e),...t}));er.displayName=F.Item.displayName;var el=r(40988),en=r(96834);function ea({options:e,selected:t,onChange:r,placeholder:n="选择选项...",emptyPlaceholder:d="没有找到选项",searchPlaceholder:f="搜索选项...",disabled:m=!1,maxSelected:p,selectedItemsDisplay:h="badges",className:v,showSelectAll:g=!1,showCheckboxes:x=!0}){let[b,y]=a.useState(!1),[w,j]=a.useState(""),E=a.useMemo(()=>{let t={};return e.forEach(e=>{let r=e.group||"default";t[r]||(t[r]=[]),t[r].push(e)}),t},[e]),k=a.useMemo(()=>Object.keys(E).filter(e=>"default"!==e),[E]),N=a.useCallback(e=>{let l;if(!m){if(t.includes(e))l=t.filter(t=>t!==e);else{if(void 0!==p&&t.length>=p)return;l=[...t,e]}r(l)}},[m,p,r,t]),S=a.useCallback(e=>{m||r(t.filter(t=>t!==e))},[m,r,t]),C=a.useCallback(()=>{if(!m){if(t.length===e.filter(e=>!e.disabled).length)r([]);else{let t=e.filter(e=>!e.disabled).map(e=>e.value);void 0!==p&&t.length>p?r(t.slice(0,p)):r(t)}}},[m,p,r,e,t]);a.useEffect(()=>{b||j("")},[b]);let A=a.useMemo(()=>{if(!w)return E;let e={};return Object.keys(E).forEach(t=>{let r=E[t].filter(e=>e.label.toLowerCase().includes(w.toLowerCase()));r.length>0&&(e[t]=r)}),e},[E,w]),R=a.useMemo(()=>{if(0===t.length)return n;if("count"===h)return`已选择 ${t.length} 项${p?` (最多 ${p} 项)`:""}`;if("labels"===h){let r=t.map(t=>{let r=e.find(e=>e.value===t);return r?.label||t});return r.length>2?`${r.slice(0,2).join(", ")} 等 ${r.length} 项`:r.join(", ")}return n},[p,e,n,t,h]),I=a.useMemo(()=>{let r=e.filter(e=>!e.disabled);return r.length>0&&r.every(e=>t.includes(e.value))},[e,t]);return(0,l.jsxs)(el.AM,{open:b,onOpenChange:y,children:[(0,l.jsx)(el.Wv,{asChild:!0,children:(0,l.jsxs)(c.$,{variant:"outline",role:"combobox","aria-expanded":b,className:(0,u.cn)("w-full justify-between",m&&"cursor-not-allowed opacity-50",v),onClick:e=>{if(m){e.preventDefault();return}},children:[(0,l.jsx)("span",{className:"truncate",children:R}),(0,l.jsx)(i,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,l.jsx)(el.hl,{className:"w-full p-0",align:"start",children:(0,l.jsxs)(Z,{children:[(0,l.jsx)(T,{placeholder:f,value:w,onValueChange:j}),"badges"===h&&t.length>0&&(0,l.jsx)("div",{className:"p-2 border-b",children:(0,l.jsx)("div",{className:"flex flex-wrap gap-1",children:t.map(t=>{let r=e.find(e=>e.value===t);return(0,l.jsxs)(en.E,{variant:"secondary",className:"flex items-center gap-1",children:[r?.label||t,(0,l.jsxs)("button",{className:"ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2",onKeyDown:e=>{"Enter"===e.key&&S(t)},onMouseDown:e=>{e.preventDefault(),e.stopPropagation()},onClick:()=>S(t),children:[(0,l.jsx)(s.A,{className:"h-3 w-3 text-muted-foreground hover:text-foreground"}),(0,l.jsxs)("span",{className:"sr-only",children:["移除 ",r?.label||t]})]})]},t)})})}),(0,l.jsxs)(X,{children:[(0,l.jsx)(Y,{children:d}),g&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(er,{onSelect:C,className:"cursor-pointer",children:[x&&(0,l.jsx)("div",{className:(0,u.cn)("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",I?"bg-primary text-primary-foreground":"opacity-50"),children:I&&(0,l.jsx)(o.A,{className:"h-3 w-3"})}),(0,l.jsx)("span",{children:I?"取消全选":"全选"})]}),(0,l.jsx)(et,{})]}),A.default&&A.default.length>0&&(0,l.jsx)(ee,{children:A.default.map(e=>(0,l.jsxs)(er,{onSelect:()=>N(e.value),disabled:e.disabled,className:(0,u.cn)("cursor-pointer",e.disabled&&"cursor-not-allowed opacity-50"),children:[x&&(0,l.jsx)("div",{className:(0,u.cn)("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",t.includes(e.value)?"bg-primary text-primary-foreground":"opacity-50"),children:t.includes(e.value)&&(0,l.jsx)(o.A,{className:"h-3 w-3"})}),(0,l.jsx)("span",{children:e.label})]},e.value))}),k.map(e=>A[e]&&0!==A[e].length?(0,l.jsx)(ee,{heading:e,children:A[e].map(e=>(0,l.jsxs)(er,{onSelect:()=>N(e.value),disabled:e.disabled,className:(0,u.cn)("cursor-pointer",e.disabled&&"cursor-not-allowed opacity-50"),children:[x&&(0,l.jsx)("div",{className:(0,u.cn)("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",t.includes(e.value)?"bg-primary text-primary-foreground":"opacity-50"),children:t.includes(e.value)&&(0,l.jsx)(o.A,{className:"h-3 w-3"})}),(0,l.jsx)("span",{children:e.label})]},e.value))},e):null)]}),p&&(0,l.jsxs)("div",{className:"p-2 text-xs text-muted-foreground border-t",children:["已选择 ",t.length,"/",p," 项"]})]})})]})}var ei=r(80013),es=r(82947),eo=r(92053),eu=r(84778);let ec=({open:e,onOpenChange:t,data:r})=>{let[i,s]=(0,a.useState)([]),{data:o}=(0,es.zo)({}),{data:u,isLoading:d}=(0,eo.Qq)(r.id),[f,{isLoading:m}]=(0,eo.RC)();(0,a.useEffect)(()=>{e&&u&&s(u.map(e=>e.courseId))},[e,u]);let p=async()=>{try{await f({productId:r.id,coursesData:{courseIds:i}}),eu.l.success("课程绑定成功"),t(!1)}catch(e){eu.l.error("课程绑定失败"),console.error("绑定课程失败:",e)}},h=o?.map(e=>({label:e.name,value:e.id}))||[];return(0,l.jsx)(n.lG,{open:e,onOpenChange:t,children:(0,l.jsxs)(n.Cf,{className:"sm:max-w-[425px] p-0 overflow-hidden",children:[(0,l.jsx)(n.c7,{className:"px-6 pt-6 pb-2",children:(0,l.jsx)(n.L3,{className:"text-xl font-semibold",children:"绑定课程"})}),(0,l.jsx)("div",{className:"px-6",children:(0,l.jsx)("div",{className:"grid gap-4 py-4",children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(ei.J,{htmlFor:"courses",className:"text-sm font-medium",children:"选择课程"}),(0,l.jsx)(ea,{selectedItemsDisplay:"count",className:"w-full",placeholder:"请选择要绑定的课程",options:h||[],onChange:e=>s(e),selected:i,disabled:d},"courses"),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"可以选择多个课程进行绑定"})]})})}),(0,l.jsxs)(n.Es,{className:"px-6 py-4 border-t",children:[(0,l.jsx)(c.$,{variant:"outline",onClick:()=>t(!1),className:"mr-2",disabled:m,children:"取消"}),(0,l.jsx)(c.$,{onClick:p,disabled:m,children:m?"绑定中...":"确认绑定"})]})]})})}},99270:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(62688).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};