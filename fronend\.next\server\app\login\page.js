(()=>{var e={};e.id=4520,e.ids=[4520],e.modules={1203:(e,r,t)=>{Promise.resolve().then(t.bind(t,48666))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var s=t(49384),o=t(82348);function a(...e){return(0,o.QP)((0,s.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14163:(e,r,t)=>{"use strict";t.d(r,{hO:()=>l,sG:()=>n});var s=t(43210),o=t(51215),a=t(8730),i=t(60687),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=s.forwardRef((e,t)=>{let{asChild:s,...o}=e,n=s?a.DX:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n,{...o,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{});function l(e,r){e&&o.flushSync(()=>e.dispatchEvent(r))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>l});var s=t(60687),o=t(43210),a=t(8730),i=t(24224),n=t(4780);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=o.forwardRef(({className:e,variant:r,size:t,asChild:o=!1,...i},d)=>{let c=o?a.DX:"button";return(0,s.jsx)(c,{className:(0,n.cn)(l({variant:r,size:t,className:e})),ref:d,...i})});d.displayName="Button"},31145:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),o=t(48088),a=t(88170),i=t.n(a),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94934)),"F:\\trae\\cardmees\\fronend\\src\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["F:\\trae\\cardmees\\fronend\\src\\app\\login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>u});var s=t(60687),o=t(43210),a=t(4780);let i=o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));i.displayName="Card";let n=o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...r}));n.displayName="CardHeader";let l=o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let d=o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},48666:(e,r,t)=>{"use strict";t.d(r,{default:()=>w});var s=t(60687),o=t(44493),a=t(43210),i=t(16189),n=t(89667),l=t(29523),d=t(80013),c=t(84778),u=t(58869),m=t(62688);let f=(0,m.A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),p=(0,m.A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),x=t(51060).A.create({baseURL:"http://127.0.0.1:3001",timeout:1e4,headers:{"Content-Type":"application/json"}});x.interceptors.request.use(e=>{let r=localStorage.getItem("token");return console.log(r," token"),r&&(e.headers=e.headers||{},e.headers.Authorization=`Bearer ${r}`),e},e=>Promise.reject(e)),x.interceptors.response.use(e=>e.data,async e=>{if(e.config,!e.response?.data)return c.l.error("网络错误，请检查您的网络连接"),Promise.reject(Error("网络错误，请检查您的网络连接"));{let r=e.response.data;if(!1===r.success&&r.error){let{message:e,code:t,status:s}=r.error;switch(s){case 401:c.l.error(e||"登录已过期","请重新登录"),localStorage.removeItem("userInfo"),localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("refreshToken"),setTimeout(()=>{window.location.href="/login"},1500);break;case 403:c.l.error(e||"没有权限访问该资源");break;case 404:c.l.error(e||"请求的资源不存在");break;case 409:c.l.error(e||"资源冲突");break;case 500:c.l.error(e||"服务器错误");break;default:c.l.error(e||"请求失败")}let o=Error(e);return Object.assign(o,r.error),Promise.reject(o)}}});var h=t(54864),g=t(77623);function v(){let e=(0,i.useRouter)(),{login:r}={login:async(e,r)=>{try{let t=await x.post("/api/auth/login",{account:e,password:r}),{accessToken:s,refreshToken:o}=t.data;return localStorage.setItem("token",s),localStorage.setItem("refresh_token",o),t.data}catch(e){throw console.error(e),e}},logout:async()=>{try{await x.post("/api/auth/logout")}catch(e){console.error("登出API调用失败:",e)}finally{localStorage.removeItem("userInfo"),localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("refreshToken"),localStorage.removeItem("persist:root"),sessionStorage.removeItem("token"),sessionStorage.removeItem("refresh_token"),sessionStorage.removeItem("refreshToken")}}},t=(0,h.wA)(),[o,m]=(0,a.useState)(!1),[v,b]=(0,a.useState)({account:"",password:""}),y=async s=>{if(s.preventDefault(),!o){if(!v.account||!v.password){c.l.warning("请输入用户名和密码");return}m(!0);try{let s=await r(v.account,v.password);if(!s)return;localStorage.setItem("token",s.accessToken),localStorage.setItem("refresh_token",s.refreshToken),t((0,g.iA)({id:s.user.id,name:s.user.name,account:s.user.account,avatar:s.user.avatar,institutionName:s.user.institutionName,institutionLogo:s.user.institutionLogo,institutionSubjectName:s.user.institutionSubjectName,token:s.accessToken,refresh_token:s.refreshToken,isAuthenticated:!0})),c.l.success("登录成功."),e.push("/dashboard")}catch(e){c.l.error("登录失败,请检查用户名和密码!")}finally{m(!1)}}};return(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsxs)("div",{className:"mb-6 md:mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"欢迎回来"}),(0,s.jsx)("p",{className:"text-gray-500 mt-2 text-sm",children:"请登录您的账号以继续"})]}),(0,s.jsxs)("form",{onSubmit:y,className:"space-y-5 flex-1",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"username",className:"text-sm font-medium text-gray-700 mb-1.5 block",children:"用户名"}),(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)(n.p,{id:"username",type:"text",value:v.account,onChange:e=>b({...v,account:e.target.value}),placeholder:"请输入用户名",className:"h-12 pl-4 pr-4 py-3 rounded-lg border-gray-200 focus-visible:ring-1 focus-visible:ring-blue-500    focus-visible:border-blue-500 w-full transition-all"}),(0,s.jsx)("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400",children:(0,s.jsx)(u.A,{size:18})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1.5",children:[(0,s.jsx)(d.J,{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"密码"}),(0,s.jsx)("a",{href:"#",className:"text-xs text-blue-600 hover:text-blue-800 transition-colors",children:"忘记密码?"})]}),(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)(n.p,{id:"password",type:"password",value:v.password,onChange:e=>b({...v,password:e.target.value}),placeholder:"请输入密码",className:"h-12 pl-4 pr-4 py-3 rounded-lg border-gray-200 focus-visible:ring-1 focus-visible:ring-blue-500    focus-visible:border-blue-500 w-full transition-all"}),(0,s.jsx)("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400",children:(0,s.jsx)(f,{size:18})})]})]}),(0,s.jsx)("div",{className:"pt-2",children:(0,s.jsxs)(l.$,{type:"submit",disabled:o,className:"w-full h-12 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700    hover:to-indigo-800 text-white font-medium flex items-center justify-center gap-2 transition-all   focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-60",children:[o?"登录中...":"登录",!o&&(0,s.jsx)(p,{size:16})]})}),(0,s.jsxs)("div",{className:"mt-auto pt-6 text-center text-xs text-gray-500",children:["通过登录，您同意我们的 ",(0,s.jsx)("a",{href:"#",className:"text-blue-600 hover:underline",children:"服务条款"})," 和",(0,s.jsx)("a",{href:"#",className:"text-blue-600 hover:underline",children:" 隐私政策"})]})]})]})}let b=function(){return(0,s.jsxs)("div",{className:"h-full flex flex-col items-center justify-center py-10 px-6 relative overflow-hidden text-white",children:[(0,s.jsx)("div",{className:"absolute -bottom-16 -left-16 w-64 h-64 rounded-full bg-indigo-500 opacity-20 blur-3xl"}),(0,s.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 rounded-full bg-blue-400 opacity-20 blur-2xl"}),(0,s.jsx)("div",{className:"flex items-center justify-center w-16 h-16 bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl mb-8 relative z-10",children:(0,s.jsx)("span",{className:"text-white text-2xl font-bold",children:"SX"})}),(0,s.jsxs)("div",{className:"text-center space-y-4 relative z-10",children:[(0,s.jsx)("h1",{className:"text-2xl md:text-3xl font-bold leading-tight",children:"思学教育"}),(0,s.jsx)("div",{className:"h-1 w-12 bg-white opacity-50 mx-auto rounded-full"}),(0,s.jsx)("p",{className:"text-sm md:text-base font-light opacity-80 max-w-xs",children:"专业的教育机构管理解决方案"})]}),(0,s.jsx)("div",{className:"mt-10 space-y-3 relative z-10",children:["智能教务管理","高效课程排期","全方位学员服务"].map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm opacity-80",children:[(0,s.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-white"}),(0,s.jsx)("span",{children:e})]},r))}),(0,s.jsxs)("div",{className:"absolute bottom-4 text-xs opacity-60 mt-auto",children:["\xa9 ",new Date().getFullYear()," 零点科技"]})]})};var y=t(30596);function w(){return(0,i.useRouter)(),(0,y.G)(e=>e.user),(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4",children:(0,s.jsxs)(o.Zp,{className:"w-full max-w-4xl overflow-hidden rounded-xl shadow-lg border-0 flex flex-col md:flex-row",children:[(0,s.jsx)("div",{className:"w-full md:w-5/12 bg-gradient-to-br from-blue-600 to-indigo-800",children:(0,s.jsx)(b,{})}),(0,s.jsx)("div",{className:"w-full md:w-7/12 p-6 sm:p-8 bg-white",children:(0,s.jsx)(v,{})})]})})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62688:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var s=t(43210);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:o,className:n="",children:l,iconNode:d,...c},u)=>(0,s.createElement)("svg",{ref:u,...i,width:r,height:r,stroke:e,strokeWidth:o?24*Number(t)/Number(r):t,className:a("lucide",n),...c},[...d.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(l)?l:[l]])),l=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...i},l)=>(0,s.createElement)(n,{ref:l,iconNode:r,className:a(`lucide-${o(e)}`,t),...i}));return t.displayName=`${e}`,t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63989:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\components\\\\login\\\\login.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\components\\login\\login.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},78148:(e,r,t)=>{"use strict";t.d(r,{b:()=>n});var s=t(43210),o=t(14163),a=t(60687),i=s.forwardRef((e,r)=>(0,a.jsx)(o.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var n=i},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var s=t(60687),o=t(43210),a=t(78148),i=t(24224),n=t(4780);let l=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=o.forwardRef(({className:e,...r},t)=>(0,s.jsx)(a.b,{ref:t,className:(0,n.cn)(l(),e),...r}));d.displayName=a.b.displayName},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687),o=t(43210),a=t(4780);let i=o.forwardRef(({className:e,type:r,...t},o)=>(0,s.jsx)("input",{type:r,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:o,...t}));i.displayName="Input"},90587:(e,r,t)=>{Promise.resolve().then(t.bind(t,63989))},94735:e=>{"use strict";e.exports=require("events")},94934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i,metadata:()=>a});var s=t(37413),o=t(63989);let a={title:{absolute:"登录 | 思学"}};function i(){return(0,s.jsx)(o.default,{})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7392,3928,3019],()=>t(31145));module.exports=s})();