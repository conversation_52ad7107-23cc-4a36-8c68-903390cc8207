import { createError } from '@fastify/error';
import { TwitterSnowflake } from "@sapphire/snowflake"

const INTERNAL_ERROR = createError('INTERNAL_ERROR', '%s', 500);
export default async function (fastify, opts) {
    fastify.get('/products',{
        schema: {
            tags: ['products'],
            querystring: {
                type: 'object',
                properties: {
                    page: { type: 'number', default: 1 },
                    pageSize: { type: 'number', default: 10 },
                    search: { type: 'string' },
                    type: { type: 'string' }
                }
            },
        },
        onRequest: [fastify.auth.authenticate, fastify.auth.requirePermission('product:read')],
        handler: async (request, reply) => {
            const user = request.user;
            const { page, pageSize, search, type } = request.query;
            const client = fastify.prisma;
            try {

                const skip = (page - 1) * pageSize;
                const take = pageSize;

                const where = {
                    institutionId: user.institutionId,
                    ...(search ? { name: { contains: search, mode: 'insensitive' } } : {}),
                    ...(type ? { packageType: type } : {})
                }
                const [total , result] = await Promise.all([
                    client.product.count({
                        where: where,
                    }),
                    client.product.findMany({
                        where: where,
                        select: {
                            id: true,
                            name: true,
                            price: true,
                            leaveCount: true,
                            packageType: true,
                            usageLimit: true,
                            validTimeRange: true,
                            timeLimitedUsage: true,
                            timeLimitType: true,
                            remarks: true,
                            status: true,
                            createdAt: true,
                        },
                        take,
                        skip
                    })
                ])

                reply.success({
                    message: '获取产品列表成功.',
                    data: {
                        list: result,
                        page,
                        pageSize,
                        total,
                    }
                });

            
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message|| '获取产品列表失败！')
            }
        }
    })
    // 获取全部可用产品不分页
    fastify.get('/products/all',{
        schema: {
            tags: ['products'],
            summary: '获取全部可用产品',
            response: {
                200: {
                    type: 'object',
                    properties: {
                        code: { type: 'number' },
                        message: { type: 'string' },
                        data: { 
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    name: { type: 'string' },
                                    price: { type: 'number' },
                                    leaveCount: { type: 'number' },
                                    packageType: { type: 'string' },
                                    usageLimit: { type: 'number' },
                                    validTimeRange: { type: 'string' },
                                    timeLimitedUsage: { type: 'number' },
                                    timeLimitType: { type: 'string' },
                                }
                            }
                         }
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;
            const client = fastify.prisma;
            try {
                const result = await client.product.findMany({
                    where: {
                        institutionId: user.institutionId,
                        status: 'active'
                    },
                    select: {
                        id: true,
                        name: true,
                        price: true,
                        leaveCount: true,
                        packageType: true,
                        usageLimit: true,
                        validTimeRange: true,
                        timeLimitedUsage: true,
                        timeLimitType: true,
                        // remarks: true,
                        // status: true,
                        // createdAt: true,
                    },
                })

                reply.success({
                    message: '获取产品成功.',
                    data: result,
                });

            
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message|| '获取产品列表失败！')
            }
        }
    })

    // 创建产品
    fastify.post('/products',{
        schema: {
            tags: ['products'],
            summary: '创建产品',
            body: {
                type: 'object',
                required: ['name', 'price'],
                properties: {
                    name: { type: 'string' },
                    price: { type: 'number' },
                    icon: { type: 'string' },
                    cover: { type: 'string' },
                    leaveCount: { type: 'number' },
                    packageType: { type: 'string' }, // 课程包类型
                    usageLimit: { type: 'number' }, // 使用限制
                    timeLimitedUsage: { type: 'number' }, // 限时消费时长
                    timeLimitType: { type: 'string' }, // 限时消费类型
                    validTimeRange: { type: 'string' }, // 有效时间范围 1、购买日算起  2、消费日算起
                    targetAudience: { type: 'string' }, // 适用人群
                    isShow: { type: 'boolean' }, // 是否显示
                    remarks: { type: 'string' }, // 备注
                    status: { type: 'string' }, // 产品状态
                }
            },
        },
        onRequest: [fastify.auth.authenticate, fastify.auth.requirePermission('product:create')],
        handler: async(request, reply) => {
            const user = request.user;
            const client = fastify.prisma;
            try {
                const { name, price, icon, cover, leaveCount, packageType, usageLimit, timeLimitedUsage, timeLimitType, validTimeRange, targetAudience, isShow, remarks, status } = request.body;
                const id = TwitterSnowflake.generate().toString();
                const result = await client.product.create({
                    data: {
                        id,
                        name,
                        price,
                        icon,
                        cover,
                        leaveCount,
                        packageType,
                        usageLimit,
                        timeLimitedUsage,
                        timeLimitType,
                        validTimeRange,
                        targetAudience,
                        isShow,
                        remarks,
                        status,
                        institutionId: user.institutionId
                    }
                })
                reply.success({
                    message: '创建产品成功.',
                    data: result
                }); 
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message|| '创建产品失败！')
            }
        }
    })

    // 更新产品
    fastify.put('/products/:productId',{
        schema: {
            tags: ['products'],
            summary: '更新产品',
            params: {
                type: 'object',
                properties: {
                    productId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                // required: ['name', 'price'],
                properties: {
                    name: { type: 'string' },
                    price: { type: 'number' },
                    icon: { type: 'string' },
                    cover: { type: 'string' },
                    leaveCount: { type: 'number' },
                    packageType: { type: 'string' }, // 课程包类型
                    usageLimit: { type: 'number' }, // 使用限制
                    timeLimitedUsage: { type: 'string' }, // 限时消费时长 单位：天/周/月
                    timeLimitType: { type: 'string' }, // 限时消费类型
                    validTimeRange: { type: 'string' }, // 有效时间范围 1、购买日算起  2、消费日算起
                    targetAudience: { type: 'string' }, // 适用人群
                    isShow: { type: 'boolean' }, // 是否显示
                    remarks: { type: 'string' }, // 备注
                    status: { type: 'string' }, // 产品状态
                }
            },
        },
        onRequest: [fastify.auth.authenticate, fastify.auth.requirePermission('product:update')],
        handler: async(request, reply) => {
            const user = request.user;
            const client = fastify.prisma;
            try {
                const { productId } = request.params;
                const { name, price, icon, cover, leaveCount, packageType, usageLimit, timeLimitedUsage, timeLimitType, validTimeRange, targetAudience, isShow, remarks, status } = request.body;
                const result = await client.product.update({
                    where: {
                        id: productId,
                        institutionId: user.institutionId
                    },
                    data: {
                        name,
                        price,
                        icon,
                        cover,
                        leaveCount,
                        packageType,
                        usageLimit,
                        timeLimitedUsage,
                        timeLimitType,
                        validTimeRange,
                        targetAudience,
                        isShow,
                        remarks,
                        status,
                    }
                })
                reply.success({
                    message: '更新产品成功.',
                    data: result
                }); 
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message|| '更新产品失败！')
            }
        }
    })
    // 删除产品
    fastify.delete('/products/:productId',{
        schema: {
            tags: ['products'],
            summary: '删除产品',
            params: {
                type: 'object',
                properties: {
                    productId: { type: 'string' }
                }
            },
        },
        onRequest: [fastify.auth.authenticate, fastify.auth.requirePermission('product:delete')],
        handler: async(request, reply) => {
            const user = request.user;
            const client = fastify.prisma;
            try {
                const { productId } = request.params;
                const result = await client.product.delete({
                    where: {
                        id: productId,
                        institutionId: user.institutionId
                    }
                })
                reply.success({
                    message: '删除产品成功.',
                    data: result
                }); 
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message|| '删除产品失败！')
            }
        }
    })

    // 获取套餐绑定的课程
    fastify.get('/products/:productId/courses',{
        schema: {
            tags: ['products'],
            summary: '获取套餐绑定的课程',
        },
        onRequest: [fastify.auth.authenticate],
        handler: async(request, reply) => {
            const user = request.user;
            const client = fastify.prisma;
            try {
                const { productId } = request.params;
                const result = await client.productCourse.findMany({
                    where: {
                        productId,
                        institutionId: user.institutionId
                    }
                })
                reply.success({
                    message: '获取套餐绑定的课程成功.',
                    data: result
                });
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message|| '获取套餐绑定的课程失败！')
            }
        }
    })
    // 套餐绑定课程
    fastify.put('/products/:productId/courses',{
        schema: {
            tags: ['products'],
            summary: '套餐绑定课程',
        },
        onRequest: [fastify.auth.authenticate, fastify.auth.requirePermission('product:update')],
        handler: async(request, reply) => {
            const user = request.user;
            const client = fastify.prisma;
            try {
                const { productId } = request.params;
                const { courseIds } = request.body;
                const result = await client.productCourse.findMany({
                    where: {
                        productId,
                        institutionId: user.institutionId
                    }
                })
                const existsResult = courseIds.filter(courseId => result.some(item => item.courseId == courseId));
                // 需要创建的课程
                const notExistsResult = courseIds.filter(courseId => !result.some(item => item.courseId == courseId));
                // 需要删除的课程
                const deleteResult = result.filter(item => !courseIds.includes(item.courseId) && item.id);
            

                if(deleteResult.length > 0){
                    await client.productCourse.deleteMany({
                        where: {
                            id:{
                                in: deleteResult.map((item) => item.id)
                            }
                        }
                    })
                }

                // // 创建新的绑定
                if(notExistsResult.length > 0){
                    await client.productCourse.createMany({
                        data: notExistsResult.map(courseId => ({
                            productId,
                        courseId,
                        institutionId: user.institutionId
                        }))
                    })
                }
                reply.success({
                    message: '套餐绑定课程成功.',
                });
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message|| '套餐绑定课程失败！')
            }
        }
    })
}