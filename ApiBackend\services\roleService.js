import { createError } from '@fastify/error';
import { nanoid } from "nanoid";
import { TwitterSnowflake } from "@sapphire/snowflake";

const NOT_FOUND_ERROR = createError('NOT_FOUND_ERROR', '%s', 404);
const VALIDATION_ERROR = createError('VALIDATION_ERROR', '%s', 400);
const INTERNAL_ERROR = createError('INTERNAL_ERROR', '%s', 500);
const AUTH_ERROR = createError('AUTH_ERROR', '%s', 401);

/**
 * Role Service
 * Handles business logic related to roles
 */
const roleService = {
  /**
   * Get all roles with pagination and search
   * @param {Object} params - Parameters
   * @param {number} params.page - Page number
   * @param {number} params.pageSize - Page size
   * @param {string} params.search - Search term
   * @param {string} params.institutionId - Institution ID
   * @param {Object} params.fastify - Fastify instance
   * @returns {Promise<Object>} Roles and total count
   */
  async getAllRoles({ page, pageSize, search, institutionId, fastify }) {
    const where = {
      ...(search ? { name: { contains: search } } : {}),
      institutionId
    };
    
    const skip = (page - 1) * pageSize;
    const take = pageSize;

    try {
      const [roles, total] = await fastify.prisma.$transaction([
        fastify.prisma.role.findMany({
          where,
          select: {
            id: true,
            name: true,
            description: true,
          },
          skip,
          take,
          orderBy: {
            createdAt: 'desc'
          }
        }),
        fastify.prisma.role.count({ where })
      ]);

      return { roles, total };
    } catch (error) {
      fastify.log.error(error);
      throw new INTERNAL_ERROR('获取角色列表失败');
    }
  },

  /**
   * Create a new role
   * @param {Object} params - Parameters
   * @param {string} params.name - Role name
   * @param {string} params.description - Role description
   * @param {string} params.userId - User ID
   * @param {boolean} params.is_super_admin - Whether the user is a super admin
   * @param {Object} params.fastify - Fastify instance
   * @returns {Promise<Object>} Created role
   */
  async createRole({ name, description, userId, is_super_admin, fastify }) {
    const client = await fastify.pg.connect();
    
    try {
      // Get institution ID
      const result = await client.query(`
        SELECT (SELECT "institutionId" FROM user_institution WHERE "userId" = $1 LIMIT 1) AS institution_id
      `, [userId]);

      const { institution_id } = result.rows[0];
      
      // Check if user has permission to create role
      if (!is_super_admin && !institution_id) {
        throw new VALIDATION_ERROR('无权创建角色');
      }

      // Generate role code
      const code = is_super_admin ? nanoid(10).toString() : `ORG_${nanoid(10).toString()}`;

      // Check if role exists
      const roleExists = await client.query(`
        SELECT 1 FROM roles WHERE name = $1 AND code = $2 LIMIT 1
      `, [name, code]);

      if (roleExists.rows.length > 0) {
        throw new VALIDATION_ERROR('角色已存在');
      }

      // Create role
      const id = TwitterSnowflake.generate().toString();
      const newRole = await client.query(`
        INSERT INTO roles (id, name, code, description, "institutionId")
        VALUES ($1, $2, $3, $4, $5) RETURNING *
      `, [id, name, code, description, institution_id]);

      return newRole.rows[0];
    } catch (error) {
      fastify.log.error(error);
      
      if (error instanceof VALIDATION_ERROR) {
        throw error;
      }
      
      throw new INTERNAL_ERROR('创建角色失败');
    } finally {
      client.release();
    }
  },

  /**
   * Delete a role by ID
   * @param {Object} params - Parameters
   * @param {string} params.roleId - Role ID
   * @param {string} params.userId - User ID
   * @param {string} params.institutionId - Institution ID
   * @param {Object} params.fastify - Fastify instance
   * @returns {Promise<Object>} Deleted role info
   */
  async deleteRole({ roleId, userId, institutionId, fastify }) {
    const client = await fastify.pg.connect();
    
    try {
      await client.query('BEGIN');

      // Check role, user binding, and permissions
      const result = await client.query(`
        SELECT
          r.id, r.name, r.code, r."institutionId",
          (SELECT COUNT(*) FROM user_roles WHERE "roleId" = $1) AS user_count,
          (SELECT "institutionId" FROM user_institution WHERE "userId" = $2) AS user_institution
        FROM roles r
        WHERE r.id = $1
      `, [roleId, userId]);

      if (result.rows.length === 0) {
        throw new VALIDATION_ERROR('角色不存在');
      }

      const { name, code, institutionId: roleInstitutionId, user_count, user_institution } = result.rows[0];
      
      // Validation checks
      if (code === 'SYSTEM_ADMIN') {
        throw new VALIDATION_ERROR('无法删除超级管理员角色');
      }
      
      if (user_count > 0) {
        throw new VALIDATION_ERROR('角色下存在用户，无法删除');
      }
      
      if (roleInstitutionId && user_institution !== roleInstitutionId) {
        throw new VALIDATION_ERROR('无权删除该角色');
      }

      // Delete role
      await client.query(`DELETE FROM roles WHERE id = $1 AND "institutionId" = $2`, [roleId, institutionId]);
      await client.query('COMMIT');

      return { name };
    } catch (error) {
      await client.query('ROLLBACK');
      fastify.log.error(error);
      
      if (error instanceof VALIDATION_ERROR) {
        throw error;
      }
      
      throw new INTERNAL_ERROR('删除角色失败');
    } finally {
      client.release();
    }
  },

  /**
   * Update a role by ID
   * @param {Object} params - Parameters
   * @param {string} params.roleId - Role ID
   * @param {string} params.name - New role name
   * @param {string} params.description - New role description
   * @param {string} params.userId - User ID
   * @param {string} params.institutionId - Institution ID
   * @param {Object} params.fastify - Fastify instance
   * @returns {Promise<Object>} Updated role info
   */
  async updateRole({ roleId, name, description, userId, institutionId, fastify }) {
    const client = await fastify.pg.connect();
    
    try {
      await client.query('BEGIN');

      // Check role and permissions
      const result = await client.query(`
        SELECT
          r.id, r.name, r.code, r."institutionId",
          (SELECT "institutionId" FROM user_institution WHERE "userId" = $2) AS user_institution
        FROM roles r
        WHERE r.id = $1
      `, [roleId, userId]);

      if (result.rows.length === 0) {
        throw new VALIDATION_ERROR('角色不存在');
      }

      const { name: oldName, code, institutionId: roleInstitutionId, user_institution } = result.rows[0];
      
      // Validation checks
      if (code === 'SYSTEM_ADMIN') {
        throw new VALIDATION_ERROR('无法操作超级管理员角色');
      }
      
      if (roleInstitutionId && user_institution !== roleInstitutionId) {
        throw new VALIDATION_ERROR('无权操作该角色');
      }

      // Update role
      await client.query(
        `UPDATE roles SET name = $2, description = $3, "updatedAt" = now() WHERE id = $1`, 
        [roleId, name, description]
      );
      
      await client.query('COMMIT');

      return { name: oldName };
    } catch (error) {
      await client.query('ROLLBACK');
      fastify.log.error(error);
      
      if (error instanceof VALIDATION_ERROR) {
        throw error;
      }
      
      throw new INTERNAL_ERROR('角色更新失败');
    } finally {
      client.release();
    }
  }
};

export default roleService;
