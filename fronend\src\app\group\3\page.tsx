'use client';

import React, { useState, useEffect } from 'react';
import ShareModal from "../components/ShareModal";
import PrizeList from './components/PrizeList';
import LuckyWheel from './components/LuckyWheel';
import PrizeResultModal from './components/PrizeResultModal';
import MyPrizesModal from './components/MyPrizesModal';
import { Gift } from 'lucide-react';
import PrizeModuleBlock from './components/PrizeModuleBlock';
import { luckyWheelData } from '@/mock/luckyWheelData';
import FriendHelp from './components/FriendHelp';
import InviteHelpModal from './components/InviteHelpModal';

export default function LuckyWheelPage() {
    const [showShareModal, setShowShareModal] = useState(false);
    const [showResultModal, setShowResultModal] = useState(false);
    const [showMyPrizesModal, setShowMyPrizesModal] = useState(false);
    const [showInviteModal, setShowInviteModal] = useState(false);
    const [canSpin, setCanSpin] = useState(true);
    const [spinCount, setSpinCount] = useState(3);
    const [prizeResult, setPrizeResult] = useState<any>(null);
    const [myPrizes, setMyPrizes] = useState<any[]>([]);
    const [helpCount, setHelpCount] = useState(0);
    const [helpRewarded, setHelpRewarded] = useState(false);
    
    // 从导入的mock数据中获取必要信息
    const res = luckyWheelData;
    const htmlData = res.data.html;
    const options = res.data.options;
    const prizes = options.prizes || [];
    const helpConfig = options.helpConfig || { enabled: false, helpNeeded: 3, rewardSpins: 2 };

    // 监听好友助力进度，当达到目标时给予奖励
    useEffect(() => {
        if (helpConfig.enabled && 
            helpCount >= helpConfig.helpNeeded && 
            !helpRewarded) {
            // 奖励抽奖机会
            setSpinCount(prev => prev + helpConfig.rewardSpins);
            setHelpRewarded(true);
            alert(`恭喜您获得${helpConfig.rewardSpins}次额外抽奖机会！`);
        }
    }, [helpCount, helpConfig, helpRewarded]);

    // 模拟好友助力的函数（实际项目中应通过API实现）
    const simulateFriendHelp = () => {
        // 模拟有新朋友助力
        setTimeout(() => {
            setHelpCount(prev => Math.min(prev + 1, helpConfig.helpNeeded));
        }, 1000);
    };

    const handleInvite = () => {
        setShowInviteModal(true);
    };

    const handleSpin = () => {
        if (canSpin) {
            if (spinCount <= 0) {
                alert('今日抽奖次数已用完，请明天再来！');
                return;
            }
            
            setCanSpin(false);
            
            // 先选择奖品，根据概率
            const totalProbability = prizes.reduce((sum, prize) => sum + prize.probability, 0);
            let random = Math.random() * totalProbability;
            let cumulativeProbability = 0;
            let selectedPrize = prizes[prizes.length - 1]; // 默认最后一个
            
            for (const prize of prizes) {
                cumulativeProbability += prize.probability;
                if (random <= cumulativeProbability) {
                    selectedPrize = prize;
                    break;
                }
            }
            
            // 设置选中的奖品
            setPrizeResult(selectedPrize);
            
            // 模拟转盘旋转时间，实际上是在等待转盘动画结束
            setTimeout(() => {
                setShowResultModal(true);
                setSpinCount(prev => prev - 1);
                setCanSpin(true);
                
                // 添加奖品到我的奖品列表
                setMyPrizes(prev => [...prev, {
                    ...selectedPrize,
                    winTime: new Date().toLocaleString()
                }]);
            }, 5000); // 增加等待时间，与转盘旋转时间匹配
        }
    };

    const handleAction = (type: string) => {
        // 移除此处的抽奖处理逻辑，改为其他功能按钮的处理
        console.log("按钮点击:", type);
    };

    const handleShare = () => {
        setShowShareModal(true);
    };
    
    const handleResultConfirm = () => {
        setShowResultModal(false);
    };

    return (
        <div className="bg-gray-50 min-h-screen font-sans pb-20">
            <img src={options.cover} alt="banner" className="w-full h-48 object-cover" />
            
            <div className="flex items-start justify-between mt-2.5 px-4">
                <div>
                    <h1 className="text-xl text-red-500 font-bold">{options.name}</h1>
                    <p className="text-sm text-gray-500 mb-2.5">{options.shareDesc}</p>
                </div>
                <div className="flex items-start space-x-2">
                    <button 
                        onClick={() => setShowMyPrizesModal(true)}
                        className="flex flex-col items-center text-gray-500 pt-1"
                    >
                        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <Gift size={18} />
                        </div>
                        <span className="text-xs mt-1">我的奖品</span>
                    </button>
                    <button 
                        onClick={handleShare}
                        className="flex flex-col items-center text-gray-500 pt-1"
                    >
                        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
                                <polyline points="16 6 12 2 8 6"></polyline>
                                <line x1="12" y1="2" x2="12" y2="15"></line>
                            </svg>
                        </div>
                        <span className="text-xs mt-1">分享</span>
                    </button>
                </div>
            </div>

            {/* 奖品列表 */}
            <PrizeList prizes={prizes} />
            
            {/* 抽奖次数显示 */}
            <div className="mx-3 my-3 p-3 bg-white rounded-lg shadow-sm">
                <div className="flex justify-between items-center">
                    <div className="text-gray-700">
                        <span className="font-medium">今日抽奖次数</span>
                    </div>
                    <div className="text-red-500 font-bold">
                        {spinCount} / 3
                    </div>
                </div>
                {spinCount === 0 && helpConfig.enabled && !helpRewarded && (
                    <div className="text-sm text-orange-500 mt-2 flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        抽奖次数已用完，邀请好友助力可获得额外抽奖机会！
                    </div>
                )}
            </div>
            
            {/* 助力功能 - 只在次数用完时显示 */}
            {helpConfig.enabled && spinCount === 0 && !helpRewarded && (
                <FriendHelp 
                    helpNeeded={helpConfig.helpNeeded}
                    currentHelp={helpCount}
                    rewardSpins={helpConfig.rewardSpins}
                    description={helpConfig.description}
                    onInvite={handleInvite}
                />
            )}
            
            {/* 转盘抽奖组件 */}
            <LuckyWheel
                prizes={prizes}
                spinning={!canSpin}
                onSpinEnd={() => {}}
                onSpin={handleSpin}
                selectedPrize={prizeResult}
            />

            {/* 活动内容模块 - 使用新的抽奖主题样式 */}
            {htmlData.module.map((mod: any, idx: number) => (
                <PrizeModuleBlock key={idx} title={mod.title} list={mod.list} />
            ))}

            {/* 底部按钮 - 移除抽奖按钮 */}
            <div className="fixed inset-x-0 bottom-0 bg-white shadow-lg flex justify-around py-2.5">
                {/* 移除抽奖按钮，替换为其他功能按钮 */}
                <div className="flex justify-around w-full">
                    <button 
                        onClick={() => setShowMyPrizesModal(true)}
                        className="flex flex-col items-center justify-center py-1 px-6 rounded-full bg-gradient-to-r from-pink-500 to-red-500 text-white"
                    >
                        <Gift size={20} className="mb-1" />
                        <span className="text-xs">我的奖品</span>
                    </button>
                    <button 
                        onClick={handleShare}
                        className="flex flex-col items-center justify-center py-1 px-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mb-1">
                            <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
                            <polyline points="16 6 12 2 8 6"></polyline>
                            <line x1="12" y1="2" x2="12" y2="15"></line>
                        </svg>
                        <span className="text-xs">分享活动</span>
                    </button>
                </div>
            </div>
            
            {/* 模态框 */}
            {showShareModal && (
                <ShareModal 
                    open={showShareModal} 
                    onClose={() => setShowShareModal(false)} 
                    shareTitle={options.shareTitle}
                    shareDesc={options.shareDesc}
                    qrCode={`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(window.location.href)}`}
                />
            )}
            
            {showResultModal && prizeResult && (
                <PrizeResultModal 
                    open={showResultModal}
                    onClose={handleResultConfirm}
                    prize={prizeResult}
                />
            )}
            
            {showMyPrizesModal && (
                <MyPrizesModal 
                    open={showMyPrizesModal}
                    onClose={() => setShowMyPrizesModal(false)}
                    prizes={myPrizes}
                />
            )}
            
            {showInviteModal && helpConfig && (
                <InviteHelpModal
                    open={showInviteModal}
                    onClose={() => {
                        setShowInviteModal(false);
                        // 测试用：模拟好友助力
                        simulateFriendHelp();
                    }}
                    shareTitle={helpConfig.shareTitle || "邀请好友助力"}
                    shareDesc={helpConfig.shareDesc || "帮我助力，你也可获得抽奖机会"}
                    qrCode={`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(window.location.href)}`}
                />
            )}
        </div>
    );
}
