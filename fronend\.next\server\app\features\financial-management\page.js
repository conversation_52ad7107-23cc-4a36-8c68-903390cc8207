(()=>{var e={};e.id=2300,e.ids=[2300],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,r,t)=>{let{createProxy:s}=t(39844);e.exports=s("F:\\trae\\cardmees\\fronend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17720:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26373:(e,r,t)=>{"use strict";t.d(r,{A:()=>x});var s=t(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:i="",children:x,iconNode:n,...c},o)=>(0,s.createElement)("svg",{ref:o,...d,width:r,height:r,stroke:e,strokeWidth:a?24*Number(t)/Number(r):t,className:l("lucide",i),...c},[...n.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(x)?x:[x]])),x=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...d},x)=>(0,s.createElement)(i,{ref:x,iconNode:r,className:l(`lucide-${a(e)}`,t),...d}));return t.displayName=`${e}`,t}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51465:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(26373).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},52568:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85814,23))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60397:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>d.a,__next_app__:()=>o,pages:()=>c,routeModule:()=>m,tree:()=>n});var s=t(65239),a=t(48088),l=t(88170),d=t.n(l),i=t(30893),x={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(x[e]=()=>i[e]);t.d(r,x);let n={children:["",{children:["features",{children:["financial-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,96537)),"F:\\trae\\cardmees\\fronend\\src\\app\\features\\financial-management\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["F:\\trae\\cardmees\\fronend\\src\\app\\features\\financial-management\\page.tsx"],o={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/features/financial-management/page",pathname:"/features/financial-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65766:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(26373).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},72845:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(26373).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83799:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(26373).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},83997:e=>{"use strict";e.exports=require("tty")},91142:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(26373).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94735:e=>{"use strict";e.exports=require("events")},96537:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(37413);t(61120);var a=t(4536),l=t.n(a),d=t(51465),i=t(65766),x=t(26373);let n=(0,x.A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);var c=t(72845);let o=(0,x.A)("Receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z",key:"q3az6g"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17.5v-11",key:"1jc1ny"}]]);var m=t(83799);let h=(0,x.A)("ChartPie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);var g=t(91142);function p(){return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,s.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(l(),{href:"/",className:"flex items-center space-x-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 transition-colors",children:[(0,s.jsx)(d.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{className:"font-medium",children:"返回首页"})]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"财务管理系统"})]})})}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-pink-600 py-16",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-6",children:(0,s.jsx)("div",{className:"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center",children:(0,s.jsx)(i.A,{className:"w-10 h-10 text-white"})})}),(0,s.jsx)("h1",{className:"text-4xl font-bold text-white mb-4",children:"财务管理"}),(0,s.jsx)("p",{className:"text-xl text-purple-100 max-w-3xl mx-auto",children:"全面的财务管理解决方案，精准掌控收支状况，助力教育机构健康发展"})]})})}),(0,s.jsx)("div",{className:"py-16 bg-white dark:bg-gray-800",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"核心功能特色"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"专业的财务管理工具集"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-xl border border-purple-200 dark:border-purple-800",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(n,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"智能收费管理"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"支持多种收费方式，自动生成收费单据，实时跟踪缴费状态"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(c.A,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"财务报表分析"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"自动生成各类财务报表，提供详细的收支分析和趋势预测"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-xl border border-green-200 dark:border-green-800",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(o,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"账单票据管理"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"电子化账单管理，支持发票开具和票据存档"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 p-6 rounded-xl border border-orange-200 dark:border-orange-800",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(m.A,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"收支统计分析"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"实时统计收入支出，多维度分析财务状况"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 p-6 rounded-xl border border-teal-200 dark:border-teal-800",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-teal-500 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(h,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"预算控制"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"制定年度预算计划，实时监控预算执行情况"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 p-6 rounded-xl border border-red-200 dark:border-red-800",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(i.A,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"欠费提醒"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"自动识别欠费学员，及时发送缴费提醒通知"})]})]})]})}),(0,s.jsx)("div",{className:"py-16 bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"财务数据一览"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"实时掌握机构财务状况"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(m.A,{className:"w-6 h-6 text-green-600 dark:text-green-400"})}),(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"\xa5156,000"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"本月收入"})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(i.A,{className:"w-6 h-6 text-blue-600 dark:text-blue-400"})}),(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"\xa545,000"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"本月支出"})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(h,{className:"w-6 h-6 text-purple-600 dark:text-purple-400"})}),(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"\xa5111,000"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"本月净利润"})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(o,{className:"w-6 h-6 text-orange-600 dark:text-orange-400"})}),(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"98.5%"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"缴费率"})]})]})]})}),(0,s.jsx)("div",{className:"py-16 bg-white dark:bg-gray-800",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"适用场景"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"满足各种教育机构的财务管理需求"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900 p-8 rounded-xl",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"培训机构"}),(0,s.jsxs)("ul",{className:"space-y-3 text-gray-600 dark:text-gray-300",children:[(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"课程费用管理"})]}),(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"学员缴费跟踪"})]}),(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"教师薪资管理"})]}),(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"运营成本控制"})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900 p-8 rounded-xl",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"学校教育"}),(0,s.jsxs)("ul",{className:"space-y-3 text-gray-600 dark:text-gray-300",children:[(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"学费收缴管理"})]}),(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"财务报表生成"})]}),(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"预算执行监控"})]}),(0,s.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{children:"资金流向分析"})]})]})]})]})]})}),(0,s.jsx)("div",{className:"py-16 bg-gradient-to-r from-purple-500 to-pink-600",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"开始使用财务管理系统"}),(0,s.jsx)("p",{className:"text-xl text-purple-100 mb-8",children:"让财务管理变得更加专业高效，助力教育机构稳健发展"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(l(),{href:"/dashboard",className:"inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-purple-600 bg-white hover:bg-gray-50 transition-colors duration-200",children:"立即体验"}),(0,s.jsx)(l(),{href:"/features",className:"inline-flex items-center justify-center px-8 py-3 border border-white text-base font-medium rounded-md text-white hover:bg-white/10 transition-colors duration-200",children:"查看更多功能"})]})]})}),(0,s.jsx)("footer",{className:"bg-gray-800 dark:bg-gray-900",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"text-center text-gray-400",children:(0,s.jsx)("p",{children:"\xa9 2024 CardMees 教育管理平台. 保留所有权利。"})})})})]})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7392,5814,3019],()=>t(60397));module.exports=s})();