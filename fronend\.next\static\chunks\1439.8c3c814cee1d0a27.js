"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1439],{1439:(e,a,t)=>{t.r(a),t.d(a,{default:()=>N});var s=t(95155),n=t(30285),r=t(9110),l=t(62523),i=t(84616),o=t(12115),d=t(55028),c=t(50228);let u=(0,d.default)(()=>t.e(238).then(t.bind(t,30238)),{loadableGenerated:{webpack:()=>[30238]},ssr:!1}),m=(0,d.default)(()=>t.e(3635).then(t.bind(t,3635)),{loadableGenerated:{webpack:()=>[3635]},ssr:!1}),x=(0,d.default)(()=>t.e(5087).then(t.bind(t,65087)),{loadableGenerated:{webpack:()=>[65087]},ssr:!1}),h=[{accessorKey:"name",header:"岗位名称",cell:e=>{let{row:a}=e;return(0,c.P)(a.getValue("name"))}},{accessorKey:"description",header:"岗位描述",cell:e=>{let{row:a}=e;return(0,c.s)(a.getValue("description"),10)}},{accessorKey:"actions",header:"操作",cell:e=>{let{row:a}=e,t=a.original;return(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)(u,{role:t}),(0,s.jsx)(m,{role:t}),(0,s.jsx)(x,{role:t})]})}}];var p=t(48436),f=t(48432),g=t(13515),v=t(45964),b=t.n(v);let j=(0,d.default)(()=>t.e(7458).then(t.bind(t,17458)),{loadableGenerated:{webpack:()=>[17458]},ssr:!1});function N(){let[e,a]=(0,o.useState)(""),[t,d]=(0,o.useState)(!1),[c,u]=(0,o.useState)({page:1,pageSize:10,total:0}),m=(0,o.useMemo)(()=>({...c,search:e}),[c,e]),{data:x,isLoading:v}=(0,g.iJ)(m),[N]=(0,g.vx)();console.log(null==x?void 0:x.list,"z111");let y=(0,o.useCallback)(b()(e=>{a(e),u(e=>({...e,page:1}))},300),[]),w=(0,o.useCallback)(e=>{y(e)},[y]),k=(0,o.useCallback)(e=>{u(a=>({...a,page:e}))},[]),C=(0,o.useCallback)(e=>{u(a=>({...a,page:1,pageSize:e}))},[]),z=(0,o.useCallback)(async e=>{try{await N(e).unwrap(),p.l.success("添加岗位成功"),d(!1)}catch(e){console.error("添加岗位失败:",e),p.l.error("添加岗位失败")}},[N]);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,s.jsx)("div",{className:"relative flex-1 max-w-md",children:(0,s.jsx)("div",{className:"flex",children:(0,s.jsx)(l.p,{value:e,onChange:e=>w(e.target.value),placeholder:"搜索岗位名称",className:"pr-10 h-10"})})}),(0,s.jsx)("div",{className:"flex flex-1 justify-end",children:(0,s.jsxs)(n.$,{onClick:()=>d(!0),size:"sm",className:"h-9 px-4 font-medium",children:[(0,s.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"新增岗位"]})})]}),(0,s.jsx)(r.b,{columns:h,data:(null==x?void 0:x.list)||[],pagination:!1,loading:v},"orgStaffPositionTable"),(0,s.jsx)(f.default,{pageSize:(null==x?void 0:x.pageSize)||10,totalItems:(null==x?void 0:x.total)||0,currentPage:(null==x?void 0:x.page)||1,onPageChange:k,onPageSizeChange:C}),t&&(0,s.jsx)(j,{open:t,onOpenChange:d,onSubmit:z})]})}},14636:(e,a,t)=>{t.d(a,{AM:()=>i,Wv:()=>o,hl:()=>d});var s=t(95155),n=t(12115),r=t(20547),l=t(59434);let i=r.bL,o=r.l9,d=n.forwardRef((e,a)=>{let{className:t,align:n="center",sideOffset:i=4,...o}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{ref:a,align:n,sideOffset:i,className:(0,l.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...o})})});d.displayName=r.UC.displayName},30285:(e,a,t)=>{t.d(a,{$:()=>d,r:()=>o});var s=t(95155),n=t(12115),r=t(99708),l=t(74466),i=t(59434);let o=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,a)=>{let{className:t,variant:n,size:l,asChild:d=!1,...c}=e,u=d?r.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(o({variant:n,size:l,className:t})),ref:a,...c})});d.displayName="Button"},48432:(e,a,t)=>{t.r(a),t.d(a,{default:()=>v});var s=t(95155),n=t(12115),r=t(42355),l=t(13052),i=t(5623),o=t(59434),d=t(30285);let c=e=>{let{className:a,...t}=e;return(0,s.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,o.cn)("mx-auto flex w-full justify-center",a),...t})};c.displayName="Pagination";let u=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,s.jsx)("ul",{ref:a,className:(0,o.cn)("flex flex-row items-center gap-1",t),...n})});u.displayName="PaginationContent";let m=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,s.jsx)("li",{ref:a,className:(0,o.cn)("",t),...n})});m.displayName="PaginationItem";let x=e=>{let{className:a,isActive:t,size:n="icon",...r}=e;return(0,s.jsx)("a",{"aria-current":t?"page":void 0,className:(0,o.cn)((0,d.r)({variant:t?"outline":"ghost",size:n}),a),...r})};x.displayName="PaginationLink";let h=e=>{let{className:a,...t}=e;return(0,s.jsxs)(x,{"aria-label":"Go to previous page",size:"default",className:(0,o.cn)("gap-1 pl-2.5",a),...t,children:[(0,s.jsx)(r.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"上一页"})]})};h.displayName="PaginationPrevious";let p=e=>{let{className:a,...t}=e;return(0,s.jsxs)(x,{"aria-label":"Go to next page",size:"default",className:(0,o.cn)("gap-1 pr-2.5",a),...t,children:[(0,s.jsx)("span",{children:"下一页"}),(0,s.jsx)(l.A,{className:"h-4 w-4"})]})};p.displayName="PaginationNext";let f=e=>{let{className:a,...t}=e;return(0,s.jsxs)("span",{"aria-hidden":!0,className:(0,o.cn)("flex h-9 w-9 items-center justify-center",a),...t,children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"更多页"})]})};f.displayName="PaginationEllipsis";var g=t(59409);function v(e){let{currentPage:a,pageSize:t,totalItems:n,onPageChange:i,onPageSizeChange:o}=e,d=Math.ceil(n/t),v=(()=>{let e=[];if(d<=5){for(let a=1;a<=d;a++)e.push(a);return e}e.push(1);let t=Math.max(2,a-1),s=Math.min(a+1,d-1);2===t&&(s=Math.min(t+2,d-1)),s===d-1&&(t=Math.max(s-2,2)),t>2&&e.push("ellipsis-start");for(let a=t;a<=s;a++)e.push(a);return s<d-1&&e.push("ellipsis-end"),d>1&&e.push(d),e})(),b=0===n?0:(a-1)*t+1,j=Math.min(a*t,n);return(0,s.jsxs)("div",{className:"flex flex-col gap-4 px-2 py-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"显示"}),(0,s.jsxs)(g.l6,{value:t.toString(),onValueChange:e=>{o(Number(e))},children:[(0,s.jsx)(g.bq,{className:"w-[4.5rem] h-7 text-xs border-border/60 hover:bg-accent/30 transition-colors",children:(0,s.jsx)(g.yv,{})}),(0,s.jsx)(g.gC,{children:[10,20,30,50].map(e=>(0,s.jsx)(g.eb,{value:e.toString(),className:"text-xs",children:e},e))})]}),(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"条"}),(0,s.jsx)("span",{className:"text-muted-foreground/40 mx-1.5",children:"|"}),n>0?(0,s.jsxs)("span",{className:"text-muted-foreground/80",children:[b,"-",j," / ",n," 条记录"]}):(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"0 条记录"})]}),(0,s.jsx)(c,{children:(0,s.jsxs)(u,{className:"gap-1",children:[(0,s.jsx)(m,{children:(0,s.jsx)(h,{onClick:()=>i(Math.max(1,a-1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(1===a?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,s.jsx)(r.A,{className:"h-4 w-4 mr-1"})})}),v.map((e,t)=>"ellipsis-start"===e||"ellipsis-end"===e?(0,s.jsx)(m,{children:(0,s.jsx)(f,{className:"h-8 w-8 flex items-center justify-center text-xs"})},"ellipsis-".concat(t)):(0,s.jsx)(m,{children:(0,s.jsx)(x,{onClick:()=>i(e),isActive:a===e,className:"h-8 w-8 text-xs font-medium rounded-md transition-colors data-[active]:bg-primary data-[active]:text-primary-foreground hover:bg-accent/50 hover:text-accent-foreground/90","aria-label":"前往第 ".concat(e," 页"),children:e})},e)),(0,s.jsx)(m,{children:(0,s.jsx)(p,{onClick:()=>i(Math.min(d,a+1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(a===d||0===d?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,s.jsx)(l.A,{className:"h-4 w-4 ml-1"})})})]})})]})}},50228:(e,a,t)=>{t.d(a,{P:()=>r,s:()=>l});var s=t(95155),n=t(73069);let r=function(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e?(0,s.jsx)("div",{className:"text-sm ".concat(a?"text-muted-foreground":""),children:e}):(0,s.jsx)("div",{})},l=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return e?(0,s.jsx)(n.c,{maxDisplayLength:a,children:e}):(0,s.jsx)("div",{className:"text-sm text-muted-foreground"})}},62523:(e,a,t)=>{t.d(a,{p:()=>l});var s=t(95155),n=t(12115),r=t(59434);let l=n.forwardRef((e,a)=>{let{className:t,type:n,...l}=e;return(0,s.jsx)("input",{type:n,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...l})});l.displayName="Input"},73069:(e,a,t)=>{t.d(a,{c:()=>m});var s=t(95155),n=t(12115),r=t(47863),l=t(66474),i=t(5196),o=t(24357),d=t(59434),c=t(14636),u=t(30285);function m(e){let{children:a,maxDisplayLength:t=15,className:m,popoverWidth:x="auto",showBorder:h=!1}=e,[p,f]=n.useState(!1),[g,v]=n.useState(!1),b=n.useMemo(()=>{if("string"==typeof a||"number"==typeof a)return a.toString();try{var e;let t=document.createElement("div");return t.innerHTML=(null==a?void 0:null===(e=a.props)||void 0===e?void 0:e.children)||"",t.textContent||""}catch(e){return console.warn("Could not extract text from children:",e),""}},[a]),j=n.useMemo(()=>{if("string"==typeof a||"number"==typeof a){let e=a.toString();return e.length>t?e.slice(0,t):e}return a},[a,t]),N=async()=>{try{await navigator.clipboard.writeText(b),v(!0),setTimeout(()=>v(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,s.jsxs)(c.AM,{open:p,onOpenChange:f,children:[(0,s.jsx)(c.Wv,{asChild:!0,children:(0,s.jsxs)(u.$,{variant:h?"outline":"ghost",role:"combobox","aria-expanded":p,className:(0,d.cn)("w-fit justify-between font-normal px-2 py-1 h-auto",!h&&"border-0 shadow-none",m),children:[(0,s.jsx)("span",{className:"mr-2 truncate",children:j}),p?(0,s.jsx)(r.A,{className:"h-4 w-4 shrink-0 opacity-50"}):(0,s.jsx)(l.A,{className:"h-4 w-4 shrink-0 opacity-50"})]})}),(0,s.jsx)(c.hl,{className:"p-0",align:"start",style:{width:x},children:(0,s.jsxs)("div",{className:"flex items-center gap-2 p-2",children:[(0,s.jsx)("span",{className:"text-sm break-all",children:b}),(0,s.jsxs)(u.$,{variant:"ghost",size:"icon",className:"h-8 w-8 shrink-0",onClick:N,children:[g?(0,s.jsx)(i.A,{className:"h-4 w-4"}):(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:g?"Copied":"Copy text"})]})]})})]})}}}]);