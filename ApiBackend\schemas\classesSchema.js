/**
 * Classes Schema
 * Defines validation schemas for class-related endpoints
 */
const classesSchema = {
    /**
     * Schema for getting classes list
     */
    getClassesListSchema: {
        tags: ['classes'],
        summary: '获取班级列表',
        description: '获取班级列表，支持分页和搜索',
        querystring: {
            type: 'object',
            properties: {
                page: { 
                    type: 'number', 
                    default: 1,
                    description: '页码，从1开始'
                },
                pageSize: { 
                    type: 'number', 
                    default: 10,
                    description: '每页数量，默认10'
                },
                search: { 
                    type: 'string',
                    description: '搜索关键词，支持班级名称和描述搜索'
                },
                status: { 
                    type: 'string',
                    enum: ['active', 'graduated', 'disbanded', 'all'],
                    default: 'active',
                    description: '班级状态'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            list: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        name: { type: 'string' },
                                        status: { type: 'string' },
                                        teacherName: { type: 'string' },
                                        classRoomName: { type: 'string' },
                                        maxStudentCount: { type: 'number' },
                                        studentCount: { type: 'number' },
                                        createdAt: { type: 'string' }
                                    }
                                }
                            },
                            total: { type: 'number' },
                            page: { type: 'number' },
                            pageSize: { type: 'number' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for getting class by ID
     */
    getClassByIdSchema: {
        tags: ['classes'],
        summary: '获取班级详情',
        description: '根据班级ID获取班级详细信息',
        params: {
            type: 'object',
            required: ['classId'],
            properties: {
                classId: { 
                    type: 'string',
                    description: '班级ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            status: { type: 'string' },
                            teacherId: { type: 'string' },
                            teacherName: { type: 'string' },
                            classRoomId: { type: 'string' },
                            classRoomName: { type: 'string' },
                            maxStudentCount: { type: 'number' },
                            times: { type: 'number' },
                            recurrenceType: { type: 'string' },
                            daily: { type: 'object' },
                            startDate: { type: 'string' },
                            endDate: { type: 'string' },
                            endType: { type: 'string' },
                            description: { type: 'string' },
                            students: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        name: { type: 'string' },
                                        phone: { type: 'string' },
                                        status: { type: 'string' }
                                    }
                                }
                            },
                            schedule: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        startDate: { type: 'string' },
                                        endDate: { type: 'string' },
                                        dayOfWeek: { type: 'number' },
                                        startTime: { type: 'string' },
                                        endTime: { type: 'string' }
                                    }
                                }
                            },
                            createdAt: { type: 'string' },
                            updatedAt: { type: 'string' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for creating class
     */
    createClassSchema: {
        tags: ['classes'],
        summary: '创建班级',
        description: '创建新班级',
        body: {
            type: 'object',
            required: ['name'],
            properties: {
                name: { 
                    type: 'string',
                    description: '班级名称'
                },
                teacherId: { 
                    type: 'string',
                    description: '教师ID'
                },
                classRoomId: { 
                    type: 'string',
                    description: '教室ID'
                },
                maxStudentCount: { 
                    type: 'number',
                    default: 20,
                    description: '最大学生数量'
                },
                times: { 
                    type: 'number',
                    description: '课时数'
                },
                recurrenceType: { 
                    type: 'string',
                    enum: ['weekly', 'daily'],
                    default: 'weekly',
                    description: '循环方式'
                },
                daily: { 
                    type: 'object',
                    properties: {
                        startTime: { type: 'string' },
                        endTime: { type: 'string' }
                    },
                    description: '每日时间设置'
                },
                startDate: { 
                    type: 'number',
                    description: '开始日期'
                },
                endDate: { 
                    type: 'number',
                    description: '结束日期'
                },
                endType: { 
                    type: 'string',
                    enum: ['number_of_times', 'times'],
                    default: 'number_of_times',
                    description: '结束类型'
                },
                status: { 
                    type: 'string',
                    enum: ['active', 'graduated', 'disbanded'],
                    default: 'active',
                    description: '班级状态'
                },
                description: { 
                    type: 'string',
                    description: '班级描述'
                },
                schedule: { 
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            startDate: { type: 'number' },
                            endDate: { type: 'number' },
                            dayOfWeek: { type: 'number' },
                            startTime: { type: 'string' },
                            endTime: { type: 'string' }
                        }
                    },
                    description: '班级课程表'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for updating class
     */
    updateClassSchema: {
        tags: ['classes'],
        summary: '更新班级',
        description: '更新班级信息',
        params: {
            type: 'object',
            required: ['classId'],
            properties: {
                classId: { 
                    type: 'string',
                    description: '班级ID'
                }
            }
        },
        body: {
            type: 'object',
            properties: {
                name: { type: 'string' },
                teacherId: { type: 'string' },
                classRoomId: { type: 'string' },
                maxStudentCount: { type: 'number' },
                times: { type: 'number' },
                recurrenceType: { 
                    type: 'string',
                    enum: ['weekly', 'daily']
                },
                daily: { 
                    type: 'object',
                    properties: {
                        startTime: { type: 'string' },
                        endTime: { type: 'string' }
                    }
                },
                startDate: { type: 'number' },
                endDate: { type: 'number' },
                endType: { 
                    type: 'string',
                    enum: ['number_of_times', 'times']
                },
                status: { 
                    type: 'string',
                    enum: ['active', 'graduated', 'disbanded']
                },
                description: { type: 'string' },
                schedule: { 
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            startDate: { type: 'number' },
                            endDate: { type: 'number' },
                            dayOfWeek: { type: 'number' },
                            startTime: { type: 'string' },
                            endTime: { type: 'string' }
                        }
                    }
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for deleting class
     */
    deleteClassSchema: {
        tags: ['classes'],
        summary: '删除班级',
        description: '删除班级',
        params: {
            type: 'object',
            required: ['classId'],
            properties: {
                classId: { 
                    type: 'string',
                    description: '班级ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for adding student to class
     */
    addStudentToClassSchema: {
        tags: ['classes'],
        summary: '添加学生到班级',
        description: '将学生添加到班级',
        params: {
            type: 'object',
            required: ['classId'],
            properties: {
                classId: { 
                    type: 'string',
                    description: '班级ID'
                }
            }
        },
        body: {
            type: 'object',
            required: ['studentId'],
            properties: {
                studentId: { 
                    type: 'string',
                    description: '学生ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for removing student from class
     */
    removeStudentFromClassSchema: {
        tags: ['classes'],
        summary: '从班级移除学生',
        description: '从班级中移除学生',
        params: {
            type: 'object',
            required: ['classId', 'studentId'],
            properties: {
                classId: { 
                    type: 'string',
                    description: '班级ID'
                },
                studentId: { 
                    type: 'string',
                    description: '学生ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    }
};

export default classesSchema;
