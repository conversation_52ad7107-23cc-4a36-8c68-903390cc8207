"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2464],{8643:(e,s,a)=>{a.d(s,{Ke:()=>i,Nt:()=>t,R6:()=>r});var l=a(88106);let t=l.bL,r=l.R6,i=l.Ke},63375:(e,s,a)=>{a.d(s,{A:()=>d});var l=a(95155),t=a(12115),r=a(59409),i=a(59434),n=a(6658);let d=(0,t.memo)(function(e){let{teacher:s,setTeacher:a,width:d="w-full",placeholder:c="选择人员",className:x="",showAllOption:m=!0,allOptionText:h="全部人员",allOptionValue:o="all",teacherList:j,disabled:u=!1}=e,{data:g,isLoading:p,error:v}=(0,n.X)(),y=(0,t.useCallback)(e=>{a(e)},[a]),f=(0,t.useCallback)(()=>(0,i.cn)("h-9 ".concat(d),x),[d,x]),N=j||g;return(0,l.jsxs)(r.l6,{value:s,onValueChange:y,disabled:u||p,children:[(0,l.jsx)(r.bq,{className:f(),children:(0,l.jsx)(r.yv,{placeholder:c})}),(0,l.jsxs)(r.gC,{children:[v&&(0,l.jsx)(r.eb,{value:"error",disabled:!0,children:String(v)}),p&&(0,l.jsx)(r.eb,{value:"loading",disabled:!0,children:"加载中..."}),!p&&!v&&(0,l.jsxs)(l.Fragment,{children:[m&&(0,l.jsx)(r.eb,{value:o,children:h}),null==N?void 0:N.map(e=>(0,l.jsx)(r.eb,{value:e.id,children:e.name},e.id))]})]})]})})},80333:(e,s,a)=>{a.d(s,{d:()=>n});var l=a(95155),t=a(12115),r=a(4884),i=a(59434);let n=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)(r.bL,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...t,ref:s,children:(0,l.jsx)(r.zi,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});n.displayName=r.bL.displayName},82245:(e,s,a)=>{a.d(s,{A:()=>g});var l=a(95155),t=a(8643),r=a(62523),i=a(85057),n=a(80333),d=a(59434),c=a(66474),x=a(69074),m=a(14186),h=a(55670),o=a(9607),j=a(72713),u=a(46102);let g=function(e){let{isAdvancedOpen:s,setIsAdvancedOpen:a,formData:g,setFormData:p}=e;return(0,l.jsx)(u.Bc,{children:(0,l.jsxs)(t.Nt,{open:s,onOpenChange:a,children:[(0,l.jsxs)(t.R6,{className:"flex items-center justify-between w-full p-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{className:"font-medium text-sm",children:"高级选项"}),(0,l.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"配置额外的课程设置"})]}),(0,l.jsx)(c.A,{className:(0,d.cn)("h-4 w-4 text-gray-500 dark:text-gray-400 transition-transform duration-200",s?"transform rotate-180":"")})]}),(0,l.jsxs)(t.Ke,{className:"p-4 bg-white dark:bg-gray-950 divide-y divide-gray-100 dark:divide-gray-800",children:[(0,l.jsxs)("div",{className:"py-4 space-y-4 first:pt-0",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(x.A,{className:"h-4 w-4 text-primary"}),(0,l.jsx)(i.J,{htmlFor:"allow-reservation",className:"font-medium",children:"开放预约"})]}),(0,l.jsx)(n.d,{id:"allow-reservation",checked:g.isReserve,onCheckedChange:e=>p({...g,isReserve:e})})]}),g.isReserve&&(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 pl-6 mt-2 animate-in fade-in slide-in-from-top-2 duration-200",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(i.J,{htmlFor:"start-time",className:"text-sm text-gray-500 dark:text-gray-400",children:"开始时间 (小时)"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(m.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(r.p,{id:"start-time",type:"text",placeholder:"例如: 24",className:"pl-10",value:g.appointmentStartTime||"",onChange:e=>p({...g,appointmentStartTime:e.target.value})})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(i.J,{htmlFor:"end-time",className:"text-sm text-gray-500 dark:text-gray-400",children:"截止时间 (小时)"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(m.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(r.p,{id:"end-time",type:"text",placeholder:"例如: 2",className:"pl-10",value:g.appointmentEndTime||"",onChange:e=>p({...g,appointmentEndTime:e.target.value})})]})]})]})]}),(0,l.jsx)("div",{className:"py-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(h.A,{className:"h-4 w-4 text-primary"}),(0,l.jsx)(i.J,{htmlFor:"student-scan",className:"font-medium",children:"学员扫码考勤"}),(0,l.jsxs)(u.m_,{children:[(0,l.jsx)(u.k$,{asChild:!0,children:(0,l.jsx)("span",{className:"inline-flex h-4 w-4 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 text-xs text-gray-500 dark:text-gray-400 cursor-help",children:"?"})}),(0,l.jsx)(u.ZI,{children:(0,l.jsx)("p",{className:"text-xs",children:"允许学员通过扫描二维码进行考勤签到"})})]})]}),(0,l.jsx)(n.d,{id:"student-scan",checked:g.isQRCodeAttendance,onCheckedChange:e=>p({...g,isQRCodeAttendance:e})})]})}),(0,l.jsx)("div",{className:"py-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(o.A,{className:"h-4 w-4 text-primary"}),(0,l.jsx)(i.J,{htmlFor:"auto-attendance",className:"font-medium",children:"系统自动考勤"}),(0,l.jsxs)(u.m_,{children:[(0,l.jsx)(u.k$,{asChild:!0,children:(0,l.jsx)("span",{className:"inline-flex h-4 w-4 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 text-xs text-gray-500 dark:text-gray-400 cursor-help",children:"?"})}),(0,l.jsx)(u.ZI,{children:(0,l.jsx)("p",{className:"text-xs",children:"系统将在课程开始时自动为学员记录考勤"})})]})]}),(0,l.jsx)(n.d,{id:"auto-attendance",checked:g.isAutoCheckIn,onCheckedChange:e=>p({...g,isAutoCheckIn:e})})]})}),(0,l.jsxs)("div",{className:"py-4 space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(x.A,{className:"h-4 w-4 text-primary"}),(0,l.jsx)(i.J,{htmlFor:"allow-leave",className:"font-medium",children:"开放请假"})]}),(0,l.jsx)(n.d,{id:"allow-leave",checked:g.isOnLeave,onCheckedChange:e=>p({...g,isOnLeave:e})})]}),g.isOnLeave&&(0,l.jsxs)("div",{className:"pl-6 space-y-2 animate-in fade-in slide-in-from-top-2 duration-200",children:[(0,l.jsx)(i.J,{htmlFor:"leave-deadline",className:"text-sm text-gray-500 dark:text-gray-400",children:"截止时间 (小时)"}),(0,l.jsxs)("div",{className:"relative max-w-xs",children:[(0,l.jsx)(m.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(r.p,{id:"leave-deadline",type:"text",placeholder:"例如: 2",className:"pl-10",value:g.leaveDeadline||"",onChange:e=>p({...g,leaveDeadline:e.target.value})})]})]})]}),(0,l.jsx)("div",{className:"py-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(j.A,{className:"h-4 w-4 text-primary"}),(0,l.jsx)(i.J,{htmlFor:"show-cycle",className:"font-medium",children:"显示周期数"}),(0,l.jsxs)(u.m_,{children:[(0,l.jsx)(u.k$,{asChild:!0,children:(0,l.jsx)("span",{className:"inline-flex h-4 w-4 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 text-xs text-gray-500 dark:text-gray-400 cursor-help",children:"?"})}),(0,l.jsx)(u.ZI,{children:(0,l.jsx)("p",{className:"text-xs",children:"在课程信息中显示当前周期数"})})]})]}),(0,l.jsx)(n.d,{id:"show-cycle",checked:g.isShowWeekCount,onCheckedChange:e=>p({...g,isShowWeekCount:e})})]})})]})]})})}},85057:(e,s,a)=>{a.d(s,{J:()=>c});var l=a(95155),t=a(12115),r=a(40968),i=a(74466),n=a(59434);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)(r.b,{ref:s,className:(0,n.cn)(d(),a),...t})});c.displayName=r.b.displayName},92164:(e,s,a)=>{a.d(s,{A:()=>d});var l=a(95155),t=a(12115),r=a(59409),i=a(59434),n=a(74651);let d=(0,t.memo)(function(e){let{value:s,onChange:a,width:d="w-full",placeholder:c="选择课程",className:x="",showAllOption:m=!1,allOptionText:h="全部课程",allOptionValue:o="all",list:j,disabled:u=!1}=e,{data:g,isLoading:p,error:v}=(0,n.zo)({}),y=(0,t.useCallback)(e=>{a(e)},[a]),f=(0,t.useCallback)(()=>(0,i.cn)("h-9 ".concat(d),x),[d,x]),N=j||g;return(0,l.jsxs)(r.l6,{value:s,onValueChange:y,disabled:u||p,children:[(0,l.jsx)(r.bq,{className:f(),children:(0,l.jsx)(r.yv,{placeholder:c})}),(0,l.jsxs)(r.gC,{children:[v&&(0,l.jsx)(r.eb,{value:"error",disabled:!0,children:String(v)}),p&&(0,l.jsx)(r.eb,{value:"loading",disabled:!0,children:"加载中..."}),!p&&!v&&(0,l.jsxs)(l.Fragment,{children:[m&&(0,l.jsx)(r.eb,{value:o,children:h}),null==N?void 0:N.map(e=>(0,l.jsx)(r.eb,{value:e.id,children:e.name},e.id))]})]})]})})}}]);