import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

/**
 * 为指定角色分配所有权限
 * 使用方法：node scripts/assign-all-permissions.js
 */
async function main() {
    const roleId = 'f550383d-fc45-4c5f-9851-9c9f85eb0f56';
    
    try {
        console.log('开始为角色分配所有权限...');
        
        // 检查角色是否存在
        const role = await prisma.role.findUnique({
            where: { id: roleId }
        });
        
        if (!role) {
            console.error(`角色不存在: ${roleId}`);
            process.exit(1);
        }
        
        console.log(`找到角色: ${role.name} (${role.code})`);
        
        // 获取所有权限
        const permissions = await prisma.permission.findMany();
        
        if (permissions.length === 0) {
            console.error('系统中没有权限记录');
            process.exit(1);
        }
        
        console.log(`找到 ${permissions.length} 个权限`);
        
        // 获取角色当前权限
        const currentRolePermissions = await prisma.rolePermission.findMany({
            where: { roleId },
            select: { permissionId: true }
        });
        
        const currentPermissionIds = new Set(currentRolePermissions.map(rp => rp.permissionId));
        
        // 找出需要添加的权限
        const permissionsToAdd = permissions.filter(p => !currentPermissionIds.has(p.id));
        
        if (permissionsToAdd.length === 0) {
            console.log('角色已经拥有所有权限');
            process.exit(0);
        }
        
        console.log(`需要添加 ${permissionsToAdd.length} 个权限`);
        
        // 批量创建角色权限
        const rolePermissions = permissionsToAdd.map(permission => ({
            id: uuidv4(),
            roleId,
            permissionId: permission.id
        }));
        
        await prisma.rolePermission.createMany({
            data: rolePermissions,
            skipDuplicates: true
        });
        
        console.log('权限分配完成！');
        console.log(`成功为角色 ${role.name} 添加了 ${permissionsToAdd.length} 个权限`);
        
        // 打印新添加的权限列表
        console.log('\n新添加的权限:');
        permissionsToAdd.forEach(permission => {
            console.log(`- ${permission.name} (${permission.code})`);
        });
        
    } catch (error) {
        console.error('分配权限时发生错误:', error);
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

main();