"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3611],{43611:(e,r,a)=>{a.r(r),a.d(r,{default:()=>u});var l=a(95155),s=a(9110),n=a(89917),t=a(6874),i=a.n(t),c=a(61809),d=a(57001);let o=[{accessorKey:"courseName",header:"课程名称",cell:e=>{let{row:r}=e;return(0,l.jsx)("div",{className:"font-medium",children:r.original.courses.name})}},{accessorKey:"currentCycleNumber",header:"当前周期数",cell:e=>{let{row:r}=e;return(0,l.jsx)("div",{className:"lowercase",children:r.original.currentWeeks})}},{accessorKey:"classTime",header:"上课时间",cell:e=>{let{row:r}=e,a=r.original.startDate,s=r.original.startTime,n=r.original.endTime,t=(0,c.Y)(a,"yyyy-MM-dd (EEEE)");return(0,l.jsxs)("div",{className:"lowercase",children:[t," ",s,"-",n]})}},{accessorKey:"teacher",header:"主讲老师",cell:e=>{let{row:r}=e;return(0,l.jsx)("div",{className:"capitalize",children:r.original.teacher.name})}},{id:"actions",header:"操作",cell:e=>{let{row:r}=e;return r.original,(0,l.jsx)("div",{className:"flex space-x-2",children:(0,l.jsx)(i(),{href:"/academic/schedule/".concat(r.original.id),children:(0,l.jsx)(d.p,{icon:n.A,tooltipText:"编辑章节"})})})}}];function u(e){let{classesSchedule:r}=e;return r?(0,l.jsx)("div",{children:(0,l.jsx)(s.b,{columns:o,data:r||[]},"classes[id]lessonDetails")}):(0,l.jsx)(l.Fragment,{children:"loading..."})}},57001:(e,r,a)=>{a.d(r,{p:()=>t});var l=a(95155),s=a(30285),n=a(46102);function t(e){let{icon:r,tooltipText:a,tooltipSide:t="top",tooltipAlign:i="center",delayDuration:c=300,variant:d="ghost",size:o="icon",className:u="h-8 w-8 hover:bg-muted",...h}=e;return(0,l.jsx)(n.Bc,{delayDuration:c,children:(0,l.jsxs)(n.m_,{children:[(0,l.jsx)(n.k$,{asChild:!0,children:(0,l.jsx)(s.$,{variant:d,size:o,className:u,...h,children:(0,l.jsx)(r,{className:"h-4 w-4 text-muted-foreground"})})}),(0,l.jsx)(n.ZI,{side:t,align:i,className:"font-medium text-xs px-3 py-1.5",children:(0,l.jsx)("p",{children:a})})]})})}},61809:(e,r,a)=>{a.d(r,{Y:()=>t});var l=a(44861),s=a(73168),n=a(24122);let t=function(e){let r,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"yyyy-MM-dd",t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(null==e)return t;try{if("string"==typeof e)r=new Date(e);else if("number"==typeof e)r=new Date(e);else{if(!(e instanceof Date))return t;r=e}if(!(0,l.f)(r))return t;return(0,s.GP)(r,a,{locale:n.g})}catch(e){return console.error("Date formatting error:",e),t}}},89917:(e,r,a)=>{a.d(r,{A:()=>l});let l=(0,a(19946).A)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])}}]);