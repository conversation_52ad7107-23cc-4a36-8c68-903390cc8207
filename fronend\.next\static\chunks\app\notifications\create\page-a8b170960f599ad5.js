(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3130],{17759:(e,r,t)=>{"use strict";t.d(r,{C5:()=>v,MJ:()=>h,Rr:()=>b,eI:()=>x,lR:()=>p,lV:()=>o,zB:()=>u});var s=t(95155),i=t(12115),n=t(99708),l=t(62177),a=t(59434),d=t(85057);let o=l.Op,c=i.createContext({}),u=e=>{let{...r}=e;return(0,s.jsx)(c.Provider,{value:{name:r.name},children:(0,s.jsx)(l.xI,{...r})})},f=()=>{let e=i.useContext(c),r=i.useContext(m),{getFieldState:t,formState:s}=(0,l.xW)(),n=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:a}=r;return{id:a,name:e.name,formItemId:"".concat(a,"-form-item"),formDescriptionId:"".concat(a,"-form-item-description"),formMessageId:"".concat(a,"-form-item-message"),...n}},m=i.createContext({}),x=i.forwardRef((e,r)=>{let{className:t,...n}=e,l=i.useId();return(0,s.jsx)(m.Provider,{value:{id:l},children:(0,s.jsx)("div",{ref:r,className:(0,a.cn)("space-y-2",t),...n})})});x.displayName="FormItem";let p=i.forwardRef((e,r)=>{let{className:t,...i}=e,{error:n,formItemId:l}=f();return(0,s.jsx)(d.J,{ref:r,className:(0,a.cn)(n&&"text-destructive",t),htmlFor:l,...i})});p.displayName="FormLabel";let h=i.forwardRef((e,r)=>{let{...t}=e,{error:i,formItemId:l,formDescriptionId:a,formMessageId:d}=f();return(0,s.jsx)(n.DX,{ref:r,id:l,"aria-describedby":i?"".concat(a," ").concat(d):"".concat(a),"aria-invalid":!!i,...t})});h.displayName="FormControl";let b=i.forwardRef((e,r)=>{let{className:t,...i}=e,{formDescriptionId:n}=f();return(0,s.jsx)("p",{ref:r,id:n,className:(0,a.cn)("text-sm text-muted-foreground",t),...i})});b.displayName="FormDescription";let v=i.forwardRef((e,r)=>{var t;let{className:i,children:n,...l}=e,{error:d,formMessageId:o}=f(),c=d?String(null!==(t=null==d?void 0:d.message)&&void 0!==t?t:""):n;return c?(0,s.jsx)("p",{ref:r,id:o,className:(0,a.cn)("text-sm font-medium text-destructive",i),...l,children:c}):null});v.displayName="FormMessage"},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o,r:()=>d});var s=t(95155),i=t(12115),n=t(99708),l=t(74466),a=t(59434);let d=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=i.forwardRef((e,r)=>{let{className:t,variant:i,size:l,asChild:o=!1,...c}=e,u=o?n.DX:"button";return(0,s.jsx)(u,{className:(0,a.cn)(d({variant:i,size:l,className:t})),ref:r,...c})});o.displayName="Button"},30356:(e,r,t)=>{"use strict";t.d(r,{C:()=>o,z:()=>d});var s=t(95155),i=t(12115),n=t(54059),l=t(9428),a=t(59434);let d=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,s.jsx)(n.bL,{className:(0,a.cn)("grid gap-2",t),...i,ref:r})});d.displayName=n.bL.displayName;let o=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,s.jsx)(n.q7,{ref:r,className:(0,a.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),...i,children:(0,s.jsx)(n.C1,{className:"flex items-center justify-center",children:(0,s.jsx)(l.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});o.displayName=n.q7.displayName},30560:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>R});var s=t(95155),i=t(12115),n=t(30285),l=t(66695),a=t(62523),d=t(88539),o=t(30356),c=t(85057),u=t(47262),f=t(80244),m=t(59434);let x=i.forwardRef((e,r)=>{let{className:t,children:i,...n}=e;return(0,s.jsxs)(f.bL,{ref:r,className:(0,m.cn)("relative overflow-hidden",t),...n,children:[(0,s.jsx)(f.LM,{className:"h-full w-full rounded-[inherit]",children:i}),(0,s.jsx)(p,{}),(0,s.jsx)(f.OK,{})]})});x.displayName=f.bL.displayName;let p=i.forwardRef((e,r)=>{let{className:t,orientation:i="vertical",...n}=e;return(0,s.jsx)(f.VM,{ref:r,orientation:i,className:(0,m.cn)("flex touch-none select-none transition-colors","vertical"===i&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===i&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),...n,children:(0,s.jsx)(f.lr,{className:"relative flex-1 rounded-full bg-border"})})});p.displayName=f.VM.displayName;var h=t(62177),b=t(90221),v=t(55594),g=t(17759),j=t(48436),y=t(35695),N=t(6658),w=t(57786);let C=v.Ik({title:v.Yj().min(1,"标题不能为空"),content:v.Yj().min(1,"内容不能为空"),recipientType:v.k5(["all","individual"]),recipientIds:v.YO(v.Yj()).optional()}),R=function(){let e=(0,y.useRouter)(),{data:r,isLoading:t}=(0,N.X)(),[i,{isLoading:f}]=(0,w.Gw)(),m=(0,h.mN)({resolver:(0,b.u)(C),defaultValues:{title:"",content:"",recipientType:"all",recipientIds:[]}}),p=async r=>{try{let t={title:r.title,content:r.content,recipientType:r.recipientType,recipientIds:"individual"===r.recipientType?r.recipientIds:void 0};await i(t).unwrap(),j.l.success("通知发送成功"),e.push("/notifications/list")}catch(e){console.error("发送通知失败:",e),j.l.error("发送通知失败")}};return(0,s.jsx)("div",{className:"container mx-auto py-6",children:(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsx)(l.ZB,{children:"创建通知"}),(0,s.jsx)(l.BT,{children:"创建一条新的通知消息"})]}),(0,s.jsx)(g.lV,{...m,children:(0,s.jsxs)("form",{onSubmit:m.handleSubmit(p),children:[(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsx)(g.zB,{control:m.control,name:"title",render:e=>{let{field:r}=e;return(0,s.jsxs)(g.eI,{children:[(0,s.jsx)(g.lR,{children:"通知标题"}),(0,s.jsx)(g.MJ,{children:(0,s.jsx)(a.p,{placeholder:"请输入通知标题",...r})}),(0,s.jsx)(g.C5,{})]})}}),(0,s.jsx)(g.zB,{control:m.control,name:"content",render:e=>{let{field:r}=e;return(0,s.jsxs)(g.eI,{children:[(0,s.jsx)(g.lR,{children:"通知内容"}),(0,s.jsx)(g.MJ,{children:(0,s.jsx)(d.T,{placeholder:"请输入通知内容",className:"min-h-[120px]",...r})}),(0,s.jsx)(g.C5,{})]})}}),(0,s.jsx)(g.zB,{control:m.control,name:"recipientType",render:e=>{let{field:r}=e;return(0,s.jsxs)(g.eI,{className:"space-y-3",children:[(0,s.jsx)(g.lR,{children:"接收者"}),(0,s.jsx)(g.MJ,{children:(0,s.jsxs)(o.z,{onValueChange:r.onChange,defaultValue:r.value,className:"flex flex-col space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(o.C,{value:"all",id:"all"}),(0,s.jsx)(c.J,{htmlFor:"all",children:"所有人"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(o.C,{value:"individual",id:"individual"}),(0,s.jsx)(c.J,{htmlFor:"individual",children:"指定人员"})]})]})}),(0,s.jsx)(g.C5,{})]})}}),"individual"===m.watch("recipientType")&&(0,s.jsx)(g.zB,{control:m.control,name:"recipientIds",render:e=>{let{field:t}=e;return(0,s.jsxs)(g.eI,{children:[(0,s.jsx)(g.lR,{children:"选择接收人"}),(0,s.jsx)(g.MJ,{children:(0,s.jsx)(x,{className:"h-[200px] rounded-md border p-4",children:(0,s.jsx)("div",{className:"space-y-2",children:null==r?void 0:r.map(e=>{var r;return(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(u.S,{id:e.id,checked:null===(r=t.value)||void 0===r?void 0:r.includes(e.id),onCheckedChange:r=>{let s=t.value||[];r?t.onChange([...s,e.id]):t.onChange(s.filter(r=>r!==e.id))}}),(0,s.jsx)(c.J,{htmlFor:e.id,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:e.name})]},e.id)})})})}),(0,s.jsx)(g.C5,{})]})}})]}),(0,s.jsxs)(l.wL,{className:"flex justify-between",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:()=>e.push("/notifications/list"),type:"button",children:"取消"}),(0,s.jsx)(n.$,{type:"submit",disabled:f||t,children:f?"发送中...":"发送通知"})]})]})})]})})}},47262:(e,r,t)=>{"use strict";t.d(r,{S:()=>d});var s=t(95155),i=t(12115),n=t(76981),l=t(5196),a=t(59434);let d=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,s.jsx)(n.bL,{ref:r,className:(0,a.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...i,children:(0,s.jsx)(n.C1,{className:(0,a.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})})});d.displayName=n.bL.displayName},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(52596),i=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,i.QP)((0,s.$)(r))}},62523:(e,r,t)=>{"use strict";t.d(r,{p:()=>l});var s=t(95155),i=t(12115),n=t(59434);let l=i.forwardRef((e,r)=>{let{className:t,type:i,...l}=e;return(0,s.jsx)("input",{type:i,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...l})});l.displayName="Input"},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>l,aR:()=>a,wL:()=>u});var s=t(95155),i=t(12115),n=t(59434);let l=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...i})});l.displayName="Card";let a=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...i})});a.displayName="CardHeader";let d=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...i})});d.displayName="CardTitle";let o=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...i})});o.displayName="CardDescription";let c=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...i})});c.displayName="CardContent";let u=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...i})});u.displayName="CardFooter"},66815:(e,r,t)=>{Promise.resolve().then(t.bind(t,30560))},85057:(e,r,t)=>{"use strict";t.d(r,{J:()=>o});var s=t(95155),i=t(12115),n=t(40968),l=t(74466),a=t(59434);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,s.jsx)(n.b,{ref:r,className:(0,a.cn)(d(),t),...i})});o.displayName=n.b.displayName},88539:(e,r,t)=>{"use strict";t.d(r,{T:()=>l});var s=t(95155),i=t(12115),n=t(59434);let l=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,s.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...i})});l.displayName="Textarea"}},e=>{var r=r=>e(e.s=r);e.O(0,[4277,8687,4201,4540,5589,5602,380,9624,6315,7358],()=>r(66815)),_N_E=e.O()}]);