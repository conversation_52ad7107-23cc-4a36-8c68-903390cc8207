"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1037,5799,8180],{15452:(e,t,l)=>{l.d(t,{G$:()=>H,Hs:()=>j,UC:()=>et,VY:()=>ea,ZL:()=>Q,bL:()=>X,bm:()=>er,hE:()=>el,hJ:()=>ee,l9:()=>K});var a=l(12115),r=l(85185),o=l(6101),n=l(46081),s=l(61285),d=l(5845),i=l(19178),c=l(25519),u=l(34378),b=l(28905),g=l(63655),p=l(92293),m=l(93795),x=l(38168),f=l(99708),h=l(95155),v="Dialog",[y,j]=(0,n.A)(v),[w,N]=y(v),C=e=>{let{__scopeDialog:t,children:l,open:r,defaultOpen:o,onOpenChange:n,modal:i=!0}=e,c=a.useRef(null),u=a.useRef(null),[b=!1,g]=(0,d.i)({prop:r,defaultProp:o,onChange:n});return(0,h.jsx)(w,{scope:t,triggerRef:c,contentRef:u,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:b,onOpenChange:g,onOpenToggle:a.useCallback(()=>g(e=>!e),[g]),modal:i,children:l})};C.displayName=v;var k="DialogTrigger",D=a.forwardRef((e,t)=>{let{__scopeDialog:l,...a}=e,n=N(k,l),s=(0,o.s)(t,n.triggerRef);return(0,h.jsx)(g.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":Z(n.open),...a,ref:s,onClick:(0,r.m)(e.onClick,n.onOpenToggle)})});D.displayName=k;var R="DialogPortal",[I,E]=y(R,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:l,children:r,container:o}=e,n=N(R,t);return(0,h.jsx)(I,{scope:t,forceMount:l,children:a.Children.map(r,e=>(0,h.jsx)(b.C,{present:l||n.open,children:(0,h.jsx)(u.Z,{asChild:!0,container:o,children:e})}))})};O.displayName=R;var _="DialogOverlay",A=a.forwardRef((e,t)=>{let l=E(_,e.__scopeDialog),{forceMount:a=l.forceMount,...r}=e,o=N(_,e.__scopeDialog);return o.modal?(0,h.jsx)(b.C,{present:a||o.open,children:(0,h.jsx)(F,{...r,ref:t})}):null});A.displayName=_;var F=a.forwardRef((e,t)=>{let{__scopeDialog:l,...a}=e,r=N(_,l);return(0,h.jsx)(m.A,{as:f.DX,allowPinchZoom:!0,shards:[r.contentRef],children:(0,h.jsx)(g.sG.div,{"data-state":Z(r.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),z="DialogContent",P=a.forwardRef((e,t)=>{let l=E(z,e.__scopeDialog),{forceMount:a=l.forceMount,...r}=e,o=N(z,e.__scopeDialog);return(0,h.jsx)(b.C,{present:a||o.open,children:o.modal?(0,h.jsx)(M,{...r,ref:t}):(0,h.jsx)(T,{...r,ref:t})})});P.displayName=z;var M=a.forwardRef((e,t)=>{let l=N(z,e.__scopeDialog),n=a.useRef(null),s=(0,o.s)(t,l.contentRef,n);return a.useEffect(()=>{let e=n.current;if(e)return(0,x.Eq)(e)},[]),(0,h.jsx)(G,{...e,ref:s,trapFocus:l.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=l.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,l=0===t.button&&!0===t.ctrlKey;(2===t.button||l)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=a.forwardRef((e,t)=>{let l=N(z,e.__scopeDialog),r=a.useRef(!1),o=a.useRef(!1);return(0,h.jsx)(G,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,n;null===(a=e.onCloseAutoFocus)||void 0===a||a.call(e,t),t.defaultPrevented||(r.current||null===(n=l.triggerRef.current)||void 0===n||n.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var a,n;null===(a=e.onInteractOutside)||void 0===a||a.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let s=t.target;(null===(n=l.triggerRef.current)||void 0===n?void 0:n.contains(s))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),G=a.forwardRef((e,t)=>{let{__scopeDialog:l,trapFocus:r,onOpenAutoFocus:n,onCloseAutoFocus:s,...d}=e,u=N(z,l),b=a.useRef(null),g=(0,o.s)(t,b);return(0,p.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:n,onUnmountAutoFocus:s,children:(0,h.jsx)(i.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Z(u.open),...d,ref:g,onDismiss:()=>u.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(J,{titleId:u.titleId}),(0,h.jsx)(Y,{contentRef:b,descriptionId:u.descriptionId})]})]})}),S="DialogTitle",q=a.forwardRef((e,t)=>{let{__scopeDialog:l,...a}=e,r=N(S,l);return(0,h.jsx)(g.sG.h2,{id:r.titleId,...a,ref:t})});q.displayName=S;var B="DialogDescription",L=a.forwardRef((e,t)=>{let{__scopeDialog:l,...a}=e,r=N(B,l);return(0,h.jsx)(g.sG.p,{id:r.descriptionId,...a,ref:t})});L.displayName=B;var V="DialogClose",U=a.forwardRef((e,t)=>{let{__scopeDialog:l,...a}=e,o=N(V,l);return(0,h.jsx)(g.sG.button,{type:"button",...a,ref:t,onClick:(0,r.m)(e.onClick,()=>o.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}U.displayName=V;var $="DialogTitleWarning",[H,W]=(0,n.q)($,{contentName:z,titleName:S,docsSlug:"dialog"}),J=e=>{let{titleId:t}=e,l=W($),r="`".concat(l.contentName,"` requires a `").concat(l.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(l.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(l.docsSlug);return a.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},Y=e=>{let{contentRef:t,descriptionId:l}=e,r=W("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return a.useEffect(()=>{var e;let a=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");l&&a&&!document.getElementById(l)&&console.warn(o)},[o,t,l]),null},X=C,K=D,Q=O,ee=A,et=P,el=q,ea=L,er=U},54165:(e,t,l)=>{l.d(t,{Cf:()=>g,Es:()=>m,HM:()=>u,L3:()=>x,c7:()=>p,lG:()=>d,rr:()=>f,zM:()=>i});var a=l(95155),r=l(12115),o=l(15452),n=l(54416),s=l(59434);let d=o.bL,i=o.l9,c=o.ZL,u=o.bm,b=r.forwardRef((e,t)=>{let{className:l,...r}=e;return(0,a.jsx)(o.hJ,{ref:t,className:(0,s.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",l),...r})});b.displayName=o.hJ.displayName;let g=r.forwardRef((e,t)=>{let{className:l,children:r,...d}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(b,{}),(0,a.jsxs)(o.UC,{ref:t,className:(0,s.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",l),...d,children:[r,(0,a.jsxs)(o.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});g.displayName=o.UC.displayName;let p=e=>{let{className:t,...l}=e;return(0,a.jsx)("div",{className:(0,s.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...l})};p.displayName="DialogHeader";let m=e=>{let{className:t,...l}=e;return(0,a.jsx)("div",{className:(0,s.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...l})};m.displayName="DialogFooter";let x=r.forwardRef((e,t)=>{let{className:l,...r}=e;return(0,a.jsx)(o.hE,{ref:t,className:(0,s.cn)("text-lg font-semibold leading-none tracking-tight",l),...r})});x.displayName=o.hE.displayName;let f=r.forwardRef((e,t)=>{let{className:l,...r}=e;return(0,a.jsx)(o.VY,{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",l),...r})});f.displayName=o.VY.displayName},54416:(e,t,l)=>{l.d(t,{A:()=>a});let a=(0,l(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},55799:(e,t,l)=>{l.r(t),l.d(t,{default:()=>p});var a=l(95155),r=l(12115);let o=(0,l(19946).A)("UserRoundCog",[["path",{d:"M2 21a8 8 0 0 1 10.434-7.62",key:"1yezr2"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["circle",{cx:"18",cy:"18",r:"3",key:"1xkwt0"}],["path",{d:"m19.5 14.3-.4.9",key:"1eb35c"}],["path",{d:"m16.9 20.8-.4.9",key:"dfjc4z"}],["path",{d:"m21.7 19.5-.9-.4",key:"q4dx6b"}],["path",{d:"m15.2 16.9-.9-.4",key:"1r0w5f"}],["path",{d:"m21.7 16.5-.9.4",key:"1knoei"}],["path",{d:"m15.2 19.1-.9.4",key:"j188fs"}],["path",{d:"m19.5 21.7-.4-.9",key:"1tonu5"}],["path",{d:"m16.9 15.2-.4-.9",key:"699xu"}]]);var n=l(30285),s=l(54165),d=l(59409),i=l(57141);let c=function(e){let{open:t,onOpenChange:l,studentType:o,handleSubmit:c}=e,[u,b]=r.useState("");return t?(0,a.jsx)(s.lG,{open:t,onOpenChange:l,children:(0,a.jsxs)(s.Cf,{className:"sm:max-w-[425px] rounded-lg border shadow-lg",children:[(0,a.jsx)(s.c7,{className:"pb-4 border-b",children:(0,a.jsx)(s.L3,{className:"text-xl font-medium",children:"修改学员类型"})}),(0,a.jsxs)("div",{className:"py-6",children:[(0,a.jsx)("label",{className:"text-sm text-gray-500 mb-2 block",children:"请选择新的学员类型"}),(0,a.jsxs)(d.l6,{defaultValue:o,onValueChange:e=>b(e),children:[(0,a.jsx)(d.bq,{className:"w-full h-10 rounded-md border border-gray-300 focus:ring-2 focus:ring-primary/20",children:(0,a.jsx)(d.yv,{placeholder:"请选择学员类型"})}),(0,a.jsx)(d.gC,{className:"max-h-60 overflow-auto rounded-md shadow-md",children:Object.entries(i.IC).map(e=>{let[t,l]=e;return(0,a.jsx)(d.eb,{value:t,className:"cursor-pointer hover:bg-gray-100",children:l.label},t)})})]})]}),(0,a.jsxs)(s.Es,{className:"pt-4 border-t flex justify-end gap-3",children:[(0,a.jsx)(n.$,{variant:"outline",onClick:()=>l(!1),className:"px-5 rounded-md hover:bg-gray-100",children:"取消"}),(0,a.jsx)(n.$,{onClick:()=>{if(""===o){alert("请选择学员类型");return}c&&c({selectStudentType:u}),b("")},className:"px-5 rounded-md shadow-sm",children:"确定"})]})]})}):null};var u=l(48436),b=l(95728),g=l(57001);let p=function(e){let{studentId:t,studentType:l}=e,[n]=(0,b.zy)(),[s,d]=r.useState(!1);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.p,{icon:o,tooltipText:"修改学员状态",onClick:()=>d(!0)}),s&&(0,a.jsx)(c,{open:s,onOpenChange:d,studentType:l,handleSubmit:e=>{n({studentId:t,data:{type:e.selectStudentType}}).unwrap().then(e=>{u.l.success("更新学员成功."),d(!1)}).catch(e=>{u.l.error("更新学员失败!")})}})]})}},57001:(e,t,l)=>{l.d(t,{p:()=>n});var a=l(95155),r=l(30285),o=l(46102);function n(e){let{icon:t,tooltipText:l,tooltipSide:n="top",tooltipAlign:s="center",delayDuration:d=300,variant:i="ghost",size:c="icon",className:u="h-8 w-8 hover:bg-muted",...b}=e;return(0,a.jsx)(o.Bc,{delayDuration:d,children:(0,a.jsxs)(o.m_,{children:[(0,a.jsx)(o.k$,{asChild:!0,children:(0,a.jsx)(r.$,{variant:i,size:c,className:u,...b,children:(0,a.jsx)(t,{className:"h-4 w-4 text-muted-foreground"})})}),(0,a.jsx)(o.ZI,{side:n,align:s,className:"font-medium text-xs px-3 py-1.5",children:(0,a.jsx)("p",{children:l})})]})})}},57141:(e,t,l)=>{l.d(t,{C9:()=>r,DT:()=>s,I2:()=>n,IC:()=>b,N4:()=>c,fb:()=>g,lc:()=>a,oD:()=>d,u7:()=>u,uq:()=>o,x9:()=>i});let a={"limited-sessions":{label:"限次",color:"capitalize font-normal bg-blue-50 text-blue-700 hover:bg-blue-100 px-2.5 py-0.5 rounded-md"},"limited-time-and-count":{label:"时次限",color:"capitalize font-normal bg-violet-50 text-violet-700 hover:bg-violet-100 px-2.5 py-0.5 rounded-md"},default:{label:"未知套餐",color:"capitalize font-normal bg-slate-100 text-slate-700 hover:bg-slate-200 px-2.5 py-0.5 rounded-md"}},r={cash:{label:"现金",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},alipay:{label:"支付宝",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},wechat:{label:"微信",color:"bg-green-100 text-green-800 hover:bg-green-200"},card:{label:"银行卡",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},other:{label:"其他",color:"bg-slate-100 text-slate-800 hover:bg-slate-200"}},o=[{id:"wechat",label:"微信"},{id:"alipay",label:"支付宝"},{id:"card",label:"银行转账"},{id:"cash",label:"现金"},{id:"other",label:"其他"}],n={done:{label:"完成",color:"bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400"},arrears:{label:"欠费",color:"bg-amber-100 text-amber-800 hover:bg-amber-200 dark:bg-amber-900/30 dark:text-amber-400"},refunded:{label:"退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400"},default:{label:"未知",color:"bg-slate-100 text-slate-800 hover:bg-slate-200 dark:bg-slate-800/50 dark:text-slate-400"}},s={active:{badgeClass:"bg-emerald-50 text-emerald-700 hover:bg-emerald-100 border-emerald-200",dotClass:"bg-emerald-500",text:"正常"},expired:{badgeClass:"bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200",dotClass:"bg-amber-500",text:"已到期"},refunded:{badgeClass:"bg-red-50 text-red-700 hover:bg-red-100 border-red-200",dotClass:"bg-red-500",text:"已退款"},frozen:{badgeClass:"bg-slate-50 text-slate-700 hover:bg-slate-100 border-slate-200",dotClass:"bg-slate-500",text:"已冻结"},default:{badgeClass:"bg-slate-100 text-slate-700 hover:bg-slate-200 border-slate-200",dotClass:"bg-slate-400",text:"未知状态"}},d={all:{className:"bg-slate-100 text-slate-800 hover:bg-slate-200",text:"全部状态"},attendance:{className:"bg-green-100 text-green-800 hover:bg-green-200",text:"已考勤"},leave:{className:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200",text:"请假"},unattended:{className:"bg-orange-100 text-orange-800 hover:bg-orange-200",text:"未考勤"},default:{className:"bg-red-100 text-red-800 hover:bg-red-200",text:"缺勤"}},i={A:{label:"非常感兴趣",color:"bg-green-100 text-green-800 hover:bg-green-200"},B:{label:"比较感兴趣",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},C:{label:"一般意向",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},D:{label:"了解意向",color:"bg-orange-100 text-orange-800 hover:bg-orange-200"},E:{label:"不感兴趣",color:"bg-red-100 text-red-800 hover:bg-red-200"},default:{label:"未知",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},c={productSales:{label:"产品销售",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},courseSales:{label:"课程销售",color:"bg-green-100 text-green-800 hover:bg-green-200"},OverduePaymentCollection:{label:"逾期款项催收",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},lifestyleAndOffice:{label:"生活和办公",color:"bg-red-100 text-red-800 hover:bg-red-200"},socialAndCatering:{label:"社交和餐饮",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},refunded:{label:"退款",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"其他",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},u={completed:{label:"已完成",color:"bg-green-100 text-green-800 hover:bg-green-200"},pending:{label:"待审核",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},refunded:{label:"已退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},failed:{label:"审核未通过",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"未知状态",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},b={formal:{label:"正式学员",color:"text-blue-500"},graduated:{label:"历史学员",color:"text-green-500"},public:{label:"公海池",color:"text-red-500"},intent:{label:"意向学员",color:"text-yellow-500"}},g={secret:{label:"保密",color:"text-gray-500"},male:{label:"男",color:"text-blue-500"},female:{label:"女",color:"text-pink-500"}}}}]);