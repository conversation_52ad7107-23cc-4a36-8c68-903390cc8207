"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[332],{47901:(e,t,r)=>{r.d(t,{A:()=>n});var l=r(95155),o=r(12115),a=r(28312);let n=e=>{let{type:t="bar",data:r,labels:n,title:d="",options:i={},height:u=300,horizontal:s=!1,colors:g=["#36A2EB","#FF6384","#FFCE56","#4BC0C0","#9966FF","#FF9F40","#4CAF50","#E91E63","#2196F3","#FFC107","#00BCD4","#795548","#607D8B","#F44336","#3F51B5","#009688","#673AB7","#CDDC39","#FF9800","#FF5722","#9C27B0","#8BC34A","#FFEB3B","#FFC107","#F4F61A","#FF4081"],onClick:c=null}=e,h=(0,o.useRef)(null),p=(0,o.useRef)(null),b="bar"===t&&s?"horizontalBar":t;return(0,o.useEffect)(()=>{let e;if(!h.current)return;p.current&&p.current.destroy();let l=h.current.getContext("2d");if(!l)return;let o=e=>{if(e<=g.length)return g;let t=[...g];for(;t.length<e;){let e=g[t.length%g.length],r=.7-.1*Math.floor(t.length/g.length);t.push(e.replace(")",", ".concat(r,")")).replace("rgb","rgba"))}return t},u=(e,r)=>{var l;if(Array.isArray(e)&&"number"==typeof e[0])return{label:"Dataset ".concat(r+1),data:e,backgroundColor:g[r%g.length],borderColor:"rgba(255, 255, 255, 0.6)",borderWidth:1};let a={label:e.label||"Dataset ".concat(r+1),hidden:e.hidden,type:e.type||t,data:e.values||e.data||[],borderWidth:e.borderWidth||1};return"line"===t?{...a,backgroundColor:e.backgroundColor||"rgba(0, 0, 0, 0)",borderColor:e.borderColor||g[r%g.length],pointBackgroundColor:e.pointBackgroundColor||g[r%g.length],tension:e.tension||.4,fill:void 0!==e.fill&&e.fill}:{...a,backgroundColor:e.backgroundColor||("pie"===t||"doughnut"===t?o((null===(l=e.values)||void 0===l?void 0:l.length)||1).map(e=>e):g[r%g.length]),borderColor:e.borderColor||"rgba(255, 255, 255, 0.6)"}};return e=(e=>Array.isArray(e)&&e.length>0&&"object"==typeof e[0]&&"values"in e[0])(r)?r.map((e,t)=>u(e,t)):Array.isArray(r[0])?r.map((e,t)=>u(e,t)):[u({label:d,values:r},0)],p.current=new a.Ay(l,{type:b,plugins:[],data:{labels:n,datasets:e},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!!d,text:d,font:{size:16}},legend:{display:e.length>1||e[0].label&&"Dataset 1"!==e[0].label,position:"top"},tooltip:{mode:"index",intersect:!1}},scales:"pie"!==t&&"doughnut"!==t?{y:{beginAtZero:!0,grid:{drawBorder:!1},ticks:{padding:10}},x:{grid:{display:!1},ticks:{padding:10}}}:void 0,onClick:c,...i}}),()=>{p.current&&p.current.destroy()}},[r,n,d,i,u,s,g,c,t,b]),(0,l.jsx)("div",{style:{height:"".concat(u,"px"),width:"100%"},children:(0,l.jsx)("canvas",{ref:h})})}},70332:(e,t,r)=>{r.r(t),r.d(t,{default:()=>u});var l=r(95155),o=r(47901),a=r(71159),n=r(12115);let d={product:{type:"doughnut",height:400,title:"学员产品"},age:{type:"doughnut",height:400,title:"学员年龄"},gender:{type:"doughnut",height:400,title:"学员性别"},course:{type:"doughnut",height:400,title:"学员课程"}},i={label:[],values:[]};function u(){let[e,t]=(0,n.useState)({product:i,age:i,gender:i,course:i}),{data:r,isLoading:u}=(0,a.mk)({}),s=(0,n.useMemo)(()=>function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"count",l={label:[],values:[]};return e.forEach(e=>{l.label.push(e[t]),l.values.push(e[r])}),l},[]);(0,n.useEffect)(()=>{r&&!u&&t({product:s(r.product,"product"),age:s(r.age,"age"),gender:s(r.gender,"gender"),course:s(r.course,"course")})},[r,u,s]);let g=(0,n.useMemo)(()=>t=>{let r=d[t],a=e[t];return(0,l.jsx)("div",{className:"bg-white rounded-lg p-4 transition-all hover:shadow-md",children:(0,l.jsx)(o.A,{type:r.type,height:r.height,labels:a.label,title:r.title,data:a.values})},t)},[e]);return(0,l.jsx)("div",{className:"w-full p-6",children:(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Object.keys(d).map(g)})})}}}]);