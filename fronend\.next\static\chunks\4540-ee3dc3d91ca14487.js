"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4540],{4993:(e,t,n)=>{var r=n(12115),u="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=r.useSyncExternalStore,l=r.useRef,c=r.useEffect,a=r.useMemo,i=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,s){var f=l(null);if(null===f.current){var p={hasValue:!1,value:null};f.current=p}else p=f.current;var y=o(e,(f=a(function(){function e(e){if(!c){if(c=!0,o=e,e=r(e),void 0!==s&&p.hasValue){var t=p.value;if(s(t,e))return l=t}return l=e}if(t=l,u(o,e))return t;var n=r(e);return void 0!==s&&s(t,n)?(o=e,t):(o=e,l=n)}var o,l,c=!1,a=void 0===n?null:n;return[function(){return e(t())},null===a?void 0:function(){return e(a())}]},[t,n,r,s]))[0],f[1]);return c(function(){p.hasValue=!0,p.value=y},[y]),i(y),y}},34540:(e,t,n)=>{n.d(t,{Kq:()=>E,Pj:()=>D,bN:()=>y,d4:()=>M,vA:()=>F,wA:()=>V});var r=n(12115),u=n(39611),o=Symbol.for("react.forward_ref"),l=Symbol.for("react.memo");function c(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}var a={notify(){},get:()=>[]},i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,s="undefined"!=typeof navigator&&"ReactNative"===navigator.product,f=i||s?r.useLayoutEffect:r.useEffect;function p(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}function y(e,t){if(p(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;let n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(let r=0;r<n.length;r++)if(!Object.prototype.hasOwnProperty.call(t,n[r])||!p(e[n[r]],t[n[r]]))return!1;return!0}var d={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},b={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},v={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},g={[o]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[l]:v};function S(e){return function(e){if("object"==typeof e&&null!==e){let{$$typeof:t}=e;switch(t){case null:switch(e=e.type){case null:case null:case null:case null:case null:return e;default:switch(e=e&&e.$$typeof){case null:case o:case null:case l:case null:return e;default:return t}}case null:return t}}}(e)===l?v:g[e.$$typeof]||d}var h=Object.defineProperty,m=Object.getOwnPropertyNames,x=Object.getOwnPropertySymbols,w=Object.getOwnPropertyDescriptor,O=Object.getPrototypeOf,j=Object.prototype,P=Symbol.for("react-redux-context"),C="undefined"!=typeof globalThis?globalThis:{},N=function(){if(!r.createContext)return{};let e=C[P]??=new Map,t=e.get(r.createContext);return!t&&(t=r.createContext(null),e.set(r.createContext,t)),t}(),E=function(e){let{children:t,context:n,serverState:u,store:o}=e,l=r.useMemo(()=>{let e=function(e,t){let n;let r=a,u=0,o=!1;function l(){s.onStateChange&&s.onStateChange()}function c(){if(u++,!n){let t,u;n=e.subscribe(l),t=null,u=null,r={clear(){t=null,u=null},notify(){(()=>{let e=t;for(;e;)e.callback(),e=e.next})()},get(){let e=[],n=t;for(;n;)e.push(n),n=n.next;return e},subscribe(e){let n=!0,r=u={callback:e,next:null,prev:u};return r.prev?r.prev.next=r:t=r,function(){n&&null!==t&&(n=!1,r.next?r.next.prev=r.prev:u=r.prev,r.prev?r.prev.next=r.next:t=r.next)}}}}}function i(){u--,n&&0===u&&(n(),n=void 0,r.clear(),r=a)}let s={addNestedSub:function(e){c();let t=r.subscribe(e),n=!1;return()=>{n||(n=!0,t(),i())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:l,isSubscribed:function(){return o},trySubscribe:function(){o||(o=!0,c())},tryUnsubscribe:function(){o&&(o=!1,i())},getListeners:()=>r};return s}(o);return{store:o,subscription:e,getServerState:u?()=>u:void 0}},[o,u]),c=r.useMemo(()=>o.getState(),[o]);return f(()=>{let{subscription:e}=l;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),c!==o.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[l,c]),r.createElement((n||N).Provider,{value:l},t)};function T(e=N){return function(){return r.useContext(e)}}var k=T();function $(e=N){let t=e===N?k:T(e),n=()=>{let{store:e}=t();return e};return Object.assign(n,{withTypes:()=>n}),n}var D=$(),V=function(e=N){let t=e===N?D:$(e),n=()=>t().dispatch;return Object.assign(n,{withTypes:()=>n}),n}(),_=(e,t)=>e===t,M=function(e=N){let t=e===N?k:T(e),n=(e,n={})=>{let{equalityFn:o=_}="function"==typeof n?{equalityFn:n}:n,{store:l,subscription:c,getServerState:a}=t();r.useRef(!0);let i=r.useCallback({[e.name]:t=>e(t)}[e.name],[e]),s=(0,u.useSyncExternalStoreWithSelector)(c.addNestedSub,l.getState,a||l.getState,i,o);return r.useDebugValue(s),s};return Object.assign(n,{withTypes:()=>n}),n}(),F=function(e){e()}},39611:(e,t,n)=>{e.exports=n(4993)}}]);