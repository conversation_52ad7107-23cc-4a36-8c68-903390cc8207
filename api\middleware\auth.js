import fp from "fastify-plugin";
import { createError } from "@fastify/error";
const AUTH_ERROR = createError('AUTH_ERROR', '%s', 401);
const FORBIDDEN = createError('FORBIDDEN', '%s', 403);

async function authMiddleware(fastify, opts) {
    // 通过token验证用户信息
    async function verifyToken(request) {
        const client = await fastify.pg.connect();

        try {
            const token = request.headers.authorization?.replace('Bearer ', '');
            if (!token) throw new AUTH_ERROR('未提供令牌');

            const decoded = await fastify.jwt.verify(token);
            // 缓存用户authData
            const cacheKey = `user:${decoded.id}:authData`;
            let cachedUser = await fastify.redis.get(cacheKey);
            if (cachedUser) {
                let userData = JSON.parse(cachedUser);
                return userData;
            }
       
            const result = await client.query(`
                SELECT 
                    u.id, u.account,
                    ARRAY_AGG(DISTINCT r.id) AS role_ids,
                    ARRAY_AGG(DISTINCT rp."permissionId") AS permission_ids,
                    EXISTS (SELECT 1 FROM user_roles ur JOIN roles r ON ur."roleId" = r.id WHERE ur."userId" = u.id AND r.code = 'SYSTEM_ADMIN') AS is_super_admin
                FROM users u
                LEFT JOIN user_roles ur ON ur."userId" = u.id
                LEFT JOIN roles r ON ur."roleId" = r.id
                LEFT JOIN role_permissions rp ON rp."roleId" = r.id
                WHERE u.id = $1
                GROUP BY u.id
            `, [decoded.id]);

            if (result.rows.length === 0 || !result.rows[0])
                throw new AUTH_ERROR('用户或角色不存在');

            const user = result.rows[0];

            const operationsResult = await client.query(`
                SELECT DISTINCT code FROM permissions WHERE id = ANY($1)
            `, [user.permission_ids]);

            const institutionResult = await client.query(`
                SELECT DISTINCT "institutionId" FROM user_institution WHERE "userId" = $1
            `, [decoded.id]);
            
            const userData = {
                id: user.id,
                account: user.account,
                SYSTEM_ADMIN: user.is_super_admin,
                institutionId: institutionResult.rows[0]?.institutionId,
                operations: operationsResult.rows.map(op => op.code),
            };

            await fastify.redis.set(cacheKey, JSON.stringify(userData));
            return userData;

        } catch (error) {
            fastify.log.error(error, '授权ow error');
            throw new AUTH_ERROR('无效令牌');
        } finally {
            client.release();
        }
    }

    function requirePermission(permissionCode) {
        return async (request, reply) => {
            if (!request.user.operations.includes(permissionCode)) {
                throw new FORBIDDEN('没有权限');
            }

        }

    }
    // 认证中间件
    async function authenticate(request, reply) {
        const user = await verifyToken(request);
        // 未来需要增加更多的用户信息
        request.user = user;
    }
    // fastify.decorateRequest('user', null);
    fastify.decorate('auth', {
        authenticate,
        requirePermission
    })
}


export default fp(authMiddleware, {
    name: 'auth-middleware',
    dependencies: ['@fastify/jwt']
});