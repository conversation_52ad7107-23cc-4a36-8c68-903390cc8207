(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[950],{5501:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(95155),r=s(12115),l=s(59409),n=s(59434),d=s(12519);let o=(0,r.memo)(function(e){let{value:t,onChange:s,width:o="w-full",placeholder:c="选择教室",className:i="",showAllOption:m=!1,allOptionText:x="全部教室",allOptionValue:u="all",list:h,disabled:f=!1}=e,{data:p,isLoading:b,error:j}=(0,d.hP)({}),v=(0,r.useCallback)(e=>{s(e)},[s]),g=(0,r.useCallback)(()=>(0,n.cn)("h-9 ".concat(o),i),[o,i]),N=h||p;return(0,a.jsxs)(l.l6,{value:t,onValueChange:v,disabled:f||b,children:[(0,a.jsx)(l.bq,{className:g(),children:(0,a.jsx)(l.yv,{placeholder:c})}),(0,a.jsxs)(l.gC,{children:[j&&(0,a.jsx)(l.eb,{value:"error",disabled:!0,children:String(j)}),b&&(0,a.jsx)(l.eb,{value:"loading",disabled:!0,children:"加载中..."}),!b&&!j&&(0,a.jsxs)(a.Fragment,{children:[m&&(0,a.jsx)(l.eb,{value:u,children:x}),null==N?void 0:N.map(e=>(0,a.jsx)(l.eb,{value:e.id,children:e.name},e.id))]})]})]})})},8643:(e,t,s)=>{"use strict";s.d(t,{Ke:()=>n,Nt:()=>r,R6:()=>l});var a=s(88106);let r=a.bL,l=a.R6,n=a.Ke},14636:(e,t,s)=>{"use strict";s.d(t,{AM:()=>d,Wv:()=>o,hl:()=>c});var a=s(95155),r=s(12115),l=s(20547),n=s(59434);let d=l.bL,o=l.l9,c=r.forwardRef((e,t)=>{let{className:s,align:r="center",sideOffset:d=4,...o}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsx)(l.UC,{ref:t,align:r,sideOffset:d,className:(0,n.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...o})})});c.displayName=l.UC.displayName},17759:(e,t,s)=>{"use strict";s.d(t,{C5:()=>j,MJ:()=>p,Rr:()=>b,eI:()=>h,lR:()=>f,lV:()=>c,zB:()=>m});var a=s(95155),r=s(12115),l=s(99708),n=s(62177),d=s(59434),o=s(85057);let c=n.Op,i=r.createContext({}),m=e=>{let{...t}=e;return(0,a.jsx)(i.Provider,{value:{name:t.name},children:(0,a.jsx)(n.xI,{...t})})},x=()=>{let e=r.useContext(i),t=r.useContext(u),{getFieldState:s,formState:a}=(0,n.xW)(),l=s(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:d}=t;return{id:d,name:e.name,formItemId:"".concat(d,"-form-item"),formDescriptionId:"".concat(d,"-form-item-description"),formMessageId:"".concat(d,"-form-item-message"),...l}},u=r.createContext({}),h=r.forwardRef((e,t)=>{let{className:s,...l}=e,n=r.useId();return(0,a.jsx)(u.Provider,{value:{id:n},children:(0,a.jsx)("div",{ref:t,className:(0,d.cn)("space-y-2",s),...l})})});h.displayName="FormItem";let f=r.forwardRef((e,t)=>{let{className:s,...r}=e,{error:l,formItemId:n}=x();return(0,a.jsx)(o.J,{ref:t,className:(0,d.cn)(l&&"text-destructive",s),htmlFor:n,...r})});f.displayName="FormLabel";let p=r.forwardRef((e,t)=>{let{...s}=e,{error:r,formItemId:n,formDescriptionId:d,formMessageId:o}=x();return(0,a.jsx)(l.DX,{ref:t,id:n,"aria-describedby":r?"".concat(d," ").concat(o):"".concat(d),"aria-invalid":!!r,...s})});p.displayName="FormControl";let b=r.forwardRef((e,t)=>{let{className:s,...r}=e,{formDescriptionId:l}=x();return(0,a.jsx)("p",{ref:t,id:l,className:(0,d.cn)("text-sm text-muted-foreground",s),...r})});b.displayName="FormDescription";let j=r.forwardRef((e,t)=>{var s;let{className:r,children:l,...n}=e,{error:o,formMessageId:c}=x(),i=o?String(null!==(s=null==o?void 0:o.message)&&void 0!==s?s:""):l;return i?(0,a.jsx)("p",{ref:t,id:c,className:(0,d.cn)("text-sm font-medium text-destructive",r),...n,children:i}):null});j.displayName="FormMessage"},22346:(e,t,s)=>{"use strict";s.d(t,{w:()=>d});var a=s(95155),r=s(12115),l=s(87489),n=s(59434);let d=r.forwardRef((e,t)=>{let{className:s,orientation:r="horizontal",decorative:d=!0,...o}=e;return(0,a.jsx)(l.b,{ref:t,decorative:d,orientation:r,className:(0,n.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",s),...o})});d.displayName=l.b.displayName},25879:(e,t,s)=>{Promise.resolve().then(s.bind(s,59199))},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>o});var a=s(95155),r=s(12115),l=s(99708),n=s(74466),d=s(59434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:s,variant:r,size:n,asChild:c=!1,...i}=e,m=c?l.DX:"button";return(0,a.jsx)(m,{className:(0,d.cn)(o({variant:r,size:n,className:s})),ref:t,...i})});c.displayName="Button"},30356:(e,t,s)=>{"use strict";s.d(t,{C:()=>c,z:()=>o});var a=s(95155),r=s(12115),l=s(54059),n=s(9428),d=s(59434);let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.bL,{className:(0,d.cn)("grid gap-2",s),...r,ref:t})});o.displayName=l.bL.displayName;let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.q7,{ref:t,className:(0,d.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),...r,children:(0,a.jsx)(l.C1,{className:"flex items-center justify-center",children:(0,a.jsx)(n.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});c.displayName=l.q7.displayName},59199:(e,t,s)=>{"use strict";s.d(t,{default:()=>L});var a=s(95155),r=s(62177),l=s(90221),n=s(17759),d=s(30285),o=s(66695),c=s(48436),i=s(62523),m=s(92164),x=s(5501);function u(e){let{form:t}=e;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(n.zB,{control:t.control,name:"name",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"班级名称"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(i.p,{placeholder:"请输入班级名称",className:"h-10 border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 text-sm bg-slate-50 hover:bg-white transition-colors",...t})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(n.zB,{control:t.control,name:"courseId",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"课程"}),(0,a.jsx)(m.A,{value:t.value,onChange:t.onChange}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}}),(0,a.jsx)(n.zB,{control:t.control,name:"maxStudentCount",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"最多人数"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(i.p,{placeholder:"班级最多学员人数",className:"h-10 border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 text-sm bg-slate-50 hover:bg-white transition-colors",...t})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})]}),(0,a.jsx)(n.zB,{control:t.control,name:"classroom",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"教室选择"}),(0,a.jsx)(x.A,{value:t.value||"",onChange:t.onChange}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})]})}var h=s(30356),f=s(14636),p=s(85511),b=s(69074),j=s(59434),v=s(73168),g=s(24122),N=s(85057),y=s(84616),w=s(12115);let C=e=>{let{value:t=[],onChange:s}=e,r=[{label:"周一",value:1},{label:"周二",value:2},{label:"周三",value:3},{label:"周四",value:4},{label:"周五",value:5},{label:"周六",value:6},{label:"周日",value:0}],[l,n]=(0,w.useState)(null),[o,c]=(0,w.useState)(!1),i=e=>t.some(t=>t.day===e),m=e=>t.find(t=>t.day===e),x=e=>{n(e),c(!0)},u=(e,a,r)=>{let l=[...t],n=l.findIndex(t=>t.day===e);-1!==n?l[n]={day:e,startTime:a,endTime:r}:l.push({day:e,startTime:a,endTime:r}),null==s||s(l),c(!1)},h=e=>{let a=t.filter(t=>t.day!==e);null==s||s(a)};return(0,a.jsxs)("div",{className:"rounded-md overflow-hidden border border-slate-200",children:[(0,a.jsx)("div",{className:"grid grid-cols-7",children:r.map(e=>(0,a.jsx)("div",{className:"text-center py-2.5 bg-slate-50 text-slate-600 font-medium text-sm border-b border-slate-200",children:e.label},e.value))}),(0,a.jsx)("div",{className:"grid grid-cols-7",children:r.map(e=>{var t;let s=m(e.value),r=i(e.value);return(0,a.jsxs)("div",{className:(0,j.cn)("flex flex-col items-center py-4 transition-colors",r?"bg-slate-50":"hover:bg-slate-50/50"),children:[(0,a.jsxs)(f.AM,{open:o&&l===e.value,onOpenChange:e=>{e||c(!1)},children:[(0,a.jsx)(f.Wv,{asChild:!0,children:(0,a.jsx)("div",{onClick:()=>x(e.value),className:(0,j.cn)("h-8 w-8 rounded-full flex items-center justify-center cursor-pointer transition-colors",r?"bg-slate-700 text-white hover:bg-slate-600":"border border-slate-300 text-slate-500 hover:border-slate-400 hover:text-slate-600"),children:r?(0,a.jsx)("span",{className:"text-xs font-medium",children:null==s?void 0:null===(t=s.startTime)||void 0===t?void 0:t.substring(0,2)}):(0,a.jsx)(y.A,{className:"h-3.5 w-3.5"})})}),(0,a.jsx)(f.hl,{className:"w-72 p-3 shadow-sm",align:"center",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h4",{className:"font-medium text-sm text-slate-700",children:[e.label,"课程时间"]}),(0,a.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"h-7 px-2 text-xs text-slate-500 hover:text-slate-700 hover:bg-slate-100",onClick:()=>h(e.value),children:"删除"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsx)(N.J,{htmlFor:"start-time-".concat(e.value),className:"text-xs text-slate-500",children:"开始时间"}),(0,a.jsx)("input",{id:"start-time-".concat(e.value),type:"time",defaultValue:(null==s?void 0:s.startTime)||"08:00",className:"w-full h-8 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"})]}),(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsx)(N.J,{htmlFor:"end-time-".concat(e.value),className:"text-xs text-slate-500",children:"结束时间"}),(0,a.jsx)("input",{id:"end-time-".concat(e.value),type:"time",defaultValue:(null==s?void 0:s.endTime)||"09:30",className:"w-full h-8 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"})]})]}),(0,a.jsx)(d.$,{type:"button",className:"w-full h-8 text-sm bg-slate-800 hover:bg-slate-700",onClick:()=>{let t=document.getElementById("start-time-".concat(e.value)),s=document.getElementById("end-time-".concat(e.value));u(e.value,t.value,s.value)},children:"确定"})]})})]}),s&&(0,a.jsxs)("div",{className:"text-xs mt-2 text-slate-600",children:[s.startTime," - ",s.endTime]})]},e.value)})})]})};function z(e){let{form:t}=e,s=t.watch("recurrenceType"),r=t.watch("endType");return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(n.zB,{control:t.control,name:"recurrenceType",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"space-y-3",children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"循环排课"}),(0,a.jsx)(n.MJ,{children:(0,a.jsxs)(h.z,{onValueChange:t.onChange,defaultValue:t.value,className:"flex flex-col space-y-2",children:[(0,a.jsxs)(n.eI,{className:"flex items-center space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors",children:[(0,a.jsx)(n.MJ,{children:(0,a.jsx)(h.C,{value:"weekly",className:"text-slate-700"})}),(0,a.jsx)(n.lR,{className:"font-normal text-slate-700 cursor-pointer w-full text-sm",children:"每周重复"})]}),(0,a.jsxs)(n.eI,{className:"flex items-center space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors",children:[(0,a.jsx)(n.MJ,{children:(0,a.jsx)(h.C,{value:"daily",className:"text-slate-700"})}),(0,a.jsx)(n.lR,{className:"font-normal text-slate-700 cursor-pointer w-full text-sm",children:"每天重复"})]})]})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}}),"weekly"===s&&(0,a.jsx)("div",{className:"p-4 border rounded-md border-slate-200",children:(0,a.jsx)(n.zB,{control:t.control,name:"weekdays",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"pt-0",children:[(0,a.jsx)(n.lR,{className:"text-sm text-slate-700 mb-3 block font-medium",children:"选择上课日期"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(C,{value:t.value||[],onChange:t.onChange})}),(0,a.jsx)(n.Rr,{className:"mt-3 text-slate-500 text-xs",children:"请选择每周上课的日期并设置时间"}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})}),"daily"===s&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(n.zB,{control:t.control,name:"daily.startTime",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开始时间"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"time",placeholder:"选择开始时间",className:"w-full h-10 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:t.value||"",onChange:e=>t.onChange(e.target.value)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}}),(0,a.jsx)(n.zB,{control:t.control,name:"daily.endTime",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"结束时间"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"time",placeholder:"选择结束时间",className:"w-full h-10 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:t.value||"",onChange:e=>t.onChange(e.target.value)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})]}),(0,a.jsx)("div",{className:"pt-2",children:(0,a.jsx)(n.zB,{control:t.control,name:"endType",render:e=>{let{field:s}=e;return(0,a.jsxs)(n.eI,{className:"space-y-3",children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"结束方式"}),(0,a.jsx)(n.MJ,{children:(0,a.jsxs)(h.z,{onValueChange:e=>{"number_of_times"===e&&t.setValue("endDate",void 0),s.onChange(e)},defaultValue:s.value,className:"flex flex-col space-y-2",children:[(0,a.jsxs)(n.eI,{className:"flex items-center space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors",children:[(0,a.jsx)(n.MJ,{children:(0,a.jsx)(h.C,{value:"times",className:"text-slate-700"})}),(0,a.jsx)(n.lR,{className:"font-normal text-slate-700 cursor-pointer w-full text-sm",children:"按时间"})]}),(0,a.jsxs)(n.eI,{className:"flex items-center space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors",children:[(0,a.jsx)(n.MJ,{children:(0,a.jsx)(h.C,{value:"number_of_times",className:"text-slate-700"})}),(0,a.jsx)(n.lR,{className:"font-normal text-slate-700 cursor-pointer w-full text-sm",children:"按次数"})]})]})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 pt-1",children:[(0,a.jsx)(n.zB,{control:t.control,name:"startDate",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开始日期"}),(0,a.jsxs)(f.AM,{children:[(0,a.jsx)(f.Wv,{asChild:!0,children:(0,a.jsx)(n.MJ,{children:(0,a.jsxs)("div",{className:(0,j.cn)("w-full flex items-center justify-between text-sm font-normal","border-b border-slate-200 py-2 text-slate-700","hover:border-slate-300 transition-colors cursor-pointer",!t.value&&"text-slate-500"),children:[t.value?(0,v.GP)(t.value,"yyyy年MM月dd日"):(0,a.jsx)("span",{children:"选择日期"}),(0,a.jsx)(b.A,{className:"h-4 w-4 opacity-50"})]})})}),(0,a.jsx)(f.hl,{className:"w-auto p-0 shadow-sm rounded-md",align:"start",children:(0,a.jsx)(p.V,{locale:g.g,mode:"single",selected:t.value,onSelect:e=>t.onChange(e),initialFocus:!0,className:"border-0"})})]}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}}),"times"===r&&(0,a.jsx)(n.zB,{control:t.control,name:"endDate",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"结束日期"}),(0,a.jsxs)(f.AM,{children:[(0,a.jsx)(f.Wv,{asChild:!0,children:(0,a.jsx)(n.MJ,{children:(0,a.jsxs)("div",{className:(0,j.cn)("w-full flex items-center justify-between text-sm font-normal","border-b border-slate-200 py-2 text-slate-700","hover:border-slate-300 transition-colors cursor-pointer",!t.value&&"text-slate-500"),children:[t.value?(0,v.GP)(t.value,"yyyy年MM月dd日"):(0,a.jsx)("span",{children:"选择日期"}),(0,a.jsx)(b.A,{className:"h-4 w-4 opacity-50"})]})})}),(0,a.jsx)(f.hl,{className:"w-auto p-0 shadow-sm rounded-md",align:"start",children:(0,a.jsx)(p.V,{locale:g.g,mode:"single",selected:t.value,onSelect:e=>t.onChange(e),initialFocus:!0,className:"border-0"})})]}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}}),"number_of_times"===r&&(0,a.jsx)(n.zB,{control:t.control,name:"times",render:e=>{var t;let{field:s}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"课程次数"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"number",placeholder:"请输入课程次数",className:"w-full h-10 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:null!==(t=s.value)&&void 0!==t?t:"",onChange:e=>s.onChange(Number.parseInt(e.target.value)||0)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})]})]})}function R(e){let{form:t}=e;return(0,a.jsx)(n.zB,{control:t.control,name:"type",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"space-y-3",children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"班级类型"}),(0,a.jsx)(n.MJ,{children:(0,a.jsxs)(h.z,{onValueChange:t.onChange,defaultValue:t.value,className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)(n.eI,{className:"flex items-start space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors",children:[(0,a.jsx)(n.MJ,{children:(0,a.jsx)(h.C,{value:"temporary",className:"mt-0.5 text-slate-700"})}),(0,a.jsxs)(n.lR,{className:"font-normal text-slate-700 cursor-pointer w-full text-sm",children:["临时班级",(0,a.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"适用于临时组织的短期课程"})]})]}),(0,a.jsxs)(n.eI,{className:"flex items-start space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors",children:[(0,a.jsx)(n.MJ,{children:(0,a.jsx)(h.C,{value:"fixed",className:"mt-0.5 text-slate-700"})}),(0,a.jsxs)(n.lR,{className:"font-normal text-slate-700 cursor-pointer w-full text-sm",children:["固定班级",(0,a.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"适用于长期固定的常规课程"})]})]})]})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})}var k=s(80333),I=s(22346),T=s(8643),M=s(381),_=s(47863),J=s(66474);function S(e){let{form:t}=e,[s,r]=(0,w.useState)(!1),l=t.watch("reservation.enabled"),d=t.watch("leave.enabled");return(0,a.jsxs)(T.Nt,{open:s,onOpenChange:r,className:"border border-slate-200 rounded-md shadow-sm overflow-hidden",children:[(0,a.jsxs)(T.R6,{className:"flex w-full items-center justify-between p-3 text-left hover:bg-slate-50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(M.A,{className:"h-4 w-4 text-slate-500"}),(0,a.jsx)("h3",{className:"text-sm font-medium text-slate-700",children:"高级选项"})]}),(0,a.jsx)("div",{className:"h-6 w-6 flex items-center justify-center text-slate-400",children:s?(0,a.jsx)(_.A,{className:"h-4 w-4"}):(0,a.jsx)(J.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)(T.Ke,{className:"px-4 pb-4 pt-2 space-y-5 border-t border-slate-200",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700 mt-2",children:"预约设置"}),(0,a.jsx)(n.zB,{control:t.control,name:"reservation.enabled",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between p-3 rounded-md border border-slate-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开放预约"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"允许学员预约此班级的课程"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(k.d,{checked:t.value,onCheckedChange:t.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}}),l&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-3 pl-4 border-l border-slate-200",children:[(0,a.jsx)(n.zB,{control:t.control,name:"reservation.appointmentStartTime",render:e=>{var t;let{field:s}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开放预约时间（小时）"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"课程开始前多少小时开放预约"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"number",placeholder:"如：24小时",className:"w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:null!==(t=s.value)&&void 0!==t?t:"",onChange:e=>s.onChange(Number(e.target.value)||0)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}}),(0,a.jsx)(n.zB,{control:t.control,name:"reservation.appointmentEndTime",render:e=>{var t;let{field:s}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"截止预约时间（小时）"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"课程开始前多少小时停止预约"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"number",placeholder:"如：1小时",className:"w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:null!==(t=s.value)&&void 0!==t?t:"",onChange:e=>s.onChange(Number(e.target.value)||0)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})]})]}),(0,a.jsx)(I.w,{className:"bg-slate-200"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:"考勤选择"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,a.jsx)(n.zB,{control:t.control,name:"attendance.studentScan",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"学员扫码考勤"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"允许学员通过扫码进行课程签到"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(k.d,{checked:t.value,onCheckedChange:t.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}}),(0,a.jsx)(n.zB,{control:t.control,name:"attendance.autoSystem",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"系统自动考勤"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"系统自动完成考勤流程"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(k.d,{checked:t.value,onCheckedChange:t.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}})]})]}),(0,a.jsx)(I.w,{className:"bg-slate-200"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:"请假选项"}),(0,a.jsx)(n.zB,{control:t.control,name:"leave.enabled",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between p-3 rounded-md border border-slate-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开放请假"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"允许学员请假"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(k.d,{checked:t.value,onCheckedChange:t.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}}),d&&(0,a.jsx)("div",{className:"pl-4 border-l border-slate-200",children:(0,a.jsx)(n.zB,{control:t.control,name:"leave.leaveDeadline",render:e=>{var t;let{field:s}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"请假截止时间（小时）"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"课程开始前多少小时停止请假"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"number",placeholder:"如：2小时",className:"w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:null!==(t=s.value)&&void 0!==t?t:"",onChange:e=>s.onChange(Number(e.target.value)||0)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})})]}),(0,a.jsx)(I.w,{className:"bg-slate-200"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:"周期设置"}),(0,a.jsx)(n.zB,{control:t.control,name:"isShowWeekCount",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"启用周期数"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"按周期划分课程，例如：第一周期、第二周期等"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(k.d,{checked:t.value,onCheckedChange:t.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}})]})]})]})}var B=s(55594);let D=B.z.object({name:B.z.string().min(1,"请输入班级名称"),courseId:B.z.string().min(1,"请选择课程包"),teacherId:B.z.string().min(1,"请选择主讲老师"),classroom:B.z.null().optional(),recurrenceType:B.z.enum(["weekly","daily"]),weekdays:B.z.array(B.z.object({day:B.z.number(),startTime:B.z.string(),endTime:B.z.string()})).optional(),daily:B.z.object({startTime:B.z.string().optional(),endTime:B.z.string().optional()}).optional(),endType:B.z.enum(["times","number_of_times"]),startDate:B.z.date().optional(),endDate:B.z.date().optional(),times:B.z.number().optional(),maxStudentCount:B.z.string().min(1,"请输入最大人数"),type:B.z.enum(["temporary","fixed"]),reservation:B.z.object({enabled:B.z.boolean(),appointmentStartTime:B.z.number().min(0,"不能小于0"),appointmentEndTime:B.z.number().min(0,"不能小于0")}),attendance:B.z.object({studentScan:B.z.boolean(),autoSystem:B.z.boolean()}),leave:B.z.object({enabled:B.z.boolean(),leaveDeadline:B.z.number().min(0,"不能小于0")}),isShowWeekCount:B.z.boolean()});var A=s(59409),F=s(27893);let V=function(e){let{form:t}=e,[s,r]=(0,w.useState)(!1),[l,d]=(0,w.useState)([]),[o]=(0,F.Kp)(),c=async e=>{let t={startDate:new Date(e.startDate).getTime(),endDate:new Date(e.endDate).getTime(),endType:e.endType,daily:e.daily,weekdays:e.weekdays,times:e.times};try{let e=await o(t);d((null==e?void 0:e.data)||[])}catch(e){console.error("获取教师空闲时间失败",e)}};return(0,w.useEffect)(()=>{let e=t.watch(e=>{if(e.startDate&&e.endType&&e.startDate){var t;(e.daily||(null===(t=e.weekdays)||void 0===t?void 0:t.length))&&(e.endDate||e.times)&&c(e)}});return()=>e.unsubscribe()},[t]),(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:" pb-2 space-y-3",children:[(0,a.jsx)(n.lR,{className:"text-sm",children:"主讲老师"}),(0,a.jsxs)(A.l6,{onValueChange:e=>t.setValue("teacherId",e),children:[(0,a.jsx)(A.bq,{className:"w-full h-10 text-sm border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 bg-transparent",children:(0,a.jsx)(A.yv,{placeholder:"选择主讲老师"})}),(0,a.jsx)(A.gC,{children:0===l.length?(0,a.jsx)("div",{className:"py-6 text-center text-sm text-slate-500",children:"暂无教师数据"}):l.map(e=>(0,a.jsxs)(A.eb,{value:e.id,className:"text-sm",children:[e.name," ",e.freeTime?(0,a.jsx)("span",{className:"text-xs text-green-500",children:"(有空)"}):(0,a.jsx)("span",{className:"text-xs text-red-500",children:"(无空)"})]},e.id))})]})]})})};function L(){let[e]=(0,F.Zj)(),t=(0,r.mN)({resolver:(0,l.u)(D),defaultValues:{name:"",recurrenceType:"weekly",weekdays:[],endType:"number_of_times",maxStudentCount:"",type:"fixed",reservation:{enabled:!1,appointmentStartTime:24,appointmentEndTime:1},attendance:{studentScan:!1,autoSystem:!1},leave:{enabled:!1,leaveDeadline:2},isShowWeekCount:!1}});return(0,a.jsx)("div",{children:(0,a.jsxs)(o.Zp,{className:"border border-slate-200 shadow-sm overflow-hidden rounded-md",children:[(0,a.jsxs)(o.aR,{className:"bg-slate-50 px-6 py-4 border-b border-slate-200",children:[(0,a.jsx)(o.ZB,{className:"text-lg font-medium text-slate-800",children:"班级排课"}),(0,a.jsx)(o.BT,{className:"text-slate-500 mt-1 text-sm",children:"创建新的班级并安排课程时间"})]}),(0,a.jsx)(o.Wu,{className:"p-6 space-y-8",children:(0,a.jsx)(n.lV,{...t,children:(0,a.jsxs)("form",{onSubmit:t.handleSubmit(function(t){var s,a;e({endType:t.endType,startDate:null===(s=t.startDate)||void 0===s?void 0:s.getTime(),endDate:null===(a=t.endDate)||void 0===a?void 0:a.getTime(),name:t.name,courseId:t.courseId,daily:t.daily,teacherId:t.teacherId,classroomId:t.classroom&&"none"!==t.classroom?t.classroom:null,recurrenceType:t.recurrenceType,weekdays:t.weekdays,times:t.times,type:t.type,maxStudentCount:t.maxStudentCount,reservation:t.reservation,attendance:t.attendance,leave:t.leave,isShowWeekCount:t.isShowWeekCount}).then(e=>{console.log(e),c.l.success("班级创建成功.")})}),className:"space-y-8",children:[(0,a.jsx)("section",{children:(0,a.jsx)(u,{form:t})}),(0,a.jsx)("section",{children:(0,a.jsx)(z,{form:t})}),(0,a.jsx)("section",{children:(0,a.jsx)(R,{form:t})}),(0,a.jsx)("section",{children:(0,a.jsx)(V,{form:t})}),(0,a.jsx)("section",{children:(0,a.jsx)(S,{form:t})}),(0,a.jsx)("div",{className:"pt-2",children:(0,a.jsx)(d.$,{type:"submit",className:"w-full h-10 text-sm font-medium bg-slate-800 hover:bg-slate-700 transition-colors rounded-md",children:"创建班级"})})]})})})]})})}},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>x,eb:()=>p,gC:()=>f,l6:()=>i,yv:()=>m});var a=s(95155),r=s(12115),l=s(14582),n=s(66474),d=s(47863),o=s(5196),c=s(59434);let i=l.bL;l.YJ;let m=l.WT,x=r.forwardRef((e,t)=>{let{className:s,children:r,...d}=e;return(0,a.jsxs)(l.l9,{ref:t,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...d,children:[r,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});x.displayName=l.l9.displayName;let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.PP,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})});u.displayName=l.PP.displayName;let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.wn,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let f=r.forwardRef((e,t)=>{let{className:s,children:r,position:n="popper",...d}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:t,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...d,children:[(0,a.jsx)(u,{}),(0,a.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(h,{})]})})});f.displayName=l.UC.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.JU,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...r})}).displayName=l.JU.displayName;let p=r.forwardRef((e,t)=>{let{className:s,children:r,...n}=e;return(0,a.jsxs)(l.q7,{ref:t,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:r})]})});p.displayName=l.q7.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.wv,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",s),...r})}).displayName=l.wv.displayName},59434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var a=s(52596),r=s(39688);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(95155),r=s(12115),l=s(59434);let n=r.forwardRef((e,t)=>{let{className:s,type:r,...n}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...n})});n.displayName="Input"},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>i,ZB:()=>o,Zp:()=>n,aR:()=>d,wL:()=>m});var a=s(95155),r=s(12115),l=s(59434);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});n.displayName="Card";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...r})});d.displayName="CardHeader";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});o.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})});c.displayName="CardDescription";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...r})});i.displayName="CardContent";let m=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...r})});m.displayName="CardFooter"},80333:(e,t,s)=>{"use strict";s.d(t,{d:()=>d});var a=s(95155),r=s(12115),l=s(4884),n=s(59434);let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.bL,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...r,ref:t,children:(0,a.jsx)(l.zi,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});d.displayName=l.bL.displayName},85057:(e,t,s)=>{"use strict";s.d(t,{J:()=>c});var a=s(95155),r=s(12115),l=s(40968),n=s(74466),d=s(59434);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.b,{ref:t,className:(0,d.cn)(o(),s),...r})});c.displayName=l.b.displayName},85511:(e,t,s)=>{"use strict";s.d(t,{V:()=>c});var a=s(95155);s(12115);var r=s(42355),l=s(13052),n=s(20081),d=s(59434),o=s(30285);function c(e){let{className:t,classNames:s,showOutsideDays:c=!0,...i}=e;return(0,a.jsx)(n.hv,{showOutsideDays:c,className:(0,d.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,d.cn)((0,o.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,d.cn)((0,o.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...s},components:{IconLeft:e=>{let{className:t,...s}=e;return(0,a.jsx)(r.A,{className:(0,d.cn)("h-4 w-4",t),...s})},IconRight:e=>{let{className:t,...s}=e;return(0,a.jsx)(l.A,{className:(0,d.cn)("h-4 w-4",t),...s})}},...i})}c.displayName="Calendar"},92164:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(95155),r=s(12115),l=s(59409),n=s(59434),d=s(74651);let o=(0,r.memo)(function(e){let{value:t,onChange:s,width:o="w-full",placeholder:c="选择课程",className:i="",showAllOption:m=!1,allOptionText:x="全部课程",allOptionValue:u="all",list:h,disabled:f=!1}=e,{data:p,isLoading:b,error:j}=(0,d.zo)({}),v=(0,r.useCallback)(e=>{s(e)},[s]),g=(0,r.useCallback)(()=>(0,n.cn)("h-9 ".concat(o),i),[o,i]),N=h||p;return(0,a.jsxs)(l.l6,{value:t,onValueChange:v,disabled:f||b,children:[(0,a.jsx)(l.bq,{className:g(),children:(0,a.jsx)(l.yv,{placeholder:c})}),(0,a.jsxs)(l.gC,{children:[j&&(0,a.jsx)(l.eb,{value:"error",disabled:!0,children:String(j)}),b&&(0,a.jsx)(l.eb,{value:"loading",disabled:!0,children:"加载中..."}),!b&&!j&&(0,a.jsxs)(a.Fragment,{children:[m&&(0,a.jsx)(l.eb,{value:u,children:x}),null==N?void 0:N.map(e=>(0,a.jsx)(l.eb,{value:e.id,children:e.name},e.id))]})]})]})})}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8687,4201,8737,4540,4582,3168,81,5589,5602,6263,9624,6315,7358],()=>t(25879)),_N_E=e.O()}]);