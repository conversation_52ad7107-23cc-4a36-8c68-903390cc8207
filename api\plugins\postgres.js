import fp from "fastify-plugin";
import pg from "@fastify/postgres";

async function postgresConnector(fastify, options) {
    try {
        const connectionString = `postgres://${fastify.config.POSTGRES_USER}:${fastify.config.POSTGRES_PASSWORD}@${fastify.config.POSTGRES_HOST}/${fastify.config.POSTGRES_DB}`;

        await fastify.register(pg, {
            connectionString,
            max: 10, // 连接池最大连接数
            idleTimeoutMillis: 30000, // 连接空闲超时时间
            connectionTimeoutMillis: 2000 // 连接超时时间
        });

        // 直接测试 pool 连接，而不是手动获取 client
        const { rows } = await fastify.pg.query('SELECT NOW()');
        fastify.log.info({ actor: 'Postgres', time: rows[0].now }, 'connected');

    } catch (error) {
        fastify.log.error({ actor: 'Postgres' }, `connection error: ${error}`);
        process.exit(1);
    }
}

export default fp(postgresConnector, {
    name: 'postgres-connector',
    dependencies: ['env-plugin']
});
