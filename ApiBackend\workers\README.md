# Workers Folder

This folder contains all BullMQ worker implementations for the application. Workers are responsible for processing jobs from the queues asynchronously.

## Structure

- **workerConfig.js**: Common configuration and utility functions for all workers
- **imageWorker.js**: Worker for processing image generation and variation tasks
- **textWorker.js**: Worker for processing text generation tasks
- **speechWorker.js**: Worker for processing speech tasks (text-to-speech, speech-to-text)
- **visionWorker.js**: Worker for processing vision tasks (image analysis, captioning)
- **index.js**: Exports all workers and initialization functions
- **startWorker.js**: Script to start specific workers

## Worker Configuration

Each worker is configured with specific settings optimized for its workload:

```javascript
const workerOptions = {
  connection: redisConfig,
  concurrency: 5, // Number of jobs to process concurrently
  lockDuration: 300000, // 5 minutes
  stalledInterval: 30000,
  maxStalledCount: 2,
  drainDelay: 5,
  autorun: true,
  metrics: {
    maxDataPoints: 100
  }
};
```

## Usage

### Starting Workers

Workers can be started using the npm scripts:

```bash
npm run worker:image    # Start only the image worker
npm run worker:text     # Start only the text worker
npm run worker:speech   # Start only the speech worker
npm run worker:vision   # Start only the vision worker
npm run worker:all      # Start all workers
```

Or directly using the startWorker.js script:

```bash
node workers/startWorker.js image
node workers/startWorker.js all
```

### Worker Implementation

Each worker processes jobs from its corresponding queue:

```javascript
const worker = new Worker(
  'queue-name',
  async (job) => {
    // Process the job
    const { taskId, type } = job.data;
    
    // Setup progress reporting
    const { progressInterval, updateProgress } = setupProgressReporting(taskId);
    
    try {
      // Process the job based on its type
      let result;
      
      // Update progress
      await updateProgress(10);
      
      // Process the job
      result = await processJob(job.data);
      
      // Mark task as completed
      await markTaskCompleted(taskId, result, type, processingTime);
      
      return result;
    } catch (error) {
      // Mark task as failed
      await markTaskFailed(taskId, error, type, Date.now() - startTime);
      
      // Rethrow the error for BullMQ to handle retries
      throw error;
    } finally {
      // Clean up resources
      clearInterval(progressInterval);
    }
  },
  workerOptions
);
```

## Error Handling

All workers have built-in error handling:

```javascript
// Add error handling for the worker
worker.on('error', (error) => {
  console.error('Worker error:', error);
});

worker.on('failed', (job, error) => {
  console.error(`Worker job ${job?.id} failed:`, error);
});

worker.on('completed', (job) => {
  console.log(`Worker completed job ${job.id}`);
});
```

## Graceful Shutdown

The startWorker.js script handles graceful shutdown of workers:

```javascript
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully...');
  await cleanupWorkers();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully...');
  await cleanupWorkers();
  process.exit(0);
});
```
