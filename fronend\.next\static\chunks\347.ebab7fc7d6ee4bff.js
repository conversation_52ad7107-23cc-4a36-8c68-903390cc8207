"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[347],{347:(e,a,s)=>{s.r(a),s.d(a,{default:()=>g});var t=s(95155),l=s(12115),r=s(9110),n=s(30285),d=s(49103),i=s(37777),c=s(48436),o=s(26126),m=s(90010),x=s(34835),h=s(57001),u=s(65436),p=s(27893);let j=function(e){let{classesId:a}=e,{studentId:s}=(0,u.G)(e=>e.currentStudent),[l]=(0,p.Xd)(),r=async()=>{try{await l({classId:a,studentId:s}),c.l.success("学员退出班级成功.")}catch(e){c.l.error("学员退出班级失败!")}};return(0,t.jsxs)(m.Lt,{children:[(0,t.jsx)(h.p,{icon:x.A,tooltipText:"退班"}),(0,t.jsxs)(m.EO,{children:[(0,t.jsxs)(m.wd,{children:[(0,t.jsx)(m.r7,{children:"确认退出班级"}),(0,t.jsx)(m.$v,{children:"确定要将学生退出此班级吗？此操作不可撤销。"})]}),(0,t.jsxs)(m.ck,{children:[(0,t.jsx)(m.Zr,{children:"取消"}),(0,t.jsx)(m.Rx,{onClick:r,children:"确认退出"})]})]})]})},f=[{accessorKey:"classesName",header:"班级名称",cell:e=>{let{row:a}=e;return(0,t.jsx)("div",{className:"font-medium",children:a.original.classesName})}},{accessorKey:"courseName",header:"课程名称",cell:e=>{let{row:a}=e;return(0,t.jsx)("div",{className:"lowercase",children:a.original.courseName})}},{accessorKey:"teacher",header:"授课老师",cell:e=>{let{row:a}=e;return(0,t.jsx)("div",{className:"capitalize",children:a.original.teacherName})}},{accessorKey:"status",header:"学员状态",cell:e=>{let{row:a}=e,s=a.original.type;return"in"===s?(0,t.jsx)(o.E,{className:"lowercase",variant:"default",children:"在班"}):"out"===s?(0,t.jsx)(o.E,{className:"lowercase",variant:"destructive",children:"退班"}):(0,t.jsx)(t.Fragment,{})}},{accessorKey:"actions",header:"操作",cell:e=>{let{row:a}=e,s=a.original.classesId;return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(j,{classesId:s})})}}];var N=s(95728);let g=function(e){let{onClassUpdate:a}=e,{studentId:s}=(0,u.G)(e=>e.currentStudent),[o,m]=(0,l.useState)(!1),[x,{isLoading:h}]=(0,p.E4)(),j=async e=>{try{m(!1),await x({classId:e.id,studentIds:[s]}),c.l.success("已成功将学员添加到班级「".concat(e.name,"」")),a&&a()}catch(e){c.l.error("添加班级过程中发生错误，请稍后重试")}},{data:g,isLoading:y}=(0,N.dx)(s);return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"班级信息"}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsxs)(n.$,{variant:"default",size:"sm",className:"hover:bg-primary/90 transition-colors h-8",onClick:()=>m(!0),children:[(0,t.jsx)(d.A,{className:"w-4 h-4 mr-1.5"}),"加入班级"]})})]}),(0,t.jsx)(r.b,{columns:f,data:g||[],loading:y,emptyMessage:"该学员尚未加入任何班级"},"myClassTable"),o&&(0,t.jsx)(i.ClassSelectionDialog,{open:o,onOpenChange:m,title:"选择班级",onSelectClass:j})]})}},37777:(e,a,s)=>{s.r(a),s.d(a,{ClassSelectionDialog:()=>f});var t=s(95155),l=s(12115),r=s(54165),n=s(30285),d=s(62523),i=s(70306),c=s(85127),o=s(73168),m=s(48432),x=s(46102),h=s(59434),u=s(27893),p=s(45964),j=s.n(p);function f(e){var a,s;let{open:p,onOpenChange:f,title:N="选择班级",onSelectClass:g,trigger:y}=e,[v,w]=(0,l.useState)(""),[b,C]=(0,l.useState)(1),[k,S]=(0,l.useState)(10),[z,A]=(0,l.useState)(""),[E,I]=(0,l.useState)(!1),R=(0,l.useRef)(j()(e=>{A(e),I(!1)},500)).current,D=(0,l.useCallback)(e=>{w(e),I(!0),R(e)},[R]),_=(0,l.useMemo)(()=>({page:b,pageSize:k,name:z}),[b,k,z]),{data:L,isLoading:Z}=(0,u.Qr)(_,{skip:!p}),$=(0,l.useCallback)(e=>{e||(w(""),A(""),C(1),S(10)),f(e)},[f]),H=(0,l.useCallback)(()=>{w(""),A(""),C(1)},[]),K=(0,l.useCallback)(e=>{g&&(g(e),f(!1))},[g,f]);return(0,l.useEffect)(()=>()=>{R.cancel()},[R]),(0,t.jsxs)(r.lG,{open:p,onOpenChange:$,children:[y&&(0,t.jsx)(r.zM,{asChild:!0,children:y}),(0,t.jsxs)(r.Cf,{className:"max-w-5xl p-0 overflow-hidden rounded-xl shadow-lg border-0",children:[(0,t.jsx)(r.c7,{className:"flex flex-row justify-between items-center py-3 px-6 border-b bg-gray-50/50",children:(0,t.jsx)(r.L3,{className:"text-base font-semibold text-gray-800",children:N})}),(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsx)("div",{className:"mb-4 bg-gray-50/70 p-3 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center flex-wrap gap-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 flex-1 min-w-[280px]",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600 font-medium whitespace-nowrap",children:"班级名称："}),(0,t.jsxs)("div",{className:"relative flex-1",children:[(0,t.jsx)(d.p,{placeholder:"请输入班级名称搜索",value:v,onChange:e=>D(e.target.value),className:"h-9 rounded-lg focus-visible:ring-primary/40 transition-all pr-8"}),E&&(0,t.jsx)("div",{className:"absolute right-3 top-1/2 -translate-y-1/2",children:(0,t.jsx)("div",{className:"h-3 w-3 border-2 border-primary/60 border-t-transparent rounded-full animate-spin"})})]})]}),v&&(0,t.jsx)(n.$,{size:"sm",variant:"outline",className:"h-9 px-4 rounded-lg font-medium",onClick:H,children:"重置"})]})}),(0,t.jsxs)("div",{className:"rounded-xl border shadow-sm bg-white overflow-hidden",children:[(0,t.jsx)(c.XI,{className:"w-full [&_th]:whitespace-nowrap [&_td]:whitespace-nowrap",children:(0,t.jsx)(c.A0,{children:(0,t.jsxs)(c.Hj,{className:"bg-gray-50/80",children:[(0,t.jsx)(c.nd,{className:"font-medium text-xs py-3 text-gray-700",children:"班级名称"}),(0,t.jsx)(c.nd,{className:"font-medium text-xs py-3 text-gray-700",children:"开班时间"}),(0,t.jsx)(c.nd,{className:"font-medium text-xs py-3 text-gray-700",children:"上课老师"}),(0,t.jsx)(c.nd,{className:"font-medium text-xs py-3 text-gray-700 text-center w-20",children:"操作"})]})})}),(0,t.jsx)("div",{className:"max-h-[300px] overflow-y-auto",children:(0,t.jsx)(c.XI,{className:"w-full",children:(0,t.jsx)(c.BF,{children:Z?(0,t.jsx)(c.Hj,{children:(0,t.jsx)(c.nA,{colSpan:6,className:"h-24 text-center text-gray-400 text-sm",children:(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsx)("div",{className:"h-4 w-4 border-2 border-primary/70 border-t-transparent rounded-full animate-spin mr-2"}),"数据加载中..."]})})}):(null==L?void 0:null===(a=L.list)||void 0===a?void 0:a.length)===0?(0,t.jsx)(c.Hj,{children:(0,t.jsx)(c.nA,{colSpan:6,className:"h-24 text-center text-gray-400 text-sm",children:"暂无符合条件的班级"})}):null==L?void 0:null===(s=L.list)||void 0===s?void 0:s.map(e=>{var a;return(0,t.jsxs)(c.Hj,{className:"h-12 hover:bg-gray-50/50 transition-colors",children:[(0,t.jsx)(c.nA,{className:"font-medium text-sm py-2.5 text-gray-700",children:e.name}),(0,t.jsx)(c.nA,{className:"text-sm py-2.5 text-gray-600",children:(0,o.GP)(new Date(e.startDate),"yyyy-MM-dd")}),(0,t.jsx)(c.nA,{className:"text-sm py-2.5 text-gray-600",children:null===(a=e.teacher)||void 0===a?void 0:a.name}),(0,t.jsx)(c.nA,{className:"py-2.5 text-center",children:(0,t.jsx)(x.Bc,{children:(0,t.jsxs)(x.m_,{children:[(0,t.jsx)(x.k$,{asChild:!0,children:(0,t.jsx)(n.$,{variant:"ghost",size:"icon",className:(0,h.cn)("h-8 w-8 rounded-full","text-primary hover:text-primary hover:bg-primary/10 transition-colors"),onClick:()=>K(e),children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})}),(0,t.jsx)(x.ZI,{side:"top",className:"text-xs font-medium",children:(0,t.jsx)("p",{children:"入班"})})]})})})]},e.id)})})})})]}),(0,t.jsx)(m.default,{currentPage:(null==L?void 0:L.page)||1,pageSize:(null==L?void 0:L.pageSize)||10,totalItems:(null==L?void 0:L.total)||0,onPageChange:C,onPageSizeChange:S})]})]})]})}},90010:(e,a,s)=>{s.d(a,{$v:()=>j,EO:()=>x,Lt:()=>i,Rx:()=>f,Zr:()=>N,ck:()=>u,r7:()=>p,tv:()=>c,wd:()=>h});var t=s(95155),l=s(12115),r=s(17649),n=s(59434),d=s(30285);let i=r.bL,c=r.l9,o=r.ZL,m=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.hJ,{className:(0,n.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...l,ref:a})});m.displayName=r.hJ.displayName;let x=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsxs)(o,{children:[(0,t.jsx)(m,{}),(0,t.jsx)(r.UC,{ref:a,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...l})]})});x.displayName=r.UC.displayName;let h=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...s})};h.displayName="AlertDialogHeader";let u=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...s})};u.displayName="AlertDialogFooter";let p=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.hE,{ref:a,className:(0,n.cn)("text-lg font-semibold",s),...l})});p.displayName=r.hE.displayName;let j=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.VY,{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",s),...l})});j.displayName=r.VY.displayName;let f=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.rc,{ref:a,className:(0,n.cn)((0,d.r)(),s),...l})});f.displayName=r.rc.displayName;let N=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.ZD,{ref:a,className:(0,n.cn)((0,d.r)({variant:"outline"}),"mt-2 sm:mt-0",s),...l})});N.displayName=r.ZD.displayName}}]);