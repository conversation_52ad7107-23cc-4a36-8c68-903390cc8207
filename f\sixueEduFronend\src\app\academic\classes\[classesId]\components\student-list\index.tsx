'use client';

import React from 'react';
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table"
import { StudentListColumns } from './Columns';
import { UserPlus } from 'lucide-react';
import ClassStudentAddDialog from '@/app/academic/schedule/[classesScheduleId]/components/dialogs/class-student-add-dialog';
import { useClasses } from '@/hooks/useClasses';
import { useParams } from 'next/navigation';
import { customToast } from '@/lib/toast';
// import ClassStudentAddDialog from '../dialog/class-student-add-dialog';



const StudentList = ({ students }: { students: any[] }) => {
  const {addStudentToClass} = useClasses()
  const classesId = useParams().classesId as string
  const users = students || [];
  // const [currentPage, setCurrentPage] = React.useState(1);
  const [open, setOpen] = React.useState(false)
  const [loading, setLoading] = React.useState(false)
  const handleStudentAdd = (data: any) => {
      addStudentToClass(classesId,[data] ).then((res) => {
        customToast.success('学员添加成功.')
        return true
        // setSelectedStudents([...selectedStudents, studentId]) 
      })
    return false
  }
 
  return (
    <>
      <div className="w-full space-y-4">
        <Button
          variant="outline"
          onClick={() => setOpen(true)}
          className="hover:bg-muted/50 transition-colors duration-200 border-border/50 hover:border-border flex items-center px-4 py-2 rounded-lg"
        >
          <UserPlus className="h-4 w-4 mr-2" />
          新增学员
        </Button>
        <DataTable
          columns={StudentListColumns}
          data={users}
          loading={loading}
        // pagination={false}
        />
      </div>
      <ClassStudentAddDialog
        open={open}
        onOpenChange={setOpen}
        students={students}
        handleSave={handleStudentAdd}
      />
    </>

  );
};

export default StudentList;
