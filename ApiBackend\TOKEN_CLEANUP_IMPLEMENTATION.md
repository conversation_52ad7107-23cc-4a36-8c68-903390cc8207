# 用户登录Token清除功能实现

## 概述
实现了用户登录时自动清除之前token记录的功能，确保每个用户在任何时候只有一个有效的登录会话。

## 问题背景
之前的登录逻辑存在以下问题：
- 用户多次登录会产生多个有效token
- 旧的token不会被清除，可能导致安全风险
- 用户可能在多个设备上同时保持登录状态

## 解决方案

### 🔧 修改的文件

#### 1. `services/authService.js`
在 `login` 函数中添加了token清除逻辑：

```javascript
// 清除用户现有token记录
// 1. 删除数据库中该用户的所有refresh token
await executeQuery(client, fastify,
    `DELETE FROM user_tokens WHERE "userId" = $1`,
    [user.id],
    { queryName: 'clearExistingTokens' }
);

// 2. 清除Redis中的token hash，使现有access token失效
const tokenHashKey = `user:${user.id}:tokenHash`;
await fastify.redis.del(tokenHashKey);

// 3. 清除用户认证缓存
const cacheKey = `user:${user.id}:authData`;
await fastify.redis.del(cacheKey);
```

#### 2. `routes/user.js`
在登录路由中添加了相同的token清除逻辑：

```javascript
// 1. 删除数据库中该用户的所有refresh token
await client.query(`DELETE FROM user_tokens WHERE "userId" = $1`, [user.id]);

// 2. 清除Redis中的token hash，使现有access token失效
const tokenHashKey = `user:${user.id}:tokenHash`;
await fastify.redis.del(tokenHashKey);

// 3. 清除用户认证缓存
const cacheKey = `user:${user.id}:authData`;
await fastify.redis.del(cacheKey);
```

#### 3. `routes/root.js`
添加了健康检查路由，用于测试脚本验证服务器状态：

```javascript
fastify.get('/health', async function (request, reply) {
    return { 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    }
})
```

### 🔄 工作流程

1. **用户发起登录请求**
2. **验证用户凭据**（账号和密码）
3. **清除现有token**：
   - 从数据库删除该用户的所有refresh token
   - 从Redis删除token hash（使access token失效）
   - 清除用户认证缓存
4. **生成新token**：
   - 生成新的access token和refresh token
   - 将refresh token存储到数据库
   - 将access token hash存储到Redis
5. **返回新token给客户端**

### 🛡️ 安全特性

#### Token失效机制
- **Access Token**: 通过清除Redis中的token hash使其失效
- **Refresh Token**: 通过从数据库删除记录使其失效
- **缓存清除**: 清除用户认证缓存，强制重新验证

#### 单点登录
- 每次登录都会清除之前的所有token
- 确保用户在任何时候只有一个有效会话
- 防止token泄露后的安全风险

### 📊 数据库影响

#### user_tokens表
- 每次登录前清除该用户的所有记录
- 每次登录后只插入一条新的refresh token记录

#### Redis缓存
- 清除token hash: `user:{userId}:tokenHash`
- 清除认证缓存: `user:{userId}:authData`

### 🧪 测试

创建了测试脚本 `scripts/test-token-cleanup.js` 来验证功能：

```bash
# 运行测试（需要先启动服务器）
node scripts/test-token-cleanup.js
```

测试内容：
- ✅ 检查登录前后的token数量
- ✅ 验证旧token失效
- ✅ 验证新token有效
- ✅ 确认token不重复

### 📝 日志记录

添加了详细的日志记录：
```javascript
fastify.log.info({
    msg: '清除用户现有token记录',
    userId: user.id,
    account: user.account
});

fastify.log.info({
    msg: '用户登录成功，已清除旧token并生成新token',
    userId: user.id,
    account: user.account
});
```

### 🔍 监控要点

1. **数据库性能**: 监控user_tokens表的删除操作性能
2. **Redis性能**: 监控Redis删除操作的响应时间
3. **用户体验**: 确保清除操作不影响登录速度
4. **错误处理**: 监控token清除过程中的异常

### ⚠️ 注意事项

1. **事务一致性**: 所有操作都在数据库事务中执行
2. **错误回滚**: 如果任何步骤失败，整个事务会回滚
3. **并发安全**: 使用数据库事务确保并发登录的安全性
4. **向后兼容**: 不影响现有的token验证逻辑

### 🚀 使用方法

功能已自动集成到现有的登录流程中，无需额外配置。用户登录时会自动：
- 清除之前的所有token
- 生成新的token
- 确保只有最新的登录会话有效

这个实现确保了系统的安全性，防止了token泄露和多设备同时登录的安全风险。
