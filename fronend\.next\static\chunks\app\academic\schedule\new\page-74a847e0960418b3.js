(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1033],{5501:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(95155),r=s(12115),l=s(59409),n=s(59434),d=s(12519);let i=(0,r.memo)(function(e){let{value:t,onChange:s,width:i="w-full",placeholder:o="选择教室",className:c="",showAllOption:m=!1,allOptionText:u="全部教室",allOptionValue:x="all",list:h,disabled:f=!1}=e,{data:b,isLoading:p,error:v}=(0,d.hP)({}),j=(0,r.useCallback)(e=>{s(e)},[s]),g=(0,r.useCallback)(()=>(0,n.cn)("h-9 ".concat(i),c),[i,c]),N=h||b;return(0,a.jsxs)(l.l6,{value:t,onValueChange:j,disabled:f||p,children:[(0,a.jsx)(l.bq,{className:g(),children:(0,a.jsx)(l.yv,{placeholder:o})}),(0,a.jsxs)(l.gC,{children:[v&&(0,a.jsx)(l.eb,{value:"error",disabled:!0,children:String(v)}),p&&(0,a.jsx)(l.eb,{value:"loading",disabled:!0,children:"加载中..."}),!p&&!v&&(0,a.jsxs)(a.Fragment,{children:[m&&(0,a.jsx)(l.eb,{value:x,children:u}),null==N?void 0:N.map(e=>(0,a.jsx)(l.eb,{value:e.id,children:e.name},e.id))]})]})]})})},8643:(e,t,s)=>{"use strict";s.d(t,{Ke:()=>n,Nt:()=>r,R6:()=>l});var a=s(88106);let r=a.bL,l=a.R6,n=a.Ke},14636:(e,t,s)=>{"use strict";s.d(t,{AM:()=>d,Wv:()=>i,hl:()=>o});var a=s(95155),r=s(12115),l=s(20547),n=s(59434);let d=l.bL,i=l.l9,o=r.forwardRef((e,t)=>{let{className:s,align:r="center",sideOffset:d=4,...i}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsx)(l.UC,{ref:t,align:r,sideOffset:d,className:(0,n.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...i})})});o.displayName=l.UC.displayName},17759:(e,t,s)=>{"use strict";s.d(t,{C5:()=>v,MJ:()=>b,Rr:()=>p,eI:()=>h,lR:()=>f,lV:()=>o,zB:()=>m});var a=s(95155),r=s(12115),l=s(99708),n=s(62177),d=s(59434),i=s(85057);let o=n.Op,c=r.createContext({}),m=e=>{let{...t}=e;return(0,a.jsx)(c.Provider,{value:{name:t.name},children:(0,a.jsx)(n.xI,{...t})})},u=()=>{let e=r.useContext(c),t=r.useContext(x),{getFieldState:s,formState:a}=(0,n.xW)(),l=s(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:d}=t;return{id:d,name:e.name,formItemId:"".concat(d,"-form-item"),formDescriptionId:"".concat(d,"-form-item-description"),formMessageId:"".concat(d,"-form-item-message"),...l}},x=r.createContext({}),h=r.forwardRef((e,t)=>{let{className:s,...l}=e,n=r.useId();return(0,a.jsx)(x.Provider,{value:{id:n},children:(0,a.jsx)("div",{ref:t,className:(0,d.cn)("space-y-2",s),...l})})});h.displayName="FormItem";let f=r.forwardRef((e,t)=>{let{className:s,...r}=e,{error:l,formItemId:n}=u();return(0,a.jsx)(i.J,{ref:t,className:(0,d.cn)(l&&"text-destructive",s),htmlFor:n,...r})});f.displayName="FormLabel";let b=r.forwardRef((e,t)=>{let{...s}=e,{error:r,formItemId:n,formDescriptionId:d,formMessageId:i}=u();return(0,a.jsx)(l.DX,{ref:t,id:n,"aria-describedby":r?"".concat(d," ").concat(i):"".concat(d),"aria-invalid":!!r,...s})});b.displayName="FormControl";let p=r.forwardRef((e,t)=>{let{className:s,...r}=e,{formDescriptionId:l}=u();return(0,a.jsx)("p",{ref:t,id:l,className:(0,d.cn)("text-sm text-muted-foreground",s),...r})});p.displayName="FormDescription";let v=r.forwardRef((e,t)=>{var s;let{className:r,children:l,...n}=e,{error:i,formMessageId:o}=u(),c=i?String(null!==(s=null==i?void 0:i.message)&&void 0!==s?s:""):l;return c?(0,a.jsx)("p",{ref:t,id:o,className:(0,d.cn)("text-sm font-medium text-destructive",r),...n,children:c}):null});v.displayName="FormMessage"},22346:(e,t,s)=>{"use strict";s.d(t,{w:()=>d});var a=s(95155),r=s(12115),l=s(87489),n=s(59434);let d=r.forwardRef((e,t)=>{let{className:s,orientation:r="horizontal",decorative:d=!0,...i}=e;return(0,a.jsx)(l.b,{ref:t,decorative:d,orientation:r,className:(0,n.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",s),...i})});d.displayName=l.b.displayName},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o,r:()=>i});var a=s(95155),r=s(12115),l=s(99708),n=s(74466),d=s(59434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,t)=>{let{className:s,variant:r,size:n,asChild:o=!1,...c}=e,m=o?l.DX:"button";return(0,a.jsx)(m,{className:(0,d.cn)(i({variant:r,size:n,className:s})),ref:t,...c})});o.displayName="Button"},52536:(e,t,s)=>{"use strict";s.d(t,{default:()=>J});var a=s(95155),r=s(62177),l=s(90221),n=s(17759),d=s(30285),i=s(66695),o=s(48436),c=s(62523),m=s(92164),u=s(5501);function x(e){let{form:t}=e;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(n.zB,{control:t.control,name:"name",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"班级名称"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(c.p,{placeholder:"请输入班级名称",className:"h-10 border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 text-sm bg-slate-50 hover:bg-white transition-colors",...t})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(n.zB,{control:t.control,name:"courseId",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"课程"}),(0,a.jsx)(m.A,{value:t.value,onChange:t.onChange}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}}),(0,a.jsx)(n.zB,{control:t.control,name:"maxStudentCount",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"最多人数"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(c.p,{placeholder:"班级最多学员人数",className:"h-10 border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 text-sm bg-slate-50 hover:bg-white transition-colors",...t})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})]}),(0,a.jsx)(n.zB,{control:t.control,name:"classroom",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"教室选择"}),(0,a.jsx)(u.A,{value:t.value||"",onChange:t.onChange}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})]})}var h=s(85057),f=s(14636),b=s(84616),p=s(12115),v=s(59434);let j=e=>{let{value:t=[],onChange:s}=e,r=[{label:"周一",value:1},{label:"周二",value:2},{label:"周三",value:3},{label:"周四",value:4},{label:"周五",value:5},{label:"周六",value:6},{label:"周日",value:0}],[l,n]=(0,p.useState)(null),[i,o]=(0,p.useState)(!1),c=e=>t.some(t=>t.day===e),m=e=>t.find(t=>t.day===e),u=e=>{n(e),o(!0)},x=(e,a,r)=>{let l=[...t],n=l.findIndex(t=>t.day===e);-1!==n?l[n]={day:e,startTime:a,endTime:r}:l.push({day:e,startTime:a,endTime:r}),null==s||s(l),o(!1)},j=e=>{let a=t.filter(t=>t.day!==e);null==s||s(a)};return(0,a.jsxs)("div",{className:"rounded-md overflow-hidden border border-slate-200",children:[(0,a.jsx)("div",{className:"grid grid-cols-7",children:r.map(e=>(0,a.jsx)("div",{className:"text-center py-2.5 bg-slate-50 text-slate-600 font-medium text-sm border-b border-slate-200",children:e.label},e.value))}),(0,a.jsx)("div",{className:"grid grid-cols-7",children:r.map(e=>{var t;let s=m(e.value),r=c(e.value);return(0,a.jsxs)("div",{className:(0,v.cn)("flex flex-col items-center py-4 transition-colors",r?"bg-slate-50":"hover:bg-slate-50/50"),children:[(0,a.jsxs)(f.AM,{open:i&&l===e.value,onOpenChange:e=>{e||o(!1)},children:[(0,a.jsx)(f.Wv,{asChild:!0,children:(0,a.jsx)("div",{onClick:()=>u(e.value),className:(0,v.cn)("h-8 w-8 rounded-full flex items-center justify-center cursor-pointer transition-colors",r?"bg-slate-700 text-white hover:bg-slate-600":"border border-slate-300 text-slate-500 hover:border-slate-400 hover:text-slate-600"),children:r?(0,a.jsx)("span",{className:"text-xs font-medium",children:null==s?void 0:null===(t=s.startTime)||void 0===t?void 0:t.substring(0,2)}):(0,a.jsx)(b.A,{className:"h-3.5 w-3.5"})})}),(0,a.jsx)(f.hl,{className:"w-72 p-3 shadow-sm",align:"center",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h4",{className:"font-medium text-sm text-slate-700",children:[e.label,"课程时间"]}),(0,a.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"h-7 px-2 text-xs text-slate-500 hover:text-slate-700 hover:bg-slate-100",onClick:()=>j(e.value),children:"删除"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsx)(h.J,{htmlFor:"start-time-".concat(e.value),className:"text-xs text-slate-500",children:"开始时间"}),(0,a.jsx)("input",{id:"start-time-".concat(e.value),type:"time",defaultValue:(null==s?void 0:s.startTime)||"08:00",className:"w-full h-8 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"})]}),(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsx)(h.J,{htmlFor:"end-time-".concat(e.value),className:"text-xs text-slate-500",children:"结束时间"}),(0,a.jsx)("input",{id:"end-time-".concat(e.value),type:"time",defaultValue:(null==s?void 0:s.endTime)||"09:30",className:"w-full h-8 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"})]})]}),(0,a.jsx)(d.$,{type:"button",className:"w-full h-8 text-sm bg-slate-800 hover:bg-slate-700",onClick:()=>{let t=document.getElementById("start-time-".concat(e.value)),s=document.getElementById("end-time-".concat(e.value));x(e.value,t.value,s.value)},children:"确定"})]})})]}),s&&(0,a.jsxs)("div",{className:"text-xs mt-2 text-slate-600",children:[s.startTime," - ",s.endTime]})]},e.value)})})]})};function g(e){let{form:t}=e;return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"p-4 border rounded-md border-slate-200",children:(0,a.jsx)(n.zB,{control:t.control,name:"weekdays",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"pt-0",children:[(0,a.jsx)(n.lR,{className:"text-sm text-slate-700 mb-3 block font-medium",children:"选择上课日期"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(j,{value:t.value||[],onChange:t.onChange})}),(0,a.jsx)(n.Rr,{className:"mt-3 text-slate-500 text-xs",children:"请选择每周上课的日期并设置时间"}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})})})}var N=s(80333),y=s(22346),w=s(8643),C=s(381),k=s(47863),z=s(66474);function R(e){let{form:t}=e,[s,r]=(0,p.useState)(!1),l=t.watch("reservation.enabled"),d=t.watch("leave.enabled");return(0,a.jsxs)(w.Nt,{open:s,onOpenChange:r,className:"border border-slate-200 rounded-md shadow-sm overflow-hidden",children:[(0,a.jsxs)(w.R6,{className:"flex w-full items-center justify-between p-3 text-left hover:bg-slate-50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(C.A,{className:"h-4 w-4 text-slate-500"}),(0,a.jsx)("h3",{className:"text-sm font-medium text-slate-700",children:"高级选项"})]}),(0,a.jsx)("div",{className:"h-6 w-6 flex items-center justify-center text-slate-400",children:s?(0,a.jsx)(k.A,{className:"h-4 w-4"}):(0,a.jsx)(z.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)(w.Ke,{className:"px-4 pb-4 pt-2 space-y-5 border-t border-slate-200",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700 mt-2",children:"预约设置"}),(0,a.jsx)(n.zB,{control:t.control,name:"reservation.enabled",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between p-3 rounded-md border border-slate-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开放预约"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"允许学员预约此班级的课程"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(N.d,{checked:t.value,onCheckedChange:t.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}}),l&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-3 pl-4 border-l border-slate-200",children:[(0,a.jsx)(n.zB,{control:t.control,name:"reservation.appointmentStartTime",render:e=>{var t;let{field:s}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开放预约时间（小时）"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"课程开始前多少小时开放预约"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"number",placeholder:"如：24小时",className:"w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:null!==(t=s.value)&&void 0!==t?t:"",onChange:e=>s.onChange(Number(e.target.value)||0)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}}),(0,a.jsx)(n.zB,{control:t.control,name:"reservation.appointmentEndTime",render:e=>{var t;let{field:s}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"截止预约时间（小时）"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"课程开始前多少小时停止预约"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"number",placeholder:"如：1小时",className:"w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:null!==(t=s.value)&&void 0!==t?t:"",onChange:e=>s.onChange(Number(e.target.value)||0)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})]})]}),(0,a.jsx)(y.w,{className:"bg-slate-200"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:"考勤选择"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,a.jsx)(n.zB,{control:t.control,name:"attendance.studentScan",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"学员扫码考勤"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"允许学员通过扫码进行课程签到"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(N.d,{checked:t.value,onCheckedChange:t.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}}),(0,a.jsx)(n.zB,{control:t.control,name:"attendance.autoSystem",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"系统自动考勤"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"系统自动完成考勤流程"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(N.d,{checked:t.value,onCheckedChange:t.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}})]})]}),(0,a.jsx)(y.w,{className:"bg-slate-200"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:"请假选项"}),(0,a.jsx)(n.zB,{control:t.control,name:"leave.enabled",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between p-3 rounded-md border border-slate-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开放请假"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"允许学员请假"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(N.d,{checked:t.value,onCheckedChange:t.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}}),d&&(0,a.jsx)("div",{className:"pl-4 border-l border-slate-200",children:(0,a.jsx)(n.zB,{control:t.control,name:"leave.leaveDeadline",render:e=>{var t;let{field:s}=e;return(0,a.jsxs)(n.eI,{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"请假截止时间（小时）"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"课程开始前多少小时停止请假"}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)("input",{type:"number",placeholder:"如：2小时",className:"w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:null!==(t=s.value)&&void 0!==t?t:"",onChange:e=>s.onChange(Number(e.target.value)||0)})}),(0,a.jsx)(n.C5,{className:"text-xs"})]})}})})]}),(0,a.jsx)(y.w,{className:"bg-slate-200"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:"周期设置"}),(0,a.jsx)(n.zB,{control:t.control,name:"isShowWeekCount",render:e=>{let{field:t}=e;return(0,a.jsxs)(n.eI,{className:"flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"启用周期数"}),(0,a.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"按周期划分课程，例如：第一周期、第二周期等"})]}),(0,a.jsx)(n.MJ,{children:(0,a.jsx)(N.d,{checked:t.value,onCheckedChange:t.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}})]})]})]})}var I=s(55594);let S=I.z.object({name:I.z.string().min(1,"请输入班级名称"),courseId:I.z.string().min(1,"请选择课程包"),teacherId:I.z.string().min(1,"请选择主讲老师"),classroom:I.z.null().optional(),recurrenceType:I.z.enum(["weekly","daily"]),weekdays:I.z.array(I.z.object({day:I.z.number(),startTime:I.z.string(),endTime:I.z.string()})).optional(),daily:I.z.object({startTime:I.z.string().optional(),endTime:I.z.string().optional()}).optional(),endType:I.z.enum(["times","number_of_times"]),startDate:I.z.date().optional(),endDate:I.z.date().optional(),times:I.z.number().optional(),maxStudentCount:I.z.string().min(1,"请输入最大人数"),type:I.z.enum(["temporary","fixed"]),reservation:I.z.object({enabled:I.z.boolean(),appointmentStartTime:I.z.number().min(0,"不能小于0"),appointmentEndTime:I.z.number().min(0,"不能小于0")}),attendance:I.z.object({studentScan:I.z.boolean(),autoSystem:I.z.boolean()}),leave:I.z.object({enabled:I.z.boolean(),leaveDeadline:I.z.number().min(0,"不能小于0")}),isShowWeekCount:I.z.boolean()});var A=s(63375);let F=function(e){let{form:t}=e;return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:" pb-2 space-y-3",children:[(0,a.jsx)(n.lR,{className:"text-sm",children:"主讲老师"}),(0,a.jsx)(A.A,{teacher:t.watch("teacherId"),setTeacher:e=>t.setValue("teacherId",e),showAllOption:!1})]})})};var T=s(27893),B=s(91347);function J(){let[e]=(0,T.BU)(),t=(0,r.mN)({resolver:(0,l.u)(S),defaultValues:{name:"",recurrenceType:"weekly",weekdays:[],endType:"number_of_times",maxStudentCount:"",type:"fixed",reservation:{enabled:!1,appointmentStartTime:24,appointmentEndTime:1},attendance:{studentScan:!1,autoSystem:!1},leave:{enabled:!1,leaveDeadline:2},isShowWeekCount:!1}});async function s(t){let s={name:t.name,courseId:t.courseId,teacherId:t.teacherId,classroomId:t.classroom&&"none"!==t.classroom?t.classroom:null,weekdays:t.weekdays,maxStudentCount:t.maxStudentCount,reservation:t.reservation,attendance:t.attendance,leave:t.leave,isShowWeekCount:t.isShowWeekCount};try{let t=await e(s).unwrap();console.log(t),o.l.success("班级创建成功.")}catch(e){o.l.error("班级创建失败.")}}return(0,a.jsx)("div",{children:(0,a.jsxs)(i.Zp,{className:"border border-slate-200 shadow-sm overflow-hidden rounded-md",children:[(0,a.jsx)(i.aR,{className:"bg-slate-50 px-6 py-4 border-b border-slate-200",children:(0,a.jsx)(i.ZB,{className:"text-lg font-medium text-slate-800",children:"临时排课"})}),(0,a.jsx)(i.Wu,{className:"p-6 space-y-8",children:(0,a.jsx)(n.lV,{...t,children:(0,a.jsxs)("form",{onSubmit:t.handleSubmit(s),className:"space-y-8",children:[(0,a.jsx)("section",{children:(0,a.jsx)(x,{form:t})}),(0,a.jsx)("section",{children:(0,a.jsx)(g,{form:t})}),(0,a.jsx)("section",{children:(0,a.jsx)(F,{form:t})}),(0,a.jsx)("section",{children:(0,a.jsx)(R,{form:t})}),(0,a.jsx)("div",{className:"pt-2",children:(0,a.jsx)(B.LQ,{permission:"schedule:create",children:(0,a.jsx)(d.$,{type:"submit",className:"w-full h-10 text-sm font-medium bg-slate-800 hover:bg-slate-700 transition-colors rounded-md",children:"创建班级"})})})]})})})]})})}},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>b,gC:()=>f,l6:()=>c,yv:()=>m});var a=s(95155),r=s(12115),l=s(14582),n=s(66474),d=s(47863),i=s(5196),o=s(59434);let c=l.bL;l.YJ;let m=l.WT,u=r.forwardRef((e,t)=>{let{className:s,children:r,...d}=e;return(0,a.jsxs)(l.l9,{ref:t,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...d,children:[r,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=l.l9.displayName;let x=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.PP,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})});x.displayName=l.PP.displayName;let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.wn,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let f=r.forwardRef((e,t)=>{let{className:s,children:r,position:n="popper",...d}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:t,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...d,children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(h,{})]})})});f.displayName=l.UC.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.JU,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...r})}).displayName=l.JU.displayName;let b=r.forwardRef((e,t)=>{let{className:s,children:r,...n}=e;return(0,a.jsxs)(l.q7,{ref:t,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:r})]})});b.displayName=l.q7.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",s),...r})}).displayName=l.wv.displayName},59434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var a=s(52596),r=s(39688);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(95155),r=s(12115),l=s(59434);let n=r.forwardRef((e,t)=>{let{className:s,type:r,...n}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...n})});n.displayName="Input"},63375:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(95155),r=s(12115),l=s(59409),n=s(59434),d=s(6658);let i=(0,r.memo)(function(e){let{teacher:t,setTeacher:s,width:i="w-full",placeholder:o="选择人员",className:c="",showAllOption:m=!0,allOptionText:u="全部人员",allOptionValue:x="all",teacherList:h,disabled:f=!1}=e,{data:b,isLoading:p,error:v}=(0,d.X)(),j=(0,r.useCallback)(e=>{s(e)},[s]),g=(0,r.useCallback)(()=>(0,n.cn)("h-9 ".concat(i),c),[i,c]),N=h||b;return(0,a.jsxs)(l.l6,{value:t,onValueChange:j,disabled:f||p,children:[(0,a.jsx)(l.bq,{className:g(),children:(0,a.jsx)(l.yv,{placeholder:o})}),(0,a.jsxs)(l.gC,{children:[v&&(0,a.jsx)(l.eb,{value:"error",disabled:!0,children:String(v)}),p&&(0,a.jsx)(l.eb,{value:"loading",disabled:!0,children:"加载中..."}),!p&&!v&&(0,a.jsxs)(a.Fragment,{children:[m&&(0,a.jsx)(l.eb,{value:x,children:u}),null==N?void 0:N.map(e=>(0,a.jsx)(l.eb,{value:e.id,children:e.name},e.id))]})]})]})})},63575:(e,t,s)=>{Promise.resolve().then(s.bind(s,52536))},65436:(e,t,s)=>{"use strict";s.d(t,{G:()=>l,j:()=>r});var a=s(34540);let r=()=>(0,a.wA)(),l=a.d4},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>d,wL:()=>m});var a=s(95155),r=s(12115),l=s(59434);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});n.displayName="Card";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...r})});d.displayName="CardHeader";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});i.displayName="CardTitle";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})});o.displayName="CardDescription";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...r})});c.displayName="CardContent";let m=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...r})});m.displayName="CardFooter"},80333:(e,t,s)=>{"use strict";s.d(t,{d:()=>d});var a=s(95155),r=s(12115),l=s(4884),n=s(59434);let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.bL,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...r,ref:t,children:(0,a.jsx)(l.zi,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});d.displayName=l.bL.displayName},85057:(e,t,s)=>{"use strict";s.d(t,{J:()=>o});var a=s(95155),r=s(12115),l=s(40968),n=s(74466),d=s(59434);let i=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.b,{ref:t,className:(0,d.cn)(i(),s),...r})});o.displayName=l.b.displayName},91347:(e,t,s)=>{"use strict";s.d(t,{LQ:()=>l});var a=s(95155),r=s(65436);let l=e=>{let{permission:t,children:s,fallback:l=null,logic:n="any"}=e,d=(0,r.G)(e=>e.userPermissions.permissions);if(!t||Array.isArray(t)&&0===t.length)return(0,a.jsx)(a.Fragment,{children:s});let i=Array.isArray(t)?t:[t],o=!1;return("all"===n?i.every(e=>d.includes(e)):i.some(e=>d.includes(e)))?(0,a.jsx)(a.Fragment,{children:s}):(0,a.jsx)(a.Fragment,{children:l})};s(30285);var n=s(12115),d=s(74466),i=s(59434);let o=(0,d.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}});n.forwardRef((e,t)=>{let{className:s,variant:r,...l}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,i.cn)(o({variant:r}),s),...l})}).displayName="Alert",n.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",s),...r})}).displayName="AlertTitle",n.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",s),...r})}).displayName="AlertDescription"},92164:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(95155),r=s(12115),l=s(59409),n=s(59434),d=s(74651);let i=(0,r.memo)(function(e){let{value:t,onChange:s,width:i="w-full",placeholder:o="选择课程",className:c="",showAllOption:m=!1,allOptionText:u="全部课程",allOptionValue:x="all",list:h,disabled:f=!1}=e,{data:b,isLoading:p,error:v}=(0,d.zo)({}),j=(0,r.useCallback)(e=>{s(e)},[s]),g=(0,r.useCallback)(()=>(0,n.cn)("h-9 ".concat(i),c),[i,c]),N=h||b;return(0,a.jsxs)(l.l6,{value:t,onValueChange:j,disabled:f||p,children:[(0,a.jsx)(l.bq,{className:g(),children:(0,a.jsx)(l.yv,{placeholder:o})}),(0,a.jsxs)(l.gC,{children:[v&&(0,a.jsx)(l.eb,{value:"error",disabled:!0,children:String(v)}),p&&(0,a.jsx)(l.eb,{value:"loading",disabled:!0,children:"加载中..."}),!p&&!v&&(0,a.jsxs)(a.Fragment,{children:[m&&(0,a.jsx)(l.eb,{value:x,children:u}),null==N?void 0:N.map(e=>(0,a.jsx)(l.eb,{value:e.id,children:e.name},e.id))]})]})]})})}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8687,4201,8737,4540,4582,5589,5602,7307,9624,6315,7358],()=>t(63575)),_N_E=e.O()}]);