"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7847],{4884:(e,t,n)=>{n.d(t,{bL:()=>x,zi:()=>k});var r=n(12115),o=n(85185),a=n(6101),i=n(46081),l=n(5845),s=n(45503),d=n(11275),u=n(63655),c=n(95155),p="Switch",[f,m]=(0,i.A)(p),[v,y]=f(p),h=r.forwardRef((e,t)=>{let{__scopeSwitch:n,name:i,checked:s,defaultChecked:d,required:p,disabled:f,value:m="on",onCheckedChange:y,form:h,...g}=e,[b,x]=r.useState(null),k=(0,a.s)(t,e=>x(e)),C=r.useRef(!1),R=!b||h||!!b.closest("form"),[D=!1,j]=(0,l.i)({prop:s,defaultProp:d,onChange:y});return(0,c.jsxs)(v,{scope:n,checked:D,disabled:f,children:[(0,c.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":D,"aria-required":p,"data-state":w(D),"data-disabled":f?"":void 0,disabled:f,value:m,...g,ref:k,onClick:(0,o.m)(e.onClick,e=>{j(e=>!e),R&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),R&&(0,c.jsx)(N,{control:b,bubbles:!C.current,name:i,value:m,checked:D,required:p,disabled:f,form:h,style:{transform:"translateX(-100%)"}})]})});h.displayName=p;var g="SwitchThumb",b=r.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,o=y(g,n);return(0,c.jsx)(u.sG.span,{"data-state":w(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});b.displayName=g;var N=e=>{let{control:t,checked:n,bubbles:o=!0,...a}=e,i=r.useRef(null),l=(0,s.Z)(n),u=(0,d.X)(t);return r.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(l!==n&&t){let r=new Event("click",{bubbles:o});t.call(e,n),e.dispatchEvent(r)}},[l,n,o]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...a,tabIndex:-1,ref:i,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function w(e){return e?"checked":"unchecked"}var x=h,k=b},9607:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("CalendarCheck",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"m9 16 2 2 4-4",key:"19s6y9"}]])},14186:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},15452:(e,t,n)=>{n.d(t,{G$:()=>Z,Hs:()=>w,UC:()=>et,VY:()=>er,ZL:()=>Q,bL:()=>J,bm:()=>eo,hE:()=>en,hJ:()=>ee,l9:()=>Y});var r=n(12115),o=n(85185),a=n(6101),i=n(46081),l=n(61285),s=n(5845),d=n(19178),u=n(25519),c=n(34378),p=n(28905),f=n(63655),m=n(92293),v=n(93795),y=n(38168),h=n(99708),g=n(95155),b="Dialog",[N,w]=(0,i.A)(b),[x,k]=N(b),C=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:i,modal:d=!0}=e,u=r.useRef(null),c=r.useRef(null),[p=!1,f]=(0,s.i)({prop:o,defaultProp:a,onChange:i});return(0,g.jsx)(x,{scope:t,triggerRef:u,contentRef:c,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:d,children:n})};C.displayName=b;var R="DialogTrigger",D=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=k(R,n),l=(0,a.s)(t,i.triggerRef);return(0,g.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":X(i.open),...r,ref:l,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});D.displayName=R;var j="DialogPortal",[O,A]=N(j,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,i=k(j,t);return(0,g.jsx)(O,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,g.jsx)(p.C,{present:n||i.open,children:(0,g.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};M.displayName=j;var E="DialogOverlay",I=r.forwardRef((e,t)=>{let n=A(E,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=k(E,e.__scopeDialog);return a.modal?(0,g.jsx)(p.C,{present:r||a.open,children:(0,g.jsx)(T,{...o,ref:t})}):null});I.displayName=E;var T=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=k(E,n);return(0,g.jsx)(v.A,{as:h.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,g.jsx)(f.sG.div,{"data-state":X(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),_="DialogContent",P=r.forwardRef((e,t)=>{let n=A(_,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=k(_,e.__scopeDialog);return(0,g.jsx)(p.C,{present:r||a.open,children:a.modal?(0,g.jsx)(F,{...o,ref:t}):(0,g.jsx)(S,{...o,ref:t})})});P.displayName=_;var F=r.forwardRef((e,t)=>{let n=k(_,e.__scopeDialog),i=r.useRef(null),l=(0,a.s)(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,y.Eq)(e)},[]),(0,g.jsx)(U,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),S=r.forwardRef((e,t)=>{let n=k(_,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,g.jsx)(U,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,i;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,i;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let l=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),U=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...s}=e,c=k(_,n),p=r.useRef(null),f=(0,a.s)(t,p);return(0,m.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,g.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":X(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(K,{titleId:c.titleId}),(0,g.jsx)($,{contentRef:p,descriptionId:c.descriptionId})]})]})}),G="DialogTitle",L=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=k(G,n);return(0,g.jsx)(f.sG.h2,{id:o.titleId,...r,ref:t})});L.displayName=G;var W="DialogDescription",q=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=k(W,n);return(0,g.jsx)(f.sG.p,{id:o.descriptionId,...r,ref:t})});q.displayName=W;var B="DialogClose",V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=k(B,n);return(0,g.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function X(e){return e?"open":"closed"}V.displayName=B;var H="DialogTitleWarning",[Z,z]=(0,i.q)(H,{contentName:_,titleName:G,docsSlug:"dialog"}),K=e=>{let{titleId:t}=e,n=z(H),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},$=e=>{let{contentRef:t,descriptionId:n}=e,o=z("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(a)},[a,t,n]),null},J=C,Y=D,Q=M,ee=I,et=P,en=L,er=q,eo=V},28905:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(12115),o=n(6101),a=n(52712),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),s=r.useRef({}),d=r.useRef(e),u=r.useRef("none"),[c,p]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(s.current);u.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let t=s.current,n=d.current;if(n!==e){let r=u.current,o=l(t);e?p("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):n&&r!==o?p("ANIMATION_OUT"):p("UNMOUNT"),d.current=e}},[e,p]),(0,a.N)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=l(s.current).includes(e.animationName);if(e.target===o&&r&&(p("ANIMATION_END"),!d.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(u.current=l(s.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{e&&(s.current=getComputedStyle(e)),i(e)},[])}}(t),s="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),d=(0,o.s)(i.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof n||i.isPresent?r.cloneElement(s,{ref:d}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},40968:(e,t,n)=>{n.d(t,{b:()=>l});var r=n(12115),o=n(63655),a=n(95155),i=r.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},54416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},55670:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},63655:(e,t,n)=>{n.d(t,{hO:()=>s,sG:()=>l});var r=n(12115),o=n(47650),a=n(99708),i=n(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...o}=e,l=r?a.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},69074:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},72713:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},88106:(e,t,n)=>{n.d(t,{Ke:()=>x,R6:()=>N,bL:()=>R});var r=n(12115),o=n(85185),a=n(46081),i=n(5845),l=n(52712),s=n(6101),d=n(63655),u=n(28905),c=n(61285),p=n(95155),f="Collapsible",[m,v]=(0,a.A)(f),[y,h]=m(f),g=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:o,defaultOpen:a,disabled:l,onOpenChange:s,...u}=e,[f=!1,m]=(0,i.i)({prop:o,defaultProp:a,onChange:s});return(0,p.jsx)(y,{scope:n,disabled:l,contentId:(0,c.B)(),open:f,onOpenToggle:r.useCallback(()=>m(e=>!e),[m]),children:(0,p.jsx)(d.sG.div,{"data-state":C(f),"data-disabled":l?"":void 0,...u,ref:t})})});g.displayName=f;var b="CollapsibleTrigger",N=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,a=h(b,n);return(0,p.jsx)(d.sG.button,{type:"button","aria-controls":a.contentId,"aria-expanded":a.open||!1,"data-state":C(a.open),"data-disabled":a.disabled?"":void 0,disabled:a.disabled,...r,ref:t,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});N.displayName=b;var w="CollapsibleContent",x=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=h(w,e.__scopeCollapsible);return(0,p.jsx)(u.C,{present:n||o.open,children:e=>{let{present:n}=e;return(0,p.jsx)(k,{...r,ref:t,present:n})}})});x.displayName=w;var k=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:o,children:a,...i}=e,u=h(w,n),[c,f]=r.useState(o),m=r.useRef(null),v=(0,s.s)(t,m),y=r.useRef(0),g=y.current,b=r.useRef(0),N=b.current,x=u.open||c,k=r.useRef(x),R=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>k.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,l.N)(()=>{let e=m.current;if(e){R.current=R.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();y.current=t.height,b.current=t.width,k.current||(e.style.transitionDuration=R.current.transitionDuration,e.style.animationName=R.current.animationName),f(o)}},[u.open,o]),(0,p.jsx)(d.sG.div,{"data-state":C(u.open),"data-disabled":u.disabled?"":void 0,id:u.contentId,hidden:!x,...i,ref:v,style:{"--radix-collapsible-content-height":g?"".concat(g,"px"):void 0,"--radix-collapsible-content-width":N?"".concat(N,"px"):void 0,...e.style},children:x&&a})});function C(e){return e?"open":"closed"}var R=g}}]);