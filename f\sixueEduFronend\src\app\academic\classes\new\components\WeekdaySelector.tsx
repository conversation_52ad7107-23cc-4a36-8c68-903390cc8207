"use client"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Plus } from "lucide-react"
import { useState } from "react"
import { cn } from "@/lib/utils"

const WeekdaySelector = ({
  value = [],
  onChange,
}: {
  value?: Array<{ day: number; startTime?: string; endTime?: string }>
  onChange?: (value: Array<{ day: number; startTime?: string; endTime?: string }>) => void
}) => {
  const weekdays = [
    { label: "周一", value: 1 },
    { label: "周二", value: 2 },
    { label: "周三", value: 3 },
    { label: "周四", value: 4 },
    { label: "周五", value: 5 },
    { label: "周六", value: 6 },
    { label: "周日", value: 0 },
  ]

  const [selectedDay, setSelectedDay] = useState<number | null>(null)
  const [timePopoverOpen, setTimePopoverOpen] = useState(false)

  const isDaySelected = (day: number) => {
    return value.some((item) => item.day === day)
  }

  const getTimeForDay = (day: number) => {
    return value.find((item) => item.day === day)
  }

  const handleDayClick = (day: number) => {
    setSelectedDay(day)
    setTimePopoverOpen(true)
  }

  const handleTimeSelection = (day: number, startTime: string, endTime: string) => {
    const newValue = [...value]
    const existingIndex = newValue.findIndex((item) => item.day === day)

    if (existingIndex !== -1) {
      newValue[existingIndex] = { day, startTime, endTime }
    } else {
      newValue.push({ day, startTime, endTime })
    }

    onChange?.(newValue)
    setTimePopoverOpen(false)
  }

  const handleRemoveDay = (day: number) => {
    const newValue = value.filter((item) => item.day !== day)
    onChange?.(newValue)
  }

  return (
    <div className="rounded-md overflow-hidden border border-slate-200">
      <div className="grid grid-cols-7">
        {weekdays.map((day) => (
          <div
            key={day.value}
            className="text-center py-2.5 bg-slate-50 text-slate-600 font-medium text-sm border-b border-slate-200"
          >
            {day.label}
          </div>
        ))}
      </div>
      <div className="grid grid-cols-7">
        {weekdays.map((day) => {
          const dayData = getTimeForDay(day.value)
          const isSelected = isDaySelected(day.value)
          return (
            <div
              key={day.value}
              className={cn(
                "flex flex-col items-center py-4 transition-colors",
                isSelected ? "bg-slate-50" : "hover:bg-slate-50/50",
              )}
            >
              <Popover
                open={timePopoverOpen && selectedDay === day.value}
                onOpenChange={(open) => {
                  if (!open) setTimePopoverOpen(false)
                }}
              >
                <PopoverTrigger asChild>
                  <div
                    onClick={() => handleDayClick(day.value)}
                    className={cn(
                      "h-8 w-8 rounded-full flex items-center justify-center cursor-pointer transition-colors",
                      isSelected
                        ? "bg-slate-700 text-white hover:bg-slate-600"
                        : "border border-slate-300 text-slate-500 hover:border-slate-400 hover:text-slate-600",
                    )}
                  >
                    {isSelected ? (
                      <span className="text-xs font-medium">{dayData?.startTime?.substring(0, 2)}</span>
                    ) : (
                      <Plus className="h-3.5 w-3.5" />
                    )}
                  </div>
                </PopoverTrigger>
                <PopoverContent className="w-72 p-3 shadow-sm" align="center">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-sm text-slate-700">{day.label}课程时间</h4>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-7 px-2 text-xs text-slate-500 hover:text-slate-700 hover:bg-slate-100"
                        onClick={() => handleRemoveDay(day.value)}
                      >
                        删除
                      </Button>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-1.5">
                        <Label htmlFor={`start-time-${day.value}`} className="text-xs text-slate-500">
                          开始时间
                        </Label>
                        <input
                          id={`start-time-${day.value}`}
                          type="time"
                          defaultValue={dayData?.startTime || "08:00"}
                          className="w-full h-8 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"
                        />
                      </div>
                      <div className="space-y-1.5">
                        <Label htmlFor={`end-time-${day.value}`} className="text-xs text-slate-500">
                          结束时间
                        </Label>
                        <input
                          id={`end-time-${day.value}`}
                          type="time"
                          defaultValue={dayData?.endTime || "09:30"}
                          className="w-full h-8 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"
                        />
                      </div>
                    </div>
                    <Button
                      type="button"
                      className="w-full h-8 text-sm bg-slate-800 hover:bg-slate-700"
                      onClick={() => {
                        const startTimeInput = document.getElementById(`start-time-${day.value}`) as HTMLInputElement
                        const endTimeInput = document.getElementById(`end-time-${day.value}`) as HTMLInputElement
                        handleTimeSelection(day.value, startTimeInput.value, endTimeInput.value)
                      }}
                    >
                      确定
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
              {dayData && (
                <div className="text-xs mt-2 text-slate-600">
                  {dayData.startTime} - {dayData.endTime}
                </div>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default WeekdaySelector

