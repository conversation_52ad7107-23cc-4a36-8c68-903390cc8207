export type PurchaseRecordSchedule = {
    id: string
    amount: number
    paymentMethod: number
    paymentTime: number
    studentProductId: string
    remarks: string
    type: string
    amountPaid: number
    giftCount: number
    giftDays: number
    amountUnpaid: number
    // amountOwed: number
    discount: number
    studentProduct: {
      id: string,
      remainingBalance: number
    },
    product: {
      id: string
      name: string
    }
    student: {
      id: string
      name: string
      phone: string
    }
    salesRepresentative: {
      id: string
      name: string
    }
    // courses: {
    //   id: string;
    //   name: string;
    // };
    // currentWeeks: number;
    // endTime: string;
    // startTime: string;
    // startDate: string;
    // teacher: { // corrected property name from 'techaer' to 'teacher'
    //   id: string;
    //   name: string;
    // },
    // weekDay: number;
  }