(()=>{var e={};e.id=4397,e.ids=[4397],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4923:(e,s,t)=>{"use strict";t.d(s,{c:()=>u});var a=t(60687),r=t(43210),i=t(3589),l=t(78272),n=t(13964),d=t(70615),o=t(4780),c=t(40988),m=t(29523);function u({children:e,maxDisplayLength:s=15,className:t,popoverWidth:u="auto",showBorder:p=!1}){let[x,h]=r.useState(!1),[f,g]=r.useState(!1),v=r.useMemo(()=>{if("string"==typeof e||"number"==typeof e)return e.toString();try{let s=document.createElement("div");return s.innerHTML=e?.props?.children||"",s.textContent||""}catch(e){return console.warn("Could not extract text from children:",e),""}},[e]),j=r.useMemo(()=>{if("string"==typeof e||"number"==typeof e){let t=e.toString();return t.length>s?t.slice(0,s):t}return e},[e,s]),y=async()=>{try{await navigator.clipboard.writeText(v),g(!0),setTimeout(()=>g(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,a.jsxs)(c.AM,{open:x,onOpenChange:h,children:[(0,a.jsx)(c.Wv,{asChild:!0,children:(0,a.jsxs)(m.$,{variant:p?"outline":"ghost",role:"combobox","aria-expanded":x,className:(0,o.cn)("w-fit justify-between font-normal px-2 py-1 h-auto",!p&&"border-0 shadow-none",t),children:[(0,a.jsx)("span",{className:"mr-2 truncate",children:j}),x?(0,a.jsx)(i.A,{className:"h-4 w-4 shrink-0 opacity-50"}):(0,a.jsx)(l.A,{className:"h-4 w-4 shrink-0 opacity-50"})]})}),(0,a.jsx)(c.hl,{className:"p-0",align:"start",style:{width:u},children:(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2",children:[(0,a.jsx)("span",{className:"text-sm break-all",children:v}),(0,a.jsxs)(m.$,{variant:"ghost",size:"icon",className:"h-8 w-8 shrink-0",onClick:y,children:[f?(0,a.jsx)(n.A,{className:"h-4 w-4"}):(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:f?"Copied":"Copy text"})]})]})})]})}},9927:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(60687),r=t(43210),i=t(47033),l=t(14952),n=t(93661),d=t(4780),o=t(29523);let c=({className:e,...s})=>(0,a.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,d.cn)("mx-auto flex w-full justify-center",e),...s});c.displayName="Pagination";let m=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("ul",{ref:t,className:(0,d.cn)("flex flex-row items-center gap-1",e),...s}));m.displayName="PaginationContent";let u=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("li",{ref:t,className:(0,d.cn)("",e),...s}));u.displayName="PaginationItem";let p=({className:e,isActive:s,size:t="icon",...r})=>(0,a.jsx)("a",{"aria-current":s?"page":void 0,className:(0,d.cn)((0,o.r)({variant:s?"outline":"ghost",size:t}),e),...r});p.displayName="PaginationLink";let x=({className:e,...s})=>(0,a.jsxs)(p,{"aria-label":"Go to previous page",size:"default",className:(0,d.cn)("gap-1 pl-2.5",e),...s,children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"上一页"})]});x.displayName="PaginationPrevious";let h=({className:e,...s})=>(0,a.jsxs)(p,{"aria-label":"Go to next page",size:"default",className:(0,d.cn)("gap-1 pr-2.5",e),...s,children:[(0,a.jsx)("span",{children:"下一页"}),(0,a.jsx)(l.A,{className:"h-4 w-4"})]});h.displayName="PaginationNext";let f=({className:e,...s})=>(0,a.jsxs)("span",{"aria-hidden":!0,className:(0,d.cn)("flex h-9 w-9 items-center justify-center",e),...s,children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"更多页"})]});f.displayName="PaginationEllipsis";var g=t(15079);function v({currentPage:e,pageSize:s,totalItems:t,onPageChange:r,onPageSizeChange:n}){let d=Math.ceil(t/s),o=(()=>{let s=[];if(d<=5){for(let e=1;e<=d;e++)s.push(e);return s}s.push(1);let t=Math.max(2,e-1),a=Math.min(e+1,d-1);2===t&&(a=Math.min(t+2,d-1)),a===d-1&&(t=Math.max(a-2,2)),t>2&&s.push("ellipsis-start");for(let e=t;e<=a;e++)s.push(e);return a<d-1&&s.push("ellipsis-end"),d>1&&s.push(d),s})(),v=0===t?0:(e-1)*s+1,j=Math.min(e*s,t);return(0,a.jsxs)("div",{className:"flex flex-col gap-4 px-2 py-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,a.jsx)("span",{className:"text-muted-foreground/80",children:"显示"}),(0,a.jsxs)(g.l6,{value:s.toString(),onValueChange:e=>{n(Number(e))},children:[(0,a.jsx)(g.bq,{className:"w-[4.5rem] h-7 text-xs border-border/60 hover:bg-accent/30 transition-colors",children:(0,a.jsx)(g.yv,{})}),(0,a.jsx)(g.gC,{children:[10,20,30,50].map(e=>(0,a.jsx)(g.eb,{value:e.toString(),className:"text-xs",children:e},e))})]}),(0,a.jsx)("span",{className:"text-muted-foreground/80",children:"条"}),(0,a.jsx)("span",{className:"text-muted-foreground/40 mx-1.5",children:"|"}),t>0?(0,a.jsxs)("span",{className:"text-muted-foreground/80",children:[v,"-",j," / ",t," 条记录"]}):(0,a.jsx)("span",{className:"text-muted-foreground/80",children:"0 条记录"})]}),(0,a.jsx)(c,{children:(0,a.jsxs)(m,{className:"gap-1",children:[(0,a.jsx)(u,{children:(0,a.jsx)(x,{onClick:()=>r(Math.max(1,e-1)),className:`h-8 px-2.5 text-xs font-medium rounded-md transition-colors ${1===e?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"}`,children:(0,a.jsx)(i.A,{className:"h-4 w-4 mr-1"})})}),o.map((s,t)=>"ellipsis-start"===s||"ellipsis-end"===s?(0,a.jsx)(u,{children:(0,a.jsx)(f,{className:"h-8 w-8 flex items-center justify-center text-xs"})},`ellipsis-${t}`):(0,a.jsx)(u,{children:(0,a.jsx)(p,{onClick:()=>r(s),isActive:e===s,className:"h-8 w-8 text-xs font-medium rounded-md transition-colors data-[active]:bg-primary data-[active]:text-primary-foreground hover:bg-accent/50 hover:text-accent-foreground/90","aria-label":`前往第 ${s} 页`,children:s})},s)),(0,a.jsx)(u,{children:(0,a.jsx)(h,{onClick:()=>r(Math.min(d,e+1)),className:`h-8 px-2.5 text-xs font-medium rounded-md transition-colors ${e===d||0===d?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"}`,children:(0,a.jsx)(l.A,{className:"h-4 w-4 ml-1"})})})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11749:(e,s,t)=>{"use strict";t.d(s,{p:()=>l});var a=t(60687),r=t(29523),i=t(76242);function l({icon:e,tooltipText:s,tooltipSide:t="top",tooltipAlign:l="center",delayDuration:n=300,variant:d="ghost",size:o="icon",className:c="h-8 w-8 hover:bg-muted",...m}){return(0,a.jsx)(i.Bc,{delayDuration:n,children:(0,a.jsxs)(i.m_,{children:[(0,a.jsx)(i.k$,{asChild:!0,children:(0,a.jsx)(r.$,{variant:d,size:o,className:c,...m,children:(0,a.jsx)(e,{className:"h-4 w-4 text-muted-foreground"})})}),(0,a.jsx)(i.ZI,{side:t,align:l,className:"font-medium text-xs px-3 py-1.5",children:(0,a.jsx)("p",{children:s})})]})})}},11860:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},18306:(e,s,t)=>{Promise.resolve().then(t.bind(t,80460))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},21843:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(37413),r=t(24597),i=t(36733);function l({children:e}){return(0,a.jsxs)("div",{className:"fixed inset-0 flex flex-col",children:[(0,a.jsx)(r.default,{}),(0,a.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,a.jsx)(i.default,{}),(0,a.jsx)("main",{className:"flex-1 p-8 pt-16 overflow-auto",children:(0,a.jsx)("div",{className:"w-full mx-auto bg-white rounded-lg shadow-sm",children:e})})]})]})}},23201:(e,s,t)=>{"use strict";t.d(s,{A:()=>b});var a=t(60687),r=t(43210),i=t(27605),l=t(63442),n=t(45880),d=t(29523),o=t(63503),c=t(89667),m=t(80013),u=t(34729),p=t(69918),x=t(39907),h=t(27479),f=t(15079),g=t(3589),v=t(78272);let j=({isOpen:e,toggleCollapsible:s,form:t})=>{let r=(0,i.xW)(),l=t||r;return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between cursor-pointer hover:bg-muted/20 p-2 rounded-md transition-colors",onClick:s,children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"附加信息"}),(0,a.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:e=>e.stopPropagation(),children:e?(0,a.jsx)(g.A,{className:"h-4 w-4"}):(0,a.jsx)(v.A,{className:"h-4 w-4"})})]}),e&&(0,a.jsxs)("div",{className:"space-y-4 pt-2 pb-1 px-2 bg-muted/10 rounded-md",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"套餐图标"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.$,{type:"button",variant:"outline",size:"sm",onClick:()=>document.getElementById("icon-upload")?.click(),children:"选择文件"}),(0,a.jsx)("input",{id:"icon-upload",type:"file",className:"hidden",onChange:e=>{let s=e.target.files?.[0];s&&l&&l.setValue("icon",s)}}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:l&&l.watch("icon")?l.watch("icon").name:"未选择文件"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"套餐图片"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.$,{type:"button",variant:"outline",size:"sm",onClick:()=>document.getElementById("image-upload")?.click(),children:"选择文件"}),(0,a.jsx)("input",{id:"image-upload",type:"file",className:"hidden",onChange:e=>{let s=e.target.files?.[0];s&&l&&l.setValue("image",s)}}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:l&&l.watch("image")?l.watch("image").name:"未选择文件"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"套餐介绍"}),(0,a.jsx)(u.T,{placeholder:"输入套餐详细介绍",rows:3,className:"resize-none",...l&&{...l.register("description"),value:l.watch("description")||""}})]})]})]})},y=n.Ik({name:n.Yj().min(1,{message:"套餐名称不能为空"}),price:n.ai().min(0,{message:"价格不能为负数"}),leaveCount:n.ai().min(0,{message:"请假次数不能为负数"}),isShow:n.zM().default(!1),packageType:n.k5(["limited-sessions","limited-time-and-count"]),usageLimit:n.ai().optional(),timeLimitedUsage:n.ai().optional(),timeLimitType:n.k5(["daily","monthly"]).optional(),validTimeRange:n.k5(["purchase-date","consumption-date"]).optional(),remarks:n.Yj().optional(),icon:n.bz().optional(),image:n.bz().optional(),description:n.Yj().optional()}).refine(e=>"limited-sessions"!==e.packageType||void 0!==e.usageLimit&&e.usageLimit>0,{message:"限次套餐必须设置有效的次数",path:["usageLimit"]}).refine(e=>"limited-time-and-count"!==e.packageType||void 0!==e.timeLimitedUsage&&e.timeLimitedUsage>0&&void 0!==e.timeLimitType,{message:"限时套餐必须设置有效的时长和单位",path:["timeLimitedUsage"]}).refine(e=>"limited-time-and-count"!==e.packageType||void 0!==e.validTimeRange,{message:"时次限套餐必须选择时长开始方式",path:["validTimeRange"]}),b=({visible:e,initialValues:s,onOk:t,onCancel:n})=>{let[g,v]=(0,r.useState)(!1),[b,N]=(0,r.useState)(!1),{register:w,handleSubmit:k,setValue:C,watch:R,reset:L,formState:{errors:A},control:E}=(0,i.mN)({resolver:(0,l.u)(y),defaultValues:{name:"",price:0,leaveCount:0,isShow:!1,packageType:"limited-sessions",usageLimit:1,timeLimitedUsage:1,timeLimitType:"daily",validTimeRange:"purchase-date",remarks:"",description:""}}),P=R("packageType"),T=R("isShow");(0,r.useEffect)(()=>{s&&L({name:s.name||"",price:Number(s.price)||0,leaveCount:Number(s.leaveCount)||0,isShow:s.isShow||!1,packageType:s.packageType||"limited-sessions",usageLimit:Number(s.usageLimit)||1,timeLimitedUsage:Number(s.timeLimitedUsage)||30,timeLimitType:s.timeLimitType||"daily",validTimeRange:s.validTimeRange||"purchase-date",remarks:s.remarks||"",description:s.description||""})},[s,L]);let z=async e=>{try{N(!0),"limited-sessions"===e.packageType&&(delete e.timeLimitedUsage,delete e.timeLimitType,delete e.validTimeRange),t(e)}catch(e){console.error("提交表单时出错:",e)}finally{N(!1)}};return(0,a.jsx)(o.lG,{open:e,onOpenChange:n,children:(0,a.jsxs)(o.Cf,{className:"sm:max-w-[640px]",children:[(0,a.jsx)(o.c7,{children:(0,a.jsx)(o.L3,{className:"text-xl",children:s?"编辑套餐":"新增套餐"})}),(0,a.jsxs)("form",{onSubmit:k(z),children:[(0,a.jsxs)("div",{className:"grid gap-6 py-4 px-2 max-h-[60vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"套餐名称"}),(0,a.jsx)(c.p,{placeholder:"输入套餐名称",...w("name")}),A.name&&(0,a.jsx)("p",{className:"text-xs text-destructive",children:A.name.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"套餐价格"}),(0,a.jsx)(c.p,{type:"number",placeholder:"0.00",...w("price",{valueAsNumber:!0,onChange:e=>{C("price",""===e.target.value?0:Number.parseFloat(e.target.value))}})}),A.price&&(0,a.jsx)("p",{className:"text-xs text-destructive",children:A.price.message})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"请假次数"}),(0,a.jsx)(c.p,{type:"number",placeholder:"请输入请假次数",...w("leaveCount",{valueAsNumber:!0,onChange:e=>{C("leaveCount",""===e.target.value?0:Number.parseInt(e.target.value))}})}),A.leaveCount&&(0,a.jsx)("p",{className:"text-xs text-destructive",children:A.leaveCount.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"是否显示在官网"}),(0,a.jsxs)("div",{className:"flex items-center h-10 px-3 rounded-md border",children:[(0,a.jsx)(h.d,{checked:T,onCheckedChange:e=>C("isShow",e)}),(0,a.jsx)("span",{className:"ml-3 text-sm",children:T?"显示":"不显示"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"套餐类别"}),(0,a.jsxs)(p.z,{disabled:!!s,value:P,onValueChange:e=>C("packageType",e),className:"grid gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 p-2 border rounded-md",children:[(0,a.jsx)(p.C,{value:"limited-sessions",id:"limited-sessions"}),(0,a.jsx)(m.J,{htmlFor:"limited-sessions",className:"cursor-pointer",children:"限次套餐（消费课时/次）"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 p-2 border rounded-md",children:[(0,a.jsx)(p.C,{value:"limited-time-and-count",id:"limited-time-and-count"}),(0,a.jsx)(m.J,{htmlFor:"limited-time-and-count",className:"cursor-pointer",children:"时次限套餐（限时限次）"})]})]}),A.packageType&&(0,a.jsx)("p",{className:"text-xs text-destructive",children:A.packageType.message})]}),(0,a.jsxs)("div",{className:"grid gap-4",children:["limited-time-and-count"===P&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 animate-in fade-in duration-300",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"时长"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(c.p,{type:"number",placeholder:"时长",className:"w-full",...w("timeLimitedUsage",{valueAsNumber:!0,onChange:e=>{C("timeLimitedUsage",""===e.target.value?0:Number.parseInt(e.target.value))}})}),(0,a.jsxs)(f.l6,{value:R("timeLimitType"),onValueChange:e=>C("timeLimitType",e),children:[(0,a.jsx)(f.bq,{className:"w-24",children:(0,a.jsx)(f.yv,{placeholder:"单位"})}),(0,a.jsxs)(f.gC,{children:[(0,a.jsx)(f.eb,{value:"daily",children:"天"}),(0,a.jsx)(f.eb,{value:"monthly",children:"月"})]})]})]}),A.timeLimitedUsage&&(0,a.jsx)("p",{className:"text-xs text-destructive",children:A.timeLimitedUsage.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"时长开始"}),(0,a.jsxs)(p.z,{value:R("validTimeRange"),disabled:!!s,onValueChange:e=>C("validTimeRange",e),className:"flex space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.C,{value:"purchase-date",id:"purchase-date"}),(0,a.jsx)(m.J,{htmlFor:"purchase-date",className:"cursor-pointer",children:"购买日算起"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.C,{value:"consumption-date",id:"consumption-date"}),(0,a.jsx)(m.J,{htmlFor:"consumption-date",className:"cursor-pointer",children:"消费日算起"})]})]}),A.validTimeRange&&(0,a.jsx)("p",{className:"text-xs text-destructive",children:A.validTimeRange.message})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"消课次数"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(c.p,{type:"number",placeholder:"次数",className:"w-full",...w("usageLimit",{valueAsNumber:!0,onChange:e=>{C("usageLimit",""===e.target.value?0:Number.parseInt(e.target.value))}})}),(0,a.jsx)("div",{className:"flex items-center justify-center px-3 border rounded-md whitespace-nowrap",children:"次"})]}),A.usageLimit&&(0,a.jsx)("p",{className:"text-xs text-destructive",children:A.usageLimit.message})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"套餐备注"}),(0,a.jsx)(u.T,{placeholder:"输入套餐备注信息",rows:3,className:"resize-none",...w("remarks")}),A.remarks&&(0,a.jsx)("p",{className:"text-xs text-destructive",children:A.remarks.message})]}),(0,a.jsx)(x.w,{className:"my-2"}),(0,a.jsx)(j,{isOpen:g,toggleCollapsible:()=>{v(!g)},form:{register:w,setValue:C,watch:R,control:E}})]}),(0,a.jsxs)(o.Es,{className:"mt-4",children:[(0,a.jsx)(d.$,{type:"button",variant:"outline",onClick:n,children:"取消"}),(0,a.jsx)(d.$,{type:"submit",disabled:b,children:b?"处理中...":"保存"})]})]})]})})}},26689:(e,s,t)=>{"use strict";t.d(s,{default:()=>q});var a=t(60687),r=t(43210),i=t.n(r),l=t(96474),n=t(29523),d=t(23201),o=t(84778),c=t(89667),m=t(15079),u=t(9927),p=t(92053),x=t(4733),h=t(27479),f=t(53541),g=t(93500),v=t(76242),j=t(88233);let y=function({id:e}){let[s]=(0,p.lY)(),t=(0,r.useCallback)(async()=>{if(e)try{await s(e),o.l.success("删除产品成功.")}catch(e){o.l.error(e?.message||"删除产品失败!")}},[e]);return(0,a.jsx)(f.LQ,{permission:"product:delete",children:(0,a.jsx)(v.Bc,{delayDuration:300,children:(0,a.jsxs)(v.m_,{children:[(0,a.jsx)(v.k$,{asChild:!0,children:(0,a.jsxs)(g.Lt,{children:[(0,a.jsx)(g.tv,{asChild:!0,children:(0,a.jsx)(n.$,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-destructive/90 hover:text-destructive-foreground",children:(0,a.jsx)(j.A,{className:"h-4 w-4 text-muted-foreground hover:text-white"})})}),(0,a.jsxs)(g.EO,{children:[(0,a.jsxs)(g.wd,{children:[(0,a.jsx)(g.r7,{children:"确认删除"}),(0,a.jsx)(g.$v,{children:"您确定要删除该套餐吗？此操作不可撤销。"})]}),(0,a.jsxs)(g.ck,{children:[(0,a.jsx)(g.Zr,{children:"取消"}),(0,a.jsx)(g.Rx,{className:"bg-destructive hover:bg-destructive/90",onClick:t,children:"删除"})]})]})]})}),(0,a.jsx)(v.ZI,{side:"top",className:"font-medium text-xs px-3 py-1.5 bg-background border shadow-sm",children:(0,a.jsx)("p",{children:"删除套餐"})})]})})})};var b=t(62688);let N=(0,b.A)("FilePen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]]);var w=t(30036),k=t(11749);let C=(0,w.default)(async()=>{},{loadableGenerated:{modules:["app\\academic\\products\\components\\product-table\\actions\\edit.tsx -> ../../dialogs/product-form"]},ssr:!1}),R=function({data:e}){let[s]=(0,p.vM)(),[t,i]=(0,r.useState)(!1),l=async t=>{try{await s({id:e.id,product:t}),i(!1)}catch(e){console.error("更新产品失败:",e)}};return(0,a.jsxs)(f.LQ,{permission:"product:update",children:[(0,a.jsx)(k.p,{icon:N,tooltipText:"编辑套餐",onClick:()=>i(!0)}),t&&(0,a.jsx)(C,{visible:t,initialValues:e,onOk:l,onCancel:()=>i(!1)},`edit-product-form-${e.id}`)]})},L=(0,b.A)("Blocks",[["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["path",{d:"M10 21V8a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1H3",key:"1fpvtg"}]]),A=(0,w.default)(()=>t.e(6e3).then(t.bind(t,86e3)),{loadableGenerated:{modules:["app\\academic\\products\\components\\product-table\\actions\\bind.tsx -> ../../dialogs/bind-course"]}}),E=function({data:e}){let[s,t]=i().useState(!1);return(0,a.jsxs)(f.LQ,{permission:"product:update",children:[(0,a.jsx)(k.p,{icon:L,tooltipText:"绑定课程",onClick:()=>t(!0)}),s&&(0,a.jsx)(A,{open:s,onOpenChange:t,data:e})]})};var P=t(96834),T=t(42474);function z({id:e,status:s}){let[t]=(0,p.vM)();return(0,a.jsx)(h.d,{defaultChecked:"active"===s,onCheckedChange:s=>{t({id:e,product:{status:s?"active":"inactive"}}).then(()=>{o.l.success("更新状态成功.")})}})}let S=[{accessorKey:"name",header:"套餐名称",cell:({row:e})=>(0,a.jsx)("div",{className:"font-medium text-slate-800",children:e.getValue("name")})},{accessorKey:"packageType",header:"套餐类型",cell:({row:e})=>{let s=e.getValue("packageType"),t=e.original.usageLimit||0;if("limited-sessions"===s)return(0,a.jsxs)(P.E,{variant:"outline",className:"font-normal px-3 py-1 rounded-full text-xs",children:["限次(",t,"/次)"]});if("limited-time-and-count"===s){let s="daily"===e.original.timeLimitType?"天":"月",r=e.original.timeLimitedUsage||0;return(0,a.jsxs)(P.E,{variant:"secondary",className:"font-normal px-3 py-1 rounded-full text-xs",children:["时次限(",r,"/",s,"、",t,"/次)"]})}return null}},{accessorKey:"price",header:"价格",cell:({row:e})=>(0,a.jsxs)("div",{className:"text-sm text-slate-600",children:["\xa5",e.getValue("price")]})},{accessorKey:"leaveCount",header:"请假总次数",cell:({row:e})=>(0,a.jsx)("div",{className:"text-sm text-slate-600 text-center",children:e.getValue("leaveCount")})},{accessorKey:"status",header:"状态",cell:({row:e})=>{let s=e.getValue("status");return(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(z,{id:e.original.id,status:s})})}},{accessorKey:"createdAt",header:"创建时间",cell:({row:e})=>(0,a.jsx)("div",{className:"text-sm text-slate-600",children:new Date(e.getValue("createdAt")).toLocaleDateString()})},{accessorKey:"remark",header:"备注",cell:({row:e})=>(0,T.s)(e.getValue("remark"))},{id:"actions",header:"操作",cell:({row:e})=>{let s=e.original;return(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(R,{data:s}),(0,a.jsx)(E,{data:s}),(0,a.jsx)(y,{id:s.id})]})}}];var V=t(20540),M=t.n(V);function q(){let[e]=(0,p.Q$)(),[s,t]=(0,r.useState)("edit"),[i,h]=(0,r.useState)(!1),[g,v]=(0,r.useState)(),[j,y]=(0,r.useState)(""),[b,N]=(0,r.useState)(""),[w,k]=(0,r.useState)({currentPage:1,pageSize:10,total:0}),C=(0,r.useCallback)(M()(e=>{y(e)},500),[]),R=(0,r.useCallback)(e=>{let s=e.target.value;e.target.value=s,C(s)},[C]),L=(0,r.useMemo)(()=>({search:j,page:w.currentPage,pageSize:w.pageSize,type:"all"===b?"":b}),[j,w,b]),{data:A,isLoading:E}=(0,p.Kc)(L),P=async t=>{try{"add"===s&&(await e(t),h(!1),o.l.success("新增产品成功."))}catch(e){o.l.error(e?.message||"新增产品失败!")}};return(0,a.jsxs)("div",{className:"p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-6 rounded-lg bg-white p-5 shadow-sm transition-all",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-4 border-b border-gray-100",children:[(0,a.jsx)("h2",{className:"text-xl font-medium text-gray-800",children:"产品套餐管理"}),(0,a.jsx)(f.LQ,{permission:"product:create",children:(0,a.jsxs)(n.$,{onClick:()=>{t("add"),v(void 0),h(!0)},className:"h-10 gap-1.5 ",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),"新增套餐"]})})]}),(0,a.jsx)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-3 w-full sm:w-auto",children:[(0,a.jsx)("div",{className:"flex items-center gap-2 flex-1 sm:flex-none",children:(0,a.jsx)(c.p,{placeholder:"搜索套餐名称",defaultValue:j,onChange:R,className:"h-10 min-w-[180px] sm:max-w-[220px]"})}),(0,a.jsx)("div",{className:"flex items-center gap-2 flex-1 sm:flex-none",children:(0,a.jsxs)(m.l6,{value:b,onValueChange:N,children:[(0,a.jsx)(m.bq,{className:"h-10 min-w-[120px] sm:max-w-[180px]",children:(0,a.jsx)(m.yv,{placeholder:"套餐类型"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"all",children:"全部类型"}),(0,a.jsx)(m.eb,{value:"limited-sessions",children:"限次"}),(0,a.jsx)(m.eb,{value:"limited-time-and-count",children:"时次限"})]})]})})]})}),(0,a.jsx)(x.b,{data:A?.list||[],columns:S,pagination:!1,loading:E}),(0,a.jsx)(u.default,{pageSize:A?.pageSize||10,currentPage:A?.page||1,totalItems:A?.total||0,onPageChange:e=>{k(s=>({...s,currentPage:e}))},onPageSizeChange:e=>{k(s=>({...s,pageSize:e}))}})]}),i&&(0,a.jsx)(d.A,{visible:i,initialValues:g,onOk:P,onCancel:()=>h(!1)},"add product form")]})}},27479:(e,s,t)=>{"use strict";t.d(s,{d:()=>w});var a=t(60687),r=t(43210),i=t(70569),l=t(98599),n=t(11273),d=t(65551),o=t(83721),c=t(18853),m=t(14163),u="Switch",[p,x]=(0,n.A)(u),[h,f]=p(u),g=r.forwardRef((e,s)=>{let{__scopeSwitch:t,name:n,checked:o,defaultChecked:c,required:u,disabled:p,value:x="on",onCheckedChange:f,form:g,...v}=e,[j,N]=r.useState(null),w=(0,l.s)(s,e=>N(e)),k=r.useRef(!1),C=!j||g||!!j.closest("form"),[R=!1,L]=(0,d.i)({prop:o,defaultProp:c,onChange:f});return(0,a.jsxs)(h,{scope:t,checked:R,disabled:p,children:[(0,a.jsx)(m.sG.button,{type:"button",role:"switch","aria-checked":R,"aria-required":u,"data-state":b(R),"data-disabled":p?"":void 0,disabled:p,value:x,...v,ref:w,onClick:(0,i.m)(e.onClick,e=>{L(e=>!e),C&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),C&&(0,a.jsx)(y,{control:j,bubbles:!k.current,name:n,value:x,checked:R,required:u,disabled:p,form:g,style:{transform:"translateX(-100%)"}})]})});g.displayName=u;var v="SwitchThumb",j=r.forwardRef((e,s)=>{let{__scopeSwitch:t,...r}=e,i=f(v,t);return(0,a.jsx)(m.sG.span,{"data-state":b(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:s})});j.displayName=v;var y=e=>{let{control:s,checked:t,bubbles:i=!0,...l}=e,n=r.useRef(null),d=(0,o.Z)(t),m=(0,c.X)(s);return r.useEffect(()=>{let e=n.current,s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==t&&s){let a=new Event("click",{bubbles:i});s.call(e,t),e.dispatchEvent(a)}},[d,t,i]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...l,tabIndex:-1,ref:n,style:{...e.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function b(e){return e?"checked":"unchecked"}var N=t(4780);let w=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(g,{className:(0,N.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:t,children:(0,a.jsx)(j,{className:(0,N.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));w.displayName=g.displayName},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,s,t)=>{"use strict";t.d(s,{T:()=>l});var a=t(60687),r=t(43210),i=t(4780);let l=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...s}));l.displayName="Textarea"},34986:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(60687),r=t(85726);function i(){return(0,a.jsx)("div",{className:"p-4 space-y-4",children:(0,a.jsxs)("div",{className:"space-y-6 rounded-lg bg-white p-5 shadow-sm transition-all",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-4 border-b border-gray-100",children:[(0,a.jsx)(r.E,{className:"h-8 w-48"}),(0,a.jsx)(r.E,{className:"h-10 w-28"})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-3 w-full sm:w-auto",children:[(0,a.jsx)(r.E,{className:"h-10 w-[220px]"}),(0,a.jsx)(r.E,{className:"h-10 w-[180px]"}),(0,a.jsx)(r.E,{className:"h-10 w-24"})]}),(0,a.jsx)(r.E,{className:"h-10 w-10"})]}),(0,a.jsxs)("div",{className:"border rounded-md",children:[(0,a.jsx)("div",{className:"flex border-b p-2 bg-muted/30",children:[,,,,,].fill(0).map((e,s)=>(0,a.jsx)(r.E,{className:"h-6 flex-1 mx-2"},s))}),Array(8).fill(0).map((e,s)=>(0,a.jsx)("div",{className:"flex border-b p-3",children:[,,,,,].fill(0).map((e,s)=>(0,a.jsx)(r.E,{className:"h-5 flex-1 mx-2"},s))},s))]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,a.jsx)(r.E,{className:"h-8 w-40"}),(0,a.jsx)(r.E,{className:"h-8 w-64"})]})]})})}},35243:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\academic\\\\products\\\\components\\\\ProductManagement.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\products\\components\\ProductManagement.tsx","default")},36458:(e,s,t)=>{Promise.resolve().then(t.bind(t,34986))},39037:(e,s,t)=>{Promise.resolve().then(t.bind(t,35243))},39907:(e,s,t)=>{"use strict";t.d(s,{w:()=>c});var a=t(60687),r=t(43210),i=t(14163),l="horizontal",n=["horizontal","vertical"],d=r.forwardRef((e,s)=>{var t;let{decorative:r,orientation:d=l,...o}=e,c=(t=d,n.includes(t))?d:l;return(0,a.jsx)(i.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:s})});d.displayName="Separator";var o=t(4780);let c=r.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...r},i)=>(0,a.jsx)(d,{ref:i,decorative:t,orientation:s,className:(0,o.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));c.displayName=d.displayName},40988:(e,s,t)=>{"use strict";t.d(s,{AM:()=>n,Wv:()=>d,hl:()=>o});var a=t(60687),r=t(43210),i=t(40599),l=t(4780);let n=i.bL,d=i.l9,o=r.forwardRef(({className:e,align:s="center",sideOffset:t=4,...r},n)=>(0,a.jsx)(i.ZL,{children:(0,a.jsx)(i.UC,{ref:n,align:s,sideOffset:t,className:(0,l.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));o.displayName=i.UC.displayName},42474:(e,s,t)=>{"use strict";t.d(s,{P:()=>i,s:()=>l});var a=t(60687),r=t(4923);let i=(e,s=!1)=>e?(0,a.jsx)("div",{className:`text-sm ${s?"text-muted-foreground":""}`,children:e}):(0,a.jsx)("div",{}),l=(e,s=2)=>e?(0,a.jsx)(r.c,{maxDisplayLength:s,children:e}):(0,a.jsx)("div",{className:"text-sm text-muted-foreground"})},53541:(e,s,t)=>{"use strict";t.d(s,{LQ:()=>i});var a=t(60687),r=t(30596);let i=({permission:e,children:s,fallback:t=null,logic:i="any"})=>{let l=(0,r.G)(e=>e.userPermissions.permissions);if(!e||Array.isArray(e)&&0===e.length)return(0,a.jsx)(a.Fragment,{children:s});let n=Array.isArray(e)?e:[e],d=!1;return("all"===i?n.every(e=>l.includes(e)):n.some(e=>l.includes(e)))?(0,a.jsx)(a.Fragment,{children:s}):(0,a.jsx)(a.Fragment,{children:t})};t(29523);var l=t(43210),n=t(24224),d=t(4780);let o=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}});l.forwardRef(({className:e,variant:s,...t},r)=>(0,a.jsx)("div",{ref:r,role:"alert",className:(0,d.cn)(o({variant:s}),e),...t})).displayName="Alert",l.forwardRef(({className:e,...s},t)=>(0,a.jsx)("h5",{ref:t,className:(0,d.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle",l.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,d.cn)("text-sm [&_p]:leading-relaxed",e),...s})).displayName="AlertDescription"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57597:(e,s,t)=>{Promise.resolve().then(t.bind(t,26689))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63442:(e,s,t)=>{"use strict";t.d(s,{u:()=>o});var a=t(27605);let r=(e,s,t)=>{if(e&&"reportValidity"in e){let r=(0,a.Jt)(t,s);e.setCustomValidity(r&&r.message||""),e.reportValidity()}},i=(e,s)=>{for(let t in s.fields){let a=s.fields[t];a&&a.ref&&"reportValidity"in a.ref?r(a.ref,t,e):a&&a.refs&&a.refs.forEach(s=>r(s,t,e))}},l=(e,s)=>{s.shouldUseNativeValidation&&i(e,s);let t={};for(let r in e){let i=(0,a.Jt)(s.fields,r),l=Object.assign(e[r]||{},{ref:i&&i.ref});if(n(s.names||Object.keys(e),r)){let e=Object.assign({},(0,a.Jt)(t,r));(0,a.hZ)(e,"root",l),(0,a.hZ)(t,r,e)}else(0,a.hZ)(t,r,l)}return t},n=(e,s)=>{let t=d(s);return e.some(e=>d(e).match(`^${t}\\.\\d+`))};function d(e){return e.replace(/\]|\[/g,"")}function o(e,s,t){return void 0===t&&(t={}),function(r,n,d){try{return Promise.resolve(function(a,l){try{var n=Promise.resolve(e["sync"===t.mode?"parse":"parseAsync"](r,s)).then(function(e){return d.shouldUseNativeValidation&&i({},d),{errors:{},values:t.raw?Object.assign({},r):e}})}catch(e){return l(e)}return n&&n.then?n.then(void 0,l):n}(0,function(e){if(Array.isArray(null==e?void 0:e.errors))return{values:{},errors:l(function(e,s){for(var t={};e.length;){var r=e[0],i=r.code,l=r.message,n=r.path.join(".");if(!t[n]){if("unionErrors"in r){var d=r.unionErrors[0].errors[0];t[n]={message:d.message,type:d.code}}else t[n]={message:l,type:i}}if("unionErrors"in r&&r.unionErrors.forEach(function(s){return s.errors.forEach(function(s){return e.push(s)})}),s){var o=t[n].types,c=o&&o[r.code];t[n]=(0,a.Gb)(n,s,t,i,c?[].concat(c,r.message):r.message)}e.shift()}return t}(e.errors,!d.shouldUseNativeValidation&&"all"===d.criteriaMode),d)};throw e}))}catch(e){return Promise.reject(e)}}}},63503:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>m,Es:()=>p,L3:()=>x,c7:()=>u,lG:()=>d});var a=t(60687),r=t(43210),i=t(26134),l=t(11860),n=t(4780);let d=i.bL;i.l9;let o=i.ZL;i.bm;let c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(i.hJ,{ref:t,className:(0,n.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));c.displayName=i.hJ.displayName;let m=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(o,{children:[(0,a.jsx)(c,{}),(0,a.jsxs)(i.UC,{ref:r,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[s,(0,a.jsxs)(i.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=i.UC.displayName;let u=({className:e,...s})=>(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});u.displayName="DialogHeader";let p=({className:e,...s})=>(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});p.displayName="DialogFooter";let x=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(i.hE,{ref:t,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));x.displayName=i.hE.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(i.VY,{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s})).displayName=i.VY.displayName},69918:(e,s,t)=>{"use strict";t.d(s,{z:()=>U,C:()=>$});var a=t(60687),r=t(43210),i=t(70569),l=t(98599),n=t(11273),d=t(14163),o=t(72942),c=t(65551),m=t(43),u=t(18853),p=t(83721),x=t(46059),h="Radio",[f,g]=(0,n.A)(h),[v,j]=f(h),y=r.forwardRef((e,s)=>{let{__scopeRadio:t,name:n,checked:o=!1,required:c,disabled:m,value:u="on",onCheck:p,form:x,...h}=e,[f,g]=r.useState(null),j=(0,l.s)(s,e=>g(e)),y=r.useRef(!1),b=!f||x||!!f.closest("form");return(0,a.jsxs)(v,{scope:t,checked:o,disabled:m,children:[(0,a.jsx)(d.sG.button,{type:"button",role:"radio","aria-checked":o,"data-state":k(o),"data-disabled":m?"":void 0,disabled:m,value:u,...h,ref:j,onClick:(0,i.m)(e.onClick,e=>{o||p?.(),b&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),b&&(0,a.jsx)(w,{control:f,bubbles:!y.current,name:n,value:u,checked:o,required:c,disabled:m,form:x,style:{transform:"translateX(-100%)"}})]})});y.displayName=h;var b="RadioIndicator",N=r.forwardRef((e,s)=>{let{__scopeRadio:t,forceMount:r,...i}=e,l=j(b,t);return(0,a.jsx)(x.C,{present:r||l.checked,children:(0,a.jsx)(d.sG.span,{"data-state":k(l.checked),"data-disabled":l.disabled?"":void 0,...i,ref:s})})});N.displayName=b;var w=e=>{let{control:s,checked:t,bubbles:i=!0,...l}=e,n=r.useRef(null),d=(0,p.Z)(t),o=(0,u.X)(s);return r.useEffect(()=>{let e=n.current,s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==t&&s){let a=new Event("click",{bubbles:i});s.call(e,t),e.dispatchEvent(a)}},[d,t,i]),(0,a.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:t,...l,tabIndex:-1,ref:n,style:{...e.style,...o,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function k(e){return e?"checked":"unchecked"}var C=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],R="RadioGroup",[L,A]=(0,n.A)(R,[o.RG,g]),E=(0,o.RG)(),P=g(),[T,z]=L(R),S=r.forwardRef((e,s)=>{let{__scopeRadioGroup:t,name:r,defaultValue:i,value:l,required:n=!1,disabled:u=!1,orientation:p,dir:x,loop:h=!0,onValueChange:f,...g}=e,v=E(t),j=(0,m.jH)(x),[y,b]=(0,c.i)({prop:l,defaultProp:i,onChange:f});return(0,a.jsx)(T,{scope:t,name:r,required:n,disabled:u,value:y,onValueChange:b,children:(0,a.jsx)(o.bL,{asChild:!0,...v,orientation:p,dir:j,loop:h,children:(0,a.jsx)(d.sG.div,{role:"radiogroup","aria-required":n,"aria-orientation":p,"data-disabled":u?"":void 0,dir:j,...g,ref:s})})})});S.displayName=R;var V="RadioGroupItem",M=r.forwardRef((e,s)=>{let{__scopeRadioGroup:t,disabled:n,...d}=e,c=z(V,t),m=c.disabled||n,u=E(t),p=P(t),x=r.useRef(null),h=(0,l.s)(s,x),f=c.value===d.value,g=r.useRef(!1);return r.useEffect(()=>{let e=e=>{C.includes(e.key)&&(g.current=!0)},s=()=>g.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",s),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",s)}},[]),(0,a.jsx)(o.q7,{asChild:!0,...u,focusable:!m,active:f,children:(0,a.jsx)(y,{disabled:m,required:c.required,checked:f,...p,...d,name:c.name,ref:h,onCheck:()=>c.onValueChange(d.value),onKeyDown:(0,i.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,i.m)(d.onFocus,()=>{g.current&&x.current?.click()})})})});M.displayName=V;var q=r.forwardRef((e,s)=>{let{__scopeRadioGroup:t,...r}=e,i=P(t);return(0,a.jsx)(N,{...i,...r,ref:s})});q.displayName="RadioGroupIndicator";var F=t(65822),G=t(4780);let U=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(S,{className:(0,G.cn)("grid gap-2",e),...s,ref:t}));U.displayName=S.displayName;let $=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(M,{ref:t,className:(0,G.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:(0,a.jsx)(q,{className:"flex items-center justify-center",children:(0,a.jsx)(F.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));$.displayName=M.displayName},74075:e=>{"use strict";e.exports=require("zlib")},77747:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var a=t(65239),r=t(48088),i=t(88170),l=t.n(i),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let o={children:["",{children:["academic",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,87047)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\products\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,80460)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\products\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21843)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["F:\\trae\\cardmees\\fronend\\src\\app\\academic\\products\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/academic/products/page",pathname:"/academic/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79551:e=>{"use strict";e.exports=require("url")},80013:(e,s,t)=>{"use strict";t.d(s,{J:()=>o});var a=t(60687),r=t(43210),i=t(78148),l=t(24224),n=t(4780);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(i.b,{ref:t,className:(0,n.cn)(d(),e),...s}));o.displayName=i.b.displayName},80460:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\academic\\\\products\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\products\\loading.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87047:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var a=t(37413),r=t(35243);let i={title:`产品套餐管理 - 蜜卡`};function l(){return(0,a.jsx)(r.default,{})}},88233:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>l});var a=t(60687),r=t(43210),i=t(4780);let l=r.forwardRef(({className:e,type:s,...t},r)=>(0,a.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...t}));l.displayName="Input"},93500:(e,s,t)=>{"use strict";t.d(s,{$v:()=>f,EO:()=>u,Lt:()=>d,Rx:()=>g,Zr:()=>v,ck:()=>x,r7:()=>h,tv:()=>o,wd:()=>p});var a=t(60687),r=t(43210),i=t(97895),l=t(4780),n=t(29523);let d=i.bL,o=i.l9,c=i.ZL,m=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(i.hJ,{className:(0,l.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s,ref:t}));m.displayName=i.hJ.displayName;let u=r.forwardRef(({className:e,...s},t)=>(0,a.jsxs)(c,{children:[(0,a.jsx)(m,{}),(0,a.jsx)(i.UC,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s})]}));u.displayName=i.UC.displayName;let p=({className:e,...s})=>(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...s});p.displayName="AlertDialogHeader";let x=({className:e,...s})=>(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});x.displayName="AlertDialogFooter";let h=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(i.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold",e),...s}));h.displayName=i.hE.displayName;let f=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(i.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));f.displayName=i.VY.displayName;let g=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(i.rc,{ref:t,className:(0,l.cn)((0,n.r)(),e),...s}));g.displayName=i.rc.displayName;let v=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(i.ZD,{ref:t,className:(0,l.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...s}));v.displayName=i.ZD.displayName},94735:e=>{"use strict";e.exports=require("events")},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,s,t)=>{"use strict";t.d(s,{E:()=>n});var a=t(60687);t(43210);var r=t(24224),i=t(4780);let l=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:s,...t}){return(0,a.jsx)("div",{className:(0,i.cn)(l({variant:s}),e),...t})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,7392,5814,3928,3443,2951,1011,1991,7605,5343,7895,3019,9879,4733],()=>t(77747));module.exports=a})();