"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5087,7649],{15452:(e,t,r)=>{r.d(t,{G$:()=>z,Hs:()=>N,UC:()=>et,VY:()=>ea,ZL:()=>Q,bL:()=>K,bm:()=>en,hE:()=>er,hJ:()=>ee,l9:()=>X});var a=r(12115),n=r(85185),o=r(6101),l=r(46081),s=r(61285),i=r(5845),d=r(19178),c=r(25519),u=r(34378),f=r(28905),p=r(63655),m=r(92293),g=r(93795),x=r(38168),v=r(99708),h=r(95155),y="Dialog",[j,N]=(0,l.A)(y),[w,D]=j(y),b=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:o,onOpenChange:l,modal:d=!0}=e,c=a.useRef(null),u=a.useRef(null),[f=!1,p]=(0,i.i)({prop:n,defaultProp:o,onChange:l});return(0,h.jsx)(w,{scope:t,triggerRef:c,contentRef:u,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:f,onOpenChange:p,onOpenToggle:a.useCallback(()=>p(e=>!e),[p]),modal:d,children:r})};b.displayName=y;var R="DialogTrigger",C=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,l=D(R,r),s=(0,o.s)(t,l.triggerRef);return(0,h.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":Y(l.open),...a,ref:s,onClick:(0,n.m)(e.onClick,l.onOpenToggle)})});C.displayName=R;var A="DialogPortal",[E,O]=j(A,{forceMount:void 0}),k=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:o}=e,l=D(A,t);return(0,h.jsx)(E,{scope:t,forceMount:r,children:a.Children.map(n,e=>(0,h.jsx)(f.C,{present:r||l.open,children:(0,h.jsx)(u.Z,{asChild:!0,container:o,children:e})}))})};k.displayName=A;var I="DialogOverlay",F=a.forwardRef((e,t)=>{let r=O(I,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,o=D(I,e.__scopeDialog);return o.modal?(0,h.jsx)(f.C,{present:a||o.open,children:(0,h.jsx)(_,{...n,ref:t})}):null});F.displayName=I;var _=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=D(I,r);return(0,h.jsx)(g.A,{as:v.DX,allowPinchZoom:!0,shards:[n.contentRef],children:(0,h.jsx)(p.sG.div,{"data-state":Y(n.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),P="DialogContent",Z=a.forwardRef((e,t)=>{let r=O(P,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,o=D(P,e.__scopeDialog);return(0,h.jsx)(f.C,{present:a||o.open,children:o.modal?(0,h.jsx)(L,{...n,ref:t}):(0,h.jsx)(T,{...n,ref:t})})});Z.displayName=P;var L=a.forwardRef((e,t)=>{let r=D(P,e.__scopeDialog),l=a.useRef(null),s=(0,o.s)(t,r.contentRef,l);return a.useEffect(()=>{let e=l.current;if(e)return(0,x.Eq)(e)},[]),(0,h.jsx)(V,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=a.forwardRef((e,t)=>{let r=D(P,e.__scopeDialog),n=a.useRef(!1),o=a.useRef(!1);return(0,h.jsx)(V,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,l;null===(a=e.onCloseAutoFocus)||void 0===a||a.call(e,t),t.defaultPrevented||(n.current||null===(l=r.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),n.current=!1,o.current=!1},onInteractOutside:t=>{var a,l;null===(a=e.onInteractOutside)||void 0===a||a.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let s=t.target;(null===(l=r.triggerRef.current)||void 0===l?void 0:l.contains(s))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),V=a.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:l,onCloseAutoFocus:s,...i}=e,u=D(P,r),f=a.useRef(null),p=(0,o.s)(t,f);return(0,m.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:l,onUnmountAutoFocus:s,children:(0,h.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Y(u.open),...i,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(S,{titleId:u.titleId}),(0,h.jsx)(W,{contentRef:f,descriptionId:u.descriptionId})]})]})}),$="DialogTitle",M=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=D($,r);return(0,h.jsx)(p.sG.h2,{id:n.titleId,...a,ref:t})});M.displayName=$;var B="DialogDescription",G=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=D(B,r);return(0,h.jsx)(p.sG.p,{id:n.descriptionId,...a,ref:t})});G.displayName=B;var H="DialogClose",U=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,o=D(H,r);return(0,h.jsx)(p.sG.button,{type:"button",...a,ref:t,onClick:(0,n.m)(e.onClick,()=>o.onOpenChange(!1))})});function Y(e){return e?"open":"closed"}U.displayName=H;var q="DialogTitleWarning",[z,J]=(0,l.q)(q,{contentName:P,titleName:$,docsSlug:"dialog"}),S=e=>{let{titleId:t}=e,r=J(q),n="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return a.useEffect(()=>{t&&!document.getElementById(t)&&console.error(n)},[n,t]),null},W=e=>{let{contentRef:t,descriptionId:r}=e,n=J("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return a.useEffect(()=>{var e;let a=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&a&&!document.getElementById(r)&&console.warn(o)},[o,t,r]),null},K=b,X=C,Q=k,ee=F,et=Z,er=M,ea=G,en=U},17649:(e,t,r)=>{r.d(t,{UC:()=>P,VY:()=>V,ZD:()=>L,ZL:()=>F,bL:()=>k,hE:()=>T,hJ:()=>_,l9:()=>I,rc:()=>Z});var a=r(12115),n=r(46081),o=r(6101),l=r(15452),s=r(85185),i=r(99708),d=r(95155),c="AlertDialog",[u,f]=(0,n.A)(c,[l.Hs]),p=(0,l.Hs)(),m=e=>{let{__scopeAlertDialog:t,...r}=e,a=p(t);return(0,d.jsx)(l.bL,{...a,...r,modal:!0})};m.displayName=c;var g=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(l.l9,{...n,...a,ref:t})});g.displayName="AlertDialogTrigger";var x=e=>{let{__scopeAlertDialog:t,...r}=e,a=p(t);return(0,d.jsx)(l.ZL,{...a,...r})};x.displayName="AlertDialogPortal";var v=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(l.hJ,{...n,...a,ref:t})});v.displayName="AlertDialogOverlay";var h="AlertDialogContent",[y,j]=u(h),N=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...c}=e,u=p(r),f=a.useRef(null),m=(0,o.s)(t,f),g=a.useRef(null);return(0,d.jsx)(l.G$,{contentName:h,titleName:w,docsSlug:"alert-dialog",children:(0,d.jsx)(y,{scope:r,cancelRef:g,children:(0,d.jsxs)(l.UC,{role:"alertdialog",...u,...c,ref:m,onOpenAutoFocus:(0,s.m)(c.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=g.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(i.xV,{children:n}),(0,d.jsx)(O,{contentRef:f})]})})})});N.displayName=h;var w="AlertDialogTitle",D=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(l.hE,{...n,...a,ref:t})});D.displayName=w;var b="AlertDialogDescription",R=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(l.VY,{...n,...a,ref:t})});R.displayName=b;var C=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(l.bm,{...n,...a,ref:t})});C.displayName="AlertDialogAction";var A="AlertDialogCancel",E=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,{cancelRef:n}=j(A,r),s=p(r),i=(0,o.s)(t,n);return(0,d.jsx)(l.bm,{...s,...a,ref:i})});E.displayName=A;var O=e=>{let{contentRef:t}=e,r="`".concat(h,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(h,"` by passing a `").concat(b,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(h,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return a.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},k=m,I=g,F=x,_=v,P=N,Z=C,L=E,T=D,V=R},57001:(e,t,r)=>{r.d(t,{p:()=>l});var a=r(95155),n=r(30285),o=r(46102);function l(e){let{icon:t,tooltipText:r,tooltipSide:l="top",tooltipAlign:s="center",delayDuration:i=300,variant:d="ghost",size:c="icon",className:u="h-8 w-8 hover:bg-muted",...f}=e;return(0,a.jsx)(o.Bc,{delayDuration:i,children:(0,a.jsxs)(o.m_,{children:[(0,a.jsx)(o.k$,{asChild:!0,children:(0,a.jsx)(n.$,{variant:d,size:c,className:u,...f,children:(0,a.jsx)(t,{className:"h-4 w-4 text-muted-foreground"})})}),(0,a.jsx)(o.ZI,{side:l,align:s,className:"font-medium text-xs px-3 py-1.5",children:(0,a.jsx)("p",{children:r})})]})})}},62525:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},65087:(e,t,r)=>{r.r(t),r.d(t,{default:()=>u});var a=r(95155),n=r(57001),o=r(90010),l=r(62525),s=r(12115),i=r(30285),d=r(48436),c=r(13515);let u=function(e){let{role:t}=e,[r]=(0,c.A$)(),[u,f]=(0,s.useState)(!1);async function p(){if(t){try{await r(t.id).unwrap(),d.l.success("删除成功")}catch(e){d.l.error((null==e?void 0:e.message)||"删除失败")}f(!1)}}return(0,a.jsxs)("div",{children:[(0,a.jsx)(n.p,{icon:l.A,tooltipText:"删除岗位",onClick:()=>f(!0)}),(0,a.jsx)(o.Lt,{open:u,onOpenChange:f,children:(0,a.jsxs)(o.EO,{children:[(0,a.jsxs)(o.wd,{children:[(0,a.jsx)(o.r7,{children:"确认删除岗位"}),(0,a.jsxs)(o.$v,{children:["您确定要删除岗位 ",null==t?void 0:t.name," 吗？此操作不可撤销。"]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,a.jsx)(i.$,{variant:"outline",onClick:()=>f(!1),children:"取消"}),(0,a.jsx)(i.$,{variant:"destructive",onClick:p,children:"确认删除"})]})]})})]})}},90010:(e,t,r)=>{r.d(t,{$v:()=>x,EO:()=>f,Lt:()=>i,Rx:()=>v,Zr:()=>h,ck:()=>m,r7:()=>g,tv:()=>d,wd:()=>p});var a=r(95155),n=r(12115),o=r(17649),l=r(59434),s=r(30285);let i=o.bL,d=o.l9,c=o.ZL,u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.hJ,{className:(0,l.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...n,ref:t})});u.displayName=o.hJ.displayName;let f=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(u,{}),(0,a.jsx)(o.UC,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...n})]})});f.displayName=o.UC.displayName;let p=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-2 text-center sm:text-left",t),...r})};p.displayName="AlertDialogHeader";let m=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...r})};m.displayName="AlertDialogFooter";let g=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold",r),...n})});g.displayName=o.hE.displayName;let x=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...n})});x.displayName=o.VY.displayName;let v=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.rc,{ref:t,className:(0,l.cn)((0,s.r)(),r),...n})});v.displayName=o.rc.displayName;let h=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.ZD,{ref:t,className:(0,l.cn)((0,s.r)({variant:"outline"}),"mt-2 sm:mt-0",r),...n})});h.displayName=o.ZD.displayName}}]);