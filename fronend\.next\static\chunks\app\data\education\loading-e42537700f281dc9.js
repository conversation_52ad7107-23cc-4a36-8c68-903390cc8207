(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9013],{51323:(s,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>d});var l=a(95155),r=a(68856);function d(){return(0,l.jsxs)("div",{className:"p-4 space-y-6",children:[(0,l.jsx)("div",{className:"flex space-x-4 mb-4",children:[,,,].fill(0).map((s,e)=>(0,l.jsx)(r.E,{className:"h-10 w-28"},e))}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[,,,].fill(0).map((s,e)=>(0,l.jsxs)("div",{className:"border p-4 rounded-md",children:[(0,l.jsx)(r.E,{className:"h-6 w-24 mb-2"}),(0,l.jsx)(r.E,{className:"h-10 w-full"}),(0,l.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,l.jsx)(r.E,{className:"h-4 w-3/4"}),(0,l.jsx)(r.E,{className:"h-4 w-1/2"})]})]},e))}),(0,l.jsxs)("div",{className:"border p-4 rounded-md",children:[(0,l.jsx)(r.E,{className:"h-6 w-32 mb-4"}),(0,l.jsx)(r.E,{className:"h-64 w-full"})]}),(0,l.jsxs)("div",{className:"border rounded-md",children:[(0,l.jsx)("div",{className:"flex border-b p-3 bg-muted/30",children:[,,,,,].fill(0).map((s,e)=>(0,l.jsx)(r.E,{className:"h-6 flex-1 mx-2"},e))}),Array(6).fill(0).map((s,e)=>(0,l.jsx)("div",{className:"flex border-b p-3",children:[,,,,,].fill(0).map((s,e)=>(0,l.jsx)(r.E,{className:"h-6 flex-1 mx-2"},e))},e))]})]})}},52711:(s,e,a)=>{Promise.resolve().then(a.bind(a,51323))},59434:(s,e,a)=>{"use strict";a.d(e,{cn:()=>d});var l=a(52596),r=a(39688);function d(){for(var s=arguments.length,e=Array(s),a=0;a<s;a++)e[a]=arguments[a];return(0,r.QP)((0,l.$)(e))}},68856:(s,e,a)=>{"use strict";a.d(e,{E:()=>d});var l=a(95155),r=a(59434);function d(s){let{className:e,...a}=s;return(0,l.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",e),...a})}}},s=>{var e=e=>s(s.s=e);s.O(0,[4277,6315,7358],()=>e(52711)),_N_E=s.O()}]);