"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2904],{54165:(e,l,a)=>{a.d(l,{Cf:()=>g,Es:()=>u,HM:()=>i,L3:()=>h,c7:()=>m,lG:()=>n,rr:()=>p,zM:()=>b});var t=a(95155),r=a(12115),s=a(15452),o=a(54416),d=a(59434);let n=s.bL,b=s.l9,c=s.ZL,i=s.bm,x=r.forwardRef((e,l)=>{let{className:a,...r}=e;return(0,t.jsx)(s.hJ,{ref:l,className:(0,d.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r})});x.displayName=s.hJ.displayName;let g=r.forwardRef((e,l)=>{let{className:a,children:r,...n}=e;return(0,t.jsxs)(c,{children:[(0,t.jsx)(x,{}),(0,t.jsxs)(s.UC,{ref:l,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...n,children:[r,(0,t.jsxs)(s.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});g.displayName=s.UC.displayName;let m=e=>{let{className:l,...a}=e;return(0,t.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left",l),...a})};m.displayName="DialogHeader";let u=e=>{let{className:l,...a}=e;return(0,t.jsx)("div",{className:(0,d.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",l),...a})};u.displayName="DialogFooter";let h=r.forwardRef((e,l)=>{let{className:a,...r}=e;return(0,t.jsx)(s.hE,{ref:l,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});h.displayName=s.hE.displayName;let p=r.forwardRef((e,l)=>{let{className:a,...r}=e;return(0,t.jsx)(s.VY,{ref:l,className:(0,d.cn)("text-sm text-muted-foreground",a),...r})});p.displayName=s.VY.displayName},57141:(e,l,a)=>{a.d(l,{C9:()=>r,DT:()=>d,I2:()=>o,IC:()=>x,N4:()=>c,fb:()=>g,lc:()=>t,oD:()=>n,u7:()=>i,uq:()=>s,x9:()=>b});let t={"limited-sessions":{label:"限次",color:"capitalize font-normal bg-blue-50 text-blue-700 hover:bg-blue-100 px-2.5 py-0.5 rounded-md"},"limited-time-and-count":{label:"时次限",color:"capitalize font-normal bg-violet-50 text-violet-700 hover:bg-violet-100 px-2.5 py-0.5 rounded-md"},default:{label:"未知套餐",color:"capitalize font-normal bg-slate-100 text-slate-700 hover:bg-slate-200 px-2.5 py-0.5 rounded-md"}},r={cash:{label:"现金",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},alipay:{label:"支付宝",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},wechat:{label:"微信",color:"bg-green-100 text-green-800 hover:bg-green-200"},card:{label:"银行卡",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},other:{label:"其他",color:"bg-slate-100 text-slate-800 hover:bg-slate-200"}},s=[{id:"wechat",label:"微信"},{id:"alipay",label:"支付宝"},{id:"card",label:"银行转账"},{id:"cash",label:"现金"},{id:"other",label:"其他"}],o={done:{label:"完成",color:"bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400"},arrears:{label:"欠费",color:"bg-amber-100 text-amber-800 hover:bg-amber-200 dark:bg-amber-900/30 dark:text-amber-400"},refunded:{label:"退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400"},default:{label:"未知",color:"bg-slate-100 text-slate-800 hover:bg-slate-200 dark:bg-slate-800/50 dark:text-slate-400"}},d={active:{badgeClass:"bg-emerald-50 text-emerald-700 hover:bg-emerald-100 border-emerald-200",dotClass:"bg-emerald-500",text:"正常"},expired:{badgeClass:"bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200",dotClass:"bg-amber-500",text:"已到期"},refunded:{badgeClass:"bg-red-50 text-red-700 hover:bg-red-100 border-red-200",dotClass:"bg-red-500",text:"已退款"},frozen:{badgeClass:"bg-slate-50 text-slate-700 hover:bg-slate-100 border-slate-200",dotClass:"bg-slate-500",text:"已冻结"},default:{badgeClass:"bg-slate-100 text-slate-700 hover:bg-slate-200 border-slate-200",dotClass:"bg-slate-400",text:"未知状态"}},n={all:{className:"bg-slate-100 text-slate-800 hover:bg-slate-200",text:"全部状态"},attendance:{className:"bg-green-100 text-green-800 hover:bg-green-200",text:"已考勤"},leave:{className:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200",text:"请假"},unattended:{className:"bg-orange-100 text-orange-800 hover:bg-orange-200",text:"未考勤"},default:{className:"bg-red-100 text-red-800 hover:bg-red-200",text:"缺勤"}},b={A:{label:"非常感兴趣",color:"bg-green-100 text-green-800 hover:bg-green-200"},B:{label:"比较感兴趣",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},C:{label:"一般意向",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},D:{label:"了解意向",color:"bg-orange-100 text-orange-800 hover:bg-orange-200"},E:{label:"不感兴趣",color:"bg-red-100 text-red-800 hover:bg-red-200"},default:{label:"未知",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},c={productSales:{label:"产品销售",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},courseSales:{label:"课程销售",color:"bg-green-100 text-green-800 hover:bg-green-200"},OverduePaymentCollection:{label:"逾期款项催收",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},lifestyleAndOffice:{label:"生活和办公",color:"bg-red-100 text-red-800 hover:bg-red-200"},socialAndCatering:{label:"社交和餐饮",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},refunded:{label:"退款",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"其他",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},i={completed:{label:"已完成",color:"bg-green-100 text-green-800 hover:bg-green-200"},pending:{label:"待审核",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},refunded:{label:"已退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},failed:{label:"审核未通过",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"未知状态",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},x={formal:{label:"正式学员",color:"text-blue-500"},graduated:{label:"历史学员",color:"text-green-500"},public:{label:"公海池",color:"text-red-500"},intent:{label:"意向学员",color:"text-yellow-500"}},g={secret:{label:"保密",color:"text-gray-500"},male:{label:"男",color:"text-blue-500"},female:{label:"女",color:"text-pink-500"}}},82904:(e,l,a)=>{a.r(l),a.d(l,{default:()=>k});var t=a(95155),r=a(12115),s=a(54165),o=a(56287),d=a(55733),n=a(55028),b=a(73168),c=a(48436),i=a(91394),x=a(65436),g=a(95728),m=a(57141),u=a(57001);let h=(0,n.default)(()=>a.e(6087).then(a.bind(a,56087)),{loadableGenerated:{webpack:()=>[56087]},ssr:!1}),p=(0,n.default)(()=>Promise.all([a.e(81),a.e(125)]).then(a.bind(a,125)),{loadableGenerated:{webpack:()=>[125]},ssr:!1}),f=(0,n.default)(()=>a.e(9694).then(a.bind(a,79694)),{loadableGenerated:{webpack:()=>[79694]},ssr:!1}),y=(0,n.default)(()=>a.e(4511).then(a.bind(a,14511)),{loadableGenerated:{webpack:()=>[14511]},ssr:!1}),v=(0,n.default)(()=>a.e(4167).then(a.bind(a,74167)),{loadableGenerated:{webpack:()=>[74167]},ssr:!1}),N=(0,n.default)(()=>Promise.all([a.e(2389),a.e(347)]).then(a.bind(a,347)),{loadableGenerated:{webpack:()=>[347]},ssr:!1}),w=(0,n.default)(()=>Promise.all([a.e(81),a.e(5589),a.e(5469)]).then(a.bind(a,15469)),{loadableGenerated:{webpack:()=>[15469]},ssr:!1}),j=[{id:"packages",label:"已购套餐",content:(0,t.jsx)(h,{})},{id:"purchaseRecord",label:"购买记录",content:(0,t.jsx)(v,{})},{id:"query",label:"上课查询",content:(0,t.jsx)(p,{})},{id:"attendanceRecord",label:"考勤记录",content:(0,t.jsx)(y,{})},{id:"myClass",label:"我的班级",content:(0,t.jsx)(N,{})},{id:"followUpRecords",label:"跟进记录",content:(0,t.jsx)(f,{})}],k=function(e){var l,a;let{open:n,onOpenChange:h}=e,{studentId:p}=(0,x.G)(e=>e.currentStudent),{data:f,isLoading:y,isError:v}=(0,g.Nq)(p),[N,{isLoading:k,isError:C}]=(0,g.zy)(),[G,z]=(0,r.useState)(!1),[R,S]=(0,r.useState)({id:"",name:"",birthday:"",phone:"",address:"",remarks:"",gender:"",type:"",cardNumber:"",avatar:""});(0,r.useEffect)(()=>{y||v||!f||S(f)},[y,v,f]);let E=async e=>{if(e.birthday&&(e.birthday=new Date(e.birthday).getTime()),JSON.stringify(e)===JSON.stringify(R)){c.l.info("学员信息未发生变化，无需提交");return}delete e.createdAt;let{data:l}=await N({studentId:p,data:e});if(C){c.l.error("学员更新错误",(null==l?void 0:l.error.message)||"更新学员信息失败!");return}c.l.success("学员更新成功",(null==l?void 0:l.message)||"更新学员信息成功."),z(!1)};return!n&&y?null:(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(s.lG,{open:n,onOpenChange:h,children:(0,t.jsxs)(s.Cf,{className:"max-w-5xl max-h-[90vh] p-0 gap-0 overflow-hidden rounded-xl flex flex-col shadow-lg border-0",children:[(0,t.jsx)(s.c7,{className:"flex flex-row justify-between items-center py-2 px-4 border-b border-gray-100 bg-white shrink-0",children:(0,t.jsx)(s.L3,{className:"text-base font-medium text-gray-700",children:"学员信息"})}),(0,t.jsxs)("div",{className:"overflow-y-auto flex-grow bg-gray-50",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-[220px_1fr]",children:[(0,t.jsxs)("div",{className:"py-6 px-4 flex flex-col items-center justify-start bg-white",children:[(0,t.jsxs)(i.eu,{className:"w-28 h-28 rounded-full overflow-hidden mb-4 ring-2 ring-blue-50 shadow-sm",children:[(0,t.jsx)(i.BK,{src:R.avatar||"",alt:"学员头像"}),(0,t.jsx)(i.q5,{children:(null===(a=R.name)||void 0===a?void 0:null===(l=a[0])||void 0===l?void 0:l.toUpperCase())||"US"})]}),(0,t.jsxs)("div",{className:"flex items-center mt-1",children:[(0,t.jsx)("span",{className:"font-medium text-gray-800 text-lg",children:null==R?void 0:R.name}),(0,t.jsx)("span",{className:"ml-2 px-2 py-0.5 bg-blue-50 text-blue-600 text-xs rounded-full",children:function(e){let l=m.IC[e]||{text:"未知",color:"text-gray-500"};return(0,t.jsx)("span",{className:l.color,children:l.label})}((null==R?void 0:R.type)||"formal")})]})]}),(0,t.jsxs)("div",{className:"bg-white p-4 relative",children:[(0,t.jsx)(u.p,{className:"absolute right-4 top-4",icon:o.A,tooltipText:"编辑信息",onClick:()=>z(!0)}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 py-2",children:[(0,t.jsx)("span",{className:"text-gray-500 w-20",children:"学员生日"}),(0,t.jsx)("span",{className:"text-gray-800",children:R.birthday?(0,b.GP)(new Date(R.birthday),"yyyy-MM-dd"):"-"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 py-2",children:[(0,t.jsx)("span",{className:"text-gray-500 w-20",children:"学员性别"}),(0,t.jsx)("span",{className:"text-gray-800",children:function(e){let l=m.fb[e];return l?(0,t.jsx)("span",{className:l.color,children:l.label}):null}((null==R?void 0:R.gender)||"secret")})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 py-2",children:[(0,t.jsx)("span",{className:"text-gray-500 w-20",children:"联系电话"}),(0,t.jsx)("span",{className:"text-gray-800",children:(null==R?void 0:R.phone)||"-"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 py-2",children:[(0,t.jsx)("span",{className:"text-gray-500 w-20",children:"学员卡号"}),(0,t.jsx)("span",{className:"text-gray-800",children:(null==R?void 0:R.cardNumber)||"-"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 py-2",children:[(0,t.jsx)("span",{className:"text-gray-500 w-20",children:"学员地址"}),(0,t.jsx)("span",{className:"text-gray-800",children:(null==R?void 0:R.address)||"-"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 py-2",children:[(0,t.jsx)("span",{className:"text-gray-500 w-20",children:"备注"}),(0,t.jsx)("span",{className:"text-gray-400 italic",children:(null==R?void 0:R.remarks)||"无"})]})]})]})]}),(0,t.jsx)("div",{className:"bg-white mt-3 rounded-t-xl shadow-sm p-4",children:(0,t.jsx)(d.Q,{tabs:j})})]})]})}),G&&(0,t.jsx)(w,{open:G,onOpenChange:z,student:R,onSave:E})]})}},91394:(e,l,a)=>{a.d(l,{BK:()=>n,eu:()=>d,q5:()=>b});var t=a(95155),r=a(12115),s=a(85977),o=a(59434);let d=r.forwardRef((e,l)=>{let{className:a,...r}=e;return(0,t.jsx)(s.bL,{ref:l,className:(0,o.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),...r})});d.displayName=s.bL.displayName;let n=r.forwardRef((e,l)=>{let{className:a,...r}=e;return(0,t.jsx)(s._V,{ref:l,className:(0,o.cn)("aspect-square h-full w-full",a),...r})});n.displayName=s._V.displayName;let b=r.forwardRef((e,l)=>{let{className:a,...r}=e;return(0,t.jsx)(s.H4,{ref:l,className:(0,o.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),...r})});b.displayName=s.H4.displayName}}]);