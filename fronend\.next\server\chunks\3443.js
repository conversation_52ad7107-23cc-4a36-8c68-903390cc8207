"use strict";exports.id=3443,exports.ids=[3443],exports.modules={43:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(43210);n(60687);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},1359:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(43210),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},3589:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},9510:(e,t,n)=>{n.d(t,{N:()=>u});var r=n(43210),o=n(11273),i=n(98599),a=n(8730),l=n(60687);function u(e){let t=e+"CollectionProvider",[n,u]=(0,o.A)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,l.jsx)(c,{scope:t,itemMap:i,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(f,n),u=(0,i.s)(t,o.collectionRef);return(0,l.jsx)(a.DX,{ref:u,children:r})});p.displayName=f;let m=e+"CollectionItemSlot",h="data-radix-collection-item",v=r.forwardRef((e,t)=>{let{scope:n,children:o,...u}=e,c=r.useRef(null),d=(0,i.s)(t,c),f=s(m,n);return r.useEffect(()=>(f.itemMap.set(c,{ref:c,...u}),()=>void f.itemMap.delete(c))),(0,l.jsx)(a.DX,{[h]:"",ref:d,children:o})});return v.displayName=m,[{Provider:d,Slot:p,ItemSlot:v},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${h}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}},11273:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>i});var r=n(43210),o=n(60687);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[l]||a,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[l]||a,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},13495:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(43210);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},13964:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14163:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>l});var r=n(43210),o=n(51215),i=n(8730),a=n(60687),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...o}=e,l=r?i.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(l,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},14952:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},18853:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(43210),o=n(66156);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},25028:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(43210),o=n(51215),i=n(14163),a=n(66156),l=n(60687),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[c,s]=r.useState(!1);(0,a.N)(()=>s(!0),[]);let d=n||c&&globalThis?.document?.body;return d?o.createPortal((0,l.jsx)(i.sG.div,{...u,ref:t}),d):null});u.displayName="Portal"},26312:(e,t,n)=>{n.d(t,{H_:()=>e4,UC:()=>e2,YJ:()=>e6,q7:()=>e3,VF:()=>e7,JU:()=>e5,ZL:()=>e1,z6:()=>e9,hN:()=>e8,bL:()=>eQ,wv:()=>te,Pb:()=>tt,G5:()=>tr,ZP:()=>tn,l9:()=>e0});var r=n(43210),o=n(70569),i=n(98599),a=n(11273),l=n(65551),u=n(14163),c=n(9510),s=n(43),d=n(31355),f=n(1359),p=n(32547),m=n(96963),h=n(55509),v=n(25028),g=n(46059),y=n(72942),w=n(8730),b=n(13495),x=n(63376),E=n(42247),C=n(60687),R=["Enter"," "],A=["ArrowUp","PageDown","End"],M=["ArrowDown","PageUp","Home",...A],S={ltr:[...R,"ArrowRight"],rtl:[...R,"ArrowLeft"]},L={ltr:["ArrowLeft"],rtl:["ArrowRight"]},N="Menu",[P,k,T]=(0,c.N)(N),[D,O]=(0,a.A)(N,[T,h.Bk,y.RG]),j=(0,h.Bk)(),I=(0,y.RG)(),[F,_]=D(N),[W,B]=D(N),K=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:a,modal:l=!0}=e,u=j(t),[c,d]=r.useState(null),f=r.useRef(!1),p=(0,b.c)(a),m=(0,s.jH)(i);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,C.jsx)(h.bL,{...u,children:(0,C.jsx)(F,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:d,children:(0,C.jsx)(W,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:l,children:o})})})};K.displayName=N;var G=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=j(n);return(0,C.jsx)(h.Mz,{...o,...r,ref:t})});G.displayName="MenuAnchor";var H="MenuPortal",[U,$]=D(H,{forceMount:void 0}),z=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=_(H,t);return(0,C.jsx)(U,{scope:t,forceMount:n,children:(0,C.jsx)(g.C,{present:n||i.open,children:(0,C.jsx)(v.Z,{asChild:!0,container:o,children:r})})})};z.displayName=H;var X="MenuContent",[V,q]=D(X),Y=r.forwardRef((e,t)=>{let n=$(X,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=_(X,e.__scopeMenu),a=B(X,e.__scopeMenu);return(0,C.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(g.C,{present:r||i.open,children:(0,C.jsx)(P.Slot,{scope:e.__scopeMenu,children:a.modal?(0,C.jsx)(Z,{...o,ref:t}):(0,C.jsx)(J,{...o,ref:t})})})})}),Z=r.forwardRef((e,t)=>{let n=_(X,e.__scopeMenu),a=r.useRef(null),l=(0,i.s)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,x.Eq)(e)},[]),(0,C.jsx)(Q,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),J=r.forwardRef((e,t)=>{let n=_(X,e.__scopeMenu);return(0,C.jsx)(Q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Q=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:m,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:b,onInteractOutside:x,onDismiss:R,disableOutsideScroll:S,...L}=e,N=_(X,n),P=B(X,n),T=j(n),D=I(n),O=k(n),[F,W]=r.useState(null),K=r.useRef(null),G=(0,i.s)(t,K,N.onContentChange),H=r.useRef(0),U=r.useRef(""),$=r.useRef(0),z=r.useRef(null),q=r.useRef("right"),Y=r.useRef(0),Z=S?E.A:r.Fragment,J=S?{as:w.DX,allowPinchZoom:!0}:void 0,Q=e=>{let t=U.current+e,n=O().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let a=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}(n.map(e=>e.textValue),t,o),a=n.find(e=>e.textValue===i)?.ref.current;(function e(t){U.current=t,window.clearTimeout(H.current),""!==t&&(H.current=window.setTimeout(()=>e(""),1e3))})(t),a&&setTimeout(()=>a.focus())};r.useEffect(()=>()=>window.clearTimeout(H.current),[]),(0,f.Oh)();let ee=r.useCallback(e=>q.current===z.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e].x,l=t[e].y,u=t[i].x,c=t[i].y;l>r!=c>r&&n<(u-a)*(r-l)/(c-l)+a&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,z.current?.area),[]);return(0,C.jsx)(V,{scope:n,searchRef:U,onItemEnter:r.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:r.useCallback(e=>{ee(e)||(K.current?.focus(),W(null))},[ee]),onTriggerLeave:r.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:$,onPointerGraceIntentChange:r.useCallback(e=>{z.current=e},[]),children:(0,C.jsx)(Z,{...J,children:(0,C.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{e.preventDefault(),K.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,C.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:b,onInteractOutside:x,onDismiss:R,children:(0,C.jsx)(y.bL,{asChild:!0,...D,dir:P.dir,orientation:"vertical",loop:a,currentTabStopId:F,onCurrentTabStopIdChange:W,onEntryFocus:(0,o.m)(m,e=>{P.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,C.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":eS(N.open),"data-radix-menu-content":"",dir:P.dir,...T,...L,ref:G,style:{outline:"none",...L.style},onKeyDown:(0,o.m)(L.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&Q(e.key));let o=K.current;if(e.target!==o||!M.includes(e.key))return;e.preventDefault();let i=O().filter(e=>!e.disabled).map(e=>e.ref.current);A.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(H.current),U.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eP(e=>{let t=e.target,n=Y.current!==e.clientX;e.currentTarget.contains(t)&&n&&(q.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});Y.displayName=X;var ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(u.sG.div,{role:"group",...r,ref:t})});ee.displayName="MenuGroup";var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(u.sG.div,{...r,ref:t})});et.displayName="MenuLabel";var en="MenuItem",er="menu.itemSelect",eo=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...l}=e,c=r.useRef(null),s=B(en,e.__scopeMenu),d=q(en,e.__scopeMenu),f=(0,i.s)(t,c),p=r.useRef(!1);return(0,C.jsx)(ei,{...l,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(er,{bubbles:!0,cancelable:!0});e.addEventListener(er,e=>a?.(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!n&&(!t||" "!==e.key)&&R.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eo.displayName=en;var ei=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:l,...c}=e,s=q(en,n),d=I(n),f=r.useRef(null),p=(0,i.s)(t,f),[m,h]=r.useState(!1),[v,g]=r.useState("");return r.useEffect(()=>{let e=f.current;e&&g((e.textContent??"").trim())},[c.children]),(0,C.jsx)(P.ItemSlot,{scope:n,disabled:a,textValue:l??v,children:(0,C.jsx)(y.q7,{asChild:!0,...d,focusable:!a,children:(0,C.jsx)(u.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...c,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eP(e=>{a?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eP(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),ea=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,C.jsx)(em,{scope:e.__scopeMenu,checked:n,children:(0,C.jsx)(eo,{role:"menuitemcheckbox","aria-checked":eL(n)?"mixed":n,...i,ref:t,"data-state":eN(n),onSelect:(0,o.m)(i.onSelect,()=>r?.(!!eL(n)||!n),{checkForDefaultPrevented:!1})})})});ea.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[eu,ec]=D(el,{value:void 0,onValueChange:()=>{}}),es=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,b.c)(r);return(0,C.jsx)(eu,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,C.jsx)(ee,{...o,ref:t})})});es.displayName=el;var ed="MenuRadioItem",ef=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=ec(ed,e.__scopeMenu),a=n===i.value;return(0,C.jsx)(em,{scope:e.__scopeMenu,checked:a,children:(0,C.jsx)(eo,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":eN(a),onSelect:(0,o.m)(r.onSelect,()=>i.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ef.displayName=ed;var ep="MenuItemIndicator",[em,eh]=D(ep,{checked:!1}),ev=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=eh(ep,n);return(0,C.jsx)(g.C,{present:r||eL(i.checked)||!0===i.checked,children:(0,C.jsx)(u.sG.span,{...o,ref:t,"data-state":eN(i.checked)})})});ev.displayName=ep;var eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eg.displayName="MenuSeparator";var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=j(n);return(0,C.jsx)(h.i3,{...o,...r,ref:t})});ey.displayName="MenuArrow";var ew="MenuSub",[eb,ex]=D(ew),eE=e=>{let{__scopeMenu:t,children:n,open:o=!1,onOpenChange:i}=e,a=_(ew,t),l=j(t),[u,c]=r.useState(null),[s,d]=r.useState(null),f=(0,b.c)(i);return r.useEffect(()=>(!1===a.open&&f(!1),()=>f(!1)),[a.open,f]),(0,C.jsx)(h.bL,{...l,children:(0,C.jsx)(F,{scope:t,open:o,onOpenChange:f,content:s,onContentChange:d,children:(0,C.jsx)(eb,{scope:t,contentId:(0,m.B)(),triggerId:(0,m.B)(),trigger:u,onTriggerChange:c,children:n})})})};eE.displayName=ew;var eC="MenuSubTrigger",eR=r.forwardRef((e,t)=>{let n=_(eC,e.__scopeMenu),a=B(eC,e.__scopeMenu),l=ex(eC,e.__scopeMenu),u=q(eC,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,C.jsx)(G,{asChild:!0,...f,children:(0,C.jsx)(ei,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eS(n.open),...e,ref:(0,i.t)(t,l.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eP(t=>{u.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eP(e=>{p();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,i=t[o?"left":"right"],a=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:i,y:t.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;!e.disabled&&(!r||" "!==t.key)&&S[a.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});eR.displayName=eC;var eA="MenuSubContent",eM=r.forwardRef((e,t)=>{let n=$(X,e.__scopeMenu),{forceMount:a=n.forceMount,...l}=e,u=_(X,e.__scopeMenu),c=B(X,e.__scopeMenu),s=ex(eA,e.__scopeMenu),d=r.useRef(null),f=(0,i.s)(t,d);return(0,C.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(g.C,{present:a||u.open,children:(0,C.jsx)(P.Slot,{scope:e.__scopeMenu,children:(0,C.jsx)(Q,{id:s.contentId,"aria-labelledby":s.triggerId,...l,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=L[c.dir].includes(e.key);t&&n&&(u.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function eS(e){return e?"open":"closed"}function eL(e){return"indeterminate"===e}function eN(e){return eL(e)?"indeterminate":e?"checked":"unchecked"}function eP(e){return t=>"mouse"===t.pointerType?e(t):void 0}eM.displayName=eA;var ek="DropdownMenu",[eT,eD]=(0,a.A)(ek,[O]),eO=O(),[ej,eI]=eT(ek),eF=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:a,onOpenChange:u,modal:c=!0}=e,s=eO(t),d=r.useRef(null),[f=!1,p]=(0,l.i)({prop:i,defaultProp:a,onChange:u});return(0,C.jsx)(ej,{scope:t,triggerId:(0,m.B)(),triggerRef:d,contentId:(0,m.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,C.jsx)(K,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:n})})};eF.displayName=ek;var e_="DropdownMenuTrigger",eW=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,l=eI(e_,n),c=eO(n);return(0,C.jsx)(G,{asChild:!0,...c,children:(0,C.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,i.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eW.displayName=e_;var eB=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eO(t);return(0,C.jsx)(z,{...r,...n})};eB.displayName="DropdownMenuPortal";var eK="DropdownMenuContent",eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eI(eK,n),l=eO(n),u=r.useRef(!1);return(0,C.jsx)(Y,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{u.current||a.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eG.displayName=eK;var eH=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ee,{...o,...r,ref:t})});eH.displayName="DropdownMenuGroup";var eU=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(et,{...o,...r,ref:t})});eU.displayName="DropdownMenuLabel";var e$=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(eo,{...o,...r,ref:t})});e$.displayName="DropdownMenuItem";var ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ea,{...o,...r,ref:t})});ez.displayName="DropdownMenuCheckboxItem";var eX=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(es,{...o,...r,ref:t})});eX.displayName="DropdownMenuRadioGroup";var eV=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ef,{...o,...r,ref:t})});eV.displayName="DropdownMenuRadioItem";var eq=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ev,{...o,...r,ref:t})});eq.displayName="DropdownMenuItemIndicator";var eY=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(eg,{...o,...r,ref:t})});eY.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ey,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var eZ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(eR,{...o,...r,ref:t})});eZ.displayName="DropdownMenuSubTrigger";var eJ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(eM,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eJ.displayName="DropdownMenuSubContent";var eQ=eF,e0=eW,e1=eB,e2=eG,e6=eH,e5=eU,e3=e$,e4=ez,e9=eX,e8=eV,e7=eq,te=eY,tt=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:i}=e,a=eO(t),[u=!1,c]=(0,l.i)({prop:r,defaultProp:i,onChange:o});return(0,C.jsx)(eE,{...a,open:u,onOpenChange:c,children:n})},tn=eZ,tr=eJ},31355:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(43210),i=n(70569),a=n(14163),l=n(98599),u=n(13495),c=n(60687),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...w}=e,b=o.useContext(d),[x,E]=o.useState(null),C=x?.ownerDocument??globalThis?.document,[,R]=o.useState({}),A=(0,l.s)(t,e=>E(e)),M=Array.from(b.layers),[S]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),L=M.indexOf(S),N=x?M.indexOf(x):-1,P=b.layersWithOutsidePointerEventsDisabled.size>0,k=N>=L,T=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){m("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));!k||n||(h?.(e),g?.(e),e.defaultPrevented||y?.())},C),D=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&m("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...b.branches].some(e=>e.contains(t))||(v?.(e),g?.(e),e.defaultPrevented||y?.())},C);return function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===b.layers.size-1&&(f?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},C),o.useEffect(()=>{if(x)return n&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(r=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(x)),b.layers.add(x),p(),()=>{n&&1===b.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=r)}},[x,C,n,b]),o.useEffect(()=>()=>{x&&(b.layers.delete(x),b.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,b]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(a.sG.div,{...w,ref:A,style:{pointerEvents:P?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,T.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function m(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,a.hO)(o,i):o.dispatchEvent(i)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},32547:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(43210),o=n(98599),i=n(14163),a=n(13495),l=n(60687),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,a.c)(v),E=(0,a.c)(g),C=r.useRef(null),R=(0,o.s)(t,e=>b(e)),A=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(A.paused||!w)return;let t=e.target;w.contains(t)?C.current=t:m(C.current,{select:!0})},t=function(e){if(A.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||m(C.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,A.paused]),r.useEffect(()=>{if(w){h.add(A);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||m(e??document.body,{select:!0}),w.removeEventListener(c,E),h.remove(A)},0)}}},[w,x,E,A]);let M=r.useCallback(e=>{if(!n&&!d||A.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&m(i,{select:!0})):(e.preventDefault(),n&&m(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,A.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:R,onKeyDown:M})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},42247:(e,t,n)=>{n.d(t,{A:()=>$});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(43210)),l="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i,a=(t=null,void 0===n&&(n=f),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(o)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=o({async:!0,ssr:!1},e),a}(),m=function(){},h=a.forwardRef(function(e,t){var n,r,l,u,f=a.useRef(null),h=a.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=h[0],g=h[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,C=e.shards,R=e.sideCar,A=e.noIsolation,M=e.inert,S=e.allowPinchZoom,L=e.as,N=e.gapMode,P=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=(n=[f,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(l=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return l.value},set current(value){var e=l.value;e!==value&&(l.value=value,l.callback(value,e))}}}})[0]).callback=r,u=l.facade,s(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}d.set(u,n)},[n]),u),T=o(o({},P),v);return a.createElement(a.Fragment,null,E&&a.createElement(R,{sideCar:p,removeScrollBar:x,shards:C,noIsolation:A,inert:M,setCallbacks:g,allowPinchZoom:!!S,lockRef:f,gapMode:N}),y?a.cloneElement(a.Children.only(w),o(o({},T),{ref:k})):a.createElement(void 0===L?"div":L,o({},T,{className:b,ref:k}),w))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:u,zeroRight:l};var v=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,o({},n))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(n),x(r),x(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},R=w(),A="data-scroll-locked",M=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},S=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},L=function(){a.useEffect(function(){return document.body.setAttribute(A,(S()+1).toString()),function(){var e=S()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},N=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;L();var i=a.useMemo(function(){return C(o)},[o]);return a.createElement(R,{styles:M(i,!t,o,n?"":"!important")})},P=!1;if("undefined"!=typeof window)try{var k=Object.defineProperty({},"passive",{get:function(){return P=!0,!0}});window.addEventListener("test",k,k),window.removeEventListener("test",k,k)}catch(e){P=!1}var T=!!P&&{passive:!1},D=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},O=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),j(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},j=function(e,t){return"v"===e?D(t,"overflowY"):D(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{var m=I(e,u),h=m[0],v=m[1]-m[2]-a*h;(h||v)&&j(e,u)&&(f+=v,p+=h),u=u instanceof ShadowRoot?u.host:u.parentNode}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},_=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},W=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},K=0,G=[];let H=(p.useMedium(function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(K++)[0],i=a.useState(w)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=_(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=O(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=O(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return F(p,t,e,"h"===p?u:c,!0)},[]),c=a.useCallback(function(e){if(G.length&&G[G.length-1]===i){var n="deltaY"in e?W(e):_(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=_(e),r.current=void 0},[]),f=a.useCallback(function(t){s(t.type,W(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,_(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return G.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,T),document.addEventListener("touchmove",c,T),document.addEventListener("touchstart",d,T),function(){G=G.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,T),document.removeEventListener("touchmove",c,T),document.removeEventListener("touchstart",d,T)}},[]);var m=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?a.createElement(N,{gapMode:e.gapMode}):null)}),v);var U=a.forwardRef(function(e,t){return a.createElement(h,o({},e,{ref:t,sideCar:H}))});U.classNames=h.classNames;let $=U},46059:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(43210),o=n(98599),i=n(66156),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef({}),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=l(u.current);s.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=l(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.N)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=l(u.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!c.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(s.current=l(u.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(u.current=getComputedStyle(e)),a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),c=(0,o.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:c}):null};function l(e){return e?.animationName||"none"}a.displayName="Presence"},47033:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},52238:(e,t,n)=>{n.r(t),n.d(t,{default:()=>o});var r=n(43210);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))})},55509:(e,t,n)=>{n.d(t,{Mz:()=>eY,i3:()=>eJ,UC:()=>eZ,bL:()=>eq,Bk:()=>eD});var r=n(43210);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,a=g(t),l=h(g(t)),u=v(l),c=p(t),s="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(m(t)){case"start":r[l]-=y*(n&&s?-1:1);break;case"end":r[l]+=y*(n&&s?-1:1)}return r}let C=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=E(c,r,u),f=r,p={},m=0;for(let n=0;n<l.length;n++){let{name:i,fn:h}=l[n],{x:v,y:g,data:y,reset:w}=await h({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=E(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function R(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=f(t,e),h=b(m),v=l[p?"floating"===d?"reference":"floating":d],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},C=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-C.top+h.top)/E.y,bottom:(C.bottom-g.bottom+h.bottom)/E.y,left:(g.left-C.left+h.left)/E.x,right:(C.right-g.right+h.right)/E.x}}function A(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function M(e){return o.some(t=>e[t]>=0)}async function S(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=m(n),u="y"===g(n),c=["left","top"].includes(a)?-1:1,s=i&&u?-1:1,d=f(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof y&&(v="end"===l?-1*y:y),u?{x:v*s,y:h*c}:{x:h*c,y:v*s}}function L(){return"undefined"!=typeof window}function N(e){return T(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function k(e){var t;return null==(t=(T(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function T(e){return!!L()&&(e instanceof Node||e instanceof P(e).Node)}function D(e){return!!L()&&(e instanceof Element||e instanceof P(e).Element)}function O(e){return!!L()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function j(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function I(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=K(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function F(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function _(e){let t=W(),n=D(e)?K(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function W(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(N(e))}function K(e){return P(e).getComputedStyle(e)}function G(e){return D(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function H(e){if("html"===N(e))return e;let t=e.assignedSlot||e.parentNode||j(e)&&e.host||k(e);return j(t)?t.host:t}function U(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=H(t);return B(n)?t.ownerDocument?t.ownerDocument.body:t.body:O(n)&&I(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=P(o);if(i){let e=$(a);return t.concat(a,a.visualViewport||[],I(o)?o:[],e&&n?U(e):[])}return t.concat(o,U(o,[],n))}function $(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function z(e){let t=K(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=O(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function X(e){return D(e)?e:e.contextElement}function V(e){let t=X(e);if(!O(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=z(t),a=(i?l(n.width):n.width)/r,u=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let q=c(0);function Y(e){let t=P(e);return W()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:q}function Z(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=X(e),l=c(1);t&&(r?D(r)&&(l=V(r)):l=V(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===P(a))&&o)?Y(a):c(0),s=(i.left+u.x)/l.x,d=(i.top+u.y)/l.y,f=i.width/l.x,p=i.height/l.y;if(a){let e=P(a),t=r&&D(r)?P(r):r,n=e,o=$(n);for(;o&&r&&t!==n;){let e=V(o),t=o.getBoundingClientRect(),r=K(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=i,d+=a,o=$(n=P(o))}}return x({width:f,height:p,x:s,y:d})}function J(e,t){let n=G(e).scrollLeft;return t?t.left+n:Z(k(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=P(e),r=k(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=W();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=k(e),n=G(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+J(e),u=-n.scrollTop;return"rtl"===K(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:u}}(k(e));else if(D(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=O(e)?V(e):c(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=Y(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===K(e).position}function en(e,t){if(!O(e)||"fixed"===K(e).position)return null;if(t)return t(e);let n=e.offsetParent;return k(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=P(e);if(F(e))return n;if(!O(e)){let t=H(e);for(;t&&!B(t);){if(D(t)&&!et(t))return t;t=H(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(N(r))&&et(r);)r=en(r,t);return r&&B(r)&&et(r)&&!_(r)?n:r||function(e){let t=H(e);for(;O(t)&&!B(t);){if(_(t))return t;if(F(t))break;t=H(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=O(t),o=k(t),i="fixed"===n,a=Z(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i){if(("body"!==N(t)||I(o))&&(l=G(t)),r){let e=Z(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=J(o))}let s=!o||r||i?c(0):Q(o,l);return{x:a.left+l.scrollLeft-u.x-s.x,y:a.top+l.scrollTop-u.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=k(r),l=!!t&&F(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),d=c(0),f=O(r);if((f||!f&&!i)&&(("body"!==N(r)||I(a))&&(u=G(r)),O(r))){let e=Z(r);s=V(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!a||f||i?c(0):Q(a,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-u.scrollTop*s.y+d.y+p.y}},getDocumentElement:k,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?F(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=U(e,[],!1).filter(e=>D(e)&&"body"!==N(e)),o=null,i="fixed"===K(e).position,a=i?H(e):e;for(;D(a)&&!B(a);){let t=K(a),n=_(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||I(a)&&!n&&function e(t,n){let r=H(t);return!(r===n||!D(r)||B(r))&&("fixed"===K(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=H(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],c=l.reduce((e,n)=>{let r=ee(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=z(e);return{width:t,height:n}},getScale:V,isElement:D,isRTL:function(e){return"rtl"===K(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let el=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:c,middlewareData:s}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let y=b(p),w={x:n,y:r},x=h(g(o)),E=v(x),C=await u.getDimensions(d),R="y"===x,A=R?"clientHeight":"clientWidth",M=l.reference[E]+l.reference[x]-w[x]-l.floating[E],S=w[x]-l.reference[x],L=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),N=L?L[A]:0;N&&await (null==u.isElement?void 0:u.isElement(L))||(N=c.floating[A]||l.floating[E]);let P=N/2-C[E]/2-1,k=i(y[R?"top":"left"],P),T=i(y[R?"bottom":"right"],P),D=N-C[E]-T,O=N/2-C[E]/2+(M/2-S/2),j=a(k,i(O,D)),I=!s.arrow&&null!=m(o)&&O!==j&&l.reference[E]/2-(O<k?k:T)-C[E]/2<0,F=I?O<k?O-k:O-D:0;return{[x]:w[x]+F,data:{[x]:j,centerOffset:O-j-F,...I&&{alignmentOffset:F}},reset:I}}}),eu=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return C(e,t,{...o,platform:i})};var ec=n(51215),es="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ed(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ef(e);return Math.round(t*n)/n}function em(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let eh=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?el({element:n.current,padding:r}).fn(t):{}:n?el({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await S(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=f(e,t),d={x:n,y:r},m=await R(t,s),v=g(p(o)),y=h(v),w=d[y],b=d[v];if(l){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+m[e],r=w-m[t];w=a(n,i(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+m[e],r=b-m[t];b=a(n,i(b,r))}let x=c.fn({...t,[y]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:l,[v]:u}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=f(e,t),s={x:n,y:r},d=g(o),m=h(d),v=s[m],y=s[d],w=f(l,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+b.mainAxis,n=i.reference[m]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var x,E;let e="y"===m?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[d])||0)+(t?0:b.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[d])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[m]:v,[d]:y}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:C,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:M="none",flipAlignment:S=!0,...L}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let N=p(l),P=g(s),k=p(s)===s,T=await (null==d.isRTL?void 0:d.isRTL(b.floating)),D=C||(k||!S?[w(s)]:function(e){let t=w(e);return[y(e),t,y(t)]}(s)),O="none"!==M;!C&&O&&D.push(...function(e,t,n,r){let o=m(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(s,S,M,T));let j=[s,...D],I=await R(t,L),F=[],_=(null==(r=u.flip)?void 0:r.overflows)||[];if(x&&F.push(I[N]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=m(e),o=h(g(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=w(a)),[a,w(a)]}(l,c,T);F.push(I[e[0]],I[e[1]])}if(_=[..._,{placement:l,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=j[e];if(t)return{data:{index:e,overflows:_},reset:{placement:t}};let n=null==(i=_.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(A){case"bestFit":{let e=null==(a=_.filter(e=>{if(O){let t=g(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l;let{placement:u,rects:c,platform:s,elements:d}=t,{apply:h=()=>{},...v}=f(e,t),y=await R(t,v),w=p(u),b=m(u),x="y"===g(u),{width:E,height:C}=c.floating;"top"===w||"bottom"===w?(o=w,l=b===(await (null==s.isRTL?void 0:s.isRTL(d.floating))?"start":"end")?"left":"right"):(l=w,o="end"===b?"top":"bottom");let A=C-y.top-y.bottom,M=E-y.left-y.right,S=i(C-y[o],A),L=i(E-y[l],M),N=!t.middlewareData.shift,P=S,k=L;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=M),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=A),N&&!b){let e=a(y.left,0),t=a(y.right,0),n=a(y.top,0),r=a(y.bottom,0);x?k=E-2*(0!==e||0!==t?e+t:a(y.left,y.right)):P=C-2*(0!==n||0!==r?n+r:a(y.top,y.bottom))}await h({...t,availableWidth:k,availableHeight:P});let T=await s.getDimensions(d.floating);return E!==T.width||C!==T.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=A(await R(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:M(e)}}}case"escaped":{let e=A(await R(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:M(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...eh(e),options:[e,t]});var eC=n(14163),eR=n(60687),eA=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eR.jsx)(eC.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eR.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eA.displayName="Arrow";var eM=n(98599),eS=n(11273),eL=n(13495),eN=n(66156),eP=n(18853),ek="Popper",[eT,eD]=(0,eS.A)(ek),[eO,ej]=eT(ek),eI=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eR.jsx)(eO,{scope:t,anchor:o,onAnchorChange:i,children:n})};eI.displayName=ek;var eF="PopperAnchor",e_=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=ej(eF,n),l=r.useRef(null),u=(0,eM.s)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,eR.jsx)(eC.sG.div,{...i,ref:u})});e_.displayName=eF;var eW="PopperContent",[eB,eK]=eT(eW),eG=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:c="center",alignOffset:s=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:m=0,sticky:h="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,b=ej(eW,n),[x,E]=r.useState(null),C=(0,eM.s)(t,e=>E(e)),[R,A]=r.useState(null),M=(0,eP.X)(R),S=M?.width??0,L=M?.height??0,N="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},P=Array.isArray(p)?p:[p],T=P.length>0,D={padding:N,boundary:P.filter(ez),altBoundary:T},{refs:O,floatingStyles:j,placement:I,isPositioned:F,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=r.useState(o);ed(p,o)||m(o);let[h,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),b=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),x=a||h,E=l||g,C=r.useRef(null),R=r.useRef(null),A=r.useRef(d),M=null!=c,S=em(c),L=em(i),N=em(s),P=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};L.current&&(e.platform=L.current),eu(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==N.current};k.current&&!ed(A.current,t)&&(A.current=t,ec.flushSync(()=>{f(t)}))})},[p,t,n,L,N]);es(()=>{!1===s&&A.current.isPositioned&&(A.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let k=r.useRef(!1);es(()=>(k.current=!0,()=>{k.current=!1}),[]),es(()=>{if(x&&(C.current=x),E&&(R.current=E),x&&E){if(S.current)return S.current(x,E,P);P()}},[x,E,P,S,M]);let T=r.useMemo(()=>({reference:C,floating:R,setReference:w,setFloating:b}),[w,b]),D=r.useMemo(()=>({reference:x,floating:E}),[x,E]),O=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=ep(D.floating,d.x),r=ep(D.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,D.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:P,refs:T,elements:D,floatingStyles:O}),[d,P,T,D,O])}({strategy:"fixed",placement:o+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=X(e),m=l||c?[...p?U(p):[],...U(t)]:[];m.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let h=p&&d?function(e,t){let n,r=null,o=k(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=f;if(s||t(),!h||!v)return;let g=u(m),y=u(o.clientWidth-(p+h)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(m+v))+"px "+-u(p)+"px",threshold:a(0,i(1,d))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==d){if(!b)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||ea(f,e.getBoundingClientRect())||c(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),l}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?Z(e):null;return f&&function t(){let r=Z(e);y&&!ea(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{l&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[ev({mainAxis:l+L,alignmentAxis:s}),f&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?ey():void 0,...D}),f&&ew({...D}),eb({...D,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),R&&eE({element:R,padding:d}),eX({arrowWidth:S,arrowHeight:L}),v&&ex({strategy:"referenceHidden",...D})]}),[W,B]=eV(I),K=(0,eL.c)(y);(0,eN.N)(()=>{F&&K?.()},[F,K]);let G=_.arrow?.x,H=_.arrow?.y,$=_.arrow?.centerOffset!==0,[z,V]=r.useState();return(0,eN.N)(()=>{x&&V(window.getComputedStyle(x).zIndex)},[x]),(0,eR.jsx)("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...j,transform:F?j.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:z,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eR.jsx)(eB,{scope:n,placedSide:W,onArrowChange:A,arrowX:G,arrowY:H,shouldHideArrow:$,children:(0,eR.jsx)(eC.sG.div,{"data-side":W,"data-align":B,...w,ref:C,style:{...w.style,animation:F?void 0:"none"}})})})});eG.displayName=eW;var eH="PopperArrow",eU={top:"bottom",right:"left",bottom:"top",left:"right"},e$=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eK(eH,n),i=eU[o.placedSide];return(0,eR.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eR.jsx)(eA,{...r,ref:t,style:{...r.style,display:"block"}})})});function ez(e){return null!==e}e$.displayName=eH;var eX=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,c]=eV(n),s={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",m="";return"bottom"===u?(p=i?s:`${d}px`,m=`${-l}px`):"top"===u?(p=i?s:`${d}px`,m=`${r.floating.height+l}px`):"right"===u?(p=`${-l}px`,m=i?s:`${f}px`):"left"===u&&(p=`${r.floating.width+l}px`,m=i?s:`${f}px`),{data:{x:p,y:m}}}});function eV(e){let[t,n="center"]=e.split("-");return[t,n]}var eq=eI,eY=e_,eZ=eG,eJ=e$},62688:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(43210);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim();var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:l="",children:u,iconNode:c,...s},d)=>(0,r.createElement)("svg",{ref:d,...a,width:t,height:t,stroke:e,strokeWidth:o?24*Number(n)/Number(t):n,className:i("lucide",l),...s},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(u)?u:[u]])),u=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...a},u)=>(0,r.createElement)(l,{ref:u,iconNode:t,className:i(`lucide-${o(e)}`,n),...a}));return n.displayName=`${e}`,n}},63376:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(c),m=function(e){!(!e||f.has(e))&&(f.add(e),m(e.parentNode))};c.forEach(m);var h=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,l),s.set(e,u),d.push(e),1===l&&a&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),c(o,i,n,"aria-hidden")):function(){return null}}},65551:(e,t,n)=>{n.d(t,{i:()=>i});var r=n(43210),o=n(13495);function i({prop:e,defaultProp:t,onChange:n=()=>{}}){let[i,a]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[i]=n,a=r.useRef(i),l=(0,o.c)(t);return r.useEffect(()=>{a.current!==i&&(l(i),a.current=i)},[i,a,l]),n}({defaultProp:t,onChange:n}),l=void 0!==e,u=l?e:i,c=(0,o.c)(n);return[u,r.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&c(n)}else a(t)},[l,e,a,c])]}},65822:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},66156:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(43210),o=globalThis?.document?r.useLayoutEffect:()=>{}},70569:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},72942:(e,t,n)=>{n.d(t,{RG:()=>x,bL:()=>P,q7:()=>k});var r=n(43210),o=n(70569),i=n(9510),a=n(98599),l=n(11273),u=n(96963),c=n(14163),s=n(13495),d=n(65551),f=n(43),p=n(60687),m="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,y,w]=(0,i.N)(v),[b,x]=(0,l.A)(v,[w]),[E,C]=b(v),R=r.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(A,{...e,ref:t})})}));R.displayName=v;var A=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:l=!1,dir:u,currentTabStopId:v,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:w,onEntryFocus:b,preventScrollOnEntryFocus:x=!1,...C}=e,R=r.useRef(null),A=(0,a.s)(t,R),M=(0,f.jH)(u),[S=null,L]=(0,d.i)({prop:v,defaultProp:g,onChange:w}),[P,k]=r.useState(!1),T=(0,s.c)(b),D=y(n),O=r.useRef(!1),[j,I]=r.useState(0);return r.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(m,T),()=>e.removeEventListener(m,T)},[T]),(0,p.jsx)(E,{scope:n,orientation:i,dir:M,loop:l,currentTabStopId:S,onItemFocus:r.useCallback(e=>L(e),[L]),onItemShiftTab:r.useCallback(()=>k(!0),[]),onFocusableItemAdd:r.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>I(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:P||0===j?-1:0,"data-orientation":i,...C,ref:A,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!P){let t=new CustomEvent(m,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);N([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),x)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>k(!1))})})}),M="RovingFocusGroupItem",S=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,...s}=e,d=(0,u.B)(),f=l||d,m=C(M,n),h=m.currentTabStopId===f,v=y(n),{onFocusableItemAdd:w,onFocusableItemRemove:b}=m;return r.useEffect(()=>{if(i)return w(),()=>b()},[i,w,b]),(0,p.jsx)(g.ItemSlot,{scope:n,id:f,focusable:i,active:a,children:(0,p.jsx)(c.sG.span,{tabIndex:h?0:-1,"data-orientation":m.orientation,...s,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?m.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return L[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=m.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>N(n))}})})})});S.displayName=M;var L={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function N(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var P=R,k=S},78272:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},96963:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(43210),i=n(66156),a=(r||(r=n.t(o,2)))["useId".toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}}};