import { AUTH_ERROR, INTERNAL_ERROR } from '../errors/index.js';
import { authService } from '../services/authService.js';

/**
 * 用户登录控制器
 * @param {Object} request - Fastify请求对象
 * @param {Object} reply - Fastify响应对象
 * @returns {Promise<Object>} 登录结果
 */
export async function login(request, reply) {
    const { account, password } = request.body;
    
    try {
        // 调用服务层处理登录逻辑
        const result = await authService.login(request.server, account, password);
        
        // 返回成功响应
        reply.success({
            message: '登录成功',
            data: result
        });
    } catch (error) {
        // 如果是已知错误类型，直接抛出
        if (error instanceof AUTH_ERROR || 
            error.name === 'NOT_FOUND_ERROR' || 
            error.name === 'AUTH_ERROR') {
            throw error;
        }
        
        // 记录未知错误
        request.server.log.error({ error, account }, '登录失败');
        throw new INTERNAL_ERROR('登录失败，请稍后重试');
    }
} 