# Queues Folder

This folder contains all BullMQ queue definitions for the application. Queues are responsible for storing and managing jobs that will be processed asynchronously by workers.

## Structure

- **queueConfig.js**: Common configuration and utility functions for all queues
- **imageQueue.js**: Queue for image generation and variation tasks
- **textQueue.js**: Queue for text generation tasks
- **speechQueue.js**: Queue for speech processing tasks (text-to-speech, speech-to-text)
- **visionQueue.js**: Queue for vision processing tasks (image analysis, captioning)
- **index.js**: Exports all queues and helper functions

## Queue Configuration

Each queue is configured with specific settings optimized for its workload:

```javascript
const queueOptions = {
  connection: redisConfig,
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 1000
    },
    removeOnComplete: {
      age: 3600, // 1 hour
      count: 1000
    },
    removeOnFail: {
      age: 3600 * 24 * 7 // 7 days
    },
    lockDuration: 300000, // 5 minutes
    timeout: 300000, // 5 minutes
  },
  limiter: {
    max: 5,
    duration: 1000,
    groupKey: 'queue-name'
  }
};
```

## Usage

### Importing Queues

```javascript
import { 
  imageQueue, 
  textQueue, 
  speechQueue, 
  visionQueue 
} from '../queues/index.js';
```

### Adding Jobs to Queues

The recommended way to add jobs is using the helper functions:

```javascript
import { 
  addImageGenerationJob,
  addTextGenerationJob,
  addTextToSpeechJob,
  addSpeechToTextJob,
  addImageAnalysisJob,
  addImageCaptioningJob
} from '../queues/index.js';

// Add an image generation job
await addImageGenerationJob(taskId, prompt, count, size, style);

// Add a text generation job
await addTextGenerationJob(taskId, prompt, maxTokens, temperature, model);
```

Alternatively, you can add jobs directly to the queues:

```javascript
await imageQueue.add('image-generation', {
  taskId,
  prompt,
  count,
  size,
  style
});
```

## Error Handling

All queues have built-in error handling:

```javascript
// Add error handling for the queue
queue.on('error', (error) => {
  console.error('Queue error:', error);
});

queue.on('failed', (job, error) => {
  console.error(`Job ${job.id} failed:`, error);
});
```

## Monitoring

Queue status can be monitored through the BullMQ dashboard available at `/admin/queues`.
