
import { ColumnDef } from "@tanstack/react-table"
import { Pencil } from "lucide-react"
import { LessonDetailsSchedule } from "./type"

import Link from "next/link"
import { formatDate } from "@/utils/table/formatDate"
import { TooltipIconButton } from "@/components/ui/tooltip-icon-button"


export const lessonDetailsColumns: ColumnDef<LessonDetailsSchedule>[] = [
  {
    accessorKey: "courseName",
    header: '课程名称',
    cell: ({ row }) => <div className="font-medium">{row.original.courses.name}</div>,
  },
  {
    accessorKey: "currentCycleNumber",
    header: "当前周期数",
    cell: ({ row }) => <div className="lowercase">{row.original.currentWeeks}</div>,
  },
  {
    accessorKey: "classTime",
    header: "上课时间",
    cell: ({ row }) => {
      const startDate = row.original.startDate
      const startTime = row.original.startTime
      const endTime = row.original.endTime
      
      const dt = formatDate(startDate, "yyyy-MM-dd (EEEE)")
      return <div className="lowercase">{dt} {startTime}-{endTime}</div>
    },
  },
  {
    accessorKey: "teacher",
    header: "主讲老师",
    cell: ({ row }) => <div className="capitalize">{row.original.teacher.name}</div>,
  },
  {
    id: "actions",
    header: "操作",
    cell: ({ row }) => {
      const schedule = row.original
      // console.log(user, row)
      return (
        <div className="flex space-x-2">
          <Link href={`/academic/schedule/${row.original.id}`}>
          <TooltipIconButton 
            icon={Pencil}
            tooltipText="编辑章节"
          />
          </Link>
        </div>
      )
    },
  },
]