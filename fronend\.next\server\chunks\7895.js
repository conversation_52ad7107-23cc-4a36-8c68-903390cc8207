"use strict";exports.id=7895,exports.ids=[7895],exports.modules={26134:(e,t,r)=>{r.d(t,{G$:()=>W,Hs:()=>b,UC:()=>et,VY:()=>eo,ZL:()=>Q,bL:()=>X,bm:()=>en,hE:()=>er,hJ:()=>ee,l9:()=>z});var o=r(43210),n=r(70569),a=r(98599),i=r(11273),s=r(96963),l=r(65551),d=r(31355),u=r(32547),c=r(25028),p=r(46059),f=r(14163),g=r(1359),m=r(42247),h=r(63376),v=r(8730),y=r(60687),x="Dialog",[D,b]=(0,i.A)(x),[j,R]=D(x),w=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:a,onOpenChange:i,modal:d=!0}=e,u=o.useRef(null),c=o.useRef(null),[p=!1,f]=(0,l.i)({prop:n,defaultProp:a,onChange:i});return(0,y.jsx)(j,{scope:t,triggerRef:u,contentRef:c,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:p,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};w.displayName=x;var C="DialogTrigger",N=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,i=R(C,r),s=(0,a.s)(t,i.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":S(i.open),...o,ref:s,onClick:(0,n.m)(e.onClick,i.onOpenToggle)})});N.displayName=C;var A="DialogPortal",[I,O]=D(A,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:a}=e,i=R(A,t);return(0,y.jsx)(I,{scope:t,forceMount:r,children:o.Children.map(n,e=>(0,y.jsx)(p.C,{present:r||i.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=A;var F="DialogOverlay",_=o.forwardRef((e,t)=>{let r=O(F,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=R(F,e.__scopeDialog);return a.modal?(0,y.jsx)(p.C,{present:o||a.open,children:(0,y.jsx)(P,{...n,ref:t})}):null});_.displayName=F;var P=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=R(F,r);return(0,y.jsx)(m.A,{as:v.DX,allowPinchZoom:!0,shards:[n.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":S(n.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),$="DialogContent",G=o.forwardRef((e,t)=>{let r=O($,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=R($,e.__scopeDialog);return(0,y.jsx)(p.C,{present:o||a.open,children:a.modal?(0,y.jsx)(T,{...n,ref:t}):(0,y.jsx)(B,{...n,ref:t})})});G.displayName=$;var T=o.forwardRef((e,t)=>{let r=R($,e.__scopeDialog),i=o.useRef(null),s=(0,a.s)(t,r.contentRef,i);return o.useEffect(()=>{let e=i.current;if(e)return(0,h.Eq)(e)},[]),(0,y.jsx)(L,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=o.forwardRef((e,t)=>{let r=R($,e.__scopeDialog),n=o.useRef(!1),a=o.useRef(!1);return(0,y.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let o=t.target;r.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),L=o.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:i,onCloseAutoFocus:s,...l}=e,c=R($,r),p=o.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(u.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:i,onUnmountAutoFocus:s,children:(0,y.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":S(c.open),...l,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(J,{titleId:c.titleId}),(0,y.jsx)(K,{contentRef:p,descriptionId:c.descriptionId})]})]})}),Z="DialogTitle",k=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=R(Z,r);return(0,y.jsx)(f.sG.h2,{id:n.titleId,...o,ref:t})});k.displayName=Z;var q="DialogDescription",M=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=R(q,r);return(0,y.jsx)(f.sG.p,{id:n.descriptionId,...o,ref:t})});M.displayName=q;var V="DialogClose",H=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,a=R(V,r);return(0,y.jsx)(f.sG.button,{type:"button",...o,ref:t,onClick:(0,n.m)(e.onClick,()=>a.onOpenChange(!1))})});function S(e){return e?"open":"closed"}H.displayName=V;var U="DialogTitleWarning",[W,Y]=(0,i.q)(U,{contentName:$,titleName:Z,docsSlug:"dialog"}),J=({titleId:e})=>{let t=Y(U),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},K=({contentRef:e,descriptionId:t})=>{let r=Y("DialogDescriptionWarning"),n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return o.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(n)},[n,e,t]),null},X=w,z=N,Q=E,ee=_,et=G,er=k,eo=M,en=H},97895:(e,t,r)=>{r.d(t,{UC:()=>$,VY:()=>L,ZD:()=>T,ZL:()=>_,bL:()=>E,hE:()=>B,hJ:()=>P,l9:()=>F,rc:()=>G});var o=r(43210),n=r(11273),a=r(98599),i=r(26134),s=r(70569),l=r(8730),d=r(60687),u="AlertDialog",[c,p]=(0,n.A)(u,[i.Hs]),f=(0,i.Hs)(),g=e=>{let{__scopeAlertDialog:t,...r}=e,o=f(t);return(0,d.jsx)(i.bL,{...o,...r,modal:!0})};g.displayName=u;var m=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(i.l9,{...n,...o,ref:t})});m.displayName="AlertDialogTrigger";var h=e=>{let{__scopeAlertDialog:t,...r}=e,o=f(t);return(0,d.jsx)(i.ZL,{...o,...r})};h.displayName="AlertDialogPortal";var v=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(i.hJ,{...n,...o,ref:t})});v.displayName="AlertDialogOverlay";var y="AlertDialogContent",[x,D]=c(y),b=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...u}=e,c=f(r),p=o.useRef(null),g=(0,a.s)(t,p),m=o.useRef(null);return(0,d.jsx)(i.G$,{contentName:y,titleName:j,docsSlug:"alert-dialog",children:(0,d.jsx)(x,{scope:r,cancelRef:m,children:(0,d.jsxs)(i.UC,{role:"alertdialog",...c,...u,ref:g,onOpenAutoFocus:(0,s.m)(u.onOpenAutoFocus,e=>{e.preventDefault(),m.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(l.xV,{children:n}),(0,d.jsx)(O,{contentRef:p})]})})})});b.displayName=y;var j="AlertDialogTitle",R=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(i.hE,{...n,...o,ref:t})});R.displayName=j;var w="AlertDialogDescription",C=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(i.VY,{...n,...o,ref:t})});C.displayName=w;var N=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(i.bm,{...n,...o,ref:t})});N.displayName="AlertDialogAction";var A="AlertDialogCancel",I=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,{cancelRef:n}=D(A,r),s=f(r),l=(0,a.s)(t,n);return(0,d.jsx)(i.bm,{...s,...o,ref:l})});I.displayName=A;var O=({contentRef:e})=>{let t=`\`${y}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${y}\` by passing a \`${w}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${y}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return o.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},E=g,F=m,_=h,P=v,$=b,G=N,T=I,B=R,L=C}};