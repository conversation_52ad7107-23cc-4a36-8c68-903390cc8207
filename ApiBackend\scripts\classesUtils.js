/**
 * Classes Utility Functions
 * Contains utility functions for class-related operations
 */

/**
 * Format class data for response
 * @param {Object} classData - Class data from database
 * @returns {Object} Formatted class data
 */
export function formatClassData(classData) {
    if (!classData) return null;
    
    return {
        ...classData,
        startDate: classData.startDate ? Number(classData.startDate) : null,
        endDate: classData.endDate ? Number(classData.endDate) : null,
        createdAt: classData.createdAt ? Number(classData.createdAt) : null,
        updatedAt: classData.updatedAt ? Number(classData.updatedAt) : null,
        daily: classData.daily ? (typeof classData.daily === 'string' ? JSON.parse(classData.daily) : classData.daily) : null
    };
}

/**
 * Format class list for response
 * @param {Array} classes - Array of class data from database
 * @returns {Array} Formatted class list
 */
export function formatClassList(classes) {
    if (!classes || !Array.isArray(classes)) return [];
    
    return classes.map(classData => formatClassData(classData));
}

/**
 * Get class status text
 * @param {string} status - Status code
 * @returns {string} Status text
 */
export function getStatusText(status) {
    const statusMap = {
        'active': '在读',
        'graduated': '已毕业',
        'disbanded': '已解散'
    };
    
    return statusMap[status] || status;
}

/**
 * Get recurrence type text
 * @param {string} recurrenceType - Recurrence type code
 * @returns {string} Recurrence type text
 */
export function getRecurrenceTypeText(recurrenceType) {
    const recurrenceTypeMap = {
        'weekly': '每周',
        'daily': '每天'
    };
    
    return recurrenceTypeMap[recurrenceType] || recurrenceType;
}

/**
 * Get end type text
 * @param {string} endType - End type code
 * @returns {string} End type text
 */
export function getEndTypeText(endType) {
    const endTypeMap = {
        'number_of_times': '按次数',
        'times': '按时间'
    };
    
    return endTypeMap[endType] || endType;
}

/**
 * Get day of week text
 * @param {number} dayOfWeek - Day of week (0-6, where 0 is Sunday)
 * @returns {string} Day of week text
 */
export function getDayOfWeekText(dayOfWeek) {
    const dayOfWeekMap = {
        0: '周日',
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六'
    };
    
    return dayOfWeekMap[dayOfWeek] || `未知(${dayOfWeek})`;
}

/**
 * Format time string (HH:MM)
 * @param {string} time - Time string
 * @returns {string} Formatted time string
 */
export function formatTimeString(time) {
    if (!time) return '';
    
    // If time is already in HH:MM format, return it
    if (/^\d{2}:\d{2}$/.test(time)) {
        return time;
    }
    
    // If time is in HH:MM:SS format, remove seconds
    if (/^\d{2}:\d{2}:\d{2}$/.test(time)) {
        return time.substring(0, 5);
    }
    
    // If time is a number (minutes since midnight), convert to HH:MM
    if (!isNaN(time)) {
        const hours = Math.floor(time / 60);
        const minutes = time % 60;
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    }
    
    return time;
}

/**
 * Format date string (YYYY-MM-DD)
 * @param {number|string} date - Date timestamp or string
 * @returns {string} Formatted date string
 */
export function formatDateString(date) {
    if (!date) return '';
    
    const dateObj = new Date(Number(date));
    const year = dateObj.getFullYear();
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const day = dateObj.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
}

/**
 * Calculate class duration in minutes
 * @param {string} startTime - Start time (HH:MM)
 * @param {string} endTime - End time (HH:MM)
 * @returns {number} Duration in minutes
 */
export function calculateClassDuration(startTime, endTime) {
    if (!startTime || !endTime) return 0;
    
    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);
    
    const startMinutes = startHour * 60 + startMinute;
    const endMinutes = endHour * 60 + endMinute;
    
    return endMinutes - startMinutes;
}

/**
 * Calculate class schedule dates
 * @param {Object} params - Parameters
 * @param {number} params.startDate - Start date timestamp
 * @param {number} params.endDate - End date timestamp
 * @param {string} params.recurrenceType - Recurrence type ('weekly' or 'daily')
 * @param {number} params.dayOfWeek - Day of week (0-6, where 0 is Sunday)
 * @param {number} params.times - Number of times
 * @returns {Array} Array of class dates
 */
export function calculateClassScheduleDates({ startDate, endDate, recurrenceType, dayOfWeek, times }) {
    const dates = [];
    const startDateObj = new Date(Number(startDate));
    const endDateObj = endDate ? new Date(Number(endDate)) : null;
    
    // Set start date to the first occurrence of dayOfWeek
    if (recurrenceType === 'weekly' && dayOfWeek !== undefined) {
        const currentDayOfWeek = startDateObj.getDay();
        const daysToAdd = (dayOfWeek - currentDayOfWeek + 7) % 7;
        startDateObj.setDate(startDateObj.getDate() + daysToAdd);
    }
    
    let currentDate = new Date(startDateObj);
    let count = 0;
    
    while ((!endDateObj || currentDate <= endDateObj) && (!times || count < times)) {
        dates.push(new Date(currentDate));
        count++;
        
        if (recurrenceType === 'weekly') {
            currentDate.setDate(currentDate.getDate() + 7);
        } else if (recurrenceType === 'daily') {
            currentDate.setDate(currentDate.getDate() + 1);
        }
    }
    
    return dates.map(date => date.getTime());
}

/**
 * Check if class is full
 * @param {number} studentCount - Current student count
 * @param {number} maxStudentCount - Maximum student count
 * @returns {boolean} Whether the class is full
 */
export function isClassFull(studentCount, maxStudentCount) {
    return studentCount >= maxStudentCount;
}

/**
 * Calculate class completion percentage
 * @param {number} completedTimes - Completed times
 * @param {number} totalTimes - Total times
 * @returns {number} Completion percentage
 */
export function calculateClassCompletion(completedTimes, totalTimes) {
    if (!totalTimes) return 0;
    return Math.round((completedTimes / totalTimes) * 100);
}

/**
 * Generate class schedule description
 * @param {Object} schedule - Class schedule
 * @returns {string} Schedule description
 */
export function generateScheduleDescription(schedule) {
    if (!schedule) return '';
    
    const { recurrenceType, dayOfWeek, startTime, endTime } = schedule;
    
    if (recurrenceType === 'weekly' && dayOfWeek !== undefined) {
        return `${getDayOfWeekText(dayOfWeek)} ${formatTimeString(startTime)}-${formatTimeString(endTime)}`;
    } else if (recurrenceType === 'daily') {
        return `每天 ${formatTimeString(startTime)}-${formatTimeString(endTime)}`;
    }
    
    return '';
}

export default {
    formatClassData,
    formatClassList,
    getStatusText,
    getRecurrenceTypeText,
    getEndTypeText,
    getDayOfWeekText,
    formatTimeString,
    formatDateString,
    calculateClassDuration,
    calculateClassScheduleDates,
    isClassFull,
    calculateClassCompletion,
    generateScheduleDescription
};
