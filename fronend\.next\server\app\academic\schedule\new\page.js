(()=>{var e={};e.id=1033,e.ids=[1033],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4553:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(60687),a=t(85726);function l(){return(0,r.jsxs)("div",{className:"p-4 space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-5 mb-6",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-100 p-1 sm:p-2",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(a.E,{className:"h-9 w-20"}),(0,r.jsx)(a.E,{className:"h-9 w-40"}),(0,r.jsx)(a.E,{className:"h-9 w-20"})]}),(0,r.jsxs)("div",{className:"flex items-center flex-wrap gap-3",children:[(0,r.jsx)(a.E,{className:"h-9 w-[180px]"}),(0,r.jsx)(a.E,{className:"h-9 w-20"}),(0,r.jsx)(a.E,{className:"h-9 w-24"})]})]})}),(0,r.jsxs)("div",{className:"flex justify-end items-center gap-2",children:[(0,r.jsx)(a.E,{className:"h-10 w-28"}),(0,r.jsx)(a.E,{className:"h-10 w-24"})]})]}),(0,r.jsxs)("div",{className:"border rounded-md shadow-sm",children:[(0,r.jsxs)("div",{className:"grid grid-cols-8 border-b",children:[(0,r.jsx)(a.E,{className:"h-10 m-2"}),Array(7).fill(0).map((e,s)=>(0,r.jsx)(a.E,{className:"h-10 m-2"},s))]}),Array(8).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"grid grid-cols-8 border-b",children:[(0,r.jsx)(a.E,{className:"h-20 m-2"}),Array(7).fill(0).map((e,s)=>(0,r.jsx)(a.E,{className:"h-20 m-2"},s))]},s))]})]})}},4987:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>i,routeModule:()=>x,tree:()=>c});var r=t(65239),a=t(48088),l=t(88170),n=t.n(l),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(s,d);let c={children:["",{children:["academic",{children:["schedule",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,70375)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\new\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,10572)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\new\\loading.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,93083)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21843)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,i=["F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\new\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/academic/schedule/new/page",pathname:"/academic/schedule/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10572:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\academic\\\\schedule\\\\new\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\new\\loading.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16342:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(60687),a=t(85726),l=t(44493);function n(){return(0,r.jsx)("div",{children:(0,r.jsxs)(l.Zp,{className:"border border-slate-200 shadow-sm overflow-hidden rounded-md",children:[(0,r.jsx)(l.aR,{className:"bg-slate-50 px-6 py-4 border-b border-slate-200",children:(0,r.jsx)(a.E,{className:"h-6 w-32"})}),(0,r.jsxs)(l.Wu,{className:"p-6 space-y-8",children:[(0,r.jsxs)("section",{className:"space-y-4",children:[(0,r.jsx)(a.E,{className:"h-5 w-40 mb-3"}),(0,r.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-2",children:[(0,r.jsx)(a.E,{className:"h-10"}),(0,r.jsx)(a.E,{className:"h-10"}),(0,r.jsx)(a.E,{className:"h-10"}),(0,r.jsx)(a.E,{className:"h-10"})]})]}),(0,r.jsxs)("section",{className:"space-y-4",children:[(0,r.jsx)(a.E,{className:"h-5 w-40 mb-3"}),(0,r.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-2",children:[(0,r.jsx)(a.E,{className:"h-10"}),(0,r.jsx)(a.E,{className:"h-10"})]}),(0,r.jsx)(a.E,{className:"h-24"})]}),(0,r.jsxs)("section",{className:"space-y-4",children:[(0,r.jsx)(a.E,{className:"h-5 w-40 mb-3"}),(0,r.jsx)(a.E,{className:"h-10"})]}),(0,r.jsxs)("section",{className:"space-y-4",children:[(0,r.jsx)(a.E,{className:"h-5 w-40 mb-3"}),(0,r.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-2",children:[(0,r.jsx)(a.E,{className:"h-10"}),(0,r.jsx)(a.E,{className:"h-10"}),(0,r.jsx)(a.E,{className:"h-10"})]})]}),(0,r.jsx)(a.E,{className:"h-10 w-full mt-4"})]})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21035:(e,s,t)=>{Promise.resolve().then(t.bind(t,93083))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31883:(e,s,t)=>{"use strict";t.d(s,{A:()=>d});var r=t(60687),a=t(43210),l=t(15079),n=t(4780),o=t(51642);let d=(0,a.memo)(function({teacher:e,setTeacher:s,width:t="w-full",placeholder:d="选择人员",className:c="",showAllOption:i=!0,allOptionText:m="全部人员",allOptionValue:x="all",teacherList:u,disabled:h=!1}){let{data:p,isLoading:j,error:f}=(0,o.X)(),v=(0,a.useCallback)(e=>{s(e)},[s]),b=(0,a.useCallback)(()=>(0,n.cn)(`h-9 ${t}`,c),[t,c]);return(0,r.jsxs)(l.l6,{value:e,onValueChange:v,disabled:h||j,children:[(0,r.jsx)(l.bq,{className:b(),children:(0,r.jsx)(l.yv,{placeholder:d})}),(0,r.jsxs)(l.gC,{children:[f&&(0,r.jsx)(l.eb,{value:"error",disabled:!0,children:String(f)}),j&&(0,r.jsx)(l.eb,{value:"loading",disabled:!0,children:"加载中..."}),!j&&!f&&(0,r.jsxs)(r.Fragment,{children:[i&&(0,r.jsx)(l.eb,{value:x,children:m}),(u||p)?.map(e=>r.jsx(l.eb,{value:e.id,children:e.name},e.id))]})]})]})})},33873:e=>{"use strict";e.exports=require("path")},35776:(e,s,t)=>{"use strict";t.d(s,{default:()=>_});var r=t(60687),a=t(27605),l=t(63442),n=t(71669),o=t(29523),d=t(44493),c=t(84778),i=t(89667),m=t(83066),x=t(60811);function u({form:e}){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(n.zB,{control:e.control,name:"name",render:({field:e})=>(0,r.jsxs)(n.eI,{children:[(0,r.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"班级名称"}),(0,r.jsx)(n.MJ,{children:(0,r.jsx)(i.p,{placeholder:"请输入班级名称",className:"h-10 border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 text-sm bg-slate-50 hover:bg-white transition-colors",...e})}),(0,r.jsx)(n.C5,{className:"text-xs"})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(n.zB,{control:e.control,name:"courseId",render:({field:e})=>(0,r.jsxs)(n.eI,{children:[(0,r.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"课程"}),(0,r.jsx)(m.A,{value:e.value,onChange:e.onChange}),(0,r.jsx)(n.C5,{className:"text-xs"})]})}),(0,r.jsx)(n.zB,{control:e.control,name:"maxStudentCount",render:({field:e})=>(0,r.jsxs)(n.eI,{children:[(0,r.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"最多人数"}),(0,r.jsx)(n.MJ,{children:(0,r.jsx)(i.p,{placeholder:"班级最多学员人数",className:"h-10 border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 text-sm bg-slate-50 hover:bg-white transition-colors",...e})}),(0,r.jsx)(n.C5,{className:"text-xs"})]})})]}),(0,r.jsx)(n.zB,{control:e.control,name:"classroom",render:({field:e})=>(0,r.jsxs)(n.eI,{children:[(0,r.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"教室选择"}),(0,r.jsx)(x.A,{value:e.value||"",onChange:e.onChange}),(0,r.jsx)(n.C5,{className:"text-xs"})]})})]})}var h=t(80013),p=t(40988),j=t(96474),f=t(43210),v=t(4780);let b=({value:e=[],onChange:s})=>{let t=[{label:"周一",value:1},{label:"周二",value:2},{label:"周三",value:3},{label:"周四",value:4},{label:"周五",value:5},{label:"周六",value:6},{label:"周日",value:0}],[a,l]=(0,f.useState)(null),[n,d]=(0,f.useState)(!1),c=s=>e.some(e=>e.day===s),i=s=>e.find(e=>e.day===s),m=e=>{l(e),d(!0)},x=(t,r,a)=>{let l=[...e],n=l.findIndex(e=>e.day===t);-1!==n?l[n]={day:t,startTime:r,endTime:a}:l.push({day:t,startTime:r,endTime:a}),s?.(l),d(!1)},u=t=>{let r=e.filter(e=>e.day!==t);s?.(r)};return(0,r.jsxs)("div",{className:"rounded-md overflow-hidden border border-slate-200",children:[(0,r.jsx)("div",{className:"grid grid-cols-7",children:t.map(e=>(0,r.jsx)("div",{className:"text-center py-2.5 bg-slate-50 text-slate-600 font-medium text-sm border-b border-slate-200",children:e.label},e.value))}),(0,r.jsx)("div",{className:"grid grid-cols-7",children:t.map(e=>{let s=i(e.value),t=c(e.value);return(0,r.jsxs)("div",{className:(0,v.cn)("flex flex-col items-center py-4 transition-colors",t?"bg-slate-50":"hover:bg-slate-50/50"),children:[(0,r.jsxs)(p.AM,{open:n&&a===e.value,onOpenChange:e=>{e||d(!1)},children:[(0,r.jsx)(p.Wv,{asChild:!0,children:(0,r.jsx)("div",{onClick:()=>m(e.value),className:(0,v.cn)("h-8 w-8 rounded-full flex items-center justify-center cursor-pointer transition-colors",t?"bg-slate-700 text-white hover:bg-slate-600":"border border-slate-300 text-slate-500 hover:border-slate-400 hover:text-slate-600"),children:t?(0,r.jsx)("span",{className:"text-xs font-medium",children:s?.startTime?.substring(0,2)}):(0,r.jsx)(j.A,{className:"h-3.5 w-3.5"})})}),(0,r.jsx)(p.hl,{className:"w-72 p-3 shadow-sm",align:"center",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("h4",{className:"font-medium text-sm text-slate-700",children:[e.label,"课程时间"]}),(0,r.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"h-7 px-2 text-xs text-slate-500 hover:text-slate-700 hover:bg-slate-100",onClick:()=>u(e.value),children:"删除"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,r.jsxs)("div",{className:"space-y-1.5",children:[(0,r.jsx)(h.J,{htmlFor:`start-time-${e.value}`,className:"text-xs text-slate-500",children:"开始时间"}),(0,r.jsx)("input",{id:`start-time-${e.value}`,type:"time",defaultValue:s?.startTime||"08:00",className:"w-full h-8 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"})]}),(0,r.jsxs)("div",{className:"space-y-1.5",children:[(0,r.jsx)(h.J,{htmlFor:`end-time-${e.value}`,className:"text-xs text-slate-500",children:"结束时间"}),(0,r.jsx)("input",{id:`end-time-${e.value}`,type:"time",defaultValue:s?.endTime||"09:30",className:"w-full h-8 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"})]})]}),(0,r.jsx)(o.$,{type:"button",className:"w-full h-8 text-sm bg-slate-800 hover:bg-slate-700",onClick:()=>{let s=document.getElementById(`start-time-${e.value}`),t=document.getElementById(`end-time-${e.value}`);x(e.value,s.value,t.value)},children:"确定"})]})})]}),s&&(0,r.jsxs)("div",{className:"text-xs mt-2 text-slate-600",children:[s.startTime," - ",s.endTime]})]},e.value)})})]})};function g({form:e}){return(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)("div",{className:"p-4 border rounded-md border-slate-200",children:(0,r.jsx)(n.zB,{control:e.control,name:"weekdays",render:({field:e})=>(0,r.jsxs)(n.eI,{className:"pt-0",children:[(0,r.jsx)(n.lR,{className:"text-sm text-slate-700 mb-3 block font-medium",children:"选择上课日期"}),(0,r.jsx)(n.MJ,{children:(0,r.jsx)(b,{value:e.value||[],onChange:e.onChange})}),(0,r.jsx)(n.Rr,{className:"mt-3 text-slate-500 text-xs",children:"请选择每周上课的日期并设置时间"}),(0,r.jsx)(n.C5,{className:"text-xs"})]})})})})}var N=t(27479),w=t(39907),y=t(295),C=t(84027),k=t(3589),R=t(78272);function z({form:e}){let[s,t]=(0,f.useState)(!1),a=e.watch("reservation.enabled"),l=e.watch("leave.enabled");return(0,r.jsxs)(y.Nt,{open:s,onOpenChange:t,className:"border border-slate-200 rounded-md shadow-sm overflow-hidden",children:[(0,r.jsxs)(y.R6,{className:"flex w-full items-center justify-between p-3 text-left hover:bg-slate-50 transition-colors",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(C.A,{className:"h-4 w-4 text-slate-500"}),(0,r.jsx)("h3",{className:"text-sm font-medium text-slate-700",children:"高级选项"})]}),(0,r.jsx)("div",{className:"h-6 w-6 flex items-center justify-center text-slate-400",children:s?(0,r.jsx)(k.A,{className:"h-4 w-4"}):(0,r.jsx)(R.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)(y.Ke,{className:"px-4 pb-4 pt-2 space-y-5 border-t border-slate-200",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"font-medium text-sm text-slate-700 mt-2",children:"预约设置"}),(0,r.jsx)(n.zB,{control:e.control,name:"reservation.enabled",render:({field:e})=>(0,r.jsxs)(n.eI,{className:"flex flex-row items-center justify-between p-3 rounded-md border border-slate-200",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开放预约"}),(0,r.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"允许学员预约此班级的课程"})]}),(0,r.jsx)(n.MJ,{children:(0,r.jsx)(N.d,{checked:e.value,onCheckedChange:e.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}),a&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-3 pl-4 border-l border-slate-200",children:[(0,r.jsx)(n.zB,{control:e.control,name:"reservation.appointmentStartTime",render:({field:e})=>(0,r.jsxs)(n.eI,{children:[(0,r.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开放预约时间（小时）"}),(0,r.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"课程开始前多少小时开放预约"}),(0,r.jsx)(n.MJ,{children:(0,r.jsx)("input",{type:"number",placeholder:"如：24小时",className:"w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:e.value??"",onChange:s=>e.onChange(Number(s.target.value)||0)})}),(0,r.jsx)(n.C5,{className:"text-xs"})]})}),(0,r.jsx)(n.zB,{control:e.control,name:"reservation.appointmentEndTime",render:({field:e})=>(0,r.jsxs)(n.eI,{children:[(0,r.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"截止预约时间（小时）"}),(0,r.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"课程开始前多少小时停止预约"}),(0,r.jsx)(n.MJ,{children:(0,r.jsx)("input",{type:"number",placeholder:"如：1小时",className:"w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:e.value??"",onChange:s=>e.onChange(Number(s.target.value)||0)})}),(0,r.jsx)(n.C5,{className:"text-xs"})]})})]})]}),(0,r.jsx)(w.w,{className:"bg-slate-200"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:"考勤选择"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,r.jsx)(n.zB,{control:e.control,name:"attendance.studentScan",render:({field:e})=>(0,r.jsxs)(n.eI,{className:"flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"学员扫码考勤"}),(0,r.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"允许学员通过扫码进行课程签到"})]}),(0,r.jsx)(n.MJ,{children:(0,r.jsx)(N.d,{checked:e.value,onCheckedChange:e.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}),(0,r.jsx)(n.zB,{control:e.control,name:"attendance.autoSystem",render:({field:e})=>(0,r.jsxs)(n.eI,{className:"flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"系统自动考勤"}),(0,r.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"系统自动完成考勤流程"})]}),(0,r.jsx)(n.MJ,{children:(0,r.jsx)(N.d,{checked:e.value,onCheckedChange:e.onChange,className:"data-[state=checked]:bg-slate-700"})})]})})]})]}),(0,r.jsx)(w.w,{className:"bg-slate-200"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:"请假选项"}),(0,r.jsx)(n.zB,{control:e.control,name:"leave.enabled",render:({field:e})=>(0,r.jsxs)(n.eI,{className:"flex flex-row items-center justify-between p-3 rounded-md border border-slate-200",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"开放请假"}),(0,r.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"允许学员请假"})]}),(0,r.jsx)(n.MJ,{children:(0,r.jsx)(N.d,{checked:e.value,onCheckedChange:e.onChange,className:"data-[state=checked]:bg-slate-700"})})]})}),l&&(0,r.jsx)("div",{className:"pl-4 border-l border-slate-200",children:(0,r.jsx)(n.zB,{control:e.control,name:"leave.leaveDeadline",render:({field:e})=>(0,r.jsxs)(n.eI,{children:[(0,r.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"请假截止时间（小时）"}),(0,r.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"课程开始前多少小时停止请假"}),(0,r.jsx)(n.MJ,{children:(0,r.jsx)("input",{type:"number",placeholder:"如：2小时",className:"w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1",value:e.value??"",onChange:s=>e.onChange(Number(s.target.value)||0)})}),(0,r.jsx)(n.C5,{className:"text-xs"})]})})})]}),(0,r.jsx)(w.w,{className:"bg-slate-200"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:"周期设置"}),(0,r.jsx)(n.zB,{control:e.control,name:"isShowWeekCount",render:({field:e})=>(0,r.jsxs)(n.eI,{className:"flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.lR,{className:"text-sm font-medium text-slate-700",children:"启用周期数"}),(0,r.jsx)(n.Rr,{className:"text-xs text-slate-500 mt-1",children:"按周期划分课程，例如：第一周期、第二周期等"})]}),(0,r.jsx)(n.MJ,{children:(0,r.jsx)(N.d,{checked:e.value,onCheckedChange:e.onChange,className:"data-[state=checked]:bg-slate-700"})})]})})]})]})]})}var E=t(45880);let P=E.z.object({name:E.z.string().min(1,"请输入班级名称"),courseId:E.z.string().min(1,"请选择课程包"),teacherId:E.z.string().min(1,"请选择主讲老师"),classroom:E.z.null().optional(),recurrenceType:E.z.enum(["weekly","daily"]),weekdays:E.z.array(E.z.object({day:E.z.number(),startTime:E.z.string(),endTime:E.z.string()})).optional(),daily:E.z.object({startTime:E.z.string().optional(),endTime:E.z.string().optional()}).optional(),endType:E.z.enum(["times","number_of_times"]),startDate:E.z.date().optional(),endDate:E.z.date().optional(),times:E.z.number().optional(),maxStudentCount:E.z.string().min(1,"请输入最大人数"),type:E.z.enum(["temporary","fixed"]),reservation:E.z.object({enabled:E.z.boolean(),appointmentStartTime:E.z.number().min(0,"不能小于0"),appointmentEndTime:E.z.number().min(0,"不能小于0")}),attendance:E.z.object({studentScan:E.z.boolean(),autoSystem:E.z.boolean()}),leave:E.z.object({enabled:E.z.boolean(),leaveDeadline:E.z.number().min(0,"不能小于0")}),isShowWeekCount:E.z.boolean()});var A=t(31883);let F=function({form:e}){return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:" pb-2 space-y-3",children:[(0,r.jsx)(n.lR,{className:"text-sm",children:"主讲老师"}),(0,r.jsx)(A.A,{teacher:e.watch("teacherId"),setTeacher:s=>e.setValue("teacherId",s),showAllOption:!1})]})})};var I=t(88397),S=t(53541);function _(){let[e]=(0,I.BU)(),s=(0,a.mN)({resolver:(0,l.u)(P),defaultValues:{name:"",recurrenceType:"weekly",weekdays:[],endType:"number_of_times",maxStudentCount:"",type:"fixed",reservation:{enabled:!1,appointmentStartTime:24,appointmentEndTime:1},attendance:{studentScan:!1,autoSystem:!1},leave:{enabled:!1,leaveDeadline:2},isShowWeekCount:!1}});async function t(s){let t={name:s.name,courseId:s.courseId,teacherId:s.teacherId,classroomId:s.classroom&&"none"!==s.classroom?s.classroom:null,weekdays:s.weekdays,maxStudentCount:s.maxStudentCount,reservation:s.reservation,attendance:s.attendance,leave:s.leave,isShowWeekCount:s.isShowWeekCount};try{let s=await e(t).unwrap();console.log(s),c.l.success("班级创建成功.")}catch(e){c.l.error("班级创建失败.")}}return(0,r.jsx)("div",{children:(0,r.jsxs)(d.Zp,{className:"border border-slate-200 shadow-sm overflow-hidden rounded-md",children:[(0,r.jsx)(d.aR,{className:"bg-slate-50 px-6 py-4 border-b border-slate-200",children:(0,r.jsx)(d.ZB,{className:"text-lg font-medium text-slate-800",children:"临时排课"})}),(0,r.jsx)(d.Wu,{className:"p-6 space-y-8",children:(0,r.jsx)(n.lV,{...s,children:(0,r.jsxs)("form",{onSubmit:s.handleSubmit(t),className:"space-y-8",children:[(0,r.jsx)("section",{children:(0,r.jsx)(u,{form:s})}),(0,r.jsx)("section",{children:(0,r.jsx)(g,{form:s})}),(0,r.jsx)("section",{children:(0,r.jsx)(F,{form:s})}),(0,r.jsx)("section",{children:(0,r.jsx)(z,{form:s})}),(0,r.jsx)("div",{className:"pt-2",children:(0,r.jsx)(S.LQ,{permission:"schedule:create",children:(0,r.jsx)(o.$,{type:"submit",className:"w-full h-10 text-sm font-medium bg-slate-800 hover:bg-slate-700 transition-colors rounded-md",children:"创建班级"})})})]})})})]})})}},40599:(e,s,t)=>{"use strict";t.d(s,{UC:()=>G,ZL:()=>W,bL:()=>J,l9:()=>$});var r=t(43210),a=t(70569),l=t(98599),n=t(11273),o=t(31355),d=t(1359),c=t(32547),i=t(96963),m=t(55509),x=t(25028),u=t(46059),h=t(14163),p=t(8730),j=t(65551),f=t(63376),v=t(42247),b=t(60687),g="Popover",[N,w]=(0,n.A)(g,[m.Bk]),y=(0,m.Bk)(),[C,k]=N(g),R=e=>{let{__scopePopover:s,children:t,open:a,defaultOpen:l,onOpenChange:n,modal:o=!1}=e,d=y(s),c=r.useRef(null),[x,u]=r.useState(!1),[h=!1,p]=(0,j.i)({prop:a,defaultProp:l,onChange:n});return(0,b.jsx)(m.bL,{...d,children:(0,b.jsx)(C,{scope:s,contentId:(0,i.B)(),triggerRef:c,open:h,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),hasCustomAnchor:x,onCustomAnchorAdd:r.useCallback(()=>u(!0),[]),onCustomAnchorRemove:r.useCallback(()=>u(!1),[]),modal:o,children:t})})};R.displayName=g;var z="PopoverAnchor";r.forwardRef((e,s)=>{let{__scopePopover:t,...a}=e,l=k(z,t),n=y(t),{onCustomAnchorAdd:o,onCustomAnchorRemove:d}=l;return r.useEffect(()=>(o(),()=>d()),[o,d]),(0,b.jsx)(m.Mz,{...n,...a,ref:s})}).displayName=z;var E="PopoverTrigger",P=r.forwardRef((e,s)=>{let{__scopePopover:t,...r}=e,n=k(E,t),o=y(t),d=(0,l.s)(s,n.triggerRef),c=(0,b.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":D(n.open),...r,ref:d,onClick:(0,a.m)(e.onClick,n.onOpenToggle)});return n.hasCustomAnchor?c:(0,b.jsx)(m.Mz,{asChild:!0,...o,children:c})});P.displayName=E;var A="PopoverPortal",[F,I]=N(A,{forceMount:void 0}),S=e=>{let{__scopePopover:s,forceMount:t,children:r,container:a}=e,l=k(A,s);return(0,b.jsx)(F,{scope:s,forceMount:t,children:(0,b.jsx)(u.C,{present:t||l.open,children:(0,b.jsx)(x.Z,{asChild:!0,container:a,children:r})})})};S.displayName=A;var _="PopoverContent",T=r.forwardRef((e,s)=>{let t=I(_,e.__scopePopover),{forceMount:r=t.forceMount,...a}=e,l=k(_,e.__scopePopover);return(0,b.jsx)(u.C,{present:r||l.open,children:l.modal?(0,b.jsx)(q,{...a,ref:s}):(0,b.jsx)(B,{...a,ref:s})})});T.displayName=_;var q=r.forwardRef((e,s)=>{let t=k(_,e.__scopePopover),n=r.useRef(null),o=(0,l.s)(s,n),d=r.useRef(!1);return r.useEffect(()=>{let e=n.current;if(e)return(0,f.Eq)(e)},[]),(0,b.jsx)(v.A,{as:p.DX,allowPinchZoom:!0,children:(0,b.jsx)(M,{...e,ref:o,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),d.current||t.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let s=e.detail.originalEvent,t=0===s.button&&!0===s.ctrlKey;d.current=2===s.button||t},{checkForDefaultPrevented:!1}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),B=r.forwardRef((e,s)=>{let t=k(_,e.__scopePopover),a=r.useRef(!1),l=r.useRef(!1);return(0,b.jsx)(M,{...e,ref:s,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(a.current||t.triggerRef.current?.focus(),s.preventDefault()),a.current=!1,l.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(a.current=!0,"pointerdown"!==s.detail.originalEvent.type||(l.current=!0));let r=s.target;t.triggerRef.current?.contains(r)&&s.preventDefault(),"focusin"===s.detail.originalEvent.type&&l.current&&s.preventDefault()}})}),M=r.forwardRef((e,s)=>{let{__scopePopover:t,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:l,disableOutsidePointerEvents:n,onEscapeKeyDown:i,onPointerDownOutside:x,onFocusOutside:u,onInteractOutside:h,...p}=e,j=k(_,t),f=y(t);return(0,d.Oh)(),(0,b.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,b.jsx)(o.qW,{asChild:!0,disableOutsidePointerEvents:n,onInteractOutside:h,onEscapeKeyDown:i,onPointerDownOutside:x,onFocusOutside:u,onDismiss:()=>j.onOpenChange(!1),children:(0,b.jsx)(m.UC,{"data-state":D(j.open),role:"dialog",id:j.contentId,...f,...p,ref:s,style:{...p.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),O="PopoverClose";function D(e){return e?"open":"closed"}r.forwardRef((e,s)=>{let{__scopePopover:t,...r}=e,l=k(O,t);return(0,b.jsx)(h.sG.button,{type:"button",...r,ref:s,onClick:(0,a.m)(e.onClick,()=>l.onOpenChange(!1))})}).displayName=O,r.forwardRef((e,s)=>{let{__scopePopover:t,...r}=e,a=y(t);return(0,b.jsx)(m.i3,{...a,...r,ref:s})}).displayName="PopoverArrow";var J=R,$=P,W=S,G=T},53541:(e,s,t)=>{"use strict";t.d(s,{LQ:()=>l});var r=t(60687),a=t(30596);let l=({permission:e,children:s,fallback:t=null,logic:l="any"})=>{let n=(0,a.G)(e=>e.userPermissions.permissions);if(!e||Array.isArray(e)&&0===e.length)return(0,r.jsx)(r.Fragment,{children:s});let o=Array.isArray(e)?e:[e],d=!1;return("all"===l?o.every(e=>n.includes(e)):o.some(e=>n.includes(e)))?(0,r.jsx)(r.Fragment,{children:s}):(0,r.jsx)(r.Fragment,{children:t})};t(29523);var n=t(43210),o=t(24224),d=t(4780);let c=(0,o.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}});n.forwardRef(({className:e,variant:s,...t},a)=>(0,r.jsx)("div",{ref:a,role:"alert",className:(0,d.cn)(c({variant:s}),e),...t})).displayName="Alert",n.forwardRef(({className:e,...s},t)=>(0,r.jsx)("h5",{ref:t,className:(0,d.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle",n.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,d.cn)("text-sm [&_p]:leading-relaxed",e),...s})).displayName="AlertDescription"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70375:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,metadata:()=>l});var r=t(37413),a=t(91016);let l={title:`临时排课 - 蜜卡`};function n(){return(0,r.jsx)(a.default,{})}},71513:(e,s,t)=>{Promise.resolve().then(t.bind(t,10572))},74075:e=>{"use strict";e.exports=require("zlib")},78217:(e,s,t)=>{Promise.resolve().then(t.bind(t,35776))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84665:(e,s,t)=>{Promise.resolve().then(t.bind(t,16342))},87945:(e,s,t)=>{Promise.resolve().then(t.bind(t,91016))},91016:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\academic\\\\schedule\\\\new\\\\components\\\\NewScheduleForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\new\\components\\NewScheduleForm.tsx","default")},93083:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\academic\\\\schedule\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\loading.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},97059:(e,s,t)=>{Promise.resolve().then(t.bind(t,4553))}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,7392,5814,3928,3443,2951,1991,7605,3019,9879,1530],()=>t(4987));module.exports=r})();