// 发送给指定用户
export async function sendToSpecifiedUser(fastify, userId, institutionId, payload) {
    const clients = fastify.websocketServer.clients;
    if (clients.size === 0) {
        return;
    }
    const message = JSON.stringify(payload);
    for (const client of clients) {
        if (client.userId === userId && client.instId === institutionId) {
            // 检查 WebSocket 是否仍然打开
            if (client.readyState === client.OPEN) {
                client.send(message);
            }
        }
    }
}

// 发送给所有用户
export async function sendToAllUsers(fastify, payload) {
    const clients = fastify.websocketServer.clients;
    if (clients.size === 0) {
        return;
    }
    const message = JSON.stringify(payload);
    for (const client of clients) {
        // 检查 WebSocket 是否仍然打开
        if (client.readyState === client.OPEN) {
            client.send(message);
        }
    }
}

// 发送给机构内所有用户
export async function sendToAllUsersInInstitution(fastify, institutionId, payload) {
    const clients = fastify.websocketServer.clients;
    if (clients.size === 0) {
        return;
    }
    const message = JSON.stringify(payload);
    for (const client of clients) {
        if (client.instId === institutionId) {
            // 检查 WebSocket 是否仍然打开
            if (client.readyState === client.OPEN) {
                client.send(message);
            }
        }
    }
}

// 发送给机构内所有用户，不包括指定用户
export async function sendToAllUsersInInstitutionExceptUser(fastify, institutionId, userId, payload) {
    const clients = fastify.websocketServer.clients;
    if (clients.size === 0) {
        return;
    }
    const message = JSON.stringify(payload);
    for (const client of clients) {
        if (client.instId === institutionId && client.userId !== userId) {
            // 检查 WebSocket 是否仍然打开
            if (client.readyState === client.OPEN) {
                client.send(message);
            }
        }
    }
}



