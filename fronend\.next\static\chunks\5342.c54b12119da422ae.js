(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5342],{4217:(e,r,t)=>{var n=t(36713),o=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(o,""):e}},5623:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>l,t:()=>i});var n=t(12115);function o(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function i(...e){return r=>{let t=!1,n=e.map(e=>{let n=o(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():o(e[r],null)}}}}function l(...e){return n.useCallback(i(...e),e)}},7985:(e,r,t)=>{e.exports="object"==typeof t.g&&t.g&&t.g.Object===Object&&t.g},12318:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},19946:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var n=t(12115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)((e,r)=>{let{color:t="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:u,className:s="",children:c,iconNode:p,...f}=e;return(0,n.createElement)("svg",{ref:r,...l,width:o,height:o,stroke:t,strokeWidth:u?24*Number(a)/Number(o):a,className:i("lucide",s),...f},[...p.map(e=>{let[r,t]=e;return(0,n.createElement)(r,t)}),...Array.isArray(c)?c:[c]])}),u=(e,r)=>{let t=(0,n.forwardRef)((t,l)=>{let{className:u,...s}=t;return(0,n.createElement)(a,{ref:l,iconNode:r,className:i("lucide-".concat(o(e)),u),...s})});return t.displayName="".concat(e),t}},20547:(e,r,t)=>{"use strict";t.d(r,{UC:()=>Z,ZL:()=>B,bL:()=>U,l9:()=>V});var n=t(12115),o=t(85185),i=t(6101),l=t(46081),a=t(19178),u=t(92293),s=t(25519),c=t(61285),p=t(35152),f=t(34378),d=t(28905),v=t(63655),h=t(99708),y=t(5845),g=t(38168),x=t(93795),m=t(95155),b="Popover",[j,C]=(0,l.A)(b,[p.Bk]),w=(0,p.Bk)(),[k,A]=j(b),O=e=>{let{__scopePopover:r,children:t,open:o,defaultOpen:i,onOpenChange:l,modal:a=!1}=e,u=w(r),s=n.useRef(null),[f,d]=n.useState(!1),[v=!1,h]=(0,y.i)({prop:o,defaultProp:i,onChange:l});return(0,m.jsx)(p.bL,{...u,children:(0,m.jsx)(k,{scope:r,contentId:(0,c.B)(),triggerRef:s,open:v,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:f,onCustomAnchorAdd:n.useCallback(()=>d(!0),[]),onCustomAnchorRemove:n.useCallback(()=>d(!1),[]),modal:a,children:t})})};O.displayName=b;var P="PopoverAnchor";n.forwardRef((e,r)=>{let{__scopePopover:t,...o}=e,i=A(P,t),l=w(t),{onCustomAnchorAdd:a,onCustomAnchorRemove:u}=i;return n.useEffect(()=>(a(),()=>u()),[a,u]),(0,m.jsx)(p.Mz,{...l,...o,ref:r})}).displayName=P;var R="PopoverTrigger",E=n.forwardRef((e,r)=>{let{__scopePopover:t,...n}=e,l=A(R,t),a=w(t),u=(0,i.s)(r,l.triggerRef),s=(0,m.jsx)(v.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":L(l.open),...n,ref:u,onClick:(0,o.m)(e.onClick,l.onOpenToggle)});return l.hasCustomAnchor?s:(0,m.jsx)(p.Mz,{asChild:!0,...a,children:s})});E.displayName=R;var N="PopoverPortal",[D,F]=j(N,{forceMount:void 0}),T=e=>{let{__scopePopover:r,forceMount:t,children:n,container:o}=e,i=A(N,r);return(0,m.jsx)(D,{scope:r,forceMount:t,children:(0,m.jsx)(d.C,{present:t||i.open,children:(0,m.jsx)(f.Z,{asChild:!0,container:o,children:n})})})};T.displayName=N;var _="PopoverContent",M=n.forwardRef((e,r)=>{let t=F(_,e.__scopePopover),{forceMount:n=t.forceMount,...o}=e,i=A(_,e.__scopePopover);return(0,m.jsx)(d.C,{present:n||i.open,children:i.modal?(0,m.jsx)(S,{...o,ref:r}):(0,m.jsx)(W,{...o,ref:r})})});M.displayName=_;var S=n.forwardRef((e,r)=>{let t=A(_,e.__scopePopover),l=n.useRef(null),a=(0,i.s)(r,l),u=n.useRef(!1);return n.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,m.jsx)(x.A,{as:h.DX,allowPinchZoom:!0,children:(0,m.jsx)(z,{...e,ref:a,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;e.preventDefault(),u.current||null===(r=t.triggerRef.current)||void 0===r||r.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey;u.current=2===r.button||t},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),W=n.forwardRef((e,r)=>{let t=A(_,e.__scopePopover),o=n.useRef(!1),i=n.useRef(!1);return(0,m.jsx)(z,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{var n,l;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,r),r.defaultPrevented||(o.current||null===(l=t.triggerRef.current)||void 0===l||l.focus(),r.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:r=>{var n,l;null===(n=e.onInteractOutside)||void 0===n||n.call(e,r),r.defaultPrevented||(o.current=!0,"pointerdown"!==r.detail.originalEvent.type||(i.current=!0));let a=r.target;(null===(l=t.triggerRef.current)||void 0===l?void 0:l.contains(a))&&r.preventDefault(),"focusin"===r.detail.originalEvent.type&&i.current&&r.preventDefault()}})}),z=n.forwardRef((e,r)=>{let{__scopePopover:t,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:i,disableOutsidePointerEvents:l,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:d,onInteractOutside:v,...h}=e,y=A(_,t),g=w(t);return(0,u.Oh)(),(0,m.jsx)(s.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,m.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:v,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:d,onDismiss:()=>y.onOpenChange(!1),children:(0,m.jsx)(p.UC,{"data-state":L(y.open),role:"dialog",id:y.contentId,...g,...h,ref:r,style:{...h.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),I="PopoverClose";function L(e){return e?"open":"closed"}n.forwardRef((e,r)=>{let{__scopePopover:t,...n}=e,i=A(I,t);return(0,m.jsx)(v.sG.button,{type:"button",...n,ref:r,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})}).displayName=I,n.forwardRef((e,r)=>{let{__scopePopover:t,...n}=e,o=w(t);return(0,m.jsx)(p.i3,{...o,...n,ref:r})}).displayName="PopoverArrow";var U=O,V=E,B=T,Z=M},20570:(e,r,t)=>{var n=t(24376),o=Object.prototype,i=o.hasOwnProperty,l=o.toString,a=n?n.toStringTag:void 0;e.exports=function(e){var r=i.call(e,a),t=e[a];try{e[a]=void 0;var n=!0}catch(e){}var o=l.call(e);return n&&(r?e[a]=t:delete e[a]),o}},24357:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},24376:(e,r,t)=>{e.exports=t(82500).Symbol},36713:e=>{var r=/\s/;e.exports=function(e){for(var t=e.length;t--&&r.test(e.charAt(t)););return t}},36815:(e,r,t)=>{var n=t(4217),o=t(67460),i=t(70771),l=0/0,a=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,s=/^0o[0-7]+$/i,c=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return l;if(o(e)){var r="function"==typeof e.valueOf?e.valueOf():e;e=o(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=n(e);var t=u.test(e);return t||s.test(e)?c(e.slice(2),t?2:8):a.test(e)?l:+e}},45964:(e,r,t)=>{var n=t(67460),o=t(76685),i=t(36815),l=Math.max,a=Math.min;e.exports=function(e,r,t){var u,s,c,p,f,d,v=0,h=!1,y=!1,g=!0;if("function"!=typeof e)throw TypeError("Expected a function");function x(r){var t=u,n=s;return u=s=void 0,v=r,p=e.apply(n,t)}function m(e){var t=e-d,n=e-v;return void 0===d||t>=r||t<0||y&&n>=c}function b(){var e,t,n,i=o();if(m(i))return j(i);f=setTimeout(b,(e=i-d,t=i-v,n=r-e,y?a(n,c-t):n))}function j(e){return(f=void 0,g&&u)?x(e):(u=s=void 0,p)}function C(){var e,t=o(),n=m(t);if(u=arguments,s=this,d=t,n){if(void 0===f)return v=e=d,f=setTimeout(b,r),h?x(e):p;if(y)return clearTimeout(f),f=setTimeout(b,r),x(d)}return void 0===f&&(f=setTimeout(b,r)),p}return r=i(r)||0,n(t)&&(h=!!t.leading,c=(y="maxWait"in t)?l(i(t.maxWait)||0,r):c,g="trailing"in t?!!t.trailing:g),C.cancel=function(){void 0!==f&&clearTimeout(f),v=0,u=d=s=f=void 0},C.flush=function(){return void 0===f?p:j(o())},C}},47863:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},48611:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},64439:e=>{var r=Object.prototype.toString;e.exports=function(e){return r.call(e)}},66474:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},67460:e=>{e.exports=function(e){var r=typeof e;return null!=e&&("object"==r||"function"==r)}},70771:(e,r,t)=>{var n=t(98233),o=t(48611);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},76685:(e,r,t)=>{var n=t(82500);e.exports=function(){return n.Date.now()}},82500:(e,r,t)=>{var n=t(7985),o="object"==typeof self&&self&&self.Object===Object&&self;e.exports=n||o||Function("return this")()},98233:(e,r,t)=>{var n=t(24376),o=t(20570),i=t(64439),l=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":l&&l in Object(e)?o(e):i(e)}},99708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>l,xV:()=>u});var n=t(12115),o=t(6101),i=t(95155),l=n.forwardRef((e,r)=>{let{children:t,...o}=e,l=n.Children.toArray(t),u=l.find(s);if(u){let e=u.props.children,t=l.map(r=>r!==u?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(a,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,t):null})}return(0,i.jsx)(a,{...o,ref:r,children:t})});l.displayName="Slot";var a=n.forwardRef((e,r)=>{let{children:t,...i}=e;if(n.isValidElement(t)){let e=function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(t),l=function(e,r){let t={...r};for(let n in r){let o=e[n],i=r[n];/^on[A-Z]/.test(n)?o&&i?t[n]=(...e)=>{i(...e),o(...e)}:o&&(t[n]=o):"style"===n?t[n]={...o,...i}:"className"===n&&(t[n]=[o,i].filter(Boolean).join(" "))}return{...e,...t}}(i,t.props);return t.type!==n.Fragment&&(l.ref=r?(0,o.t)(r,e):e),n.cloneElement(t,l)}return n.Children.count(t)>1?n.Children.only(null):null});a.displayName="SlotClone";var u=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function s(e){return n.isValidElement(e)&&e.type===u}}}]);