"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5469],{15469:(e,t,a)=>{a.r(t),a.d(t,{default:()=>f});var r=a(95155),n=a(30285),i=a(54165),d=a(62523),o=a(85057),l=a(59409),s=a(88539),c=a(57141),u=a(12115),m=a(79484),h=a(55594);let p=h.z.object({name:h.z.string().min(1,"姓名不能为空"),gender:h.z.string().min(1,"请选择性别"),phone:h.z.string().regex(/^1[3-9]\d{9}$/,"请输入正确的手机号"),idCard:h.z.string().regex(/^\d{17}[\dXx]$/,"请输入正确的身份证号"),birthday:h.z.number().min(0,"请选择生日"),type:h.z.string().min(1,"请选择学员类型")}),g={id:"",name:"",gender:"",birthday:null,phone:"",cardNumber:"",balance:"",points:"",source:"",sourceDesc:"",referrer:"",address:"",idCard:"",school:"",intentLevel:"",parentName:"",remarks:"",type:"",status:""},f=function(e){let{open:t,onOpenChange:a,student:f,onSave:y}=e,[x,b]=(0,u.useState)(g),[v,w]=(0,u.useState)({});(0,u.useEffect)(()=>{f?b({...f}):b(g),w({})},[f]);let j=(0,u.useCallback)(e=>{let{name:t,value:a}=e.target;b(e=>({...e,[t]:a})),v[t]&&w(e=>({...e,[t]:void 0}))},[v]),N=(0,u.useCallback)((e,t)=>{b(a=>({...a,[e]:t})),v[e]&&w(t=>({...t,[e]:void 0}))},[v]),M=(0,u.useMemo)(()=>{let e=new Date;return{minDate:new Date(1900,0,1),maxDate:e,defaultMonth:e}},[]),k=(0,u.useCallback)(()=>{try{return p.parse(x),w({}),!0}catch(e){if(e instanceof h.z.ZodError){let t={};e.errors.forEach(e=>{t[e.path[0]]=e.message}),w(t)}return!1}},[x]),C=(0,u.useCallback)(()=>{k()&&y&&y(x)},[y,x,k]),_=(0,u.useCallback)(()=>{a(!1)},[a]),W=(0,u.useMemo)(()=>[{id:"name",label:"姓名",type:"input",required:!0},{id:"gender",label:"性别",type:"select",options:c.fb,required:!0},{id:"birthday",label:"生日",type:"date",required:!0},{id:"idCard",label:"身份证号",type:"input",required:!0},{id:"phone",label:"手机号",type:"input",required:!0},{id:"parentName",label:"家长姓名",type:"input"},{id:"address",label:"地址",type:"input",colSpan:2},{id:"source",label:"来源",type:"input"},{id:"sourceDesc",label:"来源描述",type:"input"},{id:"referrer",label:"推荐人",type:"input"},{id:"school",label:"学校",type:"input"},{id:"intentLevel",label:"意向等级",type:"select",options:c.x9},{id:"type",label:"学员类型",type:"select",options:c.IC,required:!0},{id:"remarks",label:"备注",type:"textarea",colSpan:2}],[]);return(0,r.jsx)(i.lG,{open:t,onOpenChange:a,children:(0,r.jsxs)(i.Cf,{className:"sm:max-w-[800px]",children:[(0,r.jsx)(i.L3,{className:"text-xl font-bold mb-4",children:"学员详细信息"}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-x-6 gap-y-4 w-full mt-2 max-h-[60vh] overflow-y-auto p-2",children:W.map(e=>(0,r.jsxs)("div",{className:"space-y-2 ".concat(2===e.colSpan?"col-span-2":""),children:[(0,r.jsxs)(o.J,{htmlFor:e.id,children:[e.label,e.required&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),"input"===e.type&&(0,r.jsx)(d.p,{id:e.id,name:e.id,value:x[e.id]||"",onChange:j,className:v[e.id]?"border-red-500":""}),"select"===e.type&&(0,r.jsxs)(l.l6,{value:String(x[e.id]||""),onValueChange:t=>N(e.id,t),children:[(0,r.jsx)(l.bq,{className:v[e.id]?"border-red-500":"",children:(0,r.jsx)(l.yv,{placeholder:"选择".concat(e.label)})}),(0,r.jsx)(l.gC,{children:e.options&&Object.entries(e.options).map(e=>{let[t,a]=e;return(0,r.jsx)(l.eb,{value:t,children:a.label},t)})})]}),"date"===e.type&&(0,r.jsx)("div",{className:v[e.id]?"border border-red-500 rounded-md":"",children:(0,r.jsx)(m.l,{id:e.id,value:new Date(x[e.id]),onChange:t=>j({target:{name:e.id,value:(null==t?void 0:t.getTime())||0}}),minDate:M.minDate,maxDate:M.maxDate,defaultMonth:M.defaultMonth})}),"textarea"===e.type&&(0,r.jsx)(s.T,{id:e.id,name:e.id,value:x[e.id]||"",onChange:j,rows:3}),v[e.id]&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:v[e.id]})]},e.id))}),(0,r.jsxs)(i.Es,{className:"mt-6",children:[(0,r.jsx)(n.$,{variant:"outline",onClick:_,children:"取消"}),(0,r.jsx)(n.$,{onClick:C,children:"保存"})]})]})})}},24122:(e,t,a)=>{a.d(t,{g:()=>m});let r={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}};var n=a(67356);let i={date:(0,n.k)({formats:{full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},defaultWidth:"full"}),time:(0,n.k)({formats:{full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},defaultWidth:"full"}),dateTime:(0,n.k)({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};var d=a(34548);function o(e,t,a){var r,n,i;let o="eeee p";return(r=e,n=t,i=a,+(0,d.k)(r,i)==+(0,d.k)(n,i))?o:e.getTime()>t.getTime()?"'下个'"+o:"'上个'"+o}let l={lastWeek:o,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:o,other:"PP p"};var s=a(58698);let c={ordinalNumber:(e,t)=>{let a=Number(e);switch(null==t?void 0:t.unit){case"date":return a.toString()+"日";case"hour":return a.toString()+"时";case"minute":return a.toString()+"分";case"second":return a.toString()+"秒";default:return"第 "+a.toString()}},era:(0,s.o)({values:{narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},defaultWidth:"wide"}),quarter:(0,s.o)({values:{narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,s.o)({values:{narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},defaultWidth:"wide"}),day:(0,s.o)({values:{narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},defaultWidth:"wide"}),dayPeriod:(0,s.o)({values:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultWidth:"wide",formattingValues:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultFormattingWidth:"wide"})};var u=a(44008);let m={code:"zh-CN",formatDistance:(e,t,a)=>{let n;let i=r[e];return(n="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",String(t)),null==a?void 0:a.addSuffix)?a.comparison&&a.comparison>0?n+"内":n+"前":n},formatLong:i,formatRelative:(e,t,a,r)=>{let n=l[e];return"function"==typeof n?n(t,a,r):n},localize:c,match:{ordinalNumber:(0,a(40972).K)({matchPattern:/^(第\s*)?\d+(日|时|分|秒)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,u.A)({matchPatterns:{narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(前)/i,/^(公元)/i]},defaultParseWidth:"any"}),quarter:(0,u.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,u.A)({matchPatterns:{narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},defaultParseWidth:"any"}),day:(0,u.A)({matchPatterns:{narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},defaultParseWidth:"any"}),dayPeriod:(0,u.A)({matchPatterns:{any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}}},69074:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},79484:(e,t,a)=>{a.d(t,{l:()=>b});var r=a(95155),n=a(12115),i=a(86622),d=a(73168),o=a(24122),l=a(69074),s=a(30285),c=a(85511),u=a(14636),m=a(42355),h=a(13052);let p=["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"];function g(e){let{view:t,currentMonth:a,minDate:n,maxDate:i,onPrevious:d,onNext:o,onViewChange:l}=e,c=a.getFullYear(),u=a.getMonth(),g=Math.max(n.getFullYear(),c-6),f=Math.min(i.getFullYear(),g+11),y=()=>"calendar"===t?"month":"month"===t?"year":"calendar";return(0,r.jsx)("div",{className:"p-3 border-b",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(s.$,{variant:"ghost",size:"icon",onClick:d,className:"h-7 w-7 bg-transparent p-0",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsxs)(s.$,{variant:"ghost",onClick:()=>l(y()),className:"h-9 px-4 font-medium text-base hover:bg-transparent",children:["calendar"===t&&(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{className:"text-primary",children:p[u]}),(0,r.jsx)("span",{children:c})]}),"month"===t&&(0,r.jsx)("span",{children:c}),"year"===t&&(0,r.jsx)("span",{children:"".concat(g," - ").concat(f)})]}),(0,r.jsx)(s.$,{variant:"ghost",size:"icon",onClick:o,className:"h-7 w-7 bg-transparent p-0",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]})})}var f=a(59434);function y(e){let{currentMonth:t,onSelectMonth:a}=e,n=t.getMonth();return(0,r.jsx)("div",{className:"p-3",children:(0,r.jsx)("div",{className:"grid grid-cols-3 gap-2",children:p.map((e,t)=>(0,r.jsx)(s.$,{variant:"outline",className:(0,f.cn)("h-10 w-full font-normal hover:bg-muted",t===n&&"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground"),onClick:()=>a(t),children:e},t))})})}function x(e){let{currentYear:t,minYear:a,maxYear:n,onSelectYear:i}=e,d=Math.max(a,t-6),o=Array.from({length:Math.min(n,d+11)-d+1},(e,t)=>d+t);return(0,r.jsx)("div",{className:"p-3",children:(0,r.jsx)("div",{className:"grid grid-cols-3 gap-2",children:o.map(e=>(0,r.jsx)(s.$,{variant:"outline",className:(0,f.cn)("h-10 w-full font-normal hover:bg-muted",e===t&&"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground"),onClick:()=>i(e),children:e},e))})})}function b(e){let{id:t,value:a,onChange:m,minDate:h,maxDate:p,defaultMonth:f,placeholder:b="选择日期"}=e,[v,w]=n.useState("calendar"),[j,N]=n.useState(()=>{var e;return a||f||(e=new Date,(0,i.e)(e,-18))});return(0,r.jsxs)(u.AM,{children:[(0,r.jsx)(u.Wv,{asChild:!0,children:(0,r.jsxs)(s.$,{variant:"outline",className:"w-full h-9 px-3 text-left font-normal border-gray-200 focus:ring-primary focus:border-primary justify-start hover:bg-gray-50",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4 text-gray-500"}),a?(0,d.GP)(a,"yyyy年MM月dd日"):(0,r.jsx)("span",{className:"text-muted-foreground",children:b})]})}),(0,r.jsx)(u.hl,{className:"w-auto p-0",align:"start",children:(0,r.jsxs)("div",{className:"rounded-md border shadow-md bg-white",children:[(0,r.jsx)(g,{view:v,currentMonth:j,minDate:h,maxDate:p,onPrevious:()=>{let e=new Date(j);"calendar"===v?e.setMonth(e.getMonth()-1):e.setFullYear(e.getFullYear()-1),e.getFullYear()<h.getFullYear()||N(e)},onNext:()=>{let e=new Date(j);"calendar"===v?e.setMonth(e.getMonth()+1):e.setFullYear(e.getFullYear()+1),e.getFullYear()>p.getFullYear()||N(e)},onViewChange:w}),"month"===v?(0,r.jsx)(y,{currentMonth:j,onSelectMonth:e=>{let t=new Date(j);t.setMonth(e),N(t),w("calendar")}}):"year"===v?(0,r.jsx)(x,{currentYear:j.getFullYear(),minYear:h.getFullYear(),maxYear:p.getFullYear(),onSelectYear:e=>{let t=new Date(j);t.setFullYear(e),N(t),w("month")}}):(0,r.jsx)(c.V,{mode:"single",selected:a,onSelect:m,initialFocus:!0,locale:o.g,fromDate:h,toDate:p,month:j,onMonthChange:N,className:"rounded-md",classNames:{caption:"flex justify-center pt-2 relative items-center hidden",nav:"space-x-1 flex items-center hidden",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:"h-9 w-9 p-0 font-normal aria-selected:opacity-100",day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible"}})]})})]},t)}},85057:(e,t,a)=>{a.d(t,{J:()=>s});var r=a(95155),n=a(12115),i=a(40968),d=a(74466),o=a(59434);let l=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),s=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(i.b,{ref:t,className:(0,o.cn)(l(),a),...n})});s.displayName=i.b.displayName},85511:(e,t,a)=>{a.d(t,{V:()=>s});var r=a(95155);a(12115);var n=a(42355),i=a(13052),d=a(20081),o=a(59434),l=a(30285);function s(e){let{className:t,classNames:a,showOutsideDays:s=!0,...c}=e;return(0,r.jsx)(d.hv,{showOutsideDays:s,className:(0,o.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,o.cn)((0,l.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,o.cn)((0,l.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...a},components:{IconLeft:e=>{let{className:t,...a}=e;return(0,r.jsx)(n.A,{className:(0,o.cn)("h-4 w-4",t),...a})},IconRight:e=>{let{className:t,...a}=e;return(0,r.jsx)(i.A,{className:(0,o.cn)("h-4 w-4",t),...a})}},...c})}s.displayName="Calendar"},88539:(e,t,a)=>{a.d(t,{T:()=>d});var r=a(95155),n=a(12115),i=a(59434);let d=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...n})});d.displayName="Textarea"}}]);