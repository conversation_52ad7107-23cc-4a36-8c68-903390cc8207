/*
  Warnings:

  - You are about to drop the column `endTime` on the `student_products` table. All the data in the column will be lost.
  - You are about to drop the column `payment` on the `student_products` table. All the data in the column will be lost.
  - You are about to drop the column `remainingDays` on the `student_products` table. All the data in the column will be lost.
  - You are about to drop the column `startTime` on the `student_products` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `student_products` table. All the data in the column will be lost.
  - You are about to drop the column `unitPrice` on the `student_products` table. All the data in the column will be lost.
  - You are about to alter the column `remainingCount` on the `student_products` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,2)` to `Decimal(10,3)`.
  - You are about to alter the column `amount` on the `student_products` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `Decimal(10,3)`.
  - You are about to drop the `token_blacklist` table. If the table is not empty, all the data it contains will be lost.

*/
-- AlterTable
ALTER TABLE "student_products" DROP COLUMN "endTime",
DROP COLUMN "payment",
DROP COLUMN "remainingDays",
DROP COLUMN "startTime",
DROP COLUMN "status",
DROP COLUMN "unitPrice",
ALTER COLUMN "totalCount" DROP DEFAULT,
ALTER COLUMN "remainingCount" SET DATA TYPE DECIMAL(10,3),
ALTER COLUMN "amount" SET DATA TYPE DECIMAL(10,3);

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "description" TEXT;

-- DropTable
DROP TABLE "token_blacklist";
