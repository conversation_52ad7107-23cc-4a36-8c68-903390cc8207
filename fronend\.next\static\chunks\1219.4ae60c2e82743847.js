(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1219],{13520:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>P});var s=t(95155),l=t(12115),r=t(9110),n=t(46102),i=t(26126),o=t(73069),d=t(55028),c=t(64365),m=t(57141),x=t(63124);let u=(0,d.default)(()=>t.e(1194).then(t.bind(t,1194)),{loadableGenerated:{webpack:()=>[1194]},ssr:!1}),g=(0,d.default)(()=>t.e(3670).then(t.bind(t,33670)),{loadableGenerated:{webpack:()=>[33670]},ssr:!1}),h=e=>e<30?"text-red-600":e<60?"text-amber-600":"text-green-600",b=e=>{let{current:a,total:t,unit:l="",getColorFn:r=h}=e,n=r(t>0?a/t*100:0);return(0,s.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,s.jsx)("span",{className:"font-medium ".concat(n),children:a}),(0,s.jsx)("span",{className:"text-gray-400",children:"/"}),(0,s.jsxs)("span",{className:"text-gray-500",children:[t," ",l&&"(".concat(l,")")]})]})},p=e=>e<7?"text-red-600":e<30?"text-amber-600":"text-green-600",f=[{accessorKey:"name",header:"姓名",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"font-medium text-gray-800",children:a.original.studentName})}},{accessorKey:"phone",header:"手机号码",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"text-gray-600 font-normal",children:a.original.studentPhone})}},{accessorKey:"productName",header:"套餐名称",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"text-gray-700",children:a.original.productName})}},{accessorKey:"packageType",header:"套餐类型",cell:e=>{let{row:a}=e,t=a.original.productPackageType,l=m.lc[t]||m.lc.default;return(0,s.jsx)(i.E,{variant:"outline",className:l.color,children:l.label})}},{accessorKey:"count",header:"剩余/总次数",cell:e=>{let{row:a}=e,t=a.original.totalSessionCount,l=a.original.remainingSessionCount;return(0,s.jsx)(b,{current:l,total:t})}},{accessorKey:"validTime",header:"剩余/有效期",cell:e=>{let{row:a}=e;if("limited-time-and-count"===a.original.productPackageType){let e=a.original.product.timeLimitedUsage,t=a.original.product.timeLimitType;return"daily"===t?(a.original.startDate,a.original.endDate):"monthly"===t&&(a.original.startDate,a.original.endDate),(0,s.jsx)(b,{current:e,total:e,unit:"daily"===t?"天":"月",getColorFn:p})}return(0,s.jsx)(s.Fragment,{children:"-"})}},{accessorKey:"remainingBalance",header:"剩余金额",cell:e=>{let{row:a}=e;return(0,s.jsx)(n.Bc,{children:(0,s.jsxs)(n.m_,{children:[(0,s.jsx)(n.k$,{asChild:!0,children:(0,s.jsx)("div",{className:"text-gray-700 font-medium",children:(0,c.v)(a.getValue("remainingBalance"))})}),(0,s.jsx)(n.ZI,{children:(0,s.jsx)("p",{children:"剩余可退款金额"})})]})})}},{accessorKey:"sessionUnitPrice",header:"课程单价",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"text-gray-700 font-medium",children:(0,c.v)(a.getValue("sessionUnitPrice"))})}},{accessorKey:"remarks",header:"备注",cell:e=>{let{row:a}=e;return(0,s.jsx)(n.Bc,{children:(0,s.jsxs)(n.m_,{children:[(0,s.jsx)(n.k$,{asChild:!0,children:(0,s.jsx)(o.c,{className:"text-gray-600 text-sm max-w-[200px] truncate",children:a.getValue("remarks")})}),(0,s.jsx)(n.ZI,{children:(0,s.jsx)("p",{children:a.getValue("remarks")})})]})})}},{id:"actions",header:"操作",cell:e=>{let{row:a}=e,t=a.original,l=t.studentId;return(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(g,{studentId:l}),(0,s.jsx)(u,{studentProduct:t}),(0,s.jsx)(x.default,{studentId:l,studentProductId:t.id,remainingAmount:t.remainingBalance})]})}}];var j=t(48432),y=t(62523),v=t(85057),N=t(47924),w=t(54416),C=t(91788),k=t(30285),M=t(54165),D=t(80333),T=t(59409),S=t(3925),z=t(95728),A=t(66695);function P(){let[e,a]=(0,l.useState)(1),[t,n]=(0,l.useState)(""),o=function(e,a){let[t,s]=(0,l.useState)(e);return(0,l.useEffect)(()=>{let a=setTimeout(()=>{s(e)},500);return()=>{clearTimeout(a)}},[e,500]),t}(t,500),[d,c]=(0,l.useState)(10),[m,x]=(0,l.useState)(!1),[u,g]=(0,l.useState)(!1),[h,b]=(0,l.useState)(!1),[p,P]=(0,l.useState)({remainingTimes:!1,remainingDays:!1}),[I,R]=(0,l.useState)({remainingTimesMin:1,remainingTimesMax:10,remainingDaysMin:1,remainingDaysMax:5}),[E,L]=(0,l.useState)("all"),_=Object.values(p).filter(Boolean).length;(0,l.useEffect)(()=>{a(1)},[o]);let $=e=>{P(a=>({...a,[e]:!a[e]}))},F=e=>{let{name:a,value:t}=e.target;(""===t||/^\d+$/.test(t))&&R(e=>({...e,[a]:""===t?"":parseInt(t,10)}))},B=()=>{P({remainingTimes:!1,remainingDays:!1}),R({remainingTimesMin:1,remainingTimesMax:10,remainingDaysMin:1,remainingDaysMax:5})},K=(0,l.useMemo)(()=>{let a={page:e,pageSize:d,search:o};return"all"!==E&&(a.status=E),p.remainingTimes&&(a.remainingTimesMin=I.remainingTimesMin,a.remainingTimesMax=I.remainingTimesMax),p.remainingDays&&(a.remainingDaysMin=I.remainingDaysMin,a.remainingDaysMax=I.remainingDaysMax),{...a}},[p,e,d,o,E]),{data:V}=(0,z.og)(K),{data:Z,refetch:G}=(0,z.Iq)(K,{skip:!0}),O=async()=>{try{if(g(!0),await G(),Array.isArray(Z)){let e="".concat(new Date().toLocaleDateString()," 学员产品列表"),a=Z.map(e=>{let a={学员姓名:e.student.name,手机号码:e.student.phone,商品名称:e.product.name,剩余次数:e.remainingCount+"次",状态:"active"===e.status?"正常":"frozen"===e.status?"冻结":"notStarted"===e.status?"未开始":"expired"===e.status?"已过期":"已用完"};return e.remainingDays&&(a["剩余天数"]=e.remainingDays+"天"),a}),t=S.Wp.book_new(),s=S.Wp.json_to_sheet(a);s["!cols"]=[{wch:15},{wch:15},{wch:30},{wch:12},Z.some(e=>e.remainingDays)?{wch:12}:{},{wch:12}],S.Wp.book_append_sheet(t,s,"学员产品列表"),(0,S._h)(t,"".concat(e,".xlsx"))}}catch(e){console.error("导出失败",e)}finally{g(!1)}};return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(A.Zp,{className:"border-slate-200",children:(0,s.jsx)(A.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-end md:justify-between",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[(0,s.jsxs)("div",{className:"relative w-full sm:w-72",children:[(0,s.jsx)(N.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,s.jsx)(y.p,{value:t,onChange:e=>n(e.target.value),placeholder:"搜索学员姓名...",className:"pl-10 bg-slate-50 border-slate-200 focus:bg-white transition-colors"})]}),(0,s.jsxs)(T.l6,{value:E,onValueChange:L,children:[(0,s.jsx)(T.bq,{className:"w-[140px]",children:(0,s.jsx)(T.yv,{placeholder:"课程包状态"})}),(0,s.jsxs)(T.gC,{children:[(0,s.jsx)(T.eb,{value:"all",children:"全部状态"}),(0,s.jsx)(T.eb,{value:"active",children:"正常"}),(0,s.jsx)(T.eb,{value:"frozen",children:"冻结"}),(0,s.jsx)(T.eb,{value:"notStarted",children:"未开始"}),(0,s.jsx)(T.eb,{value:"expired",children:"已过期"}),(0,s.jsx)(T.eb,{value:"completed",children:"已用完"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[_>0&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[p.remainingTimes&&(0,s.jsxs)(i.E,{variant:"outline",className:"flex items-center gap-1 px-3 py-1",children:["剩余次数",I.remainingTimesMin,"~",I.remainingTimesMax,"次内",(0,s.jsx)(w.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>$("remainingTimes")})]}),p.remainingDays&&(0,s.jsxs)(i.E,{variant:"outline",className:"flex items-center gap-1 px-3 py-1",children:["剩余天数",I.remainingDaysMin,"~",I.remainingDaysMax,"天内",(0,s.jsx)(w.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>$("remainingDays")})]}),(0,s.jsx)(k.$,{variant:"ghost",size:"sm",onClick:B,className:"h-8 px-2",children:"清除全部"})]}),(0,s.jsxs)(M.lG,{open:h,onOpenChange:b,children:[(0,s.jsx)(M.zM,{asChild:!0,children:(0,s.jsxs)(k.$,{variant:"ghost",size:"sm",className:"flex items-center gap-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-500 hover:text-primary",children:["高级筛选",_>0&&(0,s.jsx)(i.E,{className:"ml-2 bg-white text-primary hover:bg-gray-200",children:_})]})}),(0,s.jsxs)(M.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsx)(M.c7,{children:(0,s.jsx)(M.L3,{children:"高级筛选"})}),(0,s.jsxs)("div",{className:"py-4 space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(v.J,{htmlFor:"remainingTimes",className:"flex-1",children:"剩余次数筛选"}),(0,s.jsx)(D.d,{id:"remainingTimes",checked:p.remainingTimes,onCheckedChange:()=>$("remainingTimes")})]}),p.remainingTimes&&(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-2 pl-4",children:[(0,s.jsx)(y.p,{type:"text",name:"remainingTimesMin",value:I.remainingTimesMin,onChange:F,className:"w-20"}),(0,s.jsx)("span",{children:"至"}),(0,s.jsx)(y.p,{type:"text",name:"remainingTimesMax",value:I.remainingTimesMax,onChange:F,className:"w-20"}),(0,s.jsx)("span",{children:"次"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(v.J,{htmlFor:"remainingDays",className:"flex-1",children:"剩余天数筛选"}),(0,s.jsx)(D.d,{id:"remainingDays",checked:p.remainingDays,onCheckedChange:()=>$("remainingDays")})]}),p.remainingDays&&(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-2 pl-4",children:[(0,s.jsx)(y.p,{type:"text",name:"remainingDaysMin",value:I.remainingDaysMin,onChange:F,className:"w-20"}),(0,s.jsx)("span",{children:"至"}),(0,s.jsx)(y.p,{type:"text",name:"remainingDaysMax",value:I.remainingDaysMax,onChange:F,className:"w-20"}),(0,s.jsx)("span",{children:"天"})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,s.jsx)(k.$,{variant:"outline",onClick:B,children:"重置"}),(0,s.jsx)(M.HM,{asChild:!0,children:(0,s.jsx)(k.$,{children:"确认"})})]})]})]}),(0,s.jsxs)(k.$,{variant:"outline",onClick:O,disabled:u,className:"flex items-center gap-1",children:[(0,s.jsx)(C.A,{className:"h-4 w-4"}),u?"导出中...":"导出"]})]})]})})}),(0,s.jsx)(r.b,{columns:f,data:(null==V?void 0:V.list)||[],pagination:!1,loading:m,className:"border-collapse"},"studentProductTable"),(0,s.jsx)(j.default,{currentPage:(null==V?void 0:V.page)||1,totalItems:(null==V?void 0:V.total)||0,pageSize:(null==V?void 0:V.pageSize)||10,onPageChange:a,onPageSizeChange:c})]})}},14636:(e,a,t)=>{"use strict";t.d(a,{AM:()=>i,Wv:()=>o,hl:()=>d});var s=t(95155),l=t(12115),r=t(20547),n=t(59434);let i=r.bL,o=r.l9,d=l.forwardRef((e,a)=>{let{className:t,align:l="center",sideOffset:i=4,...o}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{ref:a,align:l,sideOffset:i,className:(0,n.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...o})})});d.displayName=r.UC.displayName},32383:()=>{},48432:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>f});var s=t(95155),l=t(12115),r=t(42355),n=t(13052),i=t(5623),o=t(59434),d=t(30285);let c=e=>{let{className:a,...t}=e;return(0,s.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,o.cn)("mx-auto flex w-full justify-center",a),...t})};c.displayName="Pagination";let m=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("ul",{ref:a,className:(0,o.cn)("flex flex-row items-center gap-1",t),...l})});m.displayName="PaginationContent";let x=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("li",{ref:a,className:(0,o.cn)("",t),...l})});x.displayName="PaginationItem";let u=e=>{let{className:a,isActive:t,size:l="icon",...r}=e;return(0,s.jsx)("a",{"aria-current":t?"page":void 0,className:(0,o.cn)((0,d.r)({variant:t?"outline":"ghost",size:l}),a),...r})};u.displayName="PaginationLink";let g=e=>{let{className:a,...t}=e;return(0,s.jsxs)(u,{"aria-label":"Go to previous page",size:"default",className:(0,o.cn)("gap-1 pl-2.5",a),...t,children:[(0,s.jsx)(r.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"上一页"})]})};g.displayName="PaginationPrevious";let h=e=>{let{className:a,...t}=e;return(0,s.jsxs)(u,{"aria-label":"Go to next page",size:"default",className:(0,o.cn)("gap-1 pr-2.5",a),...t,children:[(0,s.jsx)("span",{children:"下一页"}),(0,s.jsx)(n.A,{className:"h-4 w-4"})]})};h.displayName="PaginationNext";let b=e=>{let{className:a,...t}=e;return(0,s.jsxs)("span",{"aria-hidden":!0,className:(0,o.cn)("flex h-9 w-9 items-center justify-center",a),...t,children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"更多页"})]})};b.displayName="PaginationEllipsis";var p=t(59409);function f(e){let{currentPage:a,pageSize:t,totalItems:l,onPageChange:i,onPageSizeChange:o}=e,d=Math.ceil(l/t),f=(()=>{let e=[];if(d<=5){for(let a=1;a<=d;a++)e.push(a);return e}e.push(1);let t=Math.max(2,a-1),s=Math.min(a+1,d-1);2===t&&(s=Math.min(t+2,d-1)),s===d-1&&(t=Math.max(s-2,2)),t>2&&e.push("ellipsis-start");for(let a=t;a<=s;a++)e.push(a);return s<d-1&&e.push("ellipsis-end"),d>1&&e.push(d),e})(),j=0===l?0:(a-1)*t+1,y=Math.min(a*t,l);return(0,s.jsxs)("div",{className:"flex flex-col gap-4 px-2 py-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"显示"}),(0,s.jsxs)(p.l6,{value:t.toString(),onValueChange:e=>{o(Number(e))},children:[(0,s.jsx)(p.bq,{className:"w-[4.5rem] h-7 text-xs border-border/60 hover:bg-accent/30 transition-colors",children:(0,s.jsx)(p.yv,{})}),(0,s.jsx)(p.gC,{children:[10,20,30,50].map(e=>(0,s.jsx)(p.eb,{value:e.toString(),className:"text-xs",children:e},e))})]}),(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"条"}),(0,s.jsx)("span",{className:"text-muted-foreground/40 mx-1.5",children:"|"}),l>0?(0,s.jsxs)("span",{className:"text-muted-foreground/80",children:[j,"-",y," / ",l," 条记录"]}):(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"0 条记录"})]}),(0,s.jsx)(c,{children:(0,s.jsxs)(m,{className:"gap-1",children:[(0,s.jsx)(x,{children:(0,s.jsx)(g,{onClick:()=>i(Math.max(1,a-1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(1===a?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,s.jsx)(r.A,{className:"h-4 w-4 mr-1"})})}),f.map((e,t)=>"ellipsis-start"===e||"ellipsis-end"===e?(0,s.jsx)(x,{children:(0,s.jsx)(b,{className:"h-8 w-8 flex items-center justify-center text-xs"})},"ellipsis-".concat(t)):(0,s.jsx)(x,{children:(0,s.jsx)(u,{onClick:()=>i(e),isActive:a===e,className:"h-8 w-8 text-xs font-medium rounded-md transition-colors data-[active]:bg-primary data-[active]:text-primary-foreground hover:bg-accent/50 hover:text-accent-foreground/90","aria-label":"前往第 ".concat(e," 页"),children:e})},e)),(0,s.jsx)(x,{children:(0,s.jsx)(h,{onClick:()=>i(Math.min(d,a+1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(a===d||0===d?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,s.jsx)(n.A,{className:"h-4 w-4 ml-1"})})})]})})]})}},54165:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>u,Es:()=>h,HM:()=>m,L3:()=>b,c7:()=>g,lG:()=>o,rr:()=>p,zM:()=>d});var s=t(95155),l=t(12115),r=t(15452),n=t(54416),i=t(59434);let o=r.bL,d=r.l9,c=r.ZL,m=r.bm,x=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.hJ,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...l})});x.displayName=r.hJ.displayName;let u=l.forwardRef((e,a)=>{let{className:t,children:l,...o}=e;return(0,s.jsxs)(c,{children:[(0,s.jsx)(x,{}),(0,s.jsxs)(r.UC,{ref:a,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...o,children:[l,(0,s.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=r.UC.displayName;let g=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...t})};g.displayName="DialogHeader";let h=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};h.displayName="DialogFooter";let b=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",t),...l})});b.displayName=r.hE.displayName;let p=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",t),...l})});p.displayName=r.VY.displayName},57001:(e,a,t)=>{"use strict";t.d(a,{p:()=>n});var s=t(95155),l=t(30285),r=t(46102);function n(e){let{icon:a,tooltipText:t,tooltipSide:n="top",tooltipAlign:i="center",delayDuration:o=300,variant:d="ghost",size:c="icon",className:m="h-8 w-8 hover:bg-muted",...x}=e;return(0,s.jsx)(r.Bc,{delayDuration:o,children:(0,s.jsxs)(r.m_,{children:[(0,s.jsx)(r.k$,{asChild:!0,children:(0,s.jsx)(l.$,{variant:d,size:c,className:m,...x,children:(0,s.jsx)(a,{className:"h-4 w-4 text-muted-foreground"})})}),(0,s.jsx)(r.ZI,{side:n,align:i,className:"font-medium text-xs px-3 py-1.5",children:(0,s.jsx)("p",{children:t})})]})})}},57141:(e,a,t)=>{"use strict";t.d(a,{C9:()=>l,DT:()=>i,I2:()=>n,IC:()=>x,N4:()=>c,fb:()=>u,lc:()=>s,oD:()=>o,u7:()=>m,uq:()=>r,x9:()=>d});let s={"limited-sessions":{label:"限次",color:"capitalize font-normal bg-blue-50 text-blue-700 hover:bg-blue-100 px-2.5 py-0.5 rounded-md"},"limited-time-and-count":{label:"时次限",color:"capitalize font-normal bg-violet-50 text-violet-700 hover:bg-violet-100 px-2.5 py-0.5 rounded-md"},default:{label:"未知套餐",color:"capitalize font-normal bg-slate-100 text-slate-700 hover:bg-slate-200 px-2.5 py-0.5 rounded-md"}},l={cash:{label:"现金",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},alipay:{label:"支付宝",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},wechat:{label:"微信",color:"bg-green-100 text-green-800 hover:bg-green-200"},card:{label:"银行卡",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},other:{label:"其他",color:"bg-slate-100 text-slate-800 hover:bg-slate-200"}},r=[{id:"wechat",label:"微信"},{id:"alipay",label:"支付宝"},{id:"card",label:"银行转账"},{id:"cash",label:"现金"},{id:"other",label:"其他"}],n={done:{label:"完成",color:"bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400"},arrears:{label:"欠费",color:"bg-amber-100 text-amber-800 hover:bg-amber-200 dark:bg-amber-900/30 dark:text-amber-400"},refunded:{label:"退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400"},default:{label:"未知",color:"bg-slate-100 text-slate-800 hover:bg-slate-200 dark:bg-slate-800/50 dark:text-slate-400"}},i={active:{badgeClass:"bg-emerald-50 text-emerald-700 hover:bg-emerald-100 border-emerald-200",dotClass:"bg-emerald-500",text:"正常"},expired:{badgeClass:"bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200",dotClass:"bg-amber-500",text:"已到期"},refunded:{badgeClass:"bg-red-50 text-red-700 hover:bg-red-100 border-red-200",dotClass:"bg-red-500",text:"已退款"},frozen:{badgeClass:"bg-slate-50 text-slate-700 hover:bg-slate-100 border-slate-200",dotClass:"bg-slate-500",text:"已冻结"},default:{badgeClass:"bg-slate-100 text-slate-700 hover:bg-slate-200 border-slate-200",dotClass:"bg-slate-400",text:"未知状态"}},o={all:{className:"bg-slate-100 text-slate-800 hover:bg-slate-200",text:"全部状态"},attendance:{className:"bg-green-100 text-green-800 hover:bg-green-200",text:"已考勤"},leave:{className:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200",text:"请假"},unattended:{className:"bg-orange-100 text-orange-800 hover:bg-orange-200",text:"未考勤"},default:{className:"bg-red-100 text-red-800 hover:bg-red-200",text:"缺勤"}},d={A:{label:"非常感兴趣",color:"bg-green-100 text-green-800 hover:bg-green-200"},B:{label:"比较感兴趣",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},C:{label:"一般意向",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},D:{label:"了解意向",color:"bg-orange-100 text-orange-800 hover:bg-orange-200"},E:{label:"不感兴趣",color:"bg-red-100 text-red-800 hover:bg-red-200"},default:{label:"未知",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},c={productSales:{label:"产品销售",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},courseSales:{label:"课程销售",color:"bg-green-100 text-green-800 hover:bg-green-200"},OverduePaymentCollection:{label:"逾期款项催收",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},lifestyleAndOffice:{label:"生活和办公",color:"bg-red-100 text-red-800 hover:bg-red-200"},socialAndCatering:{label:"社交和餐饮",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},refunded:{label:"退款",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"其他",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},m={completed:{label:"已完成",color:"bg-green-100 text-green-800 hover:bg-green-200"},pending:{label:"待审核",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},refunded:{label:"已退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},failed:{label:"审核未通过",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"未知状态",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},x={formal:{label:"正式学员",color:"text-blue-500"},graduated:{label:"历史学员",color:"text-green-500"},public:{label:"公海池",color:"text-red-500"},intent:{label:"意向学员",color:"text-yellow-500"}},u={secret:{label:"保密",color:"text-gray-500"},male:{label:"男",color:"text-blue-500"},female:{label:"女",color:"text-pink-500"}}},62523:(e,a,t)=>{"use strict";t.d(a,{p:()=>n});var s=t(95155),l=t(12115),r=t(59434);let n=l.forwardRef((e,a)=>{let{className:t,type:l,...n}=e;return(0,s.jsx)("input",{type:l,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...n})});n.displayName="Input"},63124:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>d});var s=t(95155),l=t(12115),r=t(84109),n=t(55028),i=t(57001);let o=(0,n.default)(()=>t.e(7045).then(t.bind(t,7045)),{loadableGenerated:{webpack:()=>[7045]},ssr:!1}),d=function(e){let{studentId:a,studentProductId:t,remainingAmount:n}=e,[d,c]=(0,l.useState)(!1);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.p,{icon:r.A,tooltipText:"退款",onClick:()=>c(!0)}),d&&(0,s.jsx)(o,{open:d,onOpenChange:c,studentId:a,studentProductId:t,remainingAmount:n})]})}},64365:(e,a,t)=>{"use strict";t.d(a,{v:()=>s});let s=e=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY",minimumFractionDigits:2}).format(e)},66695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>m});var s=t(95155),l=t(12115),r=t(59434);let n=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("div",{ref:a,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...l})});n.displayName="Card";let i=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("div",{ref:a,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",t),...l})});i.displayName="CardHeader";let o=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("div",{ref:a,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",t),...l})});o.displayName="CardTitle";let d=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("div",{ref:a,className:(0,r.cn)("text-sm text-muted-foreground",t),...l})});d.displayName="CardDescription";let c=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("div",{ref:a,className:(0,r.cn)("p-6 pt-0",t),...l})});c.displayName="CardContent";let m=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("div",{ref:a,className:(0,r.cn)("flex items-center p-6 pt-0",t),...l})});m.displayName="CardFooter"},73069:(e,a,t)=>{"use strict";t.d(a,{c:()=>x});var s=t(95155),l=t(12115),r=t(47863),n=t(66474),i=t(5196),o=t(24357),d=t(59434),c=t(14636),m=t(30285);function x(e){let{children:a,maxDisplayLength:t=15,className:x,popoverWidth:u="auto",showBorder:g=!1}=e,[h,b]=l.useState(!1),[p,f]=l.useState(!1),j=l.useMemo(()=>{if("string"==typeof a||"number"==typeof a)return a.toString();try{var e;let t=document.createElement("div");return t.innerHTML=(null==a?void 0:null===(e=a.props)||void 0===e?void 0:e.children)||"",t.textContent||""}catch(e){return console.warn("Could not extract text from children:",e),""}},[a]),y=l.useMemo(()=>{if("string"==typeof a||"number"==typeof a){let e=a.toString();return e.length>t?e.slice(0,t):e}return a},[a,t]),v=async()=>{try{await navigator.clipboard.writeText(j),f(!0),setTimeout(()=>f(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,s.jsxs)(c.AM,{open:h,onOpenChange:b,children:[(0,s.jsx)(c.Wv,{asChild:!0,children:(0,s.jsxs)(m.$,{variant:g?"outline":"ghost",role:"combobox","aria-expanded":h,className:(0,d.cn)("w-fit justify-between font-normal px-2 py-1 h-auto",!g&&"border-0 shadow-none",x),children:[(0,s.jsx)("span",{className:"mr-2 truncate",children:y}),h?(0,s.jsx)(r.A,{className:"h-4 w-4 shrink-0 opacity-50"}):(0,s.jsx)(n.A,{className:"h-4 w-4 shrink-0 opacity-50"})]})}),(0,s.jsx)(c.hl,{className:"p-0",align:"start",style:{width:u},children:(0,s.jsxs)("div",{className:"flex items-center gap-2 p-2",children:[(0,s.jsx)("span",{className:"text-sm break-all",children:j}),(0,s.jsxs)(m.$,{variant:"ghost",size:"icon",className:"h-8 w-8 shrink-0",onClick:v,children:[p?(0,s.jsx)(i.A,{className:"h-4 w-4"}):(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:p?"Copied":"Copy text"})]})]})})]})}},80333:(e,a,t)=>{"use strict";t.d(a,{d:()=>i});var s=t(95155),l=t(12115),r=t(4884),n=t(59434);let i=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.bL,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",t),...l,ref:a,children:(0,s.jsx)(r.zi,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});i.displayName=r.bL.displayName},83686:()=>{},85057:(e,a,t)=>{"use strict";t.d(a,{J:()=>d});var s=t(95155),l=t(12115),r=t(40968),n=t(74466),i=t(59434);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.b,{ref:a,className:(0,i.cn)(o(),t),...l})});d.displayName=r.b.displayName}}]);