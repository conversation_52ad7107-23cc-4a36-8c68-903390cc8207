"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[312,7649],{15452:(e,t,r)=>{r.d(t,{G$:()=>$,Hs:()=>N,UC:()=>et,VY:()=>ea,ZL:()=>Q,bL:()=>X,bm:()=>en,hE:()=>er,hJ:()=>ee,l9:()=>K});var a=r(12115),n=r(85185),s=r(6101),o=r(46081),l=r(61285),i=r(5845),d=r(19178),c=r(25519),u=r(34378),f=r(28905),p=r(63655),m=r(92293),x=r(93795),g=r(38168),v=r(99708),h=r(95155),y="Dialog",[j,N]=(0,o.A)(y),[b,D]=j(y),w=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:s,onOpenChange:o,modal:d=!0}=e,c=a.useRef(null),u=a.useRef(null),[f=!1,p]=(0,i.i)({prop:n,defaultProp:s,onChange:o});return(0,h.jsx)(b,{scope:t,triggerRef:c,contentRef:u,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:a.useCallback(()=>p(e=>!e),[p]),modal:d,children:r})};w.displayName=y;var R="DialogTrigger",C=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,o=D(R,r),l=(0,s.s)(t,o.triggerRef);return(0,h.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":M(o.open),...a,ref:l,onClick:(0,n.m)(e.onClick,o.onOpenToggle)})});C.displayName=R;var A="DialogPortal",[I,E]=j(A,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:s}=e,o=D(A,t);return(0,h.jsx)(I,{scope:t,forceMount:r,children:a.Children.map(n,e=>(0,h.jsx)(f.C,{present:r||o.open,children:(0,h.jsx)(u.Z,{asChild:!0,container:s,children:e})}))})};O.displayName=A;var k="DialogOverlay",F=a.forwardRef((e,t)=>{let r=E(k,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,s=D(k,e.__scopeDialog);return s.modal?(0,h.jsx)(f.C,{present:a||s.open,children:(0,h.jsx)(_,{...n,ref:t})}):null});F.displayName=k;var _=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=D(k,r);return(0,h.jsx)(x.A,{as:v.DX,allowPinchZoom:!0,shards:[n.contentRef],children:(0,h.jsx)(p.sG.div,{"data-state":M(n.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),Z="DialogContent",P=a.forwardRef((e,t)=>{let r=E(Z,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,s=D(Z,e.__scopeDialog);return(0,h.jsx)(f.C,{present:a||s.open,children:s.modal?(0,h.jsx)(L,{...n,ref:t}):(0,h.jsx)(T,{...n,ref:t})})});P.displayName=Z;var L=a.forwardRef((e,t)=>{let r=D(Z,e.__scopeDialog),o=a.useRef(null),l=(0,s.s)(t,r.contentRef,o);return a.useEffect(()=>{let e=o.current;if(e)return(0,g.Eq)(e)},[]),(0,h.jsx)(z,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=a.forwardRef((e,t)=>{let r=D(Z,e.__scopeDialog),n=a.useRef(!1),s=a.useRef(!1);return(0,h.jsx)(z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,o;null===(a=e.onCloseAutoFocus)||void 0===a||a.call(e,t),t.defaultPrevented||(n.current||null===(o=r.triggerRef.current)||void 0===o||o.focus(),t.preventDefault()),n.current=!1,s.current=!1},onInteractOutside:t=>{var a,o;null===(a=e.onInteractOutside)||void 0===a||a.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(s.current=!0));let l=t.target;(null===(o=r.triggerRef.current)||void 0===o?void 0:o.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&s.current&&t.preventDefault()}})}),z=a.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:l,...i}=e,u=D(Z,r),f=a.useRef(null),p=(0,s.s)(t,f);return(0,m.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:l,children:(0,h.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":M(u.open),...i,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(S,{titleId:u.titleId}),(0,h.jsx)(W,{contentRef:f,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",G=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=D(B,r);return(0,h.jsx)(p.sG.h2,{id:n.titleId,...a,ref:t})});G.displayName=B;var U="DialogDescription",V=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=D(U,r);return(0,h.jsx)(p.sG.p,{id:n.descriptionId,...a,ref:t})});V.displayName=U;var q="DialogClose",H=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,s=D(q,r);return(0,h.jsx)(p.sG.button,{type:"button",...a,ref:t,onClick:(0,n.m)(e.onClick,()=>s.onOpenChange(!1))})});function M(e){return e?"open":"closed"}H.displayName=q;var Y="DialogTitleWarning",[$,J]=(0,o.q)(Y,{contentName:Z,titleName:B,docsSlug:"dialog"}),S=e=>{let{titleId:t}=e,r=J(Y),n="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return a.useEffect(()=>{t&&!document.getElementById(t)&&console.error(n)},[n,t]),null},W=e=>{let{contentRef:t,descriptionId:r}=e,n=J("DialogDescriptionWarning"),s="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return a.useEffect(()=>{var e;let a=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&a&&!document.getElementById(r)&&console.warn(s)},[s,t,r]),null},X=w,K=C,Q=O,ee=F,et=P,er=G,ea=V,en=H},17649:(e,t,r)=>{r.d(t,{UC:()=>Z,VY:()=>z,ZD:()=>L,ZL:()=>F,bL:()=>O,hE:()=>T,hJ:()=>_,l9:()=>k,rc:()=>P});var a=r(12115),n=r(46081),s=r(6101),o=r(15452),l=r(85185),i=r(99708),d=r(95155),c="AlertDialog",[u,f]=(0,n.A)(c,[o.Hs]),p=(0,o.Hs)(),m=e=>{let{__scopeAlertDialog:t,...r}=e,a=p(t);return(0,d.jsx)(o.bL,{...a,...r,modal:!0})};m.displayName=c;var x=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(o.l9,{...n,...a,ref:t})});x.displayName="AlertDialogTrigger";var g=e=>{let{__scopeAlertDialog:t,...r}=e,a=p(t);return(0,d.jsx)(o.ZL,{...a,...r})};g.displayName="AlertDialogPortal";var v=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(o.hJ,{...n,...a,ref:t})});v.displayName="AlertDialogOverlay";var h="AlertDialogContent",[y,j]=u(h),N=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...c}=e,u=p(r),f=a.useRef(null),m=(0,s.s)(t,f),x=a.useRef(null);return(0,d.jsx)(o.G$,{contentName:h,titleName:b,docsSlug:"alert-dialog",children:(0,d.jsx)(y,{scope:r,cancelRef:x,children:(0,d.jsxs)(o.UC,{role:"alertdialog",...u,...c,ref:m,onOpenAutoFocus:(0,l.m)(c.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=x.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(i.xV,{children:n}),(0,d.jsx)(E,{contentRef:f})]})})})});N.displayName=h;var b="AlertDialogTitle",D=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(o.hE,{...n,...a,ref:t})});D.displayName=b;var w="AlertDialogDescription",R=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(o.VY,{...n,...a,ref:t})});R.displayName=w;var C=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(o.bm,{...n,...a,ref:t})});C.displayName="AlertDialogAction";var A="AlertDialogCancel",I=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,{cancelRef:n}=j(A,r),l=p(r),i=(0,s.s)(t,n);return(0,d.jsx)(o.bm,{...l,...a,ref:i})});I.displayName=A;var E=e=>{let{contentRef:t}=e,r="`".concat(h,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(h,"` by passing a `").concat(w,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(h,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return a.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},O=m,k=x,F=g,_=v,Z=N,P=C,L=I,T=D,z=R},70312:(e,t,r)=>{r.r(t),r.d(t,{default:()=>c});var a=r(95155);r(12115);var n=r(90010),s=r(46102),o=r(30285);let l=(0,r(19946).A)("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);var i=r(48436),d=r(13515);let c=function(e){let{staff:t}=e,[r]=(0,d.T7)();async function c(e){try{await r(e.id),i.l.success("操作成功")}catch(e){i.l.error((null==e?void 0:e.message)||"操作失败")}}return(0,a.jsx)(s.Bc,{delayDuration:300,children:(0,a.jsxs)(s.m_,{children:[(0,a.jsxs)(n.Lt,{children:[(0,a.jsx)(n.tv,{asChild:!0,children:(0,a.jsx)(s.k$,{asChild:!0,children:(0,a.jsx)(o.$,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",children:(0,a.jsx)(l,{className:"h-4 w-4 text-muted-foreground"})})})}),(0,a.jsxs)(n.EO,{children:[(0,a.jsxs)(n.wd,{children:[(0,a.jsxs)(n.r7,{className:"text-lg font-semibold",children:[t.userInstitutions[0].status?"离职":"恢复","员工"]}),(0,a.jsxs)(n.$v,{className:"mt-3 text-muted-foreground",children:["您确定要",t.userInstitutions[0].status?"将":"恢复",(0,a.jsx)("b",{children:t.name}),t.userInstitutions[0].status?"设置为离职":"为在职","状态吗？"]})]}),(0,a.jsxs)(n.ck,{children:[(0,a.jsx)(n.Zr,{children:"取消"}),(0,a.jsx)(n.Rx,{onClick:()=>c(t),children:"确认"})]})]})]}),(0,a.jsx)(s.ZI,{side:"top",className:"font-medium text-xs px-3 py-1.5",children:(0,a.jsxs)("p",{children:[t.userInstitutions[0].status?"离职":"恢复","员工"]})})]})})}},90010:(e,t,r)=>{r.d(t,{$v:()=>g,EO:()=>f,Lt:()=>i,Rx:()=>v,Zr:()=>h,ck:()=>m,r7:()=>x,tv:()=>d,wd:()=>p});var a=r(95155),n=r(12115),s=r(17649),o=r(59434),l=r(30285);let i=s.bL,d=s.l9,c=s.ZL,u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.hJ,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...n,ref:t})});u.displayName=s.hJ.displayName;let f=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(u,{}),(0,a.jsx)(s.UC,{ref:t,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...n})]})});f.displayName=s.UC.displayName;let p=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-2 text-center sm:text-left",t),...r})};p.displayName="AlertDialogHeader";let m=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...r})};m.displayName="AlertDialogFooter";let x=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.hE,{ref:t,className:(0,o.cn)("text-lg font-semibold",r),...n})});x.displayName=s.hE.displayName;let g=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.VY,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...n})});g.displayName=s.VY.displayName;let v=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.rc,{ref:t,className:(0,o.cn)((0,l.r)(),r),...n})});v.displayName=s.rc.displayName;let h=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.ZD,{ref:t,className:(0,o.cn)((0,l.r)({variant:"outline"}),"mt-2 sm:mt-0",r),...n})});h.displayName=s.ZD.displayName}}]);