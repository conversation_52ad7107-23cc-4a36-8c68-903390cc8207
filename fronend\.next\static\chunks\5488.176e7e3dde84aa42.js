"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5488],{15452:(e,t,r)=>{r.d(t,{G$:()=>X,Hs:()=>x,UC:()=>et,VY:()=>en,ZL:()=>Q,bL:()=>$,bm:()=>eo,hE:()=>er,hJ:()=>ee,l9:()=>z});var n=r(12115),o=r(85185),a=r(6101),l=r(46081),i=r(61285),s=r(5845),d=r(19178),u=r(25519),c=r(34378),f=r(28905),p=r(63655),g=r(92293),v=r(93795),m=r(38168),h=r(99708),y=r(95155),w="Dialog",[b,x]=(0,l.A)(w),[D,j]=b(w),C=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[f=!1,p]=(0,s.i)({prop:o,defaultProp:a,onChange:l});return(0,y.jsx)(D,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:d,children:r})};C.displayName=w;var R="DialogTrigger",I=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=j(R,r),i=(0,a.s)(t,l.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":V(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});I.displayName=R;var N="DialogPortal",[A,_]=b(N,{forceMount:void 0}),k=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=j(N,t);return(0,y.jsx)(A,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,y.jsx)(f.C,{present:r||l.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};k.displayName=N;var E="DialogOverlay",O=n.forwardRef((e,t)=>{let r=_(E,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=j(E,e.__scopeDialog);return a.modal?(0,y.jsx)(f.C,{present:n||a.open,children:(0,y.jsx)(F,{...o,ref:t})}):null});O.displayName=E;var F=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(E,r);return(0,y.jsx)(v.A,{as:h.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":V(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",G=n.forwardRef((e,t)=>{let r=_(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=j(P,e.__scopeDialog);return(0,y.jsx)(f.C,{present:n||a.open,children:a.modal?(0,y.jsx)(M,{...o,ref:t}):(0,y.jsx)(S,{...o,ref:t})})});G.displayName=P;var M=n.forwardRef((e,t)=>{let r=j(P,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,m.Eq)(e)},[]),(0,y.jsx)(L,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),S=n.forwardRef((e,t)=>{let r=j(P,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,y.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(l=r.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let i=t.target;(null===(l=r.triggerRef.current)||void 0===l?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),L=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=j(P,r),f=n.useRef(null),p=(0,a.s)(t,f);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,y.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":V(c.open),...s,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(K,{titleId:c.titleId}),(0,y.jsx)(Y,{contentRef:f,descriptionId:c.descriptionId})]})]})}),T="DialogTitle",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(T,r);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...n,ref:t})});B.displayName=T;var q="DialogDescription",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(q,r);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:t})});H.displayName=q;var W="DialogClose",U=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(W,r);return(0,y.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function V(e){return e?"open":"closed"}U.displayName=W;var Z="DialogTitleWarning",[X,J]=(0,l.q)(Z,{contentName:P,titleName:T,docsSlug:"dialog"}),K=e=>{let{titleId:t}=e,r=J(Z),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},Y=e=>{let{contentRef:t,descriptionId:r}=e,o=J("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(a)},[a,t,r]),null},$=C,z=I,Q=k,ee=O,et=G,er=B,en=H,eo=U},29869:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>i});var n=r(12115),o=r(63655),a=r(95155),l=n.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},54416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},85977:(e,t,r)=>{r.d(t,{H4:()=>x,_V:()=>b,bL:()=>w});var n=r(12115),o=r(46081),a=r(39033),l=r(52712),i=r(63655),s=r(95155),d="Avatar",[u,c]=(0,o.A)(d),[f,p]=u(d),g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[a,l]=n.useState("idle");return(0,s.jsx)(f,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,s.jsx)(i.sG.span,{...o,ref:t})})});g.displayName=d;var v="AvatarImage",m=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:d=()=>{},...u}=e,c=p(v,r),f=function(e,t){let[r,o]=n.useState("idle");return(0,l.N)(()=>{if(!e){o("error");return}let r=!0,n=new window.Image,a=e=>()=>{r&&o(e)};return o("loading"),n.onload=a("loaded"),n.onerror=a("error"),n.src=e,t&&(n.referrerPolicy=t),()=>{r=!1}},[e,t]),r}(o,u.referrerPolicy),g=(0,a.c)(e=>{d(e),c.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==f&&g(f)},[f,g]),"loaded"===f?(0,s.jsx)(i.sG.img,{...u,ref:t,src:o}):null});m.displayName=v;var h="AvatarFallback",y=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...a}=e,l=p(h,r),[d,u]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>u(!0),o);return()=>window.clearTimeout(e)}},[o]),d&&"loaded"!==l.imageLoadingStatus?(0,s.jsx)(i.sG.span,{...a,ref:t}):null});y.displayName=h;var w=g,b=m,x=y}}]);