
-- 为管理员用户分配角色
INSERT INTO "user_roles" ("id", "userId", "roleId", "createdAt", "updatedAt")
VALUES (
    'ur_admin_' || MD5(RANDOM()::TEXT),
    'cm7k94j3e0001fwtw7z05ehvj',  -- 管理员用户ID
    'role_admin',                  -- 管理员角色ID
    NOW(),
    NOW()
);

-- 验证分配是否成功
SELECT u.account, r.name as role_name
FROM "users" u
JOIN "user_roles" ur ON u.id = ur."userId"
JOIN "roles" r ON ur."roleId" = r.id
WHERE u.id = 'cm7k94j3e0001fwtw7z05ehvj';
