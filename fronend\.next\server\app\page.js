(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,r)=>{let{createProxy:a}=r(39844);e.exports=a("F:\\trae\\cardmees\\fronend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17720:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26373:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(61120);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:d="",children:n,iconNode:o,...x},c)=>(0,a.createElement)("svg",{ref:c,...l,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:i("lucide",d),...x},[...o.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(n)?n:[n]])),n=(e,t)=>{let r=(0,a.forwardRef)(({className:r,...l},n)=>(0,a.createElement)(d,{ref:n,iconNode:t,className:i(`lucide-${s(e)}`,r),...l}));return r.displayName=`${e}`,r}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32127:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(26373).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},33873:e=>{"use strict";e.exports=require("path")},40918:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(26373).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41382:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(26373).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},41949:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>c,pages:()=>x,routeModule:()=>m,tree:()=>o});var a=r(65239),s=r(48088),i=r(88170),l=r.n(i),d=r(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);r.d(t,n);let o=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85561)),"F:\\trae\\cardmees\\fronend\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}],x=["F:\\trae\\cardmees\\fronend\\src\\app\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},52568:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23))},53148:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(26373).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65766:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(26373).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},72845:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(26373).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},74075:e=>{"use strict";e.exports=require("zlib")},75234:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(26373).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85561:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(37413);r(61120);var s=r(4536),i=r.n(s),l=r(75234),d=r(26373);let n=(0,d.A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),o=(0,d.A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);var x=r(72845),c=r(41382),m=r(40918),h=r(65766),g=r(53148),u=r(32127),p=r(90230);let b=function(){return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900 transition-colors duration-300",children:[(0,a.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50 backdrop-blur-sm bg-opacity-90 dark:bg-opacity-90",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.A,{className:"h-8 w-8 text-indigo-600 dark:text-indigo-400"}),(0,a.jsxs)("span",{className:"text-xl font-bold text-gray-800 dark:text-white",children:[(0,a.jsx)("span",{className:"text-indigo-600 dark:text-indigo-400",children:"思"}),"学"]})]})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-1",children:[(0,a.jsx)(i(),{href:"/",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200",children:"首页"}),(0,a.jsx)(i(),{href:"/dashboard",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200",children:"控制台"}),(0,a.jsx)(i(),{href:"/academic",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200",children:"学术管理"}),(0,a.jsx)(i(),{href:"/finance",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200",children:"财务管理"}),(0,a.jsx)(i(),{href:"/login",className:"ml-4 px-4 py-2 rounded-md text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200",children:"登录"})]}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)("button",{className:"text-gray-600 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white focus:outline-none",children:(0,a.jsx)(n,{className:"h-6 w-6"})})})]})})}),(0,a.jsxs)("div",{className:"relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-800 dark:via-gray-900 dark:to-gray-800 py-20 sm:py-32 overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-indigo-400/20 to-purple-600/20 rounded-full blur-3xl"}),(0,a.jsx)("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-blue-400/20 to-indigo-600/20 rounded-full blur-3xl"}),(0,a.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-indigo-300/10 to-purple-400/10 rounded-full blur-3xl"})]}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-12 items-center",children:[(0,a.jsx)("div",{className:"lg:col-span-7 text-left mb-12 lg:mb-0 relative z-10",children:(0,a.jsxs)("div",{className:"max-w-2xl",children:[(0,a.jsx)("div",{className:"flex items-center space-x-3 mb-6 animate-fade-in",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 px-4 py-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full border border-indigo-200/50 dark:border-indigo-700/50",children:[(0,a.jsx)(l.A,{className:"h-5 w-5 text-indigo-600 dark:text-indigo-400"}),(0,a.jsx)("span",{className:"text-sm font-semibold text-indigo-600 dark:text-indigo-400",children:"思学"})]})}),(0,a.jsxs)("h1",{className:"text-5xl font-bold text-gray-900 dark:text-white sm:text-6xl lg:text-7xl leading-tight animate-fade-in animation-delay-150",children:["让教育管理",(0,a.jsx)("span",{className:"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400",children:"更简单高效"})]}),(0,a.jsx)("p",{className:"mt-6 text-xl text-gray-600 dark:text-gray-300 leading-relaxed animate-fade-in animation-delay-300",children:"界面成熟友善，操作简单方便的教育管理平台。集学员管理、课程安排、考勤统计、财务管理于一体，帮助教育机构优化管理，提高效率。"}),(0,a.jsxs)("div",{className:"mt-8 grid grid-cols-2 gap-4 animate-fade-in animation-delay-400",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-indigo-500 rounded-full"}),(0,a.jsx)("span",{className:"text-gray-700 dark:text-gray-300 font-medium",children:"界面友善易用"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),(0,a.jsx)("span",{className:"text-gray-700 dark:text-gray-300 font-medium",children:"操作简单便捷"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,a.jsx)("span",{className:"text-gray-700 dark:text-gray-300 font-medium",children:"管理高效智能"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{className:"text-gray-700 dark:text-gray-300 font-medium",children:"持续优化创新"})]})]}),(0,a.jsxs)("div",{className:"mt-10 flex flex-col sm:flex-row gap-4 animate-fade-in animation-delay-500",children:[(0,a.jsxs)(i(),{href:"/dashboard",className:"group inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:["立即体验",(0,a.jsx)(o,{className:"ml-2 h-5 w-5 rotate-[-90deg] group-hover:translate-x-1 transition-transform duration-300"})]}),(0,a.jsx)(i(),{href:"#features",className:"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-indigo-600 dark:text-indigo-400 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-indigo-200 dark:border-indigo-700 hover:bg-white dark:hover:bg-gray-800 rounded-xl transition-all duration-300",children:"了解更多"})]}),(0,a.jsxs)("div",{className:"mt-12 animate-fade-in animation-delay-700",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 font-medium mb-6",children:"值得信赖的教育伙伴"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600",children:"3年+"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:"专业服务"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600",children:"1000+"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:"教育机构"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600",children:"99%"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:"用户满意度"})]})]})]})]})}),(0,a.jsx)("div",{className:"lg:col-span-5 relative lg:mt-0 animate-fade-in animation-delay-400",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"relative bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-white/20 dark:border-gray-700/20",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-indigo-500/5 via-purple-500/5 to-blue-500/5 rounded-3xl"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center",children:(0,a.jsx)(x.A,{className:"w-5 h-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 dark:text-white",children:"思学"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"教育管理平台"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-400 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-xs text-green-600 dark:text-green-400 font-medium",children:"在线"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"group p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl border border-blue-100 dark:border-blue-800/30 hover:shadow-lg transition-all duration-300",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300",children:(0,a.jsx)(c.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"text-base font-semibold text-gray-900 dark:text-white",children:"学员管理"}),(0,a.jsx)("span",{className:"text-sm font-bold text-blue-600 dark:text-blue-400",children:"1,256"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"智能化学员信息管理"})]})]})}),(0,a.jsx)("div",{className:"group p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl border border-green-100 dark:border-green-800/30 hover:shadow-lg transition-all duration-300",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300",children:(0,a.jsx)(m.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"text-base font-semibold text-gray-900 dark:text-white",children:"课程安排"}),(0,a.jsx)("span",{className:"text-sm font-bold text-green-600 dark:text-green-400",children:"24"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"灵活便捷的排课系统"})]})]})}),(0,a.jsx)("div",{className:"group p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-2xl border border-purple-100 dark:border-purple-800/30 hover:shadow-lg transition-all duration-300",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300",children:(0,a.jsx)(h.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"text-base font-semibold text-gray-900 dark:text-white",children:"财务管理"}),(0,a.jsx)("span",{className:"text-sm font-bold text-purple-600 dark:text-purple-400",children:"\xa5156K"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"精准的财务统计分析"})]})]})}),(0,a.jsx)("div",{className:"group p-4 bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 rounded-2xl border border-orange-100 dark:border-orange-800/30 hover:shadow-lg transition-all duration-300",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-orange-500 to-yellow-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300",children:(0,a.jsx)(g.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"text-base font-semibold text-gray-900 dark:text-white",children:"考勤统计"}),(0,a.jsx)("span",{className:"text-sm font-bold text-orange-600 dark:text-orange-400",children:"98.5%"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"自动化考勤记录"})]})]})})]}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"系统状态"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-green-600 dark:text-green-400 font-medium",children:"运行正常"})]})]})})]})]}),(0,a.jsx)("div",{className:"absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-r from-indigo-400/20 to-purple-600/20 rounded-full blur-2xl animate-pulse"}),(0,a.jsx)("div",{className:"absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-indigo-600/20 rounded-full blur-2xl animate-pulse animation-delay-1000"})]})})]})}),(0,a.jsx)("div",{"aria-hidden":"true",className:"absolute top-1/3 right-0 -mt-20 -mr-20 lg:mt-0 lg:mr-0 lg:right-1/2 lg:translate-x-1/2 lg:-translate-y-1/2 pointer-events-none",children:(0,a.jsx)("div",{className:"hidden lg:block h-56 w-56 bg-indigo-100 dark:bg-indigo-900/20 rounded-full opacity-30 mix-blend-multiply filter blur-3xl animate-blob"})}),(0,a.jsx)("div",{"aria-hidden":"true",className:"absolute bottom-1/4 left-0 -ml-20 lg:ml-0 lg:left-1/3 pointer-events-none",children:(0,a.jsx)("div",{className:"hidden lg:block h-48 w-48 bg-purple-100 dark:bg-purple-900/20 rounded-full opacity-30 mix-blend-multiply filter blur-3xl animate-blob animation-delay-2000"})})]}),(0,a.jsx)("div",{id:"features",className:"py-16 bg-white dark:bg-gray-800",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-extrabold text-gray-900 dark:text-white sm:text-4xl",children:"核心功能模块"}),(0,a.jsx)("p",{className:"mt-4 text-xl text-gray-500 dark:text-gray-300",children:"全方位的教育管理解决方案"})]}),(0,a.jsxs)("div",{className:"mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsx)("div",{className:"relative group",children:(0,a.jsxs)("div",{className:"relative p-6 bg-white dark:bg-gray-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-600",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-300"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,a.jsx)(c.A,{className:"w-6 h-6 text-blue-600 dark:text-blue-400"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"学员管理"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-300",children:"完整的学员档案管理，包括个人信息、学习进度、成绩记录等全方位跟踪。"})]})]})}),(0,a.jsx)("div",{className:"relative group",children:(0,a.jsxs)("div",{className:"relative p-6 bg-white dark:bg-gray-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-600",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-600 opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-300"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,a.jsx)(m.A,{className:"w-6 h-6 text-green-600 dark:text-green-400"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"课程安排"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-300",children:"智能排课系统，支持教师、教室、时间的自动匹配，避免冲突，提高效率。"})]})]})}),(0,a.jsx)("div",{className:"relative group",children:(0,a.jsxs)("div",{className:"relative p-6 bg-white dark:bg-gray-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-600",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-orange-500 to-red-600 opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-300"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,a.jsx)(g.A,{className:"w-6 h-6 text-orange-600 dark:text-orange-400"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"考勤管理"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-300",children:"精准的考勤统计，支持多种签到方式，实时监控出勤率，生成详细报表。"})]})]})}),(0,a.jsx)("div",{className:"relative group",children:(0,a.jsxs)("div",{className:"relative p-6 bg-white dark:bg-gray-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-600",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-300"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,a.jsx)(h.A,{className:"w-6 h-6 text-purple-600 dark:text-purple-400"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"财务管理"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-300",children:"完整的收支管理，学费收缴、工资发放、费用统计，财务状况一目了然。"})]})]})}),(0,a.jsx)("div",{className:"relative group",children:(0,a.jsxs)("div",{className:"relative p-6 bg-white dark:bg-gray-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-600",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-teal-500 to-cyan-600 opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-300"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-teal-100 dark:bg-teal-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-teal-600 dark:text-teal-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"成绩管理"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-300",children:"多维度成绩录入与分析，支持各类考试成绩管理，生成学习报告。"})]})]})}),(0,a.jsx)("div",{className:"relative group",children:(0,a.jsxs)("div",{className:"relative p-6 bg-white dark:bg-gray-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-600",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-indigo-500 to-blue-600 opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-300"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,a.jsx)(u.A,{className:"w-6 h-6 text-indigo-600 dark:text-indigo-400"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"组织管理"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-300",children:"机构架构管理，部门设置，权限分配，让管理层级清晰有序。"})]})]})})]})]})}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-indigo-600 to-purple-700 dark:from-indigo-800 dark:to-purple-900",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto py-12 px-4 sm:py-16 sm:px-6 lg:px-8 lg:py-20",children:[(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-extrabold text-white sm:text-4xl",children:"平台数据概览"}),(0,a.jsx)("p",{className:"mt-3 text-xl text-indigo-200 sm:mt-4",children:"数字见证教育管理的专业与高效"})]}),(0,a.jsxs)("dl",{className:"mt-10 text-center sm:max-w-5xl sm:mx-auto sm:grid sm:grid-cols-4 sm:gap-8",children:[(0,a.jsxs)("div",{className:"flex flex-col group hover:scale-105 transition-transform duration-300",children:[(0,a.jsx)("dt",{className:"order-2 mt-2 text-lg leading-6 font-medium text-indigo-200 group-hover:text-white transition-colors duration-300",children:"注册学员"}),(0,a.jsx)("dd",{className:"order-1 text-5xl font-extrabold text-white group-hover:text-yellow-300 transition-colors duration-300",children:"2,580+"})]}),(0,a.jsxs)("div",{className:"flex flex-col mt-10 sm:mt-0 group hover:scale-105 transition-transform duration-300",children:[(0,a.jsx)("dt",{className:"order-2 mt-2 text-lg leading-6 font-medium text-indigo-200 group-hover:text-white transition-colors duration-300",children:"开设课程"}),(0,a.jsx)("dd",{className:"order-1 text-5xl font-extrabold text-white group-hover:text-green-300 transition-colors duration-300",children:"156+"})]}),(0,a.jsxs)("div",{className:"flex flex-col mt-10 sm:mt-0 group hover:scale-105 transition-transform duration-300",children:[(0,a.jsx)("dt",{className:"order-2 mt-2 text-lg leading-6 font-medium text-indigo-200 group-hover:text-white transition-colors duration-300",children:"教职员工"}),(0,a.jsx)("dd",{className:"order-1 text-5xl font-extrabold text-white group-hover:text-blue-300 transition-colors duration-300",children:"89+"})]}),(0,a.jsxs)("div",{className:"flex flex-col mt-10 sm:mt-0 group hover:scale-105 transition-transform duration-300",children:[(0,a.jsx)("dt",{className:"order-2 mt-2 text-lg leading-6 font-medium text-indigo-200 group-hover:text-white transition-colors duration-300",children:"出勤率"}),(0,a.jsx)("dd",{className:"order-1 text-5xl font-extrabold text-white group-hover:text-pink-300 transition-colors duration-300",children:"96.8%"})]})]}),(0,a.jsxs)("div",{className:"mt-16 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center hover:bg-white/20 transition-all duration-300",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-white mb-2",children:"\xa51,280,000"}),(0,a.jsx)("div",{className:"text-indigo-200",children:"本年度总收入"})]}),(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center hover:bg-white/20 transition-all duration-300",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-white mb-2",children:"98.5%"}),(0,a.jsx)("div",{className:"text-indigo-200",children:"家长满意度"})]}),(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center hover:bg-white/20 transition-all duration-300",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-white mb-2",children:"24/7"}),(0,a.jsx)("div",{className:"text-indigo-200",children:"系统稳定运行"})]})]})]})}),(0,a.jsx)("footer",{className:"bg-gray-800 dark:bg-gray-900",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8",children:[(0,a.jsxs)("div",{className:"xl:grid xl:grid-cols-3 xl:gap-8",children:[(0,a.jsxs)("div",{className:"space-y-8 xl:col-span-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"w-5 h-5 text-white"})}),(0,a.jsxs)("span",{className:"text-xl font-bold text-white",children:[(0,a.jsx)("span",{className:"text-indigo-400",children:"思"}),"学"]})]}),(0,a.jsx)("p",{className:"text-gray-300 text-base",children:"专业的教育管理平台，致力于为教育机构提供全方位的数字化管理解决方案，让教育管理更高效、更智能。"}),(0,a.jsxs)("div",{className:"flex space-x-6",children:[(0,a.jsxs)("a",{href:"#",className:"text-gray-400 hover:text-gray-300 transition-colors duration-200",children:[(0,a.jsx)("span",{className:"sr-only",children:"微信"}),(0,a.jsx)(p.A,{className:"h-6 w-6"})]}),(0,a.jsxs)("a",{href:"#",className:"text-gray-400 hover:text-gray-300 transition-colors duration-200",children:[(0,a.jsx)("span",{className:"sr-only",children:"QQ"}),(0,a.jsx)(p.A,{className:"h-6 w-6"})]})]})]}),(0,a.jsxs)("div",{className:"mt-12 grid grid-cols-2 gap-8 xl:mt-0 xl:col-span-2",children:[(0,a.jsxs)("div",{className:"md:grid md:grid-cols-2 md:gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-400 tracking-wider uppercase",children:"功能模块"}),(0,a.jsxs)("ul",{className:"mt-4 space-y-4",children:[(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/academic",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"学员管理"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/academic/courses",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"课程安排"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/academic/attendance",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"考勤管理"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/finance",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"财务管理"})})]})]}),(0,a.jsxs)("div",{className:"mt-12 md:mt-0",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-400 tracking-wider uppercase",children:"支持服务"}),(0,a.jsxs)("ul",{className:"mt-4 space-y-4",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"帮助中心"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"使用文档"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"视频教程"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"技术支持"})})]})]})]}),(0,a.jsxs)("div",{className:"md:grid md:grid-cols-2 md:gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-400 tracking-wider uppercase",children:"关于我们"}),(0,a.jsxs)("ul",{className:"mt-4 space-y-4",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"公司介绍"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"发展历程"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"加入我们"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"联系我们"})})]})]}),(0,a.jsxs)("div",{className:"mt-12 md:mt-0",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-400 tracking-wider uppercase",children:"法律条款"}),(0,a.jsxs)("ul",{className:"mt-4 space-y-4",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"隐私政策"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"服务条款"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-base text-gray-300 hover:text-white transition-colors duration-200",children:"用户协议"})})]})]})]})]})]}),(0,a.jsx)("div",{className:"mt-12 border-t border-gray-700 pt-8",children:(0,a.jsx)("p",{className:"text-base text-gray-400 xl:text-center",children:"\xa9 2024 思学 教育管理平台. 保留所有权利。"})})]})})]})}},90230:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(26373).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7392,5814,3019],()=>r(41949));module.exports=a})();