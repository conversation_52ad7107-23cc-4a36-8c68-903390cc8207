(()=>{var e={};e.id=1769,e.ids=[1769],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4553:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(60687),r=t(85726);function l(){return(0,a.jsxs)("div",{className:"p-4 space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-5 mb-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-100 p-1 sm:p-2",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(r.E,{className:"h-9 w-20"}),(0,a.jsx)(r.E,{className:"h-9 w-40"}),(0,a.jsx)(r.E,{className:"h-9 w-20"})]}),(0,a.jsxs)("div",{className:"flex items-center flex-wrap gap-3",children:[(0,a.jsx)(r.E,{className:"h-9 w-[180px]"}),(0,a.jsx)(r.E,{className:"h-9 w-20"}),(0,a.jsx)(r.E,{className:"h-9 w-24"})]})]})}),(0,a.jsxs)("div",{className:"flex justify-end items-center gap-2",children:[(0,a.jsx)(r.E,{className:"h-10 w-28"}),(0,a.jsx)(r.E,{className:"h-10 w-24"})]})]}),(0,a.jsxs)("div",{className:"border rounded-md shadow-sm",children:[(0,a.jsxs)("div",{className:"grid grid-cols-8 border-b",children:[(0,a.jsx)(r.E,{className:"h-10 m-2"}),Array(7).fill(0).map((e,s)=>(0,a.jsx)(r.E,{className:"h-10 m-2"},s))]}),Array(8).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"grid grid-cols-8 border-b",children:[(0,a.jsx)(r.E,{className:"h-20 m-2"}),Array(7).fill(0).map((e,s)=>(0,a.jsx)(r.E,{className:"h-20 m-2"},s))]},s))]})]})}},6223:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,metadata:()=>l});var a=t(37413),r=t(96076);let l={title:`课程详情 - 蜜卡`};function n(){return(0,a.jsx)(r.default,{})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11749:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var a=t(60687),r=t(29523),l=t(76242);function n({icon:e,tooltipText:s,tooltipSide:t="top",tooltipAlign:n="center",delayDuration:d=300,variant:i="ghost",size:o="icon",className:c="h-8 w-8 hover:bg-muted",...u}){return(0,a.jsx)(l.Bc,{delayDuration:d,children:(0,a.jsxs)(l.m_,{children:[(0,a.jsx)(l.k$,{asChild:!0,children:(0,a.jsx)(r.$,{variant:i,size:o,className:c,...u,children:(0,a.jsx)(e,{className:"h-4 w-4 text-muted-foreground"})})}),(0,a.jsx)(l.ZI,{side:t,align:n,className:"font-medium text-xs px-3 py-1.5",children:(0,a.jsx)("p",{children:s})})]})})}},11860:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21035:(e,s,t)=>{Promise.resolve().then(t.bind(t,93083))},21820:e=>{"use strict";e.exports=require("os")},21843:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(37413),r=t(24597),l=t(36733);function n({children:e}){return(0,a.jsxs)("div",{className:"fixed inset-0 flex flex-col",children:[(0,a.jsx)(r.default,{}),(0,a.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,a.jsx)(l.default,{}),(0,a.jsx)("main",{className:"flex-1 p-8 pt-16 overflow-auto",children:(0,a.jsx)("div",{className:"w-full mx-auto bg-white rounded-lg shadow-sm",children:e})})]})]})}},26134:(e,s,t)=>{"use strict";t.d(s,{G$:()=>K,Hs:()=>b,UC:()=>es,VY:()=>ea,ZL:()=>Q,bL:()=>J,bm:()=>er,hE:()=>et,hJ:()=>ee,l9:()=>Y});var a=t(43210),r=t(70569),l=t(98599),n=t(11273),d=t(96963),i=t(65551),o=t(31355),c=t(32547),u=t(25028),m=t(46059),x=t(14163),p=t(1359),f=t(42247),h=t(63376),g=t(8730),v=t(60687),j="Dialog",[y,b]=(0,n.A)(j),[N,w]=y(j),C=e=>{let{__scopeDialog:s,children:t,open:r,defaultOpen:l,onOpenChange:n,modal:o=!0}=e,c=a.useRef(null),u=a.useRef(null),[m=!1,x]=(0,i.i)({prop:r,defaultProp:l,onChange:n});return(0,v.jsx)(N,{scope:s,triggerRef:c,contentRef:u,contentId:(0,d.B)(),titleId:(0,d.B)(),descriptionId:(0,d.B)(),open:m,onOpenChange:x,onOpenToggle:a.useCallback(()=>x(e=>!e),[x]),modal:o,children:t})};C.displayName=j;var k="DialogTrigger",E=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,n=w(k,t),d=(0,l.s)(s,n.triggerRef);return(0,v.jsx)(x.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":B(n.open),...a,ref:d,onClick:(0,r.m)(e.onClick,n.onOpenToggle)})});E.displayName=k;var R="DialogPortal",[S,P]=y(R,{forceMount:void 0}),I=e=>{let{__scopeDialog:s,forceMount:t,children:r,container:l}=e,n=w(R,s);return(0,v.jsx)(S,{scope:s,forceMount:t,children:a.Children.map(r,e=>(0,v.jsx)(m.C,{present:t||n.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:l,children:e})}))})};I.displayName=R;var D="DialogOverlay",A=a.forwardRef((e,s)=>{let t=P(D,e.__scopeDialog),{forceMount:a=t.forceMount,...r}=e,l=w(D,e.__scopeDialog);return l.modal?(0,v.jsx)(m.C,{present:a||l.open,children:(0,v.jsx)(F,{...r,ref:s})}):null});A.displayName=D;var F=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,r=w(D,t);return(0,v.jsx)(f.A,{as:g.DX,allowPinchZoom:!0,shards:[r.contentRef],children:(0,v.jsx)(x.sG.div,{"data-state":B(r.open),...a,ref:s,style:{pointerEvents:"auto",...a.style}})})}),_="DialogContent",q=a.forwardRef((e,s)=>{let t=P(_,e.__scopeDialog),{forceMount:a=t.forceMount,...r}=e,l=w(_,e.__scopeDialog);return(0,v.jsx)(m.C,{present:a||l.open,children:l.modal?(0,v.jsx)($,{...r,ref:s}):(0,v.jsx)(O,{...r,ref:s})})});q.displayName=_;var $=a.forwardRef((e,s)=>{let t=w(_,e.__scopeDialog),n=a.useRef(null),d=(0,l.s)(s,t.contentRef,n);return a.useEffect(()=>{let e=n.current;if(e)return(0,h.Eq)(e)},[]),(0,v.jsx)(G,{...e,ref:d,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),t.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let s=e.detail.originalEvent,t=0===s.button&&!0===s.ctrlKey;(2===s.button||t)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),O=a.forwardRef((e,s)=>{let t=w(_,e.__scopeDialog),r=a.useRef(!1),l=a.useRef(!1);return(0,v.jsx)(G,{...e,ref:s,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(r.current||t.triggerRef.current?.focus(),s.preventDefault()),r.current=!1,l.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(r.current=!0,"pointerdown"!==s.detail.originalEvent.type||(l.current=!0));let a=s.target;t.triggerRef.current?.contains(a)&&s.preventDefault(),"focusin"===s.detail.originalEvent.type&&l.current&&s.preventDefault()}})}),G=a.forwardRef((e,s)=>{let{__scopeDialog:t,trapFocus:r,onOpenAutoFocus:n,onCloseAutoFocus:d,...i}=e,u=w(_,t),m=a.useRef(null),x=(0,l.s)(s,m);return(0,p.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:n,onUnmountAutoFocus:d,children:(0,v.jsx)(o.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":B(u.open),...i,ref:x,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(U,{titleId:u.titleId}),(0,v.jsx)(X,{contentRef:m,descriptionId:u.descriptionId})]})]})}),z="DialogTitle",M=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,r=w(z,t);return(0,v.jsx)(x.sG.h2,{id:r.titleId,...a,ref:s})});M.displayName=z;var T="DialogDescription",W=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,r=w(T,t);return(0,v.jsx)(x.sG.p,{id:r.descriptionId,...a,ref:s})});W.displayName=T;var Z="DialogClose",V=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,l=w(Z,t);return(0,v.jsx)(x.sG.button,{type:"button",...a,ref:s,onClick:(0,r.m)(e.onClick,()=>l.onOpenChange(!1))})});function B(e){return e?"open":"closed"}V.displayName=Z;var L="DialogTitleWarning",[K,H]=(0,n.q)(L,{contentName:_,titleName:z,docsSlug:"dialog"}),U=({titleId:e})=>{let s=H(L),t=`\`${s.contentName}\` requires a \`${s.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${s.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${s.docsSlug}`;return a.useEffect(()=>{e&&!document.getElementById(e)&&console.error(t)},[t,e]),null},X=({contentRef:e,descriptionId:s})=>{let t=H("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${t.contentName}}.`;return a.useEffect(()=>{let t=e.current?.getAttribute("aria-describedby");s&&t&&!document.getElementById(s)&&console.warn(r)},[r,e,s]),null},J=C,Y=E,Q=I,ee=A,es=q,et=M,ea=W,er=V},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32459:(e,s,t)=>{Promise.resolve().then(t.bind(t,35513))},33873:e=>{"use strict";e.exports=require("path")},35513:(e,s,t)=>{"use strict";t.d(s,{default:()=>es});var a=t(60687),r=t(29523),l=t(43210),n=t(11273),d=t(14163),i="Progress",[o,c]=(0,n.A)(i),[u,m]=o(i),x=l.forwardRef((e,s)=>{var t,r;let{__scopeProgress:l,value:n=null,max:i,getValueLabel:o=h,...c}=e;(i||0===i)&&!j(i)&&console.error((t=`${i}`,`Invalid prop \`max\` of value \`${t}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=j(i)?i:100;null===n||y(n,m)||console.error((r=`${n}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let x=y(n,m)?n:null,p=v(x)?o(x,m):void 0;return(0,a.jsx)(u,{scope:l,value:x,max:m,children:(0,a.jsx)(d.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":v(x)?x:void 0,"aria-valuetext":p,role:"progressbar","data-state":g(x,m),"data-value":x??void 0,"data-max":m,...c,ref:s})})});x.displayName=i;var p="ProgressIndicator",f=l.forwardRef((e,s)=>{let{__scopeProgress:t,...r}=e,l=m(p,t);return(0,a.jsx)(d.sG.div,{"data-state":g(l.value,l.max),"data-value":l.value??void 0,"data-max":l.max,...r,ref:s})});function h(e,s){return`${Math.round(e/s*100)}%`}function g(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function v(e){return"number"==typeof e}function j(e){return v(e)&&!isNaN(e)&&e>0}function y(e,s){return v(e)&&!isNaN(e)&&e<=s&&e>=0}f.displayName=p;var b=t(4780);let N=l.forwardRef(({className:e,value:s,...t},r)=>(0,a.jsx)(x,{ref:r,className:(0,b.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...t,children:(0,a.jsx)(f,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));N.displayName=x.displayName;var w=t(39907),C=t(76869),k=t(52595),E=t(30036);let R=(0,E.default)(async()=>{},{loadableGenerated:{modules:["app\\academic\\schedule\\[classesScheduleId]\\components\\classes-detail.tsx -> ./dialogs/edit-class-schedule"]},ssr:!1});function S({schedules:e}){let[s,t]=(0,l.useState)(!1);if(!e)return(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:"加载中..."});let n=e.StudentWeeklySchedule.length,d=e.StudentWeeklySchedule.filter(e=>"attendance"===e.status).length,i=e.StudentWeeklySchedule.filter(e=>"leave"===e.status).length,o=e.StudentWeeklySchedule.filter(e=>"absent"===e.status).length,c=e.StudentWeeklySchedule.filter(e=>"unattended"===e.status).length;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-xl font-medium text-gray-900",children:[(0,C.GP)(new Date(e?.startDate),"yyyy-MM-dd (EEEE)",{locale:k.g})," ",e?.startTime,"-",e?.endTime]}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:[e.name||e.classes?.name,"\xb7 ",e.courses.name]})]}),(0,a.jsx)(r.$,{onClick:()=>t(!0),variant:"outline",size:"sm",className:"self-start sm:self-center",children:"编辑"})]}),(0,a.jsx)(w.w,{className:"my-6"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"容纳人数"}),(0,a.jsx)("div",{className:"text-2xl font-medium",children:e.maxStudentCount})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"上课老师"}),(0,a.jsx)("div",{className:"text-2xl font-medium",children:e.teacher.name})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"课程进度"}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(N,{value:e.currentWeeks/e.totalWeeks*100,className:"h-2"})}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[e.currentWeeks,"/",e.totalWeeks]})]})]})]})]}),(0,a.jsx)("div",{className:"border-t px-6 py-4 bg-gray-50 mb-2",children:(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-x-6 gap-y-2 text-sm",children:[(0,a.jsx)("div",{className:"text-gray-500",children:"考勤情况"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"共"})," ",(0,a.jsx)("span",{className:"font-medium",children:n})," ",(0,a.jsx)("span",{className:"text-gray-500",children:"名学员"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"到课"})," ",(0,a.jsx)("span",{className:"font-medium text-blue-600",children:d})," ",(0,a.jsx)("span",{className:"text-gray-500",children:"人"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"请假"})," ",(0,a.jsx)("span",{className:"font-medium text-amber-600",children:i})," ",(0,a.jsx)("span",{className:"text-gray-500",children:"人"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"缺勤"})," ",(0,a.jsx)("span",{className:"font-medium text-red-600",children:o})," ",(0,a.jsx)("span",{className:"text-gray-500",children:"人"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"未考勤"})," ",(0,a.jsx)("span",{className:"font-medium",children:c})," ",(0,a.jsx)("span",{className:"text-gray-500",children:"人"})]})]})}),s&&(0,a.jsx)(R,{open:s,onOpenChange:t,schedules:e})]})}var P=t(4733),I=t(62688);let D=(0,I.A)("ClipboardCheck",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]);var A=t(23026);let F=(0,I.A)("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var _=t(63503),q=t(89667),$=t(99270),O=t(96474),G=t(76320),z=t(73480);let M=(0,E.default)(()=>t.e(9927).then(t.bind(t,9927)),{loadableGenerated:{modules:["app\\academic\\schedule\\[classesScheduleId]\\components\\dialogs\\class-student-add-dialog.tsx -> @/components/TablePagination"]}});function T({open:e,onOpenChange:s,students:t,handleSave:n}){let[d,i]=(0,l.useState)(""),[o,c]=(0,l.useState)(""),[u,m]=(0,l.useState)(1),[x,p]=(0,l.useState)(10),[f,h]=(0,l.useState)([]),{data:g,isLoading:v}=(0,G.L5)({page:u,pageSize:x,search:d},{skip:!e}),j=(0,l.useCallback)((0,z.A)(e=>{i(e)},500),[]),y=e=>t.find(s=>s.student.id===e)?"在班":"未在班",b=e=>{n(e)&&h(s=>[...s,e])};return(0,a.jsx)(_.lG,{open:e,onOpenChange:s,children:(0,a.jsxs)(_.Cf,{className:"sm:max-w-[700px] p-0 overflow-hidden bg-white rounded-md shadow-md antialiased",children:[(0,a.jsx)(_.c7,{className:"px-6 py-2.5 bg-gray-50 border-b",children:(0,a.jsx)(_.L3,{className:"text-sm font-medium text-gray-900",children:"新增学员"})}),(0,a.jsxs)("div",{className:"p-5 space-y-5",children:[(0,a.jsxs)("div",{className:"flex items-center relative",children:[(0,a.jsx)($.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400"}),(0,a.jsx)(q.p,{placeholder:"搜索学员名称",value:o,onChange:e=>{let s=e.target.value;c(s),j(s)},className:"pl-8 h-9 text-sm rounded-md border-gray-200 focus:ring-1 focus:ring-gray-200 transition-none"})]}),(0,a.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"bg-gray-50 border-b border-gray-100",children:[(0,a.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-600",children:"学员名"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-600",children:"手机号码"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-600",children:"状态"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-600",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-100",children:g?.list&&g.list.length>0?g.list.map(e=>(0,a.jsxs)("tr",{className:`hover:bg-gray-50/60 ${f.includes(e.id)?"bg-green-50/40":""}`,children:[(0,a.jsx)("td",{className:"px-4 py-3 font-medium text-gray-700",children:e.name}),(0,a.jsx)("td",{className:"px-4 py-3 text-gray-600",children:e.phone}),(0,a.jsx)("td",{className:"px-4 py-3",children:(0,a.jsx)("span",{className:`inline-flex items-center px-2 py-0.5 rounded-full text-xs ${"在班"===y(e.id)?"bg-green-50 text-green-700 ring-1 ring-green-600/10":"bg-yellow-50 text-yellow-700 ring-1 ring-yellow-600/10"}`,children:y(e.id)})}),(0,a.jsx)("td",{className:"px-4 py-3",children:(0,a.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>b(e.id),title:"添加",children:(0,a.jsx)(O.A,{className:"h-3 w-3"})})})]},e.id)):(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:4,className:"px-4 py-8 text-center text-gray-500 text-sm",children:v?"加载中...":"没有找到匹配的学员"})})})]})}),g?.total&&g.total>=10&&(0,a.jsx)(M,{totalItems:g.total,pageSize:x,currentPage:u,onPageChange:m,onPageSizeChange:p})]})]})})}var W=t(96834),Z=t(11749),V=t(88397),B=t(84778),L=t(13964),K=t(48730),H=t(11860),U=t(16189);let X={fixed:{text:"固定学员",class:"bg-blue-50 text-blue-600 border-blue-200"},temporary:{text:"临时学员",class:"bg-purple-50 text-purple-600 border-purple-200"},trial:{text:"试听学员",class:"bg-orange-50 text-orange-600 border-orange-200"},default:{text:"未知",class:"bg-slate-50 text-slate-600 border-slate-200"}},J={attendance:{text:"已考勤",class:"bg-emerald-50 text-emerald-600"},absent:{text:"缺勤",class:"bg-rose-50 text-rose-600"},leave:{text:"请假",class:"bg-amber-50 text-amber-600"},unattended:{text:"未考勤",class:"bg-slate-100 text-slate-600"},default:{text:"未考勤",class:"bg-slate-100 text-slate-600"}},Y=({studentId:e})=>{let s=function(){let[e]=(0,V.eZ)(),{classesScheduleId:s}=(0,U.useParams)();return async(t,a)=>{let r=await e({id:s,data:{studentId:t,status:a}});if(r.error){let e=r.error.data?.error?.message||r.error.data?.message;B.l.error(e||"更新考勤状态失败.");return}B.l.success("更新考勤状态成功.")}}();return(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(Z.p,{icon:L.A,tooltipText:"到课",onClick:()=>s(e,"attendance")}),(0,a.jsx)(Z.p,{icon:K.A,tooltipText:"请假",onClick:()=>s(e,"leave")}),(0,a.jsx)(Z.p,{icon:H.A,tooltipText:"缺勤",onClick:()=>s(e,"absent")})]})},Q=[{header:"姓名",accessorKey:"name",cell:({row:e})=>(0,a.jsx)("div",{className:"font-medium text-foreground",children:e.original.student?.name})},{header:"手机号码",accessorKey:"phone",cell:({row:e})=>(0,a.jsx)("div",{className:"text-muted-foreground",children:e.original.student?.phone})},{header:"上课身份",accessorKey:"studentType",cell:({row:e})=>{let s=X[e.original.studentType]||X.default;return(0,a.jsx)(W.E,{variant:"outline",className:`font-normal text-xs ${s.class}`,children:s.text})}},{header:"考勤状态",accessorKey:"status",cell:({row:e})=>{let s=J[e.getValue("status")]||J.default;return(0,a.jsx)(W.E,{variant:"secondary",className:`px-2.5 py-0.5 rounded-md text-xs font-medium ${s.class}`,children:s.text})}},{header:"操作",accessorKey:"action",cell:({row:e})=>(console.log(e.original.student?.id),(0,a.jsx)(Y,{studentId:e.original.student?.id}))}];function ee({studentData:e}){let{classesScheduleId:s}=(0,U.useParams)(),[t]=(0,V.b)(),[n]=(0,V.OU)(),[d,i]=(0,l.useState)(!1),[o,c]=(0,l.useState)([]),[u,m]=(0,l.useState)("temporary"),x=(0,l.useCallback)(e=>{console.log(e,"选中的行"),c(s=>s.length===e.length&&s.every((s,t)=>s.id===e[t]?.id)?s:e)},[]),p=async()=>{let e=o.map(e=>e.student.id);try{await n({scheduleId:s,studentIds:e}),B.l.success("删除学员成功."),c([])}catch(e){B.l.error(e.message||"删除学员失败!")}};return e?(0,a.jsxs)("div",{className:"px-4 pb-4 space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsxs)(r.$,{variant:"outline",disabled:0===o.length,children:[(0,a.jsx)(D,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"批量考勤"})]}),(0,a.jsxs)(r.$,{variant:"outline",onClick:()=>{i(!0),m("temporary")},children:[(0,a.jsx)(A.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"新增临时学员"})]}),(0,a.jsxs)(r.$,{variant:"outline",onClick:()=>{i(!0),m("trial")},children:[(0,a.jsx)(A.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"新增试听学员"})]}),(0,a.jsxs)(r.$,{onClick:p,disabled:0===o.length,className:`shadow-sm hover:shadow transition-all duration-200 rounded-lg flex items-center gap-2 px-5 py-2 font-medium ${o.length>0?"bg-red-500 hover:bg-red-600 text-white":"bg-gray-200 text-gray-500 cursor-not-allowed"}`,children:[(0,a.jsx)(F,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"移除学员"})]})]}),(0,a.jsx)(P.b,{columns:Q,data:e,selectable:!0,onSelectedRowsChange:x}),d&&(0,a.jsx)(T,{open:d,onOpenChange:i,students:e,handleSave:e=>{try{return t({scheduleId:s,data:{students:{studentId:e,type:u}}}),!0}catch(e){return B.l.error(e.message||"添加学员失败!"),!1}}})]}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:"加载中..."})}function es(){let{classesScheduleId:e}=(0,U.useParams)(),{data:s}=(0,V.rr)(e);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(S,{schedules:s}),(0,a.jsx)(ee,{studentData:s?.StudentWeeklySchedule})]})}},38035:(e,s,t)=>{Promise.resolve().then(t.bind(t,96076))},38362:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(60687),r=t(85726),l=t(44493);function n(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(l.Zp,{className:"border border-slate-200 shadow-sm overflow-hidden rounded-md",children:[(0,a.jsx)(l.aR,{className:"bg-slate-50 px-6 py-4 border-b border-slate-200",children:(0,a.jsx)(r.E,{className:"h-7 w-40"})}),(0,a.jsx)(l.Wu,{className:"p-6",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Array(6).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsx)(r.E,{className:"h-5 w-24"}),(0,a.jsx)(r.E,{className:"h-6 w-full"})]},s))})})]}),(0,a.jsxs)(l.Zp,{className:"border border-slate-200 shadow-sm overflow-hidden rounded-md",children:[(0,a.jsx)(l.aR,{className:"bg-slate-50 px-6 py-4 border-b border-slate-200",children:(0,a.jsx)(r.E,{className:"h-7 w-32"})}),(0,a.jsxs)(l.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)(r.E,{className:"h-10 w-64"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(r.E,{className:"h-10 w-24"}),(0,a.jsx)(r.E,{className:"h-10 w-24"})]})]}),(0,a.jsxs)("div",{className:"border rounded-md",children:[(0,a.jsx)("div",{className:"grid grid-cols-5 bg-slate-50 p-3 border-b",children:[,,,,,].fill(0).map((e,s)=>(0,a.jsx)(r.E,{className:"h-5"},s))}),[,,,,,].fill(0).map((e,s)=>(0,a.jsx)("div",{className:"grid grid-cols-5 p-3 border-b",children:[,,,,,].fill(0).map((e,s)=>(0,a.jsx)(r.E,{className:"h-5"},s))},s))]}),(0,a.jsx)("div",{className:"flex justify-end mt-4",children:(0,a.jsx)(r.E,{className:"h-8 w-64"})})]})]})]})}},39907:(e,s,t)=>{"use strict";t.d(s,{w:()=>c});var a=t(60687),r=t(43210),l=t(14163),n="horizontal",d=["horizontal","vertical"],i=r.forwardRef((e,s)=>{var t;let{decorative:r,orientation:i=n,...o}=e,c=(t=i,d.includes(t))?i:n;return(0,a.jsx)(l.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:s})});i.displayName="Separator";var o=t(4780);let c=r.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...r},l)=>(0,a.jsx)(i,{ref:l,decorative:t,orientation:s,className:(0,o.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));c.displayName=i.displayName},44493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>o,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>d,wL:()=>u});var a=t(60687),r=t(43210),l=t(4780);let n=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));n.displayName="Card";let d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let i=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));i.displayName="CardTitle";let o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let u=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s}));u.displayName="CardFooter"},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>u,Es:()=>x,L3:()=>p,c7:()=>m,lG:()=>i});var a=t(60687),r=t(43210),l=t(26134),n=t(11860),d=t(4780);let i=l.bL;l.l9;let o=l.ZL;l.bm;let c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.hJ,{ref:t,className:(0,d.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));c.displayName=l.hJ.displayName;let u=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(o,{children:[(0,a.jsx)(c,{}),(0,a.jsxs)(l.UC,{ref:r,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[s,(0,a.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=l.UC.displayName;let m=({className:e,...s})=>(0,a.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});m.displayName="DialogHeader";let x=({className:e,...s})=>(0,a.jsx)("div",{className:(0,d.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});x.displayName="DialogFooter";let p=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.hE,{ref:t,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));p.displayName=l.hE.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.VY,{ref:t,className:(0,d.cn)("text-sm text-muted-foreground",e),...s})).displayName=l.VY.displayName},66500:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\academic\\\\schedule\\\\[classesScheduleId]\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\[classesScheduleId]\\loading.tsx","default")},72945:(e,s,t)=>{Promise.resolve().then(t.bind(t,38362))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82673:(e,s,t)=>{Promise.resolve().then(t.bind(t,66500))},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var a=t(60687),r=t(43210),l=t(4780);let n=r.forwardRef(({className:e,type:s,...t},r)=>(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...t}));n.displayName="Input"},93083:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\academic\\\\schedule\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\loading.tsx","default")},94715:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=t(65239),r=t(48088),l=t(88170),n=t.n(l),d=t(30893),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);t.d(s,i);let o={children:["",{children:["academic",{children:["schedule",{children:["[classesScheduleId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6223)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\[classesScheduleId]\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,66500)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\[classesScheduleId]\\loading.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,93083)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21843)),"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\[classesScheduleId]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/academic/schedule/[classesScheduleId]/page",pathname:"/academic/schedule/[classesScheduleId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},94735:e=>{"use strict";e.exports=require("events")},96076:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\academic\\\\schedule\\\\[classesScheduleId]\\\\components\\\\ScheduleDetailView.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\academic\\schedule\\[classesScheduleId]\\components\\ScheduleDetailView.tsx","default")},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var a=t(60687);t(43210);var r=t(24224),l=t(4780);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:s,...t}){return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:s}),e),...t})}},97059:(e,s,t)=>{Promise.resolve().then(t.bind(t,4553))}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,7392,5814,3928,3443,2951,6869,1011,5257,3019,9879,4733],()=>t(94715));module.exports=a})();