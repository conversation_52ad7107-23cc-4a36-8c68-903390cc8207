/**
 * Menu Schema
 * Defines validation schemas for menu-related endpoints
 */

// Menu item schema with proper recursion
const menuItemSchema = {
    $id: 'menuItem',
    type: 'object',
    properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        path: { type: 'string' },
        icon: { type: 'string' },
        sort: { type: 'number' },
        hidden: { type: 'boolean' },
        children: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    path: { type: 'string' },
                    icon: { type: 'string' },
                    sort: { type: 'number' },
                    hidden: { type: 'boolean' },
                    children: {
                        type: 'array',
                        items: {
                            type: 'object',
                            additionalProperties: true
                        }
                    }
                }
            }
        }
    }
};

const menuSchema = {
    /**
     * Schema for getting user menus
     */
    getUserMenusSchema: {
        tags: ['menu'],
        summary: '获取用户菜单',
        description: '获取当前用户的菜单列表',
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'array',
                        items: menuItemSchema
                    },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for getting all menus
     */
    getAllMenusSchema: {
        tags: ['menu'],
        summary: '获取所有菜单',
        description: '获取所有菜单列表',
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'array',
                        items: menuItemSchema
                    },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for getting role menus
     */
    getRoleMenusSchema: {
        tags: ['menu'],
        summary: '获取角色菜单',
        description: '获取指定角色的菜单列表',
        params: {
            type: 'object',
            required: ['roleId'],
            properties: {
                roleId: {
                    type: 'string',
                    description: '角色ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'array',
                        items: menuItemSchema
                    },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for updating role menus
     */
    updateRoleMenusSchema: {
        tags: ['menu'],
        summary: '更新角色菜单',
        description: '更新指定角色的菜单列表',
        params: {
            type: 'object',
            required: ['roleId'],
            properties: {
                roleId: {
                    type: 'string',
                    description: '角色ID'
                }
            }
        },
        body: {
            type: 'object',
            required: ['menuIds'],
            properties: {
                menuIds: {
                    type: 'array',
                    items: { type: 'string' },
                    description: '菜单ID数组'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for creating menu
     */
    createMenuSchema: {
        tags: ['menu'],
        summary: '创建菜单',
        description: '创建新菜单',
        body: {
            type: 'object',
            required: ['name', 'path'],
            properties: {
                name: {
                    type: 'string',
                    description: '菜单名称'
                },
                path: {
                    type: 'string',
                    description: '菜单路径'
                },
                icon: {
                    type: 'string',
                    description: '菜单图标'
                },
                component: {
                    type: 'string',
                    description: '组件路径'
                },
                sort: {
                    type: 'number',
                    default: 0,
                    description: '排序'
                },
                hidden: {
                    type: 'boolean',
                    default: false,
                    description: '是否隐藏'
                },
                permissionCode: {
                    type: 'string',
                    description: '权限代码'
                },
                parentId: {
                    type: 'string',
                    description: '父菜单ID'
                },
                redirect: {
                    type: 'string',
                    description: '重定向路径'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            path: { type: 'string' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for updating menu
     */
    updateMenuSchema: {
        tags: ['menu'],
        summary: '更新菜单',
        description: '更新菜单信息',
        params: {
            type: 'object',
            required: ['menuId'],
            properties: {
                menuId: {
                    type: 'string',
                    description: '菜单ID'
                }
            }
        },
        body: {
            type: 'object',
            properties: {
                name: { type: 'string' },
                path: { type: 'string' },
                icon: { type: 'string' },
                component: { type: 'string' },
                sort: { type: 'number' },
                hidden: { type: 'boolean' },
                permissionCode: { type: 'string' },
                parentId: { type: 'string' },
                redirect: { type: 'string' }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for deleting menu
     */
    deleteMenuSchema: {
        tags: ['menu'],
        summary: '删除菜单',
        description: '删除菜单',
        params: {
            type: 'object',
            required: ['menuId'],
            properties: {
                menuId: {
                    type: 'string',
                    description: '菜单ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    }
};

export default menuSchema;
