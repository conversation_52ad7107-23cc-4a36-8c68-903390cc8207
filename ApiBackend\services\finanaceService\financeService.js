import financeModel from '../../models/financeModel.js';
import service2 from './financeService2.js';
import service3 from './financeService3.js';
import service4 from './financeService4.js';

/**
 * Finance service containing business logic for finance-related operations
 */
const service1 = {
  /**
   * Get buying records with pagination and filtering
   */
  async getBuyingRecords({ user, teacher, page, pageSize, startTime, endTime, search }) {
    const where = {
      student: {
        ...(search && {
          OR: [
            {
              name: {
                contains: search,
                mode: 'insensitive',
              }
            },
            {
              phone: {
                contains: search,
                mode: 'insensitive',
              }
            },
          ]
        }),
      },
      ...(teacher && {
        salesRepresentativeId: teacher,
      }),
      institutionId: user.institutionId,
    };

    if (startTime && endTime) {
      where.paymentTime = {
        gte: Number.parseInt(startTime),
        lte: Number.parseInt(endTime),
      };
    }

    const skip = (page - 1) * pageSize;
    const take = pageSize;
    const orderBy = {
      paymentTime: 'desc',
    };

    const [studentProductRecords, total] = await financeModel.getBuyingRecords(where, skip, take, orderBy);

    const transformedStudentProductRecords = studentProductRecords.map((item) => {
      return {
        ...item,
        paymentTime: Number.parseInt(item.paymentTime),
      };
    });

    return {
      total,
      page,
      pageSize,
      list: transformedStudentProductRecords,
    };
  },

  /**
   * Get teaching records with filtering
   */
  async getTeachingRecords({ user, startTime, endTime, search }) {
    const where = {
      ...(search && {
        teacher: {
          OR: [
            {
              name: { contains: search, mode: 'insensitive' },
            },
            {
              phone: { contains: search, mode: 'insensitive' },
            },
          ],
        },
      }),
      institutionId: user.institutionId,
    };

    if (startTime && endTime) {
      where.StudentWeeklySchedule = {
        some: {
          operatorTime: {
            gte: Number.parseInt(startTime),
            lte: Number.parseInt(endTime),
          }
        }
      };
    }

    const result = await financeModel.getTeachingRecords(where);

    let resultMap = new Map();
    result.forEach(item => {
      const teacherId = item.teacher.id;
      if (!resultMap.has(teacherId)) {
        resultMap.set(teacherId, {
          teacher: item.teacher,
          attendanceCount: 0,
          attendanceAmount: 0,
          classesCountCount: 0,
          unattendedCount: 0,
          leaveCount: 0,
          absentCount: 0,
        });
      }

      const teacherData = resultMap.get(teacherId);
      teacherData.classesCountCount += 1;
      item.StudentWeeklySchedule.forEach(items => {
        if (items.status === 'unattended') {
          teacherData.unattendedCount += 1;
        } else if (items.status === 'leave') {
          teacherData.leaveCount += 1;
        } else if (items.status === 'absent') {
          teacherData.absentCount += 1;
        }
        teacherData.attendanceCount = Number.parseFloat(teacherData.attendanceCount) + Number.parseFloat(items.attendanceCount || 0);
        teacherData.attendanceAmount = Number.parseFloat(teacherData.attendanceAmount) + Number.parseFloat(items.attendanceAmount || 0);
      });
    });

    const resultArray = Array.from(resultMap.values());
    resultArray.sort((a, b) => b.attendanceAmount - a.attendanceAmount);

    return resultArray;
  },

  /**
   * Get sales overview data
   */
  async getSalesOverview({ user, startTime, endTime, checkType }) {
    // Prepare query conditions
    const where = {
      institutionId: user.institutionId,
    };

    // Handle date ranges
    let queryStartTime, queryEndTime;

    if (!startTime || !endTime) {
      // Default to last 7 days
      queryEndTime = new Date();
      queryStartTime = new Date();
      queryStartTime.setDate(queryStartTime.getDate() - 7);
    } else {
      // Use provided range
      try {
        queryStartTime = new Date(parseInt(startTime, 10));
        queryEndTime = new Date(parseInt(endTime, 10));

        if (isNaN(queryStartTime.getTime()) || isNaN(queryEndTime.getTime())) {
          throw new Error('Invalid time range');
        }
      } catch (err) {
        throw new Error('Invalid time range');
      }
    }

    where.paymentTime = {
      gte: queryStartTime.getTime(),
      lte: queryEndTime.getTime(),
    };

    const [studentProductRefund, studentProductRecord] = await financeModel.getSalesOverviewData(where);

    return this._processSalesOverviewData(studentProductRefund, studentProductRecord, queryStartTime, queryEndTime, checkType);
  },

  /**
   * Process sales overview data
   * @private
   */
  _processSalesOverviewData(studentProductRefund, studentProductRecord, queryStartTime, queryEndTime, checkType) {
    // Create date formatters based on report type
    const formatDate = (timestamp) => {
      const date = new Date(parseInt(timestamp, 10));
      return checkType === 'daily'
        ? date.toLocaleDateString()
        : date.toLocaleDateString('default', { year: 'numeric', month: '2-digit' });
    };

    // 创建日期数据
    const dates = new Map();
    if (checkType === 'daily') {
      // Generate all dates between start and end dates for daily reports
      const daysDiff = Math.ceil((queryEndTime - queryStartTime) / (1000 * 60 * 60 * 24));
      const daysToGenerate = Math.min(daysDiff + 1, 31); // Limit to 31 days max to prevent excessive data

      for (let i = 0; i < daysToGenerate; i++) {
        const date = new Date(queryStartTime);
        date.setDate(date.getDate() + i);
        const dateKey = date.toLocaleDateString();
        dates.set(dateKey, {
          numberOfRefund: 0, // 退款次数
          refundAmount: 0, // 退款金额

          paidAmount: 0, // 支付金额
          numberOfPaid: 0, // 支付次数
          unpaidAmount: 0, // 未支付金额
        });
      }
    } else {
      // Generate all months between start and end dates for monthly reports
      const startYear = queryStartTime.getFullYear();
      const startMonth = queryStartTime.getMonth();
      const endYear = queryEndTime.getFullYear();
      const endMonth = queryEndTime.getMonth();

      // Calculate months difference
      const monthsDiff = (endYear - startYear) * 12 + (endMonth - startMonth);
      const monthsToGenerate = Math.min(monthsDiff + 1, 12); // Limit to 12 months max

      for (let i = 0; i < monthsToGenerate; i++) {
        const date = new Date(queryStartTime);
        date.setMonth(date.getMonth() + i);
        const dateKey = date.toLocaleDateString('default', { year: 'numeric', month: '2-digit' });

        dates.set(dateKey, {
          numberOfRefund: 0, // 退款次数
          refundAmount: 0, // 退款金额

          paidAmount: 0, // 支付金额
          numberOfPaid: 0, // 支付次数
          unpaidAmount: 0, // 未支付金额
        });
      }
    }

    // 处理退款数据
    for(const item of studentProductRefund) {
      const dateKey = formatDate(item.paymentTime);
      if(!dates.has(dateKey)) {
        dates.set(dateKey, {
          numberOfRefund: 0, // 退款次数
          refundAmount: 0, // 退款金额

          paidAmount: 0, // 支付金额
          numberOfPaid: 0, // 支付次数
          unpaidAmount: 0, // 未支付金额
        });
      }

      const dateData = dates.get(dateKey);
      if(item.status === 'approved') {
        dateData.numberOfRefund += 1;
        dateData.refundAmount += Number.parseFloat(item.amount || 0);
      }
    }

    for(const item of studentProductRecord) {
      const dateKey = formatDate(item.paymentTime);
      if(!dates.has(dateKey)) {
        dates.set(dateKey, {
          numberOfRefund: 0, // 退款次数
          refundAmount: 0, // 退款金额
          paidAmount: 0, // 支付金额
          numberOfPaid: 0, // 支付次数
          unpaidAmount: 0, // 未支付金额
        });
      }
      const dateData = dates.get(dateKey);
      if(item.status === 'done') {
        dateData.numberOfPaid += 1;
        dateData.paidAmount += Number.parseFloat(item.amountPaid || 0);
      } else if(item.status === 'arrears') {
        dateData.unpaidAmount += Number.parseFloat(item.amountUnpaid || 0);
      }
    }

    // Convert to array and sort
    const transformedDates = Array.from(dates.entries()).map(([date, values]) => ({
      date,
      ...values
    }));

    // 根据日期排序
    transformedDates.sort((a, b) => {
      if (checkType === 'daily') {
        return new Date(a.date) - new Date(b.date);
      } else if (checkType === 'month') {
        // For monthly reports, parse the date properly before comparing
        const [aYear, aMonth] = a.date.split('/');
        const [bYear, bMonth] = b.date.split('/');
        return new Date(aYear, aMonth - 1) - new Date(bYear, bMonth - 1);
      }
      return 0;
    });

    return this._formatSalesOverviewResponse(transformedDates, checkType);
  },

  /**
   * Format sales overview response
   * @private
   */
  _formatSalesOverviewResponse(transformedDates, checkType) {
    const dateLabels = [];
    const numberOfRefundList = [];  // 退款次数
    const refundAmountList = [];  // 退款金额
    const paidAmountList = [];  // 支付金额
    const numberOfPaidList = [];  // 支付次数
    const unpaidAmountList = [];  // 未支付金额

    for(const item of transformedDates) {
      if(checkType === 'daily') {
        dateLabels.push(item.date);
      } else if(checkType === 'month') {
        dateLabels.push(item.date.split('年')[1]);
      }
      numberOfRefundList.push(item.numberOfRefund);
      refundAmountList.push(item.refundAmount);
      paidAmountList.push(item.paidAmount);
      numberOfPaidList.push(item.numberOfPaid);
      unpaidAmountList.push(item.unpaidAmount);
    }

    const totalPaidAmount = paidAmountList.reduce((acc, curr) => acc + curr, 0);
    const totalNumberOfPaid = numberOfPaidList.reduce((acc, curr) => acc + curr, 0);

    const totalRefundAmount = refundAmountList.reduce((acc, curr) => acc + curr, 0);
    const totalNumberOfRefund = numberOfRefundList.reduce((acc, curr) => acc + curr, 0);

    const totalUnpaidAmount = unpaidAmountList.reduce((acc, curr) => acc + curr, 0);

    return {
      datasets: {
        dateLabel: dateLabels,
        numberOfRefund: numberOfRefundList,
        refundAmount: refundAmountList,
        paidAmount: paidAmountList,
        numberOfPaid: numberOfPaidList,
      },
      totals: {
        totalPaidAmount,
        totalNumberOfPaid,
        totalRefundAmount,
        totalNumberOfRefund,
        totalUnpaidAmount
      }
    };
  },
};

/**
 * Combined finance service containing all finance-related business logic
 */
const financeService = {
  ...service1,
  ...service2,
  ...service3,
  ...service4
};

export default financeService;
