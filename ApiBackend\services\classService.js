import { v4 as uuidv4 } from 'uuid';
import { TwitterSnowflake } from "@sapphire/snowflake";
import { getDaysInRange, getTuesdaysInWeeks, getWeekDayDatesInRange } from "../utils/getWeekdayDate.js";
import getWeekStartAndEnd from '../utils/getWeekStartAndEnd.js';
import { sendToSpecifiedUser } from '../libs/websocket.js';

// 确保时间字符串格式化为两位数小时
function formatTimeString(timeStr) {
    const [hours, minutes] = timeStr.split(':');
    return `${hours.padStart(2, '0')}:${minutes}`;
}

export class ClassService {
    constructor(prisma, redis, pg) {
        this.prisma = prisma;
        this.redis = redis;
        this.pg = pg;
    }

    async createTempSchedule(data, institutionId) {
        const { name, teacherId, courseId, classRoomId, maxStudentCount, weekdays, reservation, attendance, leave } = data;
        const startDate = new Date();
        const { startOfWeek, endOfWeek } = getWeekStartAndEnd(startDate);
        
        const dates = weekdays.map(weekday => ({
            startDate: getWeekDayDatesInRange(startOfWeek, endOfWeek, weekday.day, 'yyyy-MM-dd')[0],
            weekDay: weekday.day,
            startTime: weekday.startTime,
            endTime: weekday.endTime,
        }));

        const scheduleData = dates.map(item => ({
            id: uuidv4(),
            name, 
            teacherId,
            courseId,
            classRoomId,
            maxStudentCount,
            startDate: new Date(item.startDate).getTime(),
            weekDay: item.weekDay,
            startTime: item.startTime,
            endTime: item.endTime,
            currentWeeks: 1,
            totalWeeks: 1,
            institutionId
        }));

        return await this.prisma.ClassesSchedule.createMany({
            data: scheduleData
        });
    }

    async getAllSchedules(query, institutionId) {
        const { startDate, endDate, teacher } = query;
        let sDate = new Date();
        let eDate = new Date();

        if (startDate) {
            sDate = new Date(startDate);
        }
        if (endDate) {
            eDate = new Date(endDate);
        }
        if (!startDate && !endDate) {
            const { startOfWeek, endOfWeek } = getWeekStartAndEnd(new Date());
            sDate = new Date(startOfWeek);
            eDate = new Date(endOfWeek);
        }
        sDate.setHours(0, 0, 0, 0);
        eDate.setHours(23, 59, 59, 999);

        const cacheKey = `schedules:${institutionId}:${sDate.getTime()}-${eDate.getTime()}:${teacher}`;
        let cached = await this.redis.get(cacheKey);

        if (cached) {
            return JSON.parse(cached);
        }

        let teacherCondition = '';
        let queryParams = [institutionId, sDate.getTime(), eDate.getTime()];
        if (teacher) {
            teacherCondition = `AND cs."teacherId" = $4`;
            queryParams.push(teacher);
        }

        const lockKey = `${cacheKey}:lock`;
        let lock = await this.redis.set(lockKey, 1, 'NX', 'EX', 5);
        if (!lock) {
            await new Promise(resolve => setTimeout(resolve, 100));
            cached = await this.redis.get(cacheKey);
            if (cached) {
                return JSON.parse(cached);
            }
        }

        const sqlQuery = `
            SELECT 
                cs.id,
                cs.name,
                cs."startDate",
                cs."weekDay",
                cs."startTime",
                cs."endTime",
                cs."maxStudentCount",
                cs.subject,
                t.name as "teacherName",
                c."type" as "classesType",
                c.name as "className",
                co.name as "courseName",
                count(sws.id) as "studentCount"
            FROM 
                classes_schedules cs
            LEFT JOIN 
                users t ON cs."teacherId" = t.id
            LEFT JOIN 
                classes c ON cs."classesId" = c.id
            LEFT JOIN 
                courses co ON cs."courseId" = co.id
            LEFT JOIN
                student_weekly_schedules sws ON cs.id = sws."classesScheduleId"
            WHERE 
                cs."institutionId" = $1
                AND cs."startDate" >= $2
                AND cs."startDate" <= $3
                ${teacherCondition}
            GROUP BY
                cs.id, t.name, c.name, co.name,c.type
        `;

        const { rows } = await this.pg.query(sqlQuery, queryParams);
        const random = Math.floor(Math.random() * 180) + 120;
        await this.redis.set(cacheKey, JSON.stringify(rows), 'EX', (60 * 60 * 24) + random);
        await this.redis.del(lockKey);

        return rows;
    }

    async getClasses(query, institutionId) {
        const { page = 1, pageSize = 10, name, teacherId } = query;
        const skip = (page - 1) * pageSize;
        const take = pageSize;
      
        const where = {
            institutionId,
            ...(name ? {
                name: {
                    contains: name,
                    mode: 'insensitive'
                }
            } : {}),
            ...(teacherId ? { teacherId } : {}),

        };

      const {startOfWeek,endOfWeek} = getWeekStartAndEnd(new Date());
        const [total, classes] = await Promise.all([
            this.prisma.Classes.count({ where }),
            this.prisma.Classes.findMany({
                where,
                select: {
                    id: true,
                    name: true,
                    startDate: true,
                    endDate: true,
                    status: true,
                    teacher: {
                        select: {
                            id: true,
                            name: true
                        }
                    },
                    course: {
                        select: {
                            id: true,
                            name: true,
                        }
                    },
                    ClassesSchedule: {
                        where: {
                        startDate: {
                            gte: BigInt(startOfWeek.getTime()),
                            lte: BigInt(endOfWeek.getTime())
                          }
                        },
                        select: {
                            currentWeeks: true,
                            totalWeeks: true,
                          }
                    }
                },
                skip,
                take,
                orderBy: {
                    createdAt: 'desc'
                }
            })
        ]);
        // console.log(classes, "1111111111111111111111111111111111");

        const transformedResult = classes.map(item => {
            // console.log(item, "2222222222222222222222222222222222");
            const currentWeeks = item.ClassesSchedule.length > 0 ? item.ClassesSchedule[0].currentWeeks : null;
            const totalWeeks = item.ClassesSchedule.length > 0 ? item.ClassesSchedule[0].totalWeeks : null;
            delete item.ClassesSchedule;
            return {
                ...item,
                startDate: item.startDate ? Number(item.startDate) : null,
                endDate: item.endDate ? Number(item.endDate) : null,
                currentWeeks,
                totalWeeks,
            };
        });

        return {
            page,
            pageSize,
            total,
            list: transformedResult
        };
    }

    // 创建班级
    async createClass(data, institutionId, userId) {
        const {
            name, maxStudentCount = 20, teacherId, recurrenceType,
            endType, startDate, endDate, courseId, times,
            weekdays, type, daily, reservation, attendance, leave, classRoomId
        } = data;

        // 使用数据库事务确保数据一致性和提升性能
        return await this.prisma.$transaction(async (tx) => {
            const classesId = uuidv4();

            // 预先计算所有需要的数据，避免重复计算
            let scheduleData = [];
            let finalEndDate = endDate;
            let finalTimes = times;

            // 根据重复类型预先计算所有日程数据
            if (recurrenceType === 'weekly') {
                if (!weekdays || !Array.isArray(weekdays) || weekdays.length === 0) {
                    throw new Error('请选择周几');
                }

                // 优化：一次性计算所有日期，避免重复调用
                const allDates = [];
                for (const item of weekdays) {
                    const dateList = endType === 'number_of_times' ?
                        getTuesdaysInWeeks(startDate, times, item.day) :
                        getTuesdaysInWeeks(startDate, times, item.day);

                    for (const date of dateList) {
                        allDates.push({
                            day: item.day,
                            startTime: item.startTime,
                            endTime: item.endTime,
                            startDate: date
                        });
                    }
                }

                // 一次性排序
                allDates.sort((a, b) => new Date(a.startDate) - new Date(b.startDate));

                finalTimes = allDates.length;
                finalEndDate = allDates.length > 0 ?
                    new Date(allDates[allDates.length - 1].startDate).getTime() :
                    endDate;

                scheduleData = allDates;

            } else if (recurrenceType === 'daily') {
                const dates = endType === 'times' ?
                    getDaysInRange(startDate, endDate) :
                    getDaysInRange(startDate, endDate, times - 1);

                finalTimes = dates.length;
                finalEndDate = dates.length > 0 ?
                    new Date(dates[dates.length - 1]).getTime() :
                    endDate;

                scheduleData = dates.map(date => ({
                    day: new Date(date).getDay(),
                    startTime: daily.startTime || '00:00',
                    endTime: daily.endTime || '23:59',
                    startDate: date
                }));
            }

            // 创建班级（包含最终计算的数据）
            const classData = {
                id: classesId,
                name,
                teacherId,
                courseId,
                maxStudentCount,
                startDate,
                endType,
                times: finalTimes,
                endDate: finalEndDate,
                type,
                recurrenceType,
                ...(classRoomId && { classRoomId }),
                ...(reservation.enabled ? {
                    isReserve: true,
                    appointmentStartTime: reservation.appointmentStartTime,
                    appointmentEndTime: reservation.appointmentEndTime
                } : {}),
                ...(attendance.enabled ? {
                    isQRCodeAttendance: attendance.studentScan,
                    isAutoCheckIn: attendance.autoSystem
                } : {}),
                ...(leave.enabled ? {
                    isOnLeave: true,
                    leaveDeadline: leave.leaveDeadline
                } : {}),
                institutionId
            };

            // 批量生成UUID，减少函数调用开销
            const classesTimeIds = weekdays.map(() => uuidv4());
            const scheduleIds = scheduleData.map(() => uuidv4());

            // 准备班级时间详细表数据（使用createMany提升性能）
            const classesTimeData = weekdays.map((item, index) => {
                const dateList = endType === 'number_of_times' ?
                    getTuesdaysInWeeks(startDate, times, item.day) :
                    getTuesdaysInWeeks(startDate, times, item.day);

                const itemEndDate = dateList.length > 0 ?
                    new Date(dateList[dateList.length - 1]).getTime().toString() :
                    endDate.toString();

                return {
                    id: classesTimeIds[index],
                    classesId,
                    weekDay: item.day,
                    startTime: item.startTime,
                    endTime: item.endTime,
                    recurrenceType,
                    endType,
                    startDate: startDate.toString(),
                    endDate: itemEndDate,
                    institutionId
                };
            });

            // 准备班级计划数据
            const classesScheduleBaseData = {
                classesId,
                courseId,
                teacherId,
                maxStudentCount,
                subject: name,
                ...(reservation.enabled ? {
                    isReserve: true,
                    appointmentStartTime: reservation.appointmentStartTime,
                    appointmentEndTime: reservation.appointmentEndTime
                } : {}),
                ...(attendance.enabled ? {
                    isQRCodeAttendance: attendance.studentScan,
                    isAutoCheckIn: attendance.autoSystem
                } : {}),
                ...(leave.enabled ? {
                    isOnLeave: true,
                    leaveDeadline: leave.leaveDeadline
                } : {}),
                institutionId
            };

            const finalClassesScheduleData = scheduleData.map((item, index) => ({
                id: scheduleIds[index],
                startDate: new Date(item.startDate).getTime(),
                weekDay: item.day,
                startTime: item.startTime,
                endTime: item.endTime,
                currentWeeks: index + 1,
                totalWeeks: finalTimes,
                ...classesScheduleBaseData
            }));

            // 使用并行操作提升性能：同时创建班级、班级时间表和班级计划
            await Promise.all([
                // 创建班级
                tx.classes.create({ data: classData }),
                // 批量创建班级时间详细表
                weekdays.length > 0 ? tx.classesTime.createMany({ data: classesTimeData }) : Promise.resolve(),
                // 批量创建班级计划
                finalClassesScheduleData.length > 0 ? tx.classesSchedule.createMany({ data: finalClassesScheduleData }) : Promise.resolve()
            ]);

            return classesId;
        });
    }

    // 检查老师空闲时间 - 性能优化版本
    async checkTeacherFree(data, institutionId) {
        const { times, recurrenceType, daily, startDate, endDate, endType, weekdays } = data;

        // 获取全部老师
        const teachersResult = await this.prisma.userInstitution.findMany({
            where: { institutionId },
            select: {
                user: {
                    select: {
                        id: true,
                        name: true,
                    }
                }
            }
        });

        const teachers = teachersResult.map(item => item.user);
        const teacherIds = teachers.map(item => item.id);

        // 如果没有老师，直接返回空数组
        if (teacherIds.length === 0) {
            return teachers;
        }

        // 预先计算所有时间段，避免重复计算
        let timeSlots = [];

        if (recurrenceType === 'weekly') {
            if (!weekdays || !Array.isArray(weekdays) || weekdays.length === 0) {
                // 如果没有指定周几，所有老师都空闲
                teachers.forEach(teacher => { teacher.freeTime = true; });
                return teachers;
            }

            // 优化：预先计算所有日期和时间段
            const allTimeSlots = [];
            for (const wkItem of weekdays) {
                const dates = endType === 'number_of_times' ?
                    getTuesdaysInWeeks(startDate, times, wkItem.day) :
                    getTuesdaysInWeeks(startDate, times, wkItem.day);

                // 预先格式化时间字符串，避免重复调用
                const formattedStartTime = formatTimeString(wkItem.startTime);
                const formattedEndTime = formatTimeString(wkItem.endTime);

                for (const date of dates) {
                    allTimeSlots.push({
                        startDate: new Date(date).getTime(),
                        weekDay: wkItem.day,
                        startTime: formattedStartTime,
                        endTime: formattedEndTime
                    });
                }
            }

            timeSlots = allTimeSlots;

        } else if (recurrenceType === 'daily') {
            const dates = endType === 'times' ?
                getDaysInRange(startDate, endDate) :
                getDaysInRange(startDate, endDate, times - 1);

            // 预先格式化时间字符串
            const formattedStartTime = formatTimeString(daily.startTime);
            const formattedEndTime = formatTimeString(daily.endTime);

            timeSlots = dates.map(date => ({
                startDate: new Date(date).getTime(),
                weekDay: new Date(date).getDay(),
                startTime: formattedStartTime,
                endTime: formattedEndTime
            }));
        }

        // 如果没有时间段，所有老师都空闲
        if (timeSlots.length === 0) {
            teachers.forEach(teacher => { teacher.freeTime = true; });
            return teachers;
        }

        // 构建单个查询条件，避免N+1查询问题
        const conflictConditions = timeSlots.map(slot => ({
            teacherId: { in: teacherIds },
            startDate: slot.startDate,
            ...(recurrenceType === 'weekly' ? { weekDay: slot.weekDay } : {}),
            AND: [
                { startTime: { lte: slot.endTime } },
                { endTime: { gte: slot.startTime } }
            ]
        }));

        // 使用单个查询获取所有冲突的老师，大幅提升性能
        const conflictingTeachers = await this.prisma.classesSchedule.findMany({
            where: {
                OR: conflictConditions
            },
            select: {
                teacherId: true,
                teacher: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            }
        });

        // 使用Set进行高效去重，O(1)时间复杂度
        const busyTeacherIds = new Set(conflictingTeachers.map(item => item.teacherId));

        // 设置老师的空闲状态
        teachers.forEach(teacher => {
            teacher.freeTime = !busyTeacherIds.has(teacher.id);
        });

        return teachers;
    }

    async getClassesSelect(query, institutionId) {
        const { page = 1, pageSize = 10, name } = query;
        const skip = (page - 1) * pageSize;
        const take = pageSize;

        const [result, total] = await Promise.all([
            this.prisma.classes.findMany({
                where: {
                    institutionId,
                    ...(name ? { name: { contains: name, mode: 'insensitive' } } : {})
                },
                select: {
                    id: true,
                    name: true,
                    startDate: true,
                    teacher: {
                        select: {
                            id: true,
                            name: true
                        }
                    }
                },
                skip,
                take
            }),
            this.prisma.classes.count({
                where: { 
                    institutionId,
                    ...(name ? { name: { contains: name, mode: 'insensitive' } } : {})
                }
            })
        ]);

        return {
            list: result.map(item => ({
                ...item,
                startDate: item.startDate ? Number(item.startDate) : null
            })),
            total,
            page,
            pageSize
        };
    }

    async addClassCourse(classesId, courseId, institutionId) {
        const id = TwitterSnowflake.generate().toString();
        
        await this.prisma.courseClasses.create({
            data: {
                id,
                classesId,
                courseId,
                institutionId
            }
        });

        // 更新班级课程信息
        await this.prisma.classes.update({
            where: { id: classesId },
            data: { courseId }
        });
    }

    async updateClassCourse(classesId, courseId, institutionId) {
        const currentTime = new Date().getTime();
        
        const courseClasses = await this.prisma.courseClasses.findFirst({
            where: {
                classesId,
                institutionId
            }
        });

        if (!courseClasses) {
            throw new Error('班级课程不存在');
        }

        await Promise.all([
            // 更新课程关联
            this.prisma.courseClasses.update({
                where: { id: courseClasses.id },
                data: { courseId }
            }),
            // 更新未来课节的课程
            this.prisma.classesSchedule.updateMany({
                where: {
                    classesId,
                    startDate: { gt: currentTime },
                    institutionId
                },
                data: { courseId }
            })
        ]);
    }

    // 添加学生到班级 - 性能优化版本
    async addStudents(classesId, studentIds, institutionId, operatorId) {
        const currentTime = new Date().getTime();

        // 使用数据库事务确保数据一致性和提升性能
        return await this.prisma.$transaction(async (tx) => {
            // 并行执行：检查现有学生和获取未来课节
            const [existingStudents, futureSchedules] = await Promise.all([
                tx.studentClasses.findMany({
                    where: {
                        classesId,
                        studentId: { in: studentIds },
                        institutionId
                    },
                    select: {
                        studentId: true
                    }
                }),
                tx.classesSchedule.findMany({
                    where: {
                        classesId,
                        startDate: { gt: currentTime },
                        institutionId
                    },
                    select: {
                        id: true
                    }
                })
            ]);

            // 使用Set进行高效去重，O(1)时间复杂度
            const existingStudentIds = new Set(existingStudents.map(s => s.studentId));
            const newStudentIds = studentIds.filter(id => !existingStudentIds.has(id));

            if (newStudentIds.length === 0) {
                throw new Error('所选学生已在班级中');
            }

            // 批量生成UUID，减少函数调用开销
            const studentClassesUUIDs = newStudentIds.map(() => uuidv4());
            const totalScheduleRecords = newStudentIds.length * futureSchedules.length;
            const scheduleUUIDs = Array.from({ length: totalScheduleRecords }, () => uuidv4());

            // 准备学生班级关联数据
            const studentClassesData = newStudentIds.map((studentId, index) => ({
                id: studentClassesUUIDs[index],
                classesId,
                studentId,
                institutionId,
                operatorId,
                joinDate: currentTime,
                operatorTime: currentTime
            }));

            // 优化：使用单次循环生成所有课节记录
            const scheduleStudentData = [];
            let uuidIndex = 0;
            for (let i = 0; i < newStudentIds.length; i++) {
                for (let j = 0; j < futureSchedules.length; j++) {
                    scheduleStudentData.push({
                        id: scheduleUUIDs[uuidIndex++],
                        classesScheduleId: futureSchedules[j].id,
                        studentId: newStudentIds[i],
                        studentType: 'fixed',
                        institutionId
                    });
                }
            }

            // 并行执行所有创建操作
            await Promise.all([
                tx.studentClasses.createMany({
                    data: studentClassesData
                }),
                scheduleStudentData.length > 0 ? tx.studentWeeklySchedule.createMany({
                    data: scheduleStudentData
                }) : Promise.resolve()
            ]);

            return {
                addedStudents: newStudentIds.length,
                createdScheduleRecords: scheduleStudentData.length
            };
        });
    }

    // 移除学生从班级 - 性能优化版本
    async removeStudent(classesId, studentId, institutionId) {
        const currentTime = new Date().getTime();

        // 使用数据库事务确保数据一致性和提升性能
        return await this.prisma.$transaction(async (tx) => {
            // 并行执行删除操作，提升性能
            const [deletedClassRecords, deletedScheduleRecords] = await Promise.all([
                // 删除学生班级关联
                tx.studentClasses.deleteMany({
                    where: {
                        classesId,
                        studentId,
                        institutionId
                    }
                }),
                // 删除未来课节的学生记录
                tx.studentWeeklySchedule.deleteMany({
                    where: {
                        studentId,
                        institutionId,
                        classesSchedule: {
                            classesId,
                            startDate: { gt: currentTime }
                        }
                    }
                })
            ]);

            return {
                deletedClassRecords: deletedClassRecords.count,
                deletedScheduleRecords: deletedScheduleRecords.count
            };
        });
    }

    async updateClass(classesId, data, institutionId) {
        const {
            name, teacherId, status, courseId, isReserve,
            appointmentStartTime, appointmentEndTime, isQRCodeAttendance,
            isAutoCheckIn, isOnLeave, leaveDeadline, isShowWeekCount,
            classRoomId, maxStudentCount
        } = data;

        const updateData = {
            ...(maxStudentCount && { maxStudentCount: Number(maxStudentCount) }),
            ...(classRoomId && { classRoomId }),
            ...(isReserve && {
                isReserve: true,
                appointmentStartTime: Number(appointmentStartTime),
                appointmentEndTime: Number(appointmentEndTime)
            }),
            ...(isQRCodeAttendance && { isQRCodeAttendance: true }),
            ...(isAutoCheckIn && { isAutoCheckIn: true }),
            ...(isOnLeave && {
                isOnLeave: true,
                leaveDeadline: Number(leaveDeadline)
            }),
            ...(isShowWeekCount && { isShowWeekCount: true }),
            ...(name && { name }),
            ...(teacherId && { teacherId }),
            ...(status && { status })
        };

        await this.prisma.classes.update({
            where: { id: classesId, institutionId },
            data: updateData
        });

        if (courseId) {
            await this.updateClassCourse(classesId, courseId, institutionId);
        }
    }

    // 获取班级详情 - 性能优化版本
    async getClassDetail(classesId, institutionId) {
        // 使用单个查询获取所有相关数据，避免N+1查询问题
        const result = await this.prisma.classes.findFirst({
            where: {
                id: classesId,
                institutionId
            },
            select: {
                id: true,
                name: true,
                endDate: true,
                startDate: true,
                remarks: true,
                times: true,
                status: true,
                type: true,
                isReserve: true,
                maxStudentCount: true,
                appointmentStartTime: true,
                appointmentEndTime: true,
                isQRCodeAttendance: true,
                isAutoCheckIn: true,
                isOnLeave: true,
                isShowWeekCount: true,
                leaveDeadline: true,
                timeDetails: {
                    select: {
                        id: true,
                        weekDay: true,
                        startTime: true,
                        endTime: true
                    }
                },
                students: {
                    select: {
                        id: true,
                        student: {
                            select: {
                                id: true,
                                name: true,
                                phone: true,
                                type: true
                            }
                        },
                        operatorTime: true,
                        operator: {
                            select: {
                                name: true
                            }
                        },
                        type: true,
                        joinDate: true
                    }
                },
                course: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                teacher: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                // 优化：在单个查询中包含课程安排，避免额外查询
                ClassesSchedule: {
                    select: {
                        id: true,
                        startDate: true,
                        weekDay: true,
                        startTime: true,
                        endTime: true,
                        currentWeeks: true,
                        totalWeeks: true,
                        subject: true,
                        courses: {
                            select: {
                                id: true,
                                name: true
                            }
                        },
                        teacher: {
                            select: {
                                id: true,
                                name: true
                            }
                        },
                        StudentWeeklySchedule: {
                            select: {
                                id: true,
                                status: true,
                                student: {
                                    select: {
                                        id: true,
                                        name: true
                                    }
                                }
                            }
                        }
                    },
                    orderBy: {
                        startDate: 'asc'
                    }
                }
            }
        });

        if (!result) {
            throw new Error('班级不存在');
        }

        // 优化：使用单次遍历进行数据转换
        const transformedStudents = result.students.map(item => ({
            ...item,
            joinDate: item.joinDate ? Number(item.joinDate) : null,
            operatorTime: item.operatorTime ? Number(item.operatorTime) : null
        }));

        const transformedSchedules = result.ClassesSchedule.map(item => ({
            ...item,
            startDate: item.startDate ? Number(item.startDate) : null
        }));

        return {
            ...result,
            startDate: result.startDate ? Number(result.startDate) : null,
            endDate: result.endDate ? Number(result.endDate) : null,
            times: result.times ? Number(result.times) : null,
            students: transformedStudents,
            classesSchedule: transformedSchedules,
            // 移除原始的ClassesSchedule字段，避免数据重复
            ClassesSchedule: undefined
        };
    }

    async getClassSchedules(classesId, institutionId) {
        const schedules = await this.prisma.classesSchedule.findMany({
            where: {
                classesId,
                institutionId
            },
            select: {
                id: true,
                startDate: true,
                startTime: true,
                endTime: true,
                currentWeeks: true,
                courses: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                teacher: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                StudentWeeklySchedule: {
                    select: {
                        id: true,
                        status: true,
                        studentType: true,
                        operator: {
                            select: {
                                name: true
                            }
                        },
                        operatorTime: true,
                        student: {
                            select: {
                                name: true,
                                phone: true
                            }
                        }
                    }
                }
            }
        });

        return schedules.map(item => ({
            ...item,
            startDate: item.startDate ? Number(item.startDate) : null,
            StudentWeeklySchedule: item.StudentWeeklySchedule.map(student => ({
                ...student,
                operatorTime: student.operatorTime ? Number(student.operatorTime) : null
            }))
        }));
    }

    async getScheduleDetail(scheduleId, institutionId) {
        const schedule = await this.prisma.classesSchedule.findFirst({
            where: {
                id: scheduleId,
                institutionId
            },
            select: {
                id: true,
                name: true,
                startDate: true,
                weekDay: true,
                startTime: true,
                endTime: true,
                currentWeeks: true,
                totalWeeks: true,
                maxStudentCount: true,
                isReserve: true,
                appointmentStartTime: true,
                appointmentEndTime: true,
                isQRCodeAttendance: true,
                isAutoCheckIn: true,
                isOnLeave: true,
                leaveDeadline: true,
                isShowWeekCount: true,
                subject: true,
                StudentWeeklySchedule: {
                    select: {
                        id: true,
                        student: {
                            select: {
                                id: true,
                                name: true,
                                phone: true,
                                gender: true
                            }
                        },
                        status: true,
                        studentType: true,
                        operator: {
                            select: {
                                name: true
                            }
                        },
                        operatorTime: true
                    }
                },
                courses: {
                    select: {
                        id: true,
                        name: true,
                        type: true,
                        deductionPerClass: true
                    }
                },
                teacher: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                classes: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            }
        });

        if (!schedule) {
            throw new Error('课节不存在');
        }

        return {
            ...schedule,
            startDate: schedule.startDate ? Number(schedule.startDate) : null,
            StudentWeeklySchedule: schedule.StudentWeeklySchedule.map(item => ({
                ...item,
                operatorTime: item.operatorTime ? Number(item.operatorTime) : null
            }))
        };
    }

    async updateSchedule(scheduleId, data, institutionId) {
        const {
            name, startTime, endTime, subject, courseId, teacherId,
            date, isReserve, appointmentStartTime, appointmentEndTime,
            isQRCodeAttendance, isAutoCheckIn, isOnLeave, leaveDeadline,
            isShowWeekCount, classRoomId, maxStudentCount
        } = data;

        await this.prisma.classesSchedule.update({
            where: {
                id: scheduleId,
                institutionId
            },
            data: {
                ...(maxStudentCount && { maxStudentCount: Number(maxStudentCount) }),
                ...(name && { name }),
                ...(startTime && { startTime }),
                ...(endTime && { endTime }),
                ...(subject && { subject }),
                ...(courseId && { courseId }),
                ...(teacherId && { teacherId }),
                ...(date && { startDate: date }),
                ...(isReserve && {
                    isReserve: true,
                    appointmentStartTime: Number(appointmentStartTime),
                    appointmentEndTime: Number(appointmentEndTime)
                }),
                ...(isQRCodeAttendance && { isQRCodeAttendance: true }),
                ...(isAutoCheckIn && { isAutoCheckIn: true }),
                ...(isOnLeave && {
                    isOnLeave: true,
                    leaveDeadline: Number(leaveDeadline),
                    isShowWeekCount
                }),
                ...(classRoomId && { classRoomId })
            }
        });
    }

    async addScheduleStudent(scheduleId, studentData, institutionId, operatorId) {
        const { students, type, studentIds } = studentData;

        const schedule = await this.prisma.classesSchedule.findFirst({
            where: {
                id: scheduleId,
                institutionId
            },
            select: {
                teacherId: true
            }
        });


        if(studentIds.length > 0) {
            await this.prisma.studentWeeklySchedule.createMany({
                data: studentIds.map(studentId => ({
                    classesScheduleId: scheduleId,
                studentId,
                    studentType: type,
                    operatorId,
                    operatorTime: new Date().getTime(),
                    institutionId
                }))
            });
        } else {
            await this.prisma.studentWeeklySchedule.create({
                data: {
                    classesScheduleId: scheduleId,
                    studentId: students.studentId,
                    studentType: students.type,
                    operatorId,
                    operatorTime: new Date().getTime(),
                    institutionId
                }
            });
        }

        if (type === 'trial' && schedule.teacherId) {
            let student;
            if(studentIds.length > 0) {
                student = await this.prisma.student.findMany({
                    where: {
                        id: { in: studentIds }
                    },
                    select: {
                        name: true
                    }
                }); 
            }else {
                student = await this.prisma.student.findFirst({
                    where: {
                        id: students.studentId
                    },
                    select: {
                        name: true
                    }
                });
            }
            student = student.map(item => item.name);
            for(let i = 0; i < student.length; i++) {
                const payload = {
                    type: 'TRIAL_STUDENT_ADDED',
                    title: '试听学员添加',
                    student: {
                        name: student[i]
                    }
                };
                await sendToSpecifiedUser(this.fastify, schedule.teacherId, institutionId, payload);
            }
        }
    }

    async removeScheduleStudents(scheduleId, studentIds, institutionId) {
        await this.prisma.studentWeeklySchedule.deleteMany({
            where: {
                classesScheduleId: scheduleId,
                studentId: { in: studentIds },
                institutionId
            }
        });
    }

    async updateStudentAttendance(scheduleId, studentId, status, institutionId, operatorId) {
        const result = await this.prisma.studentWeeklySchedule.findFirst({
            where: {
                classesScheduleId: scheduleId,
                studentId,
                institutionId
            },
            select: {
                id: true,
                studentProductId: true,
                attendanceCount: true,
                studentType: true,
                status: true,
                attendanceAmount: true,
                classesSchedule: {
                    select: {
                        courses: {
                            select: {
                                deductionPerClass: true,
                                isDeductOnAttendance: true,
                                isDeductOnLeave: true,
                                isDeductOnAbsence: true,
                                ProductCourse: {
                                    select: {
                                        id: true,
                                        productId: true
                                    }
                                }
                            }
                        }
                    }
                },
                student: {
                    select: {
                        StudentProduct: {
                            where: {
                                enrollmentStatus: 'active'
                            },
                            select: {
                                id: true,
                                productId: true,
                                remainingSessionCount: true,
                                remainingBalance: true,
                                sessionUnitPrice: true,
                                paymentStatus: true,
                                startDate: true,
                                endDate: true,
                                product: {
                                    select: {
                                        id: true,
                                        timeLimitedUsage: true,
                                        timeLimitType: true,
                                        validTimeRange: true
                                    }
                                }
                            },
                            orderBy: {
                                createdAt: 'desc'
                            }
                        }
                    }
                }
            }
        });

        if (!result) {
            throw new Error('学员不存在');
        }

        if (result.studentType === 'trial') {
            throw new Error('试听学员不能考勤');
        }

        // 如果状态相同，退还课时
        if (result.status === status) {
            if (result.studentProductId) {
                const productResult = await this.prisma.studentProduct.findFirst({
                    where: { id: result.studentProductId }
                });

                const newRemainingCount = Number(productResult.remainingSessionCount) + Number(result.attendanceCount);
                const newRemainingBalance = Number(productResult.remainingBalance) + Number(result.attendanceAmount);

                await this.prisma.studentProduct.update({
                    where: { id: productResult.id },
                    data: {
                        remainingSessionCount: Number(newRemainingCount).toFixed(2),
                        remainingBalance: Number(newRemainingBalance).toFixed(2),
                        enrollmentStatus: newRemainingCount > 0 ? 'active' : 'completed'
                    }
                });
            }

            await this.prisma.studentWeeklySchedule.update({
                where: { id: result.id },
                data: {
                    status: 'unattended',
                    attendanceCount: 0,
                    studentProductId: null,
                    operatorId: null,
                    operatorTime: null,
                    attendanceAmount: 0,
                    productId: null
                }
            });

            return;
        }

        // 处理考勤扣课时
        const { courses } = result.classesSchedule;
        const { deductionPerClass, isDeductOnAttendance, isDeductOnLeave, isDeductOnAbsence } = courses;

        let product = null;
        let productIndex = 0;
        let isFound = false;
        let productCourse = null;

        if (result.student.StudentProduct.length < 1) {
            throw new Error('学员没有产品');
        }

        while (productIndex < result.student.StudentProduct.length) {
            product = result.student.StudentProduct[productIndex];
            productCourse = courses.ProductCourse.filter(item => item.productId === product.productId);
            
            if (Number(product.remainingSessionCount) >= Number(deductionPerClass) && productCourse.length > 0) {
                isFound = true;
                break;
            }
            productIndex++;
        }

        if (productCourse.length === 0) {
            throw new Error('没有课程对应的套餐');
        }

        if (!isFound) {
            throw new Error('当前产品课时不足');
        }

        // 处理限时限次消费
        if (product.product.validTimeRange === 'consumption-date' && !product.startDate) {
            const startDate = new Date();
            const endDate = new Date();
            const timeLimitedUsage = Number(product.product.timeLimitedUsage);
            
            if (product.product.timeLimitType === 'daily') {
                endDate.setDate(endDate.getDate() + timeLimitedUsage);
            } else {
                endDate.setMonth(endDate.getMonth() + timeLimitedUsage);
            }

            await this.prisma.studentProduct.update({
                where: { id: product.id },
                data: {
                    startDate: startDate.getTime(),
                    endDate: endDate.getTime()
                }
            });
        }

        // 计算扣除课时和金额
        let newRemainingCount = Number(product.remainingSessionCount);
        const pricePerClass = Number(product.sessionUnitPrice) * Number(deductionPerClass);
        let remainingBalance = Number(product.remainingBalance);

        if (
            (status === 'attendance' && isDeductOnAttendance) ||
            (status === 'leave' && isDeductOnLeave) ||
            (status === 'absent' && isDeductOnAbsence)
        ) {
            newRemainingCount -= Number(deductionPerClass);
            remainingBalance -= pricePerClass;
        }

        // 更新学员产品
        const newUnitPrice = remainingBalance / newRemainingCount;
        await this.prisma.studentProduct.update({
            where: { id: product.id },
            data: {
                remainingSessionCount: Number(newRemainingCount).toFixed(3),
                remainingBalance: Number(remainingBalance).toFixed(3),
                sessionUnitPrice: Number(newUnitPrice).toFixed(3),
                enrollmentStatus: newRemainingCount > 0 ? 'active' : 'completed'
            }
        });

        // 更新考勤记录
        await this.prisma.studentWeeklySchedule.update({
            where: { id: result.id },
            data: {
                status,
                attendanceCount: Number(deductionPerClass),
                studentProductId: product.id,
                operatorId,
                operatorTime: new Date().getTime(),
                attendanceAmount: Number(pricePerClass).toFixed(3),
                productId: product.productId
            }
        });
    }

    async getClassStudents(classesId, institutionId) {
        const students = await this.prisma.studentClasses.findMany({
            where: {
                classesId,
                institutionId
            },
            select: {
                joinDate: true,
                type: true,
                student: {
                    select: {
                        phone: true,
                        name: true,
                        type: true
                    }
                },
                operatorTime: true,
                operator: {
                    select: {
                        name: true
                    }
                }
            }
        });

        return students.map(item => ({
            ...item,
            operatorTime: item.operatorTime ? Number(item.operatorTime) : null,
            joinDate: item.joinDate ? Number(item.joinDate) : null
        }));
    }

    async getScheduleAttendance(classesId, scheduleId, institutionId) {
        const attendance = await this.prisma.studentWeeklySchedule.findMany({
            where: {
                classesScheduleId: scheduleId,
                institutionId
            },
            select: {
                id: true,
                status: true,
                operatorTime: true,
                studentType: true,
                student: {
                    select: {
                        id: true,
                        name: true,
                        phone: true
                    }
                },
                operator: {
                    select: {
                        name: true
                    }
                }
            }
        });

        return attendance.map(item => ({
            ...item,
            operatorTime: item.operatorTime ? Number(item.operatorTime) : null
        }));
    }

    async getClassAttendance(classesId, institutionId) {
        const schedules = await this.prisma.classesSchedule.findMany({
            where: {
                classesId,
                institutionId
            },
            select: {
                id: true,
                startDate: true,
                weekDay: true,
                startTime: true,
                endTime: true,
                currentWeeks: true,
                StudentWeeklySchedule: {
                    select: {
                        status: true,
                        student: {
                            select: {
                                id: true,
                                name: true
                            }
                        }
                    }
                }
            }
        });

        return schedules.map(item => ({
            ...item,
            startDate: item.startDate ? Number(item.startDate) : null
        }));
    }

    // 删除班级
    async deleteClass(classesId, institutionId) {
        try{
            const where = {
                id: classesId,
                institutionId
            }
            await this.prisma.$transaction(async (tx) => {
                await tx.classes.delete({
                    where
                });
                await tx.classesSchedule.deleteMany({
                    where
                });
                await tx.studentClasses.deleteMany({
                    where
                });
            });
        }catch(error){
            throw new Error('删除班级失败');
        }
    }
} 
