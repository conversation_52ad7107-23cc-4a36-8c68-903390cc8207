import fp from "fastify-plugin";
import { BaseError, errorHandler } from '../errors/index.js';

async function errorPlugin(fastify, options) {
    // 使用我们的增强错误处理器
    fastify.setErrorHandler(function (error, request, reply) {
        // 使用增强的错误处理器
        errorHandler(error, request, reply);
    });

    // 404 处理
    fastify.setNotFoundHandler(function (request, reply) {
        reply.status(404).send({
            code: 404,
            message: 'Resource not found',
            data: null
        });
    })

    // 统一响应格式的装饰器
    fastify.decorateReply('success', function ({message = 'Success', data = null}) {
        this.send({
            code: 200,
            message,
            data
        });
    })
}

export default fp(errorPlugin, {
    name: 'error-handler'
})