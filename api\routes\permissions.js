export default async function (fastify, opts) {
    fastify.get('/permissions',

        {
            schema: {
                tags: ['permissions']
            },
            handler: async function (request, reply) {
                const operations = await fastify.prisma.permission.findMany({
                    select: {
                        id: true,
                        name: true,
                        operations: {
                            select: {
                                id: true,
                                name: true,
                                code: true,
                            },
                        },
                    },
                });
                reply.success({
                    message: '获取权限成功',
                    data: operations
                })
            }
        }
    );


}