generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                           String                  @id @unique
  account                      String                  @unique
  name                         String?
  avatar                       String?
  password                     String
  phone                        String?
  isShow                       Boolean            @default(false)
  openid                       String?             // 微信唯一标识
  createdAt                    DateTime                @default(now())
  updatedAt                    DateTime                @updatedAt
  active                       Boolean                 @default(true)
  Bill                         Bill[]
  Classes                      Classes[]
  ClassesSchedule              ClassesSchedule[]
  Course                       Course[]
  institutions                 Institution[]
  Notification                 Notification[]
  OperationLog                 OperationLog[]
  StudentClasses               StudentClasses[]
  StudentFollowRecords         StudentFollowRecords[]
  StudentFollowRecordsOperator StudentFollowRecords[]  @relation("StudentFollowRecordsOperatorRelation")
  StudentProductAdjust         StudentProductAdjust[]
  StudentProductRecordOperator StudentProductRecord[]  @relation("OperatorRepRelation")
  StudentProductRecordSales    StudentProductRecord[]  @relation("SalesRepRelation")
  StudentProductRefund         StudentProductRefund[]
  StudentProduct               StudentProduct[]        @relation("OperatorRelation")
  StudentProducts              StudentProduct[]        @relation("SalesRepRelation")
  StudentWeeklySchedule        StudentWeeklySchedule[]
  Student                      Student[]               @relation("FollowerRelation")
  StudentAsOperator            Student[]               @relation("OperatorRelation")
  userInstitutions             UserInstitution[]
  UserNotification             UserNotification[]
  userRoles                    UserRole[]
  StudentProductss             StudentProduct[]        @relation("StudentProductToUser")

  @@index([account])
  @@map("users")
}

model Role {
  id              String           @id @default(uuid())
  name            String           @unique
  code            String           @unique
  description     String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime?        @updatedAt
  institutionId   String?
  rolePermissions RolePermission[]
  userRole        UserRole[]

  @@unique([id, institutionId])
  @@index([code, institutionId])
  @@map("roles")
}

model Permission {
  id              String           @id @default(uuid())
  name            String
  code            String           @unique
  description     String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime?        @updatedAt
  rolePermissions RolePermission[]

  @@index([name])
  @@index([code])
  @@map("permissions")
}

model Menu {
  id             String   @id @default(uuid())
  name           String
  path           String?
  icon           String?
  sort           Int      @default(0)
  hidden         Boolean  @default(false)
  permissionId   String?
  parentId       String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  component      String?
  permissionCode String?
  redirect       String?
  parent         Menu?    @relation("MenuToMenu", fields: [parentId], references: [id])
  children       Menu[]   @relation("MenuToMenu")

  @@index([parentId, permissionId])
  @@map("menus")
}

model UserRole {
  id        String   @id @default(uuid())
  userId    String
  roleId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  role      Role     @relation(fields: [roleId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@index([userId, roleId])
  @@map("user_roles")
}

model RolePermission {
  id           String     @id @default(uuid())
  roleId       String
  permissionId String
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@index([roleId, permissionId])
  @@map("role_permissions")
}

model Institution {
  id                   String                 @id @default(uuid())
  name                 String                 @unique
  subjectName          String?
  uscc                 String?
  logo                 String?
  introduce            String?
  phone                String?
  telePhone            String?
  managerName          String?
  userId               String
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  classes              Classes[]
  ClassesSchedule      ClassesSchedule[]
  Classroom            Classroom[]
  course               Course[]
  InstitutionAddress   InstitutionAddress[]
  user                 User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  Notification         Notification[]
  Product              Product[]
  StudentFollowRecords StudentFollowRecords[]
  StudentProductAdjust StudentProductAdjust[]
  StudentProductRefund StudentProductRefund[]
  StudentProduct       StudentProduct[]
  students             Student[]
  userInstitutions     UserInstitution[]

  @@index([userId])
  @@index([name])
  @@map("institutions")
}

model UserInstitution {
  id            String      @id @default(uuid())
  userId        String
  institutionId String
  isAdmin         Boolean     @default(false)
  status        Boolean?    @default(true)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  institution   Institution @relation(fields: [institutionId], references: [id], onDelete: Cascade)
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([institutionId])
  @@map("user_institution")
}

model OperationLog {
  id            String   @id @default(uuid())
  userId        String
  content       String
  institutionId String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @default(now()) @updatedAt
  describe      String
  operationType String
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("operation_log")
}

model Notification {
  id               String             @id @default(uuid())
  title            String
  content          String
  type             String
  createdById      String
  institutionId    String
  createdAt        DateTime           @default(now())
  updatedAt        DateTime?          @updatedAt
  createdBy        User               @relation(fields: [createdById], references: [id], onDelete: Cascade)
  institution      Institution        @relation(fields: [institutionId], references: [id], onDelete: Cascade)
  UserNotification UserNotification[]

  @@index([institutionId])
  @@index([createdById])
  @@map("notifications")
}

model UserNotification {
  id             String       @id @default(uuid())
  userId         String
  notificationId String
  status         String       @default("unread")
  readAt         BigInt?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime?    @updatedAt
  notification   Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([notificationId])
  @@map("user_notifications")
}

model Classes {
  id                   String            @id @default(uuid())
  name                 String
  teacherId            String?
  institutionId        String
  isFull               Boolean           @default(false)
  startDate            BigInt
  endType              String            @default("number_of_times")
  times                Int?
  endDate              BigInt?
  status               String            @default("active")
  createdAt            DateTime          @default(now())
  updatedAt            DateTime?         @default(now()) @updatedAt
  type                 String?           @default("fixed")
  remarks              String?
  isAutoCheckIn        Boolean           @default(false)
  isOnLeave            Boolean           @default(false)
  isQRCodeAttendance   Boolean           @default(false)
  isReserve            Boolean           @default(false)
  isShowWeekCount      Boolean           @default(true)
  leaveDeadline        Int?
  appointmentEndTime   Int?
  appointmentStartTime Int?
  classRoomId          String?
  maxStudentCount      Int?
  courseId             String?
  classRoom            Classroom?        @relation(fields: [classRoomId], references: [id])
  course               Course?           @relation(fields: [courseId], references: [id])
  institution          Institution       @relation(fields: [institutionId], references: [id], onDelete: Cascade)
  teacher              User?             @relation(fields: [teacherId], references: [id])
  ClassesSchedule      ClassesSchedule[]
  students             StudentClasses[]
  timeDetails          ClassesTime[]     @relation("ClassesToClassesTime")

  @@index([institutionId])
  @@index([teacherId])
  @@map("classes")
}

model ClassesTime {
  id            String    @id @default(uuid())
  classesId     String
  weekDay       Int
  createdAt     DateTime  @default(now())
  updatedAt     DateTime? @default(now()) @updatedAt
  endTime       String
  institutionId String
  startTime     String
  Classes       Classes[] @relation("ClassesToClassesTime")

  @@index([classesId])
  @@map("classes_time")
}

model ClassesSchedule {
  id                    String                  @id @default(uuid())
  classesId             String?
  startDate             BigInt
  weekDay               Int
  startTime             String
  endTime               String
  currentWeeks          Int
  totalWeeks            Int
  courseId              String?
  institutionId         String
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  teacherId             String?
  subject               String?
  maxStudentCount       Int?
  isAutoCheckIn         Boolean                 @default(false)
  isOnLeave             Boolean                 @default(false)
  isQRCodeAttendance    Boolean                 @default(false)
  isShowWeekCount       Boolean                 @default(true)
  leaveDeadline         Int?
  appointmentEndTime    Int?
  appointmentStartTime  Int?
  isReserve             Boolean                 @default(false)
  name                  String?
  classes               Classes?                @relation(fields: [classesId], references: [id], onDelete: Cascade)
  courses               Course?                 @relation(fields: [courseId], references: [id])
  institution           Institution             @relation(fields: [institutionId], references: [id], onDelete: Cascade)
  teacher               User?                   @relation(fields: [teacherId], references: [id])
  StudentWeeklySchedule StudentWeeklySchedule[]

  @@index([classesId])
  @@index([courseId])
  @@map("classes_schedules")
}

model Student {
  id                    String                  @id @default(uuid())
  name                  String
  gender                String
  phone                 String
  password              String?
  cardNumber            String?
  balance               Decimal                 @default(0) @db.Decimal(10, 2)
  points                Decimal                 @default(0) @db.Decimal(10, 2)
  source                String?
  referrer              String?
  address               String?
  idCard                String?
  school                String?
  parentName            String?
  remarks               String?
  institutionId         String
  status                String                  @default("active")
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @default(now()) @updatedAt
  type                  String                  @default("intent")
  operatorId            String?
  sourceDesc            String?
  followerId            String?
  birthday              BigInt?
  followUpDate          BigInt?
  intentLevel           String?
  avatar                String?
  Bill                  Bill[]
  StudentClass          StudentClasses[]
  StudentFollowRecords  StudentFollowRecords[]
  StudentProductAdjust  StudentProductAdjust[]
  StudentProductRecord  StudentProductRecord[]
  StudentProductRefund  StudentProductRefund[]
  StudentProduct        StudentProduct[]
  StudentWeeklySchedule StudentWeeklySchedule[]
  follower              User?                   @relation("FollowerRelation", fields: [followerId], references: [id])
  institution           Institution             @relation(fields: [institutionId], references: [id], onDelete: Cascade)
  operator              User?                   @relation("OperatorRelation", fields: [operatorId], references: [id], onDelete: Cascade)

  @@index([institutionId])
  @@index([name])
  @@index([phone])
  @@map("students")
}

model StudentWeeklySchedule {
  id                String          @id @default(uuid())
  classesScheduleId String
  studentId         String
  status            String          @default("unattended")
  operatorId        String?
  operatorTime      BigInt?
  studentType       String          @default("temporary")
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  institutionId     String
  studentProductId  String?
  attendanceCount   Int?
  attendanceAmount  Decimal?        @db.Decimal(10, 2)
  productId         String?
  classesSchedule   ClassesSchedule @relation(fields: [classesScheduleId], references: [id], onDelete: Cascade)
  operator          User?           @relation(fields: [operatorId], references: [id], onDelete: Cascade)
  product           Product?        @relation(fields: [productId], references: [id])
  student           Student         @relation(fields: [studentId], references: [id], onDelete: Cascade)
  studentProduct    StudentProduct? @relation(fields: [studentProductId], references: [id])

  @@unique([classesScheduleId, studentId])
  @@index([classesScheduleId])
  @@index([studentId])
  @@index([productId])
  @@map("student_weekly_schedules")
}

model StudentClasses {
  id            String    @id @default(uuid())
  studentId     String
  createdAt     DateTime  @default(now())
  updatedAt     DateTime? @default(now()) @updatedAt
  joinDate      BigInt
  classesId     String
  institutionId String
  operatorId    String
  operatorTime  BigInt
  type          String    @default("in")
  classes       Classes   @relation(fields: [classesId], references: [id], onDelete: Cascade)
  operator      User      @relation(fields: [operatorId], references: [id], onDelete: Cascade)
  student       Student   @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@unique([studentId, classesId])
  @@index([studentId])
  @@index([classesId])
  @@map("student_classes")
}

model Course {
  id                   String            @id @default(uuid())
  name                 String
  duration             Int
  type                 String            @default("group")
  teacherId            String?
  isDirectSale         Boolean           @default(false)
  price                Decimal?          @default(0) @db.Decimal(10, 2)
  deductionPerClass    Decimal?          @default(1) @db.Decimal(10, 2)
  isDeductOnAttendance Boolean           @default(true)
  isDeductOnLeave      Boolean           @default(false)
  isDeductOnAbsence    Boolean           @default(false)
  picture              String?
  description          String?
  isShow               Boolean           @default(false)
  status               String            @default("active")
  institutionId        String
  createdAt            DateTime          @default(now())
  updatedAt            DateTime?         @default(now()) @updatedAt
  Classes              Classes[]
  ClassesSchedule      ClassesSchedule[]
  classes              CourseClasses[]
  institution          Institution       @relation(fields: [institutionId], references: [id], onDelete: Cascade)
  teacher              User?             @relation(fields: [teacherId], references: [id])
  ProductCourse        ProductCourse[]

  @@index([teacherId])
  @@index([institutionId])
  @@map("courses")
}

model CourseClasses {
  id            String    @id @default(uuid())
  courseId      String
  institutionId String
  status        String    @default("active")
  createdAt     DateTime  @default(now())
  updatedAt     DateTime? @default(now()) @updatedAt
  classesId     String
  course        Course    @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([courseId, classesId])
  @@index([courseId])
  @@index([classesId])
  @@map("course_classes")
}

model Product {
  id                    String                  @id @default(uuid())
  name                  String
  price                 Decimal                 @default(0) @db.Decimal(10, 2)
  icon                  String                  @default("")
  cover                 String                  @default("")
  leaveCount            Int?
  packageType           String
  targetAudience        String?
  isShow                Boolean                 @default(false)
  status                String                  @default("active")
  institutionId         String
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  remarks               String?
  timeLimitType         String?
  timeLimitedUsage      Int?
  usageLimit            Int?
  validTimeRange        String?
  courses               ProductCourse[]
  institution           Institution             @relation(fields: [institutionId], references: [id], onDelete: Cascade)
  StudentProductAdjust  StudentProductAdjust[]
  StudentProductRecord  StudentProductRecord[]
  StudentProductRefund  StudentProductRefund[]
  StudentProduct        StudentProduct[]
  StudentWeeklySchedule StudentWeeklySchedule[]

  @@index([institutionId])
  @@map("products")
}

model ProductCourse {
  id            String   @id @default(uuid())
  productId     String
  courseId      String
  institutionId String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  course        Course   @relation(fields: [courseId], references: [id], onDelete: Cascade)
  product       Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([productId, courseId])
  @@index([productId])
  @@index([courseId])
  @@map("product_courses")
}

model StudentProduct {
  id                    String                  @id @default(uuid())
  studentId             String
  productId             String
  totalCount            Decimal?                @default(0) @db.Decimal(10, 2)
  remainingCount        Decimal?                @db.Decimal(10, 2)
  remainingDays         Decimal?                @db.Decimal(10, 2)
  status                String?                 @default("active")
  institutionId         String
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  startTime             BigInt?
  endTime               BigInt?
  amount                Int?
  operatorId            String
  payTime               BigInt?
  payment               String?
  remarks               String?
  salesRepId            String?
  unitPrice             Decimal?                @db.Decimal(10, 3)
  type                  String?                 @default("done")
  remainingAmount       Decimal?                @db.Decimal(10, 3)
  amountPaid            Decimal?                @db.Decimal(10, 2)
  buyCount              Decimal?                @default(1) @db.Decimal(10, 2)
  discount              Decimal?                @db.Decimal(10, 2)
  amountUnpaid          Decimal?                @db.Decimal(10, 2)
  endDate               BigInt?
  enrollmentStatus      String                  @default("active")
  paymentStatus         String                  @default("done")
  remainingBalance      Decimal?                @db.Decimal(10, 3)
  remainingSessionCount Decimal?                @db.Decimal(10, 2)
  sessionUnitPrice      Decimal?                @db.Decimal(10, 3)
  startDate             BigInt?
  totalSessionCount     Decimal                 @default(0) @db.Decimal(10, 2)
  StudentProductAdjust  StudentProductAdjust[]
  StudentProductRecord  StudentProductRecord[]
  institution           Institution             @relation(fields: [institutionId], references: [id], onDelete: Cascade)
  operator              User                    @relation("OperatorRelation", fields: [operatorId], references: [id], onDelete: Cascade)
  product               Product                 @relation(fields: [productId], references: [id], onDelete: Cascade)
  salesRep              User?                   @relation("SalesRepRelation", fields: [salesRepId], references: [id], onDelete: Cascade)
  student               Student                 @relation(fields: [studentId], references: [id], onDelete: Cascade)
  StudentWeeklySchedule StudentWeeklySchedule[]
  User                  User[]                  @relation("StudentProductToUser")

  @@index([institutionId])
  @@index([studentId])
  @@index([productId])
  @@index([operatorId])
  @@map("student_products")
}

model StudentProductRecord {
  id                    String          @id @default(uuid())
  studentProductId      String?
  amount                Decimal?        @db.Decimal(10, 2)
  paymentMethod         String?
  paymentTime           BigInt?
  amountPaid            Decimal?        @db.Decimal(10, 2)
  amountUnpaid          Decimal?        @db.Decimal(10, 2)
  purchaseQuantity      Decimal?        @default(1) @db.Decimal(10, 2)
  discount              Decimal?        @db.Decimal(10, 2)
  salesRepresentativeId String?
  operatorId            String?
  institutionId         String
  remarks               String?
  status                String          @default("done")
  createdAt             DateTime        @default(now())
  updatedAt             DateTime        @updatedAt
  studentId             String?
  giftCount             Decimal?        @db.Decimal(10, 2)
  giftDays              Decimal?        @db.Decimal(10, 2)
  productId             String?
  operator              User?           @relation("OperatorRepRelation", fields: [operatorId], references: [id])
  product               Product?        @relation(fields: [productId], references: [id])
  salesRepresentative   User?           @relation("SalesRepRelation", fields: [salesRepresentativeId], references: [id])
  student               Student?        @relation(fields: [studentId], references: [id])
  studentProduct        StudentProduct? @relation(fields: [studentProductId], references: [id])

  @@index([salesRepresentativeId])
  @@index([operatorId])
  @@index([studentId, studentProductId, institutionId])
  @@map("student_product_record")
}

model StudentProductRefund {
  id            String      @id @default(uuid())
  studentId     String
  productId     String
  amount        Decimal?    @db.Decimal(10, 2)
  reason        String?
  paymentMethod String?
  operatorId    String
  paymentTime   BigInt
  status        String      @default("approved")
  remarks       String?
  reviewerId    String?
  institutionId String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  institution   Institution @relation(fields: [institutionId], references: [id], onDelete: Cascade)
  operator      User        @relation(fields: [operatorId], references: [id], onDelete: Cascade)
  product       Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  student       Student     @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@index([studentId])
  @@index([operatorId])
  @@index([institutionId])
  @@map("student_product_refund")
}

model StudentProductAdjust {
  id               String          @id @default(uuid())
  studentId        String
  studentProductId String?
  type             String
  amount           Decimal?        @db.Decimal(10, 2)
  operatorId       String
  remarks          String?
  operatorTime     BigInt
  institutionId    String
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  count            Decimal?        @db.Decimal(10, 2)
  days             Decimal?        @db.Decimal(10, 2)
  afterAmount      Decimal?        @db.Decimal(10, 2)
  afterCount       Decimal?        @db.Decimal(10, 2)
  afterDays        Decimal?        @db.Decimal(10, 2)
  beforeAmount     Decimal?        @db.Decimal(10, 2)
  beforeCount      Decimal?        @db.Decimal(10, 2)
  beforeDays       Decimal?        @db.Decimal(10, 2)
  productId        String?
  institution      Institution     @relation(fields: [institutionId], references: [id], onDelete: Cascade)
  operator         User            @relation(fields: [operatorId], references: [id], onDelete: Cascade)
  product          Product?        @relation(fields: [productId], references: [id])
  student          Student         @relation(fields: [studentId], references: [id], onDelete: Cascade)
  studentProduct   StudentProduct? @relation(fields: [studentProductId], references: [id])

  @@index([studentProductId])
  @@index([operatorId])
  @@index([institutionId])
  @@map("student_product_adjusts")
}

model StudentFollowRecords {
  id                  String      @id @default(uuid())
  studentId           String
  followUpDate        BigInt?
  followUpType        String?
  followUpContent     String?
  followUpResult      String?
  followUpMethod      String?
  followUpUserId      String
  createdAt           DateTime    @default(now())
  updatedAt           DateTime    @updatedAt
  nextFollowUpContent String?
  nextFollowUpDate    BigInt?
  institutionId       String
  operatorId          String?
  followUpStauts      String?
  followUpUser        User        @relation(fields: [followUpUserId], references: [id], onDelete: Cascade)
  institution         Institution @relation(fields: [institutionId], references: [id], onDelete: Cascade)
  operator            User?       @relation("StudentFollowRecordsOperatorRelation", fields: [operatorId], references: [id])
  student             Student     @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@index([studentId])
  @@index([institutionId])
  @@index([followUpUserId])
  @@map("student_follow_records")
}

model Classroom {
  id            String             @id @default(uuid())
  name          String
  capacity      Int
  institutionId String
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  addressId     String
  active        Boolean            @default(true)
  Classes       Classes[]
  address       InstitutionAddress @relation(fields: [addressId], references: [id], onDelete: Cascade)
  institution   Institution        @relation(fields: [institutionId], references: [id], onDelete: Cascade)

  @@index([institutionId])
  @@index([addressId])
  @@map("classrooms")
}

model InstitutionAddress {
  id             String      @id @default(uuid())
  institutionId  String
  province       String
  city           String
  district       String?
  street         String?
  address        String?
  businessHours  String?
  phone          String?
  personInCharge String?
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
  Classroom      Classroom[]
  institution    Institution @relation(fields: [institutionId], references: [id], onDelete: Cascade)

  @@index([institutionId])
  @@map("institution_addresses")
}

model GeographicArea {
  name      String
  code      String?
  postCode  String?
  level     Int
  parentId  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  id        Int      @id @default(autoincrement())

  @@index([code], map: "idx_code")
  @@index([parentId], map: "idx_parent_id")
  @@map("geographic_areas")
}

model Bill {
  id            String   @id @default(uuid())
  studentId     String?
  productId     String?
  amount        Decimal? @db.Decimal(10, 2)
  paymentTime   BigInt?
  operatorId    String?
  institutionId String
  remarks       String?
  billType      String?
  status        String?  @default("completed")
  operatorTime  BigInt?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  paymentMethod String?
  source        String?
  operator      User?    @relation(fields: [operatorId], references: [id])
  student       Student? @relation(fields: [studentId], references: [id])

  @@index([studentId])
  @@index([productId])
  @@index([operatorId])
  @@index([institutionId])
  @@index([billType])
  @@map("bills")
}
