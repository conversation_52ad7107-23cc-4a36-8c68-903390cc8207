(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[339],{26126:(e,a,r)=>{"use strict";r.d(a,{E:()=>i});var t=r(95155);r(12115);var s=r(74466),l=r(59434);let n=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:a,variant:r,...s}=e;return(0,t.jsx)("div",{className:(0,l.cn)(n({variant:r}),a),...s})}},30285:(e,a,r)=>{"use strict";r.d(a,{$:()=>o,r:()=>d});var t=r(95155),s=r(12115),l=r(99708),n=r(74466),i=r(59434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=s.forwardRef((e,a)=>{let{className:r,variant:s,size:n,asChild:o=!1,...c}=e,u=o?l.DX:"button";return(0,t.jsx)(u,{className:(0,i.cn)(d({variant:s,size:n,className:r})),ref:a,...c})});o.displayName="Button"},48432:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>v});var t=r(95155),s=r(12115),l=r(42355),n=r(13052),i=r(5623),d=r(59434),o=r(30285);let c=e=>{let{className:a,...r}=e;return(0,t.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,d.cn)("mx-auto flex w-full justify-center",a),...r})};c.displayName="Pagination";let u=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("ul",{ref:a,className:(0,d.cn)("flex flex-row items-center gap-1",r),...s})});u.displayName="PaginationContent";let m=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("li",{ref:a,className:(0,d.cn)("",r),...s})});m.displayName="PaginationItem";let x=e=>{let{className:a,isActive:r,size:s="icon",...l}=e;return(0,t.jsx)("a",{"aria-current":r?"page":void 0,className:(0,d.cn)((0,o.r)({variant:r?"outline":"ghost",size:s}),a),...l})};x.displayName="PaginationLink";let h=e=>{let{className:a,...r}=e;return(0,t.jsxs)(x,{"aria-label":"Go to previous page",size:"default",className:(0,d.cn)("gap-1 pl-2.5",a),...r,children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"上一页"})]})};h.displayName="PaginationPrevious";let g=e=>{let{className:a,...r}=e;return(0,t.jsxs)(x,{"aria-label":"Go to next page",size:"default",className:(0,d.cn)("gap-1 pr-2.5",a),...r,children:[(0,t.jsx)("span",{children:"下一页"}),(0,t.jsx)(n.A,{className:"h-4 w-4"})]})};g.displayName="PaginationNext";let f=e=>{let{className:a,...r}=e;return(0,t.jsxs)("span",{"aria-hidden":!0,className:(0,d.cn)("flex h-9 w-9 items-center justify-center",a),...r,children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"更多页"})]})};f.displayName="PaginationEllipsis";var p=r(59409);function v(e){let{currentPage:a,pageSize:r,totalItems:s,onPageChange:i,onPageSizeChange:d}=e,o=Math.ceil(s/r),v=(()=>{let e=[];if(o<=5){for(let a=1;a<=o;a++)e.push(a);return e}e.push(1);let r=Math.max(2,a-1),t=Math.min(a+1,o-1);2===r&&(t=Math.min(r+2,o-1)),t===o-1&&(r=Math.max(t-2,2)),r>2&&e.push("ellipsis-start");for(let a=r;a<=t;a++)e.push(a);return t<o-1&&e.push("ellipsis-end"),o>1&&e.push(o),e})(),b=0===s?0:(a-1)*r+1,j=Math.min(a*r,s);return(0,t.jsxs)("div",{className:"flex flex-col gap-4 px-2 py-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,t.jsx)("span",{className:"text-muted-foreground/80",children:"显示"}),(0,t.jsxs)(p.l6,{value:r.toString(),onValueChange:e=>{d(Number(e))},children:[(0,t.jsx)(p.bq,{className:"w-[4.5rem] h-7 text-xs border-border/60 hover:bg-accent/30 transition-colors",children:(0,t.jsx)(p.yv,{})}),(0,t.jsx)(p.gC,{children:[10,20,30,50].map(e=>(0,t.jsx)(p.eb,{value:e.toString(),className:"text-xs",children:e},e))})]}),(0,t.jsx)("span",{className:"text-muted-foreground/80",children:"条"}),(0,t.jsx)("span",{className:"text-muted-foreground/40 mx-1.5",children:"|"}),s>0?(0,t.jsxs)("span",{className:"text-muted-foreground/80",children:[b,"-",j," / ",s," 条记录"]}):(0,t.jsx)("span",{className:"text-muted-foreground/80",children:"0 条记录"})]}),(0,t.jsx)(c,{children:(0,t.jsxs)(u,{className:"gap-1",children:[(0,t.jsx)(m,{children:(0,t.jsx)(h,{onClick:()=>i(Math.max(1,a-1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(1===a?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,t.jsx)(l.A,{className:"h-4 w-4 mr-1"})})}),v.map((e,r)=>"ellipsis-start"===e||"ellipsis-end"===e?(0,t.jsx)(m,{children:(0,t.jsx)(f,{className:"h-8 w-8 flex items-center justify-center text-xs"})},"ellipsis-".concat(r)):(0,t.jsx)(m,{children:(0,t.jsx)(x,{onClick:()=>i(e),isActive:a===e,className:"h-8 w-8 text-xs font-medium rounded-md transition-colors data-[active]:bg-primary data-[active]:text-primary-foreground hover:bg-accent/50 hover:text-accent-foreground/90","aria-label":"前往第 ".concat(e," 页"),children:e})},e)),(0,t.jsx)(m,{children:(0,t.jsx)(g,{onClick:()=>i(Math.min(o,a+1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(a===o||0===o?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,t.jsx)(n.A,{className:"h-4 w-4 ml-1"})})})]})})]})}},57001:(e,a,r)=>{"use strict";r.d(a,{p:()=>n});var t=r(95155),s=r(30285),l=r(46102);function n(e){let{icon:a,tooltipText:r,tooltipSide:n="top",tooltipAlign:i="center",delayDuration:d=300,variant:o="ghost",size:c="icon",className:u="h-8 w-8 hover:bg-muted",...m}=e;return(0,t.jsx)(l.Bc,{delayDuration:d,children:(0,t.jsxs)(l.m_,{children:[(0,t.jsx)(l.k$,{asChild:!0,children:(0,t.jsx)(s.$,{variant:o,size:c,className:u,...m,children:(0,t.jsx)(a,{className:"h-4 w-4 text-muted-foreground"})})}),(0,t.jsx)(l.ZI,{side:n,align:i,className:"font-medium text-xs px-3 py-1.5",children:(0,t.jsx)("p",{children:r})})]})})}},59434:(e,a,r)=>{"use strict";r.d(a,{cn:()=>l});var t=r(52596),s=r(39688);function l(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return(0,s.QP)((0,t.$)(a))}},61809:(e,a,r)=>{"use strict";r.d(a,{Y:()=>n});var t=r(44861),s=r(73168),l=r(24122);let n=function(e){let a,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"yyyy-MM-dd",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(null==e)return n;try{if("string"==typeof e)a=new Date(e);else if("number"==typeof e)a=new Date(e);else{if(!(e instanceof Date))return n;a=e}if(!(0,t.f)(a))return n;return(0,s.GP)(a,r,{locale:l.g})}catch(e){return console.error("Date formatting error:",e),n}}},62523:(e,a,r)=>{"use strict";r.d(a,{p:()=>n});var t=r(95155),s=r(12115),l=r(59434);let n=s.forwardRef((e,a)=>{let{className:r,type:s,...n}=e;return(0,t.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:a,...n})});n.displayName="Input"},63375:(e,a,r)=>{"use strict";r.d(a,{A:()=>d});var t=r(95155),s=r(12115),l=r(59409),n=r(59434),i=r(6658);let d=(0,s.memo)(function(e){let{teacher:a,setTeacher:r,width:d="w-full",placeholder:o="选择人员",className:c="",showAllOption:u=!0,allOptionText:m="全部人员",allOptionValue:x="all",teacherList:h,disabled:g=!1}=e,{data:f,isLoading:p,error:v}=(0,i.X)(),b=(0,s.useCallback)(e=>{r(e)},[r]),j=(0,s.useCallback)(()=>(0,n.cn)("h-9 ".concat(d),c),[d,c]),y=h||f;return(0,t.jsxs)(l.l6,{value:a,onValueChange:b,disabled:g||p,children:[(0,t.jsx)(l.bq,{className:j(),children:(0,t.jsx)(l.yv,{placeholder:o})}),(0,t.jsxs)(l.gC,{children:[v&&(0,t.jsx)(l.eb,{value:"error",disabled:!0,children:String(v)}),p&&(0,t.jsx)(l.eb,{value:"loading",disabled:!0,children:"加载中..."}),!p&&!v&&(0,t.jsxs)(t.Fragment,{children:[u&&(0,t.jsx)(l.eb,{value:x,children:m}),null==y?void 0:y.map(e=>(0,t.jsx)(l.eb,{value:e.id,children:e.name},e.id))]})]})]})})},65436:(e,a,r)=>{"use strict";r.d(a,{G:()=>l,j:()=>s});var t=r(34540);let s=()=>(0,t.wA)(),l=t.d4},68478:(e,a,r)=>{"use strict";r.d(a,{default:()=>P});var t=r(95155),s=r(9110),l=r(12115),n=r(89917),i=r(59434),d=r(26126),o=r(6874),c=r.n(o),u=r(57001),m=r(61809),x=r(55028);let h=(0,x.default)(()=>r.e(4512).then(r.bind(r,74512)),{loadableGenerated:{webpack:()=>[74512]},ssr:!1}),g=(0,x.default)(()=>r.e(668).then(r.bind(r,668)),{loadableGenerated:{webpack:()=>[668]},ssr:!1}),f=[{accessorKey:"name",header:"班级名称",cell:e=>{let{row:a}=e;return(0,t.jsx)("div",{className:"font-medium text-slate-800",children:a.original.name})}},{accessorKey:"teacherName",header:"上课老师",cell:e=>{var a;let{row:r}=e;return(0,t.jsx)("div",{className:"text-slate-700",children:null===(a=r.original.teacher)||void 0===a?void 0:a.name})}},{accessorKey:"times",header:"已上/总次数",cell:e=>{let{row:a}=e;return(0,t.jsxs)("div",{className:"text-slate-600 flex items-center",children:[(0,t.jsx)("span",{className:"font-medium text-emerald-600",children:a.original.currentWeeks}),(0,t.jsx)("span",{className:"mx-0.5 text-slate-400",children:"/"}),(0,t.jsx)("span",{children:a.original.totalWeeks})]})}},{accessorKey:"startDate",header:"开班日期",cell:e=>{let{row:a}=e,r=(0,m.Y)(a.original.startDate,"yyyy-MM-dd (EEEE)");return(0,t.jsx)("div",{className:"text-slate-700 flex items-center",children:(0,t.jsx)("span",{className:"text-slate-800 font-medium",children:r})})}},{accessorKey:"courseName",header:"当前课程",cell:e=>{var a;let{row:r}=e;return(0,t.jsx)(d.E,{className:"bg-slate-100 text-slate-700 hover:bg-slate-200 transition-colors rounded-md px-2 py-1 font-normal",children:null===(a=r.original.course)||void 0===a?void 0:a.name})}},{accessorKey:"classesStatus",header:"状态",cell:e=>{let a,{row:r}=e,s=new Date,l=new Date(r.original.startDate),n=new Date(r.original.endDate),o={ACTIVE:"active",INACTIVE:"inactive",END:"end"};a=s>l&&s<n?o.ACTIVE:s<l?o.INACTIVE:o.END;let{badgeClass:c,dotClass:u,text:m}={[o.ACTIVE]:{badgeClass:"bg-emerald-50 text-emerald-700 hover:bg-emerald-100 border-emerald-200",dotClass:"bg-emerald-500",text:"进行中"},[o.INACTIVE]:{badgeClass:"bg-slate-50 text-slate-700 hover:bg-slate-100 border-slate-200",dotClass:"bg-slate-500",text:"未开始"},[o.END]:{badgeClass:"bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200",dotClass:"bg-amber-500",text:"已结束"}}[a];return(0,t.jsx)(d.E,{className:(0,i.cn)("font-medium border py-1 px-2.5 rounded-md transition-all",c),children:(0,t.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,t.jsx)("div",{className:(0,i.cn)("w-2 h-2 rounded-full animate-pulse",u)}),m]})})}},{id:"actions",header:"操作",cell:e=>{let{row:a}=e,r=a.original;return console.log(r," classes"),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c(),{href:"/academic/classes/".concat(r.id),children:(0,t.jsx)(u.p,{icon:n.A,tooltipText:"班级详细"})}),(0,t.jsx)(h,{classes:r}),(0,t.jsx)(g,{classes:r})]})}}];var p=r(48432),v=r(62523),b=r(12318),j=r(47924),y=r(88172),N=r(59409),w=r(30285),C=r(35695),A=r(27893),k=r(63375),E=r(91347);let D=function(){let e=(0,C.useRouter)(),[a,r]=(0,l.useState)(1),[n,i]=l.useState(10),[d,o]=l.useState(""),[c,u]=l.useState(""),[m,x]=l.useState("all"),h=(0,l.useCallback)((0,y.A)(e=>{o(e)},500),[]);(0,l.useEffect)(()=>()=>{h.cancel()},[h]);let g=(0,l.useMemo)(()=>({name:d,teacherId:"all"===c?"":c,page:a,pageSize:n}),[d,c,a,n]),{data:D,isLoading:P}=(0,A.e6)(g);return(0,t.jsxs)("div",{className:"space-y-6 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-4 border-b border-gray-100",children:[(0,t.jsx)("h2",{className:"text-xl font-medium text-gray-800",children:"班级管理"}),(0,t.jsx)(E.LQ,{permission:"class:create",children:(0,t.jsxs)(w.$,{onClick:()=>e.push("/academic/classes/new"),className:"h-10 gap-1.5",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),"新增班级"]})})]}),(0,t.jsx)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-3 w-full sm:w-auto",children:[(0,t.jsxs)(N.l6,{value:m,onValueChange:x,children:[(0,t.jsx)(N.bq,{className:"h-10 w-[140px] rounded-md border-gray-200 shadow-sm transition-all hover:border-gray-300",children:(0,t.jsx)(N.yv,{placeholder:"班级状态"})}),(0,t.jsxs)(N.gC,{className:"rounded-md",children:[(0,t.jsx)(N.eb,{value:"all",children:"全部班级"}),(0,t.jsx)(N.eb,{value:"active",children:"进行中"}),(0,t.jsx)(N.eb,{value:"upcoming",children:"未开始"}),(0,t.jsx)(N.eb,{value:"completed",children:"已结束"})]})]}),(0,t.jsx)(k.A,{teacher:c,setTeacher:u,width:"w-[140px]",showAllOption:!0,allOptionText:"全部老师",placeholder:"选择老师"}),(0,t.jsxs)("div",{className:"relative flex-1 w-full sm:w-auto",children:[(0,t.jsx)(j.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"}),(0,t.jsx)(v.p,{type:"search",placeholder:"搜索班级...",className:"pl-10 h-10 w-full sm:w-[250px] lg:w-[320px] rounded-md border-gray-200 shadow-sm transition-all hover:border-gray-300",value:d,onChange:e=>{h(e.target.value)}})]})]})}),(0,t.jsx)(s.b,{columns:f,data:(null==D?void 0:D.list)||[],pagination:!1,loading:P}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)(p.default,{currentPage:(null==D?void 0:D.page)||1,pageSize:(null==D?void 0:D.pageSize)||10,totalItems:(null==D?void 0:D.total)||0,onPageChange:e=>{r(e)},onPageSizeChange:e=>{i(e)}})})]})};function P(){return(0,t.jsx)("div",{className:"space-y-4 p-4",children:(0,t.jsx)(D,{})})}},77246:(e,a,r)=>{Promise.resolve().then(r.bind(r,68478))},91347:(e,a,r)=>{"use strict";r.d(a,{LQ:()=>l});var t=r(95155),s=r(65436);let l=e=>{let{permission:a,children:r,fallback:l=null,logic:n="any"}=e,i=(0,s.G)(e=>e.userPermissions.permissions);if(!a||Array.isArray(a)&&0===a.length)return(0,t.jsx)(t.Fragment,{children:r});let d=Array.isArray(a)?a:[a],o=!1;return("all"===n?d.every(e=>i.includes(e)):d.some(e=>i.includes(e)))?(0,t.jsx)(t.Fragment,{children:r}):(0,t.jsx)(t.Fragment,{children:l})};r(30285);var n=r(12115),i=r(74466),d=r(59434);let o=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}});n.forwardRef((e,a)=>{let{className:r,variant:s,...l}=e;return(0,t.jsx)("div",{ref:a,role:"alert",className:(0,d.cn)(o({variant:s}),r),...l})}).displayName="Alert",n.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("h5",{ref:a,className:(0,d.cn)("mb-1 font-medium leading-none tracking-tight",r),...s})}).displayName="AlertTitle",n.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,d.cn)("text-sm [&_p]:leading-relaxed",r),...s})}).displayName="AlertDescription"}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,8687,4201,8737,4540,4582,5620,3168,9613,7945,6874,3015,9624,9110,6315,7358],()=>a(77246)),_N_E=e.O()}]);