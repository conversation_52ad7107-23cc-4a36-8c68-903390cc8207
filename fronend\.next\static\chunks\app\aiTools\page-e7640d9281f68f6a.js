(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4572],{17759:(e,t,r)=>{"use strict";r.d(t,{C5:()=>g,MJ:()=>v,Rr:()=>b,eI:()=>x,lR:()=>p,lV:()=>o,zB:()=>u});var a=r(95155),s=r(12115),l=r(99708),n=r(62177),i=r(59434),d=r(85057);let o=n.Op,c=s.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(c.Provider,{value:{name:t.name},children:(0,a.jsx)(n.xI,{...t})})},m=()=>{let e=s.useContext(c),t=s.useContext(f),{getFieldState:r,formState:a}=(0,n.xW)(),l=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...l}},f=s.createContext({}),x=s.forwardRef((e,t)=>{let{className:r,...l}=e,n=s.useId();return(0,a.jsx)(f.Provider,{value:{id:n},children:(0,a.jsx)("div",{ref:t,className:(0,i.cn)("space-y-2",r),...l})})});x.displayName="FormItem";let p=s.forwardRef((e,t)=>{let{className:r,...s}=e,{error:l,formItemId:n}=m();return(0,a.jsx)(d.J,{ref:t,className:(0,i.cn)(l&&"text-destructive",r),htmlFor:n,...s})});p.displayName="FormLabel";let v=s.forwardRef((e,t)=>{let{...r}=e,{error:s,formItemId:n,formDescriptionId:i,formMessageId:d}=m();return(0,a.jsx)(l.DX,{ref:t,id:n,"aria-describedby":s?"".concat(i," ").concat(d):"".concat(i),"aria-invalid":!!s,...r})});v.displayName="FormControl";let b=s.forwardRef((e,t)=>{let{className:r,...s}=e,{formDescriptionId:l}=m();return(0,a.jsx)("p",{ref:t,id:l,className:(0,i.cn)("text-sm text-muted-foreground",r),...s})});b.displayName="FormDescription";let g=s.forwardRef((e,t)=>{var r;let{className:s,children:l,...n}=e,{error:d,formMessageId:o}=m(),c=d?String(null!==(r=null==d?void 0:d.message)&&void 0!==r?r:""):l;return c?(0,a.jsx)("p",{ref:t,id:o,className:(0,i.cn)("text-sm font-medium text-destructive",s),...n,children:c}):null});g.displayName="FormMessage"},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var a=r(95155);r(12115);var s=r(74466),l=r(59434);let n=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:r}),t),...s})}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>d});var a=r(95155),s=r(12115),l=r(99708),n=r(74466),i=r(59434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=s.forwardRef((e,t)=>{let{className:r,variant:s,size:n,asChild:o=!1,...c}=e,u=o?l.DX:"button";return(0,a.jsx)(u,{className:(0,i.cn)(d({variant:s,size:n,className:r})),ref:t,...c})});o.displayName="Button"},55733:(e,t,r)=>{"use strict";r.d(t,{Q:()=>f});var a=r(95155),s=r(12115),l=r(59434),n=r(26126),i=r(74466);let d=(0,i.F)("",{variants:{variant:{default:"border-b flex -mb-px space-x-6",underline:"relative flex overflow-x-auto",pill:"bg-muted p-1 rounded-lg flex mb-4",vertical:"w-48 shrink-0 border-r pr-4 flex flex-col space-y-1"}},defaultVariants:{variant:"default"}}),o=(0,i.F)("transition-colors",{variants:{variant:{default:"py-2 border-b-2 font-medium text-sm flex items-center gap-2",underline:"py-2 mr-8 font-medium text-sm transition-colors relative flex items-center gap-2",pill:"flex-1 py-1.5 px-3 text-sm font-medium rounded-md flex items-center justify-center gap-2",vertical:"flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md text-left"},state:{active:"",inactive:""}},compoundVariants:[{variant:"default",state:"active",className:"border-primary text-primary"},{variant:"default",state:"inactive",className:"border-transparent text-muted-foreground hover:text-foreground hover:border-border"},{variant:"underline",state:"active",className:"text-primary"},{variant:"underline",state:"inactive",className:"text-muted-foreground hover:text-foreground"},{variant:"pill",state:"active",className:"bg-background text-foreground shadow-sm"},{variant:"pill",state:"inactive",className:"text-muted-foreground hover:text-foreground"},{variant:"vertical",state:"active",className:"bg-accent text-accent-foreground"},{variant:"vertical",state:"inactive",className:"text-muted-foreground hover:bg-muted hover:text-foreground"}],defaultVariants:{variant:"default",state:"inactive"}}),c=(0,i.F)("",{variants:{variant:{default:"py-4",underline:"py-4",pill:"",vertical:"flex-1"}},defaultVariants:{variant:"default"}}),u=s.memo(e=>{let{tab:t,isActive:r,showBadges:s}=e;return r?t.content?(0,a.jsx)(a.Fragment,{children:t.content}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:t.label}),s&&void 0!==t.badge&&(0,a.jsx)(n.E,{variant:"default",children:t.badge})]}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:["这是 ",t.label," 标签页的内容区域。",s&&void 0!==t.badge&&" 您有 ".concat(t.badge," 个未读").concat(t.label,"。")]})]}):null},(e,t)=>!e.isActive&&!t.isActive||e.isActive===t.isActive&&e.tab.id===t.tab.id&&e.showBadges===t.showBadges);u.displayName="TabContent";let m=s.memo(e=>{let{tab:t,isActive:r,variant:s,showIcons:i,showBadges:d,onClick:c}=e;return(0,a.jsxs)("button",{onClick:c,disabled:t.disabled,className:(0,l.cn)(o({variant:s,state:r?"active":"inactive"}),t.disabled&&"opacity-50 cursor-not-allowed"),children:[i&&t.icon,(0,a.jsx)("span",{children:t.label}),d&&void 0!==t.badge&&(0,a.jsx)(n.E,{variant:r?"default":"secondary",className:"ml-1 px-1.5 py-0.5 h-5",children:t.badge}),"underline"===s&&r&&(0,a.jsx)("span",{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-primary"})]})});function f(e){let{tabs:t,defaultTab:r,onChange:n,className:i,variant:o="default",showIcons:f=!0,showBadges:x=!0}=e,p=s.useRef(!1),[v,b]=s.useState(""),g=s.useCallback(e=>{e!==v&&(b(e),null==n||n(e))},[v,n]);s.useEffect(()=>{if(!p.current){var e;p.current=!0,b(r||(null===(e=t[0])||void 0===e?void 0:e.id)||"")}},[r,t]);let h=s.useMemo(()=>t.reduce((e,t)=>(e.set(t.id,t),e),new Map),[t]),y=s.useMemo(()=>new Set(t.map(e=>e.id)),[t]),j=s.useRef(new Map);s.useEffect(()=>{Array.from(j.current.keys()).filter(e=>!y.has(e)).forEach(e=>j.current.delete(e)),t.forEach(e=>{j.current.has(e.id)||j.current.set(e.id,()=>g(e.id))})},[t,y,g]);let N=s.useCallback(e=>j.current.get(e)||(()=>g(e)),[g]),{isVertical:w,showUnderlineBorder:C}=s.useMemo(()=>({isVertical:"vertical"===o,showUnderlineBorder:"underline"===o}),[o]),k=s.useMemo(()=>{let e=h.get(v);return e?(0,a.jsx)(u,{tab:e,isActive:!0,showBadges:x},v):null},[v,h,x]);return s.useEffect(()=>{},[t.length,v]),(0,a.jsxs)("div",{className:(0,l.cn)(w?"flex gap-8":"w-full",i),children:[(0,a.jsxs)("div",{className:d({variant:o}),children:[C&&(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-border"}),t.map(e=>(0,a.jsx)(m,{tab:e,isActive:v===e.id,variant:o,showIcons:f,showBadges:x,onClick:N(e.id)},e.id))]}),(0,a.jsx)("div",{className:c({variant:o}),children:k})]})}m.displayName="TabButton"},59409:(e,t,r)=>{"use strict";r.d(t,{bq:()=>m,eb:()=>v,gC:()=>p,l6:()=>c,yv:()=>u});var a=r(95155),s=r(12115),l=r(14582),n=r(66474),i=r(47863),d=r(5196),o=r(59434);let c=l.bL;l.YJ;let u=l.WT,m=s.forwardRef((e,t)=>{let{className:r,children:s,...i}=e;return(0,a.jsxs)(l.l9,{ref:t,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...i,children:[s,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=l.l9.displayName;let f=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.PP,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});f.displayName=l.PP.displayName;let x=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.wn,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});x.displayName=l.wn.displayName;let p=s.forwardRef((e,t)=>{let{className:r,children:s,position:n="popper",...i}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:t,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:n,...i,children:[(0,a.jsx)(f,{}),(0,a.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(x,{})]})})});p.displayName=l.UC.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.JU,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...s})}).displayName=l.JU.displayName;let v=s.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsxs)(l.q7,{ref:t,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:s})]})});v.displayName=l.q7.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",r),...s})}).displayName=l.wv.displayName},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var a=r(52596),s=r(39688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},74605:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(95155),s=r(55733),l=r(12115),n=r(59409),i=r(88539),d=r(30285),o=r(92838),c=r(59434);let u=l.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsxs)(o.bL,{ref:t,className:(0,c.cn)("relative flex w-full touch-none select-none items-center",r),...s,children:[(0,a.jsx)(o.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,a.jsx)(o.Q6,{className:"absolute h-full bg-primary"})}),(0,a.jsx)(o.zi,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]})});u.displayName=o.bL.displayName;var m=r(17759),f=r(90221),x=r(62177),p=r(55594);let v=function(){let[e,t]=(0,l.useState)([]),[r,s]=(0,l.useState)(!1),o=[{label:"1024 x 1024",value:"1024x1024"},{label:"768 x 1024",value:"768x1024"},{label:"1536 x 2048",value:"1536x2048"},{label:"576 x 1024",value:"576x1024"},{label:"1152 x 2048",value:"1152x2048"}],c=p.Ik({prompt:p.Yj().max(200,{message:"提示词不能超过200字"}).min(1,{message:"请输入提示词"}),size:p.Yj().default("1024x1024"),count:p.ai().min(1).max(4).default(1)}),v=(0,x.mN)({resolver:(0,f.u)(c),defaultValues:{prompt:"",size:"1024x1024",count:1}});async function b(e){s(!0),console.log(e),setTimeout(()=>{t(Array(e.count).fill("https://plus.unsplash.com/premium_photo-1683910982837-81f0671bbb0d")),s(!1)},2e3)}return(0,a.jsxs)("div",{className:"container py-6 space-y-8",children:[(0,a.jsx)("div",{className:"mx-auto max-w-3xl",children:(0,a.jsx)(m.lV,{...v,children:(0,a.jsxs)("form",{onSubmit:v.handleSubmit(b),className:"space-y-6",children:[(0,a.jsx)(m.zB,{control:v.control,name:"prompt",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"提示词"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(i.T,{placeholder:"描述您想要生成的图片...",className:"resize-none min-h-[100px]",maxLength:200,...t})}),(0,a.jsxs)("div",{className:"text-xs text-right text-muted-foreground",children:[t.value.length,"/200"]}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(m.zB,{control:v.control,name:"size",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"尺寸"}),(0,a.jsxs)(n.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(m.MJ,{children:(0,a.jsx)(n.bq,{children:(0,a.jsx)(n.yv,{placeholder:"选择图片尺寸"})})}),(0,a.jsx)(n.gC,{children:o.map(e=>(0,a.jsx)(n.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:v.control,name:"count",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsxs)(m.lR,{children:["生成数量: ",t.value]}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(u,{min:1,max:4,step:1,defaultValue:[t.value],onValueChange:e=>t.onChange(e[0]),className:"pt-2"})}),(0,a.jsx)(m.C5,{})]})}})]}),(0,a.jsx)(d.$,{type:"submit",className:"w-full",disabled:r,children:r?"生成中...":"生成图片"})]})})}),r&&(0,a.jsx)("div",{className:"flex justify-center items-center py-10",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}),e.length>0&&!r&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"生成结果"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:e.map((e,t)=>(0,a.jsxs)("div",{className:"overflow-hidden rounded-lg border",children:[(0,a.jsx)("img",{src:e,alt:"Generated image ".concat(t+1),className:"w-full h-auto object-cover aspect-square"}),(0,a.jsxs)("div",{className:"p-3 flex justify-between bg-muted/20",children:[(0,a.jsx)(d.$,{variant:"outline",size:"sm",children:"下载"}),(0,a.jsx)(d.$,{variant:"outline",size:"sm",children:"分享"})]})]},t))})]})]})},b=function(){let e=[{id:"copywriting",label:"文案"},{id:"image",label:"图片",content:(0,a.jsx)(v,{})}];return(0,a.jsx)("div",{className:"space-y-4 p-4",children:(0,a.jsx)(s.Q,{defaultTab:"copywriting",tabs:e})})}},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var a=r(95155),s=r(12115),l=r(40968),n=r(74466),i=r(59434);let d=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.b,{ref:t,className:(0,i.cn)(d(),r),...s})});o.displayName=l.b.displayName},88539:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var a=r(95155),s=r(12115),l=r(59434);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...s})});n.displayName="Textarea"},97443:(e,t,r)=>{Promise.resolve().then(r.bind(r,74605))}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8737,4582,5589,5602,6975,6315,7358],()=>t(97443)),_N_E=e.O()}]);