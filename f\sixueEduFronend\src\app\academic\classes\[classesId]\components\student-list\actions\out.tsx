'use client';
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Pencil,LogOut } from 'lucide-react'
import React from 'react'
import { useClasses } from '@/hooks/useClasses';
import { useParams } from 'next/navigation';
import { customToast } from '@/lib/toast';
import { TooltipIconButton } from '@/components/ui/tooltip-icon-button';


function OutClass({ student } : {student: any}) {
  const { id } = useParams() as { id: string }
  const { deleteStudentFromClass } = useClasses()
    const handleOutClass = () => {
      deleteStudentFromClass(id, student.student.id).then((res) => {
        customToast.success('学员退出班级成功.')
      })
    }
  return (
    <AlertDialog>
      <TooltipIconButton
        icon={LogOut}
        tooltipText="退班"
      />
      <TooltipProvider delayDuration={300}>
        <Tooltip>
          <AlertDialogTrigger asChild>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className='h-8 w-8 hover:bg-muted'
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
          </AlertDialogTrigger>
          <TooltipContent side="top" className="font-medium text-xs px-3 py-1.5 bg-background border shadow-sm">
            <p>退班</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认退出班级</AlertDialogTitle>
          <AlertDialogDescription>
            确定要将学生 {student.student.name} 退出此班级吗？此操作不可撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction onClick={handleOutClass}>确认退出</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export default OutClass