"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4546],{4884:(e,t,r)=>{r.d(t,{bL:()=>C,zi:()=>w});var n=r(12115),o=r(85185),a=r(6101),i=r(46081),l=r(5845),s=r(45503),d=r(11275),u=r(63655),c=r(95155),p="Switch",[f,h]=(0,i.A)(p),[v,y]=f(p),m=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:s,defaultChecked:d,required:p,disabled:f,value:h="on",onCheckedChange:y,form:m,...b}=e,[x,C]=n.useState(null),w=(0,a.s)(t,e=>C(e)),R=n.useRef(!1),j=!x||m||!!x.closest("form"),[P=!1,A]=(0,l.i)({prop:s,defaultProp:d,onChange:y});return(0,c.jsxs)(v,{scope:r,checked:P,disabled:f,children:[(0,c.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":P,"aria-required":p,"data-state":k(P),"data-disabled":f?"":void 0,disabled:f,value:h,...b,ref:w,onClick:(0,o.m)(e.onClick,e=>{A(e=>!e),j&&(R.current=e.isPropagationStopped(),R.current||e.stopPropagation())})}),j&&(0,c.jsx)(g,{control:x,bubbles:!R.current,name:i,value:h,checked:P,required:p,disabled:f,form:m,style:{transform:"translateX(-100%)"}})]})});m.displayName=p;var b="SwitchThumb",x=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=y(b,r);return(0,c.jsx)(u.sG.span,{"data-state":k(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});x.displayName=b;var g=e=>{let{control:t,checked:r,bubbles:o=!0,...a}=e,i=n.useRef(null),l=(0,s.Z)(r),u=(0,d.X)(t);return n.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(l!==r&&t){let n=new Event("click",{bubbles:o});t.call(e,r),e.dispatchEvent(n)}},[l,r,o]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...a,tabIndex:-1,ref:i,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function k(e){return e?"checked":"unchecked"}var C=m,w=x},9607:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("CalendarCheck",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"m9 16 2 2 4-4",key:"19s6y9"}]])},20547:(e,t,r)=>{r.d(t,{UC:()=>Z,ZL:()=>U,bL:()=>B,l9:()=>z});var n=r(12115),o=r(85185),a=r(6101),i=r(46081),l=r(19178),s=r(92293),d=r(25519),u=r(61285),c=r(35152),p=r(34378),f=r(28905),h=r(63655),v=r(99708),y=r(5845),m=r(38168),b=r(93795),x=r(95155),g="Popover",[k,C]=(0,i.A)(g,[c.Bk]),w=(0,c.Bk)(),[R,j]=k(g),P=e=>{let{__scopePopover:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:l=!1}=e,s=w(t),d=n.useRef(null),[p,f]=n.useState(!1),[h=!1,v]=(0,y.i)({prop:o,defaultProp:a,onChange:i});return(0,x.jsx)(c.bL,{...s,children:(0,x.jsx)(R,{scope:t,contentId:(0,u.B)(),triggerRef:d,open:h,onOpenChange:v,onOpenToggle:n.useCallback(()=>v(e=>!e),[v]),hasCustomAnchor:p,onCustomAnchorAdd:n.useCallback(()=>f(!0),[]),onCustomAnchorRemove:n.useCallback(()=>f(!1),[]),modal:l,children:r})})};P.displayName=g;var A="PopoverAnchor";n.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,a=j(A,r),i=w(r),{onCustomAnchorAdd:l,onCustomAnchorRemove:s}=a;return n.useEffect(()=>(l(),()=>s()),[l,s]),(0,x.jsx)(c.Mz,{...i,...o,ref:t})}).displayName=A;var N="PopoverTrigger",D=n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,i=j(N,r),l=w(r),s=(0,a.s)(t,i.triggerRef),d=(0,x.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":q(i.open),...n,ref:s,onClick:(0,o.m)(e.onClick,i.onOpenToggle)});return i.hasCustomAnchor?d:(0,x.jsx)(c.Mz,{asChild:!0,...l,children:d})});D.displayName=N;var M="PopoverPortal",[O,E]=k(M,{forceMount:void 0}),F=e=>{let{__scopePopover:t,forceMount:r,children:n,container:o}=e,a=j(M,t);return(0,x.jsx)(O,{scope:t,forceMount:r,children:(0,x.jsx)(f.C,{present:r||a.open,children:(0,x.jsx)(p.Z,{asChild:!0,container:o,children:n})})})};F.displayName=M;var _="PopoverContent",I=n.forwardRef((e,t)=>{let r=E(_,e.__scopePopover),{forceMount:n=r.forceMount,...o}=e,a=j(_,e.__scopePopover);return(0,x.jsx)(f.C,{present:n||a.open,children:a.modal?(0,x.jsx)(G,{...o,ref:t}):(0,x.jsx)(T,{...o,ref:t})})});I.displayName=_;var G=n.forwardRef((e,t)=>{let r=j(_,e.__scopePopover),i=n.useRef(null),l=(0,a.s)(t,i),s=n.useRef(!1);return n.useEffect(()=>{let e=i.current;if(e)return(0,m.Eq)(e)},[]),(0,x.jsx)(b.A,{as:v.DX,allowPinchZoom:!0,children:(0,x.jsx)(L,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),s.current||null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;s.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),T=n.forwardRef((e,t)=>{let r=j(_,e.__scopePopover),o=n.useRef(!1),a=n.useRef(!1);return(0,x.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(i=r.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,i;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let l=t.target;(null===(i=r.triggerRef.current)||void 0===i?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),L=n.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:i,onEscapeKeyDown:u,onPointerDownOutside:p,onFocusOutside:f,onInteractOutside:h,...v}=e,y=j(_,r),m=w(r);return(0,s.Oh)(),(0,x.jsx)(d.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,x.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:h,onEscapeKeyDown:u,onPointerDownOutside:p,onFocusOutside:f,onDismiss:()=>y.onOpenChange(!1),children:(0,x.jsx)(c.UC,{"data-state":q(y.open),role:"dialog",id:y.contentId,...m,...v,ref:t,style:{...v.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),S="PopoverClose";function q(e){return e?"open":"closed"}n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,a=j(S,r);return(0,x.jsx)(h.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})}).displayName=S,n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=w(r);return(0,x.jsx)(c.i3,{...o,...n,ref:t})}).displayName="PopoverArrow";var B=P,z=D,U=F,Z=I},40968:(e,t,r)=>{r.d(t,{b:()=>l});var n=r(12115),o=r(63655),a=r(95155),i=n.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},55670:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},69074:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},72713:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},88106:(e,t,r)=>{r.d(t,{Ke:()=>C,R6:()=>g,bL:()=>j});var n=r(12115),o=r(85185),a=r(46081),i=r(5845),l=r(52712),s=r(6101),d=r(63655),u=r(28905),c=r(61285),p=r(95155),f="Collapsible",[h,v]=(0,a.A)(f),[y,m]=h(f),b=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:o,defaultOpen:a,disabled:l,onOpenChange:s,...u}=e,[f=!1,h]=(0,i.i)({prop:o,defaultProp:a,onChange:s});return(0,p.jsx)(y,{scope:r,disabled:l,contentId:(0,c.B)(),open:f,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),children:(0,p.jsx)(d.sG.div,{"data-state":R(f),"data-disabled":l?"":void 0,...u,ref:t})})});b.displayName=f;var x="CollapsibleTrigger",g=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,a=m(x,r);return(0,p.jsx)(d.sG.button,{type:"button","aria-controls":a.contentId,"aria-expanded":a.open||!1,"data-state":R(a.open),"data-disabled":a.disabled?"":void 0,disabled:a.disabled,...n,ref:t,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});g.displayName=x;var k="CollapsibleContent",C=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=m(k,e.__scopeCollapsible);return(0,p.jsx)(u.C,{present:r||o.open,children:e=>{let{present:r}=e;return(0,p.jsx)(w,{...n,ref:t,present:r})}})});C.displayName=k;var w=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:o,children:a,...i}=e,u=m(k,r),[c,f]=n.useState(o),h=n.useRef(null),v=(0,s.s)(t,h),y=n.useRef(0),b=y.current,x=n.useRef(0),g=x.current,C=u.open||c,w=n.useRef(C),j=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>w.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,l.N)(()=>{let e=h.current;if(e){j.current=j.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();y.current=t.height,x.current=t.width,w.current||(e.style.transitionDuration=j.current.transitionDuration,e.style.animationName=j.current.animationName),f(o)}},[u.open,o]),(0,p.jsx)(d.sG.div,{"data-state":R(u.open),"data-disabled":u.disabled?"":void 0,id:u.contentId,hidden:!C,...i,ref:v,style:{"--radix-collapsible-content-height":b?"".concat(b,"px"):void 0,"--radix-collapsible-content-width":g?"".concat(g,"px"):void 0,...e.style},children:C&&a})});function R(e){return e?"open":"closed"}var j=b}}]);