"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[238],{30238:(e,s,t)=>{t.r(s),t.d(s,{default:()=>u});var a=t(95155),n=t(57001),l=t(12115),r=t(89917),i=t(55028),c=t(48436),d=t(13515);let o=(0,i.default)(()=>t.e(7458).then(t.bind(t,17458)),{loadableGenerated:{webpack:()=>[17458]},ssr:!1}),u=function(e){let{role:s}=e,[t]=(0,d.iz)(),[i,u]=(0,l.useState)(!1),h=async e=>{try{await t({roleId:s.id,data:e}),c.l.success("编辑岗位成功")}catch(e){c.l.error((null==e?void 0:e.message)||"编辑岗位失败!")}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.p,{onClick:()=>u(!0),icon:r.A,tooltipText:"编辑岗位"}),i&&(0,a.jsx)(o,{open:i,onOpenChange:u,role:s,onSubmit:h})]})}},57001:(e,s,t)=>{t.d(s,{p:()=>r});var a=t(95155),n=t(30285),l=t(46102);function r(e){let{icon:s,tooltipText:t,tooltipSide:r="top",tooltipAlign:i="center",delayDuration:c=300,variant:d="ghost",size:o="icon",className:u="h-8 w-8 hover:bg-muted",...h}=e;return(0,a.jsx)(l.Bc,{delayDuration:c,children:(0,a.jsxs)(l.m_,{children:[(0,a.jsx)(l.k$,{asChild:!0,children:(0,a.jsx)(n.$,{variant:d,size:o,className:u,...h,children:(0,a.jsx)(s,{className:"h-4 w-4 text-muted-foreground"})})}),(0,a.jsx)(l.ZI,{side:r,align:i,className:"font-medium text-xs px-3 py-1.5",children:(0,a.jsx)("p",{children:t})})]})})}},89917:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(19946).A)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])}}]);