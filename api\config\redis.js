import { Redis } from "ioredis";

export const redisConfig = {
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
}


const redis = new Redis({
    ...redisConfig,
    db: 1,
    keyPrefix: "sixue:"
});

const redisWebSocket = new Redis({
    ...redisConfig,
    db: 10,
    keyPrefix: "sixue:websocket:"

});
const redisTask = new Redis({
    ...redisConfig,
    db: 11,
    keyPrefix: "sixue:task:"
});

// 系统配置
const redisSystem = new Redis({
    ...redisConfig,
    db: process.env.REDIS_DB,
    keyPrefix: "sixue:system:"
});



export { redis, redisWebSocket, redisTask, redisSystem };