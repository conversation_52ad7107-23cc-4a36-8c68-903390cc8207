"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7458],{15452:(e,t,a)=>{a.d(t,{G$:()=>W,Hs:()=>y,UC:()=>et,VY:()=>en,ZL:()=>Q,bL:()=>X,bm:()=>er,hE:()=>ea,hJ:()=>ee,l9:()=>K});var n=a(12115),r=a(85185),o=a(6101),s=a(46081),l=a(61285),i=a(5845),d=a(19178),c=a(25519),u=a(34378),f=a(28905),p=a(63655),m=a(92293),g=a(93795),x=a(38168),h=a(99708),v=a(95155),b="Dialog",[j,y]=(0,s.A)(b),[N,w]=j(b),C=e=>{let{__scopeDialog:t,children:a,open:r,defaultOpen:o,onOpenChange:s,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[f=!1,p]=(0,i.i)({prop:r,defaultProp:o,onChange:s});return(0,v.jsx)(N,{scope:t,triggerRef:c,contentRef:u,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:d,children:a})};C.displayName=b;var D="DialogTrigger",R=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,s=w(D,a),l=(0,o.s)(t,s.triggerRef);return(0,v.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":U(s.open),...n,ref:l,onClick:(0,r.m)(e.onClick,s.onOpenToggle)})});R.displayName=D;var k="DialogPortal",[E,I]=j(k,{forceMount:void 0}),F=e=>{let{__scopeDialog:t,forceMount:a,children:r,container:o}=e,s=w(k,t);return(0,v.jsx)(E,{scope:t,forceMount:a,children:n.Children.map(r,e=>(0,v.jsx)(f.C,{present:a||s.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:o,children:e})}))})};F.displayName=k;var O="DialogOverlay",_=n.forwardRef((e,t)=>{let a=I(O,e.__scopeDialog),{forceMount:n=a.forceMount,...r}=e,o=w(O,e.__scopeDialog);return o.modal?(0,v.jsx)(f.C,{present:n||o.open,children:(0,v.jsx)(A,{...r,ref:t})}):null});_.displayName=O;var A=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=w(O,a);return(0,v.jsx)(g.A,{as:h.DX,allowPinchZoom:!0,shards:[r.contentRef],children:(0,v.jsx)(p.sG.div,{"data-state":U(r.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),M="DialogContent",P=n.forwardRef((e,t)=>{let a=I(M,e.__scopeDialog),{forceMount:n=a.forceMount,...r}=e,o=w(M,e.__scopeDialog);return(0,v.jsx)(f.C,{present:n||o.open,children:o.modal?(0,v.jsx)(G,{...r,ref:t}):(0,v.jsx)(T,{...r,ref:t})})});P.displayName=M;var G=n.forwardRef((e,t)=>{let a=w(M,e.__scopeDialog),s=n.useRef(null),l=(0,o.s)(t,a.contentRef,s);return n.useEffect(()=>{let e=s.current;if(e)return(0,x.Eq)(e)},[]),(0,v.jsx)(L,{...e,ref:l,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=a.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=n.forwardRef((e,t)=>{let a=w(M,e.__scopeDialog),r=n.useRef(!1),o=n.useRef(!1);return(0,v.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,s;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(r.current||null===(s=a.triggerRef.current)||void 0===s||s.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var n,s;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let l=t.target;(null===(s=a.triggerRef.current)||void 0===s?void 0:s.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),L=n.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:l,...i}=e,u=w(M,a),f=n.useRef(null),p=(0,o.s)(t,f);return(0,m.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:l,children:(0,v.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":U(u.open),...i,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(Y,{titleId:u.titleId}),(0,v.jsx)($,{contentRef:f,descriptionId:u.descriptionId})]})]})}),J="DialogTitle",z=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=w(J,a);return(0,v.jsx)(p.sG.h2,{id:r.titleId,...n,ref:t})});z.displayName=J;var B="DialogDescription",q=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=w(B,a);return(0,v.jsx)(p.sG.p,{id:r.descriptionId,...n,ref:t})});q.displayName=B;var H="DialogClose",S=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,o=w(H,a);return(0,v.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,r.m)(e.onClick,()=>o.onOpenChange(!1))})});function U(e){return e?"open":"closed"}S.displayName=H;var V="DialogTitleWarning",[W,Z]=(0,s.q)(V,{contentName:M,titleName:J,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,a=Z(V),r="`".concat(a.contentName,"` requires a `").concat(a.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(a.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(a.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},$=e=>{let{contentRef:t,descriptionId:a}=e,r=Z("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");a&&n&&!document.getElementById(a)&&console.warn(o)},[o,t,a]),null},X=C,K=R,Q=F,ee=_,et=P,ea=z,en=q,er=S},17458:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var n=a(95155),r=a(30285),o=a(54165),s=a(62523),l=a(85057),i=a(88539),d=a(12115);function c(e){let{open:t,onOpenChange:a,role:c,onSubmit:u}=e,[f,p]=(0,d.useState)((null==c?void 0:c.name)||""),[m,g]=(0,d.useState)((null==c?void 0:c.description)||"");return((0,d.useEffect)(()=>{t&&c?(p(c.name),g(c.description)):t&&!c&&(p(""),g(""))},[t]),t)?(0,n.jsx)(o.lG,{open:t,onOpenChange:a,children:(0,n.jsxs)(o.Cf,{className:"sm:max-w-[425px]",children:[(0,n.jsxs)(o.c7,{children:[(0,n.jsx)(o.L3,{className:"text-xl font-semibold",children:c?"编辑岗位":"新增岗位"}),(0,n.jsx)(o.rr,{className:"text-muted-foreground pt-2",children:"请填写岗位信息，带 * 为必填项"})]}),(0,n.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsxs)(l.J,{htmlFor:"name",children:["岗位名称 ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)(s.p,{id:"name",value:f,onChange:e=>p(e.target.value),placeholder:"请输入岗位名称"})]}),(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsx)(l.J,{htmlFor:"description",children:"岗位描述"}),(0,n.jsx)(i.T,{id:"description",value:m,onChange:e=>g(e.target.value),placeholder:"请输入岗位描述"})]})]}),(0,n.jsxs)(o.Es,{children:[(0,n.jsx)(r.$,{variant:"outline",onClick:()=>a(!1),children:"取消"}),(0,n.jsx)(r.$,{onClick:()=>{f.trim()&&(u({name:f,description:m}),a(!1),p(""),g(""))},disabled:!f.trim(),children:"确定"})]})]})}):null}},40968:(e,t,a)=>{a.d(t,{b:()=>l});var n=a(12115),r=a(63655),o=a(95155),s=n.forwardRef((e,t)=>(0,o.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var l=s},54165:(e,t,a)=>{a.d(t,{Cf:()=>p,Es:()=>g,HM:()=>u,L3:()=>x,c7:()=>m,lG:()=>i,rr:()=>h,zM:()=>d});var n=a(95155),r=a(12115),o=a(15452),s=a(54416),l=a(59434);let i=o.bL,d=o.l9,c=o.ZL,u=o.bm,f=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(o.hJ,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r})});f.displayName=o.hJ.displayName;let p=r.forwardRef((e,t)=>{let{className:a,children:r,...i}=e;return(0,n.jsxs)(c,{children:[(0,n.jsx)(f,{}),(0,n.jsxs)(o.UC,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...i,children:[r,(0,n.jsxs)(o.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(s.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});p.displayName=o.UC.displayName;let m=e=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};m.displayName="DialogHeader";let g=e=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};g.displayName="DialogFooter";let x=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(o.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});x.displayName=o.hE.displayName;let h=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(o.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",a),...r})});h.displayName=o.VY.displayName},54416:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},85057:(e,t,a)=>{a.d(t,{J:()=>d});var n=a(95155),r=a(12115),o=a(40968),s=a(74466),l=a(59434);let i=(0,s.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(o.b,{ref:t,className:(0,l.cn)(i(),a),...r})});d.displayName=o.b.displayName},88539:(e,t,a)=>{a.d(t,{T:()=>s});var n=a(95155),r=a(12115),o=a(59434);let s=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("textarea",{className:(0,o.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...r})});s.displayName="Textarea"}}]);