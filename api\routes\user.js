import { createError } from '@fastify/error';
import bcrypt from 'bcryptjs';
import { TwitterSnowflake } from "@sapphire/snowflake"
import { userLoginSchema, userRegisterSchema, userRoleMenus } from '../schemas/user.js';
import buildMenuTree from '../utils/buildMenuTree.js';

const AUTH_ERROR = createError('AUTH_ERROR', '%s', 401);
const INTERNAL_ERROR = createError('INTERNAL_ERROR', '%s', 500);

export default async function (fastify, opts) {
    fastify.post('/user/register', {
        schema: userRegisterSchema,

        handler: async function (request, reply) {
            const { account, password, name } = request.body;
            try {

                const [hashendPassword, id] = await Promise.all([
                    bcrypt.hash(password, 10), // 计算成本设为 10
                    TwitterSnowflake.generate().toString() // 生成唯一 ID
                ]);

                // 直接尝试创建用户，避免 `findUnique` 额外查询
                const newUser = await fastify.prisma.user.create({
                    data: {
                        id,
                        account,
                        password: hashendPassword,
                        name
                    }
                });

                // 并行生成 Token
                const token = await fastify.generateToken({ id, account });

                reply.success({
                    message: '注册成功',
                    data: { accessToken: token }
                });

            } catch (error) {
                if (error.code === 'P2002')
                    // `P2002` 是 Prisma 的 UNIQUE 约束错误
                    throw new AUTH_ERROR('用户已存在');

                if (error instanceof AUTH_ERROR)
                    throw error;
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '注册失败');
            }

        }
    })
    // 用户登录
    fastify.post('/user/login', {
        schema: userLoginSchema,
        handler: async function (request, reply) {
            const { account, password } = request.body;
            // 获取数据库连接池
            const client = await fastify.pg.connect();
            try {
            
                const result = await client.query(`
                    SELECT 
                        u.id, u.account, u.password, u.name,
                        i.name AS institution_name, i.logo as institution_logo, i."subjectName" as "institution_subjectName",
                        (SELECT COUNT(*) FROM user_notifications un WHERE un."userId" = u.id AND un.status = 'unread') as notification_count
                    FROM users u
                    LEFT JOIN user_institution ui ON ui."userId" = u.id
                    LEFT JOIN institutions i ON i.id = ui."institutionId"
                    WHERE u.account = $1
                `, [account]);

                // **用户不存在**
                if (result.rows.length === 0) {
                    throw new AUTH_ERROR('账号不存在');
                }

                // **获取用户数据**
                const user = result.rows[0];


                // **验证密码（异步执行，提高并发）**
                const validPassword = await bcrypt.compare(password, user.password);
                if (!validPassword) {
                    throw new AUTH_ERROR('密码错误');
                }

                // **并行生成 Token**
                const [token, refreshToken] = await Promise.all([
                    fastify.generateToken({
                        id: user.id,
                        account: user.account,
                        name: user.name,
                        hasInstitution: user.institution_name ? true : false,
                    }),
                    fastify.generateRefreshToken({
                        id: user.id,
                        account: user.account
                    })
                ]);
                delete user.password;
                console.log(user," user")
                reply.success({
                    message: '登录成功',
                    data: {
                        user,
                        accessToken: token,
                        refreshToken
                    }
                });

            } catch (error) {
                if (error instanceof AUTH_ERROR) {
                    throw error;
                }
                fastify.log.error({ error, account }, '登录失败');
                throw new AUTH_ERROR('登录失败，请稍后重试');
            } finally {
                // **释放数据库连接**
                client.release();
            }
        }
    });

    // 获取用户的角色菜单树
    fastify.get('/user/menus', {
        schema: userRoleMenus,
        // onRequest:[fastify.auth.authenticate],
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            try {
                const CACHE_KEY = `user:menus:${request.user.id}`;
                const CACHE_TTL = 300; // 缓存 5 分钟

                // 尝试从 Redis 获取缓存的菜单数据
                const cachedMenus = await fastify.redis.get(CACHE_KEY);
                if (cachedMenus) {
                    return reply.success({
                        message: '获取用户菜单成功.',
                        data: JSON.parse(cachedMenus) || []
                    });
                    // 
                }

                // **优化查询**：一次查询获取 `permissionId` + `menus`
                const userMenus = await fastify.pg.query(`
                    SELECT m.*
                    FROM menus m
                    LEFT JOIN permissions p ON p.id = m."permissionId"
                    LEFT JOIN role_permissions rp ON rp."permissionId" = p.id
                    LEFT JOIN roles r ON r.id = rp."roleId"
                    LEFT JOIN user_roles ur ON ur."roleId" = r.id
                    WHERE ur."userId" = $1 OR m."permissionId" IS NULL
                    GROUP BY m.id
                `, [request.user.id]);

                if (!userMenus.rows.length) {
                    throw new AUTH_ERROR('用户无权限');
                }

                // 使用 `Object` 进行去重（比 Map 更快）
                const uniqueMenus = {};
                userMenus.rows.forEach(menu => {
                    uniqueMenus[menu.id] = menu;
                });

                // 处理菜单树
                const menuTree = buildMenuTree(Object.values(uniqueMenus));
                // 根据sort排序
                menuTree.sort((a, b) => a.sort - b.sort);

                // 将结果缓存到 Redis
                await fastify.redis.set(CACHE_KEY, JSON.stringify(menuTree), 'EX', CACHE_TTL);

                reply.success({
                    message: '获取用户菜单成功.',
                    data: menuTree || []
                });
            } catch (error) {
                fastify.log.error(error);
                if (error instanceof AUTH_ERROR) {
                    throw error;
                }
                throw new INTERNAL_ERROR(error.message || '获取用户菜单失败');
            }
        }
    });
}