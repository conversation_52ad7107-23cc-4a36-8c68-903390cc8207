import authController from '../controllers/authController.js';
import authSchema from '../schemas/authSchema.js';

/**
 * Authentication Routes
 * Defines API endpoints for authentication-related operations
 * @param {Object} fastify - Fastify instance
 * @param {Object} options - Route options
 */
export default async function (fastify, options) {
    // User login
    fastify.post('/auth/login', {
        schema: authSchema.loginSchema,
        handler: authController.login
    });
    
    // User logout
    fastify.post('/auth/logout', {
        schema: authSchema.logoutSchema,
        onRequest: [fastify.auth.authenticate],
        handler: authController.logout
    });
    
    // Refresh token
    fastify.post('/auth/refresh-token', {
        schema: authSchema.refreshTokenSchema,
        handler: authController.refreshToken
    });
    
    // Get current user info
    fastify.get('/auth-new/current-user', {
        schema: authSchema.getCurrentUserSchema,
        onRequest: [fastify.auth.authenticate],
        handler: authController.getCurrentUser
    });
    
    // Update current user info
    fastify.put('/auth-new/current-user', {
        schema: authSchema.updateCurrentUserSchema,
        onRequest: [fastify.auth.authenticate],
        handler: authController.updateCurrentUser
    });
    
    // Change password
    fastify.put('/auth-new/change-password', {
        schema: authSchema.changePasswordSchema,
        onRequest: [fastify.auth.authenticate],
        handler: authController.changePassword
    });
    
    // Reset password
    fastify.post('/auth-new/reset-password/:userId', {
        schema: authSchema.resetPasswordSchema,
        onRequest: [
            fastify.auth.authenticate,
            fastify.auth.requirePermission('system:user:reset-password')
        ],
        handler: authController.resetPassword
    });
    
    // Register new user
    fastify.post('/auth-new/register', {
        schema: authSchema.registerSchema,
        onRequest: [
            fastify.auth.authenticate,
            fastify.auth.requirePermission('system:user:create')
        ],
        handler: authController.register
    });
}
