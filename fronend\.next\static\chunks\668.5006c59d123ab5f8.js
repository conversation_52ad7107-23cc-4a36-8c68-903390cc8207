"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[668,7649],{668:(e,t,r)=>{r.r(t),r.d(t,{default:()=>c});var a=r(95155);r(12115);var n=r(90010),s=r(46102),l=r(30285),o=r(62525),i=r(48436),d=r(27893);let c=function(e){let{classes:t}=e,[r,{isLoading:c}]=(0,d.oW)(),u=async()=>{try{await r(t.id).unwrap(),i.l.success("班级删除成功")}catch(r){var e;let t=(null==r?void 0:null===(e=r.data)||void 0===e?void 0:e.message)||(null==r?void 0:r.message)||"删除失败，请稍后重试";i.l.error(t)}};return(0,a.jsx)(s.Bc,{delayDuration:300,children:(0,a.jsxs)(s.m_,{children:[(0,a.jsxs)(n.Lt,{children:[(0,a.jsx)(n.tv,{asChild:!0,children:(0,a.jsx)(s.k$,{asChild:!0,children:(0,a.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-destructive/10 hover:text-destructive",disabled:c,children:(0,a.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})})})}),(0,a.jsxs)(n.EO,{children:[(0,a.jsxs)(n.wd,{children:[(0,a.jsx)(n.r7,{className:"text-lg font-semibold text-destructive",children:"删除班级"}),(0,a.jsxs)(n.$v,{className:"mt-3 text-muted-foreground",children:["您确定要删除班级 ",(0,a.jsx)("span",{className:"font-semibold text-foreground",children:t.name})," 吗？"]}),(0,a.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,a.jsx)("div",{className:"text-destructive font-medium text-sm",children:"⚠️ 警告：此操作不可撤销"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"删除后将永久移除以下相关数据："}),(0,a.jsxs)("ul",{className:"ml-4 list-disc space-y-1 text-sm text-muted-foreground",children:[(0,a.jsx)("li",{children:"班级课表信息"}),(0,a.jsx)("li",{children:"班级学生关联"}),(0,a.jsx)("li",{children:"相关考勤记录"})]})]})]}),(0,a.jsxs)(n.ck,{children:[(0,a.jsx)(n.Zr,{disabled:c,children:"取消"}),(0,a.jsx)(n.Rx,{onClick:u,disabled:c,className:"bg-destructive hover:bg-destructive/90",children:c?"删除中...":"确认删除"})]})]})]}),(0,a.jsx)(s.ZI,{side:"top",className:"font-medium text-xs px-3 py-1.5",children:(0,a.jsx)("p",{children:"删除班级"})})]})})}},15452:(e,t,r)=>{r.d(t,{G$:()=>z,Hs:()=>N,UC:()=>et,VY:()=>ea,ZL:()=>Q,bL:()=>K,bm:()=>en,hE:()=>er,hJ:()=>ee,l9:()=>X});var a=r(12115),n=r(85185),s=r(6101),l=r(46081),o=r(61285),i=r(5845),d=r(19178),c=r(25519),u=r(34378),f=r(28905),p=r(63655),m=r(92293),x=r(93795),g=r(38168),v=r(99708),h=r(95155),y="Dialog",[j,N]=(0,l.A)(y),[b,w]=j(y),D=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:s,onOpenChange:l,modal:d=!0}=e,c=a.useRef(null),u=a.useRef(null),[f=!1,p]=(0,i.i)({prop:n,defaultProp:s,onChange:l});return(0,h.jsx)(b,{scope:t,triggerRef:c,contentRef:u,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:f,onOpenChange:p,onOpenToggle:a.useCallback(()=>p(e=>!e),[p]),modal:d,children:r})};D.displayName=y;var R="DialogTrigger",C=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,l=w(R,r),o=(0,s.s)(t,l.triggerRef);return(0,h.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":$(l.open),...a,ref:o,onClick:(0,n.m)(e.onClick,l.onOpenToggle)})});C.displayName=R;var A="DialogPortal",[E,I]=j(A,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:s}=e,l=w(A,t);return(0,h.jsx)(E,{scope:t,forceMount:r,children:a.Children.map(n,e=>(0,h.jsx)(f.C,{present:r||l.open,children:(0,h.jsx)(u.Z,{asChild:!0,container:s,children:e})}))})};O.displayName=A;var k="DialogOverlay",F=a.forwardRef((e,t)=>{let r=I(k,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,s=w(k,e.__scopeDialog);return s.modal?(0,h.jsx)(f.C,{present:a||s.open,children:(0,h.jsx)(_,{...n,ref:t})}):null});F.displayName=k;var _=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=w(k,r);return(0,h.jsx)(x.A,{as:v.DX,allowPinchZoom:!0,shards:[n.contentRef],children:(0,h.jsx)(p.sG.div,{"data-state":$(n.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),Z="DialogContent",P=a.forwardRef((e,t)=>{let r=I(Z,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,s=w(Z,e.__scopeDialog);return(0,h.jsx)(f.C,{present:a||s.open,children:s.modal?(0,h.jsx)(L,{...n,ref:t}):(0,h.jsx)(V,{...n,ref:t})})});P.displayName=Z;var L=a.forwardRef((e,t)=>{let r=w(Z,e.__scopeDialog),l=a.useRef(null),o=(0,s.s)(t,r.contentRef,l);return a.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,h.jsx)(M,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),V=a.forwardRef((e,t)=>{let r=w(Z,e.__scopeDialog),n=a.useRef(!1),s=a.useRef(!1);return(0,h.jsx)(M,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,l;null===(a=e.onCloseAutoFocus)||void 0===a||a.call(e,t),t.defaultPrevented||(n.current||null===(l=r.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),n.current=!1,s.current=!1},onInteractOutside:t=>{var a,l;null===(a=e.onInteractOutside)||void 0===a||a.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(s.current=!0));let o=t.target;(null===(l=r.triggerRef.current)||void 0===l?void 0:l.contains(o))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&s.current&&t.preventDefault()}})}),M=a.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:l,onCloseAutoFocus:o,...i}=e,u=w(Z,r),f=a.useRef(null),p=(0,s.s)(t,f);return(0,m.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:l,onUnmountAutoFocus:o,children:(0,h.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":$(u.open),...i,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(W,{titleId:u.titleId}),(0,h.jsx)(S,{contentRef:f,descriptionId:u.descriptionId})]})]})}),T="DialogTitle",B=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=w(T,r);return(0,h.jsx)(p.sG.h2,{id:n.titleId,...a,ref:t})});B.displayName=T;var G="DialogDescription",H=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=w(G,r);return(0,h.jsx)(p.sG.p,{id:n.descriptionId,...a,ref:t})});H.displayName=G;var U="DialogClose",Y=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,s=w(U,r);return(0,h.jsx)(p.sG.button,{type:"button",...a,ref:t,onClick:(0,n.m)(e.onClick,()=>s.onOpenChange(!1))})});function $(e){return e?"open":"closed"}Y.displayName=U;var q="DialogTitleWarning",[z,J]=(0,l.q)(q,{contentName:Z,titleName:T,docsSlug:"dialog"}),W=e=>{let{titleId:t}=e,r=J(q),n="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return a.useEffect(()=>{t&&!document.getElementById(t)&&console.error(n)},[n,t]),null},S=e=>{let{contentRef:t,descriptionId:r}=e,n=J("DialogDescriptionWarning"),s="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return a.useEffect(()=>{var e;let a=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&a&&!document.getElementById(r)&&console.warn(s)},[s,t,r]),null},K=D,X=C,Q=O,ee=F,et=P,er=B,ea=H,en=Y},17649:(e,t,r)=>{r.d(t,{UC:()=>Z,VY:()=>M,ZD:()=>L,ZL:()=>F,bL:()=>O,hE:()=>V,hJ:()=>_,l9:()=>k,rc:()=>P});var a=r(12115),n=r(46081),s=r(6101),l=r(15452),o=r(85185),i=r(99708),d=r(95155),c="AlertDialog",[u,f]=(0,n.A)(c,[l.Hs]),p=(0,l.Hs)(),m=e=>{let{__scopeAlertDialog:t,...r}=e,a=p(t);return(0,d.jsx)(l.bL,{...a,...r,modal:!0})};m.displayName=c;var x=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(l.l9,{...n,...a,ref:t})});x.displayName="AlertDialogTrigger";var g=e=>{let{__scopeAlertDialog:t,...r}=e,a=p(t);return(0,d.jsx)(l.ZL,{...a,...r})};g.displayName="AlertDialogPortal";var v=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(l.hJ,{...n,...a,ref:t})});v.displayName="AlertDialogOverlay";var h="AlertDialogContent",[y,j]=u(h),N=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...c}=e,u=p(r),f=a.useRef(null),m=(0,s.s)(t,f),x=a.useRef(null);return(0,d.jsx)(l.G$,{contentName:h,titleName:b,docsSlug:"alert-dialog",children:(0,d.jsx)(y,{scope:r,cancelRef:x,children:(0,d.jsxs)(l.UC,{role:"alertdialog",...u,...c,ref:m,onOpenAutoFocus:(0,o.m)(c.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=x.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(i.xV,{children:n}),(0,d.jsx)(I,{contentRef:f})]})})})});N.displayName=h;var b="AlertDialogTitle",w=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(l.hE,{...n,...a,ref:t})});w.displayName=b;var D="AlertDialogDescription",R=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(l.VY,{...n,...a,ref:t})});R.displayName=D;var C=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,d.jsx)(l.bm,{...n,...a,ref:t})});C.displayName="AlertDialogAction";var A="AlertDialogCancel",E=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,{cancelRef:n}=j(A,r),o=p(r),i=(0,s.s)(t,n);return(0,d.jsx)(l.bm,{...o,...a,ref:i})});E.displayName=A;var I=e=>{let{contentRef:t}=e,r="`".concat(h,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(h,"` by passing a `").concat(D,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(h,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return a.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},O=m,k=x,F=g,_=v,Z=N,P=C,L=E,V=w,M=R},62525:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},90010:(e,t,r)=>{r.d(t,{$v:()=>g,EO:()=>f,Lt:()=>i,Rx:()=>v,Zr:()=>h,ck:()=>m,r7:()=>x,tv:()=>d,wd:()=>p});var a=r(95155),n=r(12115),s=r(17649),l=r(59434),o=r(30285);let i=s.bL,d=s.l9,c=s.ZL,u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.hJ,{className:(0,l.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...n,ref:t})});u.displayName=s.hJ.displayName;let f=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(u,{}),(0,a.jsx)(s.UC,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...n})]})});f.displayName=s.UC.displayName;let p=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-2 text-center sm:text-left",t),...r})};p.displayName="AlertDialogHeader";let m=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...r})};m.displayName="AlertDialogFooter";let x=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold",r),...n})});x.displayName=s.hE.displayName;let g=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...n})});g.displayName=s.VY.displayName;let v=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.rc,{ref:t,className:(0,l.cn)((0,o.r)(),r),...n})});v.displayName=s.rc.displayName;let h=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.ZD,{ref:t,className:(0,l.cn)((0,o.r)({variant:"outline"}),"mt-2 sm:mt-0",r),...n})});h.displayName=s.ZD.displayName}}]);