const getLogsSchema = {
    tags: ['log'],
    type: 'object',
    querystring: {
        properties: {
            page: { 
                type: 'number',
                default: 1
            },
            pageSize: { 
                type: 'number',
                default: 10
            },
            userId: { type: 'string' },
            operationType: { type: 'string' },
            describe: { type: 'string' },
            startTime: { type: 'string' },
            endTime: { type: 'string' }
        }
    },
    response: {
        200: {
            type: 'object',
            properties: {
                code: { type: 'number' },
                data: { 
                    type: 'object',
                    properties: {
                        list: { 
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    operationType: { type: 'string' },
                                    describe: { type: 'string' },
                                    createdAt: { type: 'string' },
                                    userName: { type: 'string' }
                                }
                            }
                        },
                        total: { type: 'number' },
                        page: { type: 'number' },
                        pageSize: { type: 'number' }
                    }
                },
                message: { type: 'string' }
            }
        }
    }
    
};

export default {
    getLogsSchema,
}; 