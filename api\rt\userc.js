import {createError} from '@fastify/error';
import bcrypt from 'bcryptjs';
import { TwitterSnowflake } from "@sapphire/snowflake"
import { userLoginSchema, userRegisterSchema, userRoleMenus } from '../schemas/user.js';
import buildMenuTree from '../utils/buildMenuTree.js';

const AUTH_ERROR = createError('AUTH_ERROR', '%s', 401);
const INTERNAL_ERROR = createError('INTERNAL_ERROR', '%s', 500);

export default async function (fastify, opts) {
    fastify.post('/user/register', {
        schema: userRegisterSchema,

        handler: async function (request, reply) {
            const { account, password, name} = request.body;
            try {
                const existingUser = await fastify.prisma.user.findUnique({
                    where: { account }
                });
                
                if (existingUser) {
                    throw new AUTH_ERROR('用户已存在');
                }
                
                const hashendPassword = await bcrypt.hash(password,10);
                const id = TwitterSnowflake.generate().toString();
                
                await fastify.prisma.user.create({
                    data: {
                        id,
                        account,
                        password: hashendPassword,
                        name,
                    }
                })
                const token = await fastify.generateToken({id, account})
                reply.success({
                    message: '注册成功',
                    data: {
                        token
                    }
                })
            } catch (error) {
                if(error instanceof AUTH_ERROR) 
                    throw error;
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '数据库操作失败');
            }

        }
    })
    fastify.post('/user/login', {
        schema: userLoginSchema,
        handler: async function (request, reply) {
            const { account, password } = request.body;
            try {

                const user = await fastify.prisma.user.findUnique({
                    where: { account },
                    include: {
                        userInstitutions: {
                            include: {
                                institution: {
                                    select: {
                                        id: true,
                                        name: true,
                                        logo: true,
                                        subjectName: true,
                                        introduce: true,
                                        phone: true,
                                        telePhone: true,
                                        managerName: true
                                    }
                                }
                            }
                        }
                    }
                });
    
                if (!user) {
                    throw new AUTH_ERROR('账号不存在');
                }
    
                // 验证密码
                const validPassword = await bcrypt.compare(password, user.password);
                if (!validPassword) {
                    throw new AUTH_ERROR('密码错误');
                }
    
                // 移除敏感信息
                const { password: _, ...safeUser } = user;
    
                // 处理机构信息
                let institutions = [];
                if (user.userInstitutions && user.userInstitutions.length > 0) {
                    institutions = user.userInstitutions.map(ui => ({
                        id: ui.institution.id,
                        name: ui.institution.name,
                        logo: ui.institution.logo,
                        subjectName: ui.institution.subjectName,
                        introduce: ui.institution.introduce,
                        phone: ui.institution.phone,
                        telePhone: ui.institution.telePhone,
                        managerName: ui.institution.managerName,
                        joinedAt: ui.createdAt
                    }));
                }
    
                // 生成令牌
                const [token, refreshToken] = await Promise.all([
                    fastify.generateToken({
                        id: user.id,
                        account: user.account,
                        hasInstitution: institutions.length > 0
                    }),
                    fastify.generateRefreshToken({
                        id: user.id,
                        account: user.account
                    })
                ]);
    
                // 删除不需要返回的字段
                delete safeUser.userInstitutions;
    
                reply.success({
                    message: '登录成功',
                    data: {
                        user: {
                            ...safeUser,
                            institutions,
                            hasInstitution: institutions.length > 0
                        },
                        token,
                        refreshToken
                    }
                });

            } catch (error) {
                if (error instanceof AUTH_ERROR) {
                    throw error;
                }
                fastify.log.error({ error, account }, '登录失败');
                throw new AUTH_ERROR('登录失败，请稍后重试');
            }
        }
    });

    // 获取用户的角色菜单树
    fastify.get('/user/menus', {
        schema: userRoleMenus,
        onRequest: fastify.auth([fastify.verifyToken]),
        handler: async function (request, reply) {
            try {
                let user = request.user; // 从认证中间件获取当前用户
                user = await fastify.prisma.user.findUnique({
                    where: { id: user.id },
                })
                if(!user) {
                    throw new AUTH_ERROR('用户不存在');
                }
                // console.log(fastify.prisma.userRole,"fastify.prisma")
                let userRole = await fastify.prisma.userRole.findFirst({
                    where: {
                        userId: user.id
                    },
                    include: {
                        role: true
                    },
            
                })
                let userRoleId = userRole.role.id
                let userRolePermissions = await fastify.prisma.rolePermission.findMany({
                    where: {
                        roleId: userRoleId
                    }
                })
                let a = []
                const menuPromises = userRolePermissions.map(async p => {
                    return await fastify.prisma.menu.findFirst({
                        where: {
                            OR: [
                                { permissionId: p.permissionId},
                                { permissionId: null},
                            ]
                           
                        }
                    });
                });

                const menus = await Promise.all(menuPromises);
                menus.forEach(menu => {
                    if (menu) {
                        a.push(menu);
                    }
                });
                let menuTree = buildMenuTree(a)

                reply.success({
                    message: '获取用户菜单成功',
                    data: menuTree || []
                });
            } catch (error) {
                fastify.log.error(error);
                if (error instanceof AUTH_ERROR) {
                    throw error;
                }
                throw new INTERNAL_ERROR(error.message || '获取用户菜单失败');
            }
        }
    });
}