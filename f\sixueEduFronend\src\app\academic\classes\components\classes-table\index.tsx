import { DataTable } from '@/components/ui/data-table'
import React, { useEffect } from 'react'
import { classesTableColumns } from './columns'
import { useClasses } from '@/hooks/useClasses'
import { ClassesTableType } from './type'
import TablePagination from '@/components/TablePagination'
import { Input } from "@/components/ui/input"
import { Download, Filter, Search, UserPlus } from "lucide-react"
import { debounce } from 'lodash-es';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'

function ClassesTable() {
  const router = useRouter()
  const { getClassesList } = useClasses()
  const [classesData, setClassesData] = React.useState<ClassesTableType[]>([])
  const [currentPage, setCurrentPage] = React.useState(1);
  const [pageSize, setPageSize] = React.useState(10);
  const [totalItems, setTotalItems] = React.useState(0);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [selectedTeacher, setSelectedTeacher] = React.useState<string>("");
  const [statusFilter, setStatusFilter] = React.useState("all")

  // 创建防抖搜索函数
  const debouncedSearch = React.useCallback(
    debounce((query: string) => {
      setSearchQuery(query);
    }, 500),
    []
  );

  // 处理搜索输入
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(e.target.value);
  };

  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const getClassesListData = async () => {
    const params = {
      name: searchQuery,
      teacherId: selectedTeacher === 'all' ? '' : selectedTeacher,
      page: currentPage,
      pageSize: pageSize
    }
    const response = await getClassesList(params);
    const data = response.data;
    setClassesData(data.list);
    setTotalItems(Number.parseInt(data.total));
    setPageSize(Number.parseInt(data.pageSize));
    setCurrentPage(Number.parseInt(data.page));
  }



  useEffect(() => {
    getClassesListData()

  }, [searchQuery, selectedTeacher, currentPage, pageSize])

  return (
    <div className="space-y-6 rounded-lg bg-white p-5 shadow-sm transition-all">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-4 border-b border-gray-100">
        <h2 className="text-xl font-medium text-gray-800">班级管理</h2>
        <Button 
          onClick={() => router.push("/academic/classes/new")} 
          className="h-10 gap-1.5"
        >
          <UserPlus className="h-4 w-4" />
          新增班级
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex flex-wrap items-center gap-3 w-full sm:w-auto">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="h-10 w-[140px] rounded-md border-gray-200 shadow-sm transition-all hover:border-gray-300">
              <SelectValue placeholder="班级状态" />
            </SelectTrigger>
            <SelectContent className="rounded-md">
              <SelectItem value="all">全部班级</SelectItem>
              <SelectItem value="active">进行中</SelectItem>
              <SelectItem value="upcoming">未开始</SelectItem>
              <SelectItem value="completed">已结束</SelectItem>

              </SelectContent>
          </Select>

          <div className="relative flex-1 w-full sm:w-auto">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              type="search"
              placeholder="搜索班级..."
              className="pl-10 h-10 w-full sm:w-[250px] lg:w-[320px] rounded-md border-gray-200 shadow-sm transition-all hover:border-gray-300"
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>
        </div>

        <div className="flex items-center gap-3 self-end sm:self-auto">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon" className="h-10 w-10 rounded-md border-gray-200 shadow-sm transition-all hover:border-gray-300 hover:bg-gray-50">
                <Filter className="h-4 w-4 text-gray-500" />
                <span className="sr-only">筛选</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px] rounded-md">
              <DropdownMenuItem className="cursor-pointer hover:bg-gray-50">按创建时间</DropdownMenuItem>
              <DropdownMenuItem className="cursor-pointer hover:bg-gray-50">按班级名称</DropdownMenuItem>
              <DropdownMenuItem className="cursor-pointer hover:bg-gray-50">按学生人数</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button 
            variant="outline" 
            size="icon" 
            className="h-10 w-10 rounded-md border-gray-200 shadow-sm transition-all hover:border-gray-300 hover:bg-gray-50"
          >
            <Download className="h-4 w-4 text-gray-500" />
            <span className="sr-only">导出</span>
          </Button>
        </div>
      </div>
        <DataTable 
          columns={classesTableColumns} 
          data={classesData} 
          pagination={false}
          className="bg-white" 
        />
 
      
      <div className="mt-4">
        <TablePagination
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={totalItems}
          onPageChange={(page) => {
            setCurrentPage(page);
          }}
          onPageSizeChange={(size) => {
            setPageSize(size);
          }}
        />
      </div>
    </div>
  )
}

export default ClassesTable