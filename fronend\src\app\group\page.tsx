'use client';

import React, { useState } from 'react';
import ActionButtons from "./components/ActionButtons";
import SignUpModal from './components/SignUpModal';
import StatsBar from './components/StatsBar';
import ShareModal from "./components/ShareModal";
import PriceTag from './components/PriceTag';
import ModuleBlock from './components/ModuleBlock';

export default function GroupPage() {
    const [showModal, setShowModal] = useState(false);
    const [showShareModal, setShowShareModal] = useState(false);
    // 这里建议用 props 或 fetch，演示用静态数据
    const res = {
        "data": {
            "html": {
                "module": [{
                    "title": "活动介绍",
                    "list": [{
                        "text": "速写，很多人理解成是写，或者是就像中国画很写意的书写作画，这些只说对了一种表面，速写不仅仅是一种绘画方式，更是通过速写锻炼作画者对现实生活的观察能力，分析能力与表现能力，通过作画让我们学会提炼，总结。在入门阶段我们就要做好两点充分的准备，思想准备与材料准备，并且立下恒心，因为速写不是一朝一夕就能够达到一定水平的。",
                        "type": "text"
                    }, {
                        "img": "https://image.cardmee.net/2/2021/05/image/1621906385171587078.png",
                        "type": "img"
                    }]
                }, {
                    "title": "拼团说明",
                    "list": [{
                        "text": "1.点击按钮\"我要开团\"或者\"我要参团\"\n2.参团或者开团后分享给好友，邀他一起加入。\n3.发起拼团之后24小时之内没成团的为无效团，系统\n   将会自动取消该拼团。\n4.满足对应团人数，再有人参与进来则自动升级成下\n   一团。\n5.拼团成功后生成核销码。\n6.活动结束之后去线下机构核销兑换。\n",
                        "type": "text"
                    }]
                }, {
                    "title": "机构介绍",
                    "list": [{
                        "text": "成童成美艺术是英国纳斯达克上市公司达内教育集团旗下少儿艺术教育品牌，依托集团公司18年的教学经验，专注3-18 岁少儿教育及服务， 教学体系落实创新教育理念，强调学生综合能力的培养和实际解决问题能力的提升，在兴趣激发和思维锻炼的同时，传递前沿技术，帮助中国青少年打造迎接未来世界的能力和思维视野。 作为线上线下一体化的全国化的运营品牌，线下建立了250余家校区，流动课堂走进120多个中小学公立校。线上，依靠在线教学平台，可以实现覆盖全国各地。成童成美已成为中国最大的少儿艺术教育品牌之一，累计培养学员80000万多名。",
                        "type": "text"
                    }, {
                        "img": "https://image.cardmee.net/2/2021/05/image/1621906462458113710.png",
                        "type": "img"
                    }, {
                        "img": "https://image.cardmee.net/2/2021/05/image/1621906472776731239.png",
                        "type": "img"
                    }]
                }],
                "type": 19,
                "audioUrl": "https://image.cardmee.net/540/2020/04/voice/0d3fb888-f41a-4021-8e2d-22a93b3150e5.mp3",
                "audioName": "My Wonderful Car.mp3",
                "ele1": "https://image.cardmee.net/2/2021/11/image/1636430859349609527.png"
            },
            "options": {
                "cover": "https://image.cardmee.net/default/2019/10/image/5fe2d601-9cfb-4e4f-a94f-3f78173b612d.png",
                "description": "",
                "encryptionTemplateId": "gT8dKnIrvNtuzGMFTVCepQ",
                "htmlUrl": "pages/user/front_singleGroup/",
                "level": 3,
                "maxPersons": null,
                "name": "不止5折，拼团啦！",
                "seller": {
                    "openPay": 1
                },
                "shareDesc": "团拼来袭，疯狂抢不停",
                "sharePic": "https://image.cardmee.net/default/2019/10/image/64657c6d-f3bf-4a0b-ba86-057fbc414f39.png",
                "shareTitle": "一起来拼",
                "type": 5,
                "buttons": [
                    {
                        "text": "我要开团",
                        "type": "open",
                        "enabled": true
                    },
                ],
                // stats: [
                //     { value: 10, label: '人查看' },
                //     { value: 100, label: '人分享' },
                //     { value: 200, label: '人参与' }
                // ],
                prices: {
                    currentPrice: "0.01",
                    originalPrice: "3.02",
                    endTime: new Date().getTime() + 3 * 24 * 60 * 60 * 1000 // 3天后结束
                },
                formFields: [
                    {
                        "id": "name",
                        "label": "学员姓名",
                        "type": "text",
                        "required": true,
                        "placeholder": "请输入学员姓名"
                    },
                    {
                        "id": "age",
                        "label": "学员年龄",
                        "type": "number",
                        "required": true,
                        "placeholder": "请输入学员年龄"
                    },
                    {
                        "id": "phone",
                        "label": "联系电话",
                        "type": "text",
                        "required": true,
                        "placeholder": "请输入联系电话"
                    },
                ]
            }
        },
        "msg": "",
        "code": 0
    };
    const htmlData = res.data.html;
    const options = res.data.options;
    const buttons = options.buttons || [
        { text: "我要开团", type: "open", enabled: true },
        { text: "我要参团", type: "join", enabled: true }
    ];

    const handleAction = (type: string) => {
        if (type === 'open') {
            setShowModal(true);
        } else if (type === 'join') {
        }
    };

    const handleShare = () => {
        setShowShareModal(true);
    };

    return (
        <div className="bg-white min-h-screen font-sans pb-20">
            {/* <div className="sticky top-0 z-50">
                <StatsBar stats={options.stats} audioUrl={htmlData.audioUrl} />
            </div> */}
            
            <img src={options.cover} alt="banner" className="w-full" />
            
            <PriceTag 
                currentPrice={options.prices.currentPrice} 
                originalPrice={options.prices.originalPrice} 
                endTime={options.prices.endTime} 
            />

            <div className="flex items-start justify-between mt-2.5 px-4">
                <div>
                    <h1 className="text-xl text-red-500">{options.name}</h1>
                    <p className="text-sm text-gray-400 mb-2.5">{options.shareDesc}</p>
                </div>
                <button 
                    onClick={handleShare}
                    className="flex flex-col items-center text-gray-500 pt-1"
                >
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
                            <polyline points="16 6 12 2 8 6"></polyline>
                            <line x1="12" y1="2" x2="12" y2="15"></line>
                        </svg>
                    </div>
                    <span className="text-xs mt-1">分享</span>
                </button>
            </div>

            <div className="flex justify-around my-4">
                <ActionButtons buttons={buttons} onAction={handleAction} />
            </div>

            {htmlData.module.map((mod: any, idx: number) => (
                <ModuleBlock key={idx} title={mod.title} list={mod.list} />
            ))}

            <div className="fixed inset-x-0 bottom-0 bg-white shadow-lg flex justify-around py-2.5">
                <ActionButtons buttons={buttons} onAction={handleAction} />
            </div>
            {showModal && (
                <SignUpModal 
                    open={showModal} 
                    onClose={() => setShowModal(false)} 
                    formFields={(options.formFields || []) as any}
                />
            )}
            {showShareModal && (
                <ShareModal 
                    open={showShareModal} 
                    onClose={() => setShowShareModal(false)} 
                    shareTitle={options.shareTitle || options.name}
                    shareDesc={options.shareDesc}
                    qrCode={`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(window.location.href)}`}
                />
            )}
        </div>
    );
}