"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9831],{57141:(e,l,t)=>{t.d(l,{C9:()=>r,DT:()=>s,I2:()=>b,IC:()=>u,N4:()=>g,fb:()=>i,lc:()=>a,oD:()=>d,u7:()=>c,uq:()=>o,x9:()=>n});let a={"limited-sessions":{label:"限次",color:"capitalize font-normal bg-blue-50 text-blue-700 hover:bg-blue-100 px-2.5 py-0.5 rounded-md"},"limited-time-and-count":{label:"时次限",color:"capitalize font-normal bg-violet-50 text-violet-700 hover:bg-violet-100 px-2.5 py-0.5 rounded-md"},default:{label:"未知套餐",color:"capitalize font-normal bg-slate-100 text-slate-700 hover:bg-slate-200 px-2.5 py-0.5 rounded-md"}},r={cash:{label:"现金",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},alipay:{label:"支付宝",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},wechat:{label:"微信",color:"bg-green-100 text-green-800 hover:bg-green-200"},card:{label:"银行卡",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},other:{label:"其他",color:"bg-slate-100 text-slate-800 hover:bg-slate-200"}},o=[{id:"wechat",label:"微信"},{id:"alipay",label:"支付宝"},{id:"card",label:"银行转账"},{id:"cash",label:"现金"},{id:"other",label:"其他"}],b={done:{label:"完成",color:"bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400"},arrears:{label:"欠费",color:"bg-amber-100 text-amber-800 hover:bg-amber-200 dark:bg-amber-900/30 dark:text-amber-400"},refunded:{label:"退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400"},default:{label:"未知",color:"bg-slate-100 text-slate-800 hover:bg-slate-200 dark:bg-slate-800/50 dark:text-slate-400"}},s={active:{badgeClass:"bg-emerald-50 text-emerald-700 hover:bg-emerald-100 border-emerald-200",dotClass:"bg-emerald-500",text:"正常"},expired:{badgeClass:"bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200",dotClass:"bg-amber-500",text:"已到期"},refunded:{badgeClass:"bg-red-50 text-red-700 hover:bg-red-100 border-red-200",dotClass:"bg-red-500",text:"已退款"},frozen:{badgeClass:"bg-slate-50 text-slate-700 hover:bg-slate-100 border-slate-200",dotClass:"bg-slate-500",text:"已冻结"},default:{badgeClass:"bg-slate-100 text-slate-700 hover:bg-slate-200 border-slate-200",dotClass:"bg-slate-400",text:"未知状态"}},d={all:{className:"bg-slate-100 text-slate-800 hover:bg-slate-200",text:"全部状态"},attendance:{className:"bg-green-100 text-green-800 hover:bg-green-200",text:"已考勤"},leave:{className:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200",text:"请假"},unattended:{className:"bg-orange-100 text-orange-800 hover:bg-orange-200",text:"未考勤"},default:{className:"bg-red-100 text-red-800 hover:bg-red-200",text:"缺勤"}},n={A:{label:"非常感兴趣",color:"bg-green-100 text-green-800 hover:bg-green-200"},B:{label:"比较感兴趣",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},C:{label:"一般意向",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},D:{label:"了解意向",color:"bg-orange-100 text-orange-800 hover:bg-orange-200"},E:{label:"不感兴趣",color:"bg-red-100 text-red-800 hover:bg-red-200"},default:{label:"未知",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},g={productSales:{label:"产品销售",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},courseSales:{label:"课程销售",color:"bg-green-100 text-green-800 hover:bg-green-200"},OverduePaymentCollection:{label:"逾期款项催收",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},lifestyleAndOffice:{label:"生活和办公",color:"bg-red-100 text-red-800 hover:bg-red-200"},socialAndCatering:{label:"社交和餐饮",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},refunded:{label:"退款",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"其他",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},c={completed:{label:"已完成",color:"bg-green-100 text-green-800 hover:bg-green-200"},pending:{label:"待审核",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},refunded:{label:"已退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},failed:{label:"审核未通过",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"未知状态",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},u={formal:{label:"正式学员",color:"text-blue-500"},graduated:{label:"历史学员",color:"text-green-500"},public:{label:"公海池",color:"text-red-500"},intent:{label:"意向学员",color:"text-yellow-500"}},i={secret:{label:"保密",color:"text-gray-500"},male:{label:"男",color:"text-blue-500"},female:{label:"女",color:"text-pink-500"}}},79831:(e,l,t)=>{t.r(l),t.d(l,{default:()=>k});var a=t(95155),r=t(12115),o=t(9110),b=t(26126),s=t(73168),d=t(55028),n=t(50228);let g=(0,d.default)(()=>t.e(5799).then(t.bind(t,55799)),{loadableGenerated:{webpack:()=>[55799]},ssr:!1}),c=(0,d.default)(()=>Promise.all([t.e(81),t.e(7295)]).then(t.bind(t,77295)),{loadableGenerated:{webpack:()=>[77295]},ssr:!1}),u=(0,d.default)(()=>t.e(1289).then(t.bind(t,33670)),{loadableGenerated:{webpack:()=>[33670]},ssr:!1}),i=[{accessorKey:"name",header:"姓名",cell:e=>{let{row:l}=e;return(0,a.jsx)("div",{className:"font-medium text-gray-900",children:l.getValue("name")})}},{accessorKey:"phone",header:"手机号码",cell:e=>{let{row:l}=e;return(0,a.jsx)("div",{className:"text-gray-600",children:l.getValue("phone")})}},{accessorKey:"intentLevel",header:"意向级别",cell:e=>{let{row:l}=e,t=l.getValue("intentLevel"),r="",o="";switch(t){case"A":r="非常感兴趣",o="bg-green-100 text-green-800";break;case"B":r="比较感兴趣",o="bg-blue-100 text-blue-800";break;case"C":r="一般意向",o="bg-yellow-100 text-yellow-800";break;case"D":r="了解意向",o="bg-orange-100 text-orange-800";break;case"E":r="不感兴趣",o="bg-red-100 text-red-800";break;default:r="未知",o="bg-gray-100 text-gray-800"}return(0,a.jsx)(b.E,{variant:"outline",className:"px-2.5 py-1 rounded-md font-medium ".concat(o),children:r})}},{accessorKey:"followStatus",header:"跟进状态",cell:e=>{let{row:l}=e;return(0,a.jsx)(b.E,{variant:"outline",className:"bg-purple-100 text-purple-800 px-2.5 py-1 rounded-md font-medium",children:"跟进中"})}},{accessorKey:"followPerson",header:"跟进人",cell:e=>{var l;let{row:t}=e;return(0,n.P)(null===(l=t.original.follower)||void 0===l?void 0:l.name)}},{accessorKey:"followUpDate",header:"跟进时间",cell:e=>{let{row:l}=e;return(0,n.s)(l.getValue("followUpDate")?(0,s.GP)(new Date(l.getValue("followUpDate")),"yyyy-MM-dd HH:mm:ss"):"",12)}},{accessorKey:"source",header:"学员来源",cell:e=>{let{row:l}=e;return(0,n.s)(l.getValue("source"),4)}},{accessorKey:"sourceDesc",header:"来源描述",cell:e=>{let{row:l}=e;return(0,n.s)(l.getValue("sourceDesc"),4)}},{accessorKey:"remark",header:"备注",cell:e=>{let{row:l}=e;return(0,n.s)(l.getValue("remark"),4)}},{id:"actions",header:"操作",cell:e=>{let{row:l}=e,t=l.original;return(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)(u,{studentId:t.id}),(0,a.jsx)(c,{studentId:t.id}),(0,a.jsx)(g,{studentId:t.id,studentType:t.type})]})}}];var x=t(62523),h=t(59409),m=t(57141),v=t(48432),p=t(95728),f=t(45964),y=t.n(f);let w={intentLevel:"all",search:""};function k(){let[e,l]=(0,r.useState)(1),[t,b]=(0,r.useState)(10),[s,d]=(0,r.useState)(w),n=(0,r.useCallback)(y()(e=>{d(l=>({...l,search:e.trim()}))},500),[]),g=(0,r.useCallback)(e=>{let l=e.target.value;e.target.value=l,n(l)},[n]),c=(0,r.useCallback)((e,t)=>{d(l=>({...l,[e]:t})),"intentLevel"===e&&l(1)},[]),u=(0,r.useMemo)(()=>({page:e,pageSize:t,search:s.search,type:"intent",intentLevel:"all"===s.intentLevel?void 0:s.intentLevel}),[e,t,s]),{data:f,isLoading:k}=(0,p.FQ)(u);return(0,r.useEffect)(()=>()=>{n.cancel()},[n]),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between items-start sm:items-center gap-4",children:[(0,a.jsx)("div",{className:"w-56 sm:w-56",children:(0,a.jsx)(x.p,{placeholder:"请输入学员的姓名/手机号",className:"w-full",defaultValue:s.search,onChange:g})}),(0,a.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 sm:items-center",children:(0,a.jsxs)(h.l6,{value:s.intentLevel,onValueChange:e=>c("intentLevel",e),children:[(0,a.jsx)(h.bq,{className:"w-full sm:w-[150px]",children:(0,a.jsx)(h.yv,{placeholder:"意向级别"})}),(0,a.jsxs)(h.gC,{children:[(0,a.jsx)(h.eb,{value:"all",children:"全部"}),Object.entries(m.x9).map(e=>{let[l,t]=e;return(0,a.jsx)(h.eb,{value:l,children:t.label},l)})]})]})})]})}),(0,a.jsx)(o.b,{pagination:!1,columns:i,data:(null==f?void 0:f.list)||[],loading:k},"targetStudentDataTable"),(0,a.jsx)(v.default,{currentPage:(null==f?void 0:f.page)||1,pageSize:(null==f?void 0:f.pageSize)||10,totalItems:(null==f?void 0:f.total)||0,onPageChange:l,onPageSizeChange:b})]})}}}]);