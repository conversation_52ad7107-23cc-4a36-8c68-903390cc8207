(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{27600:(e,t,o)=>{"use strict";o.d(t,{A:()=>d,AuthProvider:()=>f});var r=o(95155),n=o(65436),s=o(75521),i=o(98553),a=o(35695),u=o(12115),l=o(32422);let c=(0,u.createContext)(void 0),p=["/","/login","/404","/unauthorized","/1","/notifications/list","/features","/features/course-scheduling"];function f(e){let{children:t}=e,o=(0,n.G)(e=>e.user),[f,d]=(0,u.useState)([]),[h,m]=(0,u.useState)(!0),[g,b]=(0,u.useState)(!1),y=(0,n.j)(),v=(0,a.useRouter)(),S=(0,a.usePathname)(),k=(0,n.G)(e=>e.userMenus.menus),P=(0,n.G)(e=>e.userPermissions.permissions);console.log(P,"userPermissions");let{data:w,isLoading:_}=(0,s.DQ)({},{skip:!o.isAuthenticated||!o.token||((null==k?void 0:k.length)||0)>0}),{data:C,isLoading:O}=(0,i.H)({},{skip:!o.isAuthenticated||!o.token||((null==P?void 0:P.length)||0)>0}),I=(0,u.useCallback)((e,t)=>{if(!e)return!1;if(e===t)return!0;let o=e.split("/").filter(Boolean),r=t.split("/").filter(Boolean);if(1>=Math.abs(o.length-r.length)){if(r.length===o.length+1)for(let e=0;e<r.length;e++){let t=[...r];if(t.splice(e,1),o.join("/")===t.join("/"))return!0}if(r.length===o.length&&r.length>1){let e=0,t=0;for(let n=0;n<o.length;n++)o[n]!==r[n]?e++:t++;if(1===e&&t>0)return!0}}if(e.replace(/\/\d+(?=\/|$)/g,"")===t.replace(/\/\d+(?=\/|$)/g,""))return!0;if(e.includes("["))try{let o=e.replace(/\/\[\.\.\..*?\]/g,"/(.+)").replace(/\/\[.*?\]/g,"/([^/]+)");return new RegExp("^".concat(o,"$")).test(t)}catch(e){console.error("路径匹配正则表达式错误:",e)}return!1},[]),E=(0,u.useCallback)((e,t)=>{for(let o of e)if(I(o.path,t)||o.children&&o.children.length>0&&E(o.children,t))return!0;return!1},[I]),A=(0,u.useMemo)(()=>e=>!!p.includes(e)||!!o.isAuthenticated&&E(f,e),[o.isAuthenticated,f,E]);async function j(){let e=o.token||localStorage.getItem("token");console.log(e,"user token"),await fetch("".concat("http://127.0.0.1:3001/api/","/api/auth/logout"),{method:"POST",headers:{Authorization:"Bearer ".concat(e)}})}(0,u.useEffect)(()=>{k.length>0?(d(k),m(!1),b(!0)):w?(d(w),y({type:"userMenus/setUserMenus",payload:w}),m(!1),b(!0)):m(_)},[k,w,_,y]),console.log(C,"permissionsData"),(0,u.useEffect)(()=>{C&&y((0,l.jb)({permissions:C}))},[C]),(0,u.useEffect)(()=>{if(g&&!h&&S&&!A(S)){if(console.log("没有权限访问该页面",S),o.isAuthenticated){let e=setTimeout(()=>{A(S)||v.push("/404")},100);return()=>clearTimeout(e)}v.push("/login?redirect=".concat(encodeURIComponent(S)))}},[S,h,g,o.isAuthenticated,v,A]);let x=(0,u.useCallback)(async()=>{try{await j()}catch(e){console.error("登出API调用失败:",e)}finally{y({type:"user/clearUserInfo"}),y({type:"userMenus/clearMenus"}),y({type:"userPermissions/clearPermissions"}),localStorage.removeItem("userInfo"),localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("refreshToken"),sessionStorage.removeItem("token"),sessionStorage.removeItem("refresh_token"),sessionStorage.removeItem("refreshToken"),localStorage.removeItem("persist:root"),v.replace("/login")}},[y,v]);return(0,r.jsx)(c.Provider,{value:{menus:f,loading:h,checkAccess:A,logout:x},children:t})}let d=()=>{let e=(0,u.useContext)(c);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},35695:(e,t,o)=>{"use strict";var r=o(18999);o.o(r,"useParams")&&o.d(t,{useParams:function(){return r.useParams}}),o.o(r,"usePathname")&&o.d(t,{usePathname:function(){return r.usePathname}}),o.o(r,"useRouter")&&o.d(t,{useRouter:function(){return r.useRouter}})},39737:(e,t,o)=>{"use strict";o.d(t,{WebSocketProvider:()=>u});var r=o(95155),n=o(27600),s=o(65436),i=o(12115);let a=(0,i.createContext)(null);function u(e){let{children:t}=e,o=(0,s.G)(e=>e.user),{logout:u}=(0,n.A)(),[l,c]=(0,i.useState)(0),[p,f]=(0,i.useState)(null),d=(0,s.j)(),h=(0,s.G)(e=>e.notificationsUnreadCount.count);function m(){if(!o.isAuthenticated)return null;if(!("WebSocket"in window))return console.error("当前浏览器不支持 WebSocket。"),null;let e=new WebSocket("".concat("ws://127.0.0.1:3001/api/ws","?token=").concat(o.token));e.onopen=()=>{console.log("WebSocket 连接已打开。"),f(e)}}async function g(e){let t=JSON.parse(e.data);switch(console.log("收到消息:",t),t.type){case"CONNECT_SUCCESS":console.log("连接成功！");break;case"PONG":break;case"NOTIFICATION_ADD":d({type:"notificationsUnreadCount/setUnreadCount",payload:{count:h+1}}),console.log("通知:",t.notification);break;case"ROLE_PERMISSION_UPDATE":console.log("角色权限更新:",t.permissions),u();break;default:console.log("未知消息类型:",t.type)}}return(0,i.useEffect)(()=>{p&&p&&(p.onmessage=e=>g(e))}),(0,i.useEffect)(()=>{p&&(p.onclose=e=>{console.log("WebSocket 连接已关闭。",e),l<3?(c(e=>e+1),console.log("尝试重新连接... (".concat(l,")")),setTimeout(()=>{m()},2e3)):l<5?(c(e=>e+1),console.log("尝试重新连接... (".concat(l,")")),setTimeout(()=>{m()},5e3)):console.error("已达到最大重连次数，停止尝试。")})},[p]),(0,i.useEffect)(()=>{p||m();let e=setInterval(()=>{!function(){if(p&&p.readyState===WebSocket.OPEN){console.log("发送心跳包...");let e=JSON.stringify({type:"ping"});p.send(e)}}()},3e3);return()=>{clearInterval(e)}}),(0,r.jsx)(a.Provider,{value:p,children:t})}},52728:()=>{},65249:(e,t,o)=>{"use strict";o.d(t,{Toaster:()=>h});var r=o(95155),n=o(12115),s="(prefers-color-scheme: dark)",i=n.createContext(void 0),a={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=n.useContext(i))?e:a},l=null,c=(e,t)=>{let o;try{o=localStorage.getItem(e)||void 0}catch(e){}return o||t},p=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},f=e=>(e||(e=window.matchMedia(s)),e.matches?"dark":"light"),d=o(56671);let h=e=>{let{...t}=e,{theme:o="system"}=u();return(0,r.jsx)(d.l$,{theme:o,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...t})}},65436:(e,t,o)=>{"use strict";o.d(t,{G:()=>s,j:()=>n});var r=o(34540);let n=()=>(0,r.wA)(),s=r.d4},72514:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,52728,23)),Promise.resolve().then(o.bind(o,79909)),Promise.resolve().then(o.bind(o,65249)),Promise.resolve().then(o.bind(o,27600)),Promise.resolve().then(o.bind(o,39737))},79909:(e,t,o)=>{"use strict";o.d(t,{Providers:()=>d});var r=o(95155),n=o(34540),s=o(7986),i=o(12115);function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function u(e){return(u=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function l(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function c(e,t){return(c=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function p(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}var f=function(e){function t(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,t);for(var e,o,r,n=arguments.length,s=Array(n),i=0;i<n;i++)s[i]=arguments[i];return r=(e=(o=u(t)).call.apply(o,[this].concat(s)))&&("object"===a(e)||"function"==typeof e)?e:l(this),p(l(r),"state",{bootstrapped:!1}),p(l(r),"_unsubscribe",void 0),p(l(r),"handlePersistorState",function(){r.props.persistor.getState().bootstrapped&&(r.props.onBeforeLift?Promise.resolve(r.props.onBeforeLift()).finally(function(){return r.setState({bootstrapped:!0})}):r.setState({bootstrapped:!0}),r._unsubscribe&&r._unsubscribe())}),r}return!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&c(e,t)}(t,e),function(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(t.prototype,[{key:"componentDidMount",value:function(){this._unsubscribe=this.props.persistor.subscribe(this.handlePersistorState),this.handlePersistorState()}},{key:"componentWillUnmount",value:function(){this._unsubscribe&&this._unsubscribe()}},{key:"render",value:function(){return"function"==typeof this.props.children?this.props.children(this.state.bootstrapped):this.state.bootstrapped?this.props.children:this.props.loading}}]),t}(i.PureComponent);function d(e){let{children:t}=e;return(0,r.jsx)(n.Kq,{store:s.M,children:(0,r.jsx)(f,{loading:null,persistor:s.q,children:t})})}p(f,"defaultProps",{children:null,loading:null})}},e=>{var t=t=>e(e.s=t);e.O(0,[7690,8687,4201,4540,9624,6315,7358],()=>t(72514)),_N_E=e.O()}]);