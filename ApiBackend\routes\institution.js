import { createError } from '@fastify/error';

const INTERNAL_ERROR = createError('INTERNAL_ERROR', '%s', 500);
export default async function (fastify, opts) {
    // 获取所有老师
    fastify.get('/institutions/teachers/select', {
        schema: {
            tags: ['institutions'],
            summary: '获取机构所有老师',
            querystring: {
                type: 'object',
                properties: {
                    id: {
                        type: 'boolean',
                        default: true
                    },
                    name: {
                        type: 'boolean',
                        default: true
                    },
                    phone: {
                        type: 'boolean',
                        default: false
                    },
                    account: {
                        type: 'boolean',
                        default: false
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user;
            const { phone, id, name, account } = request.query
            const client = await fastify.pg.connect();
            try {
                const redisKey = `teachers:${user.institutionId}:${phone}:${id}:${name}:${account}`;
                const cachedResult = await fastify.redis.get(redisKey);
                if (cachedResult) {
                    return reply.success({
                        data: JSON.parse(cachedResult),
                        message: '获取机构老师成功'
                    })
                }

                // 构建SELECT字段
                const selectFields = [];
                if (id) selectFields.push('u.id');
                if (name) selectFields.push('u.name');
                if (account) selectFields.push('u.account');
                if (phone) selectFields.push('u.phone');

                const selectClause = selectFields.length > 0 ? selectFields.join(', ') : 'u.id, u.name';

                // 使用JOIN查询获取用户信息
                const result = await client.query(
                    `SELECT ${selectClause}
                     FROM "user_institution" ui
                     JOIN users u ON ui."userId" = u.id
                     WHERE ui."institutionId" = $1`,
                    [user.institutionId]
                );

                const staffs = result.rows;

                await fastify.redis.set(redisKey, JSON.stringify(staffs), 'EX', 60 * 60 * 24); // 缓存24小时

                reply.success({
                    message: '获取机构老师成功',
                    data: staffs
                })
            } catch (error) {
                console.log(error);
                throw new INTERNAL_ERROR(error.message || '获取老师列表失败');
            } finally {
                client.release();
            }
        }
    })

    // 获取机构操作日志
    fastify.get('/institutions/operationLogs', {
        schema: {
            tags: ['institutions'],
            summary: '获取机构操作日志',
            querystring: {
                type: 'object',
                properties: {
                    page: {
                        type: 'number',
                        default: 1
                    },
                    pageSize: {
                        type: 'number',
                        default: 10
                    },
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const { page = 1, pageSize = 10, userId, operationType, describe, startTime, endTime } = request.query;
            const user = request.user;
            const client = await fastify.pg.connect();
            try {


                // 转换页码和每页数量为数字
                const pageNum = Number(page);
                const pageSizeNum = Number(pageSize);

                // 构建WHERE条件
                const conditions = ['"institutionId" = $1'];
                const queryParams = [user.institutionId];
                let paramIndex = 2;

                if (userId) {
                    conditions.push(`ol."userId" = $${paramIndex}`);
                    queryParams.push(userId);
                    paramIndex++;
                }

                if (operationType) {
                    conditions.push(`ol."operationType" = $${paramIndex}`);
                    queryParams.push(operationType);
                    paramIndex++;
                }

                if (describe) {
                    conditions.push(`ol."describe" ILIKE $${paramIndex}`);
                    queryParams.push(`%${describe}%`);
                    paramIndex++;
                }

                if (startTime) {
                    conditions.push(`ol."createdAt" >= $${paramIndex}`);
                    queryParams.push(new Date(startTime));
                    paramIndex++;
                }

                if (endTime) {
                    conditions.push(`ol."createdAt" <= $${paramIndex}`);
                    queryParams.push(new Date(endTime));
                    paramIndex++;
                }

                const whereClause = conditions.join(' AND ');

                // 构建总数查询
                const countQuery = `
            SELECT COUNT(*) as total
            FROM "operation_log" as ol
            WHERE ${whereClause}
        `;

                // 构建数据查询，包含用户关联
                const dataQuery = `
            SELECT 
                ol.id,
                ol."operationType",
                ol."describe",
                ol."createdAt",
                u."name" as "userName"
            FROM 
                "operation_log" ol
            LEFT JOIN 
                "users" u ON ol."userId" = u.id
            WHERE 
                ${whereClause}
            ORDER BY 
                ol."createdAt" DESC
            OFFSET $${paramIndex}
            LIMIT $${paramIndex + 1}
        `;

                const paginationParams = [...queryParams, (pageNum - 1) * pageSizeNum, pageSizeNum];
                // 并行执行两个查询
                const [totalResult, logsResult] = await Promise.all([
                    client.query(countQuery, queryParams),
                    client.query(dataQuery, paginationParams)
                ]);

                const total = parseInt(totalResult.rows[0].total);
                return reply.success({
                    message: '获取机构操作日志成功.',
                    data: {
                        list: logsResult.rows,
                        total,
                        page: pageNum,
                        pageSize: pageSizeNum
                    }
                })
            } catch (error) {
                throw new INTERNAL_ERROR(error.message || '获取机构操作日志失败');
            }
        }
    })
    // 获取机构信息
    fastify.get("/institutions/info", {
        schema: {
            tags: ['institutions'],
            summary: '获取机构信息',
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user
            const client = await fastify.pg.connect();
            try {
                const query = `
                    SELECT 
                        i.id as "id",
                        i.name as "name",
                        i."subjectName" as "subjectName",
                        i.uscc as "uscc",
                        i.logo as "logo",
                        i.introduce as "introduce",
                        i.phone as "phone",
                        i."telePhone" as "telePhone",
                        i."managerName" as "managerName",
                        i."createdAt" as "createdAt"
                    FROM 
                        user_institution ui
                    JOIN 
                        institutions i ON ui."institutionId" = i.id
                    WHERE 
                        ui."userId" = $1
                    LIMIT 1
                `;

                const result = await client.query(query, [user.id]);

                if (result.rows.length === 0) {
                    return reply.success({
                        data: null
                    });
                }

                // 重构数据以匹配Prisma的嵌套结构
                const row = result.rows[0];

                reply.success({
                    data: row
                });
            } catch (error) {
                fastify.log.error(error, '获取机构信息失败');
                throw new INTERNAL_ERROR(error.message || '获取机构信息失败');
            } finally {
                client.release();
            }
        }
    })
    // 更新机构信息
    fastify.put("/institutions/info", {
        schema: {
            tags: ['institutions'],
            summary: '更新机构信息',
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user
            try {
                // 机构创建人和机构管理员可以修改相关信息
                const res = await fastify.prisma.institution.update({
                    data: {
                        ...request.body
                    },
                    where: {
                        id: user.institutionId
                    }
                })

                reply.success({
                    message: "机构信息更新完成!"
                })
            } catch (error) {
                throw new INTERNAL_ERROR(error.message || '机构信息更新错误！');
            }

        }
    })

    // 获取机构地址
    fastify.get("/institutions/address", {
        schema: {
            tags: ['institutions'],
            summary: '获取机构地址',
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user
            try {
                const institutionAddress = await fastify.prisma.institutionAddress.findMany({
                    where: {
                        institutionId: user.institutionId
                    },
                    select: {
                        id: true,
                        province: true,
                        city: true,
                        district: true,
                        address: true,
                        businessHours: true,
                        phone: true,
                        personInCharge: true,
                    }
                })
                reply.success({
                    message: '获取机构地址成功.',
                    data: institutionAddress
                })
            } catch (error) {
                fastify.log.error(error, '获取机构地址失败!');
                throw new INTERNAL_ERROR(error.message || '获取机构地址失败!');
            }
        }
    })

    // 创建机构地址
    fastify.post("/institutions/address", {
        schema: {
            tags: ['institutions'],
            summary: '创建机构地址',
            body: {
                type: 'object',
                properties: {
                    province: { type: 'string' },
                    city: { type: 'string' },
                    district: { type: 'string' },
                    address: { type: 'string' },
                    businessHours: { type: 'string' },
                    phone: { type: 'string' },
                    personInCharge: { type: 'string' },
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user
            const { province, city, district, address, businessHours, phone, personInCharge } = request.body
            try {
                const institutionAddress = await fastify.prisma.institutionAddress.create({
                    data: {
                        institutionId: user.institutionId,
                        province,
                        city,
                        district,
                        address,
                        businessHours,
                        phone,
                        personInCharge,
                    }
                })
                reply.success({
                    message: '创建机构地址成功.',
                    // data: institutionAddress
                })
            } catch (error) {
                throw new INTERNAL_ERROR(error.message || '创建机构地址失败!');
            }
        }
    })

    // 更新机构地址
    fastify.put("/institutions/address/:addressId", {
        schema: {
            tags: ['institutions'],
            summary: '更新机构地址',
            params: {
                type: 'object',
                properties: {
                    addressId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                properties: {
                    province: { type: 'string' },
                    city: { type: 'string' },
                    district: { type: 'string' },
                    address: { type: 'string' },
                    businessHours: { type: 'string' },
                    phone: { type: 'string' },
                    personInCharge: { type: 'string' },
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user
            const { addressId } = request.params
            const { province, city, district, address, businessHours, phone, personInCharge } = request.body
            try {
                const institutionAddress = await fastify.prisma.institutionAddress.update({
                    where: { id: addressId },
                    data: {
                        ...(province && { province }),
                        ...(city && { city }),
                        ...(district && { district }),
                        ...(address && { address }),
                        ...(businessHours && { businessHours }),
                        ...(phone && { phone }),
                        ...(personInCharge && { personInCharge })
                    }
                })
                reply.success({
                    message: '更新机构地址成功.',
                    data: institutionAddress
                })
            } catch (error) {
                throw new INTERNAL_ERROR(error.message || '更新机构地址失败!');
            }
        }
    })


    // 删除机构地址
    fastify.delete("/institutions/address/:addressId", {
        schema: {
            tags: ['institutions'],
            summary: '删除机构地址',
            params: {
                type: 'object',
                properties: {
                    addressId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user
            const { addressId } = request.params
            const client = await fastify.pg.connect();
            try {
                await client.query(
                    'DELETE FROM institution_address WHERE id = $1 AND institution_id = $2',
                    [addressId, user.institutionId]
                )
                reply.success({
                    message: '删除机构地址成功.'
                })
            } catch (error) {
                fastify.log.error(error, '删除机构地址失败!');
                throw new INTERNAL_ERROR(error.message || '删除机构地址失败!');
            } finally {
                client.release();
            }
        }
    })

    // 获取机构教室
    fastify.get("/institutions/classrooms", {
        schema: {
            tags: ['institutions'],
            summary: '获取机构教室',
            querystring: {
                type: 'object',
                properties: {
                    page: { type: 'number', default: 1 },
                    pageSize: { type: 'number', default: 10 },
                    search: { type: 'string' },
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user
            const { page, pageSize, search } = request.query
            const client = await fastify.pg.connect();
            try {
                const searchCondition = search ? `AND name ILIKE '%${search}%'` : '';
                const offset = (page - 1) * pageSize;

                const totalResult = await client.query(
                    `SELECT COUNT(*) FROM classrooms WHERE "institutionId" = $1 ${searchCondition}`,
                    [user.institutionId]
                );
                const total = parseInt(totalResult.rows[0].count);

                const classroomsResult = await client.query(
                    `SELECT c.id, c.name, c.capacity,
                     a.province as "province", a.id as "addressId",
                     a.city as "city", a.district as "district", 
                     a.address as "address"
                     FROM classrooms c
                     LEFT JOIN institution_addresses a ON c."addressId" = a.id
                     WHERE c."institutionId" = $1 ${searchCondition}
                     ORDER BY c."createdAt" DESC
                     LIMIT $2 OFFSET $3`,
                    [user.institutionId, pageSize, offset]
                );

                // 处理结果，将扁平结构转为嵌套结构
                // const institutionClassrooms = classroomsResult.rows.map(row => {
                //     return {
                //         id: row.id,
                //         name: row.name,
                //         capacity: row.capacity,
                //         addressId: row.addressId,
                //         address: row['address.id'] ? {
                //             id: row['address.id'],
                //             province: row['address.province'],
                //             city: row['address.city'],
                //             district: row['address.district'],
                //             address: row['address.address']
                //         } : null
                //     };
                // });

                reply.success({
                    message: '获取机构教室成功.',
                    data: {
                        list: classroomsResult.rows,
                        total,
                        page,
                        pageSize
                    }
                })
            } catch (error) {
                fastify.log.error(error, '获取机构教室失败!');
                throw new INTERNAL_ERROR(error.message || '获取机构教室失败!');
            } finally {
                client.release();
            }
        }
    })
    // 创建机构教室
    fastify.post("/institutions/classrooms", {
        schema: {
            tags: ['institutions'],
            summary: '创建机构教室',
            body: {
                type: 'object',
                properties: {
                    name: { type: 'string' },
                    capacity: { type: 'number' },
                    addressId: { type: 'string' },
                }
            },
            response: {
                200: {
                    type: 'object',
                    properties: {
                        code: { type: 'number' },
                        data: { type: 'object' },
                        message: { type: 'string' }
                    }
                }
            }

        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user
            const { name, capacity, addressId } = request.body
            const client = await fastify.pg.connect();
            const id = uuidv4();
            client.query('BEGIN');
            try {
                await client.query(
                    'INSERT INTO classrooms (id, name, capacity, addressId, institutionId) VALUES ($1, $2, $3, $4, $5)',
                    [id, name, capacity, addressId, user.institutionId]
                )
                client.query('COMMIT');
                reply.success({
                    message: '创建机构教室成功.',
                })
            } catch (error) {
                client.query('ROLLBACK');
                throw new INTERNAL_ERROR(error.message || '创建机构教室失败!');
            } finally {
                client.release();
            }
        }
    })
    // 更新机构教室
    fastify.put("/institutions/classrooms/:classroomId", {
        schema: {
            tags: ['institutions'],
            summary: '更新机构教室',
            params: {
                type: 'object',
                properties: {
                    classroomId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                properties: {
                    name: { type: 'string' },
                    capacity: { type: 'number' },
                    addressId: { type: 'string' },
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user
            const { classroomId } = request.params
            const { name, capacity, addressId } = request.body
            const client = await fastify.pg.connect();
            client.query('BEGIN');
            try {
                await client.query(
                    'UPDATE classrooms SET name = $1, capacity = $2, addressId = $3 WHERE id = $4 AND institutionId = $5',
                    [name, capacity, addressId, classroomId, user.institutionId]
                )
                client.query('COMMIT');
                reply.success({
                    message: '更新机构教室成功.',
                })
            } catch (error) {
                client.query('ROLLBACK');
                throw new INTERNAL_ERROR(error.message || '更新机构教室失败!');
            } finally {
                client.release();
            }
        }
    })
    // 删除机构教室
    fastify.delete("/institutions/classrooms/:classroomId", {
        schema: {
            tags: ['institutions'],
            summary: '删除机构教室',
            params: {
                type: 'object',
                properties: {
                    classroomId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user
            const { classroomId } = request.params
            const client = await fastify.pg.connect();
            try {
                await client.query(
                    'DELETE FROM classrooms WHERE id = $1 AND institution_id = $2',
                    [classroomId, user.institutionId]
                )
                reply.success({
                    message: '删除机构教室成功.'
                })
            } catch (error) {
                fastify.log.error(error, '删除机构教室失败!');
                throw new INTERNAL_ERROR(error.message || '删除机构教室失败!');
            } finally {
                client.release();
            }
        }
    })

    // 获取机构教室select
    fastify.get("/institutions/classrooms/select", {
        schema: {
            tags: ['institutions'],
            summary: '获取机构教室select',
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user
            const client = await fastify.pg.connect();
            try {
                const { rows: institutionClassrooms } = await client.query(
                    'SELECT id, name FROM classrooms WHERE "institutionId" = $1 AND active = true',
                    [user.institutionId]
                );
                reply.success({
                    message: '获取机构教室成功.',
                    data: institutionClassrooms
                })
            } catch (error) {
                fastify.log.error(error, '获取机构教室失败!');
                throw new INTERNAL_ERROR(error.message || '获取机构教室失败!');
            } finally {
                client.release();
            }
        }
    })

    // 获取机构日志
    fastify.get("/institutions/logs", {
        schema: {
            tags: ['institutions'],
            summary: '获取机构日志',
            querystring: {
                type: 'object',
                properties: {
                    page: {
                        type: 'number',
                        default: 1,
                        description: '页码，从1开始'
                    },
                    pageSize: {
                        type: 'number',
                        default: 10,
                        description: '每页数量，默认10，最大100'
                    },
                    userId: {
                        type: 'string',
                        description: '用户ID，用于筛选特定用户的日志'
                    },
                    operationType: {
                        type: 'string',
                        description: '操作类型，用于筛选特定类型的操作'
                    },
                    describe: {
                        type: 'string',
                        description: '描述关键字，用于搜索日志描述'
                    },
                    startTime: {
                        type: 'string',
                        // format: 'date-time',
                        // description: '开始时间，ISO格式'
                    },
                    endTime: {
                        type: 'string',
                        // format: 'date-time',
                        // description: '结束时间，ISO格式'
                    }
                }
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user
            const { page = 1, pageSize = 10, userId, operationType, describe, startTime: logStartTime, endTime: logEndTime } = request.query;
            try {
                const user = request.user;

                // 获取数据库连接
                const client = await request.server.pg.connect();

                try {
                    // 查询日志
                    const pageNum = Number(page);
                    const pageSizeNum = Number(pageSize);

                    // 限制每页最大数量，防止大查询
                    const limitedPageSize = Math.min(pageSizeNum, 100);

                    // 构建查询参数
                    const queryParams = [user.institutionId];
                    const conditions = [];

                    // 使用参数映射而不是字符串拼接，提高安全性和性能
                    const paramMap = {
                        institutionId: { value: user.institutionId, condition: '"institutionId" = $1' }
                    };

                    // 动态构建查询条件
                    if (userId) {
                        paramMap.userId = {
                            value: userId,
                            condition: 'ol."userId" = $' + (Object.keys(paramMap).length + 1)
                        };
                    }

                    if (operationType) {
                        paramMap.operationType = {
                            value: operationType,
                            condition: 'ol."operationType" = $' + (Object.keys(paramMap).length + 1)
                        };
                    }

                    if (describe) {
                        paramMap.describe = {
                            value: `%${describe}%`,
                            condition: 'ol."describe" ILIKE $' + (Object.keys(paramMap).length + 1)
                        };
                    }

                    if (logStartTime) {
                        paramMap.startTime = {
                            value: new Date(logStartTime),
                            condition: 'ol."createdAt" >= $' + (Object.keys(paramMap).length + 1)
                        };
                    }

                    if (logEndTime) {
                        paramMap.endTime = {
                            value: new Date(logEndTime),
                            condition: 'ol."createdAt" <= $' + (Object.keys(paramMap).length + 1)
                        };
                    }

                    // 构建查询参数数组和条件数组
                    Object.values(paramMap).forEach(param => {
                        if (param !== paramMap.institutionId) {
                            queryParams.push(param.value);
                        }
                        conditions.push(param.condition);
                    });

                    const whereClause = conditions.join(' AND ');
                    const paramCount = queryParams.length;

                    // 使用窗口函数优化查询，避免两次查询
                    const optimizedQuery = `
                WITH log_data AS (
                    SELECT
                        ol.id,
                        ol."operationType",
                        ol."describe",
                        ol."createdAt",
                        u."name" as "userName",
                        COUNT(*) OVER() as total_count
                    FROM
                        "operation_log" ol
                    LEFT JOIN
                        "users" u ON ol."userId" = u.id
                    WHERE
                        ${whereClause}
                    ORDER BY
                        ol."createdAt" DESC
                    LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
                )
                SELECT
                    id,
                    "operationType",
                    "describe",
                    "createdAt",
                    "userName",
                    COALESCE(MAX(total_count), 0) as total
                FROM
                    log_data
                GROUP BY
                    id, "operationType", "describe", "createdAt", "userName"
            `;

                    // 添加分页参数
                    const paginationParams = [
                        ...queryParams,
                        limitedPageSize,
                        (pageNum - 1) * limitedPageSize
                    ];

                    // 设置语句超时，防止长时间运行
                    await client.query('SET statement_timeout = 10000'); // 10秒超时

                    // 执行查询
                    const result = await client.query(optimizedQuery, paginationParams);

                    // 恢复默认超时
                    await client.query('SET statement_timeout = 0');

                    // 提取总数
                    const total = result.rows.length > 0 ? parseInt(result.rows[0].total) : 0;

                    // 移除total字段，保持返回数据结构一致
                    const list = result.rows.map(({ total, ...row }) => row)

                    return reply.success({
                        data: {
                            list,
                            total,
                            page: pageNum,
                            pageSize: pageSizeNum
                        },
                        message: '获取操作日志成功'
                    });
                } finally {
                    // 释放数据库连接
                    client.release();
                }
            } catch (error) {
                // 记录详细错误信息
                request.log.error({
                    msg: '获取操作日志失败',
                    error: error.message,
                    stack: error.stack,
                    query: request.query
                });

                throw new INTERNAL_ERROR('获取操作日志失败');
            }
        }
    })
}