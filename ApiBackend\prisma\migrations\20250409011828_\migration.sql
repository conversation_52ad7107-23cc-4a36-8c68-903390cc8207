/*
  Warnings:

  - The `endDate` column on the `classes` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `classId` on the `course_classes` table. All the data in the column will be lost.
  - You are about to drop the column `endDate` on the `course_classes` table. All the data in the column will be lost.
  - You are about to drop the column `isFull` on the `course_classes` table. All the data in the column will be lost.
  - You are about to drop the column `maxStudents` on the `course_classes` table. All the data in the column will be lost.
  - You are about to drop the column `startDate` on the `course_classes` table. All the data in the column will be lost.
  - You are about to alter the column `price` on the `courses` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Decimal(10,2)`.
  - You are about to alter the column `deductionPerClass` on the `courses` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Decimal(10,2)`.
  - You are about to drop the column `type` on the `operation_log` table. All the data in the column will be lost.
  - You are about to drop the column `type` on the `permissions` table. All the data in the column will be lost.
  - You are about to drop the column `classId` on the `student_classes` table. All the data in the column will be lost.
  - You are about to drop the column `age` on the `students` table. All the data in the column will be lost.
  - You are about to drop the column `email` on the `students` table. All the data in the column will be lost.
  - You are about to drop the column `followUpPerson` on the `students` table. All the data in the column will be lost.
  - You are about to drop the column `intentionLevel` on the `students` table. All the data in the column will be lost.
  - The `birthday` column on the `students` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `followUpDate` column on the `students` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the `operations` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[courseId,classesId]` on the table `course_classes` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[id,institutionId]` on the table `roles` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[studentId,classesId]` on the table `student_classes` will be added. If there are existing duplicate values, this will fail.
  - Changed the type of `startDate` on the `classes` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Added the required column `classesId` to the `course_classes` table without a default value. This is not possible if the table is not empty.
  - Added the required column `institutionId` to the `course_classes` table without a default value. This is not possible if the table is not empty.
  - Added the required column `describe` to the `operation_log` table without a default value. This is not possible if the table is not empty.
  - Added the required column `operationType` to the `operation_log` table without a default value. This is not possible if the table is not empty.
  - Added the required column `classesId` to the `student_classes` table without a default value. This is not possible if the table is not empty.
  - Added the required column `institutionId` to the `student_classes` table without a default value. This is not possible if the table is not empty.
  - Added the required column `operatorId` to the `student_classes` table without a default value. This is not possible if the table is not empty.
  - Added the required column `operatorTime` to the `student_classes` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `joinDate` on the `student_classes` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Made the column `phone` on table `students` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `students` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "course_classes" DROP CONSTRAINT "course_classes_classId_fkey";

-- DropForeignKey
ALTER TABLE "menus" DROP CONSTRAINT "menus_permissionId_fkey";

-- DropForeignKey
ALTER TABLE "operations" DROP CONSTRAINT "operations_permissionId_fkey";

-- DropForeignKey
ALTER TABLE "student_classes" DROP CONSTRAINT "student_classes_classId_fkey";

-- DropIndex
DROP INDEX "course_classes_classId_idx";

-- DropIndex
DROP INDEX "course_classes_courseId_classId_key";

-- DropIndex
DROP INDEX "permissions_name_key";

-- DropIndex
DROP INDEX "roles_name_institutionId_idx";

-- DropIndex
DROP INDEX "student_classes_classId_idx";

-- DropIndex
DROP INDEX "student_classes_studentId_classId_key";

-- AlterTable
ALTER TABLE "classes" ADD COLUMN     "appointmentEndTime" INTEGER,
ADD COLUMN     "appointmentStartTime" INTEGER,
ADD COLUMN     "classRoomId" TEXT,
ADD COLUMN     "courseId" TEXT,
ADD COLUMN     "endType" TEXT NOT NULL DEFAULT 'number_of_times',
ADD COLUMN     "isAutoCheckIn" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "isFull" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "isOnLeave" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "isQRCodeAttendance" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "isReserve" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "isShowWeekCount" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "leaveDeadline" INTEGER,
ADD COLUMN     "maxStudentCount" INTEGER,
ADD COLUMN     "remarks" TEXT,
ADD COLUMN     "times" INTEGER,
ADD COLUMN     "type" TEXT DEFAULT 'fixed',
DROP COLUMN "startDate",
ADD COLUMN     "startDate" BIGINT NOT NULL,
DROP COLUMN "endDate",
ADD COLUMN     "endDate" BIGINT,
ALTER COLUMN "status" SET DEFAULT 'active';

-- AlterTable
ALTER TABLE "course_classes" DROP COLUMN "classId",
DROP COLUMN "endDate",
DROP COLUMN "isFull",
DROP COLUMN "maxStudents",
DROP COLUMN "startDate",
ADD COLUMN     "classesId" TEXT NOT NULL,
ADD COLUMN     "institutionId" TEXT NOT NULL,
ALTER COLUMN "status" SET DEFAULT 'active';

-- AlterTable
ALTER TABLE "courses" ADD COLUMN     "isShow" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "picture" TEXT,
ADD COLUMN     "status" TEXT NOT NULL DEFAULT 'active',
ADD COLUMN     "type" TEXT NOT NULL DEFAULT 'group',
ALTER COLUMN "price" SET DATA TYPE DECIMAL(10,2),
ALTER COLUMN "deductionPerClass" SET DATA TYPE DECIMAL(10,2);

-- AlterTable
ALTER TABLE "menus" ADD COLUMN     "permissionCode" TEXT,
ADD COLUMN     "redirect" TEXT;

-- AlterTable
ALTER TABLE "operation_log" DROP COLUMN "type",
ADD COLUMN     "describe" TEXT NOT NULL,
ADD COLUMN     "operationType" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "permissions" DROP COLUMN "type";

-- AlterTable
ALTER TABLE "student_classes" DROP COLUMN "classId",
ADD COLUMN     "classesId" TEXT NOT NULL,
ADD COLUMN     "institutionId" TEXT NOT NULL,
ADD COLUMN     "operatorId" TEXT NOT NULL,
ADD COLUMN     "operatorTime" BIGINT NOT NULL,
ADD COLUMN     "type" TEXT NOT NULL DEFAULT 'in',
DROP COLUMN "joinDate",
ADD COLUMN     "joinDate" BIGINT NOT NULL;

-- AlterTable
ALTER TABLE "students" DROP COLUMN "age",
DROP COLUMN "email",
DROP COLUMN "followUpPerson",
DROP COLUMN "intentionLevel",
ADD COLUMN     "avatar" TEXT,
ADD COLUMN     "cardNumber" TEXT,
ADD COLUMN     "followerId" TEXT,
ADD COLUMN     "intentLevel" TEXT,
ADD COLUMN     "operatorId" TEXT,
ADD COLUMN     "password" TEXT,
ADD COLUMN     "sourceDesc" TEXT,
ADD COLUMN     "type" TEXT NOT NULL DEFAULT 'intent',
DROP COLUMN "birthday",
ADD COLUMN     "birthday" BIGINT,
ALTER COLUMN "phone" SET NOT NULL,
DROP COLUMN "followUpDate",
ADD COLUMN     "followUpDate" BIGINT,
ALTER COLUMN "status" SET DEFAULT 'active',
ALTER COLUMN "updatedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "user_institution" ADD COLUMN     "status" BOOLEAN DEFAULT true;

-- AlterTable
ALTER TABLE "user_roles" ALTER COLUMN "updatedAt" DROP DEFAULT;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "active" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "phone" TEXT;

-- DropTable
DROP TABLE "operations";

-- CreateTable
CREATE TABLE "classes_time" (
    "id" TEXT NOT NULL,
    "classesId" TEXT NOT NULL,
    "weekDay" INTEGER NOT NULL,
    "startTime" TEXT NOT NULL,
    "endTime" TEXT NOT NULL,
    "institutionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "classes_time_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "classes_schedules" (
    "id" TEXT NOT NULL,
    "classesId" TEXT NOT NULL,
    "startDate" BIGINT NOT NULL,
    "weekDay" INTEGER NOT NULL,
    "startTime" TEXT NOT NULL,
    "endTime" TEXT NOT NULL,
    "currentWeeks" INTEGER NOT NULL,
    "totalWeeks" INTEGER NOT NULL,
    "courseId" TEXT,
    "institutionId" TEXT NOT NULL,
    "teacherId" TEXT,
    "subject" TEXT,
    "maxStudentCount" INTEGER,
    "isReserve" BOOLEAN NOT NULL DEFAULT false,
    "appointmentStartTime" INTEGER,
    "appointmentEndTime" INTEGER,
    "isQRCodeAttendance" BOOLEAN NOT NULL DEFAULT false,
    "isAutoCheckIn" BOOLEAN NOT NULL DEFAULT false,
    "isOnLeave" BOOLEAN NOT NULL DEFAULT false,
    "leaveDeadline" INTEGER,
    "isShowWeekCount" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "classes_schedules_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "weekly_schedules" (
    "id" TEXT NOT NULL,
    "courseClassesId" TEXT NOT NULL,
    "dateTime" BIGINT NOT NULL,
    "weekDay" INTEGER NOT NULL,
    "daysTime" TEXT NOT NULL,
    "totalWeeks" INTEGER NOT NULL,
    "currentWeeks" INTEGER NOT NULL,
    "institutionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "weekly_schedules_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "student_weekly_schedules" (
    "id" TEXT NOT NULL,
    "classesScheduleId" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'unattended',
    "operatorId" TEXT,
    "operatorTime" BIGINT,
    "institutionId" TEXT NOT NULL,
    "studentType" TEXT NOT NULL DEFAULT 'temporary',
    "attendanceCount" INTEGER,
    "attendanceAmount" DECIMAL(10,2),
    "studentProductId" TEXT,
    "productId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "student_weekly_schedules_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "products" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "price" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "icon" TEXT NOT NULL DEFAULT '',
    "cover" TEXT NOT NULL DEFAULT '',
    "leaveCount" INTEGER,
    "packageType" TEXT NOT NULL,
    "usageLimit" INTEGER,
    "timeLimitedUsage" INTEGER,
    "timeLimitType" TEXT,
    "validTimeRange" TEXT,
    "remarks" TEXT,
    "targetAudience" TEXT,
    "isShow" BOOLEAN NOT NULL DEFAULT false,
    "status" TEXT NOT NULL DEFAULT 'active',
    "institutionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "product_courses" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "courseId" TEXT NOT NULL,
    "institutionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "product_courses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "student_products" (
    "id" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "startTime" BIGINT,
    "endTime" BIGINT,
    "startDate" BIGINT,
    "endDate" BIGINT,
    "totalSessionCount" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "remainingSessionCount" DECIMAL(10,2),
    "remainingBalance" DECIMAL(10,3),
    "sessionUnitPrice" DECIMAL(10,3),
    "paymentStatus" TEXT NOT NULL DEFAULT 'done',
    "enrollmentStatus" TEXT NOT NULL DEFAULT 'active',
    "totalCount" DECIMAL(10,2) DEFAULT 0,
    "remainingCount" DECIMAL(10,2),
    "remainingDays" DECIMAL(10,2),
    "remainingAmount" DECIMAL(10,3),
    "unitPrice" DECIMAL(10,3),
    "salesRepId" TEXT,
    "amount" INTEGER,
    "amountUnpaid" DECIMAL(10,2),
    "amountPaid" DECIMAL(10,2),
    "buyCount" DECIMAL(10,2) DEFAULT 1,
    "discount" DECIMAL(10,2),
    "payment" TEXT,
    "operatorId" TEXT NOT NULL,
    "payTime" BIGINT,
    "institutionId" TEXT NOT NULL,
    "remarks" TEXT,
    "type" TEXT DEFAULT 'done',
    "status" TEXT DEFAULT 'active',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "student_products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "student_product_record" (
    "id" TEXT NOT NULL,
    "studentId" TEXT,
    "productId" TEXT,
    "studentProductId" TEXT,
    "amount" DECIMAL(10,2),
    "amountPaid" DECIMAL(10,2),
    "amountUnpaid" DECIMAL(10,2),
    "purchaseQuantity" DECIMAL(10,2) DEFAULT 1,
    "discount" DECIMAL(10,2),
    "giftCount" DECIMAL(10,2),
    "giftDays" DECIMAL(10,2),
    "paymentMethod" TEXT,
    "paymentTime" BIGINT,
    "salesRepresentativeId" TEXT,
    "operatorId" TEXT,
    "institutionId" TEXT NOT NULL,
    "remarks" TEXT,
    "status" TEXT NOT NULL DEFAULT 'done',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "student_product_record_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "student_product_refund" (
    "id" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "amount" DECIMAL(10,2),
    "reason" TEXT,
    "paymentMethod" TEXT,
    "operatorId" TEXT NOT NULL,
    "paymentTime" BIGINT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'approved',
    "remarks" TEXT,
    "reviewerId" TEXT,
    "institutionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "student_product_refund_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "student_product_adjusts" (
    "id" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "productId" TEXT,
    "studentProductId" TEXT,
    "type" TEXT NOT NULL,
    "beforeCount" DECIMAL(10,2),
    "afterCount" DECIMAL(10,2),
    "beforeDays" DECIMAL(10,2),
    "afterDays" DECIMAL(10,2),
    "beforeAmount" DECIMAL(10,2),
    "afterAmount" DECIMAL(10,2),
    "amount" DECIMAL(10,2),
    "count" DECIMAL(10,2),
    "days" DECIMAL(10,2),
    "operatorId" TEXT NOT NULL,
    "remarks" TEXT,
    "operatorTime" BIGINT NOT NULL,
    "institutionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "student_product_adjusts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "student_follow_records" (
    "id" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "followUpDate" BIGINT,
    "followUpType" TEXT,
    "followUpContent" TEXT,
    "followUpResult" TEXT,
    "followUpMethod" TEXT,
    "followUpUserId" TEXT NOT NULL,
    "followUpStauts" TEXT,
    "nextFollowUpDate" BIGINT,
    "nextFollowUpContent" TEXT,
    "operatorId" TEXT,
    "institutionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "student_follow_records_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "classrooms" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "capacity" INTEGER NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "addressId" TEXT NOT NULL,
    "institutionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "classrooms_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "institution_addresses" (
    "id" TEXT NOT NULL,
    "institutionId" TEXT NOT NULL,
    "province" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "district" TEXT,
    "street" TEXT,
    "address" TEXT,
    "businessHours" TEXT,
    "phone" TEXT,
    "personInCharge" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "institution_addresses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "geographic_areas" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT,
    "postCode" TEXT,
    "level" INTEGER NOT NULL,
    "parentId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "geographic_areas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bills" (
    "id" TEXT NOT NULL,
    "studentId" TEXT,
    "productId" TEXT,
    "amount" DECIMAL(10,2),
    "paymentMethod" TEXT,
    "paymentTime" BIGINT,
    "source" TEXT,
    "operatorId" TEXT,
    "institutionId" TEXT NOT NULL,
    "remarks" TEXT,
    "billType" TEXT,
    "status" TEXT DEFAULT 'completed',
    "operatorTime" BIGINT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "bills_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_ClassesToClassesTime" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_ClassesToClassesTime_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_StudentProductToUser" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_StudentProductToUser_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "classes_time_classesId_idx" ON "classes_time"("classesId");

-- CreateIndex
CREATE INDEX "classes_schedules_classesId_idx" ON "classes_schedules"("classesId");

-- CreateIndex
CREATE INDEX "classes_schedules_courseId_idx" ON "classes_schedules"("courseId");

-- CreateIndex
CREATE INDEX "weekly_schedules_courseClassesId_idx" ON "weekly_schedules"("courseClassesId");

-- CreateIndex
CREATE INDEX "student_weekly_schedules_classesScheduleId_idx" ON "student_weekly_schedules"("classesScheduleId");

-- CreateIndex
CREATE INDEX "student_weekly_schedules_studentId_idx" ON "student_weekly_schedules"("studentId");

-- CreateIndex
CREATE INDEX "student_weekly_schedules_productId_idx" ON "student_weekly_schedules"("productId");

-- CreateIndex
CREATE UNIQUE INDEX "student_weekly_schedules_classesScheduleId_studentId_key" ON "student_weekly_schedules"("classesScheduleId", "studentId");

-- CreateIndex
CREATE INDEX "products_institutionId_idx" ON "products"("institutionId");

-- CreateIndex
CREATE INDEX "product_courses_productId_idx" ON "product_courses"("productId");

-- CreateIndex
CREATE INDEX "product_courses_courseId_idx" ON "product_courses"("courseId");

-- CreateIndex
CREATE UNIQUE INDEX "product_courses_productId_courseId_key" ON "product_courses"("productId", "courseId");

-- CreateIndex
CREATE INDEX "student_products_institutionId_idx" ON "student_products"("institutionId");

-- CreateIndex
CREATE INDEX "student_products_studentId_idx" ON "student_products"("studentId");

-- CreateIndex
CREATE INDEX "student_products_productId_idx" ON "student_products"("productId");

-- CreateIndex
CREATE INDEX "student_products_operatorId_idx" ON "student_products"("operatorId");

-- CreateIndex
CREATE INDEX "student_product_record_salesRepresentativeId_idx" ON "student_product_record"("salesRepresentativeId");

-- CreateIndex
CREATE INDEX "student_product_record_operatorId_idx" ON "student_product_record"("operatorId");

-- CreateIndex
CREATE INDEX "student_product_record_studentId_studentProductId_instituti_idx" ON "student_product_record"("studentId", "studentProductId", "institutionId");

-- CreateIndex
CREATE INDEX "student_product_refund_studentId_idx" ON "student_product_refund"("studentId");

-- CreateIndex
CREATE INDEX "student_product_refund_operatorId_idx" ON "student_product_refund"("operatorId");

-- CreateIndex
CREATE INDEX "student_product_refund_institutionId_idx" ON "student_product_refund"("institutionId");

-- CreateIndex
CREATE INDEX "student_product_adjusts_studentProductId_idx" ON "student_product_adjusts"("studentProductId");

-- CreateIndex
CREATE INDEX "student_product_adjusts_operatorId_idx" ON "student_product_adjusts"("operatorId");

-- CreateIndex
CREATE INDEX "student_product_adjusts_institutionId_idx" ON "student_product_adjusts"("institutionId");

-- CreateIndex
CREATE INDEX "student_follow_records_studentId_idx" ON "student_follow_records"("studentId");

-- CreateIndex
CREATE INDEX "student_follow_records_institutionId_idx" ON "student_follow_records"("institutionId");

-- CreateIndex
CREATE INDEX "student_follow_records_followUpUserId_idx" ON "student_follow_records"("followUpUserId");

-- CreateIndex
CREATE INDEX "classrooms_institutionId_idx" ON "classrooms"("institutionId");

-- CreateIndex
CREATE INDEX "classrooms_addressId_idx" ON "classrooms"("addressId");

-- CreateIndex
CREATE INDEX "institution_addresses_institutionId_idx" ON "institution_addresses"("institutionId");

-- CreateIndex
CREATE INDEX "idx_code" ON "geographic_areas"("code");

-- CreateIndex
CREATE INDEX "idx_parent_id" ON "geographic_areas"("parentId");

-- CreateIndex
CREATE INDEX "bills_studentId_idx" ON "bills"("studentId");

-- CreateIndex
CREATE INDEX "bills_productId_idx" ON "bills"("productId");

-- CreateIndex
CREATE INDEX "bills_operatorId_idx" ON "bills"("operatorId");

-- CreateIndex
CREATE INDEX "bills_institutionId_idx" ON "bills"("institutionId");

-- CreateIndex
CREATE INDEX "bills_billType_idx" ON "bills"("billType");

-- CreateIndex
CREATE INDEX "_ClassesToClassesTime_B_index" ON "_ClassesToClassesTime"("B");

-- CreateIndex
CREATE INDEX "_StudentProductToUser_B_index" ON "_StudentProductToUser"("B");

-- CreateIndex
CREATE INDEX "course_classes_classesId_idx" ON "course_classes"("classesId");

-- CreateIndex
CREATE UNIQUE INDEX "course_classes_courseId_classesId_key" ON "course_classes"("courseId", "classesId");

-- CreateIndex
CREATE INDEX "permissions_code_idx" ON "permissions"("code");

-- CreateIndex
CREATE INDEX "roles_code_institutionId_idx" ON "roles"("code", "institutionId");

-- CreateIndex
CREATE UNIQUE INDEX "roles_id_institutionId_key" ON "roles"("id", "institutionId");

-- CreateIndex
CREATE INDEX "student_classes_classesId_idx" ON "student_classes"("classesId");

-- CreateIndex
CREATE UNIQUE INDEX "student_classes_studentId_classesId_key" ON "student_classes"("studentId", "classesId");

-- AddForeignKey
ALTER TABLE "classes" ADD CONSTRAINT "classes_classRoomId_fkey" FOREIGN KEY ("classRoomId") REFERENCES "classrooms"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "classes" ADD CONSTRAINT "classes_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES "courses"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "classes_schedules" ADD CONSTRAINT "classes_schedules_classesId_fkey" FOREIGN KEY ("classesId") REFERENCES "classes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "classes_schedules" ADD CONSTRAINT "classes_schedules_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES "courses"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "classes_schedules" ADD CONSTRAINT "classes_schedules_institutionId_fkey" FOREIGN KEY ("institutionId") REFERENCES "institutions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "classes_schedules" ADD CONSTRAINT "classes_schedules_teacherId_fkey" FOREIGN KEY ("teacherId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "students" ADD CONSTRAINT "students_followerId_fkey" FOREIGN KEY ("followerId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "students" ADD CONSTRAINT "students_operatorId_fkey" FOREIGN KEY ("operatorId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "weekly_schedules" ADD CONSTRAINT "weekly_schedules_courseClassesId_fkey" FOREIGN KEY ("courseClassesId") REFERENCES "course_classes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_weekly_schedules" ADD CONSTRAINT "student_weekly_schedules_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "students"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_weekly_schedules" ADD CONSTRAINT "student_weekly_schedules_classesScheduleId_fkey" FOREIGN KEY ("classesScheduleId") REFERENCES "classes_schedules"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_weekly_schedules" ADD CONSTRAINT "student_weekly_schedules_operatorId_fkey" FOREIGN KEY ("operatorId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_weekly_schedules" ADD CONSTRAINT "student_weekly_schedules_studentProductId_fkey" FOREIGN KEY ("studentProductId") REFERENCES "student_products"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_weekly_schedules" ADD CONSTRAINT "student_weekly_schedules_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_classes" ADD CONSTRAINT "student_classes_classesId_fkey" FOREIGN KEY ("classesId") REFERENCES "classes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_classes" ADD CONSTRAINT "student_classes_operatorId_fkey" FOREIGN KEY ("operatorId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "products" ADD CONSTRAINT "products_institutionId_fkey" FOREIGN KEY ("institutionId") REFERENCES "institutions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_courses" ADD CONSTRAINT "product_courses_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_courses" ADD CONSTRAINT "product_courses_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES "courses"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_products" ADD CONSTRAINT "student_products_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "students"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_products" ADD CONSTRAINT "student_products_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_products" ADD CONSTRAINT "student_products_institutionId_fkey" FOREIGN KEY ("institutionId") REFERENCES "institutions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_products" ADD CONSTRAINT "student_products_salesRepId_fkey" FOREIGN KEY ("salesRepId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_products" ADD CONSTRAINT "student_products_operatorId_fkey" FOREIGN KEY ("operatorId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_record" ADD CONSTRAINT "student_product_record_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "students"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_record" ADD CONSTRAINT "student_product_record_studentProductId_fkey" FOREIGN KEY ("studentProductId") REFERENCES "student_products"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_record" ADD CONSTRAINT "student_product_record_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_record" ADD CONSTRAINT "student_product_record_salesRepresentativeId_fkey" FOREIGN KEY ("salesRepresentativeId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_record" ADD CONSTRAINT "student_product_record_operatorId_fkey" FOREIGN KEY ("operatorId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_refund" ADD CONSTRAINT "student_product_refund_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_refund" ADD CONSTRAINT "student_product_refund_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "students"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_refund" ADD CONSTRAINT "student_product_refund_operatorId_fkey" FOREIGN KEY ("operatorId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_refund" ADD CONSTRAINT "student_product_refund_institutionId_fkey" FOREIGN KEY ("institutionId") REFERENCES "institutions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_adjusts" ADD CONSTRAINT "student_product_adjusts_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_adjusts" ADD CONSTRAINT "student_product_adjusts_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "students"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_adjusts" ADD CONSTRAINT "student_product_adjusts_studentProductId_fkey" FOREIGN KEY ("studentProductId") REFERENCES "student_products"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_adjusts" ADD CONSTRAINT "student_product_adjusts_operatorId_fkey" FOREIGN KEY ("operatorId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_product_adjusts" ADD CONSTRAINT "student_product_adjusts_institutionId_fkey" FOREIGN KEY ("institutionId") REFERENCES "institutions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_follow_records" ADD CONSTRAINT "student_follow_records_operatorId_fkey" FOREIGN KEY ("operatorId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_follow_records" ADD CONSTRAINT "student_follow_records_institutionId_fkey" FOREIGN KEY ("institutionId") REFERENCES "institutions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_follow_records" ADD CONSTRAINT "student_follow_records_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "students"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_follow_records" ADD CONSTRAINT "student_follow_records_followUpUserId_fkey" FOREIGN KEY ("followUpUserId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "classrooms" ADD CONSTRAINT "classrooms_institutionId_fkey" FOREIGN KEY ("institutionId") REFERENCES "institutions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "classrooms" ADD CONSTRAINT "classrooms_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "institution_addresses"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "institution_addresses" ADD CONSTRAINT "institution_addresses_institutionId_fkey" FOREIGN KEY ("institutionId") REFERENCES "institutions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bills" ADD CONSTRAINT "bills_operatorId_fkey" FOREIGN KEY ("operatorId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bills" ADD CONSTRAINT "bills_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "students"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ClassesToClassesTime" ADD CONSTRAINT "_ClassesToClassesTime_A_fkey" FOREIGN KEY ("A") REFERENCES "classes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ClassesToClassesTime" ADD CONSTRAINT "_ClassesToClassesTime_B_fkey" FOREIGN KEY ("B") REFERENCES "classes_time"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_StudentProductToUser" ADD CONSTRAINT "_StudentProductToUser_A_fkey" FOREIGN KEY ("A") REFERENCES "student_products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_StudentProductToUser" ADD CONSTRAINT "_StudentProductToUser_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
