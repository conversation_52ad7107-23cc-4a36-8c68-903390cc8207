/**
 * Image generation worker for BullMQ
 * Handles image generation tasks using the aitools service
 */

import { Worker } from "bullmq";
import { redisTask } from "../config/redis.js";
import { generateImage, generateImageVariation } from "../services/aitools.js";
import {
  defaultWorkerOptions,
  markTaskCompleted,
  markTaskFailed,
  setupProgressReporting
} from "./workerConfig.js";
import imageQueue from "../queues/imageQueue.js";

// Worker configuration with specific settings for image generation
const workerOptions = {
  ...defaultWorkerOptions,
  concurrency: 8, // Higher concurrency for image generation
};

// Create the worker
const worker = new Worker(
  'image-generation',
  async (job) => {
    const { taskId, type = 'image-generation' } = job.data;
    console.log(`Starting image generation job ${job.id}, taskId: ${taskId}, type: ${type}`);

    const startTime = Date.now();

    // Setup progress reporting
    const { progressInterval, updateProgress } = setupProgressReporting(taskId);

    try {
      let result;

      // Process different types of image generation tasks
      if (type === 'image-generation') {
        const { prompt, model, size, quality, style } = job.data;

        // Update initial progress
        await updateProgress(10);

        // Generate the image
        result = await generateImage(prompt, model, size, quality, style);
      } else if (type === 'image-variation') {
        const { imageUrl, model, size, quality } = job.data;

        // Update initial progress
        await updateProgress(10);

        // Generate image variation
        result = await generateImageVariation(imageUrl, model, size, quality);
      } else {
        throw new Error(`Unsupported image task type: ${type}`);
      }

      // Clear progress reporting interval
      clearInterval(progressInterval);

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Mark task as completed
      await markTaskCompleted(taskId, result, type, processingTime);

      console.log(`Image generation job ${job.id} completed, processing time: ${processingTime}ms`);
      return result;
    } catch (error) {
      console.error(`Image generation job ${job.id} failed:`, error);

      // Clear progress reporting interval
      clearInterval(progressInterval);

      // Mark task as failed
      await markTaskFailed(taskId, error, type, Date.now() - startTime);

      // Rethrow the error for BullMQ to handle retries
      throw error;
    }
  },
  workerOptions
);

// Add error handling for the worker
worker.on('error', (error) => {
  console.error('Image generation worker error:', error);
});

worker.on('failed', (job, error) => {
  console.error(`Image generation worker job ${job?.id} failed:`, error);
});

worker.on('completed', (job) => {
  console.log(`Image generation worker completed job ${job.id}`);
});

export default imageQueue;
