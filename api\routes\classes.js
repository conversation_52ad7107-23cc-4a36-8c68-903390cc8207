import { createError } from '@fastify/error';
import { TwitterSnowflake } from "@sapphire/snowflake"
import { getDaysInRange, getTuesdaysInWeeks, getWeekDayDatesInRange } from "../utils/getWeekdayDate.js"
import getWeekStartAndEnd from '../utils/getWeekStartAndEnd.js';
import { sendToSpecifiedUser } from '../libs/websocket.js';
import fastJson from "fast-json-stringify"


const AUTH_ERROR = createError('AUTH_ERROR', '%s', 401);
const INTERNAL_ERROR = createError('INTERNAL_ERROR', '%s', 500);


// 确保时间字符串格式化为两位数小时
function formatTimeString(timeStr) {
    const [hours, minutes] = timeStr.split(':');
    return `${hours.padStart(2, '0')}:${minutes}`;
}

export default async function (fastify, opts) {

    // 快捷创建临时计划
    fastify.post('/classes/temp-schedule', {
        schema: {
            tags: ['classes'],
            summary: '快捷创建临时计划',
            body: {
                type: 'object',
                properties: {
                    name: { type: 'string' },
                    teacherId: { type: 'string' },
                    courseId: { type: 'string' },
                    classRoomId: { type: 'string' },
                    maxStudentCount: { type: 'number' },
                    weekdays: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                day: {
                                    type: 'number',
                                    default: 1
                                },
                                startTime: {
                                    type: 'string'
                                },
                                endTime: {
                                    type: 'string'
                                }
                            }
                        }
                    },
                    courseId: {
                        type: 'string', // 课程id
                    },
                    // 预约选项
                    reservation: {
                        type: 'object',
                        properties: {
                            enabled: {
                                type: 'boolean',
                                default: false,
                            },
                            appointmentStartTime: {
                                type: 'number',
                                default: 0,
                            },
                            appointmentEndTime: {
                                type: 'number',
                                default: 0,
                            }
                        }
                    },
                    // 考勤选项
                    attendance: {
                        type: 'object',
                        properties: {
                            studentScan: {
                                type: 'boolean',
                                default: false,
                            },
                            autoSystem: {
                                type: 'boolean',
                                default: false,
                            },
                        }
                    },
                    // 请假选项
                    leave: {
                        type: 'object',
                        properties: {
                            enabled: {
                                type: 'boolean',
                                default: false,
                            },
                            leaveDeadline: {
                                type: 'number',
                                default: 0,
                            },
                        }
                    },
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user;
            const { name, teacherId, courseId, classRoomId, maxStudentCount, weekdays, reservation, attendance, leave } = request.body;
           
            try {
                const id = TwitterSnowflake.generate().toString();

                const startDate = new Date();
            
                const { startOfWeek, endOfWeek } = getWeekStartAndEnd(startDate);
                // getWeekDayDatesInRange
                const dates = [];
                weekdays.forEach((weekday) => {
                    const a = getWeekDayDatesInRange(startOfWeek, endOfWeek, weekday.day, 'yyyy-MM-dd');
                    let b = {
                        startDate: a[0],
                        weekDay: weekday.day,
                        startTime: weekday.startTime,
                        endTime: weekday.endTime,
                    }
                    dates.push(b);
                })
                console.log(dates,"dates")
                
                const data = dates.map((item) => {
                    return {
                        name, 
                        teacherId,
                        courseId,
                        classRoomId,
                        maxStudentCount,
                        startDate: new Date(item.startDate).getTime(),
                        weekDay: item.weekDay,
                        startTime: item.startTime,
                        endTime: item.endTime,
                        currentWeeks:1,
                        totalWeeks:1,
                        institutionId: user.institutionId
                    }
                })
                const result = await fastify.prisma.ClassesSchedule.createMany({
                    data: data
                })
                console.log(result,"result")
                reply.success({
                    message: '快捷创建临时计划成功'
                })
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '快捷创建临时计划失败');
            }
        }
    })

    //获取计划表
    fastify.get('/classes/all/schedules', {
        schema: {
            tags: ['classes'],
            summary: '获取计划表',
            querystring: {
                type: 'object',
                properties: {
                    startDate: { type: 'number' },
                    endDate: { type: 'number' },
                    teacher: { type: 'string', default: '' }
                }
            },
            response: {
                200: {
                    type: 'object',
                    properties: {
                        code: { type: 'number' },
                        data: { 
                            type: 'array', 
                            items: { 
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    name: { type: 'string' },
                                    startDate: { type: 'number' },
                                    weekDay: { type: 'number' },
                                    startTime: { type: 'string' },
                                    endTime: { type: 'string' },
                                    maxStudentCount: { type: 'number' },
                                    subject: { type: 'string' },
                                    // teacherId: { type: 'string' },
                                    teacherName: { type: 'string' },
                                    // classesId: { type: 'string' },
                                    classesType: { type: 'string' },
                                    className: { type: 'string' },
                                    // courseId: { type: 'string' },
                                    courseName: { type: 'string' },
                                }
                            },   
                        },
                        message: { type: 'string' }
                    }
                }
            }
            
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user;
            const { startDate, endDate, teacher } = request.query;
            let sDate = new Date();
            let eDate = new Date();

            if (startDate) {
                sDate = new Date(startDate);
            }
            if (endDate) {
                eDate = new Date(endDate);
            }
            if (!startDate && !endDate) {
                const { startOfWeek, endOfWeek } = getWeekStartAndEnd(new Date());
                sDate = new Date(startOfWeek);
                eDate = new Date(endOfWeek);
            }
            sDate.setHours(0, 0, 0, 0);
            eDate.setHours(23, 59, 59, 999);

            const cacheKey = `schedules:${user.institutionId}:${sDate.getTime()}-${eDate.getTime()}:${teacher}`;
            let cached = await fastify.redis.get(cacheKey);

            if (cached) {
                return reply.success({ data: JSON.parse(cached), message: '获取计划表成功 (缓存)' });
            }
            
            let teacherCondition = '';
            let queryParams = [user.institutionId, sDate.getTime(), eDate.getTime()];
            if (teacher) {
              teacherCondition = `AND cs."teacherId" = $4`;
              queryParams.push(teacher);
            }

             // 缓存击穿保护
            const lockKey = `${cacheKey}:lock`;
            let lock = await fastify.redis.set(lockKey, 1, 'NX', 'EX', 5);
            if (!lock) {
                // 有其他请求在查库, 等待100ms后重查缓存
                await new Promise(resolve => setTimeout(resolve, 100));
                cached = await fastify.redis.get(cacheKey);
                if (cached) {
                    return reply.success({ data: JSON.parse(cached), message: '获取计划表成功.' });
                }
            }

            const query = `
                SELECT 
                    cs.id,
                    cs.name,
                    cs."startDate",
                    cs."weekDay",
                    cs."startTime",
                    cs."endTime",
                    cs."maxStudentCount",
                    cs.subject,
                    t.name as "teacherName",
                    c."type" as "classesType",
                    c.name as "className",
                    co.name as "courseName"
                FROM 
                    classes_schedules cs
                LEFT JOIN 
                    users t ON cs."teacherId" = t.id
                LEFT JOIN 
                    classes c ON cs."classesId" = c.id
                LEFT JOIN 
                    courses co ON cs."courseId" = co.id
                WHERE 
                    cs."institutionId" = $1
                    AND cs."startDate" >= $2
                    AND cs."startDate" <= $3
                    ${teacherCondition}
            `;

            
            const { rows } = await fastify.pg.query(query, queryParams);
            // 缓存24小时 + 随机1-3分钟
            const random = Math.floor(Math.random() * 180) + 120;
            await fastify.redis.set(cacheKey, JSON.stringify(rows), 'EX', (60 * 60 * 24) + random);
            await fastify.redis.del(lockKey); // 释放锁


            reply.success({
                data: rows,
                message: '获取计划表成功'
            })
        }
    })


    // 获取班级id和name
    fastify.get('/classes/select', {
        schema: {
            tags: ['classes'],
            summary: '获取班级id和name',
            querystring: {
                type: 'object',
                properties: {
                    page: { type: 'number', default: 1 },
                    pageSize: { type: 'number', default: 10 },
                    name: { type: 'string' },
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;
            const { page = 1, pageSize = 10, name } = request.query;
            const skip = (page - 1) * pageSize;
            const take = pageSize;
            const [result, total] = await Promise.all([
                fastify.prisma.classes.findMany({
                    where: {
                        institutionId: user.institutionId,
                        ...(name ? { name: { contains: name, mode: 'insensitive' } } : {})
                    },
                    select: {
                        id: true,
                        name: true,
                        startDate: true,
                        teacher: {
                            select: {
                                id: true,
                                name: true
                            }
                        }
                    },
                    skip,
                    take
                }),
                fastify.prisma.classes.count({
                    where: { institutionId: user.institutionId, ...(name ? { name: { contains: name, mode: 'insensitive' } } : {}) }
                })
            ])

            const transformedResult = result.map(item => ({
                ...item,
                startDate: item.startDate ? Number(item.startDate) : null
            }))
            reply.success({
                data: {
                    list: transformedResult,
                    total: total,
                    page: page,
                    pageSize: pageSize
                },
                message: '获取班级id和name成功'
            })
        }
    })
    // 获取班级
    fastify.get('/classes', {
        schema: {
            tags: ['classes'],
            summary: '获取班级列表',
            querystring: {
                type: 'object',
                properties: {
                    page: { type: 'number', default: 1 },
                    pageSize: { type: 'number', default: 10 },
                    teacherId: { type: 'string' },
                    name: { type: 'string' },
                }
            },

        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const { page = 1, pageSize = 10, name, teacherId } = request.query;
            const user = request.user;
            try {
                const skip = (page - 1) * pageSize;
                const take = pageSize;
                const { startOfWeek, endOfWeek } = getWeekStartAndEnd(new Date());
              
                const where = {
                    institutionId: user.institutionId,
                    ...(name ? {
                        name: {
                            contains: name,
                            mode: 'insensitive'
                        }
                    } : {}),
                    ...(teacherId ? {
                        teacherId
                    } : {}),
                }
              
                // ========= 新获取班级计划 =========

                const [total, classes] = await Promise.all([
                    fastify.prisma.Classes.count({
                        where,
                        // include: { ClassesSchedule: true }
                    }),
                    fastify.prisma.Classes.findMany({
                        where,
                        select: {
                            id: true,
                            name: true,
                            startDate: true,
                            endDate: true,
                            status: true,
                            teacher: {
                            select: {
                                id: true,
                                name: true
                            }
                        },
                        course: {
                            select: {
                                id: true,
                                name: true,
                            }
                        },
                        ClassesSchedule: {
                            where: {
                                startDate: {
                                    gte: BigInt(startOfWeek.getTime()),
                                    lte: BigInt(endOfWeek.getTime())
                                }
                                // 或者：currentWeeks: currentWeek
                            },
                            select: {
                                id: true,
                                currentWeeks: true,
                                totalWeeks: true,
                                // startDate: true,

                            }
                        }

                        },
                        skip,
                        take,
                        orderBy: {
                            createdAt: 'desc'
                        }
                    })
                ])
    
                // ========= 新获取班级计划 =========
                const transformedResult = classes.map(item => {
                    const currentWeeks = item.ClassesSchedule.length > 0 ? item.ClassesSchedule[0].currentWeeks : null
                    const totalWeeks = item.ClassesSchedule.length > 0 ? item.ClassesSchedule[0].totalWeeks : null
                    delete item.ClassesSchedule
                    return {
                        ...item,
                        startDate: item.startDate ? Number(item.startDate) : null,
                        endDate: item.endDate ? Number(item.endDate) : null,
                        currentWeeks: currentWeeks,
                        totalWeeks: totalWeeks,
                    }
                })
               
                reply.success({
                    data: {
                        page,
                        pageSize,
                        total,
                        list: transformedResult
                    },
                    message: '获取班级列表成功'
                });
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '获取班级列表失败');
            } finally {
                // client.release();
            }
        }
    })
    // 创建班级
    fastify.post('/classes', {
        schema: {
            tags: ['classes'],
            summary: '创建班级',
            body: {
                type: 'object',
                required: ['name'],
                properties: {
                    name: { type: 'string' },
                    teacherId: { type: 'string' },
                    classRoomId: { type: 'string' },
                    maxStudentCount: {
                        type: 'number',
                        default: 20,
                    },
                    times: {
                        type: 'number',

                    },
                    recurrenceType: {
                        type: 'string', //循环方式,weekly 每周, daily 每天
                        default: 'weekly'
                    },
                    daily: {
                        type: 'object',
                        properties: {
                            startTime: {
                                type: 'string'
                            },
                            endTime: {
                                type: 'string'
                            }
                        }
                    },
                    startDate: {
                        type: 'number',
                    },
                    endDate: {
                        type: 'number',
                    },
                    endType: {
                        type: 'string', // number_of_times为按次，times为按时
                        default: 'number_of_times'
                    },
                    weekdays: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                day: {
                                    type: 'number',
                                    default: 1
                                },
                                startTime: {
                                    type: 'string'
                                },
                                endTime: {
                                    type: 'string'
                                }
                            }
                        }
                    },
                    courseId: {
                        type: 'string', // 课程id
                    },
                    // 预约选项
                    reservation: {
                        type: 'object',
                        properties: {
                            enabled: {
                                type: 'boolean',
                                default: false,
                            },
                            appointmentStartTime: {
                                type: 'number',
                                default: 0,
                            },
                            appointmentEndTime: {
                                type: 'number',
                                default: 0,
                            }
                        }
                    },
                    // 考勤选项
                    attendance: {
                        type: 'object',
                        properties: {
                            studentScan: {
                                type: 'boolean',
                                default: false,
                            },
                            autoSystem: {
                                type: 'boolean',
                                default: false,
                            },
                        }
                    },
                    // 请假选项
                    leave: {
                        type: 'object',
                        properties: {
                            enabled: {
                                type: 'boolean',
                                default: false,
                            },
                            leaveDeadline: {
                                type: 'number',
                                default: 0,
                            },
                        }
                    },
                    type: {
                        type: 'string',
                        default: 'fixed' // 班级类型：temporary 临时班级，fixed 固定班级
                    }
                },
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;
            const {
                name, maxStudentCount = 20, teacherId, recurrenceType,
                endType, startDate, endDate, courseId, times,
                weekdays, type, daily, reservation, attendance, leave, classRoomId
            } = request.body;
            const client = await fastify.pg.connect();
            try {


                // 创建班级 
                const classesId = TwitterSnowflake.generate().toString();
                const courseClass = await fastify.prisma.classes.create({
                    data: {
                        id: classesId,
                        name,
                        teacherId,
                        courseId,
                        maxStudentCount,
                        startDate,
                        endType,
                        times,
                        endDate,
                        type,
                        ...(classRoomId && { classRoomId }),
                        ...(reservation.enabled ? {
                            isReserve: true,
                            appointmentStartTime: reservation.appointmentStartTime,
                            appointmentEndTime: reservation.appointmentEndTime
                        } : {}),
                        ...(attendance.enabled ? {
                            isQRCodeAttendance: attendance.studentScan,
                            isAutoCheckIn: attendance.autoSystem
                        } : {}),
                        ...(leave.enabled ? {
                            isOnLeave: true,
                            leaveDeadline: leave.leaveDeadline
                        } : {}),
                        institutionId: user.institutionId
                    }
                })

                // 创建班级时间详细表
                const classesTimeId = [] // 班级时间id
                weekdays.map(async (item) => {
                    let id = TwitterSnowflake.generate().toString();
                    classesTimeId.push(id)
                    await fastify.prisma.classesTime.create({
                        data: {
                            id,
                            classesId,
                            weekDay: item.day,
                            startTime: item.startTime,
                            endTime: item.endTime,
                            institutionId: user.institutionId
                        }
                    })
                })


                // 创建班级计划公共data
                const classesScheduleData = {
                    classesId,
                    courseId,
                    teacherId,
                    maxStudentCount,
                    ...(reservation.enabled ? {
                        isReserve: true,
                        appointmentStartTime: reservation.appointmentStartTime,
                        appointmentEndTime: reservation.appointmentEndTime
                    } : {}),
                    ...(attendance.enabled ? {
                        isQRCodeAttendance: attendance.studentScan,
                        isAutoCheckIn: attendance.autoSystem
                    } : {}),
                    ...(leave.enabled ? {
                        isOnLeave: true,
                        leaveDeadline: leave.leaveDeadline
                    } : {}),
                    institutionId: user.institutionId
                }
            
                // 班级计划
                const classesSchedule = []

                // 创建日期对应
                if (recurrenceType === 'weekly') {
                    if (!weekdays || !Array.isArray(weekdays) || weekdays.length === 0) {
                        throw new Error('请选择周几')
                    }
                    // 根据每周重复为计次或结束时间
                    const dayData = weekdays.map((item) => {
                        let dayData = endType === 'number_of_times' ?
                            getTuesdaysInWeeks(startDate, times, item.day) :
                            getTuesdaysInWeeks(startDate, times, item.day)
                        return {
                            day: item.day,
                            list: dayData,
                            startTime: item.startTime,
                            endTime: item.endTime
                        }
                    })

                    const newDayData = []
                    for (const item of dayData) {
                        for (const item2 of item.list) {
                            newDayData.push({
                                day: item.day,
                                startTime: item.startTime,
                                endTime: item.endTime,
                                startDate: item2
                            })
                        }
                    }
                    // 排序newDayData
                    newDayData.sort((a, b) => {
                        const dateA = new Date(a.startDate);
                        const dateB = new Date(b.startDate);
                        return dateA - dateB;  // 升序排列
                    });

                    // 创建班级计划
                    let index = 0;
                    let i = 1;
                    const totalWeeks = newDayData.length;
                    await fastify.prisma.classes.update({
                        where: {
                            id: classesId
                        },
                        data: {
                            times: totalWeeks,
                            endDate: new Date(newDayData[newDayData.length - 1].startDate).getTime()
                        }
                    })

                    for (const item of newDayData) {
                        let classsesScheduleId = TwitterSnowflake.generate().toString();
                        const currentWeeks = i;
                        classesSchedule.push({
                            id: classsesScheduleId,
                            startDate: new Date(item.startDate).getTime(),
                            weekDay: item.day,
                            startTime: item.startTime,
                            endTime: item.endTime,
                            currentWeeks,
                            totalWeeks,
                            ...classesScheduleData
                        });

                        i++; // 确保每次异步执行完后递增

                         index++; // 处理下一周数据
                    }


                } else if (recurrenceType === 'daily') {
                    const dates = endType === 'times' ? getDaysInRange(startDate, endDate) : getDaysInRange(startDate, endDate, times - 1)
                    const totalWeeks = dates.length;
                    await fastify.prisma.classes.update({
                        where: {
                            id: classesId
                        },
                        data: {
                            times: totalWeeks,
                            endDate: new Date(dates[dates.length - 1]).getTime()
                        }
                    })
                    dates.map((date, index) => {
                        let classsesScheduleId = TwitterSnowflake.generate().toString();
                        classesSchedule.push({
                            id: classsesScheduleId,
                            
                            startDate: new Date(date).getTime(),
                            weekDay: new Date(date).getDay(),
                            startTime: daily.startTime || '00:00',
                            endTime: daily.endTime || '23:59',
                            currentWeeks: index + 1,
                            totalWeeks,
                            ...classesScheduleData
                        });
                    })
                    await fastify.prisma.classes.update({
                        where: {
                            id: classesId
                        },
                        data: {
                            times: totalWeeks
                        }
                    })
                }

                // 创建班级计划
                await fastify.prisma.classesSchedule.createMany({
                    data: classesSchedule
                })


                reply.success({
                    message: '创建班级成功！'
                })
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '创建班级失败');
            } finally {
                client.release();
            }
        }
    })

    // 获取课程老师空闲时间
    fastify.post('/classes/check-teacher-free', {
        schema: {
            tags: ['classes'],
            summary: '获取课程老师时候空闲',
            body: {
                type: 'object',
                properties: {
                    times: {
                        type: 'number'
                    },
                    recurrenceType: {
                        type: 'string', //循环方式,weekly 每周, daily 每天
                        default: 'weekly'
                    },
                    daily: {
                        type: 'object',
                        properties: {
                            startTime: {
                                type: 'string'
                            },
                            endTime: {
                                type: 'string'
                            }
                        }
                    },
                    startDate: {
                        type: 'number',
                    },
                    endDate: {
                        type: 'number',
                    },
                    endType: {
                        type: 'string', // number_of_times为按次，times为按时
                        default: 'number_of_times'
                    },
                    weekdays: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                day: {
                                    type: 'number',
                                    default: 1
                                },
                                startTime: {
                                    type: 'string'
                                },
                                endTime: {
                                    type: 'string'
                                }
                            }
                        }
                    },
                }
            }

        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;
            const { times, recurrenceType, daily, startDate, endDate, endType, weekdays } = request.body;
            const classesTime = [] // 班级时间id
            // 获取时间

            weekdays.map((item) => {
                classesTime.push({
                    day: item.day,
                    startTime: item.startTime,
                    endTime: item.endTime
                })
            })
            // 获取全部老师
            const teachersResult = await fastify.prisma.userInstitution.findMany({
                where: {
                    institutionId: user.institutionId,
                },
                select: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                        }
                    }
                }
            })
            const teachers = teachersResult.map((item) => item.user)
            const teacherIds = teachers.map((item) => item.id)


            // 老师空闲时间
            const teacherFreeTime = []

            if (recurrenceType === 'weekly') {
                // 根据每周重复为计次或结束时间
                const dayData = weekdays.map((item) => {
                    let dayData = endType === 'number_of_times' ?
                        getTuesdaysInWeeks(startDate, times, item.day) :
                        getTuesdaysInWeeks(startDate, times, item.day)
                    return dayData
                })
                const newDayData = []
                for (const item of dayData) {
                    newDayData.push(...item)
                }
                // 排序newDayData
                newDayData.sort((a, b) => a - b)


                // 获取老师空闲时间
                for (const dataItem of newDayData) {
                    for (const wkItem of weekdays) {
                        const startTime = formatTimeString(wkItem.startTime);
                        const endTime = formatTimeString(wkItem.endTime);
                        const teacherFreeTimeItem = await fastify.prisma.classesSchedule.findMany({
                            where: {
                                teacherId: { in: teacherIds },
                                startDate: new Date(dataItem).getTime(),
                                weekDay: wkItem.day,
                                AND: [
                                    { startTime: { lte: endTime } },  // 数据库开始时间 <= 传入结束时间
                                    { endTime: { gte: startTime } }   // 数据库结束时间 >= 传入开始时间
                                ]
                            },
                            select: {
                                teacher: {
                                    select: {
                                        id: true,
                                        name: true,
                                    }
                                }
                            }
                        })
                        teacherFreeTime.push(...teacherFreeTimeItem)
                    }
                    console.log(teacherFreeTime, "teacherFreeTime")
                }

            }else if (recurrenceType === 'daily') {
                const dates = endType === 'times' ? getDaysInRange(startDate, endDate) : getDaysInRange(startDate, endDate, times - 1)
                for (const date of dates) {
                    const startTime = formatTimeString(daily.startTime);
                    const endTime = formatTimeString(daily.endTime);
                    const teacherFreeTimeItem = await fastify.prisma.classesSchedule.findMany({
                        where: {
                            teacherId: { in: teacherIds },
                            startDate: new Date(date).getTime(),
                            AND: [
                                { startTime: { lte: endTime } },  // 数据库开始时间 <= 传入结束时间
                                { endTime: { gte: startTime } }   // 数据库结束时间 >= 传入开始时间
                            ]
                        },
                        select: {
                            teacher: {
                                select: {
                                    id: true,
                                    name: true,
                                }
                            }
                        }
                    })
                    teacherFreeTime.push(...teacherFreeTimeItem)
                }
            }
            // 将teacherFreeTime去重
            const uniqueTeacherFreeTime = teacherFreeTime.filter((item, index, self) =>
                index === self.findIndex((t) => t.teacher.id === item.teacher.id)
            )
            teachers.map((item) => {
                item.freeTime = (uniqueTeacherFreeTime.filter((t) => t.teacher.id === item.id).length > 0) ? false : true
            })


            reply.success({
                data: teachers,
                message: '获取老师空闲时间成功.'
            })

        }
    })

    // 班级添加课程
    fastify.post('/classes/:classesId/courses', {
        schema: {
            tags: ['classes'],
            summary: '班级添加课程',
            params: {
                type: 'object',
                required: ['classesId'],
                properties: {
                    classesId: { type: 'string' },
                },
            },
            body: {
                type: 'object',
                required: ['courseId'],
                properties: {
                    courseId: { type: 'string' },
                },
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;
            const { classesId } = request.params;
            const { courseId } = request.body;
            const client = await fastify.pg.connect();
            try {
                const id = TwitterSnowflake.generate().toString();
                const result = await client.query(
                    `INSERT INTO course_classes ("id" ,"classesId", "courseId","institutionId") VALUES ($1, $2, $3, $4) RETURNING *`,
                    [id, classesId, courseId, user.institutionId]
                );

                reply.success({
                    data: result.rows[0],
                    message: '班级添加课程成功'
                });
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '班级添加课程失败');
            } finally {
                client.release();
            }
        }
    })
    // 班级更新课程
    fastify.put('/classes/:classesId/courses', {
        schema: {
            tags: ['classes'],
            body: {
                type: 'object',
                required: ['courseId'],
                properties: {
                    courseId: { type: 'string' },
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user;
            const { classesId } = request.params;
            const { courseId } = request.body;
            const client = fastify.prisma;
            try {
                const currentTime = new Date().getTime();
                const course_classesRes = await client.courseClasses.findFirst({
                    where: {
                        classesId,
                        institutionId: user.institutionId
                    }
                })
                await Promise.all([
                    client.classesSchedule.updateMany({
                        where: {
                            classesId,
                            institutionId: user.institutionId
                        },
                        data: {
                            courseId
                        }
                    }),
                    client.courseClasses.update({
                        where: {
                            id: course_classesRes.id,
                        },
                        data: {
                            courseId
                        }
                    }),
                    client.classesSchedule.updateMany({
                        where: {
                            startDate: {
                                gt: currentTime
                            },
                            classesId,
                            institutionId: user.institutionId
                        },
                        data: {
                            courseId
                        }
                    })
                ])


                reply.success({
                    message: '班级课程更新成功.'
                })
            } catch (error) {
                // fastify.log.
                console.error(error)
                throw new INTERNAL_ERROR('班级课程更新错误！')
            }
        }
    })

    // 班级学员签到(批量) 未完成
    fastify.post('/classes/:classId/checkin/multi', {
        schema: {
            tags: ['classes'],
            summary: '班级学员签到',
            params: {
                type: 'object',
                required: ['classId'],
                properties: {
                    classId: { type: 'string' },
                },
            },
            body: {
                type: 'object',
                required: ['studentIds'],
                properties: {
                    studentIds: {
                        type: 'array',
                        items: {
                            type: 'string'
                        }
                    },
                },
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;
            const { classId } = request.params;
            const { studentIds } = request.body;
            // const client = await fastify.pg.connect();
            try {
                if (!Array.isArray(studentIds)) {
                    throw new VALIDATION_ERROR('学生ID必须是一个数组');
                }
                if (studentIds.some(id => typeof id !== 'string' || id.trim() === '')) {
                    throw new VALIDATION_ERROR('学生ID必须是字符串');
                }
                const currentUserInstitution = await fastify.prisma.userInstitution.findFirst({ where: { userId: user.id } });
                if (!currentUserInstitution) {
                    throw new AUTH_ERROR('用户不存在');
                }
                const classes = await fastify.prisma.classes.findFirst({
                    where: {
                        id: classId,
                        institutionId: user.institutionId
                    }
                })

                if (!classes) {
                    throw new AUTH_ERROR('用户无权操作该班级');
                }
                const courseClasses = await fastify.prisma.courseClass.findFirst({
                    where: {
                        classId: classId
                    },
                    include: {
                        course: true
                    }
                })
                if (!courseClasses) {
                    throw new AUTH_ERROR('该班级没有课程');
                }
                // console.log(fastify.prisma.studentProduct,"fastify.prisma.studentProduct")
                const studentProductRes = await fastify.prisma.studentProduct.findMany({
                    where: {
                        studentId: {
                            in: studentIds
                        }
                    },
                    include: {
                        product: true,
                        student: {
                            name: true
                        }
                    }
                })
                if (studentProductRes.length !== studentIds.length) {
                    throw new AUTH_ERROR('学生不存在');
                }

                const studentClassesRes = await fastify.prisma.studentClass.findMany({
                    where: {
                        classId: classId,
                        studentId: {
                            in: studentIds
                        }
                    }
                });
                if (studentClassesRes.length !== studentIds.length) {
                    throw new AUTH_ERROR('学生不存在');
                }
                if (courseClasses.status !== 'ACTIVE') {
                    throw new AUTH_ERROR('班级课程可能已经毕业或被禁用');
                }
                if (!courseClasses.course.status) {
                    throw new AUTH_ERROR('课程已被禁用');
                }
                const normal = []
                const late = []
                const deductionPerClass = courseClasses.course.deductionPerClass
                studentClassesRes.forEach(item => {
                    if (item.remainingCount - deductionPerClass >= 0) {
                        normal.push({
                            ...item,
                            remainingCount: item.remainingCount - deductionPerClass
                        })
                    } else {
                        late.push({
                            ...item
                        })
                    }
                })
                normal.forEach(async item => {
                    await fastify.prisma.studentProduct.update({
                        where: {
                            id: item.id
                        },
                        data: {
                            remainingCount: item.remainingCount
                        }
                    })
                })
                const not = late.map(item => item.student.name)
                reply.success({
                    // data: result.rows[0],
                    data: not,
                    message: `班级学员签到成功`
                });
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '班级学员签到失败');
            }
        }
    })


    // 班级添加固定学员(批量添加)
    fastify.post('/classes/:classesId/students', {
        schema: {
            tags: ['classes'],
            summary: '班级添加学员',
            params: {
                type: 'object',
                required: ['classesId'],
                properties: {
                    classesId: { type: 'string' },
                },
            },
            body: {
                type: 'object',
                required: ['studentIds'],
                properties: {
                    studentIds: {
                        type: 'array',
                        items: {
                            type: 'string'
                        }
                    },
                },
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;
            const { classesId } = request.params;
            const { studentIds } = request.body;
            const client = await fastify.pg.connect();
            try {
                if (!Array.isArray(studentIds)) {
                    throw new VALIDATION_ERROR('学生ID必须是一个数组');
                }
                if (studentIds.some(id => typeof id !== 'string' || id.trim() === '')) {
                    throw new VALIDATION_ERROR('学生ID不能为空或无效');
                }


                // 添加学员到学生班级关联表
                studentIds.map(async (id) => {
                    let currentTime = new Date().getTime()
                    await fastify.prisma.studentClasses.create({
                        data: {
                            classesId,
                            studentId: id,
                            institutionId: user.institutionId,
                            operatorId: user.id,
                            joinDate: currentTime,
                            operatorTime: currentTime,
                        }
                    })
                })

                // 查询班级课程表
                let currentDate = new Date().getTime()
                const classesScheduleResult = await fastify.prisma.classesSchedule.findMany({
                    where: {
                        classesId,
                        startDate: {
                            gt: currentDate
                        },
                        institutionId: user.institutionId
                    }
                })
                // 根据班级课程表创建学生的每周课表
                classesScheduleResult.map((item) => {
                    studentIds.map(async (id) => {
                        await fastify.prisma.studentWeeklySchedule.create({
                            data: {
                                classesScheduleId: item.id,
                                studentId: id,
                                studentType: 'fixed',
                                institutionId: user.institutionId
                            }
                        })
                    })

                })


                reply.success({
                    message: '班级添加学员成功'
                });
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '班级添加学员失败');
            } finally {
                client.release();
            }
        }
    })
    // 班级删除学员
    fastify.delete('/classes/:classesId/students/:studentId', {
        schema: {
            tags: ['classes'],
            summary: '班级删除学员',
            params: {
                type: 'object',
                required: ['classesId', 'studentId'],
                properties: {
                    classesId: { type: 'string' },
                    studentId: { type: 'string' },
                },
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;
            const { classesId, studentId } = request.params;
            const client = await fastify.pg.connect();
            try {
                const result = await client.query(
                    `DELETE FROM student_classes WHERE "classesId" = $1 AND "studentId" = $2 RETURNING *`,
                    [classesId, studentId]
                );

                const currentDate = new Date().getTime()

                const studentWeeklyScheduleResult = await fastify.prisma.studentWeeklySchedule.findMany({
                    where: {
                        studentId,
                        institutionId: user.institutionId,
                        classesSchedule: {
                            startDate: {
                                gt: currentDate
                            }
                        }

                    }
                })
                studentWeeklyScheduleResult.map(async (item) => {
                    await fastify.prisma.studentWeeklySchedule.delete({
                        where: {
                            id: item.id
                        }
                    })
                })

                if (result.rows.length === 0) {
                    throw new AUTH_ERROR('班级不存在');
                }
                reply.success({
                    data: result.rows[0],
                    message: '班级删除学员成功'
                });
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '班级删除学员失败');
            } finally {
                client.release();
            }
        }
    })
    // 删除班级
    fastify.delete('/classes/:classId', {
        schema: {
            tags: ['classes'],
            summary: '删除班级',
            params: {
                type: 'object',
                required: ['classId'],
                properties: {
                    classId: { type: 'string' },
                },
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;
            const { classId } = request.params;
            const client = await fastify.pg.connect();
            try {
                const result = await client.query(
                    `DELETE FROM classes WHERE "id" = $1 AND "institutionId" = $2 RETURNING *`,
                    [classId, user.institutionId]
                );
                if (result.rows.length === 0) {
                    throw new AUTH_ERROR('班级不存在');
                }
                reply.success({
                    data: result.rows[0],
                    message: '删除班级成功'
                });
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '删除班级失败');
            } finally {
                client.release();
            }
        }
    })
    // 更新班级
    fastify.put('/classes/:classesId', {
        schema: {
            tags: ['classes'],
            summary: '更新班级',
            params: {
                type: 'object',
                required: ['classesId'],
                properties: {
                    classesId: { type: 'string' },
                },
            },
            body: {
                type: 'object',
                properties: {
                    name: { type: 'string' },
                    teacherId: { type: 'string' },
                    courseId: { type: 'string', default: null },
                    isReserve: { type: 'boolean' },
                    appointmentStartTime: { type: 'string' },
                    appointmentEndTime: { type: 'string' },
                    isQRCodeAttendance: { type: 'boolean' },
                    isAutoCheckIn: { type: 'boolean' },
                    isOnLeave: { type: 'boolean' },
                    leaveDeadline: { type: 'string' },
                    maxStudentCount: { type: 'string' },
                    isShowWeekCount: {
                        type: 'boolean'
                    },
                    classRoomId: { type: 'string' },
                    status: {
                        type: 'string',
                        default: 'active',
                        enum: ['active', 'graduated', 'disbanded']
                    },
                },
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;
            const { classesId } = request.params;
            const {
                name, teacherId, status, courseId, isReserve,
                appointmentStartTime, appointmentEndTime, isQRCodeAttendance,
                isAutoCheckIn, isOnLeave, leaveDeadline, isShowWeekCount,
                classRoomId, maxStudentCount
            } = request.body;
            const client = await fastify.pg.connect();
            try {
                const updateData = {
                    ...(maxStudentCount && { maxStudentCount: Number.parseFloat(maxStudentCount) }),
                    ...(classRoomId && { classRoomId }),
                    ...(isReserve ? {
                        isReserve: true,
                        appointmentStartTime: Number.parseFloat(appointmentStartTime),
                        appointmentEndTime: Number.parseFloat(appointmentEndTime)
                    } : {}),
                    ...(isQRCodeAttendance ? {
                        isQRCodeAttendance: true,
                    } : {}),
                    ...(isAutoCheckIn ? {
                        isAutoCheckIn: true,
                    } : {}),
                    ...(isOnLeave ? {
                        isOnLeave: true,
                        leaveDeadline: Number.parseFloat(leaveDeadline)
                    } : {}),
                    ...(isShowWeekCount ? {
                        isShowWeekCount: true,
                    } : {}),
                }

                await fastify.prisma.classes.update({
                    where: {
                        id: classesId,
                        institutionId: user.institutionId
                    },
                    data: updateData
                })

                const result = await client.query(
                    `UPDATE classes SET name = $1, "teacherId" = $2, "status" = $3 WHERE "id" = $4 AND "institutionId" = $5 RETURNING *`,
                    [name, teacherId, status, classesId, user.institutionId]
                );

                if (result.rows.length === 0) {
                    throw new AUTH_ERROR('班级不存在');
                }
                const courseClassesResult = await fastify.prisma.courseClasses.findFirst({
                    where: {
                        classesId: classesId,
                        institutionId: user.institutionId
                    }
                })
                if (courseId && courseClassesResult.courseId !== courseId) {
                    const courseResult = await fastify.prisma.course.findFirst({
                        where: {
                            id: courseId,
                            institutionId: user.institutionId
                        }
                    })
                    if (!courseResult) {
                        throw new AUTH_ERROR('课程不存在');
                    }
                    await fastify.prisma.courseClasses.update({
                        where: {
                            id: courseClassesResult.id,
                            institutionId: user.institutionId
                        },
                        data: {
                            courseId: courseId
                        }
                    })
                }

                reply.success({
                    data: result.rows[0],
                    message: '更新班级成功'
                });
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '更新班级失败');
            } finally {
                client.release();
            }
        }
    })

    // 获取班级详细
    fastify.get('/classes/:classesId', {
        schema: {
            tags: ['classes'],
            summary: '获取班级详细',
            params: {
                type: 'object',
                required: ['classesId'],
                properties: {
                    classesId: { type: 'string' },
                },
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const { classesId } = request.params;
            const user = request.user;
            const client = fastify.prisma;
            try {

                const result = await client.classes.findFirst({
                    where: {
                        id: classesId,
                        institutionId: user.institutionId
                    },
                    select: {
                        id: true,
                        name: true,
                        endDate: true,
                        startDate: true,
                        remarks: true,
                        times: true,
                        status: true,
                        type: true,
                        isReserve: true,
                        maxStudentCount: true,
                        appointmentStartTime: true,
                        appointmentEndTime: true,
                        isQRCodeAttendance: true,
                        isAutoCheckIn: true,
                        isOnLeave: true,
                        isShowWeekCount: true,
                        leaveDeadline: true,
                        timeDetails: {
                            select: {
                                id: true,
                                weekDay: true,
                                startTime: true,
                                endTime: true
                            }
                        },
                        students: {
                            select: {
                                id: true,
                                student: {
                                    select: {
                                        id: true,
                                        name: true,
                                        phone: true,
                                        type: true
                                    }
                                },
                                operatorTime: true,
                                operator: {
                                    select: {
                                        name: true
                                    }
                                },
                                type: true,
                                joinDate: true,
                            }
                        },
                        course: {
                            select: {
                                id: true,
                                name: true,
                            }
                        },
                        // course: {
                        //     select: {
                        //         id: true,
                        //         status: true,
                        //         courseId: true,
                        //         course: {
                        //             select: {
                        //                 id: true,
                        //                 name: true
                        //             }
                        //         }
                        //     }
                        // },
                        teacher: {
                            select: {
                                id: true,
                                name: true
                            }
                        }
                    }
                })

                const classesScheduleResult = await client.classesSchedule.findMany({
                    where: {
                        classesId
                    },
                    select: {
                        id: true,
                        startDate: true,
                        weekDay: true,
                        startTime: true,
                        endTime: true,
                        currentWeeks: true,
                        totalWeeks: true,
                        subject:true,
                        courses: {
                            select: {
                                id: true,
                                name: true
                            }
                        },
                        teacher: {
                            select: {
                                id: true,
                                name: true
                            }
                        },
                        StudentWeeklySchedule: {
                            select: {
                                status: true,
                                student: {
                                    select: {
                                        id: true,
                                        name: true
                                    }
                                }
                            }
                        },
                    },
                    orderBy: {
                        startDate: 'asc'
                    }
                })

                const transformedclassesScheduleResult = classesScheduleResult.map(item => ({
                    ...item,
                    startDate: item.startDate ? Number(item.startDate) : null,
                }));
                const transformedResult = {
                    ...result,
                    startDate: result.startDate ? Number(result.startDate) : null,
                    endDate: result.endDate ? Number(result.endDate) : null,
                    times: result.times ? Number(result.times) : null,
                    students: result.students.map(item => ({
                        ...item,
                        joinDate: item.joinDate ? Number(item.joinDate) : null,
                        operatorTime: item.operatorTime ? Number(item.operatorTime) : null
                    })),
                    classesSchedule: transformedclassesScheduleResult,
                };

                reply.success({
                    data: transformedResult,
                    message: '获取班级成功'
                });
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '获取班级失败');
            }
        }
    })
    // 获取班级课程章节列表
    fastify.get('/classes/:classesId/schedules', {
        schema: {
            tags: ['classes'],
            summary: '获取班级课程章节列表',
            params: {
                type: 'object',
                required: ['classesId'],
                properties: {
                    classesId: { type: 'string' },
                },
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const { classesId } = request.params;
            const user = request.user;
            const client = fastify.prisma;
            try {

                const result = await client.classesSchedule.findMany({
                    where: {
                        classesId,
                        institutionId: user.institutionId
                    },
                    select: {
                        id: true,
                        startDate: true,
                        startTime: true,
                        endTime: true,
                        currentWeeks: true,
                        courses: {
                            select: {
                                id: true,
                                name: true
                            }
                        },
                        teacher: {
                            select: {
                                id: true,
                                name: true
                            }
                        }
                    }
                })
                const transformedResult = result.map(item => ({
                    ...item,
                    startDate: item.startDate ? Number(item.startDate) : null,
                }));
                // 获取章节学员
                const resultData = []
                for (const item of transformedResult) {
                    console.log('item.id', item.id)
                    const studentResult = await client.studentWeeklySchedule.findMany({
                        where: {
                            classesScheduleId: item.id
                        },
                        select: {
                            id: true,
                            status: true,
                            studentType: true,
                            operator: {
                                select: {
                                    name: true
                                }
                            },
                            operatorTime: true,
                            student: {
                                select: {
                                    name: true,
                                    phone: true
                                }
                            }
                        }
                    })
                    const studentTransformedResult = studentResult.map(item => ({
                        ...item,
                        operatorTime: item.operatorTime ? Number(item.operatorTime) : null,
                    }));
                    item['studentList'] = studentTransformedResult
                    console.log(item)
                    resultData.push(item)
                }

                reply.success({
                    data: resultData,
                    message: '获取班级课程章节成功'
                });
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '获取班级课程章节失败');
            }
        }
    })

    // 获取班级章节详细 获取章节信息(开班时间、上课老师、课程、上课进度)
    fastify.get('/classes/schedules/:scheduleId', {
        schema: {
            tags: ['classes'],
            summary: '获取班级章节详细',
            params: {
                type: 'object',
                required: ['scheduleId'],
                properties: {
                    scheduleId: { type: 'string' },
                },
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const { scheduleId } = request.params;
            const user = request.user;
            const client = fastify.prisma;
            try {
                const result = await client.classesSchedule.findFirst({
                    where: {
                        id: scheduleId,
                        institutionId: user.institutionId
                    },
                    select: {
                        id: true,
                        name: true,
                        startDate: true,
                        weekDay: true,
                        startTime: true,
                        endTime: true,
                        currentWeeks: true,
                        totalWeeks: true,
                        maxStudentCount: true,
                        isReserve: true,
                        appointmentStartTime: true,
                        appointmentEndTime: true,
                        isQRCodeAttendance: true,
                        isAutoCheckIn: true,
                        isOnLeave: true,
                        leaveDeadline: true,
                        isShowWeekCount: true,
                        subject: true,
                        StudentWeeklySchedule: {
                            select: {
                                id: true,
                                student: {
                                    select: {
                                        id: true,
                                        name: true,
                                        phone: true,
                                        gender: true,
                                    }
                                },
                                status: true,
                                studentType: true,
                                operator: {
                                    select: {
                                        name: true
                                    }
                                },
                                operatorTime: true,
                            }
                        },
                        courses: {
                            select: {
                                id: true,
                                name: true,
                                type: true,
                                deductionPerClass: true,

                            }
                        },
                        teacher: {
                            select: {

                                id: true,
                                name: true
                            }
                        },
                        classes: {
                            select: {
                                id: true,
                                name: true,

                            }
                        }
                    }
                })
                const transformedResult = {
                    ...result,
                    startDate: result.startDate ? Number(result.startDate) : null,
                    StudentWeeklySchedule: result.StudentWeeklySchedule.map(item => ({
                        ...item,
                        operatorTime: item.operatorTime ? Number(item.operatorTime) : null,
                    }))
                }

                reply.success({
                    data: transformedResult,
                    message: '获取班级章节详细成功'
                })
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '获取班级章节详细失败');
            }
        }
    })

    // 更新班级课节内容
    fastify.put('/classes/schedules/:scheduleId', {
        schema: {
            tags: ['classes'],
            summary: '更新班级课节内容',
            body: {
                type: 'object',
                properties: {
                    name: { type: 'string' },
                    date: { type: 'number' },
                    startTime: { type: 'string' },
                    endTime: { type: 'string' },
                    subject: { type: 'string' },
                    isReserve: { type: 'boolean' },
                    appointmentStartTime: { type: 'string' },
                    appointmentEndTime: { type: 'string' },
                    isQRCodeAttendance: { type: 'boolean' },
                    isAutoCheckIn: { type: 'boolean' },
                    isOnLeave: { type: 'boolean' },
                    maxStudentCount: { type: 'string' },
                    leaveDeadline: { type: 'string' },
                    isShowWeekCount: {
                        type: 'boolean'
                    },
                    course: { type: 'string' },
                    teacher: { type: 'string' },
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const { scheduleId } = request.params;
            const {
                name, startTime, endTime, subject, course, teacher,
                date, isReserve, appointmentStartTime, appointmentEndTime,
                isQRCodeAttendance, isAutoCheckIn, isOnLeave, leaveDeadline,
                isShowWeekCount, classRoomId, maxStudentCount } = request.body;
            const user = request.user;
            const client = fastify.prisma;
            try {
                const result = await client.classesSchedule.update({
                    where: {
                        id: scheduleId,
                        institutionId: user.institutionId
                    },
                    data: {
                        ...(maxStudentCount && { maxStudentCount: Number.parseFloat(maxStudentCount) }),
                        ...(name && { name: name }),
                        ...(startTime && { startTime: startTime }),
                        ...(endTime && { endTime: endTime }),
                        ...(subject && { subject: subject }),
                        ...(course && { courseId: course }),
                        ...(teacher && { teacherId: teacher }),
                        ...(date && { startDate: date }),
                        ...(isReserve && {
                            isReserve: true,
                            appointmentStartTime: Number.parseFloat(appointmentStartTime),
                            appointmentEndTime: Number.parseFloat(appointmentEndTime)
                        }),
                        ...(isQRCodeAttendance && { isQRCodeAttendance: true }),
                        ...(isAutoCheckIn && { isAutoCheckIn: true }),
                        ...(isOnLeave && {
                            isOnLeave: true,
                            leaveDeadline: Number.parseFloat(leaveDeadline),
                            isShowWeekCount: isShowWeekCount
                        }),
                        ...(classRoomId && { classRoomId: classRoomId }),
                    }
                })
                reply.success({
                    message: '更新班级课节内容成功'
                })
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '更新班级课节内容失败');
            }
        }
    })

    // 班级章节添加学生
    fastify.post('/classes/schedules/:scheduleId/students', {
        schema: {
            tags: ['classes'],
            summary: '班级章节添加学生',
            params: {
                type: 'object',
                required: ['scheduleId'],
                properties: {
                    scheduleId: { type: 'string' }
                },
            },
            body: {
                type: 'object',
                required: ['students'],
                properties: {
                    students: {
                        type: 'object',
                        properties: {
                            studentId: { type: 'string' },
                            type: {
                                type: 'string',
                                default: 'temporary',
                                enum: ['fixed', 'temporary', 'trial']
                            }
                        }
                    },
                },
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const { scheduleId } = request.params;
            const { studentId, students } = request.body;
            const user = request.user;
            const client = fastify.prisma;
            try {

                const classesSchedule = await client.classesSchedule.findFirst({
                    where: {
                        id: scheduleId,
                        institutionId: user.institutionId
                    },
                    select: {
                        teacherId: true,
                    }
                })
                

                const result = await client.studentWeeklySchedule.create({
                    data: {
                        classesScheduleId: scheduleId,
                        studentId: students.studentId,
                        studentType: students.type,
                        operatorId: user.id,
                        operatorTime: new Date().getTime(),
                        institutionId: user.institutionId
                    }
                })
                if (students.type == 'trial') {
                    // 等待添加通知教师
                    if(classesSchedule.teacherId){
                        // 通过websocket发送通知教师
                        const payload  = {
                            type: 'TRIAL_STUDENT_ADDED',
                            title: '试听学员添加',
                            student: {
                                name: '小白'
                            }
                        }
                        sendToSpecifiedUser(fastify, classesSchedule.teacherId, user.institutionId, payload)
                    }
                }

                reply.success({
                    // data: result,
                    message: '添加学生成功'

                })
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '添加学生失败');
            }
        }
    })
    // 班级章节删除学生 待添加日志记录
    fastify.delete('/classes/schedules/:scheduleId/students', {
        schema: {
            tags: ['classes'],
            summary: '班级章节删除学生',
            params: {
                type: 'object',
                required: ['scheduleId'],
                properties: {
                    scheduleId: { type: 'string' },
                },
            },
            body: {
                type: 'object',
                required: ['studentIds'],
                properties: {
                    studentIds: {
                        type: 'array',
                        items: { type: 'string' },
                        default: []
                    },
                },
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user;
            const { scheduleId } = request.params;
            const { studentIds } = request.body;
            const client = fastify.prisma;
            try {
                const result = await client.studentWeeklySchedule.findMany({
                    where: {
                        classesScheduleId: scheduleId,
                        studentId: { in: studentIds },
                        institutionId: user.institutionId
                    }
                })
                if (result.length !== studentIds.length) {
                    throw new INTERNAL_ERROR('学员不存在');
                }
                await client.studentWeeklySchedule.deleteMany({
                    where: {
                        id: { in: result.map(item => item.id) },
                        institutionId: user.institutionId
                    }
                })
                reply.success({
                    message: '章节删除学员成功.'
                })
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR('班级章节移除学员错误！' || error.message)
            }
        }

    })

    // 班级计划学员考勤
    fastify.put('/classes/schedules/:scheduleId/studentAttendance', {
        schema: {
            tags: ['classes'],
            summary: '班级计划学员考勤',
            params: {
                type: 'object',
                required: ['scheduleId'],
                properties: {
                    scheduleId: { type: 'string' },
                },
            },
            body: {
                type: 'object',
                required: ['studentId', 'status'],
                properties: {
                    studentId: { type: 'string' },
                    status: {
                        type: 'string',
                        default: 'unattended',
                        enum: ['unattended', 'attendance', 'leave', 'absent']
                    },
                },
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const { scheduleId } = request.params;
            const { studentId, status } = request.body;
            const user = request.user;
            const client = fastify.prisma;
            try {
                const result = await client.studentWeeklySchedule.findFirst({
                    where: {
                        classesScheduleId: scheduleId,
                        studentId: studentId,
                        institutionId: user.institutionId,
                    },
                    select: {
                        id: true,
                        studentProductId: true,
                        attendanceCount: true,
                        studentType: true,
                        status: true,
                        attendanceAmount: true,
                        classesSchedule: {
                            select: {
                                courses: {
                                    select: {
                                        // 每节课考勤扣除课时
                                        deductionPerClass: true,
                                        // 是否在考勤时扣除课时
                                        isDeductOnAttendance: true,
                                        // 是否在请假时扣除课时
                                        isDeductOnLeave: true,
                                        // 是否在缺勤时扣除课时
                                        isDeductOnAbsence: true,
                                        ProductCourse: {
                                            select: {
                                                id: true,
                                                productId: true,
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        student: {
                            select: {
                                StudentProduct: {
                                    where: {
                                        enrollmentStatus: 'active',
                                    },
                                    select: {
                                        id: true,
                                        productId: true,
                                        remainingSessionCount: true,
                                        remainingBalance: true,
                                        sessionUnitPrice: true,
                                        paymentStatus: true,
                                        startDate: true,
                                        endDate: true,
                                        product: {
                                            select: {
                                                id: true,
                                                timeLimitedUsage: true, // 限时消费时长
                                                timeLimitType: true, // 限时消费类型"daily" 或 "monthly"
                                                validTimeRange: true, // 有效时间范围开始方式 
                                            }
                                        }
                                    },
                                    orderBy: {
                                        createdAt: 'desc',
                                    }
                                }
                            }
                        }
                    }
                })
                if (!result) {
                    throw new INTERNAL_ERROR('学员不存在');
                }
                if (result.studentType == 'trial') {
                    return reply.status(400).send({
                        code: 400,
                        message: '试听学员不能考勤!'
                    })
                }

                // 如果学员状态等于考勤状态，则退还课时           
                if (result.status == status) {
                    const productResult = await client.studentProduct.findFirst({
                        where: { id: result.studentProductId },
                        select: {
                            id: true,
                            remainingSessionCount: true,
                            remainingBalance: true,
                            sessionUnitPrice: true,
                        }
                    })
                    const newRemainingCount = Number.parseFloat(productResult.remainingSessionCount) + Number.parseFloat(result.attendanceCount)
                    const newRemainingBalance = Number.parseFloat(productResult.remainingBalance) + Number.parseFloat(result.attendanceAmount)
                    const updateStudentProduct = await client.studentProduct.update({
                        where: { id: productResult.id },
                        data: {
                            remainingSessionCount: Number.parseFloat(newRemainingCount).toFixed(2),
                            remainingBalance: Number.parseFloat(newRemainingBalance).toFixed(2),
                            status: newRemainingCount > 0 ? 'active' : 'completed'
                        }
                    })

                }
                const { student, classesSchedule } = result;
                const { deductionPerClass, isDeductOnAttendance, isDeductOnLeave, isDeductOnAbsence
                    , ProductCourse
                } = classesSchedule.courses;
                let product = null;
                let pricePerClass = 0; // 单节课价格
                // 考勤扣课 开始;
                if (result.status !== status) {

                    // 找到剩余课时大于等于扣课时数的课时
                    let productIndex = 0;

                    let isFound = false;
                    let productCourse = null;
                    if(student.StudentProduct.length < 1){
                        return reply.status(400).send({
                            code: 400,
                            message: '学员没有产品!'
                        })
                    }
                    // 如果学员有多个产品，则需要遍历所有产品
                    while (productIndex < student.StudentProduct.length) {
                        product = student.StudentProduct[productIndex];
                        productCourse = ProductCourse.filter(item => item.productId == product.productId);
                        if (Number.parseFloat(product.remainingSessionCount) >= Number.parseFloat(deductionPerClass) && productCourse.length > 0) {
                            isFound = true;
                            break;
                        }
                        productIndex++;
                    }
                    if (productCourse.length == 0) {
                        return reply.status(400).send({
                            code: 400,
                            message: '没有课程对应的套餐!'
                        })
                    }

                    if (!isFound) {
                        return reply.status(400).send({
                            code: 400,
                            message: '当前产品课时不足!'
                        })
                    }

                    // 套餐为限时限次消费，且有效时间范围为消费日算起consumption-date
                    if (product.product.validTimeRange === "consumption-date" && !product.startDate) {
                        // 计算开始日期和结束日期
                        const startDate = new Date();
                        const endDate = new Date();
                        const timeLimitedUsage = Number.parseFloat(product.product.timeLimitedUsage);
                        product.product.timeLimitType == "daily" ? endDate.setDate(endDate.getDate() + timeLimitedUsage) : endDate.setMonth(endDate.getMonth() + timeLimitedUsage);
                        await client.studentProduct.update({
                            where: { id: product.id },
                            data: {
                                startDate: startDate.getTime(),
                                endDate: endDate.getTime()
                            }
                        })

                    }






                    // 新的剩余课时
                    let newRemainingCount = product.remainingSessionCount;

                    // 计算这节课的价格
                    pricePerClass = Number.parseFloat(product.sessionUnitPrice) * Number.parseFloat(deductionPerClass);
                    let remainingBalance = product.remainingBalance;

                    if (status == 'attendance' && isDeductOnAttendance && result.status != 'attendance') {
                        newRemainingCount = product.remainingSessionCount - deductionPerClass;
                        remainingBalance = product.remainingBalance - pricePerClass;
                    }
                    if (status == 'leave' && isDeductOnLeave && result.status != 'leave') {
                        newRemainingCount = product.remainingSessionCount - deductionPerClass;
                        remainingBalance = product.remainingBalance - pricePerClass;
                    }
                    if (status == 'absent' && isDeductOnAbsence && result.status != 'absent') {
                        newRemainingCount = product.remainingSessionCount - deductionPerClass;
                        remainingBalance = product.remainingBalance - pricePerClass;
                    }
                    // 更新学员产品
                    const newUnitPrice = Number.parseFloat(remainingBalance) / Number.parseFloat(newRemainingCount);
                    const updateStudentProduct = await client.studentProduct.update({
                        where: { id: product.id },
                        data: {
                            remainingSessionCount: Number.parseFloat(newRemainingCount).toFixed(3),
                            remainingBalance: Number.parseFloat(remainingBalance).toFixed(3),
                            sessionUnitPrice: Number.parseFloat(newUnitPrice).toFixed(3),
                            status: newRemainingCount > 0 ? 'active' : 'completed'
                        }
                    })
                }


                // 考勤扣课 结束
                const updateResult = await client.studentWeeklySchedule.update({
                    where: { id: result.id },
                    data: {
                        status: result.status === status ? "unattended" : status,
                        attendanceCount: result.status === status ? 0 : Number.parseInt(deductionPerClass),
                        studentProductId: result.status === status ? null : product.id,
                        operatorId: result.status === status ? null : user.id,
                        operatorTime: result.status === status ? null : new Date().getTime(),
                        attendanceAmount: result.status === status ? 0 : Number.parseFloat(pricePerClass).toFixed(3),
                        institutionId: user.institutionId,
                        productId: result.status === status ? null : product.productId,
                    }
                })



                reply.success({
                    message: '班级计划学员考勤成功'
                })
            } catch (error) {
                throw new INTERNAL_ERROR(error.message || '班级计划学员考勤失败');
            }
        }
    })
    // 班级获取学生
    fastify.get('/classes/:classesId/students', {
        schema: {
            tags: ['classes'],
            summary: '班级获取学生',
            params: {
                type: 'object',
                required: ['classesId'],
                properties: {
                    classesId: { type: 'string' },
                },
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const { classesId } = request.params;
            const user = request.user;
            const client = fastify.prisma;
            try {
                const result = await client.studentClasses.findMany({
                    where: {
                        classesId,
                        institutionId: user.institutionId
                    },
                    select: {
                        joinDate: true,
                        type: true,
                        student: {
                            select: {
                                phone: true,
                                name: true,
                                type: true
                            }
                        },
                        operatorTime: true,
                        operator: {
                            select: {
                                name: true
                            }
                        }
                    }
                })
                const transformedResult = result.map(item => ({
                    ...item,
                    startDate: item.startDate ? Number(item.startDate) : null,
                    operatorTime: item.operatorTime ? Number(item.operatorTime) : null,
                    joinDate: item.joinDate ? Number(item.joinDate) : null,
                }));
                reply.success({
                    data: transformedResult,
                    message: '班级获取学生成功'

                })
            } catch (error) {
                throw new INTERNAL_ERROR(error.message || '班级获取学生失败');
            }
        }
    })

    // 班级章节获取学生出勤记录
    fastify.get('/classes/:classesId/schedules/:scheduleId/studentAttendance', {
        schema: {
            tags: ['classes'],
            summary: '班级获取学生出勤记录',
            params: {
                type: 'object',
                properties: {
                    classesId: {
                        type: 'string'
                    },
                    scheduleId: {
                        type: 'string'
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user;
            const { classesId, scheduleId } = request.params;
            const client = fastify.prisma;
            try {
                const result = await client.studentWeeklySchedule.findMany({
                    where: {
                        classesScheduleId: scheduleId,
                        institutionId: user.institutionId
                    },
                    select: {
                        id: true,
                        status: true,
                        operatorTime: true,
                        studentType: true,
                        student: {
                            select: {
                                id: true,
                                name: true,
                                phone: true,
                            }
                        },
                        operator: {
                            select: {
                                name: true
                            }
                        }
                    }
                })
                const transformedResult = result.map((item) => {
                    return {
                        ...item,
                        operatorTime: item.operatorTime ? Number(item.operatorTime) : null,
                    }
                })

                reply.success({
                    data: transformedResult,
                    message: '获取章节考勤记录成功.'
                })
            } catch (error) {
                throw new INTERNAL_ERROR(error.message || '获取课节考勤记录错误！')
            }
        }
    })

    // 测试获取班级考勤记录
    fastify.get('/classes/:classesId/attendance', {
        schema: {
            tags: ['classes'],
            summary: '获取班级考勤记录',
            params: {
                type: 'object',
                properties: {
                    classesId: {
                        type: 'string'
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user;
            const { classesId } = request.params;
            const client = fastify.prisma;
            try {
                const result = await client.classesSchedule.findMany({
                    where: {
                        classesId,
                        institutionId: user.institutionId
                    },
                    select: {
                        id: true,
                        startDate: true,
                        weekDay: true,
                        startTime: true,
                        endTime: true,
                        currentWeeks: true,
                        StudentWeeklySchedule: {
                            select: {
                                status: true,
                                student: {
                                    select: {
                                        id: true,
                                        name: true
                                    }
                                }
                            }
                        },
                    }
                })
                const transformedResult = result.map((item) => {
                    return {
                        ...item,
                        startDate: item.startDate ? Number(item.startDate) : null,
                    }
                })

                reply.success({
                    data: transformedResult,
                    message: '获取班级考勤记录成功.'
                })
            } catch (error) {
                throw new INTERNAL_ERROR('获取班级考勤记录错误！' || error.message)

            }
        }
    })
}