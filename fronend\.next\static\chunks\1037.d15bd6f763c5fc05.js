"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1037],{54165:(e,l,t)=>{t.d(l,{Cf:()=>x,Es:()=>m,HM:()=>g,L3:()=>h,c7:()=>u,lG:()=>b,rr:()=>p,zM:()=>n});var a=t(95155),r=t(12115),o=t(15452),s=t(54416),d=t(59434);let b=o.bL,n=o.l9,c=o.ZL,g=o.bm,i=r.forwardRef((e,l)=>{let{className:t,...r}=e;return(0,a.jsx)(o.hJ,{ref:l,className:(0,d.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r})});i.displayName=o.hJ.displayName;let x=r.forwardRef((e,l)=>{let{className:t,children:r,...b}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(i,{}),(0,a.jsxs)(o.UC,{ref:l,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...b,children:[r,(0,a.jsxs)(o.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(s.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});x.displayName=o.UC.displayName;let u=e=>{let{className:l,...t}=e;return(0,a.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left",l),...t})};u.displayName="DialogHeader";let m=e=>{let{className:l,...t}=e;return(0,a.jsx)("div",{className:(0,d.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",l),...t})};m.displayName="DialogFooter";let h=r.forwardRef((e,l)=>{let{className:t,...r}=e;return(0,a.jsx)(o.hE,{ref:l,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",t),...r})});h.displayName=o.hE.displayName;let p=r.forwardRef((e,l)=>{let{className:t,...r}=e;return(0,a.jsx)(o.VY,{ref:l,className:(0,d.cn)("text-sm text-muted-foreground",t),...r})});p.displayName=o.VY.displayName},54416:(e,l,t)=>{t.d(l,{A:()=>a});let a=(0,t(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},55799:(e,l,t)=>{t.r(l),t.d(l,{default:()=>u});var a=t(95155),r=t(12115);let o=(0,t(19946).A)("UserRoundCog",[["path",{d:"M2 21a8 8 0 0 1 10.434-7.62",key:"1yezr2"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["circle",{cx:"18",cy:"18",r:"3",key:"1xkwt0"}],["path",{d:"m19.5 14.3-.4.9",key:"1eb35c"}],["path",{d:"m16.9 20.8-.4.9",key:"dfjc4z"}],["path",{d:"m21.7 19.5-.9-.4",key:"q4dx6b"}],["path",{d:"m15.2 16.9-.9-.4",key:"1r0w5f"}],["path",{d:"m21.7 16.5-.9.4",key:"1knoei"}],["path",{d:"m15.2 19.1-.9.4",key:"j188fs"}],["path",{d:"m19.5 21.7-.4-.9",key:"1tonu5"}],["path",{d:"m16.9 15.2-.4-.9",key:"699xu"}]]);var s=t(30285),d=t(54165),b=t(59409),n=t(57141);let c=function(e){let{open:l,onOpenChange:t,studentType:o,handleSubmit:c}=e,[g,i]=r.useState("");return l?(0,a.jsx)(d.lG,{open:l,onOpenChange:t,children:(0,a.jsxs)(d.Cf,{className:"sm:max-w-[425px] rounded-lg border shadow-lg",children:[(0,a.jsx)(d.c7,{className:"pb-4 border-b",children:(0,a.jsx)(d.L3,{className:"text-xl font-medium",children:"修改学员类型"})}),(0,a.jsxs)("div",{className:"py-6",children:[(0,a.jsx)("label",{className:"text-sm text-gray-500 mb-2 block",children:"请选择新的学员类型"}),(0,a.jsxs)(b.l6,{defaultValue:o,onValueChange:e=>i(e),children:[(0,a.jsx)(b.bq,{className:"w-full h-10 rounded-md border border-gray-300 focus:ring-2 focus:ring-primary/20",children:(0,a.jsx)(b.yv,{placeholder:"请选择学员类型"})}),(0,a.jsx)(b.gC,{className:"max-h-60 overflow-auto rounded-md shadow-md",children:Object.entries(n.IC).map(e=>{let[l,t]=e;return(0,a.jsx)(b.eb,{value:l,className:"cursor-pointer hover:bg-gray-100",children:t.label},l)})})]})]}),(0,a.jsxs)(d.Es,{className:"pt-4 border-t flex justify-end gap-3",children:[(0,a.jsx)(s.$,{variant:"outline",onClick:()=>t(!1),className:"px-5 rounded-md hover:bg-gray-100",children:"取消"}),(0,a.jsx)(s.$,{onClick:()=>{if(""===o){alert("请选择学员类型");return}c&&c({selectStudentType:g}),i("")},className:"px-5 rounded-md shadow-sm",children:"确定"})]})]})}):null};var g=t(48436),i=t(95728),x=t(57001);let u=function(e){let{studentId:l,studentType:t}=e,[s]=(0,i.zy)(),[d,b]=r.useState(!1);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.p,{icon:o,tooltipText:"修改学员状态",onClick:()=>b(!0)}),d&&(0,a.jsx)(c,{open:d,onOpenChange:b,studentType:t,handleSubmit:e=>{s({studentId:l,data:{type:e.selectStudentType}}).unwrap().then(e=>{g.l.success("更新学员成功."),b(!1)}).catch(e=>{g.l.error("更新学员失败!")})}})]})}},57001:(e,l,t)=>{t.d(l,{p:()=>s});var a=t(95155),r=t(30285),o=t(46102);function s(e){let{icon:l,tooltipText:t,tooltipSide:s="top",tooltipAlign:d="center",delayDuration:b=300,variant:n="ghost",size:c="icon",className:g="h-8 w-8 hover:bg-muted",...i}=e;return(0,a.jsx)(o.Bc,{delayDuration:b,children:(0,a.jsxs)(o.m_,{children:[(0,a.jsx)(o.k$,{asChild:!0,children:(0,a.jsx)(r.$,{variant:n,size:c,className:g,...i,children:(0,a.jsx)(l,{className:"h-4 w-4 text-muted-foreground"})})}),(0,a.jsx)(o.ZI,{side:s,align:d,className:"font-medium text-xs px-3 py-1.5",children:(0,a.jsx)("p",{children:t})})]})})}},57141:(e,l,t)=>{t.d(l,{C9:()=>r,DT:()=>d,I2:()=>s,IC:()=>i,N4:()=>c,fb:()=>x,lc:()=>a,oD:()=>b,u7:()=>g,uq:()=>o,x9:()=>n});let a={"limited-sessions":{label:"限次",color:"capitalize font-normal bg-blue-50 text-blue-700 hover:bg-blue-100 px-2.5 py-0.5 rounded-md"},"limited-time-and-count":{label:"时次限",color:"capitalize font-normal bg-violet-50 text-violet-700 hover:bg-violet-100 px-2.5 py-0.5 rounded-md"},default:{label:"未知套餐",color:"capitalize font-normal bg-slate-100 text-slate-700 hover:bg-slate-200 px-2.5 py-0.5 rounded-md"}},r={cash:{label:"现金",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},alipay:{label:"支付宝",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},wechat:{label:"微信",color:"bg-green-100 text-green-800 hover:bg-green-200"},card:{label:"银行卡",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},other:{label:"其他",color:"bg-slate-100 text-slate-800 hover:bg-slate-200"}},o=[{id:"wechat",label:"微信"},{id:"alipay",label:"支付宝"},{id:"card",label:"银行转账"},{id:"cash",label:"现金"},{id:"other",label:"其他"}],s={done:{label:"完成",color:"bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400"},arrears:{label:"欠费",color:"bg-amber-100 text-amber-800 hover:bg-amber-200 dark:bg-amber-900/30 dark:text-amber-400"},refunded:{label:"退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400"},default:{label:"未知",color:"bg-slate-100 text-slate-800 hover:bg-slate-200 dark:bg-slate-800/50 dark:text-slate-400"}},d={active:{badgeClass:"bg-emerald-50 text-emerald-700 hover:bg-emerald-100 border-emerald-200",dotClass:"bg-emerald-500",text:"正常"},expired:{badgeClass:"bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200",dotClass:"bg-amber-500",text:"已到期"},refunded:{badgeClass:"bg-red-50 text-red-700 hover:bg-red-100 border-red-200",dotClass:"bg-red-500",text:"已退款"},frozen:{badgeClass:"bg-slate-50 text-slate-700 hover:bg-slate-100 border-slate-200",dotClass:"bg-slate-500",text:"已冻结"},default:{badgeClass:"bg-slate-100 text-slate-700 hover:bg-slate-200 border-slate-200",dotClass:"bg-slate-400",text:"未知状态"}},b={all:{className:"bg-slate-100 text-slate-800 hover:bg-slate-200",text:"全部状态"},attendance:{className:"bg-green-100 text-green-800 hover:bg-green-200",text:"已考勤"},leave:{className:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200",text:"请假"},unattended:{className:"bg-orange-100 text-orange-800 hover:bg-orange-200",text:"未考勤"},default:{className:"bg-red-100 text-red-800 hover:bg-red-200",text:"缺勤"}},n={A:{label:"非常感兴趣",color:"bg-green-100 text-green-800 hover:bg-green-200"},B:{label:"比较感兴趣",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},C:{label:"一般意向",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},D:{label:"了解意向",color:"bg-orange-100 text-orange-800 hover:bg-orange-200"},E:{label:"不感兴趣",color:"bg-red-100 text-red-800 hover:bg-red-200"},default:{label:"未知",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},c={productSales:{label:"产品销售",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},courseSales:{label:"课程销售",color:"bg-green-100 text-green-800 hover:bg-green-200"},OverduePaymentCollection:{label:"逾期款项催收",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},lifestyleAndOffice:{label:"生活和办公",color:"bg-red-100 text-red-800 hover:bg-red-200"},socialAndCatering:{label:"社交和餐饮",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},refunded:{label:"退款",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"其他",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},g={completed:{label:"已完成",color:"bg-green-100 text-green-800 hover:bg-green-200"},pending:{label:"待审核",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},refunded:{label:"已退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},failed:{label:"审核未通过",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"未知状态",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},i={formal:{label:"正式学员",color:"text-blue-500"},graduated:{label:"历史学员",color:"text-green-500"},public:{label:"公海池",color:"text-red-500"},intent:{label:"意向学员",color:"text-yellow-500"}},x={secret:{label:"保密",color:"text-gray-500"},male:{label:"男",color:"text-blue-500"},female:{label:"女",color:"text-pink-500"}}}}]);