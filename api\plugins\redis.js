import fp from "fastify-plugin";
import { redis, redisWebSocket, redisTask } from "../config/redis.js";

async function redisConnector(fastify, options) {
   

    redisWebSocket.on("connect", () => {
        fastify.log.info("redisWebSocket client connected");
    });
    redisWebSocket.on("error", (err) => {
        fastify.log.error("redisWebSocket connection error:", err);
    });
    redisTask.on("connect", () => {
        fastify.log.info("redisTask client connected");
    });
    redisTask.on("error", (err) => {
        fastify.log.error("redisTask connection error:", err);
    });

    // 处理连接事件
    redis.on("connect", () => {
        fastify.log.info("Redis client connected");
    });
    redis.on("error", (err) => {
        fastify.log.error("Redis connection error:", err);
    });
    // 将 redis 客户端注册到 fastify 实例
    fastify.decorate("redis", redis);   
    fastify.decorate("redisWebSocket", redisWebSocket);

    fastify.decorate("redisTask", redisTask);

    // 在服务器关闭时关闭 Redis 连接
    fastify.addHook("onClose", async (instance) => {
        await redis.quit();
        await redisWebSocket.quit();
        await redisTask.quit();
    })
}

export default fp(redisConnector, {
    name: "fastify-redis",
})