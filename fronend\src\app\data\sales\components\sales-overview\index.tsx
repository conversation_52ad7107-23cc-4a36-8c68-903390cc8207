'use client'
import React, { useState, useEffect, useCallback, useMemo } from 'react'

import { BarChart3Icon, LineChartIcon, TrendingUpIcon, CreditCardIcon, ArrowDownIcon } from 'lucide-react'

import ChartComponent from '@/components/ChartComponent'
import { Button } from '@/components/ui/button'

import {  CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import timeRangeCalcTimestamps from '@/utils/timeRangeCalcTimestamps'
import TimeRangeSelect from '@/components/time-range-select'
import { DateRange } from 'react-day-picker'
import { useGetSalesOverviewQuery } from '@/stores/services/financeApi'



// 报表类型选项
const REPORT_TYPE_OPTIONS = [
  { value: 'daily', label: '日报表' },
  { value: 'month', label: '月报表' }
] as const;

interface FilterState {
  timeRange: string;
  checkType: string;
  dateRange: DateRange | undefined;
}

interface TotalState {
  totalPaidAmount: number;
  totalNumberOfPaid: number;
  totalRefundAmount: number;
  totalNumberOfRefund: number;
  totalUnpaidAmount: number;
}

const INITIAL_TOTAL: TotalState = {
  totalPaidAmount: 0,
  totalNumberOfPaid: 0,
  totalRefundAmount: 0,
  totalNumberOfRefund: 0,
  totalUnpaidAmount: 0
};

function SalesOverview() {

    // 状态管理
    const [dateLabel, setDateLabel] = useState<string[]>([])
    const [filters, setFilters] = useState<FilterState>({
        timeRange: "last7days",
        checkType: "daily",
        dateRange: undefined
    })
    const [chartType, setChartType] = useState<"bar" | "line">("line")
    const [total, setTotal] = useState<TotalState>(INITIAL_TOTAL)

    const [chartData, setChartData] = useState<any[]>([])


    const queryArgs = useMemo(() => {
        const { startTime, endTime } = timeRangeCalcTimestamps(filters.timeRange, filters.dateRange)
        return {
            startTime,
            endTime,
            checkType: filters.checkType
        }
    },[filters])

        const { data, isLoading } = useGetSalesOverviewQuery(queryArgs)

    useEffect(() => {
        if (!data || isLoading) return;

        const { datasets, totals } = data;
        setDateLabel(datasets.dateLabel);
        setTotal(totals);
        setChartData([
            { 
                label: '销售数据',
                values: datasets.paidAmount,
                borderColor: '#38bdf8',
                backgroundColor: 'rgba(56, 189, 248, 0.1)',
                borderWidth: 2
            },
            {   
                label: '退款数据', 
                values: datasets.refundAmount,
                borderColor: '#f43f5e',
                backgroundColor: 'rgba(244, 63, 94, 0.1)',
                borderWidth: 2
            },
            { 
                label: '成交数', 
                values: datasets.numberOfPaid,
                hidden: true
            },
            { 
                label: '退款数',
                values: datasets.numberOfRefund,
                hidden: true
            }
        ]);


    },[data, isLoading])

    // 处理筛选条件变化
    const handleFilterChange = useCallback((key: keyof FilterState, value: any) => {
        setFilters(prev => ({ ...prev, [key]: value }));
    }, []);

    // 切换图表类型
    const toggleChartType = useCallback(() => setChartType(prev => prev === "bar" ? "line" : "bar"), []);

    // 计算净收入比例
    const netIncomeRatio = useMemo(() => {
        if (total.totalPaidAmount <= 0) return "0";
        return (((total.totalPaidAmount - total.totalRefundAmount) / total.totalPaidAmount) * 100).toFixed(1);
    }, [total.totalPaidAmount, total.totalRefundAmount]);

    // 计算净收入
    const netIncome = useMemo(() => {
        return total.totalPaidAmount - total.totalRefundAmount;
    }, [total.totalPaidAmount, total.totalRefundAmount]);

    return (
        <div className="space-y-6 animate-in">
            {/* 数据摘要区域 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* 销售卡片 */}
                <div className="bg-background rounded-lg transition-all duration-200 group hover:shadow-md hover:translate-y-[-2px]">
                    <CardHeader className="pb-2 border-b border-transparent group-hover:border-primary/10 bg-background group-hover:bg-blue-50/50 dark:group-hover:bg-blue-950/10">
                        <CardTitle className="flex items-center text-sm font-medium text-muted-foreground group-hover:text-blue-600">
                            <CreditCardIcon className="mr-2 h-4 w-4 text-blue-500/70 group-hover:text-blue-500" />
                            销售数据
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                        <div className="flex justify-between items-center">
                            <div>
                                <p className="text-xs text-muted-foreground mb-1">销售单数</p>
                                <p className="text-2xl font-bold">{total.totalNumberOfPaid.toLocaleString()}</p>
                            </div>
                            <div className="text-right">
                                <p className="text-xs text-muted-foreground mb-1">销售总额</p>
                                <p className="text-2xl font-bold text-blue-600">¥{total.totalPaidAmount.toLocaleString()}</p>
                            </div>
                        </div>
                    </CardContent>
                </div>

                {/* 退款卡片 */}
                <div className="bg-background rounded-lg transition-all duration-200 group hover:shadow-md hover:translate-y-[-2px]">
                    <CardHeader className="pb-2 border-b border-transparent group-hover:border-primary/10 bg-background group-hover:bg-red-50/50 dark:group-hover:bg-red-950/10">
                        <CardTitle className="flex items-center text-sm font-medium text-muted-foreground group-hover:text-red-600">
                            <ArrowDownIcon className="mr-2 h-4 w-4 text-red-500/70 group-hover:text-red-500" />
                            退款数据
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                        <div className="flex justify-between items-center">
                            <div>
                                <p className="text-xs text-muted-foreground mb-1">退款单数</p>
                                <p className="text-2xl font-bold">{total.totalNumberOfRefund.toLocaleString()}</p>
                            </div>
                            <div className="text-right">
                                <p className="text-xs text-muted-foreground mb-1">退款总额</p>
                                <p className="text-2xl font-bold text-red-600">¥{total.totalRefundAmount.toLocaleString()}</p>
                            </div>
                        </div>
                    </CardContent>
                </div>

                {/* 欠款金额 */}
                <div className="bg-background rounded-lg transition-all duration-200 group hover:shadow-md hover:translate-y-[-2px]">
                    <CardHeader className="pb-2 border-b border-transparent group-hover:border-primary/10 bg-background group-hover:bg-amber-50/50 dark:group-hover:bg-amber-950/10">
                        <CardTitle className="flex items-center text-sm font-medium text-muted-foreground group-hover:text-amber-600">
                            <TrendingUpIcon className="mr-2 h-4 w-4 text-amber-500/70 group-hover:text-amber-500" />
                            欠款金额
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                        <div className="flex justify-between items-center">
                            <div className="text-right">
                                <p className="text-xs text-muted-foreground mb-1">欠款金额</p>
                                <p className="text-2xl font-bold text-amber-600">¥{total.totalUnpaidAmount.toLocaleString()}</p>
                            </div>
                        </div>
                    </CardContent>
                </div>
                {/* 净收入卡片 */}
                <div className="bg-background rounded-lg transition-all duration-200 group hover:shadow-md hover:translate-y-[-2px]">
                    <CardHeader className="pb-2 border-b border-transparent group-hover:border-primary/10 bg-background group-hover:bg-emerald-50/50 dark:group-hover:bg-emerald-950/10">
                        <CardTitle className="flex items-center text-sm font-medium text-muted-foreground group-hover:text-emerald-600">
                            <TrendingUpIcon className="mr-2 h-4 w-4 text-emerald-500/70 group-hover:text-emerald-500" />
                            净收入
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                        <div className="flex justify-between items-center">
                            <div>
                                <p className="text-xs text-muted-foreground mb-1">净值比例</p>
                                <p className="text-2xl font-bold">{netIncomeRatio}%</p>
                            </div>
                            <div className="text-right">
                                <p className="text-xs text-muted-foreground mb-1">净收入</p>
                                <p className="text-2xl font-bold text-emerald-600">¥{netIncome.toLocaleString()}</p>
                            </div>
                        </div>
                    </CardContent>
                </div>
            </div>

            {/* 图表区域 */}
            <div >
                <CardHeader className='flex-row items-center border-b border-transparent pb-4'>
                    <div>
                        <CardTitle className="flex items-center text-lg">
                            <TrendingUpIcon className="mr-2 h-5 w-5 text-primary/70" />
                            销售趋势分析
                        </CardTitle>
                        <CardDescription>
                            销售与退款数据分析
                        </CardDescription>
                    </div>

                    <Button
                        variant="ghost"
                        size="sm"
                        className="ml-auto flex items-center gap-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                        onClick={toggleChartType}
                    >
                        {chartType === "bar" ? 
                            <><LineChartIcon className="h-4 w-4 mr-1" /> 切换为折线图</> : 
                            <><BarChart3Icon className="h-4 w-4 mr-1" /> 切换为柱状图</>}
                    </Button>
                </CardHeader>
                
                <CardContent className="pt-4">
                    <div className="mb-6 flex flex-wrap gap-3 items-center p-3 bg-muted/30 rounded-lg">
                        <TimeRangeSelect 
                            timeRange={filters.timeRange}
                            onTimeRangeChange={(value) => handleFilterChange('timeRange', value)}
                            dateRange={filters.dateRange}
                            onDateRangeChange={(value) => handleFilterChange('dateRange', value)}
                        />
                        
                        <Select 
                            value={filters.checkType} 
                            onValueChange={(value) => handleFilterChange('checkType', value)}
                        >
                            <SelectTrigger className="h-9 w-[140px]">
                                <SelectValue placeholder="报表类型" />
                            </SelectTrigger>
                            <SelectContent>
                                {REPORT_TYPE_OPTIONS.map(option => (
                                    <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    
                    {isLoading ? (
                        <div className="flex justify-center items-center h-[400px]">
                            <div className="flex flex-col items-center gap-2">
                                <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                                <p className="text-sm text-muted-foreground">加载数据中...</p>
                            </div>
                        </div>
                    ) : (
                        <ChartComponent
                            height={400}
                            type={chartType}
                            data={chartData}
                            labels={dateLabel}
                            title="销售数据"
                        />
                    )}
                </CardContent>
            </div>
        </div>
    )
}

export default SalesOverview