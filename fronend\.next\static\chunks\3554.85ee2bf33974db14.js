"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3554,7649],{12318:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},15452:(e,t,r)=>{r.d(t,{G$:()=>z,Hs:()=>N,UC:()=>et,VY:()=>ea,ZL:()=>Q,bL:()=>K,bm:()=>en,hE:()=>er,hJ:()=>ee,l9:()=>X});var a=r(12115),n=r(85185),o=r(6101),s=r(46081),l=r(61285),i=r(5845),d=r(19178),c=r(25519),u=r(34378),p=r(28905),f=r(63655),m=r(92293),g=r(93795),x=r(38168),h=r(99708),v=r(95155),y="Dialog",[j,N]=(0,s.A)(y),[b,D]=j(y),w=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:o,onOpenChange:s,modal:d=!0}=e,c=a.useRef(null),u=a.useRef(null),[p=!1,f]=(0,i.i)({prop:n,defaultProp:o,onChange:s});return(0,v.jsx)(b,{scope:t,triggerRef:c,contentRef:u,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:p,onOpenChange:f,onOpenToggle:a.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};w.displayName=y;var R="DialogTrigger",C=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,s=D(R,r),l=(0,o.s)(t,s.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":Y(s.open),...a,ref:l,onClick:(0,n.m)(e.onClick,s.onOpenToggle)})});C.displayName=R;var A="DialogPortal",[E,O]=j(A,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:o}=e,s=D(A,t);return(0,v.jsx)(E,{scope:t,forceMount:r,children:a.Children.map(n,e=>(0,v.jsx)(p.C,{present:r||s.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:o,children:e})}))})};I.displayName=A;var k="DialogOverlay",F=a.forwardRef((e,t)=>{let r=O(k,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,o=D(k,e.__scopeDialog);return o.modal?(0,v.jsx)(p.C,{present:a||o.open,children:(0,v.jsx)(_,{...n,ref:t})}):null});F.displayName=k;var _=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=D(k,r);return(0,v.jsx)(g.A,{as:h.DX,allowPinchZoom:!0,shards:[n.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":Y(n.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),P="DialogContent",Z=a.forwardRef((e,t)=>{let r=O(P,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,o=D(P,e.__scopeDialog);return(0,v.jsx)(p.C,{present:a||o.open,children:o.modal?(0,v.jsx)(L,{...n,ref:t}):(0,v.jsx)(T,{...n,ref:t})})});Z.displayName=P;var L=a.forwardRef((e,t)=>{let r=D(P,e.__scopeDialog),s=a.useRef(null),l=(0,o.s)(t,r.contentRef,s);return a.useEffect(()=>{let e=s.current;if(e)return(0,x.Eq)(e)},[]),(0,v.jsx)(B,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=a.forwardRef((e,t)=>{let r=D(P,e.__scopeDialog),n=a.useRef(!1),o=a.useRef(!1);return(0,v.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,s;null===(a=e.onCloseAutoFocus)||void 0===a||a.call(e,t),t.defaultPrevented||(n.current||null===(s=r.triggerRef.current)||void 0===s||s.focus(),t.preventDefault()),n.current=!1,o.current=!1},onInteractOutside:t=>{var a,s;null===(a=e.onInteractOutside)||void 0===a||a.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let l=t.target;(null===(s=r.triggerRef.current)||void 0===s?void 0:s.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),B=a.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:s,onCloseAutoFocus:l,...i}=e,u=D(P,r),p=a.useRef(null),f=(0,o.s)(t,p);return(0,m.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:s,onUnmountAutoFocus:l,children:(0,v.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Y(u.open),...i,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(S,{titleId:u.titleId}),(0,v.jsx)(W,{contentRef:p,descriptionId:u.descriptionId})]})]})}),G="DialogTitle",U=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=D(G,r);return(0,v.jsx)(f.sG.h2,{id:n.titleId,...a,ref:t})});U.displayName=G;var V="DialogDescription",q=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=D(V,r);return(0,v.jsx)(f.sG.p,{id:n.descriptionId,...a,ref:t})});q.displayName=V;var H="DialogClose",M=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,o=D(H,r);return(0,v.jsx)(f.sG.button,{type:"button",...a,ref:t,onClick:(0,n.m)(e.onClick,()=>o.onOpenChange(!1))})});function Y(e){return e?"open":"closed"}M.displayName=H;var $="DialogTitleWarning",[z,J]=(0,s.q)($,{contentName:P,titleName:G,docsSlug:"dialog"}),S=e=>{let{titleId:t}=e,r=J($),n="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return a.useEffect(()=>{t&&!document.getElementById(t)&&console.error(n)},[n,t]),null},W=e=>{let{contentRef:t,descriptionId:r}=e,n=J("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return a.useEffect(()=>{var e;let a=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&a&&!document.getElementById(r)&&console.warn(o)},[o,t,r]),null},K=w,X=C,Q=I,ee=F,et=Z,er=U,ea=q,en=M},17649:(e,t,r)=>{r.d(t,{UC:()=>P,VY:()=>B,ZD:()=>L,ZL:()=>F,bL:()=>I,hE:()=>T,hJ:()=>_,l9:()=>k,rc:()=>Z});var a=r(12115),n=r(46081),o=r(6101),s=r(15452),l=r(85185),i=r(99708),d=r(95155),c="AlertDialog",[u,p]=(0,n.A)(c,[s.Hs]),f=(0,s.Hs)(),m=e=>{let{__scopeAlertDialog:t,...r}=e,a=f(t);return(0,d.jsx)(s.bL,{...a,...r,modal:!0})};m.displayName=c;var g=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=f(r);return(0,d.jsx)(s.l9,{...n,...a,ref:t})});g.displayName="AlertDialogTrigger";var x=e=>{let{__scopeAlertDialog:t,...r}=e,a=f(t);return(0,d.jsx)(s.ZL,{...a,...r})};x.displayName="AlertDialogPortal";var h=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=f(r);return(0,d.jsx)(s.hJ,{...n,...a,ref:t})});h.displayName="AlertDialogOverlay";var v="AlertDialogContent",[y,j]=u(v),N=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...c}=e,u=f(r),p=a.useRef(null),m=(0,o.s)(t,p),g=a.useRef(null);return(0,d.jsx)(s.G$,{contentName:v,titleName:b,docsSlug:"alert-dialog",children:(0,d.jsx)(y,{scope:r,cancelRef:g,children:(0,d.jsxs)(s.UC,{role:"alertdialog",...u,...c,ref:m,onOpenAutoFocus:(0,l.m)(c.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=g.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(i.xV,{children:n}),(0,d.jsx)(O,{contentRef:p})]})})})});N.displayName=v;var b="AlertDialogTitle",D=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=f(r);return(0,d.jsx)(s.hE,{...n,...a,ref:t})});D.displayName=b;var w="AlertDialogDescription",R=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=f(r);return(0,d.jsx)(s.VY,{...n,...a,ref:t})});R.displayName=w;var C=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=f(r);return(0,d.jsx)(s.bm,{...n,...a,ref:t})});C.displayName="AlertDialogAction";var A="AlertDialogCancel",E=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,{cancelRef:n}=j(A,r),l=f(r),i=(0,o.s)(t,n);return(0,d.jsx)(s.bm,{...l,...a,ref:i})});E.displayName=A;var O=e=>{let{contentRef:t}=e,r="`".concat(v,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(v,"` by passing a `").concat(w,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(v,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return a.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},I=m,k=g,F=x,_=h,P=N,Z=C,L=E,T=D,B=R},57001:(e,t,r)=>{r.d(t,{p:()=>s});var a=r(95155),n=r(30285),o=r(46102);function s(e){let{icon:t,tooltipText:r,tooltipSide:s="top",tooltipAlign:l="center",delayDuration:i=300,variant:d="ghost",size:c="icon",className:u="h-8 w-8 hover:bg-muted",...p}=e;return(0,a.jsx)(o.Bc,{delayDuration:i,children:(0,a.jsxs)(o.m_,{children:[(0,a.jsx)(o.k$,{asChild:!0,children:(0,a.jsx)(n.$,{variant:d,size:c,className:u,...p,children:(0,a.jsx)(t,{className:"h-4 w-4 text-muted-foreground"})})}),(0,a.jsx)(o.ZI,{side:s,align:l,className:"font-medium text-xs px-3 py-1.5",children:(0,a.jsx)("p",{children:r})})]})})}},73554:(e,t,r)=>{r.r(t),r.d(t,{default:()=>c});var a=r(95155),n=r(12115),o=r(12318),s=r(90010),l=r(48436),i=r(57001),d=r(95728);let c=function(e){let{studentId:t}=e,[r]=(0,d.nu)(),[c,u]=(0,n.useState)(!1),p=async()=>{try{await r({studentId:t}),l.l.success("分配学员成功."),u(!1)}catch(e){l.l.error(e.message||"分配学员失败!")}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.p,{icon:o.A,tooltipText:"分配给自己",onClick:()=>u(!0)}),(0,a.jsx)(s.Lt,{open:c,onOpenChange:u,children:(0,a.jsxs)(s.EO,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(s.wd,{children:[(0,a.jsx)(s.r7,{children:"确认操作"}),(0,a.jsx)(s.$v,{children:"确认将学员分配给自己？分配后该学员将从公海池中移除。"})]}),(0,a.jsxs)(s.ck,{children:[(0,a.jsx)(s.Zr,{className:"h-9",children:"取消"}),(0,a.jsx)(s.Rx,{onClick:p,className:"h-9 bg-primary hover:bg-primary/90",children:"确认"})]})]})})]})}},90010:(e,t,r)=>{r.d(t,{$v:()=>x,EO:()=>p,Lt:()=>i,Rx:()=>h,Zr:()=>v,ck:()=>m,r7:()=>g,tv:()=>d,wd:()=>f});var a=r(95155),n=r(12115),o=r(17649),s=r(59434),l=r(30285);let i=o.bL,d=o.l9,c=o.ZL,u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.hJ,{className:(0,s.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...n,ref:t})});u.displayName=o.hJ.displayName;let p=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(u,{}),(0,a.jsx)(o.UC,{ref:t,className:(0,s.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...n})]})});p.displayName=o.UC.displayName;let f=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,s.cn)("flex flex-col space-y-2 text-center sm:text-left",t),...r})};f.displayName="AlertDialogHeader";let m=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,s.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...r})};m.displayName="AlertDialogFooter";let g=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.hE,{ref:t,className:(0,s.cn)("text-lg font-semibold",r),...n})});g.displayName=o.hE.displayName;let x=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.VY,{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...n})});x.displayName=o.VY.displayName;let h=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.rc,{ref:t,className:(0,s.cn)((0,l.r)(),r),...n})});h.displayName=o.rc.displayName;let v=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.ZD,{ref:t,className:(0,s.cn)((0,l.r)({variant:"outline"}),"mt-2 sm:mt-0",r),...n})});v.displayName=o.ZD.displayName}}]);