"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2319],{15452:(e,t,r)=>{r.d(t,{G$:()=>X,Hs:()=>D,UC:()=>et,VY:()=>en,ZL:()=>Q,bL:()=>Y,bm:()=>eo,hE:()=>er,hJ:()=>ee,l9:()=>$});var n=r(12115),o=r(85185),a=r(6101),l=r(46081),i=r(61285),s=r(5845),d=r(19178),u=r(25519),c=r(34378),f=r(28905),p=r(63655),g=r(92293),m=r(93795),v=r(38168),h=r(99708),y=r(95155),w="Dialog",[x,D]=(0,l.A)(w),[b,j]=x(w),C=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[f=!1,p]=(0,s.i)({prop:o,defaultProp:a,onChange:l});return(0,y.jsx)(b,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:d,children:r})};C.displayName=w;var R="DialogTrigger",I=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=j(R,r),i=(0,a.s)(t,l.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":Z(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});I.displayName=R;var N="DialogPortal",[A,_]=x(N,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=j(N,t);return(0,y.jsx)(A,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,y.jsx)(f.C,{present:r||l.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=N;var O="DialogOverlay",k=n.forwardRef((e,t)=>{let r=_(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=j(O,e.__scopeDialog);return a.modal?(0,y.jsx)(f.C,{present:n||a.open,children:(0,y.jsx)(F,{...o,ref:t})}):null});k.displayName=O;var F=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(O,r);return(0,y.jsx)(m.A,{as:h.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":Z(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",G=n.forwardRef((e,t)=>{let r=_(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=j(P,e.__scopeDialog);return(0,y.jsx)(f.C,{present:n||a.open,children:a.modal?(0,y.jsx)(S,{...o,ref:t}):(0,y.jsx)(L,{...o,ref:t})})});G.displayName=P;var S=n.forwardRef((e,t)=>{let r=j(P,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(M,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=n.forwardRef((e,t)=>{let r=j(P,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,y.jsx)(M,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(l=r.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let i=t.target;(null===(l=r.triggerRef.current)||void 0===l?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),M=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=j(P,r),f=n.useRef(null),p=(0,a.s)(t,f);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,y.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Z(c.open),...s,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(J,{titleId:c.titleId}),(0,y.jsx)(K,{contentRef:f,descriptionId:c.descriptionId})]})]})}),T="DialogTitle",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(T,r);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...n,ref:t})});B.displayName=T;var q="DialogDescription",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(q,r);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:t})});W.displayName=q;var H="DialogClose",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(H,r);return(0,y.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}V.displayName=H;var U="DialogTitleWarning",[X,z]=(0,l.q)(U,{contentName:P,titleName:T,docsSlug:"dialog"}),J=e=>{let{titleId:t}=e,r=z(U),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},K=e=>{let{contentRef:t,descriptionId:r}=e,o=z("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(a)},[a,t,r]),null},Y=C,$=I,Q=E,ee=k,et=G,er=B,en=W,eo=V},54416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},56287:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]])},85977:(e,t,r)=>{r.d(t,{H4:()=>D,_V:()=>x,bL:()=>w});var n=r(12115),o=r(46081),a=r(39033),l=r(52712),i=r(63655),s=r(95155),d="Avatar",[u,c]=(0,o.A)(d),[f,p]=u(d),g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[a,l]=n.useState("idle");return(0,s.jsx)(f,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,s.jsx)(i.sG.span,{...o,ref:t})})});g.displayName=d;var m="AvatarImage",v=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:d=()=>{},...u}=e,c=p(m,r),f=function(e,t){let[r,o]=n.useState("idle");return(0,l.N)(()=>{if(!e){o("error");return}let r=!0,n=new window.Image,a=e=>()=>{r&&o(e)};return o("loading"),n.onload=a("loaded"),n.onerror=a("error"),n.src=e,t&&(n.referrerPolicy=t),()=>{r=!1}},[e,t]),r}(o,u.referrerPolicy),g=(0,a.c)(e=>{d(e),c.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==f&&g(f)},[f,g]),"loaded"===f?(0,s.jsx)(i.sG.img,{...u,ref:t,src:o}):null});v.displayName=m;var h="AvatarFallback",y=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...a}=e,l=p(h,r),[d,u]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>u(!0),o);return()=>window.clearTimeout(e)}},[o]),d&&"loaded"!==l.imageLoadingStatus?(0,s.jsx)(i.sG.span,{...a,ref:t}):null});y.displayName=h;var w=g,x=v,D=y}}]);