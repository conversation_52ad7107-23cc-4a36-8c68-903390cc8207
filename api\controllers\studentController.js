import { INTERNAL_ERROR } from '../errors/index.js';
import { studentService } from '../services/studentService.js';

/**
 * 学生控制器
 */
export const studentController = {
    /**
     * 获取学生列表
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学生列表
     */
    async getStudentList(request, reply) {
        try {
            const institutionId = request.user.institutionId;
            const { page, pageSize, search, follower, type, intentLevel } = request.query;

            // 调用服务层获取学生列表
            const { students, total } = await studentService.getStudentList(request.server, {
                institutionId,
                page,
                pageSize,
                search,
                follower,
                type,
                intentLevel
            });

            // 返回成功响应
            reply.success({
                data: {
                    list: students,
                    total,
                    page: parseInt(page, 10) || 1,
                    pageSize: parseInt(pageSize, 10) || 10
                },
                message: '获取学生列表成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            // 记录未知错误
            request.server.log.error(error);
            throw new INTERNAL_ERROR('获取学生列表失败');
        }
    },
    /**
     * 获取简易学员列表
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 简易学员列表
     */
    async getSimpleStudentList(request, reply) {
        try {
            const institutionId = request.user.institutionId;
            const { page, pageSize, search} = request.query;
            const currentPage = Math.max(Number(page), 1);
            const limit = Math.max(Number(pageSize), 1);
            const offset = (currentPage - 1) * limit;
            // 调用服务层获取简易学员列表
            const { students, total } = await studentService.getSimpleStudentList(request.server, {
                institutionId,
                limit,
                offset,
                search
            });
            // 返回成功响应
            reply.success({
                data: {
                    list: students,
                    total,
                    page: parseInt(page, 10) || 1,
                    pageSize: parseInt(pageSize, 10) || 10
                },
                message: '获取简易学员列表成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
            throw new INTERNAL_ERROR('获取简易学员列表失败');
        }
    },
    /**
     * 获取单个学生信息
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学生信息
     */
    async getStudentById(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            // 调用服务层获取学生信息
            const student = await studentService.getStudentById(request.server, {
                studentId,
                institutionId
            });

            // 返回成功响应
            reply.success({
                data: student,
                message: '获取学生信息成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            // 记录未知错误
            request.server.log.error(error);
            throw new INTERNAL_ERROR('获取学生信息失败');
        }
    },
    /**
     * 更新学生信息
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 更新后的学生信息
     */
    async updateStudent(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            const { name, gender, phone, age, birthday, email, balance, points, followUpPerson, followUpDate, source, referrer, address, idCard, school, intentionLevel, parentName, status, remark, type } = request.body;
        
            // 调用服务层更新学生信息
            const updatedStudent = await studentService.updateStudent(request.server, {
                studentId,
                institutionId,
                name, gender, phone, age, birthday, email, balance, points, followUpPerson, followUpDate, source, referrer, address, idCard, school, intentionLevel, parentName, status, remark, type
            });
            // 返回成功响应
            reply.success({
                message: '更新学生信息成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
            throw new INTERNAL_ERROR('更新学生信息失败');
        }
    },
    /**
     * 批量删除学员
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 删除后的学员列表  
     */
    async deleteStudent(request, reply) {
        try {
            const { studentIds } = request.body;
            const institutionId = request.user.institutionId;
            // 调用服务层批量删除学员
            const deletedStudents = await studentService.deleteStudent(request.server, {
                studentIds,
                institutionId
            });
            // 返回成功响应
            reply.success({
                message: '删除学员成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
            throw new INTERNAL_ERROR('删除学生信息失败');
        }
    },
    /**
     * 获取学生上课记录
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学生上课记录
     */
    async getClassesHistory(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            const { page = 1, pageSize = 10, startDate, endDate } = request.query;
            const skip = (page - 1) * pageSize;
            const take = pageSize;
            // 调用服务层获取学生上课记录
            const { list, total } = await studentService.getClassesHistory(request.server, {
                studentId,
                institutionId,
                skip,
                take,
                startDate,
                endDate
            });
            // 返回成功响应
            reply.success({
                data: {
                    list,
                    total,
                    page: parseInt(page, 10) || 1,
                    pageSize: parseInt(pageSize, 10) || 10
                },
                message: '获取学生上课记录成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 获取学生购买套餐
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学生购买套餐
     */
    async getProducts(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            const { status } = request.query;
            // 调用服务层获取学生购买套餐
            const result = await studentService.getProducts(request.server, {
                studentId,
                institutionId,
                status
            });
            // 返回成功响应
            reply.success({
                data: result,
                message: '获取学生购买套餐成功' 
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }   
            // 记录未知错误
                request.server.log.error(error);
            throw new INTERNAL_ERROR('获取学生购买套餐失败');
        }
    },
    /**
     * 获取学生购买记录
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学生购买记录
     */
    async getProductsRecords(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
           // 调用服务层获取学生购买记录
           const result = await studentService.getProductsRecords(request.server, {
            studentId,
            institutionId
           });
           // 返回成功响应
           reply.success({
            data: result,
            message: '获取学生购买记录成功'
           });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 获取学生考勤记录
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学生考勤记录
     */
    async getAttendance(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            const { page = 1, pageSize = 10, startDate, endDate } = request.query;
            const currentPage = Math.max(Number(page), 1);
            const limit = Math.max(Number(pageSize), 1);
            const offset = (currentPage - 1) * limit;
            // 调用服务层获取学生考勤记录
            const { list, total } = await studentService.getAttendance(request.server, {
                studentId,
                institutionId,
                limit,
                offset,
                startDate,
                endDate
            });
            // 返回成功响应
            reply.success({
                data: {
                    list,
                    total,
                    page: parseInt(page, 10) || 1,
                    pageSize: parseInt(pageSize, 10) || 10
                },
                message: '获取学生考勤记录成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 获取学员班级
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学员班级
     */
    async getClasses(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            // 调用服务层获取学员班级
            const result = await studentService.getClasses(request.server, {
                studentId,
                institutionId
            });
            // 返回成功响应
            reply.success({
                data: result,
                message: '获取学员班级成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 学员退出班级
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学员退出班级
     */
    async outClasses(request, reply) {
        try {
            const { classesId, studentId } = request.params;
            const institutionId = request.user.institutionId;
            // 调用服务层学员退出班级
            await studentService.outClasses(request.server, {
                classesId, studentId, institutionId });
            // 返回成功响应
            reply.success({
                message: '学员退出班级成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 获取学员跟进记录
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学员跟进记录
     */
    async getFollowRecords(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            // 调用服务层获取学员跟进记录
            const result = await studentService.getFollowRecords(request.server, {
                studentId,
                institutionId
            });
            // 返回成功响应
            reply.success({
                data: result,
                message: '获取学员跟进记录成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 新增学员跟进记录
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 新增学员跟进记录
     */
    async addFollowRecords(request, reply) {
        try {
            const { studentId } = request.params;
            const institutionId = request.user.institutionId;
            const { followUpDate, nextFollowUpDate, followUpContent, followUpUserId, intentLevel } = request.body;
            // 调用服务层新增学员跟进记录
            await studentService.addFollowRecords(request.server, { studentId, institutionId, followUpDate, nextFollowUpDate, followUpContent, followUpUserId, intentLevel, userId: request.user.id });
            // 返回成功响应
            reply.success({
                message: '新增学员跟进记录成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 创建学生产品
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 创建学生产品
     */
    async createStudentProduct(request, reply) {
        try {
            const { studentId } = request.params;
            const institutionId = request.user.institutionId;
            const productData = request.body;
            const amountPaid = productData.prepaidAmount;
            // const balance = productData.amount - amountPaid;
            // 调用服务层创建学生产品
            await studentService.createStudentProduct(request.server, {...productData, amountPaid, studentId, institutionId, userId: request.user.id});
            // 返回成功响应
            reply.success({
                message: '创建学生产品成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 更新学生产品
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 更新学生产品
     */
    async updateStudentProduct(request, reply) {
        try {
            const { studentId, studentProductId } = request.params;
            const institutionId = request.user.institutionId;
            const { status, totalCount } = request.body;
            // 调用服务层更新学生产品
            await studentService.updateStudentProduct(request.server, { studentId, studentProductId, institutionId, status, totalCount, userId: request.user.id });
            // 返回成功响应
            reply.success({
                message: '更新学生产品成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 创建学员跟进人
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 创建学员跟进人
     */
    async createFollowUp(request, reply) {
        try {
            const { studentId } = request.params;
            const institutionId = request.user.institutionId;
            // 调用服务层创建学员跟进人
            const followUpDate = Number(new Date().getTime());
            await studentService.createFollowUp(request.server, { studentId, institutionId, followUpDate,userId: request.user.id });
            // 返回成功响应
            reply.success({
                message: '创建学员跟进人成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    }
}; 

