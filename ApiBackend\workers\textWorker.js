/**
 * Text generation worker for BullMQ
 * Handles text generation tasks using the textGeneration service
 */

import { Worker } from "bullmq";
import { redisTask } from "../config/redis.js";
import { generateText } from "../services/textGeneration.js";
import {
  defaultWorkerOptions,
  markTaskCompleted,
  markTaskFailed,
  setupProgressReporting
} from "./workerConfig.js";
import textQueue from "../queues/textQueue.js";

// Create the worker
const worker = new Worker(
  'text-generation',
  async (job) => {
    const { taskId, prompt, maxTokens, temperature, model } = job.data;
    console.log(`Starting text generation job ${job.id}, taskId: ${taskId}`);

    const startTime = Date.now();

    // Setup progress reporting
    const { progressInterval, updateProgress } = setupProgressReporting(taskId);

    try {
      // Update initial progress
      await updateProgress(10);

      // Generate the text
      const result = await generateText(prompt, maxTokens, temperature, model);

      // Clear progress reporting interval
      clearInterval(progressInterval);

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Mark task as completed
      await markTaskCompleted(taskId, result, 'text-generation', processingTime);

      console.log(`Text generation job ${job.id} completed, processing time: ${processingTime}ms`);
      return result;
    } catch (error) {
      console.error(`Text generation job ${job.id} failed:`, error);

      // Clear progress reporting interval
      clearInterval(progressInterval);

      // Mark task as failed
      await markTaskFailed(taskId, error, 'text-generation', Date.now() - startTime);

      // Rethrow the error for BullMQ to handle retries
      throw error;
    }
  },
  defaultWorkerOptions
);

// Add error handling for the worker
worker.on('error', (error) => {
  console.error('Text generation worker error:', error);
});

worker.on('failed', (job, error) => {
  console.error(`Text generation worker job ${job?.id} failed:`, error);
});

worker.on('completed', (job) => {
  console.log(`Text generation worker completed job ${job.id}`);
});

export default textQueue;
