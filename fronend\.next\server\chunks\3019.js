exports.id=3019,exports.ids=[3019],exports.modules={4637:(e,t,r)=>{"use strict";r.d(t,{K:()=>d,v:()=>n});var s=r(20340),o=r(65586),a=r(52023);let n=(0,s.xP)({reducerPath:"notificationsApi",baseQuery:(0,o.cw)({baseUrl:"/api",prepareHeaders:(e,{getState:t})=>{let r=t().user?.token;return r&&e.set("authorization",`Bearer ${r}`),e}}),endpoints:e=>({getUnreadCount:e.query({query:()=>"/notifications/unreadCount",transformResponse:e=>(console.log("Unread count response:",e),200===e.code&&"number"==typeof e.data)?e.data:0,transformErrorResponse:async(e,t,r)=>{console.error("Error response:",e);let{status:s,data:o}=e;return 401===s&&await (0,a.B)(),0}})})}),{useGetUnreadCountQuery:d}=n},7112:(e,t,r)=>{"use strict";r.d(t,{A:()=>m,AuthProvider:()=>c});var s=r(60687),o=r(30596),a=r(41565),n=r(41169),d=r(16189),i=r(43210);r(50534);let u=(0,i.createContext)(void 0),l=["/","/login","/404","/unauthorized","/1","/notifications/list","/features","/features/course-scheduling"];function c({children:e}){let t=(0,o.G)(e=>e.user),[r,c]=(0,i.useState)([]),[m,p]=(0,i.useState)(!0),[g,h]=(0,i.useState)(!1),f=(0,o.j)(),y=(0,d.useRouter)();(0,d.usePathname)();let T=(0,o.G)(e=>e.userMenus.menus),v=(0,o.G)(e=>e.userPermissions.permissions);console.log(v,"userPermissions");let{data:S,isLoading:q}=(0,a.DQ)({},{skip:!t.isAuthenticated||!t.token||(T?.length||0)>0}),{data:P,isLoading:E}=(0,n.H)({},{skip:!t.isAuthenticated||!t.token||(v?.length||0)>0}),R=(0,i.useCallback)((e,t)=>{if(!e)return!1;if(e===t)return!0;let r=e.split("/").filter(Boolean),s=t.split("/").filter(Boolean);if(1>=Math.abs(r.length-s.length)){if(s.length===r.length+1)for(let e=0;e<s.length;e++){let t=[...s];if(t.splice(e,1),r.join("/")===t.join("/"))return!0}if(s.length===r.length&&s.length>1){let e=0,t=0;for(let o=0;o<r.length;o++)r[o]!==s[o]?e++:t++;if(1===e&&t>0)return!0}}if(e.replace(/\/\d+(?=\/|$)/g,"")===t.replace(/\/\d+(?=\/|$)/g,""))return!0;if(e.includes("["))try{let r=e.replace(/\/\[\.\.\..*?\]/g,"/(.+)").replace(/\/\[.*?\]/g,"/([^/]+)");return RegExp(`^${r}$`).test(t)}catch(e){console.error("路径匹配正则表达式错误:",e)}return!1},[]),w=(0,i.useCallback)((e,t)=>{for(let r of e)if(R(r.path,t)||r.children&&r.children.length>0&&w(r.children,t))return!0;return!1},[R]),b=(0,i.useMemo)(()=>e=>!!l.includes(e)||!!t.isAuthenticated&&w(r,e),[t.isAuthenticated,r,w]);async function C(){let e=t.token||localStorage.getItem("token");console.log(e,"user token"),await fetch("http://127.0.0.1:3001/api//api/auth/logout",{method:"POST",headers:{Authorization:`Bearer ${e}`}})}console.log(P,"permissionsData");let A=(0,i.useCallback)(async()=>{try{await C()}catch(e){console.error("登出API调用失败:",e)}finally{f({type:"user/clearUserInfo"}),f({type:"userMenus/clearMenus"}),f({type:"userPermissions/clearPermissions"}),localStorage.removeItem("userInfo"),localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("refreshToken"),sessionStorage.removeItem("token"),sessionStorage.removeItem("refresh_token"),sessionStorage.removeItem("refreshToken"),localStorage.removeItem("persist:root"),y.replace("/login")}},[f,y]);return(0,s.jsx)(u.Provider,{value:{menus:r,loading:m,checkAccess:b,logout:A},children:e})}let m=()=>{let e=(0,i.useContext)(u);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},15888:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>d});var s=r(60687),o=r(54864),a=r(19369),n=r(55964);function d({children:e}){return(0,s.jsx)(o.Kq,{store:a.M,children:(0,s.jsx)(n.Q,{loading:null,persistor:a.q,children:e})})}},19369:(e,t,r)=>{"use strict";r.d(t,{q:()=>eq,M:()=>eS});var s=r(11208),o=r(76067),a=r(65586),n=r(61792),d=r(23599),i=r(51642);let u=(0,o.Z0)({name:"auth",initialState:{token:"",isAuthenticated:!0},reducers:{setCredentials:(e,t)=>{e.token=t.payload.token,e.isAuthenticated=!0},clearCredentials:e=>{e.token=null,e.isAuthenticated=!1}}}),{setCredentials:l,clearCredentials:c}=u.actions,m=u.reducer;var p=r(77623);let g=(0,o.Z0)({name:"userMenus",initialState:{menus:[],loading:!1,error:null},reducers:{setUserMenus:(e,t)=>{e.menus=t.payload,e.loading=!1,e.error=null},setLoading:(e,t)=>{e.loading=t.payload},setError:(e,t)=>{e.error=t.payload,e.loading=!1},addMenuItem:(e,t)=>{e.menus.push(t.payload)},updateMenuItem:(e,t)=>{let r=e.menus.findIndex(e=>e.id===t.payload.id);-1!==r&&(e.menus[r]=t.payload)},removeMenuItem:(e,t)=>{e.menus=e.menus.filter(e=>e.id!==t.payload)},clearMenus:e=>{e.menus=[]}}}),{setUserMenus:h,setLoading:f,setError:y,addMenuItem:T,updateMenuItem:v,removeMenuItem:S,clearMenus:q}=g.actions,P=g.reducer;var E=r(50534);let R=(0,o.Z0)({name:"currentStudent",initialState:{studentId:""},reducers:{setCurrentStudent:(e,t)=>{e.studentId=t.payload.studentId}}}),{setCurrentStudent:w}=R.actions,b=R.reducer;var C=r(4637);let A=(0,o.Z0)({name:"notificationsUnreadCount",initialState:{count:0},reducers:{setUnreadCount:(e,t)=>{e.count=t.payload.count}}}),{setUnreadCount:I}=A.actions,k=A.reducer,G=(0,o.zD)("teacher/fetchTeachers",async(e,{dispatch:t,rejectWithValue:r})=>{try{console.log("开始获取教师列表...");let e=await t(i.Y.endpoints.getTeacherIdAndName.initiate());if("error"in e)return console.error("获取教师列表失败:",e.error),r("获取教师列表失败");let s=e.data;if(console.log("获取到的教师列表:",s),!s||!Array.isArray(s))return console.error("教师数据格式不正确:",s),r("教师数据格式不正确");return s.map(e=>({id:String(e.id),name:String(e.name)}))}catch(e){return console.error("获取教师列表失败:",e),r(e.message||"获取教师列表失败")}}),$=(0,o.Z0)({name:"teacher",initialState:{teachers:[],isLoading:!1,error:null},reducers:{clearTeachers:e=>{e.teachers=[],e.error=null}},extraReducers:e=>{e.addCase(G.pending,e=>{e.isLoading=!0,e.error=null}).addCase(G.fulfilled,(e,t)=>{e.isLoading=!1,e.teachers=t.payload,console.log("Redux state 更新后的教师列表:",e.teachers)}).addCase(G.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload||"获取教师列表失败",console.error("Redux state 更新失败:",e.error)})}}),{clearTeachers:x}=$.actions,O=$.reducer;var j=r(76320),M=r(88397),U=r(82947),N=r(85091),F=r(78166),L=r(92053),D=r(91738);let z=(0,D.w)("financeApi",["Finance"]).injectEndpoints({endpoints:e=>({getBuyingRecord:e.query({query:e=>({url:"/finance/buying-record",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Finance"]}),getTeachingRecord:e.query({query:e=>({url:"/finance/teaching-record",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Finance"]}),getSalesOverview:e.query({query:e=>({url:"/finance/sales-overview",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:null,providesTags:["Finance"]}),getSalesRepOverview:e.query({query:e=>({url:"/finance/salesrep-overview",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:null,providesTags:["Finance"]}),getStudentOverview:e.query({query:e=>({url:"/finance/student-overview",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:null,providesTags:["Finance"]}),getEducationOverview:e.query({query:e=>({url:"/finance/education-overview",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:null,providesTags:["Finance"]}),getStudentFees:e.query({query:e=>({url:"/finance/student-fees",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Finance"]}),getStudentClassSummary:e.query({query:e=>({url:"/finance/student-class-summary",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Finance"]}),getRefundRecord:e.query({query:e=>({url:"/finance/refund-records",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Finance"]})})}),{useGetBuyingRecordQuery:Z,useGetTeachingRecordQuery:B,useGetSalesOverviewQuery:W,useGetSalesRepOverviewQuery:Q,useGetStudentOverviewQuery:_,useGetEducationOverviewQuery:H,useGetStudentFeesQuery:Y,useGetStudentClassSummaryQuery:K,useGetRefundRecordQuery:X}=z,V=(0,D.w)("billsApi",["Bills"]).injectEndpoints({endpoints:e=>({createBill:e.mutation({query:e=>({url:"/bills",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"创建账单失败")},invalidatesTags:["Bills"]}),getBills:e.query({query:e=>({url:"/bills",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Bills"]}),getBillChartData:e.query({query:e=>({url:"/bills/annual-data",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:null,providesTags:["Bills"]})})}),{useCreateBillMutation:J,useGetBillsQuery:ee,useGetBillChartDataQuery:et}=V,er=(0,D.w)("staffApi",["Staff","StaffRoles"]).injectEndpoints({endpoints:e=>({getStaff:e.query({query:e=>({url:"/staff",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Staff"]}),resetStaffPassword:e.mutation({query:e=>({url:`/staff/${e}/resetPassword`,method:"POST"}),invalidatesTags:["Staff"]}),updateStaffWorkingStatus:e.mutation({query:e=>({url:`/staff/${e}/workingStatus`,method:"PUT"}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"操作失败")},invalidatesTags:["Staff"]}),createStaff:e.mutation({query:e=>({url:"/staff",method:"POST",body:e}),invalidatesTags:["Staff"]}),getStaffRoleList:e.query({query:e=>({url:"/roles",method:"GET",params:e}),transformResponse:e=>(console.log(e,"11111111111111111111"),200===e.code&&e.data)?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["StaffRoles"]}),createStaffRole:e.mutation({query:e=>({url:"/roles",method:"POST",body:e}),invalidatesTags:["StaffRoles"]}),updateStaffRole:e.mutation({query:({roleId:e,data:t})=>({url:`/roles/${e}`,method:"PUT",body:t}),invalidatesTags:["StaffRoles"]}),deleteStaffRole:e.mutation({query:e=>({url:`/roles/${e}`,method:"DELETE"}),invalidatesTags:["StaffRoles"]}),updateStaffInfo:e.mutation({query:({staffId:e,data:t})=>({url:`/staff/${e}`,method:"PUT",body:t}),invalidatesTags:["Staff"]}),updateStaffRolePermission:e.mutation({query:({roleId:e,data:t})=>({url:`/roles/${e}/permissions`,method:"PUT",body:t}),invalidatesTags:["Staff"]}),uploadStaffAvatar:e.mutation({query:e=>({url:"/staff/avatar",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"上传头像失败")},invalidatesTags:["Staff"]})})}),{useGetStaffQuery:es,useResetStaffPasswordMutation:eo,useUpdateStaffWorkingStatusMutation:ea,useCreateStaffMutation:en,useGetStaffRoleListQuery:ed,useCreateStaffRoleMutation:ei,useUpdateStaffRoleMutation:eu,useDeleteStaffRoleMutation:el,useUpdateStaffInfoMutation:ec,useUpdateStaffRolePermissionMutation:em,useUploadStaffAvatarMutation:ep}=er;var eg=r(41565),eh=r(41169),ef=r(58876);let ey={key:"root",storage:d.A,whitelist:["user","userMenus","userPermissions"]},eT=(0,s.HY)({[j.W4.reducerPath]:j.W4.reducer,[i.Y.reducerPath]:i.Y.reducer,[C.v.reducerPath]:C.v.reducer,[M.ZM.reducerPath]:M.ZM.reducer,[U.hh.reducerPath]:U.hh.reducer,[N.ZZ.reducerPath]:N.ZZ.reducer,[F.O.reducerPath]:F.O.reducer,[L.ZN.reducerPath]:L.ZN.reducer,[z.reducerPath]:z.reducer,[V.reducerPath]:V.reducer,[eh.d.reducerPath]:eh.d.reducer,[er.reducerPath]:er.reducer,[eg.pQ.reducerPath]:eg.pQ.reducer,[ef.OT.reducerPath]:ef.OT.reducer,auth:m,user:p.Ay,userMenus:P,userPermissions:E.Ay,currentStudent:b,notificationsUnreadCount:k,teacher:O}),ev=(0,n.rL)(ey,(e,t)=>"user/clearUserInfo"===t.type?(localStorage.removeItem("persist:root"),eT(void 0,t)):eT(e,t)),eS=(0,o.U1)({reducer:ev,middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE"]}}).concat(j.W4.middleware,i.Y.middleware,C.v.middleware,M.ZM.middleware,U.hh.middleware,N.ZZ.middleware,F.O.middleware,L.ZN.middleware,z.middleware,V.middleware,er.middleware,eg.pQ.middleware,eh.d.middleware,ef.OT.middleware),devTools:!1}),eq=(0,n.GM)(eS);(0,a.$k)(eS.dispatch)},30596:(e,t,r)=>{"use strict";r.d(t,{G:()=>a,j:()=>o});var s=r(54864);let o=()=>(0,s.wA)(),a=s.d4},33270:(e,t,r)=>{Promise.resolve().then(r.bind(r,15888)),Promise.resolve().then(r.bind(r,64616)),Promise.resolve().then(r.bind(r,7112)),Promise.resolve().then(r.bind(r,72119))},33427:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},41169:(e,t,r)=>{"use strict";r.d(t,{H:()=>o,d:()=>s});let s=(0,r(91738).w)("usersApi",["Users"]).injectEndpoints({endpoints:e=>({getCurrentPermissions:e.query({query:e=>({url:"/user/current-permissions",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data.permissionCodes:[],providesTags:["Users"]})})}),{useGetCurrentPermissionsQuery:o}=s},41565:(e,t,r)=>{"use strict";r.d(t,{DQ:()=>a,pQ:()=>s});let s=(0,r(91738).w)("menusApi",["Menus"]).injectEndpoints({endpoints:e=>({getAllMenus:e.query({query:()=>({url:"/menus",method:"GET"}),transformResponse:e=>200===e.code?e.data:[],providesTags:["Menus"]}),getUserMenus:e.query({query:()=>({url:"/menus/user-menus",method:"GET"}),transformResponse:e=>200===e.code?e.data:[],providesTags:["Menus"]}),getRoleMenus:e.query({query:e=>({url:`/menus/role-menus/${e}`,method:"GET"}),transformResponse:e=>200===e.code?e.data:[],providesTags:["Menus"]})})}),{useGetAllMenusQuery:o,useGetUserMenusQuery:a,useGetRoleMenusQuery:n}=s},47273:(e,t,r)=>{"use strict";r.d(t,{WebSocketProvider:()=>o});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call WebSocketProvider() from the server but WebSocketProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\contexts\\websocket.tsx","WebSocketProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useWebSocket() from the server but useWebSocket is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\contexts\\websocket.tsx","useWebSocket")},48150:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\components\\providers.tsx","Providers")},48482:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\components\\ui\\sonner.tsx","Toaster")},50534:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n,jb:()=>o});let s=(0,r(76067).Z0)({name:"userPermissions",initialState:{permissions:[]},reducers:{setPermissions:(e,t)=>{e.permissions=t.payload.permissions},clearPermissions:e=>{e.permissions=[]}}}),{setPermissions:o,clearPermissions:a}=s.actions,n=s.reducer},51642:(e,t,r)=>{"use strict";r.d(t,{X:()=>o,Y:()=>s});let s=(0,r(91738).w)("getTeacherIdAndNameApi",["getTeacherIdAndName"]).injectEndpoints({endpoints:e=>({getTeacherIdAndName:e.query({query:()=>"/institutions/teachers/select",transformResponse:e=>200===e.code&&e.data?e.data:[],providesTags:["getTeacherIdAndName"]})})}),{useGetTeacherIdAndNameQuery:o}=s},52023:(e,t,r)=>{"use strict";r.d(t,{B:()=>c});var s=r(51060),o=r(19369),a=r(77623);let n=s.A.create({baseURL:"http://127.0.0.1:3001",timeout:1e4,headers:{"Content-Type":"application/json"}}),d=!1,i=[],u=e=>{i.forEach(t=>t(e)),i=[]},l=e=>{i.push(e)},c=async()=>{try{if(d)return new Promise(e=>{l(t=>{e(t)})});d=!0;let e=localStorage.getItem("refresh_token")||localStorage.getItem("refreshToken");if(!e)return console.error("刷新令牌不存在"),d=!1,null;let{accessToken:t,refreshToken:r}=(await n.post("/api/auth/refresh-token",null,{headers:{Authorization:`Bearer ${e}`}})).data;localStorage.setItem("token",t),localStorage.setItem("refresh_token",r),localStorage.setItem("refreshToken",r);let s=o.M.getState().user;return o.M.dispatch((0,a.iA)({...s,token:t,refresh_token:r,isAuthenticated:!0})),u(t),d=!1,t}catch(e){return console.error("刷新令牌失败:",e),d=!1,localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("refreshToken"),null}}},58876:(e,t,r)=>{"use strict";r.d(t,{D3:()=>o,Fj:()=>i,Gw:()=>n,OT:()=>s,_R:()=>d,eT:()=>u,gG:()=>a});let s=(0,r(91738).w)("notificationApi",["Notification"]).injectEndpoints({endpoints:e=>({getNotificationList:e.query({query:e=>({url:"/notifications",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:(console.log(e,"11111111111111111111"),{list:[],total:0,page:1,pageSize:10}),transformErrorResponse:(e,t,r)=>(console.log(e,t,r),e.message||"获取通知列表失败"),providesTags:["Notification"]}),getNotificationDetail:e.query({query:e=>`/notifications/${e}`,transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"获取通知详情失败")},providesTags:(e,t,r)=>[{type:"Notification",id:r}]}),createNotification:e.mutation({query:e=>({url:"/notifications",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"创建通知失败")},invalidatesTags:["Notification"]}),markAsRead:e.mutation({query:e=>({url:"/notifications/read",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"标记为已读失败")},invalidatesTags:["Notification"]}),markAllAsRead:e.mutation({query:()=>({url:"/notifications/readAll",method:"POST"}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"标记为全部已读失败")},invalidatesTags:["Notification"]}),deleteNotifications:e.mutation({query:e=>({url:"/notifications/delete",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"批量删除失败")},invalidatesTags:["Notification"]})})}),{useGetNotificationListQuery:o,useGetNotificationDetailQuery:a,useCreateNotificationMutation:n,useMarkAsReadMutation:d,useMarkAllAsReadMutation:i,useDeleteNotificationsMutation:u}=s},61135:()=>{},64616:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>n});var s=r(60687),o=r(10218),a=r(52581);let n=({...e})=>{let{theme:t="system"}=(0,o.D)();return(0,s.jsx)(a.l$,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})}},70379:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},71858:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\contexts\\auth-context.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\contexts\\auth-context.tsx","useAuth")},72119:(e,t,r)=>{"use strict";r.d(t,{WebSocketProvider:()=>i});var s=r(60687),o=r(7112),a=r(30596),n=r(43210);let d=(0,n.createContext)(null);function i({children:e}){(0,a.G)(e=>e.user);let{logout:t}=(0,o.A)(),[r,i]=(0,n.useState)(0),[u,l]=(0,n.useState)(null);return(0,a.j)(),(0,a.G)(e=>e.notificationsUnreadCount.count),(0,s.jsx)(d.Provider,{value:u,children:e})}},76320:(e,t,r)=>{"use strict";r.d(t,{FQ:()=>o,L5:()=>u,W4:()=>s,nu:()=>n});let s=(0,r(91738).w)("studentApi",["Student","StudentProduct","FollowRecord","Attendance"]).injectEndpoints({endpoints:e=>({getStudentList:e.query({query:e=>({url:"/students",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Student"]}),deleteStudents:e.mutation({query:e=>({url:"/students",method:"DELETE",body:{studentIds:e}}),invalidatesTags:["Student"]}),addStudentFollower:e.mutation({query:e=>({url:"/students/follow-up",method:"POST",body:e}),invalidatesTags:["Student"]}),createStudent:e.mutation({query:e=>({url:"/students/add",method:"POST",body:e}),invalidatesTags:["Student"]}),createProductForStudent:e.mutation({query:({studentId:e,productData:t})=>({url:`/students/${e}/products`,method:"POST",body:t}),invalidatesTags:["StudentProduct"]}),getStudentSelectList:e.query({query:({page:e=1,pageSize:t=10,search:r=""})=>({url:"/students/simple",method:"GET",params:{page:e,pageSize:t,search:r}}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Student"]}),getStudentInfo:e.query({query:e=>({url:`/students/${e}`,method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员信息失败")},providesTags:(e,t,r)=>[{type:"Student",id:r}]}),getStudentProducts:e.query({query:e=>({url:`/students/${e}/products`,method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["StudentProduct"]}),getStudentProductPurchaseRecord:e.query({query:e=>({url:`/students/${e}/products/records`,method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["StudentProduct"]}),getStudentAttendanceRecord:e.query({query:({studentId:e,...t})=>({url:`/students/${e}/attendance`,method:"GET",params:t}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["Attendance"]}),getStudentClasses:e.query({query:e=>({url:`/students/${e}/classes`,method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["Student"]}),getStudentProduct:e.query({query:e=>({url:"/students/products",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["StudentProduct"]}),getStudentProductDownload:e.query({query:e=>({url:"/students/products/download",method:"GET",params:e})}),adjustStudentProduct:e.mutation({query:({studentId:e,studentProductId:t,data:r})=>({url:`/students/${e}/products/${t}`,method:"PUT",body:r}),invalidatesTags:["StudentProduct"]}),getIntentionStudent:e.query({query:e=>({url:"/students/intent",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["Student"]}),addStudentFollowRecord:e.mutation({query:({studentId:e,data:t})=>({url:`/students/${e}/followRecords`,method:"POST",body:t}),invalidatesTags:["FollowRecord"]}),getStudentFollowRecord:e.query({query:e=>({url:`/students/${e}/followRecords`,method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["FollowRecord"]}),updateStudentInfo:e.mutation({query:({studentId:e,data:t})=>({url:`/students/${e}`,method:"PUT",body:t}),invalidatesTags:["Student"]}),getStudentClassRecord:e.query({query:({studentId:e,...t})=>({url:`/students/${e}/classesHistory`,method:"GET",params:t}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["Student"]}),getAttendanceRecord:e.query({query:e=>({url:"/students/attendanceRecords",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["Attendance"]}),getFollowRecords:e.query({query:e=>({url:"/students/followRecords",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["FollowRecord"]}),getProductAdjustRecord:e.query({query:e=>({url:"/students/products-adjustments",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["StudentProduct"]}),refundStudentProduct:e.mutation({query:({studentId:e,studentProductId:t,data:r})=>({url:`/students/${e}/products/${t}/refund`,method:"POST",body:r}),invalidatesTags:["StudentProduct"]}),getStudentClassRecordQuery:e.query({query:e=>({url:"/students/classes-query",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["Student"]})})}),{useGetStudentListQuery:o,useDeleteStudentsMutation:a,useAddStudentFollowerMutation:n,useCreateStudentMutation:d,useCreateProductForStudentMutation:i,useGetStudentSelectListQuery:u,useGetStudentInfoQuery:l,useGetStudentProductsQuery:c,useGetStudentProductPurchaseRecordQuery:m,useGetStudentAttendanceRecordQuery:p,useGetStudentClassesQuery:g,useGetStudentProductQuery:h,useGetStudentProductDownloadQuery:f,useAdjustStudentProductMutation:y,useGetIntentionStudentQuery:T,useAddStudentFollowRecordMutation:v,useGetStudentFollowRecordQuery:S,useUpdateStudentInfoMutation:q,useGetStudentClassRecordQuery:P,useGetAttendanceRecordQuery:E,useGetFollowRecordsQuery:R,useGetProductAdjustRecordQuery:w,useRefundStudentProductMutation:b,useGetStudentClassRecordQueryQuery:C}=s},77623:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n,Hf:()=>a,iA:()=>o});let s=(0,r(76067).Z0)({name:"user",initialState:{id:"",name:"",account:"",avatar:void 0,institutionName:"",institutionLogo:"",institutionSubjectName:"",token:"",refresh_token:"",isAuthenticated:!1},reducers:{setUserInfo:(e,t)=>{e.id=t.payload.id,e.name=t.payload.name,e.account=t.payload.account,e.institutionName=t.payload.institutionName,e.institutionLogo=t.payload.institutionLogo,e.institutionSubjectName=t.payload.institutionSubjectName,e.token=t.payload.token,e.refresh_token=t.payload.refresh_token,e.isAuthenticated=!0},clearUserInfo:e=>{e.id="",e.name="",e.account="",e.institutionName="",e.institutionLogo="",e.institutionSubjectName="",e.token="",e.refresh_token="",e.isAuthenticated=!1}}}),{setUserInfo:o,clearUserInfo:a}=s.actions,n=s.reducer},78166:(e,t,r)=>{"use strict";r.d(t,{O:()=>s,d:()=>o});let s=(0,r(91738).w)("publicApi",["Public"]).injectEndpoints({endpoints:e=>({uploadImage:e.mutation({query:e=>({url:"/public/upload/image",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"上传图片失败")}})})}),{useUploadImageMutation:o}=s},82947:(e,t,r)=>{"use strict";r.d(t,{AA:()=>n,VD:()=>i,hh:()=>s,p5:()=>o,v5:()=>d,zo:()=>a});let s=(0,r(91738).w)("coursesApi",["Courses"]).injectEndpoints({endpoints:e=>({getCourseList:e.query({query:e=>({url:"/courses",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Courses"]}),getCourseSelectList:e.query({query:()=>({url:"/courses/select",method:"GET"}),transformResponse:e=>200===e.code&&e.data?e.data:[],providesTags:["Courses"]}),createCourse:e.mutation({query:e=>({url:"/courses",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"创建课程失败")},invalidatesTags:["Courses"]}),deleteCourse:e.mutation({query:e=>({url:`/courses/${e}`,method:"DELETE"}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"删除课程失败")},invalidatesTags:["Courses"]}),updateCourse:e.mutation({query:({courseId:e,course:t})=>({url:`/courses/${e}`,method:"PUT",body:t}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"更新课程失败")},invalidatesTags:["Courses"]})})}),{useGetCourseListQuery:o,useGetCourseSelectListQuery:a,useCreateCourseMutation:n,useDeleteCourseMutation:d,useUpdateCourseMutation:i}=s},84778:(e,t,r)=>{"use strict";r.d(t,{l:()=>o});var s=r(52581);let o={success:(e,t)=>{(0,s.oR)(e,{description:t,icon:"✅"})},error:(e,t)=>{(0,s.oR)(e,{description:t,icon:"❌"})},info:(e,t)=>{(0,s.oR)(e,{description:t,icon:"ℹ️"})},warning:(e,t)=>{(0,s.oR)(e,{description:t,icon:"⚠️"})}}},85091:(e,t,r)=>{"use strict";r.d(t,{KN:()=>l,YC:()=>m,ZZ:()=>s,aP:()=>a,hP:()=>c,lN:()=>h,uL:()=>o});let s=(0,r(91738).w)("institutionApi",["Institution","InstitutionAddress","InstitutionClassroom"]).injectEndpoints({endpoints:e=>({getInstitutionInfo:e.query({query:()=>({url:"/institutions/info",method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取机构信息失败")},providesTags:["Institution"]}),updateInstitutionInfo:e.mutation({query:e=>({url:"/institutions/info",method:"PUT",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"更新机构信息失败")},invalidatesTags:["Institution"]}),getInstitutionAddress:e.query({query:()=>({url:"/institutions/address",method:"GET"}),transformResponse:e=>200===e.code&&e.data?e.data:[],providesTags:["InstitutionAddress"]}),createInstitutionAddress:e.mutation({query:e=>({url:"/institutions/address",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"创建机构地址失败")},invalidatesTags:["InstitutionAddress"]}),updateInstitutionAddress:e.mutation({query:({addressId:e,data:t})=>({url:`/institutions/address/${e}`,method:"PUT",body:t}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"更新机构地址失败")},invalidatesTags:["InstitutionAddress"]}),deleteInstitutionAddress:e.mutation({query:e=>({url:`/institutions/address/${e}`,method:"DELETE"}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"删除机构地址失败")},invalidatesTags:["InstitutionAddress"]}),getInstitutionClassrooms:e.query({query:({search:e="",page:t=1,pageSize:r=10})=>({url:"/institutions/classrooms",method:"GET",params:{search:e,page:t,pageSize:r}}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["InstitutionClassroom"]}),getInstitutionClassroomOptions:e.query({query:()=>({url:"/institutions/classrooms/select",method:"GET"}),transformResponse:e=>200===e.code&&e.data?e.data:[],providesTags:["InstitutionClassroom"]}),createInstitutionClassroom:e.mutation({query:e=>({url:"/institutions/classrooms",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"创建机构教室失败")},invalidatesTags:["InstitutionClassroom"]}),updateInstitutionClassroom:e.mutation({query:({classroomId:e,data:t})=>({url:`/institutions/classrooms/${e}`,method:"PUT",body:t}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"更新机构教室失败")},invalidatesTags:["InstitutionClassroom"]}),deleteInstitutionClassroom:e.mutation({query:e=>({url:`/institutions/classrooms/${e}`,method:"DELETE"}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"删除机构教室失败")},invalidatesTags:["InstitutionClassroom"]}),getInstitutionLogs:e.query({query:e=>({url:"/institutions/logs",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10}})})}),{useGetInstitutionInfoQuery:o,useUpdateInstitutionInfoMutation:a,useGetInstitutionAddressQuery:n,useCreateInstitutionAddressMutation:d,useUpdateInstitutionAddressMutation:i,useDeleteInstitutionAddressMutation:u,useGetInstitutionClassroomsQuery:l,useGetInstitutionClassroomOptionsQuery:c,useCreateInstitutionClassroomMutation:m,useUpdateInstitutionClassroomMutation:p,useDeleteInstitutionClassroomMutation:g,useGetInstitutionLogsQuery:h}=s},88397:(e,t,r)=>{"use strict";r.d(t,{BU:()=>S,D_:()=>i,Kp:()=>n,OU:()=>T,Xd:()=>l,ZM:()=>s,Zj:()=>o,b:()=>y,e6:()=>a,eZ:()=>f,rM:()=>m,rr:()=>p});let s=(0,r(91738).w)("classesApi",["Classes","ClassSchedule","ClassesDetail"]).injectEndpoints({endpoints:e=>({createClasses:e.mutation({query:e=>({url:"/classes",method:"POST",body:e}),invalidatesTags:["Classes"]}),getClassesList:e.query({query:({page:e=1,teacherId:t="",pageSize:r=10,name:s=""})=>({url:"/classes",method:"GET",params:{page:e,pageSize:r,name:s,teacherId:t}}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Classes"]}),getTeacherFreeTime:e.mutation({query:e=>({url:"/classes/check-teacher-free",method:"POST",body:e}),transformResponse:e=>200===e.code&&e.data?e.data:[]}),postAppendOrCopyClassesSchedule:e.mutation({query:({classesId:e,data:t})=>({url:`/classes/append-or-copy-schedule/${e}`,method:"POST",body:t}),transformResponse:e=>200===e.code&&e.data?e.data:[]}),getClassesSelect:e.query({query:({page:e=1,pageSize:t=10,name:r=""})=>({url:"/classes/select",method:"GET",params:{page:e,pageSize:t,name:r}}),providesTags:["Classes"]}),getClassesByIdDetail:e.query({query:e=>({url:`/classes/${e}`,method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取班级详情失败")},providesTags:["ClassesDetail"]}),addStudentToClass:e.mutation({query:({classId:e,studentIds:t})=>({url:`/classes/${e}/students`,method:"POST",body:{studentIds:t}}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"添加学生到班级失败")},invalidatesTags:["ClassesDetail"]}),deleteStudentFromClass:e.mutation({query:({classId:e,studentId:t})=>({url:`/classes/${e}/students/${t}`,method:"DELETE"}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"从班级删除学生失败")},invalidatesTags:["ClassesDetail"]}),updateClasses:e.mutation({query:({classesId:e,data:t})=>({url:`/classes/${e}`,method:"PUT",body:t}),invalidatesTags:["ClassesDetail"]}),getClassesSchedule:e.query({query:e=>({url:"/classes/all/schedules",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取班级课表失败")},providesTags:["ClassSchedule"]}),getClassesSchedulesDetail:e.query({query:e=>({url:`/classes/schedules/${e}`,method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取班级计划详情失败")},providesTags:["ClassSchedule"]}),updateClassesSchedules:e.mutation({query:({scheduleId:e,data:t})=>({url:`/classes/schedules/${e}`,method:"PUT",body:t}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"更新班级计划失败")},invalidatesTags:["ClassSchedule"]}),updateClassesStudentAttendance:e.mutation({query:({id:e,data:t})=>({url:`/classes/schedules/${e}/studentAttendance`,method:"PUT",body:t}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"更新班级学生考勤失败")},invalidatesTags:["ClassSchedule"]}),addStudentToClassSchedule:e.mutation({query:({scheduleId:e,data:t})=>({url:`/classes/schedules/${e}/students`,method:"POST",body:t}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"添加学生到班级计划失败")},invalidatesTags:["ClassSchedule"]}),deleteStudentFromClassSchedule:e.mutation({query:({scheduleId:e,studentIds:t})=>({url:`/classes/schedules/${e}/students`,method:"DELETE",body:{studentIds:t}}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"从班级计划删除学生失败")},invalidatesTags:["ClassSchedule"]}),createClassesSchedules:e.mutation({query:e=>({url:"/classes/temp-schedule",method:"POST",body:e}),invalidatesTags:["ClassSchedule"]}),deleteClasses:e.mutation({query:e=>({url:`/classes/${e}`,method:"delete"}),invalidatesTags:["Classes"]})})}),{useCreateClassesMutation:o,useGetClassesListQuery:a,useGetTeacherFreeTimeMutation:n,useGetClassesSelectQuery:d,useGetClassesByIdDetailQuery:i,useAddStudentToClassMutation:u,useDeleteStudentFromClassMutation:l,useUpdateClassesMutation:c,useGetClassesScheduleQuery:m,useGetClassesSchedulesDetailQuery:p,useUpdateClassesSchedulesMutation:g,usePostAppendOrCopyClassesScheduleMutation:h,useUpdateClassesStudentAttendanceMutation:f,useAddStudentToClassScheduleMutation:y,useDeleteStudentFromClassScheduleMutation:T,useDeleteClassesMutation:v,useCreateClassesSchedulesMutation:S}=s},91738:(e,t,r)=>{"use strict";r.d(t,{w:()=>m});var s=r(65586),o=r(20340),a=r(52023),n=r(77623),d=r(19369),i=r(84778);let u={count:0,maxRetries:3,reset(){this.count=0},increment(){return this.count+=1,this.count},exceedsMaxRetries(){return this.count>=this.maxRetries}},l=(0,s.cw)({baseUrl:"http://127.0.0.1:3001/api/",prepareHeaders:(e,{getState:t})=>{let r=t().user?.token;return r&&e.set("authorization",`Bearer ${r}`),e}}),c=async(e,t,r)=>{let s=await l(e,t,r);if(console.log(s,"result base api."),s.error){let o=s.error.status,c=s.error.data;if(401===o||c?.code===401){console.log("Token 已过期，尝试刷新...");let o=u.increment();if(console.log(`Token 错误计数: ${o}/${u.maxRetries}`),u.exceedsMaxRetries())return console.log("Token 错误次数超过最大限制，退出登录"),i.l.error("登录已过期","请重新登录"),d.M.dispatch((0,n.Hf)()),localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("refreshToken"),setTimeout(()=>{window.location.href="/login"},1500),s;try{await (0,a.B)()?(console.log("Token 刷新成功，重试请求"),u.reset(),s=await l(e,t,r)):(console.error("Token 刷新失败，无法获取新token"),i.l.error("登录已过期","请重新登录"),d.M.dispatch((0,n.Hf)()),setTimeout(()=>{window.location.href="/login"},1500))}catch(e){console.error("Token 刷新过程中发生错误:",e),i.l.error("登录已过期","请重新登录"),d.M.dispatch((0,n.Hf)()),setTimeout(()=>{window.location.href="/login"},1500)}}else u.reset()}else u.reset();return s},m=(e,t=[])=>(0,o.xP)({reducerPath:e,keepUnusedDataFor:60,baseQuery:c,tagTypes:t,endpoints:()=>({})})},92053:(e,t,r)=>{"use strict";r.d(t,{Kc:()=>o,Q$:()=>n,Qq:()=>u,RC:()=>l,ZN:()=>s,lY:()=>d,vM:()=>i});let s=(0,r(91738).w)("productsApi",["Products","ProductCourses"]).injectEndpoints({endpoints:e=>({getProducts:e.query({query:e=>({url:"/products",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Products"]}),getAllProducts:e.query({query:()=>({url:"/products/all",method:"GET"}),transformResponse:e=>200===e.code&&e.data?e.data:[],providesTags:["Products"]}),createProduct:e.mutation({query:e=>({url:"/products",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"创建产品失败")},invalidatesTags:["Products"]}),deleteProduct:e.mutation({query:e=>({url:`/products/${e}`,method:"DELETE"}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"删除产品失败")},invalidatesTags:["Products"]}),updateProduct:e.mutation({query:({id:e,product:t})=>({url:`/products/${e}`,method:"PUT",body:t}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"更新产品失败")},invalidatesTags:["Products"]}),getProductsCoursesById:e.query({query:e=>({url:`/products/${e}/courses`,method:"GET"}),transformResponse:e=>200===e.code&&e.data?e.data:[],providesTags:["ProductCourses"]}),updateCoursesByProductId:e.mutation({query:({productId:e,coursesData:t})=>({url:`/products/${e}/courses`,method:"PUT",body:t}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"更新产品课程失败")},invalidatesTags:["ProductCourses"]})})}),{useGetProductsQuery:o,useGetAllProductsQuery:a,useCreateProductMutation:n,useDeleteProductMutation:d,useUpdateProductMutation:i,useGetProductsCoursesByIdQuery:u,useUpdateCoursesByProductIdMutation:l}=s},93518:(e,t,r)=>{Promise.resolve().then(r.bind(r,48150)),Promise.resolve().then(r.bind(r,48482)),Promise.resolve().then(r.bind(r,71858)),Promise.resolve().then(r.bind(r,47273))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>i});var s=r(37413);r(61135);var o=r(48482),a=r(48150),n=r(71858),d=r(47273);let i={title:"蜜卡",description:"CardMees Application"};function u({children:e}){return(0,s.jsx)("html",{lang:"zh-CN",children:(0,s.jsxs)("body",{className:"bg-gray-100 dark:bg-gray-900 transition-colors duration-200",children:[(0,s.jsx)(a.Providers,{children:(0,s.jsx)(n.AuthProvider,{children:(0,s.jsx)(d.WebSocketProvider,{children:e})})}),(0,s.jsx)(o.Toaster,{})]})})}}};