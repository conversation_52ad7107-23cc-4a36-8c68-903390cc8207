(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7516],{17019:(e,a,n)=>{Promise.resolve().then(n.bind(n,63791))},63791:(e,a,n)=>{"use strict";n.d(a,{default:()=>h});var r=n(95155);n(12115);var s=n(55028),d=n(55733),i=n(91347),l=n(68056);let c=(0,s.default)(()=>Promise.all([n.e(8687),n.e(4201),n.e(8737),n.e(4582),n.e(5620),n.e(3168),n.e(9613),n.e(7945),n.e(81),n.e(9574),n.e(9624),n.e(9110),n.e(8983)]).then(n.bind(n,28983)),{loadableGenerated:{webpack:()=>[28983]},ssr:!1}),t=(0,s.default)(()=>Promise.all([n.e(8687),n.e(4201),n.e(8737),n.e(4582),n.e(5620),n.e(3168),n.e(9613),n.e(7945),n.e(81),n.e(5992),n.e(9624),n.e(9110),n.e(5256)]).then(n.bind(n,15256)),{loadableGenerated:{webpack:()=>[15256]},ssr:!1});function h(){var e;let{hasPermission:a}=(0,l.J)()||{hasPermission:()=>!1},n=[{id:"purchaseRecord",key:"purchaseRecord",label:"购买记录",content:(0,r.jsx)(i.LQ,{permission:"finance:purchase:read",children:(0,r.jsx)(c,{})}),hidden:!a("finance:purchase:read")},{id:"refundRecord",key:"refundRecord",label:"退款记录",content:(0,r.jsx)(i.LQ,{permission:"finance:refund:read",children:(0,r.jsx)(t,{})}),hidden:!a("finance:refund:read")}].filter(e=>!e.hidden);return 0===n.length?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("p",{className:"text-gray-500",children:"您没有查看收据管理的权限"})}):(0,r.jsx)("div",{className:"space-y-4 p-4",children:(0,r.jsx)(d.Q,{defaultTab:(null===(e=n[0])||void 0===e?void 0:e.key)||"purchaseRecord",tabs:n,variant:"underline"})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,4540,6639,6315,7358],()=>a(17019)),_N_E=e.O()}]);