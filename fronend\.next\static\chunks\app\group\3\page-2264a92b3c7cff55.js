(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6172],{5196:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(19946).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},12396:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>M});var l=s(95155),a=s(12115),r=s(56904);function i(e){let{prizes:t}=e;return(0,l.jsxs)("div",{className:"mx-3 my-3 p-2 bg-white rounded-lg shadow-sm",children:[(0,l.jsx)("h3",{className:"text-center font-medium text-gray-800 mb-2",children:"奖品一览"}),(0,l.jsx)("div",{className:"grid grid-cols-3 gap-2",children:t.map(e=>(0,l.jsxs)("div",{className:"flex flex-col items-center",children:[(0,l.jsxs)("div",{className:"relative w-20 h-20 overflow-hidden rounded-lg",children:[(0,l.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-cover"}),(0,l.jsx)("div",{className:"absolute inset-x-0 bottom-0 bg-black bg-opacity-60 py-1",children:(0,l.jsx)("p",{className:"text-xs text-center text-white",children:e.name})})]}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-1 text-center",children:e.description}),(0,l.jsx)("p",{className:"text-xs text-red-500 font-medium",children:e.value})]},e.id))})]})}var n=s(58832),c=s(19946);let d=(0,c.A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);function o(e){let{prizes:t,spinning:s,onSpinEnd:r,onSpin:i,selectedPrize:c}=e,o=(0,a.useRef)(null),[m,x]=(0,a.useState)(0),h=(0,a.useRef)([]);return(0,a.useEffect)(()=>{if(!o.current)return;let e=o.current,s=e.getContext("2d");if(!s)return;let l=e.width/2,a=e.height/2,r=["#FF5252","#FF7043","#FFCA28","#66BB6A","#29B6F6","#7E57C2","#EC407A","#F44336","#26C6DA"];s.clearRect(0,0,e.width,e.height),s.beginPath(),s.arc(l,a,150,0,2*Math.PI),s.fillStyle="#FFC107",s.fill();let i=t.reduce((e,t)=>e+t.probability,0);h.current=[];let n=0;t.forEach((e,t)=>{let c=2*Math.PI*(e.probability/i),d=n+c;h.current.push({startAngle:n,endAngle:d,prize:e}),s.beginPath(),s.moveTo(l,a),s.arc(l,a,145,n,d),s.closePath(),s.fillStyle=r[t%r.length],s.fill(),s.beginPath(),s.moveTo(l,a),s.lineTo(l+145*Math.cos(n),a+145*Math.sin(n)),s.strokeStyle="rgba(255, 255, 255, 0.8)",s.lineWidth=2,s.stroke(),s.save(),s.translate(l,a);let o=n+c/2,m=180/Math.PI*c,x=13,f=e.name,u=94.25;m<45&&(x=11),m<25&&(x=9,u=87),m<15&&(x=8,u=72.5,f.length>3&&(f=f.substring(0,3))),s.rotate(o),o>Math.PI/2&&o<3*Math.PI/2?(s.rotate(Math.PI),s.textAlign="center",s.textBaseline="middle",s.fillStyle="#FFF",s.font="bold ".concat(x,"px Arial"),s.strokeStyle="rgba(0, 0, 0, 0.7)",s.lineWidth=2,s.strokeText(f,-u,0),s.fillText(f,-u,0)):(s.textAlign="center",s.textBaseline="middle",s.fillStyle="#FFF",s.font="bold ".concat(x,"px Arial"),s.strokeStyle="rgba(0, 0, 0, 0.7)",s.lineWidth=2,s.strokeText(f,u,0),s.fillText(f,u,0)),s.restore(),n=d})},[t,m]),(0,a.useEffect)(()=>{let e;let t=10,l=m;if(s&&c){let e=h.current.find(e=>e.prize.id===c.id);l=e?360*Math.floor(m/360)+(2*Math.PI-(e.startAngle+e.endAngle)/2)*180/Math.PI%360+1800:m+1800+Math.floor(360*Math.random())}else s&&(l=m+1800+Math.floor(360*Math.random()));let a=m,i=()=>{s&&a<l?(l-a<360&&(t=Math.max(.5,.985*t)),x(a+=t),e=requestAnimationFrame(i)):s&&a>=l&&r()};return s&&(e=requestAnimationFrame(i)),()=>{e&&cancelAnimationFrame(e)}},[s,c,m,r]),(0,l.jsxs)("div",{className:"mx-3 my-6 flex flex-col items-center",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 z-10 drop-shadow-lg",children:(0,l.jsx)("div",{className:"w-8 h-12 relative",children:(0,l.jsx)(n.A,{size:36,className:"text-red-600 filter drop-shadow-md"})})}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"relative flex items-center justify-center",style:{transform:"rotate(".concat(m,"deg)"),transition:s?"none":"transform 0.5s ease-out"},children:(0,l.jsx)("canvas",{ref:o,width:310,height:310,className:"rounded-full shadow-lg"})}),(0,l.jsx)("div",{className:"absolute inset-0 flex items-center justify-center z-10",onClick:()=>{!s&&i&&i()},children:(0,l.jsxs)("div",{className:"rounded-full flex items-center justify-center shadow-md \n                ".concat(s?"cursor-not-allowed":"cursor-pointer hover:brightness-110 active:brightness-90","\n                bg-gradient-to-r from-yellow-500 to-red-500 border-2 border-white\n                transition-all duration-200"),style:{width:80,height:80},children:[(0,l.jsx)("span",{className:"text-white font-bold text-sm",children:s?"抽奖中":"开始"}),s&&(0,l.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,l.jsx)("div",{className:"w-full h-full rounded-full border-t-2 border-white opacity-80 animate-spin"})})]})}),(0,l.jsx)("div",{className:"absolute inset-0 rounded-full border-8 border-yellow-300 border-opacity-50 pointer-events-none"}),s&&(0,l.jsx)("div",{className:"absolute inset-0 flex items-center justify-center z-20 pointer-events-none",children:(0,l.jsxs)("div",{className:"bg-black bg-opacity-50 text-white px-4 py-2 rounded-full shadow-lg flex items-center",children:[(0,l.jsx)(d,{className:"w-4 h-4 mr-2 animate-spin"}),(0,l.jsx)("span",{children:"抽奖中..."})]})})]})]}),(0,l.jsx)("p",{className:"text-center text-sm text-gray-500 mt-4",children:s?"幸运大转盘旋转中...":"点击中心按钮或底部按钮开始抽奖"})]})}let m=(0,c.A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),x=(0,c.A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),h=(0,c.A)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);var f=s(5196),u=s(13052);function p(e){let{open:t,onClose:s,prize:a}=e;return t?(0,l.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,l.jsxs)("div",{className:"bg-white rounded-xl overflow-hidden max-w-sm w-full shadow-2xl animate-fade-in",children:[(0,l.jsxs)("div",{className:"p-5 bg-gradient-to-r from-red-500 to-orange-500 flex items-center justify-center",children:[(0,l.jsx)(m,{className:"text-yellow-200 w-6 h-6 mr-2"}),(0,l.jsx)("h2",{className:"text-center text-white text-xl font-bold",children:"恭喜您获奖"}),(0,l.jsx)(m,{className:"text-yellow-200 w-6 h-6 ml-2"})]}),(0,l.jsxs)("div",{className:"p-6 flex flex-col items-center relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-yellow-100/30 to-transparent pointer-events-none"}),(0,l.jsxs)("div",{className:"w-28 h-28 rounded-full bg-gradient-to-br from-red-50 to-orange-50 mb-4 flex items-center justify-center overflow-hidden shadow-inner relative",children:[(0,l.jsx)("img",{src:a.image,alt:a.name,className:"w-full h-full object-cover"}),(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]}),(0,l.jsxs)("div",{className:"flex items-center mb-1",children:[a.id<=2&&(0,l.jsx)(x,{className:"text-yellow-500 w-5 h-5 mr-1.5"}),(0,l.jsx)("h3",{className:"text-lg font-bold text-red-500",children:a.name})]}),(0,l.jsxs)("p",{className:"text-gray-600 mb-2 flex items-center",children:[(0,l.jsx)(h,{className:"text-gray-400 w-4 h-4 mr-1.5"}),a.description]}),(0,l.jsxs)("p",{className:"text-sm text-gray-500 mb-5",children:["价值：",a.value]}),(0,l.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg w-full mb-5 border border-gray-100 shadow-sm",children:(0,l.jsxs)("div",{className:"flex",children:[(0,l.jsx)("div",{className:"mt-0.5 mr-2 flex-shrink-0",children:(0,l.jsx)(f.A,{className:"text-green-500 w-4 h-4"})}),(0,l.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:a.id<=3?"请联系客服登记领奖信息，我们将尽快为您安排奖品发放。":'奖品已自动添加至您的账户，请在"我的奖品"中查看。'})]})}),(0,l.jsx)("div",{className:"flex justify-center w-full",children:(0,l.jsxs)("button",{onClick:s,className:"w-full py-3.5 bg-gradient-to-r from-red-500 to-orange-500 text-white font-bold rounded-lg flex items-center justify-center shadow-md hover:shadow-lg transition-shadow",children:["确定领取 ",(0,l.jsx)(u.A,{className:"w-4 h-4 ml-1"})]})})]})]})}):null}var b=s(54416);let g=(0,c.A)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]);var j=s(69074);function v(e){let{open:t,onClose:s,prizes:a}=e;return t?(0,l.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,l.jsxs)("div",{className:"bg-white rounded-xl overflow-hidden max-w-sm w-full shadow-2xl max-h-[90vh] flex flex-col",children:[(0,l.jsxs)("div",{className:"p-4 bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-between",children:[(0,l.jsxs)("h2",{className:"text-white text-lg font-bold flex items-center",children:[(0,l.jsx)(x,{className:"w-5 h-5 mr-2"}),"我的奖品"]}),(0,l.jsx)("button",{onClick:s,className:"text-white hover:bg-white/20 rounded-full p-1.5 transition-colors",children:(0,l.jsx)(b.A,{className:"w-5 h-5"})})]}),(0,l.jsx)("div",{className:"overflow-y-auto flex-1",children:a.length>0?(0,l.jsx)("div",{className:"divide-y divide-gray-100",children:a.map((e,t)=>(0,l.jsxs)("div",{className:"p-4 flex",children:[(0,l.jsx)("div",{className:"w-16 h-16 rounded-lg overflow-hidden mr-3 flex-shrink-0",children:(0,l.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-cover"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"flex items-center mb-1",children:[e.id<=3&&(0,l.jsx)("span",{className:"inline-block px-1.5 py-0.5 bg-yellow-100 text-yellow-800 text-xs rounded-sm mr-1.5",children:1===e.id?"一等奖":2===e.id?"二等奖":"三等奖"}),(0,l.jsx)("h3",{className:"font-medium text-gray-800",children:e.name})]}),(0,l.jsxs)("div",{className:"text-sm text-gray-500 flex items-center mb-1",children:[(0,l.jsx)(g,{className:"w-3.5 h-3.5 mr-1"}),e.description," \xb7 价值",e.value]}),(0,l.jsxs)("div",{className:"text-xs text-gray-400 flex items-center",children:[(0,l.jsx)(j.A,{className:"w-3.5 h-3.5 mr-1"}),"中奖时间: ",e.winTime]})]}),(0,l.jsx)("div",{className:"flex items-center ml-2",children:(0,l.jsx)("button",{className:"text-blue-500 text-xs border border-blue-500 rounded-full px-2.5 py-1 hover:bg-blue-50 transition-colors",children:"查看"})})]},t))}):(0,l.jsxs)("div",{className:"p-8 flex flex-col items-center justify-center text-center",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-3",children:(0,l.jsx)(g,{className:"w-8 h-8 text-gray-400"})}),(0,l.jsx)("p",{className:"text-gray-500 mb-2",children:"您还没有获得任何奖品"}),(0,l.jsx)("p",{className:"text-gray-400 text-sm",children:"快去参与抽奖，赢取精美礼品吧！"})]})}),a.length>0&&(0,l.jsx)("div",{className:"p-4 border-t border-gray-100",children:(0,l.jsx)("button",{onClick:s,className:"w-full py-2.5 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-medium rounded-lg flex items-center justify-center",children:"关闭"})})]})}):null}let y=(0,c.A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),w=e=>{let{title:t,list:s}=e;return(0,l.jsxs)("div",{className:"mx-3 my-4 overflow-hidden rounded-xl bg-white shadow-md border-2 border-yellow-300",children:[(0,l.jsxs)("div",{className:"bg-gradient-to-r from-red-500 to-yellow-500 px-4 py-3 flex items-center justify-between",children:[(0,l.jsxs)("h3",{className:"text-lg font-bold text-white flex items-center",children:[(0,l.jsx)(h,{className:"mr-2",size:18}),t]}),(0,l.jsx)("div",{className:"flex",children:[void 0,void 0,void 0].map((e,t)=>(0,l.jsx)(y,{size:14,className:"text-yellow-200 ml-1",fill:"currentColor"},t))})]}),(0,l.jsx)("div",{className:"p-4",children:s.map((e,t)=>(0,l.jsxs)("div",{className:"mb-4 last:mb-0",children:["text"===e.type&&(0,l.jsx)("div",{className:"text-gray-700 whitespace-pre-line leading-relaxed",children:e.text}),"img"===e.type&&(0,l.jsx)("div",{className:"mt-2 rounded-lg overflow-hidden border-2 border-yellow-100",children:(0,l.jsx)("img",{src:e.img,alt:"",className:"w-full"})})]},t))})]})},N={data:{html:{module:[{title:"活动介绍",list:[{text:'为庆祝艺术教育中心成立十周年，我们特别推出"艺术梦想家"抽奖活动。参与活动即有机会赢取价值千元的艺术课程、绘画工具套装及精美文创礼品。活动期间，每位用户每天可获得3次免费抽奖机会。感谢广大学员和家长多年来的支持与信任，让我们一起分享喜悦，共同成长！',type:"text"},{img:"https://images.unsplash.com/photo-1550006590-37c7069df9f2?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",type:"img"}]},{title:"活动规则",list:[{text:'1. 每位用户每天可获得3次免费抽奖机会\n2. 点击"立即抽奖"按钮开始转盘抽奖\n3. 转盘停止后会立即显示抽中奖品\n4. 实物奖品将在7个工作日内安排发放\n5. 课程奖品可在3个月内使用，请联系客服预约\n6. 活动最终解释权归艺术教育中心所有',type:"text"}]},{title:"奖品说明",list:[{text:"一等奖：价值1980元的艺术课程季卡一张\n二等奖：专业绘画工具套装\n三等奖：精美文创礼品一份\n纪念奖：艺术中心文化衫一件\n参与奖：课程优惠券100元",type:"text"},{img:"https://images.unsplash.com/photo-1607082349566-187342175e2f?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",type:"img"}]}],audioUrl:"https://image.cardmee.net/540/2020/04/voice/0d3fb888-f41a-4021-8e2d-22a93b3150e5.mp3"},options:{cover:"https://images.unsplash.com/photo-1639322537228-f710d846310a?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",name:"艺术教育中心十周年庆抽奖活动",shareDesc:"参与抽奖赢大奖，最高价值1980元艺术课程",shareTitle:"艺术教育中心十周年庆幸运大转盘",buttons:[{text:"立即抽奖",type:"spin",enabled:!0}],stats:[{value:1583,label:"人参与"},{value:463,label:"人分享"},{value:128,label:"人获奖"}],prizes:[{id:1,name:"一等奖",description:"艺术课程季卡",image:"https://images.unsplash.com/photo-1605721911519-3dfeb3be25e7?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",probability:.01,value:"1980元"},{id:2,name:"二等奖",description:"专业绘画工具套装",image:"https://images.unsplash.com/photo-1513364776144-60967b0f800f?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",probability:.03,value:"498元"},{id:3,name:"三等奖",description:"精美文创礼品",image:"https://images.unsplash.com/photo-1555181937-efe4e074a301?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",probability:.06,value:"198元"},{id:4,name:"纪念奖",description:"艺术中心文化衫",image:"https://images.unsplash.com/photo-1576566588028-4147f3842f27?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",probability:.2,value:"99元"},{id:5,name:"参与奖",description:"课程优惠券",image:"https://images.unsplash.com/photo-1561715276-a2d087060f1d?ixlib=rb-4.0.3&auto=format&fit=crop&w=900&q=80",probability:.7,value:"100元"}],helpConfig:{enabled:!0,helpNeeded:3,rewardSpins:2,description:"邀请3位好友助力，可额外获得2次抽奖机会！",buttonText:"邀请好友助力",shareTitle:"帮我助力，一起抽大奖",shareDesc:"点击链接为我助力，你也可获得抽奖机会哦"}}}};var k=s(17580);let A=e=>{let{helpNeeded:t,currentHelp:s,rewardSpins:a,description:r,onInvite:i}=e,n=Math.min(100,s/t*100);return(0,l.jsx)("div",{className:"mx-3 my-4 bg-white rounded-lg shadow-md overflow-hidden border-2 border-orange-100",children:(0,l.jsxs)("div",{className:"p-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(k.A,{size:18,className:"text-orange-500 mr-2"}),(0,l.jsx)("h3",{className:"font-bold text-gray-800",children:"好友助力"})]}),(0,l.jsxs)("div",{className:"text-sm text-orange-500 font-medium",children:[s,"/",t,"人"]})]}),(0,l.jsx)("div",{className:"bg-gray-200 h-3 rounded-full mb-3",children:(0,l.jsx)("div",{className:"bg-gradient-to-r from-orange-400 to-red-500 h-3 rounded-full transition-all duration-500 ease-in-out",style:{width:"".concat(n,"%")}})}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:r}),(0,l.jsxs)("button",{onClick:i,className:"w-full py-2.5 bg-gradient-to-r from-orange-400 to-red-500 text-white font-medium rounded-lg flex items-center justify-center",children:[(0,l.jsx)(k.A,{size:16,className:"mr-2"}),"邀请好友助力"]}),s>=t&&(0,l.jsxs)("div",{className:"mt-3 text-center text-sm text-green-600 font-medium",children:["\uD83C\uDF89 助力成功！已获得",a,"次额外抽奖机会"]})]})})},z=e=>{let{open:t,onClose:s,shareTitle:a,shareDesc:r,qrCode:i}=e;return t?(0,l.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-sm",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center p-4 border-b",children:[(0,l.jsx)("h3",{className:"font-bold text-lg",children:"邀请好友助力"}),(0,l.jsx)("button",{onClick:s,className:"text-gray-500",children:(0,l.jsx)(b.A,{size:20})})]}),(0,l.jsxs)("div",{className:"p-5",children:[(0,l.jsxs)("div",{className:"text-center mb-4",children:[(0,l.jsx)("h4",{className:"font-bold text-lg mb-2",children:a}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:r})]}),(0,l.jsx)("div",{className:"flex justify-center mb-4",children:(0,l.jsx)("img",{src:i,alt:"邀请二维码",className:"w-48 h-48"})}),(0,l.jsxs)("div",{className:"text-center text-sm text-gray-500",children:[(0,l.jsx)("p",{children:"截屏保存二维码，发送给好友"}),(0,l.jsx)("p",{children:"或点击下方按钮直接分享"})]}),(0,l.jsxs)("div",{className:"flex justify-center space-x-3 mt-4",children:[(0,l.jsxs)("button",{className:"bg-green-500 hover:bg-green-600 text-white rounded-lg px-4 py-2 flex items-center",children:[(0,l.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{d:"M1.898 0v24h20.204v-24h-20.204zm17.546 16.885c-.528 1.485-2.106 2.495-3.867 2.495-2.118 0-3.833-1.602-3.833-3.573 0-1.961 1.715-3.574 3.833-3.574.374 0 .739.053 1.079.152l-1.016-1.159h-3.02v-4.266h5.711c.262.261.648.486.906.756.302.316.588.644.851.977.263.334.502.682.717 1.043.216.362.405.736.564 1.118.16.382.288.773.384 1.168.096.395.16.793.192 1.191.031.398.031.796 0 1.192-.032.397-.096.793-.192 1.188-.096.395-.224.785-.384 1.167-.16.382-.348.756-.564 1.118-.215.363-.454.71-.717 1.044zm-3.698-3.574c-.252 0-.491.097-.669.271s-.278.409-.278.656.1.483.278.656.417.271.669.271c.251 0 .492-.097.67-.271s.277-.409.277-.656-.1-.482-.277-.656c-.178-.174-.419-.271-.67-.271zm8.172-6.155h-2.189v-.866h.549v-1.735h1.094v1.735h.546v.866zm-15.126-4.352h4.21v1.735h-4.21v-1.735zm1.094 2.599h2.023v1.21h-2.023v-1.21zm6.777 7.111c.683 0 1.31-.33 1.693-1.096-.012-.043-.025-.085-.037-.128h-3.374c-.228.196-.384.455-.445.741-.089.418.009.853.268 1.196.262.345.663.574 1.094.628.268.024.537.004.794-.06.257-.063.498-.174.707-.325.057-.042.112-.087.164-.135.051-.048.099-.1.143-.155.034-.043.066-.088.096-.136l-.737-.565c-.099.113-.227.199-.371.249-.144.05-.299.064-.45.039-.075-.012-.148-.037-.216-.073-.068-.036-.13-.084-.185-.142-.054-.058-.099-.125-.135-.199-.018-.037-.034-.076-.048-.115h2.039zm-2.039-1.12c.018-.038.039-.074.061-.11.023-.035.049-.069.077-.101.056-.064.122-.119.194-.166.073-.046.152-.083.235-.11s.17-.043.258-.043.179.015.264.043c.085.029.166.068.238.118.073.05.138.108.193.175.055.067.101.143.137.224h-1.657zm3.117-2.688c0 .302-.117.592-.326.805-.208.214-.49.334-.785.334-.295 0-.577-.12-.785-.334s-.325-.503-.325-.805h2.221zm.866-.866h-3.955v.173h3.955v-.173z"})}),"微信好友"]}),(0,l.jsxs)("button",{className:"bg-blue-500 hover:bg-blue-600 text-white rounded-lg px-4 py-2 flex items-center",children:[(0,l.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{d:"M7.982 17.513c-1.224-1.224-1.224-3.209 0-4.433s3.209-1.224 4.433 0 1.224 3.209 0 4.433-3.209 1.224-4.433 0zm3.35-3.35c-.655-.655-1.716-.655-2.371 0-.655.655-.655 1.716 0 2.371.655.655 1.716.655 2.371 0 .655-.655.655-1.716 0-2.371zm-7.835 3.35c-.129.129-.129.339 0 .468.129.129.339.129.468 0 .129-.129.129-.339 0-.468-.129-.129-.339-.129-.468 0zm15.606-15.606c-4.271-4.271-11.196-4.271-15.468 0s-4.271 11.196 0 15.468c4.271 4.271 11.196 4.271 15.468 0s4.271-11.196 0-15.468zm-1.06 14.408c-3.696 3.696-9.651 3.696-13.348 0s-3.696-9.651 0-13.348 9.651-3.696 13.348 0 3.696 9.651 0 13.348zm-8.162-9.324c-.129.129-.129.339 0 .468.129.129.339.129.468 0 .129-.129.129-.339 0-.468-.129-.129-.339-.129-.468 0z"})}),"朋友圈"]})]})]})]})}):null};function M(){let[e,t]=(0,a.useState)(!1),[s,n]=(0,a.useState)(!1),[c,d]=(0,a.useState)(!1),[m,x]=(0,a.useState)(!1),[f,u]=(0,a.useState)(!0),[b,g]=(0,a.useState)(3),[j,y]=(0,a.useState)(null),[k,M]=(0,a.useState)([]),[C,S]=(0,a.useState)(0),[q,F]=(0,a.useState)(!1),T=N.data.html,P=N.data.options,L=P.prizes||[],D=P.helpConfig||{enabled:!1,helpNeeded:3,rewardSpins:2};(0,a.useEffect)(()=>{D.enabled&&C>=D.helpNeeded&&!q&&(g(e=>e+D.rewardSpins),F(!0),alert("恭喜您获得".concat(D.rewardSpins,"次额外抽奖机会！")))},[C,D,q]);let I=()=>{setTimeout(()=>{S(e=>Math.min(e+1,D.helpNeeded))},1e3)},B=()=>{t(!0)};return(0,l.jsxs)("div",{className:"bg-gray-50 min-h-screen font-sans pb-20",children:[(0,l.jsx)("img",{src:P.cover,alt:"banner",className:"w-full h-48 object-cover"}),(0,l.jsxs)("div",{className:"flex items-start justify-between mt-2.5 px-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-xl text-red-500 font-bold",children:P.name}),(0,l.jsx)("p",{className:"text-sm text-gray-500 mb-2.5",children:P.shareDesc})]}),(0,l.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,l.jsxs)("button",{onClick:()=>d(!0),className:"flex flex-col items-center text-gray-500 pt-1",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center",children:(0,l.jsx)(h,{size:18})}),(0,l.jsx)("span",{className:"text-xs mt-1",children:"我的奖品"})]}),(0,l.jsxs)("button",{onClick:B,className:"flex flex-col items-center text-gray-500 pt-1",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,l.jsx)("path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"}),(0,l.jsx)("polyline",{points:"16 6 12 2 8 6"}),(0,l.jsx)("line",{x1:"12",y1:"2",x2:"12",y2:"15"})]})}),(0,l.jsx)("span",{className:"text-xs mt-1",children:"分享"})]})]})]}),(0,l.jsx)(i,{prizes:L}),(0,l.jsxs)("div",{className:"mx-3 my-3 p-3 bg-white rounded-lg shadow-sm",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("div",{className:"text-gray-700",children:(0,l.jsx)("span",{className:"font-medium",children:"今日抽奖次数"})}),(0,l.jsxs)("div",{className:"text-red-500 font-bold",children:[b," / 3"]})]}),0===b&&D.enabled&&!q&&(0,l.jsxs)("div",{className:"text-sm text-orange-500 mt-2 flex items-center",children:[(0,l.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),"抽奖次数已用完，邀请好友助力可获得额外抽奖机会！"]})]}),D.enabled&&0===b&&!q&&(0,l.jsx)(A,{helpNeeded:D.helpNeeded,currentHelp:C,rewardSpins:D.rewardSpins,description:D.description,onInvite:()=>{x(!0)}}),(0,l.jsx)(o,{prizes:L,spinning:!f,onSpinEnd:()=>{},onSpin:()=>{if(f){if(b<=0){alert("今日抽奖次数已用完，请明天再来！");return}u(!1);let e=Math.random()*L.reduce((e,t)=>e+t.probability,0),t=0,s=L[L.length-1];for(let l of L)if(e<=(t+=l.probability)){s=l;break}y(s),setTimeout(()=>{n(!0),g(e=>e-1),u(!0),M(e=>[...e,{...s,winTime:new Date().toLocaleString()}])},5e3)}},selectedPrize:j}),T.module.map((e,t)=>(0,l.jsx)(w,{title:e.title,list:e.list},t)),(0,l.jsx)("div",{className:"fixed inset-x-0 bottom-0 bg-white shadow-lg flex justify-around py-2.5",children:(0,l.jsxs)("div",{className:"flex justify-around w-full",children:[(0,l.jsxs)("button",{onClick:()=>d(!0),className:"flex flex-col items-center justify-center py-1 px-6 rounded-full bg-gradient-to-r from-pink-500 to-red-500 text-white",children:[(0,l.jsx)(h,{size:20,className:"mb-1"}),(0,l.jsx)("span",{className:"text-xs",children:"我的奖品"})]}),(0,l.jsxs)("button",{onClick:B,className:"flex flex-col items-center justify-center py-1 px-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white",children:[(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mb-1",children:[(0,l.jsx)("path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"}),(0,l.jsx)("polyline",{points:"16 6 12 2 8 6"}),(0,l.jsx)("line",{x1:"12",y1:"2",x2:"12",y2:"15"})]}),(0,l.jsx)("span",{className:"text-xs",children:"分享活动"})]})]})}),e&&(0,l.jsx)(r.A,{open:e,onClose:()=>t(!1),shareTitle:P.shareTitle,shareDesc:P.shareDesc,qrCode:"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=".concat(encodeURIComponent(window.location.href))}),s&&j&&(0,l.jsx)(p,{open:s,onClose:()=>{n(!1)},prize:j}),c&&(0,l.jsx)(v,{open:c,onClose:()=>d(!1),prizes:k}),m&&D&&(0,l.jsx)(z,{open:m,onClose:()=>{x(!1),I()},shareTitle:D.shareTitle||"邀请好友助力",shareDesc:D.shareDesc||"帮我助力，你也可获得抽奖机会",qrCode:"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=".concat(encodeURIComponent(window.location.href))})]})}},13052:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17580:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},56904:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var l=s(95155);s(12115);var a=s(66766),r=s(54416);function i(e){let{open:t,onClose:s,qrCode:i,shareTitle:n,shareDesc:c}=e;return t?(0,l.jsx)("div",{className:"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4",children:(0,l.jsxs)("div",{className:"bg-white rounded-2xl w-full max-w-sm relative",children:[(0,l.jsx)("button",{onClick:s,className:"absolute top-2 right-2 text-gray-400 hover:text-gray-600 p-1",children:(0,l.jsx)(r.A,{size:24})}),(0,l.jsxs)("div",{className:"pt-6 pb-4 px-6 text-center border-b border-gray-100",children:[(0,l.jsx)("h3",{className:"text-xl font-bold",children:"分享给好友"}),(0,l.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"邀请好友一起参与"})]}),(0,l.jsx)("div",{className:"grid grid-cols-4 gap-2 p-6",children:[{icon:"/icons/wechat.svg",name:"微信",color:"bg-green-500"},{icon:"/icons/moments.svg",name:"朋友圈",color:"bg-green-600"},{icon:"/icons/qq.svg",name:"QQ",color:"bg-blue-400"},{icon:"/icons/weibo.svg",name:"微博",color:"bg-red-500"}].map(e=>(0,l.jsxs)("button",{className:"flex flex-col items-center",children:[(0,l.jsx)("div",{className:"w-12 h-12 ".concat(e.color," rounded-full flex items-center justify-center mb-1 text-white"),children:e.icon?(0,l.jsx)(a.default,{src:e.icon,alt:e.name,width:24,height:24}):e.name.charAt(0)}),(0,l.jsx)("span",{className:"text-xs",children:e.name})]},e.name))}),i&&(0,l.jsxs)("div",{className:"px-6 pb-6 flex flex-col items-center",children:[(0,l.jsx)("div",{className:"border border-gray-200 rounded-lg p-2 mb-2",children:(0,l.jsx)("img",{src:i,alt:"二维码",className:"w-40 h-40 object-contain"})}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"扫描二维码参与活动"})]}),(0,l.jsx)("div",{className:"bg-gray-50 p-4 rounded-b-2xl border-t border-gray-100",children:(0,l.jsxs)("div",{className:"bg-white p-3 rounded-lg border border-gray-200",children:[(0,l.jsx)("h4",{className:"font-medium text-sm",children:n}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:c})]})})]})}):null}},58832:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(19946).A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},88778:(e,t,s)=>{Promise.resolve().then(s.bind(s,12396))}},e=>{var t=t=>e(e.s=t);e.O(0,[9209,6315,7358],()=>t(88778)),_N_E=e.O()}]);