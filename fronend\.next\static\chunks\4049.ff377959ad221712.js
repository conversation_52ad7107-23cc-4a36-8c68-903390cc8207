"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4049],{94049:(e,t,r)=>{r.r(t),r.d(t,{default:()=>l});var n=r(12115);let l=n.forwardRef(function(e,t){let{title:r,titleId:l,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},a),r?n.createElement("title",{id:l},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 12 3.269 3.125A59.769 59.769 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.875L5.999 12Zm0 0h7.5"}))})}}]);