"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3670],{33670:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var k=a(95155);let d=(0,a(19946).A)("UserCog",[["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m21.7 16.4-.9-.3",key:"12j9ji"}],["path",{d:"m15.2 13.9-.9-.3",key:"1fdjdi"}],["path",{d:"m16.6 18.7.3-.9",key:"heedtr"}],["path",{d:"m19.1 12.2.3-.9",key:"1af3ki"}],["path",{d:"m19.6 18.7-.4-1",key:"1x9vze"}],["path",{d:"m16.8 12.3-.4-1",key:"vqeiwj"}],["path",{d:"m14.3 16.6 1-.4",key:"1qlj63"}],["path",{d:"m20.7 13.8 1-.4",key:"1v5t8k"}]]);var n=a(12115),l=a(55028),p=a(35035),r=a(65436),c=a(57001);let h=(0,l.default)(()=>Promise.all([a.e(3168),a.e(2319),a.e(2904)]).then(a.bind(a,82904)),{loadableGenerated:{webpack:()=>[82904]},ssr:!1});function i(e){let{studentId:t}=e,a=(0,r.j)(),[l,i]=(0,n.useState)(!1);return(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(c.p,{icon:d,tooltipText:"学员详细",onClick:()=>{a((0,p.ZZ)({studentId:t})),i(!0)}}),l&&(0,k.jsx)(h,{open:l,onOpenChange:i})]})}}}]);