import fp from "fastify-plugin"
import env from "@fastify/env"


const schema = {
    type: 'object',
    required: ['NODE_ENV', 'PORT', 'REDIS_HOST', 'REDIS_PORT'],
    properties: {
        // basic
        PORT: {
            type: 'number',
            default: 3000
        },
        NODE_ENV: {
            type: 'string',
            default: 'development'
        },

        // postgres
        POSTGRES_HOST: {
            type: 'string',
            default: 'localhost'
        },
        POSTGRES_USER: {
            type: 'string',
            default: 'postgres'
        },
        POSTGRES_PASSWORD: {
            type: 'string',
            default: 'postgres'
        },
        POSTGRES_DB: {
            type: 'string',
            default: 'postgres'
        },
        // redis
        REDIS_HOST: {
            type: 'string',
            default: 'localhost'
        },
        REDIS_PORT: {
            type: 'number',
            default: 6379
        },
        REDIS_PASSWORD: {
            type: 'string',
            default: ''
        },
        REDIS_DB: {
            type: 'number',
            default: 0
        },
        // jwt
        JWT_SECRET: {
            type: 'string',
            default: 'jwtsecret'
        }
        
    }
}

async function envPlugin(fastify, options) {
    await fastify.register(env, {
        schema: schema,
        dotenv: true
    })
}

export default fp(envPlugin, {
    name: 'env-plugin',
    confKey: 'config',
    dependencies: []
})