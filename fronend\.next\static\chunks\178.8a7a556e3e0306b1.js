"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[178],{61809:(e,a,l)=>{l.d(a,{Y:()=>n});var t=l(44861),s=l(73168),r=l(24122);let n=function(e){let a,l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"yyyy-MM-dd",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(null==e)return n;try{if("string"==typeof e)a=new Date(e);else if("number"==typeof e)a=new Date(e);else{if(!(e instanceof Date))return n;a=e}if(!(0,t.f)(a))return n;return(0,s.GP)(a,l,{locale:r.g})}catch(e){return console.error("Date formatting error:",e),n}}},63375:(e,a,l)=>{l.d(a,{A:()=>c});var t=l(95155),s=l(12115),r=l(59409),n=l(59434),d=l(6658);let c=(0,s.memo)(function(e){let{teacher:a,setTeacher:l,width:c="w-full",placeholder:i="选择人员",className:o="",showAllOption:u=!0,allOptionText:m="全部人员",allOptionValue:f="all",teacherList:x,disabled:p=!1}=e,{data:h,isLoading:g,error:b}=(0,d.X)(),j=(0,s.useCallback)(e=>{l(e)},[l]),y=(0,s.useCallback)(()=>(0,n.cn)("h-9 ".concat(c),o),[c,o]),N=x||h;return(0,t.jsxs)(r.l6,{value:a,onValueChange:j,disabled:p||g,children:[(0,t.jsx)(r.bq,{className:y(),children:(0,t.jsx)(r.yv,{placeholder:i})}),(0,t.jsxs)(r.gC,{children:[b&&(0,t.jsx)(r.eb,{value:"error",disabled:!0,children:String(b)}),g&&(0,t.jsx)(r.eb,{value:"loading",disabled:!0,children:"加载中..."}),!g&&!b&&(0,t.jsxs)(t.Fragment,{children:[u&&(0,t.jsx)(r.eb,{value:f,children:m}),null==N?void 0:N.map(e=>(0,t.jsx)(r.eb,{value:e.id,children:e.name},e.id))]})]})]})})},66695:(e,a,l)=>{l.d(a,{BT:()=>i,Wu:()=>o,ZB:()=>c,Zp:()=>n,aR:()=>d,wL:()=>u});var t=l(95155),s=l(12115),r=l(59434);let n=s.forwardRef((e,a)=>{let{className:l,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",l),...s})});n.displayName="Card";let d=s.forwardRef((e,a)=>{let{className:l,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",l),...s})});d.displayName="CardHeader";let c=s.forwardRef((e,a)=>{let{className:l,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",l),...s})});c.displayName="CardTitle";let i=s.forwardRef((e,a)=>{let{className:l,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("text-sm text-muted-foreground",l),...s})});i.displayName="CardDescription";let o=s.forwardRef((e,a)=>{let{className:l,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("p-6 pt-0",l),...s})});o.displayName="CardContent";let u=s.forwardRef((e,a)=>{let{className:l,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("flex items-center p-6 pt-0",l),...s})});u.displayName="CardFooter"},70178:(e,a,l)=>{l.r(a),l.d(a,{default:()=>K});var t=l(95155),s=l(12115),r=l(9110),n=l(55028),d=l(26126),c=l(45968),i=l(79181),o=l(50228),u=l(61809);let m=(0,n.default)(()=>l.e(1289).then(l.bind(l,33670)),{loadableGenerated:{webpack:()=>[33670]},ssr:!1}),f=(0,n.default)(()=>l.e(2248).then(l.bind(l,82248)),{loadableGenerated:{webpack:()=>[82248]},ssr:!1}),x=(0,n.default)(()=>l.e(1037).then(l.bind(l,55799)),{loadableGenerated:{webpack:()=>[55799]},ssr:!1}),p=(0,n.default)(()=>l.e(6278).then(l.bind(l,86278)),{loadableGenerated:{webpack:()=>[86278]},ssr:!1}),h=[{accessorKey:"name",header:"学员名称",cell:e=>{let{row:a}=e;return(0,o.P)(a.getValue("name"))}},{accessorKey:"phone",header:"手机号码",cell:e=>{let{row:a}=e;return(0,o.P)(a.getValue("phone"))}},{accessorKey:"age",header:"年龄",cell:e=>{let{row:a}=e;return(0,o.P)((0,c.C)(a.original.birthday||""))}},{accessorKey:"gender",header:"性别",cell:e=>{let{row:a}=e;return(0,o.P)((0,i.$)(a.getValue("gender")))}},{accessorKey:"address",header:"家庭住址",cell:e=>{let{row:a}=e;return(0,o.s)(a.getValue("address"),4)}},{accessorKey:"birthday",header:"学员生日",cell:e=>{let{row:a}=e,l=a.original.birthday;return l?(0,o.P)((0,u.Y)(new Date(l),"MM-dd"),!0):(0,o.P)("-",!0)}},{accessorKey:"followerName",header:"跟进人",cell:e=>{let{row:a}=e;return(0,o.P)((0,t.jsx)(d.E,{variant:"outline",className:"font-normal text-xs",children:a.original.followerName}))}},{accessorKey:"remarks",header:"备注",cell:e=>{let{row:a}=e;return(0,o.s)(a.getValue("remarks"))}},{accessorKey:"createdAt",header:"加入时间",cell:e=>{let{row:a}=e;return(0,o.s)((0,u.Y)(a.getValue("createdAt"),"yyyy-MM-dd HH:mm:ss"))}},{id:"actions",header:"操作",cell:e=>{let{row:a}=e,l=a.original;return console.log(l,"student"),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m,{studentId:l.id},"studentInfoDialog-".concat(l.id)),(0,t.jsx)(f,{student:l},"actionStudentPayment-".concat(l.id)),(0,t.jsx)(x,{studentId:l.id,studentType:l.type||"formal"},"actionChangeStudentType-".concat(l.id)),(0,t.jsx)(p,{},"classesSelection-".concat(l.id))]})}}];var g=l(62523),b=l(95728),j=l(48432),y=l(30285),N=l(48436),v=l(63375),w=l(45964),C=l.n(w),k=l(90010);function S(e){let{isOpen:a,title:l="确认",content:s,confirmText:r="确认",cancelText:n="取消",onConfirm:d,onCancel:c}=e;return(0,t.jsx)(k.Lt,{open:a,onOpenChange:e=>!e&&c(),children:(0,t.jsxs)(k.EO,{children:[(0,t.jsxs)(k.wd,{children:[(0,t.jsx)(k.r7,{children:l}),(0,t.jsx)(k.$v,{children:s})]}),(0,t.jsxs)(k.ck,{children:[(0,t.jsx)(k.Zr,{onClick:c,children:n}),(0,t.jsx)(k.Rx,{onClick:d,children:r})]})]})})}var R=l(40694),P=l(47924),T=l(66695),D=l(91347);let z=(0,n.default)(()=>Promise.all([l.e(81),l.e(5589),l.e(8732)]).then(l.bind(l,38732)),{loadableGenerated:{webpack:()=>[38732]},ssr:!1}),V={searchName:"",page:1,pageSize:10};function K(){let[e]=(0,b.eq)(),[a,l]=(0,s.useState)(!1),[n,d]=(0,s.useState)("all"),[c,i]=(0,s.useState)([]),[o,u]=(0,s.useState)(V),m=(0,s.useRef)(null),{confirm:f,ConfirmDialog:x}=function(){let[e,a]=(0,s.useState)(!1),[l,r]=(0,s.useState)(null),[n,d]=(0,s.useState)(null),c=(0,s.useCallback)(e=>new Promise(l=>{r(e),a(!0),d(()=>l)}),[]),i=(0,s.useCallback)(()=>{a(!1),null==n||n(!0),d(null),r(null)},[n]),o=(0,s.useCallback)(()=>{a(!1),null==n||n(!1),d(null),r(null)},[n]);return{confirm:c,ConfirmDialog:(0,s.useCallback)(()=>l?(0,t.jsx)(S,{isOpen:e,title:l.title,content:l.content,confirmText:l.confirmText,cancelText:l.cancelText,onConfirm:i,onCancel:o}):null,[e,l,i,o])}}(),p=(0,s.useMemo)(()=>C()(e=>{u(a=>({...a,searchName:e,page:1}))},300),[]),w=(0,s.useMemo)(()=>"all"===n&&o.searchName.trim()?R.hT:{page:o.page,pageSize:o.pageSize,search:o.searchName,follower:"all"===n?"":n},[o,n]),k=(0,s.useCallback)(e=>{i(a=>a.length===e.length&&a.every((a,l)=>{var t;return a.id===(null===(t=e[l])||void 0===t?void 0:t.id)})?a:e)},[]),{data:K,isLoading:L,refetch:M}=(0,b.FQ)(w,{pollingInterval:6e4,skipPollingIfUnfocused:!0}),Z=(0,s.useCallback)(async()=>{if(0!==c.length&&await f({title:"确认删除",content:"确定要删除选中的 ".concat(c.length," 个学员吗？"),confirmText:"删除",cancelText:"取消"}))try{let a=c.map(e=>e.id),l=await e(a).unwrap();(null==l?void 0:l.code)===200&&(i([]),N.l.success(l.message),M())}catch(e){N.l.error("删除失败")}},[e,c,M,f]),A=(0,s.useCallback)(e=>{p(e)},[p]),E=(0,s.useCallback)(e=>{u(a=>({...a,page:e}))},[]),I=(0,s.useCallback)(e=>{u(a=>({...a,pageSize:e,page:1}))},[]),G=(0,s.useCallback)(e=>{d(e),u(e=>({...e,page:1}))},[]),O=(0,s.useCallback)(()=>{l(!0)},[]),Y=(0,s.useCallback)(()=>{l(!1),M()},[M]);return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(T.Zp,{className:"border-slate-200",children:(0,t.jsx)(T.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-end md:justify-between",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[(0,t.jsxs)("div",{className:"relative w-full sm:w-72",children:[(0,t.jsx)(P.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,t.jsx)(g.p,{ref:m,placeholder:"搜索名称...",defaultValue:o.searchName,onChange:e=>A(e.target.value),className:"pl-10 bg-slate-50 border-slate-200 focus:bg-white transition-colors"})]}),(0,t.jsx)("div",{className:"w-[140px]",children:(0,t.jsx)(v.A,{teacher:n,setTeacher:G})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(D.LQ,{permission:"student:create",children:(0,t.jsx)(y.$,{onClick:O,children:"新增学员"})}),(0,t.jsx)(D.LQ,{permission:"student:delete",children:(0,t.jsx)(y.$,{variant:"destructive",onClick:Z,disabled:0===c.length,children:"删除学员"})})]})]})})}),(0,t.jsx)("div",{className:"bg-white rounded-lg border border-slate-200",children:(0,t.jsx)(r.b,{selectable:!0,onSelectedRowsChange:k,columns:h,data:(null==K?void 0:K.list)||[],pagination:!1,loading:L},"studentInfoTable")}),(0,t.jsx)(j.default,{totalItems:(null==K?void 0:K.total)||0,pageSize:o.pageSize,currentPage:o.page,onPageChange:E,onPageSizeChange:I}),(0,t.jsx)(x,{}),a&&(0,t.jsx)(z,{open:a,onOpenChange:l,onSuccess:Y})]})}},90010:(e,a,l)=>{l.d(a,{$v:()=>h,EO:()=>m,Lt:()=>c,Rx:()=>g,Zr:()=>b,ck:()=>x,r7:()=>p,tv:()=>i,wd:()=>f});var t=l(95155),s=l(12115),r=l(17649),n=l(59434),d=l(30285);let c=r.bL,i=r.l9,o=r.ZL,u=s.forwardRef((e,a)=>{let{className:l,...s}=e;return(0,t.jsx)(r.hJ,{className:(0,n.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",l),...s,ref:a})});u.displayName=r.hJ.displayName;let m=s.forwardRef((e,a)=>{let{className:l,...s}=e;return(0,t.jsxs)(o,{children:[(0,t.jsx)(u,{}),(0,t.jsx)(r.UC,{ref:a,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",l),...s})]})});m.displayName=r.UC.displayName;let f=e=>{let{className:a,...l}=e;return(0,t.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...l})};f.displayName="AlertDialogHeader";let x=e=>{let{className:a,...l}=e;return(0,t.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...l})};x.displayName="AlertDialogFooter";let p=s.forwardRef((e,a)=>{let{className:l,...s}=e;return(0,t.jsx)(r.hE,{ref:a,className:(0,n.cn)("text-lg font-semibold",l),...s})});p.displayName=r.hE.displayName;let h=s.forwardRef((e,a)=>{let{className:l,...s}=e;return(0,t.jsx)(r.VY,{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",l),...s})});h.displayName=r.VY.displayName;let g=s.forwardRef((e,a)=>{let{className:l,...s}=e;return(0,t.jsx)(r.rc,{ref:a,className:(0,n.cn)((0,d.r)(),l),...s})});g.displayName=r.rc.displayName;let b=s.forwardRef((e,a)=>{let{className:l,...s}=e;return(0,t.jsx)(r.ZD,{ref:a,className:(0,n.cn)((0,d.r)({variant:"outline"}),"mt-2 sm:mt-0",l),...s})});b.displayName=r.ZD.displayName}}]);