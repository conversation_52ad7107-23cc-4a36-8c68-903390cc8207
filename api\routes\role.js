import { INTERNAL_ERROR, NOT_FOUND_ERROR, VALIDATION_ERROR } from '../errors/index.js'
import { nanoid } from "nanoid"
import { TwitterSnowflake } from "@sapphire/snowflake"
import { roleCreateSchemas } from '../schemas/role.js';
import { sendToSpecifiedUser } from '../libs/websocket.js';

export default async function (fastify, opts) {

    // 获取角色权限
    fastify.get("/roles/:roleId/permissions", {
        schema: {
            tags: ['roles'],
            summary: '获取角色详情权限',
            description: '获取根据角色id获取角色权限',
        },
        // onRequst: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const { roleId } = request.params;
            const roleWithPermissions = await fastify.prisma.role.findUnique({
                where: { id: roleId },
                select: {
                    id: true,
                    name: true,
                    rolePermissions: {
                        select: {
                            permission: {
                                select: {
                                    id: true,
                                    name: true,
                                    type: true,
                                    operations: {
                                        select: {
                                            id: true,
                                            name: true,
                                            code: true,
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            });
            const operations = roleWithPermissions.rolePermissions.map((r) => {
                return r.permission
            })
            reply.success({
                message: '获取角色权限成功',
                data: operations
            })
        }
    })

    // 给角色分配权限
    fastify.post('/roles/:roleId/permissions', {
        schema: {
            tags: ["roles"],
            summary: "给角色分配权限",
            description: "给角色分配权限",
            params: {
                type: "object",
                properties: {
                    roleId: {
                        type: "string",
                    },
                }
            },
        },
        handler: async function (request, reply) {

            const { roleId } = request.params;
            const { permissions } = request.body;

            if (!Array.isArray(permissions) || permissions.length === 0) {
                throw new VALIDATION_ERROR("permissions 不能为空且必须为非空数组");
            }
            
            const client = fastify.prisma;
            
            try {
                const role = await client.role.findUnique({
                    where: { id: roleId },
                    select: { id: true }
                });
                if (!role) {
                    throw new VALIDATION_ERROR("角色不存在");
                }
                const foundPermissions = await client.permission.findMany({
                    where: { id: { in: permissions } },
                    select: { id: true }
                });
                if (foundPermissions.length !== permissions.length) {
                    throw new VALIDATION_ERROR("部分权限不存在");
                }
                await fastify.prisma.$transaction([
                    fastify.prisma.rolePermission.deleteMany({ where: { roleId } }),
                    fastify.prisma.rolePermission.createMany({
                        data: permissions.map(pid => ({ roleId, permissionId: pid }))
                    })
                ]);
                reply.success({
                    message: "分配权限成功",
                })
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || "分配权限失败");
            }

        }
    })

    // 获取所有角色列表
    fastify.get("/roles", {
        schema: {
            tags: ['roles'],
            summary: '获取所有角色列表',
            description: '获取所有角色列表',
            params: {
                type: 'object',
                properties: {
                    page: { type: 'number' },
                    pageSize: { type: 'number' },
                    search: { type: 'string' },
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            // 检查用户权限是否拥有获取角色
            const { page = 1, pageSize = 10, search } = request.query
            const user = request.user
            try {

                const where = {
                    ...(search ? { name: { contains: search } } : {}),
                    institutionId: user.institutionId
                }
                const skip = (page - 1) * pageSize
                const take = pageSize


                const [roles, total] = await fastify.prisma.$transaction([
                    fastify.prisma.role.findMany({
                        where,
                        select: {
                            id: true,
                            name: true,
                            description: true,
                        },
                        skip: Number(skip),
                        take: Number(take),
                        orderBy: {
                            createdAt: 'desc'
                        }
                    }),
                    fastify.prisma.role.count({ where })
                ])

   
                reply.success({
                    data: {
                        list: roles,
                        total,
                        page,
                        pageSize
                    },
                    message: '获取角色列表成功'
                });
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '获取角色列表失败');
            } finally {
                // client.release();
            }
        }
    });


    // 创建新角色
    fastify.post("/roles", {
        schema: roleCreateSchemas,
        onRequest: [fastify.auth.authenticate],
        // preHandler: [fastify.auth.requirePermission(RoleConstants.ROLE.CEATE)],
        handler: async function (request, reply) {
            const { name, description } = request.body;
            const { id: userId, SYSTEM_ADMIN: is_super_admin } = request.user
            const client = await fastify.pg.connect();
            try {
                const result = await client.query(`
                    SELECT (SELECT "institutionId" FROM user_institution WHERE "userId" = $1 LIMIT 1) AS institution_id
                `, [userId]);

                const { institution_id } = result.rows[0];
                if (!is_super_admin && !institution_id) {
                    throw new VALIDATION_ERROR('无权创建角色');
                }

                // **生成角色代码**
                const code = is_super_admin ? nanoid(10).toString() : `ORG_${nanoid(10).toString()}`;

                // **检查角色是否存在**
                const roleExists = await client.query(`
                    SELECT 1 FROM roles WHERE name = $1 AND code = $2 LIMIT 1
                `, [name, code]);

                if (roleExists.rows.length > 0) {
                    throw new VALIDATION_ERROR('角色已存在');
                }

                // **创建角色**
                const id = TwitterSnowflake.generate().toString()
                const newRole = await client.query(`
                    INSERT INTO roles (id, name, code, description, "institutionId")
                    VALUES ($1, $2, $3, $4, $5) RETURNING *
                `, [id, name, code, description, institution_id]);

                reply.success({
                    message: '角色创建成功',
                    data: {
                        ...newRole.rows[0],
                        isInstitutionRole: !is_super_admin
                    }
                });
            } catch (error) {
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '创建角色失败');
            } finally {
                client.release();
            }
        }
    });
    // 根据角色id删除
    fastify.delete('/roles/:roleId', {
        schema: {
            tags: ['roles'],
            summary: '删除角色',
            description: '删除角色',
            params: {
                type: 'object',
                required: ['roleId'],
                properties: {
                    roleId: {
                        type: 'string',
                        description: '角色ID'
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        // preHandler: [fastify.auth.requirePermission('system:role:delete')],
        handler: async function (request, reply) {
            const { roleId } = request.params;
            const client = await fastify.pg.connect();
            client.query('BEGIN');
            try {

                // **合并查询：检查角色、用户绑定、权限**
                const result = await client.query(`
                    SELECT 
                        r.id, r.name, r.code, r."institutionId",
                        (SELECT COUNT(*) FROM user_roles WHERE "roleId" = $1) AS user_count,
                        (SELECT "institutionId" FROM user_institution WHERE "userId" = $2) AS user_institution
                    FROM roles r
                    WHERE r.id = $1
                `, [roleId, request.user.id]);

                if (result.rows.length === 0) throw new VALIDATION_ERROR('角色不存在')

                const { id, name, code, institutionid, user_count, user_institution } = result.rows[0]
                if (code === 'SYSTEM_ADMIN') throw new VALIDATION_ERROR('无法删除超级管理员角色');
                if (user_count > 0) throw new VALIDATION_ERROR('角色下存在用户，无法删除');
                if (institutionid && user_institution !== institutionid) {
                    throw new VALIDATION_ERROR('无权删除该角色');
                }

                //删除角色
                client.query(`DELETE FROM roles WHERE id = $1 AND "institutionId" = $2`, [roleId, request.user.institutionid]);
                client.query('COMMIT');

                reply.success({ message: `角色"${name}"删除成功` });
            } catch (error) {
                await client.query('ROLLBACK');
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '删除角色失败');
            } finally {
                client.release();
            }

        }
    });

    // 根据角色id更新
    fastify.put('/roles/:roleId', {
        schema: {
            tags: ['roles'],
            summary: '更新角色信息',
            description: '更新指定角色，同时进行权限和依赖检查',
            params: {
                type: 'object',
                properties: {
                    roleId: {
                        type: 'string',
                        description: '角色ID'
                    }
                },
                required: ['roleId']
            },
            body: {
                type: "object",
                required: ['name'],
                properties: {
                    name: {
                        type: 'string',
                    },
                    description: {
                        type: 'string',
                    },
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        // preHandler: [fastify.auth.requirePermission('system:role:update')],
        handler: async function (request, reply) {
            const { roleId } = request.params;
            const data = request.body
            const client = await fastify.pg.connect();
            try {
                await client.query('BEGIN');

                // **合并查询：检查角色、权限**
                const result = await client.query(`
                    SELECT 
                        r.id, r.name, r.code, r."institutionId",
                        (SELECT "institutionId" FROM user_institution WHERE "userId" = $2) AS user_institution
                    FROM roles r
                    WHERE r.id = $1
                `, [roleId, request.user.id]);

                if (result.rows.length === 0) throw new VALIDATION_ERROR('角色不存在');

                const { id, name, code, institutionid, user_institution } = result.rows[0];

                if (code === 'SYSTEM_ADMIN') throw new VALIDATION_ERROR('无法操作超级管理员角色');
                if (institutionid && user_institution !== institutionid) {
                    throw new VALIDATION_ERROR('无权操作该角色');
                }

                // **更新角色**
                await client.query(`UPDATE roles SET name = $2, description = $3, "updatedAt" = now() WHERE id = $1`, [roleId, data.name, data.description]);
                await client.query('COMMIT');

                reply.success({ message: `角色 "${name}" 更新成功` });
            } catch (error) {
                await client.query('ROLLBACK');
                fastify.log.error(error);
                throw new INTERNAL_ERROR(error.message || '角色更新失败');
            } finally {
                client.release();
            }
        }
    })

    // 根据角色id更新权限菜单
    fastify.put('/roles/:roleId/permissions', {
        schema: {
            tags: ['roles'],
            summary: '更新角色权限菜单',
            description: '更新指定角色的权限菜单',
            params: {
                type: 'object',
                properties: {
                    roleId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                required: ['permissions'],
                properties: {
                    permissions: { 
                        type: 'array', 
                        items: { 
                            type: 'string',
                            description: '菜单ID'
                        }
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async function (request, reply) {
            const user = request.user
            const { roleId } = request.params
            const { permissions } = request.body
            try {
                const role = await fastify.prisma.role.findUnique({
                    where: { id: roleId, institutionId: user.institutionId },
                    select: { id: true,
                        rolePermissions: {
                            select: {
                                permission: {
                                    select: {
                                        id: true,
                                        code: true
                                    }
                                }
                            }
                    }   }
                })
                if (!role) throw new VALIDATION_ERROR('角色不存在')

                // 获取传入的菜单 
                const menus = await fastify.prisma.menu.findMany({
                    where: { id: { in: permissions } },
                    select: { id: true, permissionCode: true }
                })
                if (menus.length !== permissions.length) throw new VALIDATION_ERROR('部分菜单不存在')

                // 角色当前拥有的权限code集合
                const oldPermissionCodes = role.rolePermissions.map(rp => rp.permission.code)
                // 新菜单对应的权限code集合
                const newPermissionCodes = menus.map(menu => menu.permissionCode)

                // 需要删除的权限（原有有但新菜单没有）
                const needDeletePermissions = role.rolePermissions
                    .filter(rp => !newPermissionCodes.includes(rp.permission.code))
                    .map(rp => rp.permission.id)

                // 需要新增的权限（新菜单有但原有没有）
                const menusToCreate = menus.filter(menu => !oldPermissionCodes.includes(menu.permissionCode))

                // 查询新增权限id
                const createPermissions = await fastify.prisma.permission.findMany({
                    where: { code: { in: menusToCreate.map(menu => menu.permissionCode) } },
                    select: { id: true }
                })

                // 删除角色权限
                if (needDeletePermissions.length > 0) {
                    await fastify.prisma.rolePermission.deleteMany({
                        where: { roleId, permissionId: { in: needDeletePermissions } }
                    })
                }

                // 创建角色权限
                if (createPermissions.length > 0) {
                    await fastify.prisma.rolePermission.createMany({
                        data: createPermissions.map(permission => ({
                            roleId,
                            permissionId: permission.id
                        }))
                    })
                }

                // 踢出角色在线用户
                const userRoles = await fastify.prisma.userRole.findMany({
                    where: {
                        roleId
                    },
                    select: {
                        userId: true
                    }
                })
                const payload = {
                    type: 'ROLE_PERMISSION_UPDATE',
                }

                for(const userRole of userRoles){
                   await sendToSpecifiedUser(fastify, userRole.userId, user.institutionId, payload)
                }
                reply.success({ message: '角色权限菜单更新成功' })
                
            } catch (error) {
                fastify.log.error(error)
                throw new INTERNAL_ERROR(error.message || '更新角色权限菜单失败')
            }
            
        }
    })
}