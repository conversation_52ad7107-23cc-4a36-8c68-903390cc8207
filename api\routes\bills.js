export default function (fastify, options) {
    // 获取账单
    fastify.get('/bills', {
        schema: {
            tags: ['bills'],
            summary: '获取账单列表',
            querystring: {
                type: 'object',
                properties: {
                    page: {
                        type: 'number',
                        default: 1
                    },
                    pageSize: {
                        type: 'number',
                        default: 10
                    },
                    startTime: { type: 'string' },
                    endTime: { type: 'string' },
                    source: {
                        type: 'string'
                    },
                    operator: {
                        type: 'string'
                    },
                    billType: {
                        type: 'string',
                        // enum: ['income', 'expense']
                    },
                    status: {
                        type: 'string',
                        // enum: ['pending', 'completed', 'failed', 'refunded']
                    },
                    paymentMethod: {
                        type: 'string'
                    }
                },
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;
            const client = fastify.prisma;
            const { page = 1, pageSize = 10, startTime, endTime, operator,
                billType, status, paymentMethod, source } = request.query;
            const where = {
                ...(operator && { operatorId: operator }),
                ...(billType && { billType }),
                ...(status && { status }),
                ...(paymentMethod && { paymentMethod }),
                ...(source && { source }),
                institutionId: user.institutionId
            }

            if (startTime && endTime) {
                where.paymentTime = {
                    gte: Number.parseInt(startTime),
                    lte: Number.parseInt(endTime)
                }
            }
            const skip = (page - 1) * pageSize;
            const take = pageSize;

            const [bills, total] = await Promise.all([
                client.bill.findMany({
                    where,
                    select: {
                        id: true,
                        amount: true,
                        billType: true,
                        status: true,
                        source: true,
                        paymentMethod: true,
                        paymentTime: true,
                        operatorTime: true,
                        remarks: true,
                        operator: {
                            select: {
                                id: true,
                                name: true
                            }
                        }


                    },
                    skip,
                    take,
                    orderBy: {
                        paymentTime: 'desc'
                    }
                }),
                client.bill.count({
                    where
                })
            ])

            // 处理账单数据
            bills.forEach(bill => {
                bill.paymentTime = Number(bill.paymentTime);
                bill.operatorTime = Number(bill.operatorTime);
            })

            reply.success({
                message: '获取账单列表成功.',
                data: {
                    list: bills,
                    total,
                    page,
                    pageSize
                }

            })
        },
    })
    // 创建账单
    fastify.post('/bills', {
        schema: {
            tags: ['bills'],
            summary: '创建账单',
            body: {
                type: 'object',
                properties: {
                    amount: { type: 'number' }, // 金额
                    paymentMethod: { type: 'string' }, // 支付方式
                    paymentTime: { type: 'string' }, // 支付时间
                    operator: { type: 'string' }, // 操作人
                    studentId: { type: 'string' }, // 学生id
                    productId: { type: 'string' }, // 产品id
                    source: { type: 'string' }, // 途径
                    type: {
                        type: 'string',
                        enum: ['income', 'expense']
                    }, // 类型
                    remarks: { type: 'string' }, // 备注
                    
                },
                required: ['amount', 'type']
            },
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;
            const client = fastify.prisma;
            try {
                const { studentId, productId, amount, type, source,
                    remarks, paymentMethod, paymentTime, operator } = request.body;
                const bill = await client.bill.create({
                    data: {
                        ...(studentId && { studentId }),
                        ...(productId && { productId }),
                        amount,
                        billType: type,
                        source,
                        ...(remarks && { remarks }),
                        paymentMethod,
                        paymentTime: paymentTime ? paymentTime : new Date().getTime(),
                        operatorId: operator ? operator : user.id,
                        operatorTime: new Date().getTime(),
                        institutionId: user.institutionId
                    },
                });

                reply.success({
                    message: '账单创建成功.',
                })
            } catch (error) {
                reply.status(500).send({ message: error.message });
            }
        },
    })


    // 获取统计图表
    fastify.get('/bills/statistics', {
        schema: {
            tags: ['bills']
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;

        }
    })


    // 生成图表
    fastify.get('/bills/annual-data', {
        schema: {
            querystring: {
                type: 'object',
                properties: {
                    type: { type: 'string', enum: ['monthly', 'yearly', 'range'], default: 'yearly' },
                    startDate: { type: 'number'},
                    endDate: { type: 'number'},
                    label: { type: 'string', enum: ['income', 'expense', 'all'], default: 'all' },
                    year: { type: 'integer', nullable: true },
                    month: { type: 'integer', minimum: 1, maximum: 12, nullable: true },
                },
                additionalProperties: false,
            }
        },
        handler: async (request, reply) => {
            try {
                const { type, startDate, endDate, label, year, month } = request.query;
                const currentDate = new Date();
                const targetYear = year || currentDate.getFullYear();
                const targetMonth = month ? month - 1 : currentDate.getMonth();
                const currentYear = currentDate.getFullYear();
                const currentMonth = currentDate.getMonth();
                
                // Determine date range
                let startTimestamp, endTimestamp;
                
                if (type === 'range' && startDate && endDate) {
                    startTimestamp = new Date(startDate).getTime();
                    endTimestamp = new Date(endDate).getTime();
                } else if (type === 'monthly') {
                    startTimestamp = new Date(targetYear, targetMonth, 1).getTime();
                    endTimestamp = new Date(targetYear, targetMonth + 1, 1).getTime();
                } else { // yearly
                    startTimestamp = new Date(targetYear, 0, 1).getTime();
                    endTimestamp = new Date(targetYear === currentYear ? 
                        targetYear : currentMonth + 1, 1 ,
                        targetYear + 1, 0, 1).getTime();
                }
                
                // Query data
                const bills = await fastify.prisma.bill.findMany({ 
                    where: {
                        paymentTime: { gte: startTimestamp, lt: endTimestamp },
                        status: 'completed',
                        ...(label !== 'all' && { billType: label })
                    }
                });
                
                // Process chart data
                const { labels, datasets } = processChartData(
                    bills, type, label, startTimestamp, endTimestamp, 
                    targetYear, targetMonth, currentYear, currentMonth
                );
                reply.success({
                    data: {
                        labels, 
                        datasets,
                        metadata: { year: targetYear, month, type, isCurrentYear: targetYear === currentYear }
                    },
                    message: "获取数据成功."
                })
            
            } catch (error) {
                request.log.error(error);
                reply.code(500).send({ error: 'Internal server error' });
            }
        }
    });
    
    function processChartData(bills, type, label, startTimestamp, endTimestamp, year, month, currentYear, currentMonth) {
        // Generate labels
        let labels;
        
        if (type === 'range') {
            const start = new Date(startTimestamp);
            const end = new Date(endTimestamp);
            const daysInRange = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
            
            labels = Array.from({ length: daysInRange }, (_, i) => {
                const date = new Date(start);
                date.setDate(start.getDate() + i);
                return `${date.getMonth() + 1}-${date.getDate()}`;
            });
        } else if (type === 'monthly') {
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            labels = Array.from({ length: daysInMonth }, (_, i) => i + 1);
        } else { // yearly
            const monthLabels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
            labels = year === currentYear ? monthLabels.slice(0, currentMonth + 1) : monthLabels;
        }
        
        // Initialize datasets
        const datasets = [];
        const dataMap = {};
        
        if (label === 'all' || label === 'expense') {
            const expenseData = new Array(labels.length).fill(0);
            datasets.push({ label: '支出', data: expenseData });
            dataMap['expense'] = expenseData;
        }
        
        if (label === 'all' || label === 'income') {
            const incomeData = new Array(labels.length).fill(0);
            datasets.push({ label: '收入', data: incomeData });
            dataMap['income'] = incomeData;
        }
        
        // Populate data
        bills.forEach(bill => {
            const billTime = Number(bill.paymentTime);
            const billAmount = Number(bill.amount);
            let index;
            
            if (type === 'range') {
                index = Math.floor((billTime - startTimestamp) / (1000 * 60 * 60 * 24));
            } else if (type === 'monthly') {
                index = new Date(billTime).getDate() - 1;
            } else { // yearly
                index = new Date(billTime).getMonth();
            }
            
            if (index >= 0 && index < labels.length && dataMap[bill.billType]) {
                dataMap[bill.billType][index] += billAmount;
            }
        });
        
        return { labels, datasets };
    }

}
