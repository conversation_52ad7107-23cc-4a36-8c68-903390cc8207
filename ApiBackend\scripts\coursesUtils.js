/**
 * Courses Utility Functions
 * Contains utility functions for course-related operations
 */

/**
 * Format course data for response
 * @param {Object} courseData - Course data from database
 * @returns {Object} Formatted course data
 */
export function formatCourseData(courseData) {
    if (!courseData) return null;
    
    // Extract products from ProductCourse relation
    const products = courseData.ProductCourse 
        ? courseData.ProductCourse.map(pc => ({
            id: pc.product.id,
            name: pc.product.name,
            ...(pc.product.price !== undefined && { price: pc.product.price })
        }))
        : [];
    
    // Create formatted course object
    const formattedCourse = {
        ...courseData,
        products
    };
    
    // Remove ProductCourse relation
    delete formattedCourse.ProductCourse;
    
    return formattedCourse;
}

/**
 * Format course list for response
 * @param {Array} courses - Array of course data from database
 * @returns {Array} Formatted course list
 */
export function formatCourseList(courses) {
    if (!courses || !Array.isArray(courses)) return [];
    
    return courses.map(course => formatCourseData(course));
}

/**
 * Get course type text
 * @param {string} type - Course type code
 * @returns {string} Course type text
 */
export function getCourseTypeText(type) {
    const typeMap = {
        'group': '小组课',
        'one-on-one': '一对一',
        'online': '线上课',
        'offline': '线下课'
    };
    
    return typeMap[type] || type;
}

/**
 * Get course status text
 * @param {string} status - Status code
 * @returns {string} Status text
 */
export function getCourseStatusText(status) {
    const statusMap = {
        'active': '正常',
        'inactive': '停用',
        'deleted': '已删除'
    };
    
    return statusMap[status] || status;
}

/**
 * Format duration to hours and minutes
 * @param {number} duration - Duration in minutes
 * @returns {string} Formatted duration
 */
export function formatDuration(duration) {
    if (!duration) return '0分钟';
    
    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;
    
    if (hours > 0 && minutes > 0) {
        return `${hours}小时${minutes}分钟`;
    } else if (hours > 0) {
        return `${hours}小时`;
    } else {
        return `${minutes}分钟`;
    }
}

/**
 * Calculate total classes from course hours
 * @param {number} courseHours - Course hours
 * @param {number} deductionPerClass - Deduction per class
 * @returns {number} Total classes
 */
export function calculateTotalClasses(courseHours, deductionPerClass = 1) {
    if (!courseHours || !deductionPerClass) return 0;
    
    return Math.floor(courseHours / deductionPerClass);
}

/**
 * Check if course is associated with product
 * @param {Object} course - Course data
 * @param {string} productId - Product ID
 * @returns {boolean} Whether the course is associated with the product
 */
export function isAssociatedWithProduct(course, productId) {
    if (!course || !course.products || !Array.isArray(course.products) || !productId) {
        return false;
    }
    
    return course.products.some(product => product.id === productId);
}

/**
 * Filter courses by criteria
 * @param {Array} courses - Array of course data
 * @param {Object} criteria - Filter criteria
 * @returns {Array} Filtered course list
 */
export function filterCourses(courses, criteria) {
    if (!courses || !Array.isArray(courses)) return [];
    if (!criteria) return courses;
    
    return courses.filter(course => {
        // Filter by name
        if (criteria.name && !course.name.toLowerCase().includes(criteria.name.toLowerCase())) {
            return false;
        }
        
        // Filter by type
        if (criteria.type && course.type !== criteria.type) {
            return false;
        }
        
        // Filter by status
        if (criteria.status && course.status !== criteria.status) {
            return false;
        }
        
        // Filter by direct sale
        if (criteria.isDirectSale !== undefined && course.isDirectSale !== criteria.isDirectSale) {
            return false;
        }
        
        // Filter by product
        if (criteria.productId && !isAssociatedWithProduct(course, criteria.productId)) {
            return false;
        }
        
        return true;
    });
}

/**
 * Sort courses by field
 * @param {Array} courses - Array of course data
 * @param {string} field - Field to sort by
 * @param {string} order - Sort order ('asc' or 'desc')
 * @returns {Array} Sorted course list
 */
export function sortCourses(courses, field, order = 'asc') {
    if (!courses || !Array.isArray(courses)) return [];
    if (!field) return courses;
    
    return [...courses].sort((a, b) => {
        let valueA = a[field];
        let valueB = b[field];
        
        // Handle string fields
        if (typeof valueA === 'string') {
            valueA = valueA.toLowerCase();
        }
        if (typeof valueB === 'string') {
            valueB = valueB.toLowerCase();
        }
        
        // Sort by the field
        if (valueA < valueB) return order === 'asc' ? -1 : 1;
        if (valueA > valueB) return order === 'asc' ? 1 : -1;
        return 0;
    });
}

/**
 * Group courses by type
 * @param {Array} courses - Array of course data
 * @returns {Object} Grouped courses
 */
export function groupCoursesByType(courses) {
    if (!courses || !Array.isArray(courses)) return {};
    
    const groupedCourses = {};
    
    courses.forEach(course => {
        const type = course.type || 'other';
        
        if (!groupedCourses[type]) {
            groupedCourses[type] = [];
        }
        
        groupedCourses[type].push(course);
    });
    
    return groupedCourses;
}

/**
 * Calculate course price based on hours
 * @param {number} basePrice - Base price per hour
 * @param {number} hours - Course hours
 * @param {number} discount - Discount percentage (0-100)
 * @returns {number} Calculated price
 */
export function calculateCoursePrice(basePrice, hours, discount = 0) {
    if (!basePrice || !hours) return 0;
    
    const totalPrice = basePrice * hours;
    const discountAmount = totalPrice * (discount / 100);
    
    return totalPrice - discountAmount;
}

/**
 * Get deduction rules text
 * @param {Object} course - Course data
 * @returns {string} Deduction rules text
 */
export function getDeductionRulesText(course) {
    if (!course) return '';
    
    const rules = [];
    
    if (course.isDeductOnAttendance) {
        rules.push('出勤扣课时');
    }
    
    if (course.isDeductOnLeave) {
        rules.push('请假扣课时');
    }
    
    if (course.isDeductOnAbsence) {
        rules.push('缺勤扣课时');
    }
    
    return rules.join('，');
}

export default {
    formatCourseData,
    formatCourseList,
    getCourseTypeText,
    getCourseStatusText,
    formatDuration,
    calculateTotalClasses,
    isAssociatedWithProduct,
    filterCourses,
    sortCourses,
    groupCoursesByType,
    calculateCoursePrice,
    getDeductionRulesText
};
