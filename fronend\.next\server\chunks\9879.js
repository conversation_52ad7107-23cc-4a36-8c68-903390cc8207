exports.id=9879,exports.ids=[9879],exports.modules={1134:(e,t,n)=>{Promise.resolve().then(n.bind(n,21891)),Promise.resolve().then(n.bind(n,14238))},4780:(e,t,n)=>{"use strict";n.d(t,{cn:()=>r});var a=n(49384),s=n(82348);function r(...e){return(0,s.QP)((0,a.$)(e))}},14238:(e,t,n)=>{"use strict";n.d(t,{default:()=>b});var a=n(60687),s=n(43210),r=n.n(s),l=n(85814),o=n.n(l),i=n(16189),d=n(4780),c=n(3589),u=n(78272),f=n(14952),m=n(47033);let h={ChartBarIcon:(0,s.lazy)(()=>n.e(5994).then(n.bind(n,45994)).then(e=>({default:e.default}))),ClipboardDocumentCheckIcon:(0,s.lazy)(()=>n.e(7446).then(n.bind(n,27446)).then(e=>({default:e.default}))),DocumentTextIcon:(0,s.lazy)(()=>n.e(1245).then(n.bind(n,61245)).then(e=>({default:e.default}))),CalendarDaysIcon:(0,s.lazy)(()=>n.e(1022).then(n.bind(n,61022)).then(e=>({default:e.default}))),BookOpenIcon:(0,s.lazy)(()=>n.e(8582).then(n.bind(n,8582)).then(e=>({default:e.default}))),ReceiptPercentIcon:(0,s.lazy)(()=>n.e(150).then(n.bind(n,70150)).then(e=>({default:e.default}))),BanknotesIcon:(0,s.lazy)(()=>n.e(714).then(n.bind(n,30714)).then(e=>({default:e.default}))),BellIcon:(0,s.lazy)(()=>Promise.resolve().then(n.bind(n,52238)).then(e=>({default:e.default}))),InboxIcon:(0,s.lazy)(()=>n.e(3917).then(n.bind(n,23917)).then(e=>({default:e.default}))),PaperAirplaneIcon:(0,s.lazy)(()=>n.e(7421).then(n.bind(n,7421)).then(e=>({default:e.default}))),AcademicCapIcon:(0,s.lazy)(()=>n.e(5428).then(n.bind(n,45428)).then(e=>({default:e.default}))),UserGroupIcon:(0,s.lazy)(()=>n.e(3635).then(n.bind(n,93635)).then(e=>({default:e.default}))),BuildingOfficeIcon:(0,s.lazy)(()=>n.e(8855).then(n.bind(n,8855)).then(e=>({default:e.default}))),ChartPieIcon:(0,s.lazy)(()=>n.e(1377).then(n.bind(n,61377)).then(e=>({default:e.default}))),ShoppingBagIcon:(0,s.lazy)(()=>n.e(6719).then(n.bind(n,76719)).then(e=>({default:e.default}))),BriefcaseIcon:(0,s.lazy)(()=>n.e(2733).then(n.bind(n,12733)).then(e=>({default:e.default}))),TagIcon:(0,s.lazy)(()=>n.e(6453).then(n.bind(n,96453)).then(e=>({default:e.default}))),CurrencyDollarIcon:(0,s.lazy)(()=>n.e(1082).then(n.bind(n,31082)).then(e=>({default:e.default}))),InformationCircleIcon:(0,s.lazy)(()=>n.e(9127).then(n.bind(n,99127)).then(e=>({default:e.default})))},x=({name:e,className:t="h-6 w-6",fallback:n=null,...r})=>{let[l,o]=(0,s.useState)(null),[i,d]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{h[e]?(o(h[e]),d(!1)):(console.warn(`图标 "${e}" 未在预设映射中定义`),d(!0))},[e]),i||!l)?n||(0,a.jsx)("span",{className:t}):(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)("span",{className:t}),children:(0,a.jsx)(l,{className:t,"aria-hidden":"true",...r})})};var p=n(30596);let b=r().memo(()=>{let[e,t]=(0,s.useState)(!1),[n,r]=(0,s.useState)([]),l=(0,i.usePathname)(),h=(0,p.G)(e=>e.userMenus.menus)||[],b=(0,s.useMemo)(()=>h,[h]);(0,s.useEffect)(()=>{let e=((e,t)=>{let n=[],a=(e,s,r)=>{for(let r of e){if(r.path===t)return n.push(...s),!0;if(r.children?.length&&a(r.children,[...s,r.id],r.path||""))return!0}return!1};return a(e,[],""),n})(b,l);e.length>0&&r(t=>Array.from(new Set([...t,...e])))},[l,b]);let g=(0,s.useCallback)(e=>{r(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},[]),v=(0,s.useCallback)(e=>!!e&&(l===e||l.startsWith(e+"/")),[l]),y=(0,s.useCallback)((t,s=0)=>{let r=t.children&&t.children.length>0,l=n.includes(t.id),i=v(t.path),f=(0,a.jsxs)("div",{className:"flex items-center flex-1 min-w-0",children:[(0,a.jsx)("div",{className:(0,d.cn)("flex-shrink-0 w-5 h-5",i&&"text-blue-600",e&&"mx-auto"),children:(0,a.jsx)(x,{name:t.icon,className:(0,d.cn)("h-5 w-5",i&&"text-blue-600")})}),!e&&(0,a.jsx)("span",{className:(0,d.cn)("ml-3 truncate",i&&"font-medium text-blue-600"),children:t.name})]});return(0,a.jsxs)("div",{className:(0,d.cn)("w-full",s>0&&"pl-4"),children:[t.path?(0,a.jsx)(o(),{href:t.path,className:(0,d.cn)("flex items-center w-full p-2 rounded-lg transition-colors duration-200","hover:bg-gray-100",i?"bg-blue-50 text-blue-600":"text-gray-700 hover:text-gray-900",e&&"justify-center"),children:f}):(0,a.jsx)("button",{onClick:()=>g(t.id),className:(0,d.cn)("flex items-center w-full p-2 rounded-lg transition-colors duration-200","hover:bg-gray-100 text-gray-700 hover:text-gray-900",e&&"justify-center",l&&"bg-gray-50"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between w-full",children:[f,!e&&r&&(0,a.jsx)("div",{className:"flex-shrink-0 ml-2",children:l?(0,a.jsx)(c.A,{className:"w-4 h-4"}):(0,a.jsx)(u.A,{className:"w-4 h-4"})})]})}),r&&l&&!e&&(0,a.jsx)("div",{className:"mt-1 space-y-1 animate-slideDown",children:t.children?.map(e=>y(e,s+1))})]},t.id)},[n,v,e,g]);return(0,a.jsx)("nav",{className:(0,d.cn)("bg-white shadow-xl transition-all duration-300 ease-in-out border-r border-gray-200 relative group",e?"w-16":"w-64"),children:(0,a.jsxs)("div",{className:(0,d.cn)("sticky top-0 flex flex-col h-full ",e?"py-2 px-1 pt-16":"p-4 pt-16"),children:[(0,a.jsx)("div",{className:"flex-1 space-y-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent",children:b.map(e=>y(e))}),(0,a.jsx)("button",{onClick:()=>t(!e),className:(0,d.cn)("absolute top-1/2 -right-3 w-6 h-6 bg-white border border-gray-200 rounded-full","flex items-center justify-center text-gray-500 hover:text-gray-700","transform -translate-y-1/2 shadow-sm hover:shadow transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"),children:e?(0,a.jsx)(f.A,{className:"w-4 h-4"}):(0,a.jsx)(m.A,{className:"w-4 h-4"})})]})})})},15046:(e,t,n)=>{Promise.resolve().then(n.bind(n,24597)),Promise.resolve().then(n.bind(n,36733))},21342:(e,t,n)=>{"use strict";n.d(t,{I:()=>f,SQ:()=>m,_2:()=>h,hO:()=>x,lp:()=>p,mB:()=>b,rI:()=>c,ty:()=>u});var a=n(60687),s=n(43210),r=n(26312),l=n(14952),o=n(13964),i=n(65822),d=n(4780);let c=r.bL,u=r.l9,f=r.YJ;r.ZL,r.Pb,r.z6,s.forwardRef(({className:e,inset:t,children:n,...s},o)=>(0,a.jsxs)(r.ZP,{ref:o,className:(0,d.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...s,children:[n,(0,a.jsx)(l.A,{className:"ml-auto"})]})).displayName=r.ZP.displayName,s.forwardRef(({className:e,...t},n)=>(0,a.jsx)(r.G5,{ref:n,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=r.G5.displayName;let m=s.forwardRef(({className:e,sideOffset:t=4,...n},s)=>(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{ref:s,sideOffset:t,className:(0,d.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));m.displayName=r.UC.displayName;let h=s.forwardRef(({className:e,inset:t,...n},s)=>(0,a.jsx)(r.q7,{ref:s,className:(0,d.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...n}));h.displayName=r.q7.displayName;let x=s.forwardRef(({className:e,children:t,checked:n,...s},l)=>(0,a.jsxs)(r.H_,{ref:l,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...s,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})}),t]}));x.displayName=r.H_.displayName,s.forwardRef(({className:e,children:t,...n},s)=>(0,a.jsxs)(r.hN,{ref:s,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=r.hN.displayName;let p=s.forwardRef(({className:e,inset:t,...n},s)=>(0,a.jsx)(r.JU,{ref:s,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...n}));p.displayName=r.JU.displayName;let b=s.forwardRef(({className:e,...t},n)=>(0,a.jsx)(r.wv,{ref:n,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));b.displayName=r.wv.displayName},21891:(e,t,n)=>{"use strict";n.d(t,{default:()=>m});var a=n(60687);n(43210);var s=n(85814),r=n.n(s),l=n(52238),o=n(30596),i=n(21342),d=n(29523),c=n(54864),u=n(4637),f=n(7112);function m(){let{data:e}=(0,u.K)(),t=(0,o.G)(e=>e.user),n=(0,o.G)(e=>e.notificationsUnreadCount);(0,c.wA)();let{logout:s}=(0,f.A)();return console.log(t,"userssss"),(0,a.jsx)("header",{className:"fixed top-0 left-0 right-0 h-14 z-50 bg-blue-500",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-full px-4 text-white",children:[(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsx)(r(),{href:"#",className:"text-lg font-semibold",children:t.institutionName?t.institutionName:"蜜卡"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsx)(d.$,{variant:"ghost",size:"icon",asChild:!0,className:"relative",children:(0,a.jsxs)(r(),{href:"/notifications/list",children:[(0,a.jsx)(l.default,{className:"h-5 w-5"}),n.count>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 flex items-center justify-center min-w-[18px] h-[18px] px-1 text-[10px] font-medium rounded-full bg-red-500 text-white border border-blue-500",children:n.count>99?"99+":n.count})]})}),(0,a.jsxs)(i.rI,{children:[(0,a.jsx)(i.ty,{asChild:!0,children:(0,a.jsx)(d.$,{variant:"ghost",className:"flex items-center space-x-2 h-8 p-0 hover:bg-transparent focus:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 group",children:(0,a.jsx)("div",{className:"flex flex-col items-start ml-2",children:(0,a.jsx)("span",{className:"hidden sm:inline-block font-medium text-sm leading-tight max-w-[100px] truncate group-hover:text-blue-100 transition-colors",children:t.name||"用户"})})})}),(0,a.jsxs)(i.SQ,{align:"end",className:"w-56",children:[(0,a.jsx)(i.lp,{children:(0,a.jsx)("div",{className:"flex flex-col space-y-1",children:(0,a.jsx)("p",{className:"text-sm font-medium",children:t.name||"用户"})})}),(0,a.jsx)(i.mB,{}),(0,a.jsxs)(i.I,{children:[(0,a.jsx)(i._2,{onClick:()=>console.log("个人信息"),children:"个人信息"}),(0,a.jsx)(i._2,{onClick:()=>console.log("账号设置"),children:"账号设置"})]}),(0,a.jsx)(i.mB,{}),(0,a.jsx)(i._2,{onClick:s,className:"text-destructive focus:text-destructive",children:"退出登录"})]})]})]})]})})}},24597:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});let a=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\components\\Header.tsx","default")},29523:(e,t,n)=>{"use strict";n.d(t,{$:()=>d,r:()=>i});var a=n(60687),s=n(43210),r=n(8730),l=n(24224),o=n(4780);let i=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef(({className:e,variant:t,size:n,asChild:s=!1,...l},d)=>{let c=s?r.DX:"button";return(0,a.jsx)(c,{className:(0,o.cn)(i({variant:t,size:n,className:e})),ref:d,...l})});d.displayName="Button"},36733:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});let a=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\components\\\\Sidebar\\\\index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\components\\Sidebar\\index.tsx","default")}};