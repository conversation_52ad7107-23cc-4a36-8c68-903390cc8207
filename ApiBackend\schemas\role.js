/**
 * Role Schemas
 * Defines JSON schemas for role API endpoints
 */
const roleSchema = {
  // Schema for getting all roles
  getAllRolesSchema: {
    tags: ['roles'],
    summary: '获取所有角色列表',
    description: '获取所有角色列表',
    querystring: {
      type: 'object',
      properties: {
        page: { type: 'number' },
        pageSize: { type: 'number' },
        search: { type: 'string' }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          message: { type: 'string' },
          code: { type: 'number' },
          data: {
            type: 'object',
            properties: {
              list: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    description: { type: 'string', nullable: true }
                  }
                }
              },
              total: { type: 'number' },
              page: { type: 'number' },
              pageSize: { type: 'number' }
            }
          }
        }
      }
    }
  },

  // Schema for creating a role
  createRoleSchema: {
    tags: ['roles'],
    summary: '创建新角色',
    description: '创建新角色',
    body: {
      type: 'object',
      required: ['name'],
      properties: {
        name: { type: 'string' },
        description: { type: 'string' }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          success: { type: 'boolean' },
          message: { type: 'string' },
          data: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              code: { type: 'string' },
              description: { type: 'string', nullable: true },
              createdAt: { type: 'string' },
              isInstitutionRole: { type: 'boolean' }
            }
          }
        }
      }
    }
  },

  // Schema for deleting a role
  deleteRoleSchema: {
    tags: ['roles'],
    summary: '删除角色',
    description: '删除角色',
    params: {
      type: 'object',
      required: ['roleId'],
      properties: {
        roleId: {
          type: 'string',
          description: '角色ID'
        }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          success: { type: 'boolean' },
          message: { type: 'string' }
        }
      }
    }
  },

  // Schema for updating a role
  updateRoleSchema: {
    tags: ['roles'],
    summary: '更新角色信息',
    description: '更新指定角色，同时进行权限和依赖检查',
    params: {
      type: 'object',
      required: ['roleId'],
      properties: {
        roleId: {
          type: 'string',
          description: '角色ID'
        }
      }
    },
    body: {
      type: 'object',
      required: ['name'],
      properties: {
        name: { type: 'string' },
        description: { type: 'string' }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          success: { type: 'boolean' },
          message: { type: 'string' }
        }
      }
    }
  },
    getRolePermissionsSchema: {
    tags: ['roles'],
    summary: '获取角色权限',
    description: '获取指定角色的所有权限',
    params: {
      type: 'object',
      properties: {
        roleId: { type: 'string' }
      },
      required: ['roleId']
    },
    response: {
      200: {
        type: 'object',
        properties: {
          success: { type: 'boolean' },
          message: { type: 'string' },
          data: {
            type: 'object',
            properties: {
              role: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  code: { type: 'string' },
                  description: { type: 'string', nullable: true }
                }
              },
              permissions: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    code: { type: 'string' },
                    description: { type: 'string', nullable: true }
                  }
                }
              }
            }
          }
        }
      }
    }
  },

  // Schema for assigning role permissions
  assignRolePermissionsSchema: {
    tags: ['roles'],
    summary: '分配角色权限',
    description: '为指定角色分配权限',
    params: {
      type: 'object',
      properties: {
        roleId: { type: 'string' }
      },
      required: ['roleId']
    },
    body: {
      type: 'object',
      properties: {
        permissionIds: {
          type: 'array',
          items: { type: 'string' }
        },
        replaceExisting: {
          type: 'boolean',
          default: false
        }
      },
      required: ['permissionIds']
    },
    response: {
      200: {
        type: 'object',
        properties: {
          success: { type: 'boolean' },
          message: { type: 'string' },
          data: {
            type: 'object',
            properties: {
              roleId: { type: 'string' },
              assignedPermissions: { type: 'integer' }
            }
          }
        }
      }
    }
  },

  // Schema for removing role permissions
  removeRolePermissionsSchema: {
    tags: ['roles'],
    summary: '移除角色权限',
    description: '从指定角色移除权限',
    params: {
      type: 'object',
      properties: {
        roleId: { type: 'string' }
      },
      required: ['roleId']
    },
    body: {
      type: 'object',
      properties: {
        permissionIds: {
          type: 'array',
          items: { type: 'string' }
        }
      },
      required: ['permissionIds']
    },
    response: {
      200: {
        type: 'object',
        properties: {
          success: { type: 'boolean' },
          message: { type: 'string' },
          data: {
            type: 'object',
            properties: {
              roleId: { type: 'string' },
              removedPermissions: { type: 'integer' }
            }
          }
        }
      }
    }
  },

  // Schema for updating role permissions based on menu IDs
  updateRolePermissionsSchema: {
    tags: ['roles'],
    summary: '更新角色权限菜单',
    description: '更新指定角色的权限菜单',
    params: {
      type: 'object',
      properties: {
        roleId: { type: 'string' }
      },
      required: ['roleId']
    },
    body: {
      type: 'object',
      required: ['permissions'],
      properties: {
        permissions: {
          type: 'array',
          items: {
            type: 'string',
            description: '菜单ID'
          }
        }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          success: { type: 'boolean' },
          message: { type: 'string' },
          data: {
            type: 'object',
            properties: {
              roleId: { type: 'string' },
              updatedMenus: { type: 'integer' },
              removedPermissions: { type: 'integer' },
              addedPermissions: { type: 'integer' }
            }
          }
        }
      }
    }
  }
};

export default roleSchema;