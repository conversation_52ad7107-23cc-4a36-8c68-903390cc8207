import { createError } from '@fastify/error';
import buildMenuTree from '../utils/buildMenuTree.js';

const AUTH_ERROR = createError('AUTH_ERROR', '%s', 401);
const INTERNAL_ERROR = createError('INTERNAL_ERROR', '%s', 500);

// 公共：移除菜单树中的字段
function removeFieldsFromTree(tree, removeFields) {
    if (Array.isArray(tree)) {
        tree.forEach(item => removeFieldsFromTree(item, removeFields));
    } else if (tree && typeof tree === 'object') {
        for (const key in tree) {
            if (removeFields.includes(key)) {
                delete tree[key];
            } else if (typeof tree[key] === 'object') {
                removeFieldsFromTree(tree[key], removeFields);
            }
        }
    }
}

// 公共：获取权限代码
async function getPermissionCodes(client, userId) {
    // 优化查询：使用索引提示和限制返回字段
    const query = `
        SELECT DISTINCT p.code
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp."permissionId"
        JOIN roles r ON rp."roleId" = r.id
        JOIN user_roles ur ON r.id = ur."roleId"
        WHERE ur."userId" = $1
        /* 索引提示 */
        ORDER BY p.code
    `;

    const result = await client.query(query, [userId]);
    return new Set(result.rows.map(row => row.code));
}

// 公共：获取所有菜单
async function fetchAllMenus(client) {
    // 优化查询：添加索引提示和查询计划提示
    const query = `
        SELECT id, name, path, icon, sort, hidden, "permissionId", "parentId",
               component, "permissionCode", redirect
        FROM menus
        /* 强制使用索引扫描 */
        ORDER BY sort ASC
        /* 提示查询优化器使用索引 */
    `;

    // 设置语句超时，防止长时间运行
    await client.query('SET statement_timeout = 5000');
    const result = await client.query(query);
    // 恢复默认超时
    await client.query('SET statement_timeout = 0');

    return result.rows;
}

// 公共：根据权限过滤菜单
function filterMenusByPermissions(menus, permissionCodes) {
    return menus.filter(menu =>
        !menu.permissionCode || permissionCodes.has(menu.permissionCode)
    );
}

// 公共：处理菜单树
function processMenuTree(menus) {
    const menuTree = buildMenuTree(menus);

    // 递归遍历移除component、parentId、permissionId
    const removeFields = ['component', 'parentId', 'permissionId', 'redirect', 'permissionCode'];
    removeFieldsFromTree(menuTree, removeFields);

    // 根据sort排序
    menuTree.sort((a, b) => a.sort - b.sort);

    return menuTree;
}

async function getUserMenus(fastify, user) {
    // 添加缓存支持
    const cacheKey = `user:menus:${user.id}`;
    const cacheTTL = 300; // 缓存5分钟

    try {
        // 尝试从缓存获取
        const cachedMenus = await fastify.redis.get(cacheKey);
        if (cachedMenus) {
            return JSON.parse(cachedMenus);
        }

        // 缓存未命中，从数据库获取
        const client = await fastify.pg.connect();
        try {
            // 记录开始时间，用于性能监控
            const startTime = process.hrtime.bigint();

            await client.query('BEGIN');

            // 获取用户权限代码
            const permissionCodes = await getPermissionCodes(client, user.id);

            // 获取所有菜单
            const allMenus = await fetchAllMenus(client);

            // 根据权限过滤菜单
            const filteredMenus = filterMenusByPermissions(allMenus, permissionCodes);

            // 处理菜单树
            const menuTree = processMenuTree(filteredMenus);

            await client.query('COMMIT');

            // 计算查询耗时
            const endTime = process.hrtime.bigint();
            const duration = Number(endTime - startTime) / 1000000; // 转换为毫秒

            // 记录性能指标
            fastify.log.debug({
                msg: 'Menu query performance',
                userId: user.id,
                duration: `${duration.toFixed(2)}ms`,
                menuCount: filteredMenus.length
            });

            // 将结果存入缓存
            const result = menuTree || [];
            await fastify.redis.set(cacheKey, JSON.stringify(result), 'EX', cacheTTL);

            return result;
        } catch (error) {
            await client.query('ROLLBACK');
            fastify.log.error({
                msg: 'Failed to get user menus',
                userId: user.id,
                error: error.message,
                stack: error.stack
            });

            if (error instanceof AUTH_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(error.message || '获取用户菜单失败');
        } finally {
            client.release();
        }
    } catch (error) {
        // 缓存操作失败，记录错误但继续尝试数据库查询
        if (!(error instanceof AUTH_ERROR || error instanceof INTERNAL_ERROR)) {
            fastify.log.error({
                msg: 'Cache operation failed in getUserMenus',
                error: error.message
            });

            // 尝试从数据库获取（无缓存）
            return getUserMenusNoCache(fastify, user);
        }
        throw error;
    }
}

// 无缓存版本的菜单获取函数（作为缓存失败的后备方案）
async function getUserMenusNoCache(fastify, user) {
    const client = await fastify.pg.connect();
    try {
        await client.query('BEGIN');

        // 获取用户权限代码
        const permissionCodes = await getPermissionCodes(client, user.id);

        // 获取所有菜单
        const allMenus = await fetchAllMenus(client);

        // 根据权限过滤菜单
        const filteredMenus = filterMenusByPermissions(allMenus, permissionCodes);

        // 处理菜单树
        const menuTree = processMenuTree(filteredMenus);

        await client.query('COMMIT');
        return menuTree || [];
    } catch (error) {
        await client.query('ROLLBACK');
        fastify.log.error(error);
        if (error instanceof AUTH_ERROR) {
            throw error;
        }
        throw new INTERNAL_ERROR(error.message || '获取用户菜单失败');
    } finally {
        client.release();
    }
}

async function getAllMenus(fastify, user) {
    const client = await fastify.pg.connect();
    try {
        await client.query('BEGIN');

        // 获取机构管理员
        const adminQuery = `
            SELECT "userId"
            FROM user_institution
            WHERE "institutionId" = $1 AND "isAdmin" = true
            LIMIT 1
        `;
        const adminResult = await client.query(adminQuery, [user.institutionId]);

        if (adminResult.rows.length === 0) {
            throw new AUTH_ERROR('未找到机构管理员');
        }

        const adminUserId = adminResult.rows[0].userId;

        // 获取管理员权限代码
        const permissionCodes = await getPermissionCodes(client, adminUserId);

        // 获取所有菜单
        const allMenus = await fetchAllMenus(client);

        // 根据权限过滤菜单
        const filteredMenus = filterMenusByPermissions(allMenus, permissionCodes);

        // 处理菜单树
        const menuTree = processMenuTree(filteredMenus);

        await client.query('COMMIT');
        return menuTree;
    } catch (error) {
        await client.query('ROLLBACK');
        fastify.log.error(error);
        if (error instanceof AUTH_ERROR) {
            throw error;
        }
        throw new INTERNAL_ERROR(error.message || '获取菜单失败');
    } finally {
        client.release();
    }
}

async function getRoleMenus(fastify, roleId) {
    const client = await fastify.pg.connect();
    try {
        await client.query('BEGIN');

        // 获取角色权限代码
        const rolePermissionsQuery = `
            SELECT p.code
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp."permissionId"
            WHERE rp."roleId" = $1
        `;
        const rolePermissionsResult = await client.query(rolePermissionsQuery, [roleId]);
        const permissionCodes = new Set(rolePermissionsResult.rows.map(row => row.code));

        // 获取所有菜单
        const allMenus = await fetchAllMenus(client);

        // 根据权限过滤菜单
        const filteredMenus = filterMenusByPermissions(allMenus, permissionCodes);

        // 处理菜单树
        const menuTree = processMenuTree(filteredMenus);

        await client.query('COMMIT');
        return menuTree;
    } catch (error) {
        await client.query('ROLLBACK');
        fastify.log.error(error);
        if (error instanceof AUTH_ERROR) {
            throw error;
        }
        throw new INTERNAL_ERROR(error.message || '获取角色菜单失败');
    } finally {
        client.release();
    }
}

export default {
    getUserMenus,
    getAllMenus,
    getRoleMenus
}
