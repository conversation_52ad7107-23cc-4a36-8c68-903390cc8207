import fp from "fastify-plugin";
import { createError } from "@fastify/error";
import crypto from "crypto";

const AUTH_ERROR = createError('AUTH_ERROR', '%s', 401);
const FORBIDDEN = createError('FORBIDDEN', '%s', 403);
const TOKEN_EXPIRED = createError('TOKEN_EXPIRED', '%s', 401);

async function authMiddleware(fastify, _opts) {
    // 通过token验证用户信息
    async function verifyToken(request) {
        // 提取并验证令牌
        const token = request.headers.authorization?.replace('Bearer ', '');
        if (!token) {
            throw new AUTH_ERROR('未提供令牌');
        }

        try {
            // 验证JWT令牌
            let decoded;
            try {
                decoded = await fastify.jwt.verify(token);
            } catch (err) {
                // 处理特定的JWT错误
                if (err.name === 'TokenExpiredError') {
                    throw new TOKEN_EXPIRED('令牌已过期，请重新登录');
                }
                throw new AUTH_ERROR('无效令牌');
            }

            // 缓存键
            const cacheKey = `user:${decoded.id}:authData`;
            const tokenHashKey = `user:${decoded.id}:tokenHash`;

            // 注意：不再使用数据库黑名单，改用Redis token hash验证

            // 使用管道批处理Redis命令，减少网络往返
            const [cachedUserResult, tokenHashResult] = await fastify.redis.multi()
                .get(cacheKey)
                .get(tokenHashKey)
                .exec();

            // 检查令牌是否已被吊销（通过比较哈希值）
            const cachedTokenHash = tokenHashResult[1];

            const currentTokenHash = crypto.createHash('sha256').update(token).digest('hex');

            // 如果Redis中有token hash，则必须匹配；如果没有，则允许通过（可能是新登录）
            if (cachedTokenHash && cachedTokenHash !== currentTokenHash) {
                throw new AUTH_ERROR('令牌已被吊销，请重新登录');
            }

            // 检查缓存中是否有用户数据
            const cachedUser = cachedUserResult[1];
            if (cachedUser) {
                // 缓存命中
                // 更新缓存过期时间（延长会话）
                await fastify.redis.expire(cacheKey, 3600); // 1小时
                return JSON.parse(cachedUser);
            }

            // 获取数据库连接
            const client = await fastify.pg.connect();

            try {
                // 优化查询：使用事务和预编译语句
                await client.query('BEGIN');

                // 使用单一查询获取用户、角色和权限信息
                const userQuery = `
                    SELECT
                        u.id,
                        u.account,
                        ARRAY_AGG(DISTINCT r.id) AS role_ids,
                        ARRAY_AGG(DISTINCT rp."permissionId") AS permission_ids,
                        EXISTS (
                            SELECT 1
                            FROM user_roles ur
                            JOIN roles r ON ur."roleId" = r.id
                            WHERE ur."userId" = u.id AND r.code = 'SYSTEM_ADMIN'
                        ) AS is_super_admin,
                        (
                            SELECT ARRAY_AGG(DISTINCT ui."institutionId")
                            FROM user_institution ui
                            WHERE ui."userId" = u.id
                        ) AS institution_ids
                    FROM users u
                    LEFT JOIN user_roles ur ON ur."userId" = u.id
                    LEFT JOIN roles r ON ur."roleId" = r.id
                    LEFT JOIN role_permissions rp ON rp."roleId" = r.id
                    WHERE u.id = $1 AND u.active = true
                    GROUP BY u.id
                `;

                const result = await client.query(userQuery, [decoded.id]);

                if (result.rows.length === 0 || !result.rows[0]) {
                    throw new AUTH_ERROR('用户或角色不存在');
                }

                const user = result.rows[0];

                // 获取权限代码
                const operationsQuery = `
                    SELECT DISTINCT code
                    FROM permissions
                    WHERE id = ANY($1)
                `;

                const operationsResult = await client.query(operationsQuery, [user.permission_ids]);

                // 提交事务
                await client.query('COMMIT');

                // 构建用户数据
                const userData = {
                    id: user.id,
                    account: user.account,
                    SYSTEM_ADMIN: user.is_super_admin,
                    institutionId: user.institution_ids?.[0] || null,
                    operations: operationsResult.rows.map(op => op.code),
                    lastAuthenticated: new Date().toISOString()
                };

                // 使用管道批处理Redis命令
                await fastify.redis.multi()
                    .set(cacheKey, JSON.stringify(userData), 'EX', 3600) // 缓存1小时
                    .set(tokenHashKey, currentTokenHash, 'EX', 86400) // 令牌哈希缓存24小时
                    .exec();

                return userData;
            } catch (error) {
                // 回滚事务
                await client.query('ROLLBACK');
                throw error;
            } finally {
                // 释放数据库连接
                client.release();
            }
        } catch (error) {
            // 记录详细错误信息
            fastify.log.error({
                msg: '授权错误',
                error: error.message,
                stack: error.stack,
                path: request.url,
                method: request.method
            });

            // 重新抛出适当的错误
            if (error.code === 'AUTH_ERROR' || error.code === 'TOKEN_EXPIRED') {
                throw error;
            }
            throw new AUTH_ERROR('无效令牌');
        }
    }

    // 权限检查函数 - 优化版本
    function requirePermission(permissionCode) {
        // 支持多个权限码（任一满足即可）
        const codes = Array.isArray(permissionCode) ? permissionCode : [permissionCode];

        return async (request, _reply) => {
            // 超级管理员跳过权限检查
            if (request.user.SYSTEM_ADMIN) {
                return;
            }
            // 检查用户是否有任一所需权限
            const hasPermission = codes.some(code =>
                request.user.operations && request.user.operations.includes(code)
            );

            if (!hasPermission) {
                throw new FORBIDDEN(`没有所需权限: ${codes.join(', ')}`);
            }
        };
    }

    // 认证中间件 - 优化版本
    async function authenticate(request, _reply) {
        try {
            const user = await verifyToken(request);
            request.user = user;
        } catch (error) {
            // 添加更友好的错误消息
            if (error.code === 'TOKEN_EXPIRED') {
                throw new TOKEN_EXPIRED('您的登录已过期，请重新登录');
            }
            throw error;
        }
    }

    // 注销/吊销令牌
    async function revokeToken(userId, token) {
        if (!userId || !token) {
            throw new Error('用户ID和令牌都是必需的');
        }

        try {
            // 获取当前存储的令牌哈希键和用户缓存键
            const tokenHashKey = `user:${userId}:tokenHash`;
            const cacheKey = `user:${userId}:authData`;

            // 使用管道批处理Redis命令
            await fastify.redis.multi()
                // 生成新的随机哈希值，使当前令牌无效
                .set(tokenHashKey, crypto.randomBytes(16).toString('hex'), 'EX', 86400) // 24小时
                // 清除用户缓存数据
                .del(cacheKey)
                .exec();

            // 注意：不再使用数据库黑名单，Redis token hash已足够

            return true;
        } catch (error) {
            fastify.log.error({
                msg: '吊销令牌失败',
                error: error.message,
                userId
            });
            return false;
        }
    }

    // 装饰Fastify实例
    fastify.decorate('auth', {
        authenticate,
        requirePermission,
        revokeToken
    });
}


export default fp(authMiddleware, {
    name: 'auth-middleware',
    dependencies: ['@fastify/jwt']
});