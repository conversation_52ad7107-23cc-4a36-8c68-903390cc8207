"use strict";exports.id=5586,exports.ids=[5586],exports.modules={16709:(e,t,r)=>{r.d(t,{Q:()=>f});var a=r(60687),s=r(43210),i=r(4780),n=r(96834),l=r(24224);let d=(0,l.F)("",{variants:{variant:{default:"border-b flex -mb-px space-x-6",underline:"relative flex overflow-x-auto",pill:"bg-muted p-1 rounded-lg flex mb-4",vertical:"w-48 shrink-0 border-r pr-4 flex flex-col space-y-1"}},defaultVariants:{variant:"default"}}),o=(0,l.F)("transition-colors",{variants:{variant:{default:"py-2 border-b-2 font-medium text-sm flex items-center gap-2",underline:"py-2 mr-8 font-medium text-sm transition-colors relative flex items-center gap-2",pill:"flex-1 py-1.5 px-3 text-sm font-medium rounded-md flex items-center justify-center gap-2",vertical:"flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md text-left"},state:{active:"",inactive:""}},compoundVariants:[{variant:"default",state:"active",className:"border-primary text-primary"},{variant:"default",state:"inactive",className:"border-transparent text-muted-foreground hover:text-foreground hover:border-border"},{variant:"underline",state:"active",className:"text-primary"},{variant:"underline",state:"inactive",className:"text-muted-foreground hover:text-foreground"},{variant:"pill",state:"active",className:"bg-background text-foreground shadow-sm"},{variant:"pill",state:"inactive",className:"text-muted-foreground hover:text-foreground"},{variant:"vertical",state:"active",className:"bg-accent text-accent-foreground"},{variant:"vertical",state:"inactive",className:"text-muted-foreground hover:bg-muted hover:text-foreground"}],defaultVariants:{variant:"default",state:"inactive"}}),c=(0,l.F)("",{variants:{variant:{default:"py-4",underline:"py-4",pill:"",vertical:"flex-1"}},defaultVariants:{variant:"default"}}),u=s.memo(({tab:e,isActive:t,showBadges:r})=>t?e.content?(0,a.jsx)(a.Fragment,{children:e.content}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:e.label}),r&&void 0!==e.badge&&(0,a.jsx)(n.E,{variant:"default",children:e.badge})]}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:["这是 ",e.label," 标签页的内容区域。",r&&void 0!==e.badge&&` 您有 ${e.badge} 个未读${e.label}。`]})]}):null,(e,t)=>!e.isActive&&!t.isActive||e.isActive===t.isActive&&e.tab.id===t.tab.id&&e.showBadges===t.showBadges);u.displayName="TabContent";let v=s.memo(({tab:e,isActive:t,variant:r,showIcons:s,showBadges:l,onClick:d})=>(0,a.jsxs)("button",{onClick:d,disabled:e.disabled,className:(0,i.cn)(o({variant:r,state:t?"active":"inactive"}),e.disabled&&"opacity-50 cursor-not-allowed"),children:[s&&e.icon,(0,a.jsx)("span",{children:e.label}),l&&void 0!==e.badge&&(0,a.jsx)(n.E,{variant:t?"default":"secondary",className:"ml-1 px-1.5 py-0.5 h-5",children:e.badge}),"underline"===r&&t&&(0,a.jsx)("span",{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-primary"})]}));function f({tabs:e,defaultTab:t,onChange:r,className:n,variant:l="default",showIcons:o=!0,showBadges:f=!0}){let m=s.useRef(!1),[x,g]=s.useState(""),p=s.useCallback(e=>{e!==x&&(g(e),r?.(e))},[x,r]);s.useEffect(()=>{m.current||(m.current=!0,g(t||e[0]?.id||""))},[t,e]);let b=s.useMemo(()=>e.reduce((e,t)=>(e.set(t.id,t),e),new Map),[e]),h=s.useMemo(()=>new Set(e.map(e=>e.id)),[e]),y=s.useRef(new Map);s.useEffect(()=>{Array.from(y.current.keys()).filter(e=>!h.has(e)).forEach(e=>y.current.delete(e)),e.forEach(e=>{y.current.has(e.id)||y.current.set(e.id,()=>p(e.id))})},[e,h,p]);let N=s.useCallback(e=>y.current.get(e)||(()=>p(e)),[p]),{isVertical:j,showUnderlineBorder:A}=s.useMemo(()=>({isVertical:"vertical"===l,showUnderlineBorder:"underline"===l}),[l]),w=s.useMemo(()=>{let e=b.get(x);return e?(0,a.jsx)(u,{tab:e,isActive:!0,showBadges:f},x):null},[x,b,f]);return s.useEffect(()=>{},[e.length,x]),(0,a.jsxs)("div",{className:(0,i.cn)(j?"flex gap-8":"w-full",n),children:[(0,a.jsxs)("div",{className:d({variant:l}),children:[A&&(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-border"}),e.map(e=>(0,a.jsx)(v,{tab:e,isActive:x===e.id,variant:l,showIcons:o,showBadges:f,onClick:N(e.id)},e.id))]}),(0,a.jsx)("div",{className:c({variant:l}),children:w})]})}v.displayName="TabButton"},53541:(e,t,r)=>{r.d(t,{LQ:()=>i});var a=r(60687),s=r(30596);let i=({permission:e,children:t,fallback:r=null,logic:i="any"})=>{let n=(0,s.G)(e=>e.userPermissions.permissions);if(!e||Array.isArray(e)&&0===e.length)return(0,a.jsx)(a.Fragment,{children:t});let l=Array.isArray(e)?e:[e],d=!1;return("all"===i?l.every(e=>n.includes(e)):l.some(e=>n.includes(e)))?(0,a.jsx)(a.Fragment,{children:t}):(0,a.jsx)(a.Fragment,{children:r})};r(29523);var n=r(43210),l=r(24224),d=r(4780);let o=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}});n.forwardRef(({className:e,variant:t,...r},s)=>(0,a.jsx)("div",{ref:s,role:"alert",className:(0,d.cn)(o({variant:t}),e),...r})).displayName="Alert",n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("h5",{ref:r,className:(0,d.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle",n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,d.cn)("text-sm [&_p]:leading-relaxed",e),...t})).displayName="AlertDescription"},80250:(e,t,r)=>{r.d(t,{J:()=>s});var a=r(30596);function s(){let e=(0,a.G)(e=>e.userPermissions?.permissions||[]);return{hasPermission:(t,r="any")=>{if(!t||Array.isArray(t)&&0===t.length)return!0;if(!Array.isArray(e))return!1;let a=Array.isArray(t)?t:[t];return"all"===r?a.every(t=>e.includes(t)):a.some(t=>e.includes(t))}}}},96834:(e,t,r)=>{r.d(t,{E:()=>l});var a=r(60687);r(43210);var s=r(24224),i=r(4780);let n=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:t}),e),...r})}}};