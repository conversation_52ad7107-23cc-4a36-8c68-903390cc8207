"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6162],{4884:(e,t,r)=>{r.d(t,{bL:()=>w,zi:()=>j});var o=r(12115),n=r(85185),a=r(6101),l=r(46081),i=r(5845),s=r(45503),d=r(11275),c=r(63655),u=r(95155),p="Switch",[f,v]=(0,l.A)(p),[h,g]=f(p),x=o.forwardRef((e,t)=>{let{__scopeSwitch:r,name:l,checked:s,defaultChecked:d,required:p,disabled:f,value:v="on",onCheckedChange:g,form:x,...m}=e,[y,w]=o.useState(null),j=(0,a.s)(t,e=>w(e)),R=o.useRef(!1),k=!y||x||!!y.closest("form"),[D=!1,P]=(0,i.i)({prop:s,defaultProp:d,onChange:g});return(0,u.jsxs)(h,{scope:r,checked:D,disabled:f,children:[(0,u.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":D,"aria-required":p,"data-state":C(D),"data-disabled":f?"":void 0,disabled:f,value:v,...m,ref:j,onClick:(0,n.m)(e.onClick,e=>{P(e=>!e),k&&(R.current=e.isPropagationStopped(),R.current||e.stopPropagation())})}),k&&(0,u.jsx)(b,{control:y,bubbles:!R.current,name:l,value:v,checked:D,required:p,disabled:f,form:x,style:{transform:"translateX(-100%)"}})]})});x.displayName=p;var m="SwitchThumb",y=o.forwardRef((e,t)=>{let{__scopeSwitch:r,...o}=e,n=g(m,r);return(0,u.jsx)(c.sG.span,{"data-state":C(n.checked),"data-disabled":n.disabled?"":void 0,...o,ref:t})});y.displayName=m;var b=e=>{let{control:t,checked:r,bubbles:n=!0,...a}=e,l=o.useRef(null),i=(0,s.Z)(r),c=(0,d.X)(t);return o.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(i!==r&&t){let o=new Event("click",{bubbles:n});t.call(e,r),e.dispatchEvent(o)}},[i,r,n]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...a,tabIndex:-1,ref:l,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function C(e){return e?"checked":"unchecked"}var w=x,j=y},5623:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},15452:(e,t,r)=>{r.d(t,{G$:()=>H,Hs:()=>C,UC:()=>et,VY:()=>eo,ZL:()=>Q,bL:()=>Y,bm:()=>en,hE:()=>er,hJ:()=>ee,l9:()=>$});var o=r(12115),n=r(85185),a=r(6101),l=r(46081),i=r(61285),s=r(5845),d=r(19178),c=r(25519),u=r(34378),p=r(28905),f=r(63655),v=r(92293),h=r(93795),g=r(38168),x=r(99708),m=r(95155),y="Dialog",[b,C]=(0,l.A)(y),[w,j]=b(y),R=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,c=o.useRef(null),u=o.useRef(null),[p=!1,f]=(0,s.i)({prop:n,defaultProp:a,onChange:l});return(0,m.jsx)(w,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};R.displayName=y;var k="DialogTrigger",D=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,l=j(k,r),i=(0,a.s)(t,l.triggerRef);return(0,m.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":W(l.open),...o,ref:i,onClick:(0,n.m)(e.onClick,l.onOpenToggle)})});D.displayName=k;var P="DialogPortal",[A,O]=b(P,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:a}=e,l=j(P,t);return(0,m.jsx)(A,{scope:t,forceMount:r,children:o.Children.map(n,e=>(0,m.jsx)(p.C,{present:r||l.open,children:(0,m.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=P;var N="DialogOverlay",F=o.forwardRef((e,t)=>{let r=O(N,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=j(N,e.__scopeDialog);return a.modal?(0,m.jsx)(p.C,{present:o||a.open,children:(0,m.jsx)(I,{...n,ref:t})}):null});F.displayName=N;var I=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=j(N,r);return(0,m.jsx)(h.A,{as:x.DX,allowPinchZoom:!0,shards:[n.contentRef],children:(0,m.jsx)(f.sG.div,{"data-state":W(n.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),_="DialogContent",M=o.forwardRef((e,t)=>{let r=O(_,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=j(_,e.__scopeDialog);return(0,m.jsx)(p.C,{present:o||a.open,children:a.modal?(0,m.jsx)(G,{...n,ref:t}):(0,m.jsx)(q,{...n,ref:t})})});M.displayName=_;var G=o.forwardRef((e,t)=>{let r=j(_,e.__scopeDialog),l=o.useRef(null),i=(0,a.s)(t,r.contentRef,l);return o.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,m.jsx)(L,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),q=o.forwardRef((e,t)=>{let r=j(_,e.__scopeDialog),n=o.useRef(!1),a=o.useRef(!1);return(0,m.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,l;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(n.current||null===(l=r.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),n.current=!1,a.current=!1},onInteractOutside:t=>{var o,l;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let i=t.target;(null===(l=r.triggerRef.current)||void 0===l?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),L=o.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,u=j(_,r),p=o.useRef(null),f=(0,a.s)(t,p);return(0,v.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,m.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":W(u.open),...s,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(K,{titleId:u.titleId}),(0,m.jsx)(J,{contentRef:p,descriptionId:u.descriptionId})]})]})}),T="DialogTitle",B=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=j(T,r);return(0,m.jsx)(f.sG.h2,{id:n.titleId,...o,ref:t})});B.displayName=T;var S="DialogDescription",Z=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=j(S,r);return(0,m.jsx)(f.sG.p,{id:n.descriptionId,...o,ref:t})});Z.displayName=S;var z="DialogClose",U=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,a=j(z,r);return(0,m.jsx)(f.sG.button,{type:"button",...o,ref:t,onClick:(0,n.m)(e.onClick,()=>a.onOpenChange(!1))})});function W(e){return e?"open":"closed"}U.displayName=z;var X="DialogTitleWarning",[H,V]=(0,l.q)(X,{contentName:_,titleName:T,docsSlug:"dialog"}),K=e=>{let{titleId:t}=e,r=V(X),n="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return o.useEffect(()=>{t&&!document.getElementById(t)&&console.error(n)},[n,t]),null},J=e=>{let{contentRef:t,descriptionId:r}=e,n=V("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return o.useEffect(()=>{var e;let o=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&o&&!document.getElementById(r)&&console.warn(a)},[a,t,r]),null},Y=R,$=D,Q=E,ee=F,et=M,er=B,eo=Z,en=U},20547:(e,t,r)=>{r.d(t,{UC:()=>U,ZL:()=>z,bL:()=>S,l9:()=>Z});var o=r(12115),n=r(85185),a=r(6101),l=r(46081),i=r(19178),s=r(92293),d=r(25519),c=r(61285),u=r(35152),p=r(34378),f=r(28905),v=r(63655),h=r(99708),g=r(5845),x=r(38168),m=r(93795),y=r(95155),b="Popover",[C,w]=(0,l.A)(b,[u.Bk]),j=(0,u.Bk)(),[R,k]=C(b),D=e=>{let{__scopePopover:t,children:r,open:n,defaultOpen:a,onOpenChange:l,modal:i=!1}=e,s=j(t),d=o.useRef(null),[p,f]=o.useState(!1),[v=!1,h]=(0,g.i)({prop:n,defaultProp:a,onChange:l});return(0,y.jsx)(u.bL,{...s,children:(0,y.jsx)(R,{scope:t,contentId:(0,c.B)(),triggerRef:d,open:v,onOpenChange:h,onOpenToggle:o.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:p,onCustomAnchorAdd:o.useCallback(()=>f(!0),[]),onCustomAnchorRemove:o.useCallback(()=>f(!1),[]),modal:i,children:r})})};D.displayName=b;var P="PopoverAnchor";o.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,a=k(P,r),l=j(r),{onCustomAnchorAdd:i,onCustomAnchorRemove:s}=a;return o.useEffect(()=>(i(),()=>s()),[i,s]),(0,y.jsx)(u.Mz,{...l,...n,ref:t})}).displayName=P;var A="PopoverTrigger",O=o.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,l=k(A,r),i=j(r),s=(0,a.s)(t,l.triggerRef),d=(0,y.jsx)(v.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":B(l.open),...o,ref:s,onClick:(0,n.m)(e.onClick,l.onOpenToggle)});return l.hasCustomAnchor?d:(0,y.jsx)(u.Mz,{asChild:!0,...i,children:d})});O.displayName=A;var E="PopoverPortal",[N,F]=C(E,{forceMount:void 0}),I=e=>{let{__scopePopover:t,forceMount:r,children:o,container:n}=e,a=k(E,t);return(0,y.jsx)(N,{scope:t,forceMount:r,children:(0,y.jsx)(f.C,{present:r||a.open,children:(0,y.jsx)(p.Z,{asChild:!0,container:n,children:o})})})};I.displayName=E;var _="PopoverContent",M=o.forwardRef((e,t)=>{let r=F(_,e.__scopePopover),{forceMount:o=r.forceMount,...n}=e,a=k(_,e.__scopePopover);return(0,y.jsx)(f.C,{present:o||a.open,children:a.modal?(0,y.jsx)(G,{...n,ref:t}):(0,y.jsx)(q,{...n,ref:t})})});M.displayName=_;var G=o.forwardRef((e,t)=>{let r=k(_,e.__scopePopover),l=o.useRef(null),i=(0,a.s)(t,l),s=o.useRef(!1);return o.useEffect(()=>{let e=l.current;if(e)return(0,x.Eq)(e)},[]),(0,y.jsx)(m.A,{as:h.DX,allowPinchZoom:!0,children:(0,y.jsx)(L,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),s.current||null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;s.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),q=o.forwardRef((e,t)=>{let r=k(_,e.__scopePopover),n=o.useRef(!1),a=o.useRef(!1);return(0,y.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,l;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(n.current||null===(l=r.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),n.current=!1,a.current=!1},onInteractOutside:t=>{var o,l;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let i=t.target;(null===(l=r.triggerRef.current)||void 0===l?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),L=o.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:o,onOpenAutoFocus:n,onCloseAutoFocus:a,disableOutsidePointerEvents:l,onEscapeKeyDown:c,onPointerDownOutside:p,onFocusOutside:f,onInteractOutside:v,...h}=e,g=k(_,r),x=j(r);return(0,s.Oh)(),(0,y.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:n,onUnmountAutoFocus:a,children:(0,y.jsx)(i.qW,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:v,onEscapeKeyDown:c,onPointerDownOutside:p,onFocusOutside:f,onDismiss:()=>g.onOpenChange(!1),children:(0,y.jsx)(u.UC,{"data-state":B(g.open),role:"dialog",id:g.contentId,...x,...h,ref:t,style:{...h.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),T="PopoverClose";function B(e){return e?"open":"closed"}o.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,a=k(T,r);return(0,y.jsx)(v.sG.button,{type:"button",...o,ref:t,onClick:(0,n.m)(e.onClick,()=>a.onOpenChange(!1))})}).displayName=T,o.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,n=j(r);return(0,y.jsx)(u.i3,{...n,...o,ref:t})}).displayName="PopoverArrow";var S=D,Z=O,z=I,U=M},24357:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(19946).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>i});var o=r(12115),n=r(63655),a=r(95155),l=o.forwardRef((e,t)=>(0,a.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},47924:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},54416:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},84109:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(19946).A)("RefreshCcw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]])}}]);