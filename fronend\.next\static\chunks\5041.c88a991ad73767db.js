"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5041],{5041:(e,a,t)=>{t.r(a),t.d(a,{default:()=>j});var l=t(95155),r=t(12115),s=t(9110),n=t(50228),c=t(61809);let i=[{accessorKey:"studentName",header:"姓名",cell:e=>{let{row:a}=e;return(0,n.P)(a.original.studentName)}},{accessorKey:"productName",header:"产品名称",cell:e=>{let{row:a}=e;return(0,n.P)(a.original.productName)}},{accessorKey:"beforeCount",header:"调整前次数",cell:e=>{let{row:a}=e;return(0,n.P)(a.original.beforeCount)}},{accessorKey:"afterCount",header:"调整后次数",cell:e=>{let{row:a}=e;return(0,n.P)(a.original.afterCount)}},{accessorKey:"type",header:"操作类型",cell:e=>{let{row:a}=e,t="add"===a.original.type?"增加":"reduce"===a.original.type?"减少":"未知",r="add"===a.original.type?"text-emerald-600":"reduce"===a.original.type?"text-rose-600":"text-slate-600";return(0,l.jsx)("div",{className:"".concat(r," text-sm font-medium"),children:t})}},{accessorKey:"attendanceCount",header:"调整课时数",cell:e=>{let{row:a}=e;return(0,n.P)(a.original.count)}},{accessorKey:"attendanceAmount",header:"调整产品时间",cell:e=>{let{row:a}=e;return(0,n.P)(a.original.days)}},{accessorKey:"operatorTime",header:"操作时间",cell:e=>{let{row:a}=e;return(0,n.P)((0,c.Y)(a.original.operatorTime,"yyyy-MM-dd HH:mm:ss"))}},{accessorKey:"operatorName",header:"操作人",cell:e=>{let{row:a}=e;return(0,n.P)(a.original.operatorName)}},{accessorKey:"beforeDays",header:"调整前天数",cell:e=>{let{row:a}=e;return(0,n.P)(a.original.beforeDays)}},{accessorKey:"afterDays",header:"调整后天数",cell:e=>{let{row:a}=e;return(0,n.P)(a.original.afterDays)}},{accessorKey:"remarks",header:"备注",cell:e=>{let{row:a}=e;return(0,n.s)(a.original.remarks)}}];var d=t(48432),o=t(62523),u=t(66695),m=t(82007),h=t(58012),g=t(63375),x=t(47924),y=t(1482),f=t(95728),p=t(45964),b=t.n(p);function j(){let[e,a]=(0,r.useState)(1),[t,n]=(0,r.useState)(10),[c,p]=(0,r.useState)(""),[j,v]=(0,r.useState)("today"),[N,w]=(0,r.useState)(void 0),[C,P]=(0,r.useState)("all"),K=(0,r.useCallback)(b()(e=>{p(e),a(1)},500),[]),S=(0,r.useMemo)(()=>{let{startTime:e,endTime:a}=(0,m.A)(j,N);return{search:c,teacherId:"all"===C?"":C,...e&&{startTime:e},...a&&{endTime:a}}},[j,N,c,C]),{data:k,isLoading:D}=(0,f.Jb)(S);return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(u.Zp,{className:"border-slate-200",children:(0,l.jsx)(u.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-end md:justify-between",children:[(0,l.jsx)("div",{className:"w-full md:w-auto md:flex-1 md:max-w-md",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,l.jsx)(o.p,{type:"text",placeholder:"搜索学员姓名",onChange:e=>K(e.target.value),className:"pl-10 bg-slate-50 border-slate-200 focus:bg-white transition-colors"})]})}),(0,l.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(y.A,{className:"h-4 w-4 text-slate-500"}),(0,l.jsx)("span",{className:"text-sm text-slate-500 hidden sm:inline",children:"筛选："})]}),(0,l.jsx)(g.A,{teacher:C,setTeacher:P,width:"w-[140px]"}),(0,l.jsx)(h.A,{timeRange:j,onTimeRangeChange:v,dateRange:N,onDateRangeChange:w,className:"bg-white"})]})]})})}),(0,l.jsx)(s.b,{pagination:!1,loading:D,columns:i,data:(null==k?void 0:k.list)||[]},"studentAttendanceTable"),(0,l.jsx)(d.default,{currentPage:(null==k?void 0:k.page)||1,totalItems:(null==k?void 0:k.total)||0,pageSize:(null==k?void 0:k.pageSize)||10,onPageChange:a,onPageSizeChange:n})]})}},61809:(e,a,t)=>{t.d(a,{Y:()=>n});var l=t(44861),r=t(73168),s=t(24122);let n=function(e){let a,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"yyyy-MM-dd",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(null==e)return n;try{if("string"==typeof e)a=new Date(e);else if("number"==typeof e)a=new Date(e);else{if(!(e instanceof Date))return n;a=e}if(!(0,l.f)(a))return n;return(0,r.GP)(a,t,{locale:s.g})}catch(e){return console.error("Date formatting error:",e),n}}},63375:(e,a,t)=>{t.d(a,{A:()=>i});var l=t(95155),r=t(12115),s=t(59409),n=t(59434),c=t(6658);let i=(0,r.memo)(function(e){let{teacher:a,setTeacher:t,width:i="w-full",placeholder:d="选择人员",className:o="",showAllOption:u=!0,allOptionText:m="全部人员",allOptionValue:h="all",teacherList:g,disabled:x=!1}=e,{data:y,isLoading:f,error:p}=(0,c.X)(),b=(0,r.useCallback)(e=>{t(e)},[t]),j=(0,r.useCallback)(()=>(0,n.cn)("h-9 ".concat(i),o),[i,o]),v=g||y;return(0,l.jsxs)(s.l6,{value:a,onValueChange:b,disabled:x||f,children:[(0,l.jsx)(s.bq,{className:j(),children:(0,l.jsx)(s.yv,{placeholder:d})}),(0,l.jsxs)(s.gC,{children:[p&&(0,l.jsx)(s.eb,{value:"error",disabled:!0,children:String(p)}),f&&(0,l.jsx)(s.eb,{value:"loading",disabled:!0,children:"加载中..."}),!f&&!p&&(0,l.jsxs)(l.Fragment,{children:[u&&(0,l.jsx)(s.eb,{value:h,children:m}),null==v?void 0:v.map(e=>(0,l.jsx)(s.eb,{value:e.id,children:e.name},e.id))]})]})]})})}}]);