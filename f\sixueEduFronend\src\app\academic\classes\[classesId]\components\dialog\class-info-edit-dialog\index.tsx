"use client"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useEffect, useState } from "react"
import useCourses from "@/hooks/useCourses"
import { useTeacher } from "@/hooks/useTeacher"
import { useClasses } from "@/hooks/useClasses"
import { customToast } from "@/lib/toast"
import { useInstitution } from "@/hooks/institution/useInstitution"
import AdvancedOptions from "./advanced-options"

interface ClassInfoEditDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  classes: any
}

// 修改 FormDataType 中的日期类型为字符串
interface FormDataType {
  name: string;
  courseId: string;
  classRoomId: string;
  teacherId: string;
  maxStudentCount: number | string;

  isReserve: boolean;
  appointmentStartTime: string;
  appointmentEndTime: string;
  isQRCodeAttendance: boolean;
  isAutoCheckIn: boolean;
  isOnLeave: boolean;
  leaveDeadline: string;
  isShowWeekCount: boolean;
}

export default function ClassInfoEditDialog({ open, onOpenChange, classes }: ClassInfoEditDialogProps) {
  
  const { getCourseSelectList } = useCourses()
  const { getTeacherListIdAndName } = useTeacher()
  const { updateClasses } = useClasses()
  const { getInstitutionClassroomSelect } = useInstitution()

  const [courses, setCourses] = useState([])
  const [teachers, setTeachers] = useState([])
  const [classrooms, setClassrooms] = useState([])
  // console.log(classes,"classes edit")
  const [formData, setFormData] = useState<FormDataType>({
    name: classes.name,
    courseId: classes.course.id,
    classRoomId: classes.classroom,
    teacherId: classes.teacher.id,
    // 新增高级选项
    isReserve: classes.isReserve,
    appointmentStartTime: classes.appointmentEndTime,
    appointmentEndTime: classes.appointmentEndTime,
    isQRCodeAttendance: classes.isQRCodeAttendance,
    isAutoCheckIn: classes.isAutoCheckIn,
    isOnLeave: classes.isOnLeave,
    leaveDeadline: classes.leaveDeadline,
    isShowWeekCount: classes.isShowWeekCount,
    maxStudentCount: classes.maxStudentCount,
  })
  
  // 控制折叠面板状态
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)
  
  useEffect(()=> {
    if(open) {
      Promise.all([
        getCourseSelectList(),
        getTeacherListIdAndName(),
        getInstitutionClassroomSelect()
      ]).then(([courses, teachers,classRoom]) => {
        setCourses(courses)
        setTeachers(teachers)
        setClassrooms(classRoom)
      })
    }

  },[open])
  
  const handleEditSubmit = () => {
    // 处理表单提交
    updateClasses(classes.id, formData).then((res) => {
      console.log(res)
      customToast.success('班级信息修改成功.')
      onOpenChange(false)
    }).catch((error) => { 
      customToast.error('班级信息修改失败!')

    })
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] ">
        <DialogHeader>
          <DialogTitle className="text-xl">修改班级</DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 py-4 px-2 max-h-[60vh] overflow-y-auto">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">班级名称</label>
              <Input 
                defaultValue={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}  
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">选择课程</label>
              <Select defaultValue={formData.courseId} 
                  onValueChange={(value) => setFormData({...formData, courseId: value})}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {
                    courses.map((course: any) => (
                      <SelectItem key={course.id} value={course.id}>{course.name}</SelectItem>
                    ))
                  }
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">上课教室</label>
              <Select defaultValue={formData.classRoomId} onValueChange={(value) => setFormData({...formData, classRoomId: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="请选择" />
                </SelectTrigger>
                <SelectContent>
                  {
                    classrooms.map((classroom: any) => (
                      <SelectItem key={classroom.id} value={classroom.id}>{classroom.name}</SelectItem>
                    ))
                  }
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">授课老师</label>
              <Select 
              defaultValue={formData.teacherId} 
              onValueChange={(value) => setFormData({...formData, teacherId: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="请选择" />
                </SelectTrigger>
                <SelectContent>
                  {
                    teachers.map((teacher: any) => (
                      <SelectItem key={teacher.id} value={teacher.id}>{teacher.name}</SelectItem>
                    ))
                  }
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-2">
              <label className="text-sm font-medium">最大学员数</label>
              <Input 
                defaultValue={formData.maxStudentCount}
                onChange={(e) => setFormData({...formData, maxStudentCount: e.target.value})}  
              />
            </div>
          
          {/* 高级选项折叠面板 */}
          <AdvancedOptions 
            isAdvancedOpen={isAdvancedOpen}
            setIsAdvancedOpen={setIsAdvancedOpen}
            formData={formData}
            setFormData={setFormData}
          />
        </div>
        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleEditSubmit}>确认</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

