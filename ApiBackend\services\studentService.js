import {
    INTERNAL_ERROR, NOT_FOUND_ERROR, FORBIDDEN_ERROR, UNAUTHORIZED_ERROR, CONFLICT_ERROR, BaseError
} from '../errors/index.js';
// import { TwitterSnowflake } from 'nodejs-snowflake';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcrypt';
import { addMonths } from 'date-fns';


/**
 * 学生服务
 */
export const studentService = {
    /**
     * 获取学生列表
     * @param {Object} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 学生列表和总数
     */
    async getStudentList(server, params) {
        const {
            institutionId,
            page = 1,
            pageSize = 10,
            search,
            follower,
            type = 'formal',
            intentLevel
        } = params;

        // 解析分页参数
        const pageNum = parseInt(page, 10) || 1;
        const limit = parseInt(pageSize, 10) || 10;
        const offset = (pageNum - 1) * limit;

        // 获取数据库连接
        const client = await server.pg.connect();

        try {
            // 构建查询条件
            let whereConditions = ['s."institutionId" = $1'];
            const queryParams = [institutionId];
            let paramIndex = 2;

            if (follower) {
                whereConditions.push(`s."followerId" = $${paramIndex}`);
                queryParams.push(follower);
                paramIndex++;
            }

            if (type !== 'all') {
                whereConditions.push(`s.type = $${paramIndex}`);
                queryParams.push(type);
                paramIndex++;
            }

            if (intentLevel) {
                whereConditions.push(`s."intentLevel" = $${paramIndex}`);
                queryParams.push(intentLevel);
                paramIndex++;
            }

            if (search) {
                whereConditions.push(`(s.name ILIKE $${paramIndex} OR s.phone ILIKE $${paramIndex})`);
                queryParams.push(`%${search}%`);
                paramIndex++;
            }

            const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

            // 查询学生数据
            const studentQuery = `
                SELECT
                    s.id, s.name, s.phone, s.gender, s.address, s.remarks,
                    s.type, s."intentLevel", s."followUpDate", s.birthday,
                    s.status, s.source, s."sourceDesc", s."createdAt",
                    u.id as "followerId", u.name as "followerName"
                FROM
                    students s
                LEFT JOIN
                    users u ON s."followerId" = u.id
                ${whereClause}
                ORDER BY
                    s."createdAt" DESC
                LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
            `;

            // 查询总数
            const countQuery = `
                SELECT
                    COUNT(*) as total
                FROM
                    students s
                ${whereClause}
            `;

            // 记录查询参数和SQL语句，用于调试
            server.log.debug({
                studentQuery,
                countQuery,
                queryParams: [...queryParams, limit, offset],
                countParams: queryParams
            }, 'SQL查询参数');

            // 执行查询
            const [studentResult, countResult] = await Promise.all([
                client.query(studentQuery, [...queryParams, limit, offset]),
                client.query(countQuery, queryParams)
            ]);

            // 处理结果
            const students = studentResult.rows.map(item => {
                // 处理日期字段
                const createdAt = item.createdAt instanceof Date
                    ? item.createdAt.getTime()
                    : typeof item.createdAt === 'string'
                        ? new Date(item.createdAt).getTime()
                        : Number(item.createdAt) || 0;

                return {
                    ...item,
                    birthday: item.birthday ? Number(item.birthday) : '',
                    followUpDate: item.followUpDate ? Number(item.followUpDate) : '',
                    createdAt: createdAt,
                };
            });

            const total = parseInt(countResult.rows[0].total, 10);

            return {
                students,
                total
            };
        } catch (error) {
            // 记录详细错误信息
            server.log.error({
                error: error.message,
                stack: error.stack,
                params: { institutionId, page, pageSize, search, follower, type, intentLevel }
            }, '获取学生列表失败');

            // 检查是否是表不存在错误
            if (error.message && error.message.includes('relation "students" does not exist')) {
                throw new INTERNAL_ERROR('数据库表结构不匹配，请检查数据库配置');
            }

            throw new INTERNAL_ERROR(`获取学生列表失败: ${error.message}`);
        } finally {
            // 释放数据库连接
            client.release();
        }
    },
    /**
     * 获取简易学员列表
     * @param {Object} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 简易学员列表
     */
    async getSimpleStudentList(server, params) {
        const { institutionId, limit, offset, search } = params;
        const client = await server.pg.connect();
        try {
            const conditions = ['s."institutionId" = $1'];
            const queryParams = [institutionId];
            let paramIndex = 2;

            if (search) {
                conditions.push(`(s.name ILIKE $${paramIndex} OR s.phone ILIKE $${paramIndex})`);
                queryParams.push(`%${search}%`);
                paramIndex++;
            }

            const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

            const studentQuery = `
                SELECT
                    s.id, s.name, s.phone
                FROM
                    students s
                ${whereClause}
                ORDER BY
                    s."createdAt" DESC
                LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
            `;

            // Add pagination parameters to queryParams
            queryParams.push(limit || 10); // Default to 10 if limit is not provided
            queryParams.push(offset || 0); // Default to 0 if offset is not provided

            const countQuery = `
                SELECT
                    COUNT(*) as total
                FROM
                    students s
                ${whereClause}
            `;

            // Create a copy of queryParams without the pagination parameters
            const countQueryParams = queryParams.slice(0, -2);

            const [studentResult, countResult] = await Promise.all([
                client.query(studentQuery, queryParams),
                client.query(countQuery, countQueryParams)
            ]);

            const total = parseInt(countResult.rows[0].total, 10);

            return {
                students: studentResult.rows,
                total
            };
        } catch (error) {

            throw new INTERNAL_ERROR(`获取简易学员列表失败: ${error.message}`);
        } finally {
            client.release();
        }
    },

    /**
     * 获取单个学生信息
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 学生信息
     */
    async getStudentById(server, params) {
        const { studentId, institutionId } = params;

        // 获取数据库连接
        const client = await server.pg.connect();

        try {
            // 构建查询条件
            const conditions = ['s."institutionId" = $1'];
            const queryParams = [institutionId];
            let paramIndex = 2;

            if (studentId) {
                conditions.push(`s.id = $${paramIndex}`);
                queryParams.push(studentId);
                paramIndex++;
            }

            const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

            // 查询学生数据
            const studentQuery = `
                SELECT
                    s.id, s.name, s.phone, s.gender, s.address, s.remarks,
                    s.type, s."intentLevel", s."followUpDate", s.birthday,
                    s.status, s.source, s."sourceDesc", s."createdAt",
                    u.id as "followerId", u.name as "followerName"
                FROM
                    students s
                LEFT JOIN
                    users u ON s."followerId" = u.id
                ${whereClause}
            `;
            // 记录查询参数和SQL语句，用于调试
            server.log.debug({
                studentQuery,
                queryParams: [...queryParams],
                countParams: queryParams
            }, 'SQL查询参数');

            // 执行查询
            const [studentResult] = await Promise.all([
                client.query(studentQuery, queryParams)
            ]);

            // 处理结果
            const student = studentResult.rows[0];

            if (!student) {
                throw new INTERNAL_ERROR('学生不存在');
            }
            return student;
        } catch (error) {
            // 记录详细错误信息
            server.log.error({
                error: error.message,
                stack: error.stack,
                params: { studentId, institutionId }
            }, '获取学生信息失败');

            throw new INTERNAL_ERROR(`获取学生信息失败: ${error.message}`);
        } finally {
            // 释放数据库连接
            client.release();
        }
    },
    /**
     * 更新学生信息
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 更新参数
     * @returns {Promise<Object>} 更新后的学生信息
     */
    async updateStudent(server, params) {
        const client = await server.pg.connect();


        const { studentId, institutionId, name, gender, phone, age, birthday, email, balance, points, followUpPerson, followUpDate, source, referrer, address, idCard, school, intentionLevel, parentName, status, remark, type } = params;
        const updateFields = {};

        // Process followUpDate to handle empty string case
        let processedFollowUpDate = followUpDate;
        if (followUpDate === "") {
            processedFollowUpDate = null;
        }

        const fieldMap = {
            name, gender, phone, age, birthday, email, balance, points,
            followUpPerson, followUpDate: processedFollowUpDate, source, referrer, address, idCard,
            school, intentionLevel, parentName, status, remark, type
        };

        Object.entries(fieldMap).forEach(([key, value]) => {
            if (value !== undefined) {
                updateFields[key] = value;
            }
        });
        try {
            if (Object.keys(updateFields).length === 0) {
                const { rows } = await client.query(
                    'SELECT * FROM "students" WHERE id = $1 AND "institutionId" = $2',
                    [studentId, institutionId]
                );
                return rows[0];
            }

            const fields = Object.keys(updateFields).map(field =>
                /[A-Z]/.test(field) ? `"${field}"` : field
            );

            const placeholders = Object.keys(updateFields).map((_, idx) => `$${idx + 3}`);
            const setClause = fields.map((field, idx) => `${field} = ${placeholders[idx]}`).join(', ');

            const query = `
                    UPDATE "students"
                    SET ${setClause}
                    WHERE id = $1 AND "institutionId" = $2
                    RETURNING *
                `;

            const values = [
                studentId,
                institutionId,
                ...Object.values(updateFields)
            ];
            console.log(values)
            const { rows } = await client.query(query, values);

            if (rows.length === 0) {
                throw new INTERNAL_ERROR('学生不存在或更新失败');
            }
            return rows[0];
        } catch (error) {
            // 记录详细错误信息
            server.log.error({
                error: error.message,
                stack: error.stack,
                params: { studentId, institutionId, name, gender, phone, age, birthday, email, balance, points, followUpPerson, followUpDate, source, referrer, address, idCard, school, intentionLevel, parentName, status, remark, type }
            }, '更新学生信息失败');

            throw new INTERNAL_ERROR(`更新学生信息失败: ${error.message}`);
        } finally {
            // 释放数据库连接
            client.release();
        }
    },
    /**
     * 批量删除学员
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 删除参数
     * @returns {Promise<Object>} 删除后的学员列表
     */
    async deleteStudent(server, params) {
        const { studentIds, institutionId } = params;
        const client = await server.pg.connect();
        try {
            const query = `
                DELETE FROM "students"
                WHERE id = ANY($1) AND "institutionId" = $2
            `;
            const values = [studentIds, institutionId];
            await client.query(query, values);
        } catch (error) {
            // 记录详细错误信息
            server.log.error({
                error: error.message,
                stack: error.stack,
                params: { studentIds }
            }, '删除学员失败');

            throw new INTERNAL_ERROR(`删除学员失败: ${error.message}`);
        } finally {
            // 释放数据库连接
            client.release();
        }
    },

    /**
     * 获取学生上课记录
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 学生上课记录
     */
    async getClassesHistory(server, params) {
        const { studentId, institutionId, skip, take, startDate, endDate } = params;
        const client = await server.pg.connect();
        const limit = parseInt(take, 10) || 10;

        try {
            // 记录输入参数
            server.log.info({
                studentId,
                institutionId,
                skip,
                take,
                startDate,
                endDate
            }, '获取学生上课记录参数');

            // 首先检查学生是否存在
            const studentCheckQuery = `
                SELECT COUNT(*) AS count
                FROM "students"
                WHERE id = $1 AND "institutionId" = $2
            `;

            const studentCheckResult = await client.query(studentCheckQuery, [studentId, institutionId]);
            const studentExists = parseInt(studentCheckResult.rows[0].count) > 0;

            server.log.info({
                studentExists,
                studentId,
                institutionId
            }, '学生存在检查');

            if (!studentExists) {
                return {
                    list: [],
                    total: 0,
                    message: '学生不存在'
                };
            }

            // 检查学生是否有上课记录
            const scheduleCheckQuery = `
                SELECT COUNT(*) AS count
                FROM "student_weekly_schedules" "sws"
                WHERE "studentId" = $1 AND "institutionId" = $2
            `;

            const scheduleCheckResult = await client.query(scheduleCheckQuery, [studentId, institutionId]);
            const hasSchedules = parseInt(scheduleCheckResult.rows[0].count) > 0;

            if (!hasSchedules) {
                return {
                    list: [],
                    total: 0,
                    message: '学生没有上课记录'
                };
            }

            const params = [studentId, institutionId];
            const conditions = [
                '"sws"."studentId" = $1',
                '"sws"."institutionId" = $2'
            ];

            let paramIndex = 3;
            if (startDate) {
                // 确保日期格式正确
                let formattedStartDate;
                if (typeof startDate === 'string') {
                    const parsedDate = new Date(startDate);
                    if (isNaN(parsedDate.getTime())) {
                        throw new INTERNAL_ERROR('无效的开始日期格式');
                    }
                    formattedStartDate = parsedDate.getTime();
                } else {
                    formattedStartDate = Number(startDate);
                    if (isNaN(formattedStartDate)) {
                        throw new INTERNAL_ERROR('无效的开始日期格式');
                    }
                }

                conditions.push(`"cs"."startDate" >= $${paramIndex++}`);
                params.push(formattedStartDate);
            }
            if (endDate) {
                // 确保日期格式正确
                let formattedEndDate;
                if (typeof endDate === 'string') {
                    const parsedDate = new Date(endDate);
                    if (isNaN(parsedDate.getTime())) {
                        throw new INTERNAL_ERROR('无效的结束日期格式');
                    }
                    formattedEndDate = parsedDate.getTime();
                } else {
                    formattedEndDate = Number(endDate);
                    if (isNaN(formattedEndDate)) {
                        throw new INTERNAL_ERROR('无效的结束日期格式');
                    }
                }

                conditions.push(`"cs"."startDate" <= $${paramIndex++}`);
                params.push(formattedEndDate);
            }

            params.push(limit, skip);

            const query = `
                WITH filtered_schedules AS (
                    SELECT
                        "sws"."id",
                        "sws"."status",
                        "cs"."id" AS "scheduleId",
                        "cs"."subject",
                        "cs"."startDate",
                        "cs"."startTime",
                        "cs"."endTime",
                        "c"."name" AS "className",
                        "co"."name" AS "courseName",
                        "t"."name" AS "teacherName"
                    FROM "student_weekly_schedules" "sws"
                    INNER JOIN "classes_schedules" "cs" ON "sws"."classesScheduleId" = "cs"."id"
                    LEFT JOIN "classes" "c" ON "cs"."classesId" = "c"."id"
                    LEFT JOIN "courses" "co" ON "cs"."courseId" = "co"."id"
                    LEFT JOIN "users" "t" ON "cs"."teacherId" = "t"."id"
                    WHERE ${conditions.join(' AND ')}
                    ORDER BY "cs"."startDate" DESC
                    LIMIT $${paramIndex++} OFFSET $${paramIndex}
                )
                SELECT * FROM filtered_schedules
            `;

            const countQuery = `
                SELECT COUNT(*) AS total
                FROM "student_weekly_schedules" "sws"
                INNER JOIN "classes_schedules" "cs" ON "sws"."classesScheduleId" = "cs"."id"
                WHERE ${conditions.join(' AND ')}
            `;

            const [scheduleResult, countResult] = await Promise.all([
                client.query(query, params),
                client.query(countQuery, params.slice(0, paramIndex - 2))
            ]);

            const total = parseInt(countResult.rows[0].total);

            return {
                list: scheduleResult.rows,
                total,
            };
        } catch (error) {
            // 记录详细错误信息
            server.log.error({
                error: error.message,
                stack: error.stack,
                params: { studentId, institutionId, skip, take, startDate, endDate }
            }, '获取学生上课记录失败');

            throw new INTERNAL_ERROR(`获取学生上课记录失败: ${error.message}`);
        } finally {
            // 释放数据库连接
            client.release();
        }
    },
    /**
     * 获取学生购买套餐
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 学生购买套餐
     */
    async getProducts(server, params) {
        const { studentId, institutionId, status } = params;
        const client = await server.pg.connect();
        try {
            const query = `
                SELECT
                    "sp"."id",
                    "sp"."startDate",
                    "sp"."endDate",
                    "sp"."totalSessionCount",
                    "sp"."remainingSessionCount",
                    "sp"."enrollmentStatus",
                    "p"."id" AS "productId",
                    "p"."name" AS "productName",
                    "p"."packageType" AS "productPackageType"
                FROM "student_products" "sp"
                LEFT JOIN "products" "p" ON "sp"."productId" = "p"."id"
                WHERE "sp"."studentId" = $1 AND "sp"."institutionId" = $2
                AND ("sp"."enrollmentStatus" = $3 OR $3 = '')
                ORDER BY "sp"."startDate" DESC
            `;
            const values = [studentId, institutionId, status];
            const { rows } = await client.query(query, values);
            console.log(rows, 'rows');
            return rows;
        } catch (error) {
            throw new INTERNAL_ERROR(`获取学生购买套餐失败: ${error.message}`);
        } finally {
            client.release();
        }
    },
    /**
     * 获取学生购买记录
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 学生购买记录
     */
    async getProductsRecords(server, params) {
        const { studentId, institutionId } = params;
        const client = await server.pg.connect();
        try {
            const query = `
                SELECT
                    "spr"."id",
                    "spr"."amount",
                    "spr"."amountPaid",
                    "spr"."amountUnpaid",
                    "spr"."paymentTime",
                    "spr"."paymentMethod",
                    "spr"."discount",
                    "spr"."giftCount",
                    "spr"."giftDays",
                    "spr"."purchaseQuantity",
                    "spr"."status",
                    "spr"."createdAt",
                    "sp"."id" AS "studentProductId",
                    "sp"."startDate" AS "studentProductStartDate",
                    "sp"."endDate" AS "studentProductEndDate",
                    "sp"."totalSessionCount" AS "studentProductTotalSessionCount",
                    "sp"."remainingSessionCount" AS "studentProductRemainingSessionCount",
                    "sp"."enrollmentStatus" AS "studentProductEnrollmentStatus",
                    "sp"."paymentStatus" AS "studentProductPaymentStatus",
                    "sp"."payTime" AS "studentProductPaymentTime",
                    "p"."id" AS "productId",
                    "p"."name" AS "productName",
                    "p"."packageType" AS "productPackageType",
                    "u"."name" AS "operatorName",
                    "us2"."name" AS "salesRepName"
                FROM "student_product_record" "spr"
                LEFT JOIN "student_products" "sp" ON "spr"."studentProductId" = "sp"."id"
                LEFT JOIN "products" "p" ON "sp"."productId" = "p"."id"
                LEFT JOIN "users" "u" ON "spr"."operatorId" = "u"."id"
                LEFT JOIN "users" "us2" ON "sp"."salesRepId" = "us2"."id"
                WHERE "spr"."studentId" = $1 AND "spr"."institutionId" = $2
                ORDER BY "spr"."createdAt" DESC
            `;
            const values = [studentId, institutionId];
            const { rows } = await client.query(query, values);
            console.log(rows, 'rows');
            return rows;
        } catch (error) {
            throw new INTERNAL_ERROR(`获取学生购买记录失败: ${error.message}`);
        } finally {
            client.release();
        }
    },
    /**
     * 获取学生考勤记录
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 学生考勤记录
     */
    async getAttendance(server, params) {
        const { studentId, institutionId, limit, offset } = params;
        const client = await server.pg.connect();
        try {
            // 查询数据列表
            const resultQuery = await client.query(`
    SELECT
      s.id,
      s.status,
      s."attendanceCount",
      s."operatorTime",

      -- 操作人信息
      u.name as "operatorName",

      -- 学生信息
      stu.id as "studentId",
      stu.name as "studentName",

      -- 产品信息
      p.name as "productName",

      -- 排课信息
      cs.id as "scheduleId",
      cs."startDate",
      cs."startTime",
      cs."endTime",
      cs.subject,

      c.name as "courseName",
      cs.name as "classesName"

    FROM "student_weekly_schedules" s
    LEFT JOIN "users" u ON u.id = s."operatorId"
    LEFT JOIN "students" stu ON stu.id = s."studentId"
    LEFT JOIN "student_products" sp ON sp.id = s."studentProductId"
    LEFT JOIN "products" p ON p.id = sp."productId"
    LEFT JOIN "classes_schedules" cs ON cs.id = s."classesScheduleId"
    LEFT JOIN "courses" c ON c.id = cs."courseId"
    LEFT JOIN "classes" cl ON cl.id = cs."classesId"
    WHERE s."studentId" = $1 AND s."institutionId" = $2 AND s.status = 'attendance'
    ORDER BY s."operatorTime" DESC
    LIMIT $3 OFFSET $4
  `, [studentId, institutionId, limit, offset]);

            const countQuery = await client.query(`
    SELECT COUNT(*) FROM "student_weekly_schedules"
    WHERE "studentId" = $1 AND "institutionId" = $2 AND status = 'attendance'
  `, [studentId, institutionId]);

            const rows = resultQuery.rows;
            const total = Number(countQuery.rows[0].count);

            return {
                list: rows,
                total
            }

        } catch (error) {
            throw new INTERNAL_ERROR(`获取学生考勤记录失败: ${error.message}`);
        } finally {
            client.release();
        }
    },
    /**
     * 获取学员班级
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 学员班级
     */
    async getClasses(server, params) {
        const { studentId, institutionId } = params;
        const client = await server.pg.connect();
        try {
            const query = `
                SELECT
                    "sc"."id",
                    "sc"."joinDate",
                    "sc"."operatorTime",
                    "sc"."type",
                    "c"."id" AS "classesId",
                    "c"."name" AS "classesName",
                    "co"."name" AS "courseName",
                    "t"."name" AS "teacherName"
                FROM "student_classes" "sc"
                LEFT JOIN "classes" "c" ON "sc"."classesId" = "c"."id"
                LEFT JOIN "courses" "co" ON "c"."courseId" = "co"."id"
                LEFT JOIN "users" "t" ON "c"."teacherId" = "t"."id"
                WHERE "sc"."studentId" = $1 AND "sc"."institutionId" = $2
            `;
            const values = [studentId, institutionId];
            const { rows } = await client.query(query, values);
            return rows;
        } catch (error) {
            throw new INTERNAL_ERROR(`获取学员班级失败: ${error.message}`);
        } finally {
            client.release();
        }
    },
    /**
     * 学员退出班级
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 学员退出班级
     */
    async outClasses(server, params) {
        const { classesId, studentId, institutionId } = params;
        const client = await server.pg.connect();
        try {
            const query = `
                DELETE FROM "student_classes"
                WHERE "classesId" = $1 AND "studentId" = $2 AND "institutionId" = $3
            `;
            const values = [classesId, studentId, institutionId];
            await client.query(query, values);

            // 查询学员未上课的课时
            const currentDate = new Date().getTime()
            const studentWeeklyScheduleResult = await client.query(`
                SELECT "sws"."id" FROM "student_weekly_schedules" as "sws"
                LEFT JOIN "classes_schedules" as "cs" ON "sws"."classesScheduleId" = "cs"."id"
                WHERE "sws"."studentId" = $1 AND "cs"."institutionId" = $2 AND "cs"."id" = $3 AND "cs"."startDate" > $4
            `, [studentId, institutionId, classesId, currentDate]);
            studentWeeklyScheduleResult.rows.map(async (item) => {
                await client.query(`
                    DELETE FROM "student_weekly_schedules"
                    WHERE "id" = $1
                `, [item.id]);
            })

            return true
        } catch (error) {
            throw new INTERNAL_ERROR(`学员退出班级失败: ${error.message}`);
        } finally {
            client.release();
        }
    },
    /**
     * 获取学员跟进记录
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 学员跟进记录
     */
    async getFollowRecords(server, params) {
        const { studentId, institutionId } = params;
        const client = await server.pg.connect();
        try {
            const query = `
                SELECT
                    "sf"."id",
                    "sf"."followUpDate",
                    "sf"."nextFollowUpDate",
                    "sf"."followUpContent",
                    "u"."name" AS "followUpUserName"
                FROM "student_follow_records" "sf"
                LEFT JOIN "users" "u" ON "sf"."followUpUserId" = "u"."id"
                WHERE "sf"."studentId" = $1 AND "sf"."institutionId" = $2
                ORDER BY "sf"."followUpDate" DESC
            `;
            const values = [studentId, institutionId];
            const { rows } = await client.query(query, values);
            return rows;
        } catch (error) {
            throw new INTERNAL_ERROR(`获取学员跟进记录失败: ${error.message}`);
        } finally {
            client.release();
        }
    },
    /**
     * 新增学员跟进记录
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 新增学员跟进记录
     */
    async addFollowRecords(server, params) {
        const {
            studentId, institutionId, followUpDate, nextFollowUpDate,
            followUpContent, followUpUserId, intentLevel, userId
        } = params;

        try {
            // 使用 Prisma 客户端
            if (intentLevel) {
                await server.prisma.student.update({
                    where: {
                        id: studentId,
                        institutionId
                    },
                    data: {
                        intentLevel
                    }
                });
            }

            // 创建跟进记录
            await server.prisma.StudentFollowRecords.create({
                data: {
                    id: uuidv4(),
                    studentId,
                    followUpDate: followUpDate ? BigInt(followUpDate) : BigInt(new Date().getTime()),
                    nextFollowUpDate: nextFollowUpDate ? BigInt(nextFollowUpDate) : null,
                    followUpContent,
                    followUpUserId,
                    institutionId,
                    operatorId: userId,
                    createdAt: new Date(),
                    updatedAt: new Date()
                }
            });

            return true;
        } catch (error) {
            throw new INTERNAL_ERROR(`新增学员跟进记录失败: ${error.message}`);
        }
    },
    /**
     * 创建学生产品
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 创建学生产品
     */
    async createStudentProduct(server, params) {
        const { studentId, institutionId, productId, amount, bonusLessons, dateTime,
            remarks, payment, salesRep, prepaidAmount, userId } = params;
        const client = await server.pg.connect();
        try {
            // 开始事务
            await client.query('BEGIN');

            const productResult = await client.query(`
                SELECT * FROM "products" WHERE "id" = $1 AND "institutionId" = $2 AND "status" = 'active'
            `, [productId, institutionId]);
            const studentResult = await client.query(`
                SELECT * FROM "students" WHERE "id" = $1 AND "institutionId" = $2
            `, [studentId, institutionId]);
            if (!productResult.rows[0] || !studentResult.rows[0]) {
                throw new INTERNAL_ERROR('产品或学生不存在');
            }
            const student = studentResult.rows[0]
            const product = productResult.rows[0]

            const id = uuidv4();
            const totalCount = product.usageLimit + Number(bonusLessons);
            const unitPrice = Number.parseFloat(Number(product.price) / Number(totalCount)).toFixed(3);

            // ============= 新创建学生产品 =============
            const data = {
                id,
                studentId,
                productId,
                totalSessionCount: Number(totalCount),
                remainingSessionCount: Number(totalCount),
                sessionUnitPrice: Number(unitPrice),
                enrollmentStatus: 'active',
                operatorId: userId,
                institutionId: institutionId,
            }
            // 根据套餐类型设置开始时间
            if(product.packageType === "limited-sessions"){
                data.startDate = Number(new Date().getTime())
            }

            // 根据套餐有效时间范围设置开始时间与结束时间
            if(product.packageType === "purchase-date"){
                data.startDate = Number(new Date().getTime())
                // 根据套餐有效时间范围设置结束时间
                if(product.timeLimitType === "daily"){
                    data.endDate = Number(new Date().getTime()) + product.timeLimitedUsage * 24 * 60 * 60 * 1000
                }else if(product.timeLimitType === "monthly"){
                    // 使用date-fns 计算结束时间
                    data.endDate = addMonths(new Date(), product.timeLimitedUsage)
                }
            }

            // 计算剩余金额
            const remainingBalance = Number(product.price) - Number(prepaidAmount);
            const amountUnpaid = Number(product.price) - Number(prepaidAmount)
            // 如果剩余金额小于0代表全额支付则设置套餐总结，否则设置支付金额
            data.remainingBalance = remainingBalance <= 0 ? product.price : prepaidAmount
            data.paymentStatus = remainingBalance <= 0 ? 'done' : 'arrears'

            const studentProduct = await client.query(`
                INSERT INTO "student_products" (
                    "id",
                    "studentId",
                    "productId",
                    "totalSessionCount",
                    "remainingSessionCount",
                    "sessionUnitPrice",
                    "enrollmentStatus",
                    "operatorId",
                    "institutionId",
                    "startDate",
                    "endDate",
                    "remainingBalance",
                    "paymentStatus",
                    "amount",
                    "salesRepId",
                    "amountPaid",
                    "amountUnpaid",
                    "updatedAt"
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, now()
                )
                RETURNING *
            `, [
                id, studentId, productId, totalCount, totalCount, unitPrice, 'active',
                userId, institutionId, data.startDate, data.endDate, data.remainingBalance,
                data.paymentStatus, product.price, salesRep, prepaidAmount, amountUnpaid
            ]);

            // 创建学生产品记录
            const createStudentProductRecord = await client.query(`
                INSERT INTO "student_product_record" (
                    "id",
                    "studentId",
                    "productId",
                    "studentProductId",
                    "amount",
                    "amountPaid",
                    "amountUnpaid",
                    "purchaseQuantity",
                    "discount",
                    "giftCount",
                    "giftDays",
                    "paymentMethod",
                    "paymentTime",
                    "salesRepresentativeId",
                    "operatorId",
                    "status",
                    "institutionId",
                    "remarks",
                    "updatedAt"
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, now()
                )
                RETURNING *
            `, [
                uuidv4(), studentId, productId, id, product.price, prepaidAmount,
                remainingBalance, 1, 10, bonusLessons, 0, payment, dateTime,
                salesRep || userId, userId, 'done', institutionId, remarks
            ]);

            // 创建账单表
            const createBillRemark = `${student.name} 购买 ${product.name}`
            const createBill = await client.query(`
                INSERT INTO "bills" (
                    "id",
                    "studentId",
                    "productId",
                    "amount",
                    "paymentMethod",
                    "paymentTime",
                    "billType",
                    "source",
                    "remarks",
                    "operatorId",
                    "institutionId",
                    "updatedAt"
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, now()
                )
                RETURNING *
            `, [
                uuidv4(), studentId, productId, prepaidAmount, payment, dateTime,
                'income', 'productSales', createBillRemark, userId, institutionId
            ]);

            const createOperationLog = await client.query(`
                INSERT INTO "operation_log" (
                    "id",
                    "userId",
                    "operationType",
                    "content",
                    "describe",
                    "institutionId",
                    "updatedAt"
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, now()
                )
                RETURNING *
            `, [
                uuidv4(), userId, 'create',
                `为 ${student.name} 创建套餐"${product.name}" 收款: ${amount} 赠送课时: ${bonusLessons}`,
                `为 ${student.name} 创建套餐"${product.name}"`,
                institutionId
            ]);

            // 创建学生产品调整记录
            if(product.packageType === "limited-time-and-count"){
                // 等待计算赠送时长
            }

            // 判断是增加还是减少
            const adjustType = totalCount > product.usageLimit ? 'add' : 'reduce';
            const adjustCount = Math.abs(totalCount - product.usageLimit);

            const createStudentProductAdjust = await client.query(`
                INSERT INTO "student_product_adjusts" (
                    "id",
                    "studentId",
                    "studentProductId",
                    "beforeCount",
                    "afterCount",
                    "type",
                    "operatorId",
                    "operatorTime",
                    "remarks",
                    "productId",
                    "institutionId",
                    "beforeAmount",
                    "afterAmount",
                    "count",
                    "days",
                    "beforeDays",
                    "afterDays",
                    "updatedAt"
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, now()
                )
                RETURNING *
            `, [
                uuidv4(),
                studentId,
                id,
                product.usageLimit,
                totalCount,
                adjustType,
                userId,
                Number(new Date().getTime()),
                remarks,
                productId,
                institutionId,
                product.price,
                product.price,
                adjustCount,
                0,
                0,
                0
            ]);

            // 提交事务
            await client.query('COMMIT');

            // ============= 结束新创建学生产品 =============
            return studentProduct.rows[0];
        } catch (error) {
            // 回滚事务
            await client.query('ROLLBACK');
            throw new INTERNAL_ERROR(`创建学生产品失败: ${error.message}`);
        } finally {
            client.release();
        }
    },
    /**
     * 更新学员套餐内容
     * @param {Object} server - Fastify服务器实例
     * @param {Object} params - 更新参数
     * @returns {Promise<void>}
     */
    async updateStudentProduct(server, params) {
        const { studentId ,studentProductId, remainingCount, remarks, remainingDays, status, institutionId, userId } = params;
        const client = await server.pg.connect();
        try {
            // 开始事务
            await client.query('BEGIN');

            // 获取当前套餐信息
            const { rows: [currentProduct] } = await client.query(`
                SELECT sp.*, s.name as studentName, p.name as productName 
                FROM student_products sp
                LEFT JOIN students s ON s.id = sp."studentId"
                LEFT JOIN products p ON p.id = sp."productId"
                WHERE sp."productId" = $1 AND sp."studentId" = $2 AND sp."institutionId" = $3
            `, [studentProductId, studentId, institutionId]);

            if (!currentProduct) {
                throw new INTERNAL_ERROR('学员套餐不存在');
            }

            // 准备更新数据
            const updateData = [];
            const updateValues = [];
            let paramIndex = 1;

            if (remainingCount !== undefined) {
                updateData.push(`"remainingSessionCount" = $${paramIndex}`);
                updateValues.push(remainingCount);
                paramIndex++;
            }

            if (remainingDays !== undefined) {
                updateData.push(`"remainingDays" = $${paramIndex}`);
                updateValues.push(remainingDays);
                paramIndex++;
            }

            if (status) {
                updateData.push(`"enrollmentStatus" = $${paramIndex}`);
                updateValues.push(status);
                paramIndex++;
            }

            // 添加更新时间
            updateData.push(`"updatedAt" = NOW()`);

            // 更新学员套餐
            if (updateData.length > 0) {
                updateValues.push(studentProductId, institutionId);
                await client.query(`
                    UPDATE "student_products"
                    SET ${updateData.join(', ')}
                    WHERE "id" = $${paramIndex} AND "institutionId" = $${paramIndex + 1}
                `, updateValues);
            }

            // 创建调整记录
            if (remainingCount !== undefined) {
                const adjustType = remainingCount > currentProduct.remaining_session_count ? 'add' : 'reduce';
                const adjustCount = Math.abs(remainingCount - currentProduct.remaining_session_count);

                await client.query(`
                    INSERT INTO "student_product_adjustments" (
                        "id",
                        "studentId",
                        "studentProductId",
                        "beforeCount",
                        "afterCount",
                        "type",
                        "operatorId",
                        "operatorTime",
                        "remarks",
                        "institutionId",
                        "updatedAt"
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW())
                `, [
                    uuidv4(),
                    currentProduct.student_id,
                    studentProductId,
                    currentProduct.remaining_session_count,
                    remainingCount,
                    adjustType,
                    userId,
                    Date.now(),
                    remarks || '',
                    institutionId
                ]);
            }

            // 创建操作日志
            await client.query(`
                INSERT INTO "operation_log" (
                    "id",
                    "userId",
                    "operationType",
                    "content",
                    "describe",
                    "institutionId",
                    "updatedAt"
                ) VALUES ($1, $2, $3, $4, $5, $6, NOW())
            `, [
                uuidv4(),
                userId,
                'update',
                `调整学员 ${currentProduct.student_name} 的套餐 "${currentProduct.product_name}" 内容`,
                remarks || '调整套餐内容',
                institutionId
            ]);

            // 提交事务
            await client.query('COMMIT');
        } catch (error) {
            // 回滚事务
            await client.query('ROLLBACK');
            throw new INTERNAL_ERROR(`更新学员套餐内容失败: ${error.message}`);
        } finally {
            client.release();
        }
    },
    /**
     * 创建学员跟进人
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 创建学员跟进人
     */
    async createFollowUp(server, params) {
        const { studentId, institutionId, followUpDate, userId } = params;
        const client = await server.pg.connect();
        client.query('BEGIN');
        try {

            const updateResult = await client.query(`
                UPDATE "students" SET "type" = 'intent', "followerId" = $1, "followUpDate" = $2 WHERE "id" = $3 AND "institutionId" = $4
            `, [userId, followUpDate, studentId, institutionId]);


            const createResult = await client.query(`
                INSERT INTO "student_follow_records" (
                    "id",
                    "studentId",
                    "followUpDate",
                    "followUpContent",
                    "followUpUserId",
                    "operatorId",
                    "institutionId"
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7
                )
                RETURNING *
            `, [
                uuidv4(),
                studentId,
                followUpDate,
                '创建跟进人',
                userId,
                userId,
                institutionId
            ]);

            return createResult.rows[0];
        } catch (error) {
            client.query('ROLLBACK');
            throw new INTERNAL_ERROR(`创建学员跟进人失败: ${error.message}`);
        } finally {
            client.query('COMMIT');
            client.release();
        }
    },
    /**
     * 获取学员套餐列表
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 学员套餐列表
     */
    async getStudentProducts(server, params) {
        const { institutionId, page = 1, pageSize = 10, search, remainingTimesMin,
            remainingTimesMax, remainingDaysMin, remainingDaysMax, status
        } = params;
        const client = await server.pg.connect();
        try {
            const conditions = ['"sp"."institutionId" = $1'];
            const queryParams = [institutionId];
            let paramIndex = 2;

            if (search) {
                conditions.push(`("s"."name" ILIKE $${paramIndex} OR "s"."phone" ILIKE $${paramIndex})`);
                queryParams.push(`%${search}%`);
                paramIndex++;
            }

            if (remainingTimesMin) {
                conditions.push(`"sp"."remainingSessionCount" >= $${paramIndex}`);
                queryParams.push(remainingTimesMin);
                paramIndex++;
            }

            if (remainingTimesMax) {
                conditions.push(`"sp"."remainingSessionCount" <= $${paramIndex}`);
                queryParams.push(remainingTimesMax);
                paramIndex++;
            }

            if (remainingDaysMin) {
                conditions.push(`"sp"."remainingDays" >= $${paramIndex}`);
                queryParams.push(remainingDaysMin);
                paramIndex++;
            }

            if (remainingDaysMax) {
                conditions.push(`"sp"."remainingDays" <= $${paramIndex}`);
                queryParams.push(remainingDaysMax);
                paramIndex++;
            }

            if (status) {
                conditions.push(`"sp"."enrollmentStatus" = $${paramIndex}`);
                queryParams.push(status);
                paramIndex++;
            }

            const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

            const studentQuery = `
                SELECT
                    "sp"."id",
                    "sp"."startDate",
                    "sp"."endDate",
                    "sp"."totalSessionCount",
                    "sp"."remainingSessionCount",
                    "sp"."sessionUnitPrice",
                    "sp"."enrollmentStatus",
                    "sp"."remainingBalance",
                    "p"."id" AS "productId",
                    "p"."name" AS "productName",
                    "p"."packageType" AS "productPackageType",
                    "p"."usageLimit",
                    "p"."timeLimitType",
                    "p"."timeLimitedUsage",
                    "p"."validTimeRange",
                    "s"."id" AS "studentId",
                    "s"."name" AS "studentName",
                    "s"."phone" AS "studentPhone"
                FROM "student_products" "sp"
                LEFT JOIN "products" "p" ON "sp"."productId" = "p"."id"
                LEFT JOIN "students" "s" ON "sp"."studentId" = "s"."id"
                ${whereClause}
                AND sp."enrollmentStatus" != 'refunded'
                ORDER BY "sp"."createdAt" DESC
                LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
            `;

            const countQuery = `
                SELECT COUNT(*) AS total
                FROM "student_products" "sp"
                LEFT JOIN "students" "s" ON "sp"."studentId" = "s"."id"
                ${whereClause}
            `;

            const [studentResult, countResult] = await Promise.all([
                client.query(studentQuery, [...queryParams, pageSize, (page - 1) * pageSize]),
                client.query(countQuery, queryParams)
            ]);

            const total = parseInt(countResult.rows[0].total);

            return {
                list: studentResult.rows,
                total
            };
        } catch (error) {
            throw new INTERNAL_ERROR(`获取学员套餐列表失败: ${error.message}`);
        } finally {
            client.release();
        }
    },
    /**
     * 获取学员产品调整记录
     * @param {string} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 学员产品调整记录
     */
    async getStudentProductAdjustments(server, params) {
        const { studentId, institutionId, page = 1, pageSize = 10, search, teacherId, endTime, startTime } = params;
        const client = await server.pg.connect();
        try {
            const conditions = ['"spa"."institutionId" = $1'];
            const queryParams = [institutionId];
            let paramIndex = 2;

            if (search) {
                conditions.push(`(s.name ILIKE $${paramIndex} OR s.phone ILIKE $${paramIndex})`);
                queryParams.push(`%${search}%`);
                paramIndex++;
            }

            if (teacherId) {
                conditions.push(`"spa"."operatorId" = $${paramIndex}`);
                queryParams.push(teacherId);
                paramIndex++;
            }

            if (endTime) {
                conditions.push(`"spa"."operatorTime" <= $${paramIndex}`);
                queryParams.push(endTime);
                paramIndex++;
            }

            if (startTime) {
                conditions.push(`"spa"."operatorTime" >= $${paramIndex}`);
                queryParams.push(startTime);
                paramIndex++;
            }

            const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

            const studentQuery = `
                SELECT
                    "spa"."id",
                    "spa"."beforeCount",
                    "spa"."afterCount",
                    "spa"."beforeDays",
                    "spa"."afterDays",
                    "spa"."type",
                    "spa"."operatorId",
                    "spa"."operatorTime",
                    "spa"."remarks",
                    "p"."id" AS "productId",
                    "p"."name" AS "productName",
                    "p"."packageType" AS "productPackageType",
                    s.id AS "studentId",
                    s.name AS "studentName",
                    u.name AS "operatorName",
                    s.phone AS "studentPhone"
                FROM "student_product_adjusts" "spa"
                LEFT JOIN "products" "p" ON "spa"."productId" = "p"."id"
                LEFT JOIN "students" "s" ON "spa"."studentId" = "s"."id"
                LEFT JOIN "users" "u" ON "spa"."operatorId" = "u"."id"
                ${whereClause}
                ORDER BY "spa"."createdAt" DESC
                LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
            `;

            const countQuery = `
                SELECT COUNT(*) AS total
                FROM "student_product_adjusts" "spa"
                ${whereClause}
            `;

            const [studentResult, countResult] = await Promise.all([
                client.query(studentQuery, [...queryParams, pageSize, (page - 1) * pageSize]),
                client.query(countQuery, queryParams)
            ]);

            const total = parseInt(countResult.rows[0].total);

            return {
                list: studentResult.rows,
                total
            };
        } catch (error) {
            throw new INTERNAL_ERROR(`获取学员产品调整记录失败: ${error.message}`);
        } finally {
            client.release();
        }
    },
    /**
     * 获取整体学员上课查询
     * @param {Object} server - Fastify服务器实例
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 学员上课记录
     */
    async getClassesQuery(server, params) {
        const { take, skip, search, studentId, status, startDate, endDate, institutionId } = params;
        const client = await server.pg.connect();
        try {
            let whereConditions = ['sws."institutionId" = $1'];
            const queryParams = [institutionId];
            let paramIndex = 2;

            if (search) {
                whereConditions.push(`(s.name ILIKE $${paramIndex} OR s.phone ILIKE $${paramIndex})`);
                queryParams.push(`%${search}%`);
                paramIndex++;
            }

            if (status && status !== "all") {
                whereConditions.push(`sws.status = $${paramIndex}`);
                queryParams.push(status);
                paramIndex++;
            }

            if (startDate && endDate) {
                whereConditions.push(`cs."startDate" BETWEEN $${paramIndex} AND $${paramIndex + 1}`);
                queryParams.push(Number(startDate));
                queryParams.push(Number(endDate));
                paramIndex += 2;
            }

            if (studentId) {
                whereConditions.push(`sws."studentId" = $${paramIndex}`);
                queryParams.push(studentId);
                paramIndex++;
            }

            const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

            const query = `
                SELECT
                    sws.id,
                    sws.status,
                    cs.id as "scheduleId",
                    cs."startDate",
                    cs."startTime",
                    cs."endTime",
                    cs.subject,
                    t.id as "teacherId",
                    t.name as "teacherName",
                    c.id as "courseId",
                    c.name as "courseName",
                    cl.id as "classId",
                    cl.name as "className",
                    s.id as "studentId",
                    s.name as "studentName"
                FROM "student_weekly_schedules" sws
                LEFT JOIN "classes_schedules" cs ON sws."classesScheduleId" = cs.id
                LEFT JOIN "users" t ON cs."teacherId" = t.id
                LEFT JOIN "courses" c ON cs."courseId" = c.id
                LEFT JOIN "classes" cl ON cs."classesId" = cl.id
                LEFT JOIN "students" s ON sws."studentId" = s.id
                ${whereClause}
                ORDER BY cs."startDate" DESC
                LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
            `;
            const countQuery = `
                SELECT COUNT(*) FROM "student_weekly_schedules" sws
                LEFT JOIN "classes_schedules" cs ON sws."classesScheduleId" = cs.id
                LEFT JOIN "students" s ON sws."studentId" = s.id
                ${whereClause}
            `;

            queryParams.push(take || 10);
            queryParams.push(skip || 0);

            // 添加调试日志
            server.log.debug({
                query,
                countQuery,
                queryParams,
                whereClause
            }, 'SQL查询参数');

            const [result, total] = await Promise.all([
                client.query(query, queryParams),
                client.query(countQuery, queryParams.slice(0, -2))
            ]);

            // 添加结果调试日志
            server.log.debug({
                resultCount: result.rows.length,
                totalCount: total.rows[0].count
            }, '查询结果统计');

            return {
                list: result.rows,
                total: parseInt(total.rows[0].count || '0'),
            };
        } catch (error) {
            server.log.error({
                error: error.message,
                stack: error.stack,
                params
            }, '获取学员上课记录失败');
            throw new INTERNAL_ERROR(`获取学员上课记录失败: ${error.message}`);
        } finally {
            client.release();
        }
    },

    // 获取意向学员列表
    async getIntentStudents(fastify, user, { status, search, page, pageSize }) {
        const skip = (page - 1) * pageSize;
        const take = pageSize;

        const [result, total] = await Promise.all([
            fastify.prisma.student.findMany({
                where: {
                    status: status ? { equals: status } : undefined,
                    name: search ? { contains: search } : undefined,
                    phone: search ? { contains: search } : undefined,
                    institutionId: user.institutionId,
                    type: 'intent'
                },
                select: {
                    id: true,
                    name: true,
                    phone: true,
                    gender: true,
                    birthday: true,
                    source: true,
                    sourceDesc: true,
                    followUpDate: true,
                    intentLevel: true,
                    type: true,
                    follower: {
                        select: {
                            id: true,
                            name: true,
                        }
                    },
                },
                skip,
                take
            }),
            fastify.prisma.student.count({
                where: {
                    status: status ? { equals: status } : undefined,
                    name: search ? { contains: search } : undefined,
                    phone: search ? { contains: search } : undefined,
                    institutionId: user.institutionId,
                    type: 'intent'
                }
            })
        ]);

        const serializedResult = result.map(item => ({
            ...item,
            birthday: item.birthday ? Number(item.birthday) : null,
            followUpDate: item.followUpDate ? Number(item.followUpDate) : null,
        }));

        return {
            list: serializedResult,
            total,
            page,
            pageSize
        };
    },

    // 获取考勤记录
    async getAttendanceRecords(fastify, user, { page, pageSize, status, teacherId, endTime, startTime, search }) {
        const skip = (page - 1) * pageSize;
        const take = pageSize;

        const where = {
            institutionId: user.institutionId,
            ...(status && { status }),
            ...(teacherId && { operatorId: teacherId }),
            ...(search && {
                student: { name: { contains: search, mode: 'insensitive' } }
            })
        };

        if (endTime || startTime) {
            where.operatorTime = {
                ...(endTime ? { lte: Number(endTime) } : {}),
                ...(startTime ? { gte: Number(startTime) } : {}),
            };
        }

        const [result, total] = await Promise.all([
            fastify.prisma.studentWeeklySchedule.findMany({
                where,
                select: {
                    id: true,
                    status: true,
                    attendanceCount: true,
                    attendanceAmount: true,
                    operator: {
                        select: {
                            id: true,
                            name: true,
                        }
                    },
                    operatorTime: true,
                    student: {
                        select: {
                            id: true,
                            name: true,
                        }
                    },
                    product: {
                        select: {
                            id: true,
                            name: true,
                        }
                    },
                    classesSchedule: {
                        select: {
                            id: true,
                            startDate: true,
                            startTime: true,
                            endTime: true,
                            subject: true,
                            courses: {
                                select: {
                                    id: true,
                                    name: true,
                                }
                            },
                            classes: {
                                select: {
                                    id: true,
                                    name: true,
                                }
                            },
                            teacher: {
                                select: {
                                    id: true,
                                    name: true,
                                }
                            }
                        }
                    }
                },
                skip,
                take,
            }),
            fastify.prisma.studentWeeklySchedule.count({
                where,
            })
        ]);

        const serializedResult = result.map(item => ({
            ...item,
            operatorTime: Number(item.operatorTime),
            classesSchedule: {
                ...item.classesSchedule,
                startDate: Number(item.classesSchedule.startDate),
            }
        }));

        return {
            list: serializedResult,
            total,
            page,
            pageSize
        };
    },

    /**
     * 学员产品退款
     * @param {Object} server - Fastify服务器实例
     * @param {Object} params - 退款参数
     * @returns {Promise<boolean>} 退款结果
     */
    async refundProduct(server, params) {
        const { studentId, productId, institutionId, refundReason, paymentMethod, userId } = params;
        const client = await server.pg.connect();
        try {
            await client.query('BEGIN');

            // 获取学生产品信息
            const productResult = await client.query(`
                SELECT * FROM "student_products"
                WHERE "id" = $1 AND "studentId" = $2 AND "institutionId" = $3
            `, [productId, studentId, institutionId]);

            if (!productResult.rows[0]) {
                throw new INTERNAL_ERROR('学生产品不存在');
            }

            const product = productResult.rows[0];
            // 检查产品状态
            if (product.enrollmentStatus === 'refunded') {
                throw new CONFLICT_ERROR('该产品已经退款');
            }

            if (product.enrollmentStatus !== 'active') {
                throw new FORBIDDEN_ERROR('只有激活状态的产品才能退款');
            }
            // 验证退款金额
            const refundAmount = product.remainingBalance;
            if (refundAmount <= 0) {
                throw new FORBIDDEN_ERROR('剩余余额不足，无法退款');
            }
            // 更新学生产品状态
            await client.query(`
                UPDATE "student_products"
                SET "enrollmentStatus" = 'refunded',
                    "updatedAt" = now()
                WHERE "id" = $1
            `, [productId]);



            // 创建退款记录
            await client.query(`
                INSERT INTO "student_product_refund" (
                    "id",
                    "studentId",
                    "productId",
                    "amount",
                    "reason",
                    "paymentMethod",
                    "paymentTime",
                    "operatorId",
                    "institutionId",
                    "updatedAt"
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, now()
                )
            `, [
                uuidv4(),
                studentId,
                product.productId,
                refundAmount,
                refundReason,
                paymentMethod,
                new Date().getTime(),
                userId,
                institutionId
            ]);

            // 创建账单记录
            await client.query(`
                INSERT INTO "bills" (
                    "id",
                    "studentId",
                    "productId",
                    "amount",
                    "paymentMethod",
                    "paymentTime",
                    "billType",
                    "source",
                    "remarks",
                    "operatorId",
                    "institutionId",
                    "updatedAt"
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, now()
                )
            `, [
                uuidv4(),
                studentId,
                product.productId,
                -refundAmount,
                paymentMethod,
                new Date().getTime(),
                'expense',
                'productRefund',
                refundReason,
                userId,
                institutionId
            ]);

            await client.query('COMMIT');
            return true;
        } catch (error) {
            await client.query('ROLLBACK');
            throw new INTERNAL_ERROR(`学员产品退款失败: ${error.message}`);
        } finally {
            client.release();
        }
    },
    /**
     * 机构添加学员
     * @param {Object} server - Fastify服务器实例
     * @param {Object} params - 添加学员参数
     * @returns {Promise<Object>} 创建的学生信息
     */
    async addStudent(server, params) {
        const {
            name, gender, phone, birthday, sourceDesc, intention,
            follower, source, referrer, address, idCard, remarks,
            type, institutionId, operatorId
        } = params;

        try {
            const result = await server.prisma.student.findFirst({
                where: {
                    phone: phone,
                    institutionId: institutionId
                }
            })
            if (result) {
                throw new CONFLICT_ERROR('学生已存在', {
                    phone,
                    institutionId
                });
            }
        } catch (error) {
            if (error instanceof BaseError) {
                throw error;
            }
            throw new INTERNAL_ERROR(`检查学生是否存在时发生错误: ${error.message}`);
        }

        try {
            const result = await server.prisma.student.create({
                data: {
                    id: uuidv4(),
                    name,
                    phone,
                    gender,
                    birthday,
                    sourceDesc,
                    intentLevel: intention,
                    followerId: follower ? follower : null,
                    source,
                    referrer,
                    address,
                    idCard,
                    remarks,
                    institutionId,
                    type,
                    operatorId
                }
            });

            return result;
        } catch (error) {
            throw new INTERNAL_ERROR(`创建学生记录失败: ${error.message}`);
        }
    },

    /**
     * 学员创建跟进人
     * @param {Object} server - Fastify服务器实例
     * @param {Object} params - 创建跟进人参数
     * @returns {Promise<void>}
     */
    async createFollowUpPerson(server, params) {
        const { studentId, institutionId, followUpPersonId } = params;
        const followUpDate = new Date().getTime();

        try {
            await server.prisma.$transaction([
                server.prisma.student.update({
                    where: {
                        id: studentId,
                        institutionId: institutionId,
                    },
                    data: {
                        type: 'intent',
                        followerId: followUpPersonId,
                        followUpDate: followUpDate,
                    }
                }),
                server.prisma.studentFollowRecords.create({
                    data: {
                        id: uuidv4(),
                        studentId,
                        followUpDate: followUpDate,
                        followUpContent: '创建跟进人',
                        followUpUserId: followUpPersonId,
                        operatorId: followUpPersonId,
                        institutionId: institutionId,
                    }
                })
            ]);
        } catch (error) {
            throw new INTERNAL_ERROR(`创建跟进人失败: ${error.message}`);
        }
    },

    /**
     * 注册学生
     * @param {Object} server - Fastify服务器实例
     * @param {Object} params - 注册学生参数
     * @returns {Promise<Object>} 创建的学生信息
     */
    async registerStudent(server, params) {
        const { institutionId, name, gender, phone } = params;

        try {
            const institutionResult = await server.prisma.institution.findFirst({
                where: {
                    id: institutionId
                }
            });

            if (!institutionResult) {
                throw new INTERNAL_ERROR('学校不存在');
            }

            const password = await bcrypt.hash(BASICCONSTANTS.DEFAULT_PASSWORD, 10);
            const result = await server.prisma.student.create({
                data: {
                    id: uuidv4(),
                    name,
                    gender,
                    phone,
                    password,
                    institutionId,
                }
            });

            return result;
        } catch (error) {
            throw new INTERNAL_ERROR(`注册学生失败: ${error.message}`);
        }
    },

    async updateStudentAttendance(scheduleId, studentId, status, institutionId, operatorId) {
        const result = await this.prisma.studentWeeklySchedule.findFirst({
            where: {
                classesScheduleId: scheduleId,
                studentId,
                institutionId
            },
            select: {
                id: true,
                studentProductId: true,
                attendanceCount: true,
                studentType: true,
                status: true,
                attendanceAmount: true,
                classesSchedule: {
                    select: {
                        courses: {
                            select: {
                                deductionPerClass: true,
                                isDeductOnAttendance: true,
                                isDeductOnLeave: true,
                                isDeductOnAbsence: true,
                                ProductCourse: {
                                    select: {
                                        id: true,
                                        productId: true
                                    }
                                }
                            }
                        }
                    }
                },
                student: {
                    select: {
                        StudentProduct: {
                            where: {
                                enrollmentStatus: 'active'
                            },
                            select: {
                                id: true,
                                productId: true,
                                remainingSessionCount: true,
                                remainingBalance: true,
                                sessionUnitPrice: true,
                                paymentStatus: true,
                                startDate: true,
                                endDate: true,
                                product: {
                                    select: {
                                        id: true,
                                        timeLimitedUsage: true,
                                        timeLimitType: true,
                                        validTimeRange: true
                                    }
                                }
                            },
                            orderBy: {
                                createdAt: 'desc'
                            }
                        }
                    }
                }
            }
        });

        if (!result) {
            throw new Error('学员不存在');
        }

        if (result.studentType === 'trial') {
            throw new Error('试听学员不能考勤');
        }

        // 如果状态相同，退还课时
        if (result.status === status) {
            if (result.studentProductId) {
                const productResult = await this.prisma.studentProduct.findFirst({
                    where: { id: result.studentProductId }
                });

                const newRemainingCount = Number(productResult.remainingSessionCount) + Number(result.attendanceCount);
                const newRemainingBalance = Number(productResult.remainingBalance) + Number(result.attendanceAmount);

                await this.prisma.studentProduct.update({
                    where: { id: productResult.id },
                    data: {
                        remainingSessionCount: Number(newRemainingCount).toFixed(2),
                        remainingBalance: Number(newRemainingBalance).toFixed(2),
                        enrollmentStatus: newRemainingCount > 0 ? 'active' : 'completed'
                    }
                });
            }

            await this.prisma.studentWeeklySchedule.update({
                where: { id: result.id },
                data: {
                    status: 'unattended',
                    attendanceCount: 0,
                    studentProductId: null,
                    operatorId: null,
                    operatorTime: null,
                    attendanceAmount: 0,
                    productId: null
                }
            });

            return;
        }

        // 处理考勤扣课时
        const { courses } = result.classesSchedule;
        const { deductionPerClass, isDeductOnAttendance, isDeductOnLeave, isDeductOnAbsence } = courses;

        let product = null;
        let productIndex = 0;
        let isFound = false;
        let productCourse = null;

        if (result.student.StudentProduct.length < 1) {
            throw new Error('学员没有产品');
        }

        while (productIndex < result.student.StudentProduct.length) {
            product = result.student.StudentProduct[productIndex];
            productCourse = courses.ProductCourse.filter(item => item.productId === product.productId);
            
            if (Number(product.remainingSessionCount) >= Number(deductionPerClass) && productCourse.length > 0) {
                isFound = true;
                break;
            }
            productIndex++;
        }

        if (productCourse.length === 0) {
            throw new Error('没有课程对应的套餐');
        }

        if (!isFound) {
            throw new Error('当前产品课时不足');
        }

        // 处理限时限次消费
        if (product.product.validTimeRange === 'consumption-date' && !product.startDate) {
            const startDate = new Date();
            const endDate = new Date();
            const timeLimitedUsage = Number(product.product.timeLimitedUsage);
            
            if (product.product.timeLimitType === 'daily') {
                endDate.setDate(endDate.getDate() + timeLimitedUsage);
            } else {
                endDate.setMonth(endDate.getMonth() + timeLimitedUsage);
            }

            await this.prisma.studentProduct.update({
                where: { id: product.id },
                data: {
                    startDate: startDate.getTime(),
                    endDate: endDate.getTime()
                }
            });
        }

        // 计算扣除课时和金额
        let newRemainingCount = Number(product.remainingSessionCount);
        const pricePerClass = Number(product.sessionUnitPrice) * Number(deductionPerClass);
        let remainingBalance = Number(product.remainingBalance);

        if (
            (status === 'attendance' && isDeductOnAttendance) ||
            (status === 'leave' && isDeductOnLeave) ||
            (status === 'absent' && isDeductOnAbsence)
        ) {
            newRemainingCount -= Number(deductionPerClass);
            remainingBalance -= pricePerClass;
        }

        // 更新学员产品
        const newUnitPrice = remainingBalance / newRemainingCount;
        await this.prisma.studentProduct.update({
            where: { id: product.id },
            data: {
                remainingSessionCount: Number(newRemainingCount).toFixed(3),
                remainingBalance: Number(remainingBalance).toFixed(3),
                sessionUnitPrice: Number(newUnitPrice).toFixed(3),
                enrollmentStatus: newRemainingCount > 0 ? 'active' : 'completed'
            }
        });

        // 更新考勤记录
        await this.prisma.studentWeeklySchedule.update({
            where: { id: result.id },
            data: {
                status,
                attendanceCount: Number(deductionPerClass),
                studentProductId: product.id,
                operatorId,
                operatorTime: new Date().getTime(),
                attendanceAmount: Number(pricePerClass).toFixed(3),
                productId: product.productId
            }
        });
    },

};