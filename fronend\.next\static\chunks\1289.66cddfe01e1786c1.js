"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1289,3670],{33670:(e,t,a)=>{a.r(t),a.d(t,{default:()=>p});var n=a(95155);let d=(0,a(19946).A)("UserCog",[["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m21.7 16.4-.9-.3",key:"12j9ji"}],["path",{d:"m15.2 13.9-.9-.3",key:"1fdjdi"}],["path",{d:"m16.6 18.7.3-.9",key:"heedtr"}],["path",{d:"m19.1 12.2.3-.9",key:"1af3ki"}],["path",{d:"m19.6 18.7-.4-1",key:"1x9vze"}],["path",{d:"m16.8 12.3-.4-1",key:"vqeiwj"}],["path",{d:"m14.3 16.6 1-.4",key:"1qlj63"}],["path",{d:"m20.7 13.8 1-.4",key:"1v5t8k"}]]);var s=a(12115),r=a(55028),c=a(35035),i=a(65436),l=a(57001);let h=(0,r.default)(()=>Promise.all([a.e(3168),a.e(2319),a.e(2904)]).then(a.bind(a,82904)),{loadableGenerated:{webpack:()=>[82904]},ssr:!1});function p(e){let{studentId:t}=e,a=(0,i.j)(),[r,p]=(0,s.useState)(!1);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(l.p,{icon:d,tooltipText:"学员详细",onClick:()=>{a((0,c.ZZ)({studentId:t})),p(!0)}}),r&&(0,n.jsx)(h,{open:r,onOpenChange:p})]})}},57001:(e,t,a)=>{a.d(t,{p:()=>r});var n=a(95155),d=a(30285),s=a(46102);function r(e){let{icon:t,tooltipText:a,tooltipSide:r="top",tooltipAlign:c="center",delayDuration:i=300,variant:l="ghost",size:h="icon",className:p="h-8 w-8 hover:bg-muted",...k}=e;return(0,n.jsx)(s.Bc,{delayDuration:i,children:(0,n.jsxs)(s.m_,{children:[(0,n.jsx)(s.k$,{asChild:!0,children:(0,n.jsx)(d.$,{variant:l,size:h,className:p,...k,children:(0,n.jsx)(t,{className:"h-4 w-4 text-muted-foreground"})})}),(0,n.jsx)(s.ZI,{side:r,align:c,className:"font-medium text-xs px-3 py-1.5",children:(0,n.jsx)("p",{children:a})})]})})}}}]);