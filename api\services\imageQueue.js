import { Queue, Worker } from "bullmq";
import { redisTask, redisConfig } from "../config/redis.js";
import { generateImage } from "./aitools.js";

const imageQueue = new Queue('image-generation', {
  connection: redisConfig,
  defaultJobOptions: {
    attempts: 3, // 重试 3 次
    backoff: { type: 'exponential', delay: 1000 }, // 指数退避
    removeOnComplete: true, // 任务完成后立即移除
    removeOnFail: { age: 3600 * 24 * 7 }, // 任务失败后保留一定时间后移除
    lockDuration: 300000, // 锁持续时间增加到5分钟
  },
  limiter: {
    max: 5, // 降低最大并发数
    duration: 1000, // 每秒 3 个任务
  },
});

// 添加错误处理
imageQueue.on('error', (error) => {
  console.error('队列错误:', error);
});

imageQueue.on('failed', (job, error) => {
  console.error(`任务 ${job.id} 失败:`, error);
});

const worker = new Worker(
  'image-generation',
  async (job) => {
    const { taskId, prompt, count, size } = job.data;
    console.log(`开始处理任务 ${job.id}, taskId: ${taskId}`);
    
    try {
      // 更新任务状态为处理中
      await redisTask.set(
        taskId,
        JSON.stringify({ status: 'processing', taskId, progress: 0 }),
        'EX',
        3600
      );
      
      // 定期更新进度，保持锁活跃
      const progressInterval = setInterval(async () => {
        try {
          await job.updateProgress(10);
        } catch (e) {
          console.error('更新进度失败:', e);
        }
      }, 10000);
      
      // 执行图片生成
      const result = await generateImage(prompt, count, size);
      
      // 清除进度更新定时器
      clearInterval(progressInterval);
      
      // 更新任务状态为完成，存储1小时
      await redisTask.set(
        taskId,
        JSON.stringify({ status: 'completed', taskId, result }),
        'EX',
        60 * 60
      ); 
      
      console.log(`任务 ${job.id} 完成`);
      return result;
    } catch (error) {
      console.error(`任务 ${job.id} 失败:`, error);
      
      // 更新任务状态为失败
      await redisTask.set(
        taskId,
        JSON.stringify({ 
          status: 'failed', 
          taskId, 
          error: error.message,
          stack: error.stack
        }),
        'EX',
        3600
      );
      
      // 重新抛出错误，让BullMQ处理重试
      throw error;
    }
  },
  {
    connection: redisConfig,
    concurrency: 5, // 降低Worker并发数
    lockDuration: 300000, // 锁持续时间增加到5分钟
    stalledInterval: 30000, // 检查卡住任务的间隔
    maxStalledCount: 1, // 最大卡住次数
    // removeOnComplete: true, // 任务完成后立即移除
  }
);

// 添加Worker错误处理
worker.on('error', (error) => {
  console.error('Worker错误:', error);
});

worker.on('failed', (job, error) => {
  console.error(`Worker处理任务 ${job?.id} 失败:`, error);
});

worker.on('completed', (job) => {
  console.log(`Worker完成任务 ${job.id}`);
});

export default imageQueue;