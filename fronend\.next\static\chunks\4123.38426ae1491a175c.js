"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4123],{24123:(e,t,r)=>{r.r(t),r.d(t,{default:()=>a});var n=r(12115);let a=n.forwardRef(function(e,t){let{title:r,titleId:a,...o}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},o),r?n.createElement("title",{id:a},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"}),n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"}))})}}]);