(()=>{var e={};e.id=7874,e.ids=[7874],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,s,r)=>{let{createProxy:t}=r(39844);e.exports=t("F:\\trae\\cardmees\\fronend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11031:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var t=r(37413);r(61120);var a=r(4536),l=r.n(a),i=r(51465),d=r(32127),x=r(26373);let n=(0,x.A)("Network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]]);var c=r(41382);let m=(0,x.A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),o=(0,x.A)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]),h=(0,x.A)("Layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]]);var g=r(32769),p=r(91142);function j(){return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,t.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(l(),{href:"/",className:"flex items-center space-x-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 transition-colors",children:[(0,t.jsx)(i.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"font-medium",children:"返回首页"})]}),(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"组织管理系统"})]})})}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-indigo-500 to-purple-600 py-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex justify-center mb-6",children:(0,t.jsx)("div",{className:"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center",children:(0,t.jsx)(d.A,{className:"w-10 h-10 text-white"})})}),(0,t.jsx)("h1",{className:"text-4xl font-bold text-white mb-4",children:"组织管理"}),(0,t.jsx)("p",{className:"text-xl text-indigo-100 max-w-3xl mx-auto",children:"完善的组织架构管理，灵活的权限控制，助力教育机构规范化运营"})]})})}),(0,t.jsx)("div",{className:"py-16 bg-white dark:bg-gray-800",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"核心功能特色"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"全方位的组织架构管理解决方案"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 p-6 rounded-xl border border-indigo-200 dark:border-indigo-800",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center mb-4",children:(0,t.jsx)(n,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"组织架构管理"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"灵活构建多层级组织架构，支持部门、班级、小组等多种组织形式"})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4",children:(0,t.jsx)(c.A,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"人员信息管理"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"统一管理教师、学员、管理员等各类人员信息和档案"})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-xl border border-green-200 dark:border-green-800",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mb-4",children:(0,t.jsx)(m,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"权限控制系统"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"细粒度的权限管理，确保数据安全和操作规范"})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-xl border border-purple-200 dark:border-purple-800",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mb-4",children:(0,t.jsx)(o,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"角色权限配置"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"预设多种角色模板，支持自定义角色和权限配置"})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 p-6 rounded-xl border border-orange-200 dark:border-orange-800",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mb-4",children:(0,t.jsx)(h,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"多层级管理"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"支持总部-分校-班级等多层级管理模式"})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 p-6 rounded-xl border border-teal-200 dark:border-teal-800",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-teal-500 rounded-lg flex items-center justify-center mb-4",children:(0,t.jsx)(g.A,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"系统参数配置"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"灵活的系统参数设置，适应不同机构的管理需求"})]})]})]})}),(0,t.jsx)("div",{className:"py-16 bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"组织架构示例"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"清晰的层级结构，便于管理和协作"})]}),(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8",children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-8",children:[(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("div",{className:"w-32 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mb-2",children:(0,t.jsx)("span",{className:"text-white font-semibold",children:"教育集团总部"})})}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row items-center space-y-4 lg:space-y-0 lg:space-x-8",children:[(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("div",{className:"w-28 h-14 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mb-2",children:(0,t.jsx)("span",{className:"text-white font-medium text-sm",children:"北京分校"})})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("div",{className:"w-28 h-14 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mb-2",children:(0,t.jsx)("span",{className:"text-white font-medium text-sm",children:"上海分校"})})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("div",{className:"w-28 h-14 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mb-2",children:(0,t.jsx)("span",{className:"text-white font-medium text-sm",children:"深圳分校"})})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("div",{className:"w-24 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-medium text-xs",children:"教学部"})})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("div",{className:"w-24 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-medium text-xs",children:"招生部"})})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("div",{className:"w-24 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-medium text-xs",children:"财务部"})})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("div",{className:"w-24 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-medium text-xs",children:"行政部"})})})]})]})})]})}),(0,t.jsx)("div",{className:"py-16 bg-white dark:bg-gray-800",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"权限管理体系"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"精细化的权限控制，保障系统安全"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 p-6 rounded-xl border border-red-200 dark:border-red-800",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(m,{className:"w-8 h-8 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"超级管理员"})]}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600 dark:text-gray-300",children:[(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"系统全部功能权限"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"用户权限管理"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"系统配置管理"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"数据备份恢复"})]})]})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(d.A,{className:"w-8 h-8 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"校区管理员"})]}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600 dark:text-gray-300",children:[(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"校区数据管理"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"教师学员管理"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"课程安排管理"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"财务数据查看"})]})]})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-xl border border-green-200 dark:border-green-800",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(c.A,{className:"w-8 h-8 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"普通教师"})]}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600 dark:text-gray-300",children:[(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"学员信息查看"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"课程安排查看"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"成绩录入管理"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"考勤记录管理"})]})]})]})]})]})}),(0,t.jsx)("div",{className:"py-16 bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"适用场景"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"满足各种规模教育机构的组织管理需求"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"大型教育集团"}),(0,t.jsxs)("ul",{className:"space-y-3 text-gray-600 dark:text-gray-300",children:[(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"多层级组织架构管理"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"跨校区统一管理"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"集团化权限控制"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"标准化流程管理"})]})]})]}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"中小型培训机构"}),(0,t.jsxs)("ul",{className:"space-y-3 text-gray-600 dark:text-gray-300",children:[(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"简化的组织结构"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"灵活的角色配置"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"快速部署实施"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"成本效益优化"})]})]})]})]})]})}),(0,t.jsx)("div",{className:"py-16 bg-gradient-to-r from-indigo-500 to-purple-600",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"开始使用组织管理系统"}),(0,t.jsx)("p",{className:"text-xl text-indigo-100 mb-8",children:"构建规范的组织架构，实现高效的团队协作，提升管理水平"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)(l(),{href:"/dashboard",className:"inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 transition-colors duration-200",children:"立即体验"}),(0,t.jsx)(l(),{href:"/features",className:"inline-flex items-center justify-center px-8 py-3 border border-white text-base font-medium rounded-md text-white hover:bg-white/10 transition-colors duration-200",children:"查看更多功能"})]})]})}),(0,t.jsx)("footer",{className:"bg-gray-800 dark:bg-gray-900",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"text-center text-gray-400",children:(0,t.jsx)("p",{children:"\xa9 2024 CardMees 教育管理平台. 保留所有权利。"})})})})]})}},12412:e=>{"use strict";e.exports=require("assert")},17720:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},18633:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>o,tree:()=>n});var t=r(65239),a=r(48088),l=r(88170),i=r.n(l),d=r(30893),x={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(x[e]=()=>d[e]);r.d(s,x);let n={children:["",{children:["features",{children:["organization-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,11031)),"F:\\trae\\cardmees\\fronend\\src\\app\\features\\organization-management\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["F:\\trae\\cardmees\\fronend\\src\\app\\features\\organization-management\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},o=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/features/organization-management/page",pathname:"/features/organization-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26373:(e,s,r)=>{"use strict";r.d(s,{A:()=>x});var t=r(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=(...e)=>e.filter((e,s,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===s).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,t.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:d="",children:x,iconNode:n,...c},m)=>(0,t.createElement)("svg",{ref:m,...i,width:s,height:s,stroke:e,strokeWidth:a?24*Number(r)/Number(s):r,className:l("lucide",d),...c},[...n.map(([e,s])=>(0,t.createElement)(e,s)),...Array.isArray(x)?x:[x]])),x=(e,s)=>{let r=(0,t.forwardRef)(({className:r,...i},x)=>(0,t.createElement)(d,{ref:x,iconNode:s,className:l(`lucide-${a(e)}`,r),...i}));return r.displayName=`${e}`,r}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32127:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},32769:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},33873:e=>{"use strict";e.exports=require("path")},41382:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},51465:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},52568:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,85814,23))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91142:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,7392,5814,3019],()=>r(18633));module.exports=t})();