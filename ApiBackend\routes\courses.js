import { createError } from '@fastify/error';
import { v4 as uuidv4 } from 'uuid';
const INTERNAL_ERROR = createError('INTERNAL_ERROR', '%s', 500);
export default async function (fastify, opts) {

    // 获取课程列表
    fastify.get('/courses', {
        schema: {
            tags: ['courses'],
            summary: '获取课程列表',
            querystring: {
                type: 'object',
                properties: {
                    page: { type: 'number' },
                    pageSize: { type: 'number' },
                    search: { type: 'string' },
                    type: { type: 'string' }
                }
            },
            response: {
                200: {
                    type: 'object',
                    properties: {
                        code: { type: 'number' },
                        message: { type: 'string' },
                        data: { 
                            type: 'object',
                            properties: {
                                list: { 
                                    type: 'array', 
                                    items: { 
                                        type: 'object',
                                        properties: {
                                            id: { type: 'string' },
                                            name: { type: 'string' },
                                            picture: { type: 'string' },
                                            type: { type: 'string' },
                                            duration: { type: 'number' },
                                            teacherId: { type: 'string' },
                                            isDirectSale: { type: 'boolean' },
                                            deductionPerClass: { type: 'number' },
                                            status: { type: 'string' },
                                            ProductCourse: { 
                                                type: 'array',
                                                items: { 
                                                    type: 'object',
                                                    properties: {
                                                        productId: { type: 'string' },
                                                        product: { 
                                                            type: 'object',
                                                            properties: {
                                                                id: { type: 'string' },
                                                                name: { type: 'string' }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    } },
                                page: { type: 'number' },
                                pageSize: { type: 'number' },
                                total: { type: 'number' }
                            }
                        }
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate, fastify.auth.requirePermission('course:read')],
    }, async function (request, reply) {
        const { user } = request;
        const { page = 1, pageSize = 10, search, type } = request.query;
        const skip = (page - 1) * pageSize;
        const take = pageSize;
        const where = {
            name: {
                contains: search || '',
                mode: 'insensitive'
            },
            ...(type ? { type: type } : {}),
            institutionId: user.institutionId
        }
        const [data, total] = await Promise.all([
            fastify.prisma.course.findMany({
                where: where,
                select: {
                    id: true,
                    name: true,
                    type: true,
                    duration: true,
                    teacherId: true,
                    isDirectSale: true,
                    deductionPerClass: true,
                    picture: true,
                    status: true,
                    ProductCourse: {
                        select: {
                            productId: true,
                            product: {
                                select: {
                                    id: true,
                                    name: true,
                                }
                            }
                        }
                    }
                },
                take,
                skip
            }),
            fastify.prisma.course.count({
                where: where,
            })
        ])
        reply.success({
            data: {
                list: data,
                page,
                pageSize,
                total
            },

            // total
        })
    })
    // 获取课程select列表
    fastify.get('/courses/select', {
        schema: {
            tags: ['courses'],
            summary: '获取课程select列表',
            querystring: {
                type: 'object',
                properties: {
                    id: {
                        type: 'boolean',
                        default: true
                    },
                    name: {
                        type: 'boolean',
                        default: true
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
    }, async function (request, reply) {
        const { user } = request;
        const { id, name } = request.query;
        const data = await fastify.prisma.course.findMany({
            where: {
                institutionId: user.institutionId
            },
            select: {
                id: id,
                name: name
            }
        })
        reply.success({
            data
        })
    
    })

    //  更新课程
    fastify.put('/courses/:courseId', {
        schema: {
            tags: ['courses'],
            summary: '更新课程',
            body: {
                type: 'object',
                // required: ['name'],
                properties: {
                    name: { type: 'string' },
                    type: { type: 'string' },
                    status: { type: 'string' },
                    picture: { type: 'string' },
                    isShow: { type: 'boolean' },
                    duration: { type: 'number' },
                    description: { type: 'string' },
                    isDirectSale: { type: 'boolean' },
                    price: { type: 'number' },
                    deductionPerClass: { type: 'number' },
                    isDeductOnAttendance: { type: 'boolean' },
                    isDeductOnLeave: { type: 'boolean' },
                    isDeductOnAbsence: { type: 'boolean' },
                }

            }
        },
        onRequest: [fastify.auth.authenticate, fastify.auth.requirePermission('course:update')],
        handler: async (request, reply) => {
            const user = request.user;
            const { courseId } = request.params;
            const { name, type, status, picture, isShow, duration, description, isDirectSale, price, deductionPerClass, isDeductOnAttendance, isDeductOnLeave, isDeductOnAbsence } = request.body;
            try {
                const result = await fastify.prisma.course.update({
                    where: {
                        id: courseId,
                        institutionId: user.institutionId
                    },
                    data: {
                        name,
                        type,
                        picture,
                        status,
                        isShow,
                        duration,
                        description,
                        isDirectSale,
                        price,
                        deductionPerClass,
                        isDeductOnAttendance,
                        isDeductOnLeave,
                        isDeductOnAbsence
                    }
                })
                reply.success({
                    data: result,
                    message: '更新课程成功',
                })
            } catch (error) {
                throw new INTERNAL_ERROR(error.message || '更新课程失败');
            }
        }
    })

    //创建课程
    fastify.post('/courses', {
        schema: {
            tags: ['courses'],
            summary: '创建课程',
            body: {
                type: 'object',
                required: ['name'],
                properties: {
                    name: { type: 'string' },
                    type: {
                        type: 'string', 
                        default: 'group'
                    },
                    picture: {
                        type: 'string',
                        default: '',
                        description: '课程封面'
                    },
                    isShow: {
                        type: 'boolean',
                        default: false,
                        description: '是否显示'
                    },
                    description: {
                        type: 'string'
                    },
                    duration: {
                        type: 'number',
                        default: 60,
                        description: '课程时长'
                    },
                    isDirectSale: {
                        type: 'boolean',
                        default: false,
                        description: '是否直销'
                    },
                    price: {
                        type: 'number',
                        default: 0.0,
                        description: '直销价格'
                    },
                    deductionPerClass: {
                        type: 'number',
                        default: 1,
                        description: '课时扣除/次'
                    },
                    isDeductOnAttendance: {
                        type: 'boolean',
                        default: true,
                        description: '到课扣课'
                    },
                    isDeductOnLeave: {
                        type: 'boolean',
                        default: false,
                        description: '请假扣课'
                    },
                    isDeductOnAbsence: {
                        type: 'boolean',
                        default: false,
                        description: '缺勤扣课'
                    },
                    status: {
                        type: 'string',
                        default: 'active',
                        description: '课程状态'
                    }
                },
            }
        },
        onRequest: [fastify.auth.authenticate, fastify.auth.requirePermission('course:create')],
    }, async function (request, reply) {
        const user = request.user;
        const {
            name, type, picture, isShow, duration, description, isDirectSale, price, deductionPerClass, isDeductOnAttendance,
            isDeductOnLeave, isDeductOnAbsence, status
        } = request.body;
        
        try {
            const id = uuidv4();
            const client = await fastify.pg.connect();
            
            try {
                // 开始事务
                await client.query('BEGIN');
                
                // 插入课程数据
                const result = await client.query(
                    `INSERT INTO courses (
                        id, name, type, picture, "isShow", duration, description, 
                        "isDirectSale", price, "deductionPerClass", 
                        "isDeductOnAttendance", "isDeductOnLeave", "isDeductOnAbsence",
                        "institutionId", "status", "createdAt", "updatedAt"
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, NOW(), NOW())
                    RETURNING *`,
                    [
                        id, name, type, picture, isShow, duration, description,
                        isDirectSale, price, deductionPerClass,
                        isDeductOnAttendance, isDeductOnLeave, isDeductOnAbsence,
                        user.institutionId, status
                    ]
                );
                
                // 提交事务
                await client.query('COMMIT');
                
                reply.success({
                    data: result.rows[0],
                    message: '创建课程成功'
                });
            } catch (error) {
                // 回滚事务
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            throw new INTERNAL_ERROR(error.message || '创建课程失败');
        }
    });

    // 删除课程
    fastify.delete('/courses/:courseId', {
        schema: {
            tags: ['courses'],
            summary: '删除课程',
            params: {
                type: 'object',
                required: ['courseId'],
                properties: {
                    courseId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate, fastify.auth.requirePermission('course:delete')],
        handler: async function (request, reply) {
            const { courseId } = request.params;
            const user = request.user;
            
            try {
                const client = await fastify.pg.connect();
                
                try {
                    // 开始事务
                    await client.query('BEGIN');

                    // 删除课程
                    await client.query(
                        `DELETE FROM courses 
                         WHERE id = $1 AND "institutionId" = $2
                         RETURNING id`,
                        [courseId, user.institutionId]
                    );
                    
                    // 提交事务
                    await client.query('COMMIT');
                    
                    reply.success({
                        message: '删除课程成功'
                    });
                } catch (error) {
                    // 回滚事务
                    await client.query('ROLLBACK');
                    throw error;
                } finally {
                    client.release();
                }
            } catch (error) {
                throw new INTERNAL_ERROR(error.message || '删除课程失败');
            }
        }
    });
}