"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3635],{3635:(e,s,r)=>{r.r(s),r.d(s,{default:()=>x});var a=r(95155),i=r(57001),n=r(12115),t=r(32919),l=r(55028),d=r(13515),c=r(48436),o=r(75521);let h=(0,l.default)(()=>r.e(6744).then(r.bind(r,96744)),{loadableGenerated:{webpack:()=>[96744]},ssr:!1}),u=e=>{var s;let r=[e.id];return(null===(s=e.children)||void 0===s?void 0:s.length)>0&&e.children.forEach(e=>{r.push(...u(e))}),r},p=e=>Array.isArray(e)?e.reduce((e,s)=>[...e,...u(s)],[]):[];function x(e){let{role:s}=e,[r,l]=(0,n.useState)(!1),{data:u,isLoading:x}=(0,o.wZ)(s.id,{skip:!r}),[m,{isLoading:k}]=(0,d.Ry)(),g=(0,n.useMemo)(()=>u?p(u):[],[u]),w=(0,n.useCallback)(async e=>{try{console.log("保存权限:",e),await m({roleId:s.id,data:{permissions:e}}),c.l.success("权限更新成功"),l(!1)}catch(e){console.error("更新权限失败:",e),c.l.error("更新权限失败")}},[s.id,m]);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.p,{icon:t.A,tooltipText:"权限设置",onClick:()=>l(!0),disabled:x||k}),r&&(0,a.jsx)(h,{open:r,onOpenChange:l,initialSelected:g,onSave:w})]})}},32919:(e,s,r)=>{r.d(s,{A:()=>a});let a=(0,r(19946).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},57001:(e,s,r)=>{r.d(s,{p:()=>t});var a=r(95155),i=r(30285),n=r(46102);function t(e){let{icon:s,tooltipText:r,tooltipSide:t="top",tooltipAlign:l="center",delayDuration:d=300,variant:c="ghost",size:o="icon",className:h="h-8 w-8 hover:bg-muted",...u}=e;return(0,a.jsx)(n.Bc,{delayDuration:d,children:(0,a.jsxs)(n.m_,{children:[(0,a.jsx)(n.k$,{asChild:!0,children:(0,a.jsx)(i.$,{variant:c,size:o,className:h,...u,children:(0,a.jsx)(s,{className:"h-4 w-4 text-muted-foreground"})})}),(0,a.jsx)(n.ZI,{side:t,align:l,className:"font-medium text-xs px-3 py-1.5",children:(0,a.jsx)("p",{children:r})})]})})}}}]);