"use client"

import { useEffect, useState } from "react"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { cn } from "@/lib/utils"
import { Check, ChevronsUpDown } from "lucide-react"
import { useTeacher } from "@/hooks/useTeacher"
import useCourses from "@/hooks/useCourses"
import type { z } from "zod"
import type { UseFormReturn } from "react-hook-form"
import type formSchema from "../schema/form"
import { useInstitution } from "@/hooks/institution/useInstitution"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

interface SelectOption {
  label: string
  value: string
}

interface BasicInfoFormProps {
  form: UseFormReturn<z.infer<typeof formSchema>>
}

export default function BasicInfoForm({ form }: BasicInfoFormProps) {
  const { getCourseSelectList } = useCourses()
  const { getInstitutionClassroomSelect } = useInstitution()

  const [courseOptions, setCourseOptions] = useState<SelectOption[]>([])
  const [classroomOpen, setClassroomOpen] = useState(false)
  const [classrooms, setClassrooms] = useState<
    {
      id: string
      name: string
    }[]
  >([])

  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        const [classroomsResponse, coursesResponse] = await Promise.all([
          getInstitutionClassroomSelect(),
          getCourseSelectList(),
        ])


        setClassrooms(classroomsResponse)

        setCourseOptions(
          coursesResponse.map((course: any) => ({
            label: course.name,
            value: course.id,
          })),
        )
      } catch (error) {
        console.error("加载表单数据失败", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  return (
    <div className="space-y-6">
      {loading ? (
        <div className="space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-9 w-full" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-9 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-9 w-full" />
            </div>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-9 w-full" />
          </div>
        </div>
      ) : (
        <>
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-slate-700">班级名称</FormLabel>
                <FormControl>
                  <Input
                    placeholder="请输入班级名称"
                    className="h-10 border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 text-sm bg-slate-50 hover:bg-white transition-colors"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="courseId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-slate-700">课程</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger
                        className="h-10 border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 text-sm bg-slate-50 hover:bg-white transition-colors"
                      >
                        <SelectValue placeholder="选择课程" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="max-h-[280px]">
                      {courseOptions.length === 0 ? (
                        <div className="py-6 text-center text-sm text-slate-500">暂无课程数据</div>
                      ) : (
                        courseOptions.map((course) => (
                          <SelectItem key={course.value} value={course.value} className="text-sm">
                            {course.label}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />

          <FormField
            control={form.control}
            name="maxStudentCount"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-slate-700">最多人数</FormLabel>
                <FormControl>
                  <Input
                    placeholder="班级最多学员人数"
                    className="h-10 border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 text-sm bg-slate-50 hover:bg-white transition-colors"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />

          </div>

          <FormField
            control={form.control}
            name="classroom"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-slate-700">教室选择</FormLabel>
                <Select onValueChange={field.onChange} value={field.value || ""}>
                  <FormControl>
                    <SelectTrigger
                      className="h-10 border-slate-200 focus:border-slate-400 focus:ring-1 focus:ring-slate-300 text-sm bg-slate-50 hover:bg-white transition-colors"
                    >
                      <SelectValue placeholder="选择教室" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="max-h-[280px]">
                    <SelectItem value="none" className="text-sm">
                      选择教室
                    </SelectItem>
                    {classrooms.length === 0 ? (
                      <div className="py-6 text-center text-sm text-slate-500">暂无教室数据</div>
                    ) : (
                      classrooms.map((room) => (
                        <SelectItem key={room.id} value={room.id} className="text-sm">
                          {room.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
        </>
      )}
    </div>
  )
}

