import fetch from 'node-fetch';
import { v4 as uuidv4 } from 'uuid';
import uploadToQiniu from "../libs/qiniu.js";

/**
 * 文本转语音
 * @param {string} text - 要转换的文本
 * @param {string} voice - 语音类型 (female, male, child)
 * @param {number} speed - 语速 (0.5-2)
 * @param {string} format - 音频格式 (mp3, wav)
 * @returns {Promise<Object>} 转换结果
 */
export async function textToSpeech(text, voice = 'female', speed = 1, format = 'mp3') {
    try {
        const startTime = Date.now();
        
        // 映射语音类型到API参数
        const voiceMap = {
            'female': 0, // 女声
            'male': 1,   // 男声
            'child': 2   // 童声
        };
        
        const voiceType = voiceMap[voice] || 0;
        
        // 调用百度文心千帆TTS API
        const response = await fetch('https://qianfan.baidubce.com/v1/audio/tts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.BAIDU_API_KEY}`
            },
            body: JSON.stringify({
                text: text,
                voice_type: voiceType,
                speed: speed,
                format: format
            })
        });
        
        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (!data.audio_url) {
            throw new Error('未返回有效音频 URL');
        }
        
        // 计算生成时间
        const generateTime = Date.now() - startTime;
        console.log(`语音生成完成，用时 ${generateTime} 毫秒`);
        
        // 上传到七牛云
        const id = uuidv4();
        const audioUrl = await uploadToQiniu(data.audio_url, id, format);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        console.log(`语音上传完成，总用时 ${duration} 毫秒`);
        
        return {
            id: data.id || id,
            created: data.created || new Date().toISOString(),
            audioUrl: audioUrl,
            format: format,
            duration: data.duration || null // 音频时长（秒）
        };
    } catch (error) {
        throw new Error(`文本转语音失败: ${error.message}`);
    }
}

/**
 * 语音转文本
 * @param {string} audioUrl - 语音文件URL
 * @param {string} language - 语言 (zh, en, auto)
 * @returns {Promise<Object>} 转换结果
 */
export async function speechToText(audioUrl, language = 'zh') {
    try {
        const startTime = Date.now();
        
        // 下载音频文件
        const audioResponse = await fetch(audioUrl);
        if (!audioResponse.ok) {
            throw new Error(`下载音频文件失败: ${audioResponse.statusText}`);
        }
        
        // 获取音频数据
        const audioBuffer = await audioResponse.buffer();
        
        // 调用百度文心千帆ASR API
        const response = await fetch('https://qianfan.baidubce.com/v1/audio/asr', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.BAIDU_API_KEY}`
            },
            body: JSON.stringify({
                audio: audioBuffer.toString('base64'),
                language: language === 'auto' ? 'auto' : (language === 'en' ? 'en' : 'zh'),
                format: audioUrl.toLowerCase().endsWith('.wav') ? 'wav' : 'mp3'
            })
        });
        
        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (!data.result) {
            throw new Error('未返回有效识别结果');
        }
        
        // 计算处理时间
        const processTime = Date.now() - startTime;
        console.log(`语音识别完成，用时 ${processTime} 毫秒`);
        
        return {
            id: data.id || uuidv4(),
            created: data.created || new Date().toISOString(),
            text: data.result,
            language: data.language || language,
            confidence: data.confidence || null
        };
    } catch (error) {
        throw new Error(`语音转文本失败: ${error.message}`);
    }
}
