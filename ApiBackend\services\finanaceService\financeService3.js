import financeModel from '../../models/financeModel.js';
import {
  parseISO,
  fromUnixTime,
  format,
  startOfDay,
  endOfDay,
  addDays,
  differenceInDays,
  startOfMonth,
  endOfMonth,
  addMonths,
  isBefore,
  isValid,
  getTime,
} from 'date-fns';

/**
 * Additional finance service methods
 */
export default {
  /**
   * Generate daily data for education overview
   * @private
   */
  async _generateDailyData(institutionId, start, end) {
    const result = [];
    const startDay = startOfDay(start);
    const endDay = endOfDay(end);

    // 计算天数差
    const daysDiff = differenceInDays(endDay, startDay) + 1;

    for (let i = 0; i < daysDiff; i++) {
      const currentDate = addDays(startDay, i);
      const dayStart = getTime(startOfDay(currentDate));
      const dayEnd = getTime(endOfDay(currentDate));

      const [count, attendanceCount] = await Promise.all([
        financeModel.countSchedules(institutionId, dayStart, dayEnd),
        financeModel.countSchedules(institutionId, dayStart, dayEnd, 'attendance')
      ]);

      // 格式化日期为 YYYY-MM-DD
      const dateStr = format(currentDate, 'yyyy-MM-dd');

      result.push({
        date: dateStr,
        count,
        attendanceCount
      });
    }

    return result;
  },

  /**
   * Generate monthly data for education overview
   * @private
   */
  async _generateMonthlyData(institutionId, start, end) {
    const result = [];
    const months = [];

    // 获取起止月份列表
    let current = startOfMonth(start);
    const lastMonth = startOfMonth(end);

    // 生成月份列表
    while (!isBefore(lastMonth, current)) {
      const monthStart = getTime(startOfMonth(current));
      const monthEnd = getTime(endOfMonth(current));

      months.push({
        start: monthStart,
        end: monthEnd,
        label: format(current, 'yyyy-MM')
      });

      // 移至下月
      current = addMonths(current, 1);
    }

    // 获取各月统计数据
    for (const monthData of months) {
      const [count, attendanceCount] = await Promise.all([
        financeModel.countSchedules(institutionId, monthData.start, monthData.end),
        financeModel.countSchedules(institutionId, monthData.start, monthData.end, 'attendance')
      ]);

      result.push({
        date: monthData.label,
        count,
        attendanceCount
      });
    }

    return result;
  },

  /**
   * Get student fees data
   */
  async getStudentFees({ user, page, pageSize, search }) {
    const where = {
      institutionId: user.institutionId,
    };
    
    if (search) {
      where.student = {
        name: {
          contains: search,
        }
      };
    }
    
    const skip = (page - 1) * pageSize;
    const take = pageSize;
    const orderBy = {
      payTime: 'desc',
    };

    const [total, studentProducts, studentProduct] = await Promise.all([
      financeModel.getStudentProductsCount(where),
      financeModel.getStudentProducts(where, skip, take, orderBy),
      financeModel.getAllStudentProducts(user.institutionId)
    ]);
    
    const transformedStudentProducts = studentProducts.map((item) => {
      return {
        ...item,
        payTime: Number.parseInt(item.payTime),
      };
    });

    const remainingCount = studentProduct.reduce((acc, curr) => {
      acc.remainingSessionCount += Number(curr.remainingSessionCount) || 0;
      acc.remainingBalance += Number(curr.remainingBalance) || 0;
      return acc;
    }, { remainingSessionCount: 0, remainingBalance: 0 });

    return {
      total,
      page,
      pageSize,
      list: transformedStudentProducts,
      ...remainingCount,

    };
  },

  /**
   * Get student class summary data
   */
  async getStudentClassSummary({ user, startTime, endTime, checkType }) {
    const where = {
      institutionId: user.institutionId,
    };
    
    if (startTime && endTime) {
      where.startDate = {
        gte: startTime,
        lte: endTime
      };
    } else {
      where.startDate = {
        gte: new Date().getTime() - 1000 * 60 * 60 * 24 * 30,
        lte: new Date().getTime()
      };
    }

    // =================== 时间范围数据生成 ===================
    const parseTimestamp = (timestamp) => {
      if (!timestamp) return new Date();

      const numTimestamp = Number(timestamp);
      if (isNaN(numTimestamp)) return new Date();

      // Handle milliseconds (13 digits) or seconds (10 digits)
      const date = String(numTimestamp).length <= 10
        ? fromUnixTime(numTimestamp)
        : new Date(numTimestamp);

      return isValid(date) ? date : new Date();
    };

    // 解析输入的起止日期
    let startDate = parseTimestamp(startTime);
    let endDate = parseTimestamp(endTime);

    // 确保日期有效性
    if (isBefore(endDate, startDate)) {
      [startDate, endDate] = [endDate, startDate];
    }

    let timeRangeData = [];

    // 日视图数据生成
    if (checkType === 'daily') {
      timeRangeData = await this._generateDailyDataForClassSummary(startDate, endDate);
    } else if (checkType === 'monthly') {
      timeRangeData = await this._generateMonthlyDataForClassSummary(startDate, endDate);
    }

    const result = await financeModel.getClassesScheduleWithAttendance(where);

    const dataMap = new Map();

    timeRangeData.forEach((item) => {
      const dateKey = item.date;
      if (!dataMap.has(dateKey)) {
        dataMap.set(dateKey, {
          count: 0,
          attendanceCount: 0,
          attendanceAmount: 0
        });
      }
    });

    result.forEach((item) => {
      const dateKey = item.startDate;
      const date = new Date(Number(dateKey));
      let dateStr;
      if (checkType === 'daily') {
        dateStr = format(date, 'yyyy-MM-dd');
      } else if (checkType === 'monthly') {
        dateStr = format(date, 'yyyy-MM');
      }
      if (!dataMap.has(dateStr)) {
        dataMap.set(dateStr, {
          count: 0,
          attendanceCount: 0,
          attendanceAmount: 0
        });
      }
      const data = dataMap.get(dateStr);
      data.count = item.StudentWeeklySchedule.length;
      data.attendanceCount = item.StudentWeeklySchedule.reduce((acc, curr) => acc + Number(curr.attendanceCount || 0), 0);
      data.attendanceAmount = item.StudentWeeklySchedule.reduce((acc, curr) => acc + Number(curr.attendanceAmount || 0), 0);
      dataMap.set(dateStr, data);
    });

    const dataArray = Array.from(dataMap.entries());
    dataArray.sort((a, b) => a[0] - b[0]);
    const transformedData = dataArray.map(([date, data]) => ({
      date,
      ...data
    }));

    return {
      timeRangeData,
      list: transformedData
    };
  },

  /**
   * Generate daily data for class summary
   * @private
   */
  async _generateDailyDataForClassSummary(start, end) {
    const result = [];
    const startDay = startOfDay(start);
    const endDay = endOfDay(end);

    // 计算天数差
    const daysDiff = differenceInDays(endDay, startDay) + 1;

    for (let i = 0; i < daysDiff; i++) {
      const currentDate = addDays(startDay, i);
      // 格式化日期为 YYYY-MM-DD
      const dateStr = format(currentDate, 'yyyy-MM-dd');

      result.push({
        date: dateStr,
      });
    }

    return result;
  },

  /**
   * Generate monthly data for class summary
   * @private
   */
  async _generateMonthlyDataForClassSummary(start, end) {
    const result = [];
    const months = [];

    // 获取起止月份列表
    let current = startOfMonth(start);
    const lastMonth = startOfMonth(end);

    // 生成月份列表
    while (!isBefore(lastMonth, current)) {
      months.push({
        label: format(current, 'yyyy-MM')
      });

      // 移至下月
      current = addMonths(current, 1);
    }

    // 获取各月统计数据
    for (const monthData of months) {
      result.push({
        date: monthData.label,
      });
    }

    return result;
  },
};
