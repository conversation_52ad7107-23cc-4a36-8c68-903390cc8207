(()=>{var e={};e.id=109,e.ids=[109],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,r)=>{let{createProxy:s}=r(39844);e.exports=s("F:\\trae\\cardmees\\fronend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17720:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26373:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:d="",children:n,iconNode:c,...x},o)=>(0,s.createElement)("svg",{ref:o,...i,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:l("lucide",d),...x},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(n)?n:[n]])),n=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},n)=>(0,s.createElement)(d,{ref:n,iconNode:t,className:l(`lucide-${a(e)}`,r),...i}));return r.displayName=`${e}`,r}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32769:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},33873:e=>{"use strict";e.exports=require("path")},40918:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},51465:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},52568:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23))},53148:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59552:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var s=r(37413);r(61120);var a=r(4536),l=r.n(a),i=r(26373);let d=(0,i.A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),n=(0,i.A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),c=(0,i.A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var x=r(53148),o=r(32769),m=r(72845),h=r(51465),g=r(75234),p=r(40918),u=r(91142);let y=function(){let e=[{icon:d,title:"智能排课算法",description:"基于AI算法的智能排课，自动优化时间安排，提高资源利用率",details:["AI智能算法优化","多约束条件处理","资源最优分配","时间冲突避免","效率最大化排课"]},{icon:n,title:"冲突检测提醒",description:"实时检测时间、教室、教师冲突，提前预警避免排课错误",details:["实时冲突检测","多维度冲突分析","预警提醒机制","冲突解决建议","历史冲突记录"]},{icon:c,title:"教室资源管理",description:"统一管理教室资源，合理分配使用，提高场地利用效率",details:["教室信息管理","容量限制设置","设备配置记录","使用率统计","维护状态跟踪"]},{icon:x.A,title:"课程时间优化",description:"根据教学规律和学员特点，优化课程时间安排",details:["黄金时间段识别","学员作息考虑","教师偏好设置","课程间隔优化","连续课程安排"]},{icon:o.A,title:"批量排课操作",description:"支持批量导入、复制、调整课程，大幅提升排课效率",details:["批量课程导入","模板复制功能","批量时间调整","快速复制排课","批量状态更新"]},{icon:m.A,title:"排课数据分析",description:"深入分析排课数据，优化资源配置，提升管理决策",details:["资源利用率分析","教师工作量统计","热门时段识别","排课效率评估","趋势预测分析"]}];return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,s.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50 backdrop-blur-sm bg-opacity-90 dark:bg-opacity-90",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)(l(),{href:"/features",className:"flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors",children:[(0,s.jsx)(h.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"返回功能列表"})]})}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsxs)(l(),{href:"/",className:"flex items-center space-x-2",children:[(0,s.jsx)(g.A,{className:"h-8 w-8 text-indigo-600 dark:text-indigo-400"}),(0,s.jsxs)("span",{className:"text-xl font-bold text-gray-800 dark:text-white",children:[(0,s.jsx)("span",{className:"text-indigo-600 dark:text-indigo-400",children:"Card"}),"Mees"]})]})}),(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsx)(l(),{href:"/dashboard",className:"px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-lg transition-colors",children:"进入系统"})})]})})}),(0,s.jsx)("div",{className:"bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 dark:from-gray-800 dark:via-gray-900 dark:to-gray-800 py-20",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-6",children:(0,s.jsx)("div",{className:"w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-2xl flex items-center justify-center",children:(0,s.jsx)(p.A,{className:"w-10 h-10 text-green-600 dark:text-green-400"})})}),(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white sm:text-5xl mb-4",children:"课程安排系统"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"智能化排课解决方案，自动处理复杂的时间、教室、教师安排，让排课变得简单高效"})]})})}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"核心功能特性"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"六大核心模块，构建智能化排课管理体系"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map((e,t)=>{let r=e.icon;return(0,s.jsx)("div",{className:"group",children:(0,s.jsxs)("div",{className:"relative p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-700",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-600 opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-300"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,s.jsx)(r,{className:"w-6 h-6 text-green-600 dark:text-green-400"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:e.description}),(0,s.jsx)("ul",{className:"space-y-2",children:e.details.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400",children:[(0,s.jsx)(u.A,{className:"w-4 h-4 text-green-500"}),(0,s.jsx)("span",{children:e})]},t))})]})]})},t)})})]}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 py-16",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"使用效果统计"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"数据证明智能排课系统的显著效果"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{title:"排课效率提升",description:"智能算法大幅提升排课速度和准确性",percentage:"90%"},{title:"资源利用率",description:"优化资源配置，提高教室和教师利用率",percentage:"85%"},{title:"冲突减少",description:"智能检测机制显著减少排课冲突",percentage:"95%"},{title:"管理成本降低",description:"自动化排课减少人工成本投入",percentage:"70%"}].map((e,t)=>(0,s.jsx)("div",{className:"text-center group",children:(0,s.jsxs)("div",{className:"relative p-6 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border border-green-100 dark:border-green-800/30 hover:shadow-lg transition-all duration-300",children:[(0,s.jsx)("div",{className:"text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-emerald-600 mb-2",children:e.percentage}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-sm",children:e.description})]})},t))})]})}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"智能排课流程"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"简单四步，完成复杂的课程安排"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0",children:(0,s.jsx)("span",{className:"text-green-600 dark:text-green-400 font-bold",children:"1"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"设置基础信息"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"录入教师信息、教室资源、课程设置等基础数据，为智能排课提供数据支撑。"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0",children:(0,s.jsx)("span",{className:"text-green-600 dark:text-green-400 font-bold",children:"2"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"配置排课规则"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"设置排课约束条件，如教师偏好时间、教室容量限制、课程间隔要求等。"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0",children:(0,s.jsx)("span",{className:"text-green-600 dark:text-green-400 font-bold",children:"3"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"智能算法排课"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"系统自动运行智能算法，综合考虑各种约束条件，生成最优排课方案。"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0",children:(0,s.jsx)("span",{className:"text-green-600 dark:text-green-400 font-bold",children:"4"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"审核与调整"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"查看排课结果，进行必要的手动调整，确认后发布课程表给相关人员。"})]})]})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl p-8 border border-green-100 dark:border-green-800/30",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-6",children:"系统优势"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 text-green-500"}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"AI智能算法优化"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 text-green-500"}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"实时冲突检测"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 text-green-500"}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"资源最优配置"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 text-green-500"}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"批量操作支持"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 text-green-500"}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"数据分析洞察"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 text-green-500"}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"灵活调整机制"})]})]}),(0,s.jsxs)("div",{className:"mt-8 p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white mb-2",children:"排课效率对比"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"传统人工排课"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"2-3天"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"智能系统排课"}),(0,s.jsx)("span",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:"10-30分钟"})]})]})]})]})]})]}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-green-600 to-emerald-700 dark:from-green-800 dark:to-emerald-900",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"开始使用智能排课系统"}),(0,s.jsx)("p",{className:"text-xl text-green-200 mb-8",children:"让AI为您处理复杂的排课工作，专注于教学质量提升"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(l(),{href:"/dashboard",className:"px-8 py-4 bg-white text-green-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors",children:"立即体验"}),(0,s.jsx)(l(),{href:"/features",className:"px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-green-600 transition-colors",children:"查看其他功能"})]})]})})]})}},60483:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>c});var s=r(65239),a=r(48088),l=r(88170),i=r.n(l),d=r(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);r.d(t,n);let c={children:["",{children:["features",{children:["course-scheduling",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,59552)),"F:\\trae\\cardmees\\fronend\\src\\app\\features\\course-scheduling\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,x=["F:\\trae\\cardmees\\fronend\\src\\app\\features\\course-scheduling\\page.tsx"],o={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/features/course-scheduling/page",pathname:"/features/course-scheduling",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72845:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},74075:e=>{"use strict";e.exports=require("zlib")},75234:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91142:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7392,5814,3019],()=>r(60483));module.exports=s})();