// export async function CalendarViewDataGeneration(fastify, opts) {
//     const result = [];
//                 const startDay = startOfDay(start);
//                 const endDay = endOfDay(end);
                
//                 // 计算天数差
//                 const daysDiff = differenceInDays(endDay, startDay) + 1;
                
//                 for (let i = 0; i < daysDiff; i++) {
//                     const currentDate = addDays(startDay, i);
//                     const dayStart = getTime(startOfDay(currentDate));
//                     const dayEnd = getTime(endOfDay(currentDate));
                    
//                     const [count, attendanceCount] = await Promise.all([
//                         countSchedules(dayStart, dayEnd),
//                         countSchedules(dayStart, dayEnd, 'attendance')
//                     ]);
                    
//                     // 格式化日期为 YYYY-MM-DD
//                     const dateStr = format(currentDate, 'yyyy-MM-dd');
                    
//                     result.push({
//                         date: dateStr,
//                         count,
//                         attendanceCount
//                     });
//                 }
                
//                 return result;
// }

