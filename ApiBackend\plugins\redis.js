import fp from "fastify-plugin";
import { redis, redisWebSocket, redisTask, redisSystem } from "../config/redis.js";
import redisResilience from "../utils/redis-resilience.js";

// Redis 监控指标
const redisMetrics = {
    commands: 0,
    errors: 0,
    timeouts: 0,
    reconnects: 0,
    circuitBreaks: 0,
    totalResponseTime: 0,
    avgResponseTime: 0
};

// 健康检查函数
async function checkRedisHealth(redisClient) {
    try {
        const startTime = process.hrtime.bigint();
        await redisClient.ping();
        const endTime = process.hrtime.bigint();
        const responseTime = Number(endTime - startTime) / 1000000; // 转换为毫秒
        return {
            status: 'healthy',
            responseTime: responseTime.toFixed(2) + 'ms'
        };
    } catch (error) {
        return {
            status: 'unhealthy',
            error: error.message
        };
    }
}

async function redisConnector(fastify, options) {
    // 注册弹性Redis工具
    fastify.decorate("redisResilience", redisResilience);

    // 注册Redis监控指标
    fastify.decorate("redisMetrics", redisMetrics);

    // 注册Redis健康检查
    fastify.decorate("checkRedisHealth", async () => {
        return {
            main: await checkRedisHealth(redis),
            websocket: await checkRedisHealth(redisWebSocket),
            task: await checkRedisHealth(redisTask),
            system: await checkRedisHealth(redisSystem)
        };
    });

    // 设置事件监听器
    const setupRedisListeners = (client, name) => {
        client.on("connect", () => {
            fastify.log.info(`${name} client connected`);
        });

        client.on("ready", () => {
            fastify.log.info(`${name} client ready`);
        });

        client.on("error", (err) => {
            redisMetrics.errors++;
            fastify.log.error(`${name} connection error:`, err);
        });

        client.on("close", () => {
            fastify.log.warn(`${name} connection closed`);
        });

        client.on("reconnecting", () => {
            redisMetrics.reconnects++;
            fastify.log.warn(`${name} client reconnecting...`);
        });

        client.on("end", () => {
            fastify.log.warn(`${name} connection ended`);
        });

        // 监控命令执行
        const originalSend = client.sendCommand;
        client.sendCommand = function(command) {
            redisMetrics.commands++;
            const startTime = process.hrtime.bigint();

            const promise = originalSend.apply(this, arguments);

            promise.then(() => {
                const endTime = process.hrtime.bigint();
                const responseTime = Number(endTime - startTime) / 1000000; // 转换为毫秒
                redisMetrics.totalResponseTime += responseTime;
                redisMetrics.avgResponseTime = redisMetrics.totalResponseTime / redisMetrics.commands;
            }).catch(err => {
                if (err.message.includes('timeout')) {
                    redisMetrics.timeouts++;
                }
            });

            return promise;
        };
    };

    // 为每个Redis客户端设置监听器
    setupRedisListeners(redis, "Redis");
    setupRedisListeners(redisWebSocket, "RedisWebSocket");
    setupRedisListeners(redisTask, "RedisTask");
    setupRedisListeners(redisSystem, "RedisSystem");

    // 将Redis客户端注册到fastify实例
    fastify.decorate("redis", redis);
    fastify.decorate("redisWebSocket", redisWebSocket);
    fastify.decorate("redisTask", redisTask);
    fastify.decorate("redisSystem", redisSystem);

    // 在服务器关闭时关闭Redis连接
    fastify.addHook("onClose", async (instance) => {
        try {
            await Promise.all([
                redis.quit(),
                redisWebSocket.quit(),
                redisTask.quit(),
                redisSystem.quit()
            ]);
            fastify.log.info("All Redis connections closed gracefully");
        } catch (error) {
            fastify.log.error("Error closing Redis connections:", error);
        }
    })
}

export default fp(redisConnector, {
    name: "fastify-redis",
})