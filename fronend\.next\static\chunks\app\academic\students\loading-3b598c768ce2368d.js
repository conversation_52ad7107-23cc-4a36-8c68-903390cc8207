(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2504],{8050:(e,s,a)=>{Promise.resolve().then(a.bind(a,87034))},59434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>c});var r=a(52596),l=a(39688);function c(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,l.QP)((0,r.$)(s))}},68856:(e,s,a)=>{"use strict";a.d(s,{E:()=>c});var r=a(95155),l=a(59434);function c(e){let{className:s,...a}=e;return(0,r.jsx)("div",{className:(0,l.cn)("animate-pulse rounded-md bg-muted",s),...a})}},87034:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>c});var r=a(95155),l=a(68856);function c(){return(0,r.jsxs)("div",{className:"space-y-4 p-4",children:[(0,r.jsx)("div",{className:"flex items-center space-x-2 border-b pb-1",children:Array(6).fill(0).map((e,s)=>(0,r.jsx)(l.E,{className:"h-9 w-24 rounded-md ".concat(0===s?"bg-primary/20":"bg-muted")},s))}),(0,r.jsxs)("div",{className:"space-y-4 mt-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)(l.E,{className:"h-10 w-64"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(l.E,{className:"h-9 w-24"}),(0,r.jsx)(l.E,{className:"h-9 w-24"})]})]}),(0,r.jsxs)("div",{className:"border rounded-md",children:[(0,r.jsx)("div",{className:"flex border-b p-2 bg-muted/30",children:Array(6).fill(0).map((e,s)=>(0,r.jsx)(l.E,{className:"h-6 flex-1 mx-2"},s))}),Array(8).fill(0).map((e,s)=>(0,r.jsx)("div",{className:"flex border-b p-3",children:Array(6).fill(0).map((e,s)=>(0,r.jsx)(l.E,{className:"h-5 flex-1 mx-2"},s))},s))]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,r.jsx)(l.E,{className:"h-8 w-40"}),(0,r.jsx)(l.E,{className:"h-8 w-64"})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,6315,7358],()=>s(8050)),_N_E=e.O()}]);