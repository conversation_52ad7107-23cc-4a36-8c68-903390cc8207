import logService from '../services/log.js';

/**
 * 日志控制器
 */
export default  {
        /**
     * 获取操作日志列表
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     */
    getLogs: async (request, reply) => {
        try {
            const user = request.user;
            const queryParams = request.query;
            
                const result = await logService.getLogs(
                    request.server.pg, 
                    queryParams, 
                    user.institutionId
                );
                
                reply.success({
                    data: result,
                    message: '获取操作日志成功'
                });
            } catch (error) {
                request.log.error(error);
                throw new Error('获取操作日志失败');
            // reply.internalServerError('获取操作日志失败');
        }
    }
}
