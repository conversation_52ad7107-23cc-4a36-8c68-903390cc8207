export const authLoginSchema = {
	tags: ['auth'],
	summary: '用户登录',
	description: '通过账号密码登录',
	body: {
		type: 'object',
		required: ['account', 'password'],
		properties: {
			account: {
				type: 'string',
				minLength: 3,
				maxLength: 20,
				description: '账号'
			},
			password: {
				type: 'string',
				minLength: 6,
				description: '密码'
			}
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'object',
					properties: {
						user: {
							type: 'object',
							properties: {
								id: { type: 'string' },
								account: { type: 'string' },
								name: { type: 'string' },
								institution_subjectName: { type: 'string' },
								institution_name: { type: 'string' },
								institution_logo: { type: 'string' },

							}
						},
						accessToken: { type: 'string' },
						refreshToken: { type: 'string' }
					}
				}
			}
		}
	}
}