import { createError } from '@fastify/error';
import buildMenuTree from '../utils/buildMenuTree.js';

const AUTH_ERROR = createError('AUTH_ERROR', '%s', 401);
const INTERNAL_ERROR = createError('INTERNAL_ERROR', '%s', 500);

// 公共：移除菜单树中的字段
function removeFieldsFromTree(tree, removeFields) {
    if (Array.isArray(tree)) {
        tree.forEach(item => removeFieldsFromTree(item, removeFields));
    } else if (tree && typeof tree === 'object') {
        for (const key in tree) {
            if (removeFields.includes(key)) {
                delete tree[key];
            } else if (typeof tree[key] === 'object') {
                removeFieldsFromTree(tree[key], removeFields);
            }
        }
    }
}

// 公共：获取权限代码
async function getPermissionCodes(client, userId) {
    const query = `
        SELECT p.code
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp."permissionId"
        JOIN roles r ON rp."roleId" = r.id
        JOIN user_roles ur ON r.id = ur."roleId"
        WHERE ur."userId" = $1
    `;
    
    const result = await client.query(query, [userId]);
    return new Set(result.rows.map(row => row.code));
}

// 公共：获取所有菜单
async function fetchAllMenus(client) {
    const query = `
        SELECT id, name, path, icon, sort, hidden, "permissionId", "parentId", 
               component, "permissionCode", redirect
        FROM menus
        ORDER BY sort ASC
    `;
    
    const result = await client.query(query);
    return result.rows;
}

// 公共：根据权限过滤菜单
function filterMenusByPermissions(menus, permissionCodes) {
    return menus.filter(menu => 
        !menu.permissionCode || permissionCodes.has(menu.permissionCode)
    );
}

// 公共：处理菜单树
function processMenuTree(menus) {
    const menuTree = buildMenuTree(menus);
    
    // 递归遍历移除component、parentId、permissionId
    const removeFields = ['component', 'parentId', 'permissionId', 'redirect', 'permissionCode'];
    removeFieldsFromTree(menuTree, removeFields);
    
    // 根据sort排序
    menuTree.sort((a, b) => a.sort - b.sort);
    
    return menuTree;
}

async function getUserMenus(fastify, user) {
    const client = await fastify.pg.connect();
    try {
        await client.query('BEGIN');
        
        // 获取用户权限代码
        const permissionCodes = await getPermissionCodes(client, user.id);
        
        // 获取所有菜单
        const allMenus = await fetchAllMenus(client);
        
        // 根据权限过滤菜单
        const filteredMenus = filterMenusByPermissions(allMenus, permissionCodes);
        
        // 处理菜单树
        const menuTree = processMenuTree(filteredMenus);
        
        await client.query('COMMIT');
        return menuTree || [];
    } catch (error) {
        await client.query('ROLLBACK');
        fastify.log.error(error);
        if (error instanceof AUTH_ERROR) {
            throw error;
        }
        throw new INTERNAL_ERROR(error.message || '获取用户菜单失败');
    } finally {
        client.release();
    }
}

async function getAllMenus(fastify, user) {
    const client = await fastify.pg.connect();
    try {
        await client.query('BEGIN');
        
        // 获取机构管理员
        const adminQuery = `
            SELECT "userId"
            FROM user_institution
            WHERE "institutionId" = $1 AND "isAdmin" = true
            LIMIT 1
        `;
        const adminResult = await client.query(adminQuery, [user.institutionId]);
        
        if (adminResult.rows.length === 0) {
            throw new AUTH_ERROR('未找到机构管理员');
        }
        
        const adminUserId = adminResult.rows[0].userId;
        
        // 获取管理员权限代码
        const permissionCodes = await getPermissionCodes(client, adminUserId);
        
        // 获取所有菜单
        const allMenus = await fetchAllMenus(client);
        
        // 根据权限过滤菜单
        const filteredMenus = filterMenusByPermissions(allMenus, permissionCodes);
        
        // 处理菜单树
        const menuTree = processMenuTree(filteredMenus);
        
        await client.query('COMMIT');
        return menuTree;
    } catch (error) {
        await client.query('ROLLBACK');
        fastify.log.error(error);
        if (error instanceof AUTH_ERROR) {
            throw error;
        }
        throw new INTERNAL_ERROR(error.message || '获取菜单失败');
    } finally {
        client.release();
    }
}

async function getRoleMenus(fastify, roleId) {
    const client = await fastify.pg.connect();
    try {
        await client.query('BEGIN');
        
        // 获取角色权限代码
        const rolePermissionsQuery = `
            SELECT p.code
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp."permissionId"
            WHERE rp."roleId" = $1
        `;
        const rolePermissionsResult = await client.query(rolePermissionsQuery, [roleId]);
        const permissionCodes = new Set(rolePermissionsResult.rows.map(row => row.code));
        
        // 获取所有菜单
        const allMenus = await fetchAllMenus(client);
        
        // 根据权限过滤菜单
        const filteredMenus = filterMenusByPermissions(allMenus, permissionCodes);
        
        // 处理菜单树
        const menuTree = processMenuTree(filteredMenus);
        
        await client.query('COMMIT');
        return menuTree;
    } catch (error) {
        await client.query('ROLLBACK');
        fastify.log.error(error);
        if (error instanceof AUTH_ERROR) {
            throw error;
        }
        throw new INTERNAL_ERROR(error.message || '获取角色菜单失败');
    } finally {
        client.release();
    }
}

export default {
    getUserMenus,
    getAllMenus,
    getRoleMenus
}
