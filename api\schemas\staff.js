
// 获取员工列表
const getStaffListSchema = {
    tags: ['staff'],
    summary: '获取员工列表',
    querystring: {
        type: 'object',
        properties: {
            page: {
                type: 'number',
                default: 1
            },
            pageSize: {
                type: 'number',
                default: 10
            },
            search: {
                type: 'string'
            }
        }
    },
}

// 获取员工信息
const getStaffInfoSchema = {
    tags: ['staff'],
    summary: '获取员工信息',
    params: {
        type: 'object',
        required: ['staffId'],
        properties: {
            staffId: {
                type: 'string'
            }
        }
    },
}
// 删除员工
const deleteStaffSchema = {
    tags: ['staff'],
    summary: '删除员工',
    params: {
        type: 'object',
        required: ['staffId'],
        properties: {
            staffId: {
                type: 'string'
            }
        }
    },
}

// 更新员工
const updateStaffSchema = {
    tags: ['staff'],
    summary: '更新员工信息',
    params: {
        type: 'object',
        required: ['staffId'],
        properties: {
            staffId: {
                type: 'string'
            }
        }
    },
    body: {
        type: 'object',
        properties: {
            name: { type: 'string' },
            avatar: { type: 'string' },
            isShow: { type: 'boolean' },
            roles: { type: 'array', items: { type: 'string' } },
            account: {
                type: 'string',
                minLength: 3
            }
        }
    }
}
// 更新员工状态
const updateStaffStatusSchema = {
    tags: ['staff'],
    summary: '更新员工状态',
    params: {
        type: 'object',
        required: ['staffId'],
        properties: {
            staffId: {
                type: 'string'
            }
        }
    }
}

// 重置员工密码
const resetStaffPasswordSchema = {
    tags: ['staff'],
    summary: '重置员工密码',
    params: {
        type: 'object',
        properties: {
            staffId: {
                type: 'string'
            }
        }
    },
    response: {
        200: {
            type: 'object',
            properties: {
                code: {
                    type: 'number'
                },
                message: {
                    type: 'string'
                }
            }
        }
    }
}
// 上传头像
const uploadAvatarSchema = {
    tags: ['staff'],
    summary: '上传头像',
    consumes: ['multipart/form-data'],
}

// 创建员工
const createStaffSchema = {
    tags: ['staff'],
    summary: '创建员工',
    body: {
        type: 'object',
        required: ['account'],
        properties: {
            name: { type: 'string' },
            phone: {
                type: 'string',
                pattern: '^1[3-9]\\d{9}$',
                minLength: 11,
                maxLength: 11
            },
            avatar: { type: 'string' },
            description: { type: 'string' },
            account: {
                type: 'string',
                minLength: 6
            },
            password: {
                type: 'string',
                minLength: 6
            },
            roleId: {
                type: 'string',
                minLength: 1
            }
        }
    }
}


export default {
    createStaffSchema,
    getStaffListSchema,
    getStaffInfoSchema,
    deleteStaffSchema,
    updateStaffSchema,
    updateStaffStatusSchema,
    resetStaffPasswordSchema,
    uploadAvatarSchema
}