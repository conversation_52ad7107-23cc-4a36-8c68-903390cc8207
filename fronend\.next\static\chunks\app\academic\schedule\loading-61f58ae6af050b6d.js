(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1531],{35177:(s,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>c});var l=a(95155),r=a(68856);function c(){return(0,l.jsxs)("div",{className:"p-4 space-y-6",children:[(0,l.jsxs)("div",{className:"flex flex-col gap-5 mb-6",children:[(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-100 p-1 sm:p-2",children:(0,l.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)(r.E,{className:"h-9 w-20"}),(0,l.jsx)(r.E,{className:"h-9 w-40"}),(0,l.jsx)(r.E,{className:"h-9 w-20"})]}),(0,l.jsxs)("div",{className:"flex items-center flex-wrap gap-3",children:[(0,l.jsx)(r.E,{className:"h-9 w-[180px]"}),(0,l.jsx)(r.E,{className:"h-9 w-20"}),(0,l.jsx)(r.E,{className:"h-9 w-24"})]})]})}),(0,l.jsxs)("div",{className:"flex justify-end items-center gap-2",children:[(0,l.jsx)(r.E,{className:"h-10 w-28"}),(0,l.jsx)(r.E,{className:"h-10 w-24"})]})]}),(0,l.jsxs)("div",{className:"border rounded-md shadow-sm",children:[(0,l.jsxs)("div",{className:"grid grid-cols-8 border-b",children:[(0,l.jsx)(r.E,{className:"h-10 m-2"}),Array(7).fill(0).map((s,e)=>(0,l.jsx)(r.E,{className:"h-10 m-2"},e))]}),Array(8).fill(0).map((s,e)=>(0,l.jsxs)("div",{className:"grid grid-cols-8 border-b",children:[(0,l.jsx)(r.E,{className:"h-20 m-2"}),Array(7).fill(0).map((s,e)=>(0,l.jsx)(r.E,{className:"h-20 m-2"},e))]},e))]})]})}},49821:(s,e,a)=>{Promise.resolve().then(a.bind(a,35177))},59434:(s,e,a)=>{"use strict";a.d(e,{cn:()=>c});var l=a(52596),r=a(39688);function c(){for(var s=arguments.length,e=Array(s),a=0;a<s;a++)e[a]=arguments[a];return(0,r.QP)((0,l.$)(e))}},68856:(s,e,a)=>{"use strict";a.d(e,{E:()=>c});var l=a(95155),r=a(59434);function c(s){let{className:e,...a}=s;return(0,l.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",e),...a})}}},s=>{var e=e=>s(s.s=e);s.O(0,[4277,6315,7358],()=>e(49821)),_N_E=s.O()}]);