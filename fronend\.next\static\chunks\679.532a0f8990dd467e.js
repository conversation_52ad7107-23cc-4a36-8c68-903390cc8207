"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[679],{14636:(e,t,a)=>{a.d(t,{AM:()=>o,Wv:()=>i,hl:()=>d});var l=a(95155),r=a(12115),s=a(20547),n=a(59434);let o=s.bL,i=s.l9,d=r.forwardRef((e,t)=>{let{className:a,align:r="center",sideOffset:o=4,...i}=e;return(0,l.jsx)(s.ZL,{children:(0,l.jsx)(s.UC,{ref:t,align:r,sideOffset:o,className:(0,n.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...i})})});d.displayName=s.UC.displayName},40679:(e,t,a)=>{a.r(t),a.d(t,{default:()=>N});var l=a(95155),r=a(12115),s=a(46102),n=a(30285),o=a(49103),i=a(55028),d=a(9110),c=a(26126),m=a(57141),u=a(61809),g=a(50228);let x=[{accessorKey:"amount",header:"金额",cell:e=>{let{row:t}=e,a=t.original.amount,r=t.original.billType;return(0,l.jsxs)("div",{className:"font-medium ".concat("income"===r?"text-emerald-600":"text-rose-600"),children:["income"===r?"+":"-",a]})}},{accessorKey:"billType",header:"账单类型",cell:e=>{let{row:t}=e,a=t.original.billType;return(0,l.jsx)(c.E,{variant:"income"===a?"outline":"secondary",className:"".concat("income"===a?"border-emerald-200 bg-emerald-50 text-emerald-700":"border-rose-200 bg-rose-50 text-rose-700"," rounded-md font-normal"),children:"income"===a?"收入":"支出"})}},{accessorKey:"paymentMethod",header:"支付方式",cell:e=>{let{row:t}=e,a=t.original.paymentMethod,{color:r,label:s}=m.C9[a]||m.C9.other;return(0,l.jsx)(c.E,{className:"rounded-md font-normal ".concat(r),children:s})}},{accessorKey:"path",header:"途径",cell:e=>{var t;let{row:a}=e,r=a.original.path,{label:s,color:n}=m.N4[r]||m.N4.other,o=(null===(t=m.N4[r])||void 0===t?void 0:t.label)||"未知途径";return(0,l.jsx)(c.E,{variant:"outline",className:"rounded-md font-normal ".concat(n),children:s||o})}},{accessorKey:"status",header:"状态",cell:e=>{let{row:t}=e,a=t.original.status,{label:r,color:s}=m.u7[a]||m.u7.other;return(0,l.jsx)(c.E,{variant:"outline",className:"rounded-md font-normal ".concat(s),children:r})}},{accessorKey:"paymentTime",header:"支付时间",cell:e=>{let{row:t}=e,a=t.original.paymentTime;return a?(0,g.P)((0,u.Y)(a,"yyyy-MM-dd HH:mm:ss")):(0,g.P)("-")}},{accessorKey:"operator",header:"操作人",cell:e=>{let{row:t}=e;return(0,g.P)(t.original.operator.name)}},{accessorKey:"operatorTime",header:"操作时间",cell:e=>{let{row:t}=e,a=t.original.operatorTime;return a?(0,g.P)((0,u.Y)(a,"yyyy-MM-dd HH:mm:ss")):(0,g.P)("-")}},{accessorKey:"remarks",header:"备注",cell:e=>{let{row:t}=e;return(0,g.s)(t.original.remarks)}}];var b=a(48432),h=a(82007),p=a(58012),f=a(59409),v=a(5959),y=a(91347);let j=(0,i.default)(()=>a.e(5270).then(a.bind(a,25270)),{loadableGenerated:{webpack:()=>[25270]},ssr:!1}),w={timeRange:"last7days",dateRange:void 0,type:"all",source:"all"},N=function(){let[e,t]=(0,r.useState)(!1),[a,i]=(0,r.useState)(1),[c,u]=(0,r.useState)(10),[g,N]=(0,r.useState)(w),C=(0,r.useCallback)((e,t)=>{N(a=>({...a,[e]:t}))},[g]),D=(0,r.useMemo)(()=>{let{startTime:e,endTime:t}=(0,h.A)(g.timeRange,g.dateRange);return{page:a,pageSize:c,billType:"all"!==g.type?g.type:void 0,source:"all"!==g.source?g.source:void 0,startTime:e,endTime:t}},[a,c,g]),{data:k,isLoading:M}=(0,v.gk)(D),T=(0,r.useCallback)(()=>{i(1)},[]);return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,l.jsxs)("div",{className:"flex-1 flex items-center gap-4",children:[(0,l.jsx)(p.A,{timeRange:g.timeRange,onTimeRangeChange:e=>C("timeRange",e),dateRange:g.dateRange,onDateRangeChange:e=>C("dateRange",e)}),(0,l.jsxs)(f.l6,{value:g.source,onValueChange:e=>C("source",e),children:[(0,l.jsx)(f.bq,{className:"h-10 w-[140px]",children:(0,l.jsx)(f.yv,{placeholder:"请选择途径"})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"all",children:"全部"}),Object.entries(m.N4).map(e=>{let[t,a]=e;return(0,l.jsx)(f.eb,{value:t,children:a.label},t)})]})]}),(0,l.jsxs)(f.l6,{value:g.type,onValueChange:e=>C("type",e),children:[(0,l.jsx)(f.bq,{className:"h-9 w-[140px]",children:(0,l.jsx)(f.yv,{placeholder:"请选择类型"})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"all",children:"全部"}),(0,l.jsx)(f.eb,{value:"income",children:"收入"}),(0,l.jsx)(f.eb,{value:"expense",children:"支出"})]})]})]}),(0,l.jsx)(n.$,{className:"px-8",onClick:T,children:"筛选"})]}),(0,l.jsx)(y.LQ,{permission:"finance:bill:create",children:(0,l.jsx)("div",{className:"flex justify-end mb-4",children:(0,l.jsx)(s.Bc,{children:(0,l.jsxs)(s.m_,{children:[(0,l.jsx)(s.k$,{asChild:!0,children:(0,l.jsxs)(n.$,{onClick:()=>t(!0),size:"sm",variant:"secondary",className:"flex items-center gap-1",children:[(0,l.jsx)(o.A,{className:"h-4 w-4"})," 新增账单"]})}),(0,l.jsx)(s.ZI,{children:"添加新的账单记录"})]})})})}),(0,l.jsx)(d.b,{columns:x,pagination:!1,data:(null==k?void 0:k.list)||[],loading:M}),(0,l.jsx)(b.default,{currentPage:(null==k?void 0:k.page)||1,pageSize:(null==k?void 0:k.pageSize)||10,totalItems:(null==k?void 0:k.total)||0,onPageChange:i,onPageSizeChange:e=>{u(e),i(1)}}),e&&(0,l.jsx)(j,{open:e,onOpenChange:t})]})}},48432:(e,t,a)=>{a.r(t),a.d(t,{default:()=>f});var l=a(95155),r=a(12115),s=a(42355),n=a(13052),o=a(5623),i=a(59434),d=a(30285);let c=e=>{let{className:t,...a}=e;return(0,l.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,i.cn)("mx-auto flex w-full justify-center",t),...a})};c.displayName="Pagination";let m=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,l.jsx)("ul",{ref:t,className:(0,i.cn)("flex flex-row items-center gap-1",a),...r})});m.displayName="PaginationContent";let u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,l.jsx)("li",{ref:t,className:(0,i.cn)("",a),...r})});u.displayName="PaginationItem";let g=e=>{let{className:t,isActive:a,size:r="icon",...s}=e;return(0,l.jsx)("a",{"aria-current":a?"page":void 0,className:(0,i.cn)((0,d.r)({variant:a?"outline":"ghost",size:r}),t),...s})};g.displayName="PaginationLink";let x=e=>{let{className:t,...a}=e;return(0,l.jsxs)(g,{"aria-label":"Go to previous page",size:"default",className:(0,i.cn)("gap-1 pl-2.5",t),...a,children:[(0,l.jsx)(s.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{children:"上一页"})]})};x.displayName="PaginationPrevious";let b=e=>{let{className:t,...a}=e;return(0,l.jsxs)(g,{"aria-label":"Go to next page",size:"default",className:(0,i.cn)("gap-1 pr-2.5",t),...a,children:[(0,l.jsx)("span",{children:"下一页"}),(0,l.jsx)(n.A,{className:"h-4 w-4"})]})};b.displayName="PaginationNext";let h=e=>{let{className:t,...a}=e;return(0,l.jsxs)("span",{"aria-hidden":!0,className:(0,i.cn)("flex h-9 w-9 items-center justify-center",t),...a,children:[(0,l.jsx)(o.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"sr-only",children:"更多页"})]})};h.displayName="PaginationEllipsis";var p=a(59409);function f(e){let{currentPage:t,pageSize:a,totalItems:r,onPageChange:o,onPageSizeChange:i}=e,d=Math.ceil(r/a),f=(()=>{let e=[];if(d<=5){for(let t=1;t<=d;t++)e.push(t);return e}e.push(1);let a=Math.max(2,t-1),l=Math.min(t+1,d-1);2===a&&(l=Math.min(a+2,d-1)),l===d-1&&(a=Math.max(l-2,2)),a>2&&e.push("ellipsis-start");for(let t=a;t<=l;t++)e.push(t);return l<d-1&&e.push("ellipsis-end"),d>1&&e.push(d),e})(),v=0===r?0:(t-1)*a+1,y=Math.min(t*a,r);return(0,l.jsxs)("div",{className:"flex flex-col gap-4 px-2 py-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,l.jsx)("span",{className:"text-muted-foreground/80",children:"显示"}),(0,l.jsxs)(p.l6,{value:a.toString(),onValueChange:e=>{i(Number(e))},children:[(0,l.jsx)(p.bq,{className:"w-[4.5rem] h-7 text-xs border-border/60 hover:bg-accent/30 transition-colors",children:(0,l.jsx)(p.yv,{})}),(0,l.jsx)(p.gC,{children:[10,20,30,50].map(e=>(0,l.jsx)(p.eb,{value:e.toString(),className:"text-xs",children:e},e))})]}),(0,l.jsx)("span",{className:"text-muted-foreground/80",children:"条"}),(0,l.jsx)("span",{className:"text-muted-foreground/40 mx-1.5",children:"|"}),r>0?(0,l.jsxs)("span",{className:"text-muted-foreground/80",children:[v,"-",y," / ",r," 条记录"]}):(0,l.jsx)("span",{className:"text-muted-foreground/80",children:"0 条记录"})]}),(0,l.jsx)(c,{children:(0,l.jsxs)(m,{className:"gap-1",children:[(0,l.jsx)(u,{children:(0,l.jsx)(x,{onClick:()=>o(Math.max(1,t-1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(1===t?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,l.jsx)(s.A,{className:"h-4 w-4 mr-1"})})}),f.map((e,a)=>"ellipsis-start"===e||"ellipsis-end"===e?(0,l.jsx)(u,{children:(0,l.jsx)(h,{className:"h-8 w-8 flex items-center justify-center text-xs"})},"ellipsis-".concat(a)):(0,l.jsx)(u,{children:(0,l.jsx)(g,{onClick:()=>o(e),isActive:t===e,className:"h-8 w-8 text-xs font-medium rounded-md transition-colors data-[active]:bg-primary data-[active]:text-primary-foreground hover:bg-accent/50 hover:text-accent-foreground/90","aria-label":"前往第 ".concat(e," 页"),children:e})},e)),(0,l.jsx)(u,{children:(0,l.jsx)(b,{onClick:()=>o(Math.min(d,t+1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(t===d||0===d?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,l.jsx)(n.A,{className:"h-4 w-4 ml-1"})})})]})})]})}},50228:(e,t,a)=>{a.d(t,{P:()=>s,s:()=>n});var l=a(95155),r=a(73069);let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e?(0,l.jsx)("div",{className:"text-sm ".concat(t?"text-muted-foreground":""),children:e}):(0,l.jsx)("div",{})},n=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return e?(0,l.jsx)(r.c,{maxDisplayLength:t,children:e}):(0,l.jsx)("div",{className:"text-sm text-muted-foreground"})}},57141:(e,t,a)=>{a.d(t,{C9:()=>r,DT:()=>o,I2:()=>n,IC:()=>u,N4:()=>c,fb:()=>g,lc:()=>l,oD:()=>i,u7:()=>m,uq:()=>s,x9:()=>d});let l={"limited-sessions":{label:"限次",color:"capitalize font-normal bg-blue-50 text-blue-700 hover:bg-blue-100 px-2.5 py-0.5 rounded-md"},"limited-time-and-count":{label:"时次限",color:"capitalize font-normal bg-violet-50 text-violet-700 hover:bg-violet-100 px-2.5 py-0.5 rounded-md"},default:{label:"未知套餐",color:"capitalize font-normal bg-slate-100 text-slate-700 hover:bg-slate-200 px-2.5 py-0.5 rounded-md"}},r={cash:{label:"现金",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},alipay:{label:"支付宝",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},wechat:{label:"微信",color:"bg-green-100 text-green-800 hover:bg-green-200"},card:{label:"银行卡",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},other:{label:"其他",color:"bg-slate-100 text-slate-800 hover:bg-slate-200"}},s=[{id:"wechat",label:"微信"},{id:"alipay",label:"支付宝"},{id:"card",label:"银行转账"},{id:"cash",label:"现金"},{id:"other",label:"其他"}],n={done:{label:"完成",color:"bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400"},arrears:{label:"欠费",color:"bg-amber-100 text-amber-800 hover:bg-amber-200 dark:bg-amber-900/30 dark:text-amber-400"},refunded:{label:"退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400"},default:{label:"未知",color:"bg-slate-100 text-slate-800 hover:bg-slate-200 dark:bg-slate-800/50 dark:text-slate-400"}},o={active:{badgeClass:"bg-emerald-50 text-emerald-700 hover:bg-emerald-100 border-emerald-200",dotClass:"bg-emerald-500",text:"正常"},expired:{badgeClass:"bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200",dotClass:"bg-amber-500",text:"已到期"},refunded:{badgeClass:"bg-red-50 text-red-700 hover:bg-red-100 border-red-200",dotClass:"bg-red-500",text:"已退款"},frozen:{badgeClass:"bg-slate-50 text-slate-700 hover:bg-slate-100 border-slate-200",dotClass:"bg-slate-500",text:"已冻结"},default:{badgeClass:"bg-slate-100 text-slate-700 hover:bg-slate-200 border-slate-200",dotClass:"bg-slate-400",text:"未知状态"}},i={all:{className:"bg-slate-100 text-slate-800 hover:bg-slate-200",text:"全部状态"},attendance:{className:"bg-green-100 text-green-800 hover:bg-green-200",text:"已考勤"},leave:{className:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200",text:"请假"},unattended:{className:"bg-orange-100 text-orange-800 hover:bg-orange-200",text:"未考勤"},default:{className:"bg-red-100 text-red-800 hover:bg-red-200",text:"缺勤"}},d={A:{label:"非常感兴趣",color:"bg-green-100 text-green-800 hover:bg-green-200"},B:{label:"比较感兴趣",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},C:{label:"一般意向",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},D:{label:"了解意向",color:"bg-orange-100 text-orange-800 hover:bg-orange-200"},E:{label:"不感兴趣",color:"bg-red-100 text-red-800 hover:bg-red-200"},default:{label:"未知",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},c={productSales:{label:"产品销售",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},courseSales:{label:"课程销售",color:"bg-green-100 text-green-800 hover:bg-green-200"},OverduePaymentCollection:{label:"逾期款项催收",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},lifestyleAndOffice:{label:"生活和办公",color:"bg-red-100 text-red-800 hover:bg-red-200"},socialAndCatering:{label:"社交和餐饮",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},refunded:{label:"退款",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"其他",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},m={completed:{label:"已完成",color:"bg-green-100 text-green-800 hover:bg-green-200"},pending:{label:"待审核",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},refunded:{label:"已退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},failed:{label:"审核未通过",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"未知状态",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},u={formal:{label:"正式学员",color:"text-blue-500"},graduated:{label:"历史学员",color:"text-green-500"},public:{label:"公海池",color:"text-red-500"},intent:{label:"意向学员",color:"text-yellow-500"}},g={secret:{label:"保密",color:"text-gray-500"},male:{label:"男",color:"text-blue-500"},female:{label:"女",color:"text-pink-500"}}},58012:(e,t,a)=>{a.d(t,{A:()=>u});var l=a(95155);a(12115);var r=a(59409),s=a(14636),n=a(30285),o=a(73168),i=a(69074),d=a(85511),c=a(24122);let m=[{label:"今天",value:"today"},{label:"本周",value:"thisWeek"},{label:"最近7天",value:"last7days"},{label:"本月",value:"thisMonth"},{label:"上一个月",value:"lastMonth"},{label:"自定义",value:"custom"}],u=e=>{let{timeRange:t,onTimeRangeChange:a,dateRange:u,onDateRangeChange:g,className:x,items:b=m}=e;return(0,l.jsxs)("div",{className:"flex items-center gap-2 ".concat(x||""),children:[(0,l.jsxs)(r.l6,{value:t,onValueChange:a,children:[(0,l.jsx)(r.bq,{className:"h-9 w-[140px]",children:(0,l.jsx)(r.yv,{placeholder:"时间筛选"})}),(0,l.jsx)(r.gC,{children:b.map(e=>(0,l.jsx)(r.eb,{value:e.value,children:e.label},e.value))})]}),"custom"===t&&g&&(0,l.jsxs)(s.AM,{children:[(0,l.jsx)(s.Wv,{asChild:!0,children:(0,l.jsxs)(n.$,{variant:"outline",className:"h-9 px-3 text-sm font-normal justify-start text-left",children:[(0,l.jsx)(i.A,{className:"mr-2 h-3.5 w-3.5"}),(null==u?void 0:u.from)?u.to?(0,l.jsxs)("span",{className:"truncate max-w-[180px]",children:[(0,o.GP)(u.from,"yyyy-MM-dd")," 至 ",(0,o.GP)(u.to,"yyyy-MM-dd")]}):(0,o.GP)(u.from,"yyyy-MM-dd"):(0,l.jsx)("span",{children:"自定义时间段"})]})}),(0,l.jsx)(s.hl,{className:"w-auto p-0",align:"start",children:(0,l.jsx)(d.V,{mode:"range",selected:u,onSelect:g,initialFocus:!0,numberOfMonths:2,locale:c.g})})]})]})}},61809:(e,t,a)=>{a.d(t,{Y:()=>n});var l=a(44861),r=a(73168),s=a(24122);let n=function(e){let t,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"yyyy-MM-dd",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(null==e)return n;try{if("string"==typeof e)t=new Date(e);else if("number"==typeof e)t=new Date(e);else{if(!(e instanceof Date))return n;t=e}if(!(0,l.f)(t))return n;return(0,r.GP)(t,a,{locale:s.g})}catch(e){return console.error("Date formatting error:",e),n}}},73069:(e,t,a)=>{a.d(t,{c:()=>u});var l=a(95155),r=a(12115),s=a(47863),n=a(66474),o=a(5196),i=a(24357),d=a(59434),c=a(14636),m=a(30285);function u(e){let{children:t,maxDisplayLength:a=15,className:u,popoverWidth:g="auto",showBorder:x=!1}=e,[b,h]=r.useState(!1),[p,f]=r.useState(!1),v=r.useMemo(()=>{if("string"==typeof t||"number"==typeof t)return t.toString();try{var e;let a=document.createElement("div");return a.innerHTML=(null==t?void 0:null===(e=t.props)||void 0===e?void 0:e.children)||"",a.textContent||""}catch(e){return console.warn("Could not extract text from children:",e),""}},[t]),y=r.useMemo(()=>{if("string"==typeof t||"number"==typeof t){let e=t.toString();return e.length>a?e.slice(0,a):e}return t},[t,a]),j=async()=>{try{await navigator.clipboard.writeText(v),f(!0),setTimeout(()=>f(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,l.jsxs)(c.AM,{open:b,onOpenChange:h,children:[(0,l.jsx)(c.Wv,{asChild:!0,children:(0,l.jsxs)(m.$,{variant:x?"outline":"ghost",role:"combobox","aria-expanded":b,className:(0,d.cn)("w-fit justify-between font-normal px-2 py-1 h-auto",!x&&"border-0 shadow-none",u),children:[(0,l.jsx)("span",{className:"mr-2 truncate",children:y}),b?(0,l.jsx)(s.A,{className:"h-4 w-4 shrink-0 opacity-50"}):(0,l.jsx)(n.A,{className:"h-4 w-4 shrink-0 opacity-50"})]})}),(0,l.jsx)(c.hl,{className:"p-0",align:"start",style:{width:g},children:(0,l.jsxs)("div",{className:"flex items-center gap-2 p-2",children:[(0,l.jsx)("span",{className:"text-sm break-all",children:v}),(0,l.jsxs)(m.$,{variant:"ghost",size:"icon",className:"h-8 w-8 shrink-0",onClick:j,children:[p?(0,l.jsx)(o.A,{className:"h-4 w-4"}):(0,l.jsx)(i.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"sr-only",children:p?"Copied":"Copy text"})]})]})})]})}},82007:(e,t,a)=>{a.d(t,{A:()=>l});function l(e,t){let a,l;let r=new Date;if(e&&"custom"!==e)switch(e){case"today":a=new Date(r.setHours(0,0,0,0)).getTime(),l=new Date(r.setHours(23,59,59,999)).getTime();break;case"yesterday":let s=new Date(r);s.setDate(s.getDate()-1),a=new Date(s.setHours(0,0,0,0)).getTime(),l=new Date(s.setHours(23,59,59,999)).getTime();break;case"thisWeek":let n=r.getDate()-r.getDay()+(0===r.getDay()?-6:1),o=new Date(r.setDate(n));a=new Date(o.setHours(0,0,0,0)).getTime();let i=new Date(o);i.setDate(i.getDate()+6),l=new Date(i.setHours(23,59,59,999)).getTime();break;case"thisMonth":a=new Date(r.getFullYear(),r.getMonth(),1).getTime(),l=new Date(r.getFullYear(),r.getMonth()+1,0,23,59,59,999).getTime();break;case"last7days":let d=new Date(r);d.setDate(d.getDate()-7),a=new Date(d.setHours(0,0,0,0)).getTime(),l=new Date(r.setHours(23,59,59,999)).getTime();break;case"lastMonth":a=new Date(new Date(r.getFullYear(),r.getMonth()-1,1).setHours(0,0,0,0)).getTime(),l=new Date(r.getFullYear(),r.getMonth(),0,23,59,59,999).getTime();break;case"thisYear":a=new Date(r.getFullYear(),0,1).getTime(),l=new Date(r.getFullYear(),11,31,23,59,59,999).getTime()}else(null==t?void 0:t.from)&&(a=new Date(t.from.setHours(0,0,0,0)).getTime(),l=t.to?new Date(t.to.setHours(23,59,59,999)).getTime():new Date(t.from.setHours(23,59,59,999)).getTime());return{startTime:a,endTime:l}}},85511:(e,t,a)=>{a.d(t,{V:()=>d});var l=a(95155);a(12115);var r=a(42355),s=a(13052),n=a(20081),o=a(59434),i=a(30285);function d(e){let{className:t,classNames:a,showOutsideDays:d=!0,...c}=e;return(0,l.jsx)(n.hv,{showOutsideDays:d,className:(0,o.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,o.cn)((0,i.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,o.cn)((0,i.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...a},components:{IconLeft:e=>{let{className:t,...a}=e;return(0,l.jsx)(r.A,{className:(0,o.cn)("h-4 w-4",t),...a})},IconRight:e=>{let{className:t,...a}=e;return(0,l.jsx)(s.A,{className:(0,o.cn)("h-4 w-4",t),...a})}},...c})}d.displayName="Calendar"}}]);