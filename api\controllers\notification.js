import { NotificationService } from '../services/notification.js';

/**
 * 通知控制器
 */
export class NotificationController {
  /**
   * 创建通知
   * @param {Object} request - Fastify请求对象
   * @param {Object} reply - Fastify响应对象
   * @returns {Promise<Object>} - 响应结果
   */
  static async createNotification(request, reply) {
    try {
      const { isAll, userIds, title, content } = request.body;
      const user = request.user;
      const client = request.server.prisma;
      
      const result = await NotificationService.createNotification({
        client,
        user,
        isAll,
        userIds,
        title,
        content,
        fastify: request.server
      });
      
      return reply.success(result);
    } catch (error) {
      console.error(error);
      throw new Error('通知创建失败');
    }
  }
  
  /**
   * 获取通知列表
   * @param {Object} request - Fastify请求对象
   * @param {Object} reply - Fastify响应对象
   * @returns {Promise<Object>} - 响应结果
   */
  static async getNotifications(request, reply) {
    try {
      const { page, pageSize, status } = request.query;
      const user = request.user;
      
      const result = await NotificationService.getNotifications({
        fastify: request.server,
        user,
        page,
        pageSize,
        status
      });
      
      return reply.success({
        message: '通知列表获取成功',
        data: result
      });
    } catch (error) {
      console.error('获取通知列表失败:', error);
      throw new Error('获取通知列表失败');
    }
  }
  
  /**
   * 获取通知详情
   * @param {Object} request - Fastify请求对象
   * @param {Object} reply - Fastify响应对象
   * @returns {Promise<Object>} - 响应结果
   */
  static async getNotificationDetail(request, reply) {
    try {
      const { id } = request.params;
      const user = request.user;
      
      const notification = await NotificationService.getNotificationDetail({
        fastify: request.server,
        user,
        id
      });
      
      return reply.success({
        message: '通知获取成功',
        data: notification
      });
    } catch (error) {
      console.error(error);
      throw new Error(error.message || '通知获取失败');
    }
  }
  
  /**
   * 获取未读通知数量
   * @param {Object} request - Fastify请求对象
   * @param {Object} reply - Fastify响应对象
   * @returns {Promise<Object>} - 响应结果
   */
  static async getUnreadCount(request, reply) {
    try {
      const user = request.user;
      const client = request.server.prisma;
      
      const count = await NotificationService.getUnreadCount({
        client,
        userId: user.id
      });
      
      return reply.success({
        message: '未读通知数量获取成功',
        data: count
      });
    } catch (error) {
      console.error(error);
      throw new Error('获取未读通知数量失败');
    }
  }
  
  /**
   * 批量标记通知为已读
   * @param {Object} request - Fastify请求对象
   * @param {Object} reply - Fastify响应对象
   * @returns {Promise<Object>} - 响应结果
   */
  static async markAsRead(request, reply) {
    try {
      const { ids } = request.body;
      const user = request.user;
      const client = request.server.prisma;
      
      const result = await NotificationService.markAsRead({
        client,
        userId: user.id,
        ids
      });
      
      return reply.success(result);
    } catch (error) {
      console.error(error);
      throw new Error('通知已读处理失败');
    }
  }
  
  /**
   * 标记所有通知为已读
   * @param {Object} request - Fastify请求对象
   * @param {Object} reply - Fastify响应对象
   * @returns {Promise<Object>} - 响应结果
   */
  static async markAllAsRead(request, reply) {
    try {
      const user = request.user;
      const client = request.server.prisma;
      
      const result = await NotificationService.markAllAsRead({
        client,
        userId: user.id
      });
      
      return reply.success(result);
    } catch (error) {
      console.error(error);
      throw new Error('通知已读处理失败');
    }
  }
  
  /**
   * 批量删除通知
   * @param {Object} request - Fastify请求对象
   * @param {Object} reply - Fastify响应对象
   * @returns {Promise<Object>} - 响应结果
   */
  static async deleteNotifications(request, reply) {
    try {
      const { ids } = request.body;
      const user = request.user;
      const client = request.server.prisma;
      
      const result = await NotificationService.deleteNotifications({
        client,
        userId: user.id,
        ids
      });
      
      return reply.success(result);
    } catch (error) {
      console.error(error);
      throw new Error('通知删除失败');
    }
  }
} 