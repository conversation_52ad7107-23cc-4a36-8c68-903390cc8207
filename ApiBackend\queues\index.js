/**
 * Queues index file
 * Exports all BullMQ queues for easy importing
 */

import imageQueue, { addImageGenerationJob, addImageVariationJob } from './imageQueue.js';
import textQueue, { addTextGenerationJob } from './textQueue.js';
import speechQueue, { addTextToSpeechJob, addSpeechToTextJob } from './speechQueue.js';
import visionQueue, { addImageAnalysisJob, addImageCaptioningJob } from './visionQueue.js';

// Export all queues
export {
  imageQueue,
  textQueue,
  speechQueue,
  visionQueue
};

// Export all job adding functions
export {
  addImageGenerationJob,
  addImageVariationJob,
  addTextGenerationJob,
  addTextToSpeechJob,
  addSpeechToTextJob,
  addImageAnalysisJob,
  addImageCaptioningJob
};

/**
 * Initialize all queues
 * This function can be called to ensure all queues are initialized
 */
export const initializeQueues = () => {
  console.log('Initializing BullMQ queues...');
  
  // Log the status of each queue
  console.log('Image generation queue initialized');
  console.log('Text generation queue initialized');
  console.log('Speech processing queue initialized');
  console.log('Vision processing queue initialized');
  
  return {
    imageQueue,
    textQueue,
    speechQueue,
    visionQueue
  };
};

// Export default as the initialize function for easy importing
export default initializeQueues;
