(()=>{var e={};e.id=6976,e.ids=[6976],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4851:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\data\\\\sales\\\\components\\\\SalesDataView.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\data\\sales\\components\\SalesDataView.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19587:(e,s)=>{"use strict";function t(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"encodeURIPath",{enumerable:!0,get:function(){return t}})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28169:(e,s,t)=>{"use strict";t.d(s,{default:()=>u});var r=t(60687);t(43210);var a=t(16709),n=t(30036),l=t(53541),d=t(80250);let i=(0,n.default)(async()=>{},{loadableGenerated:{modules:["app\\data\\sales\\components\\SalesDataView.tsx -> ./sales-overview"]},ssr:!1}),o=(0,n.default)(async()=>{},{loadableGenerated:{modules:["app\\data\\sales\\components\\SalesDataView.tsx -> ./sales-statistics"]},ssr:!1}),c=(0,n.default)(async()=>{},{loadableGenerated:{modules:["app\\data\\sales\\components\\SalesDataView.tsx -> ./student-overview"]},ssr:!1});function u(){let{hasPermission:e}=(0,d.J)()||{hasPermission:()=>!1},s=[{id:"sales-overview",key:"sales-overview",label:"销售概览",content:(0,r.jsx)(l.LQ,{permission:"data:sales:overview:read",children:(0,r.jsx)(i,{})}),hidden:!e("data:sales:overview:read")},{id:"sales-statistics",key:"sales-statistics",label:"销售统计",content:(0,r.jsx)(l.LQ,{permission:"data:sales:staff:read",children:(0,r.jsx)(o,{})}),hidden:!e("data:sales:staff:read")},{id:"sales-ranking",key:"sales-ranking",label:"学员概览",content:(0,r.jsx)(l.LQ,{permission:"data:sales:student:read",children:(0,r.jsx)(c,{})}),hidden:!e("data:sales:student:read")}].filter(e=>!e.hidden);return 0===s.length?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("p",{className:"text-gray-500",children:"您没有查看销售数据的权限"})}):(0,r.jsx)("div",{className:"space-y-4 p-4",children:(0,r.jsx)(a.Q,{tabs:s,variant:"underline",defaultTab:s[0]?.key||"sales-overview"})})}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30036:(e,s,t)=>{"use strict";t.d(s,{default:()=>a.a});var r=t(49587),a=t.n(r)},33873:e=>{"use strict";e.exports=require("path")},36059:(e,s,t)=>{Promise.resolve().then(t.bind(t,28169))},49587:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"default",{enumerable:!0,get:function(){return a}});let r=t(14985)._(t(64963));function a(e,s){var t;let a={};"function"==typeof e&&(a.loader=e);let n={...a,...s};return(0,r.default)({...n,modules:null==(t=n.loadableGenerated)?void 0:t.modules})}("function"==typeof s.default||"object"==typeof s.default&&null!==s.default)&&void 0===s.default.__esModule&&(Object.defineProperty(s.default,"__esModule",{value:!0}),Object.assign(s.default,s),e.exports=s.default)},51553:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>o});var r=t(65239),a=t(48088),n=t(88170),l=t.n(n),d=t(30893),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);t.d(s,i);let o={children:["",{children:["data",{children:["sales",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,91088)),"F:\\trae\\cardmees\\fronend\\src\\app\\data\\sales\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,52381)),"F:\\trae\\cardmees\\fronend\\src\\app\\data\\sales\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,69160)),"F:\\trae\\cardmees\\fronend\\src\\app\\data\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["F:\\trae\\cardmees\\fronend\\src\\app\\data\\sales\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/data/sales/page",pathname:"/data/sales",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},52381:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\data\\\\sales\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\data\\sales\\loading.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56780:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"BailoutToCSR",{enumerable:!0,get:function(){return a}});let r=t(81208);function a(e){let{reason:s,children:t}=e;throw Object.defineProperty(new r.BailoutToCSRError(s),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64777:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"PreloadChunks",{enumerable:!0,get:function(){return d}});let r=t(60687),a=t(51215),n=t(29294),l=t(19587);function d(e){let{moduleIds:s}=e,t=n.workAsyncStorage.getStore();if(void 0===t)return null;let d=[];if(t.reactLoadableManifest&&s){let e=t.reactLoadableManifest;for(let t of s){if(!e[t])continue;let s=e[t].files;d.push(...s)}}return 0===d.length?null:(0,r.jsx)(r.Fragment,{children:d.map(e=>{let s=t.assetPrefix+"/_next/"+(0,l.encodeURIPath)(e);return e.endsWith(".css")?(0,r.jsx)("link",{precedence:"dynamic",href:s,rel:"stylesheet",as:"style"},e):((0,a.preload)(s,{as:"script",fetchPriority:"low"}),null)})})}},64963:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"default",{enumerable:!0,get:function(){return o}});let r=t(60687),a=t(43210),n=t(56780),l=t(64777);function d(e){return{default:e&&"default"in e?e.default:e}}let i={loader:()=>Promise.resolve(d(()=>null)),loading:null,ssr:!0},o=function(e){let s={...i,...e},t=(0,a.lazy)(()=>s.loader().then(d)),o=s.loading;function c(e){let d=o?(0,r.jsx)(o,{isLoading:!0,pastDelay:!0,error:null}):null,i=!s.ssr||!!s.loading,c=i?a.Suspense:a.Fragment,u=s.ssr?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.PreloadChunks,{moduleIds:s.modules}),(0,r.jsx)(t,{...e})]}):(0,r.jsx)(n.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(t,{...e})});return(0,r.jsx)(c,{...i?{fallback:d}:{},children:u})}return c.displayName="LoadableComponent",c}},69160:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(37413),a=t(24597),n=t(36733);function l({children:e}){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.default,{}),(0,r.jsxs)("div",{className:"flex h-[calc(100vh)]",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("main",{className:"flex-1 p-8 pt-16 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400",children:(0,r.jsx)("div",{className:"max-w-8xl mx-auto bg-white rounded-lg shadow-sm p-4",children:e})})]})]})}},70403:(e,s,t)=>{Promise.resolve().then(t.bind(t,4851))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83095:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(60687),a=t(85726);function n(){return(0,r.jsx)("main",{className:"p-4",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex space-x-4 border-b pb-2",children:[,,,].fill(0).map((e,s)=>(0,r.jsx)(a.E,{className:"h-10 w-28"},s))}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[,,,].fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"border p-4 rounded-md",children:[(0,r.jsx)(a.E,{className:"h-6 w-24 mb-2"}),(0,r.jsx)(a.E,{className:"h-10 w-full"}),(0,r.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,r.jsx)(a.E,{className:"h-4 w-3/4"}),(0,r.jsx)(a.E,{className:"h-4 w-1/2"})]})]},s))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"border p-4 rounded-md",children:[(0,r.jsx)(a.E,{className:"h-6 w-32 mb-4"}),(0,r.jsx)(a.E,{className:"h-64 w-full"})]}),(0,r.jsxs)("div",{className:"border p-4 rounded-md",children:[(0,r.jsx)(a.E,{className:"h-6 w-32 mb-4"}),(0,r.jsx)(a.E,{className:"h-64 w-full"})]})]}),(0,r.jsxs)("div",{className:"border rounded-md",children:[(0,r.jsx)("div",{className:"flex border-b p-3 bg-muted/30",children:[,,,,,].fill(0).map((e,s)=>(0,r.jsx)(a.E,{className:"h-6 flex-1 mx-2"},s))}),Array(6).fill(0).map((e,s)=>(0,r.jsx)("div",{className:"flex border-b p-3",children:[,,,,,].fill(0).map((e,s)=>(0,r.jsx)(a.E,{className:"h-6 flex-1 mx-2"},s))},s))]})]})})}},83997:e=>{"use strict";e.exports=require("tty")},85726:(e,s,t)=>{"use strict";t.d(s,{E:()=>n});var r=t(60687),a=t(4780);function n({className:e,...s}){return(0,r.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...s})}},89847:(e,s,t)=>{Promise.resolve().then(t.bind(t,52381))},91088:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>n});var r=t(37413),a=t(4851);let n={title:`销售数据 - 蜜卡`};function l(){return(0,r.jsx)(a.default,{})}},94575:(e,s,t)=>{Promise.resolve().then(t.bind(t,83095))},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,7392,5814,3928,3443,3019,9879,5586],()=>t(51553));module.exports=r})();