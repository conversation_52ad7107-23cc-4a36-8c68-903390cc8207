import productsController from '../controllers/productsController.js';
import productsSchema from '../schemas/productsSchema.js';

/**
 * Products Routes
 * Defines API endpoints for product-related operations
 * @param {Object} fastify - Fastify instance
 * @param {Object} options - Route options
 */
export default async function (fastify, options) {
    // Get products list
    fastify.get('/products-new', {
        schema: productsSchema.getProductsListSchema,
        onRequest: [fastify.auth.authenticate],
        handler: productsController.getProductsList
    });
    
    // Get all active products
    fastify.get('/products-new/active', {
        schema: productsSchema.getAllActiveProductsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: productsController.getAllActiveProducts
    });
    
    // Get product by ID
    fastify.get('/products-new/:productId', {
        schema: productsSchema.getProductByIdSchema,
        onRequest: [fastify.auth.authenticate],
        handler: productsController.getProductById
    });
    
    // Create product
    fastify.post('/products-new', {
        schema: productsSchema.createProductSchema,
        onRequest: [
            fastify.auth.authenticate,
            fastify.auth.requirePermission('product:create')
        ],
        handler: productsController.createProduct
    });
    
    // Update product
    fastify.put('/products-new/:productId', {
        schema: productsSchema.updateProductSchema,
        onRequest: [
            fastify.auth.authenticate,
            fastify.auth.requirePermission('product:update')
        ],
        handler: productsController.updateProduct
    });
    
    // Delete product
    fastify.delete('/products-new/:productId', {
        schema: productsSchema.deleteProductSchema,
        onRequest: [
            fastify.auth.authenticate,
            fastify.auth.requirePermission('product:delete')
        ],
        handler: productsController.deleteProduct
    });
    
    // Get product courses
    fastify.get('/products-new/:productId/courses', {
        schema: productsSchema.getProductCoursesSchema,
        onRequest: [fastify.auth.authenticate],
        handler: productsController.getProductCourses
    });
    
    // Update product courses
    fastify.put('/products-new/:productId/courses', {
        schema: productsSchema.updateProductCoursesSchema,
        onRequest: [
            fastify.auth.authenticate,
            fastify.auth.requirePermission('product:update')
        ],
        handler: productsController.updateProductCourses
    });
}
