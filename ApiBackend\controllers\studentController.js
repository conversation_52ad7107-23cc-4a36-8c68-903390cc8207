import { INTERNAL_ERROR, BaseError } from '../errors/index.js';
import { studentService } from '../services/studentService.js';

/**
 * 学生控制器
 */
export const studentController = {
    /**
     * 获取学生列表
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学生列表
     */
    async getStudentList(request, reply) {
        try {
            const institutionId = request.user.institutionId;
            const { page, pageSize, search, follower, type, intentLevel } = request.query;

            // 调用服务层获取学生列表
            const { students, total } = await studentService.getStudentList(request.server, {
                institutionId,
                page,
                pageSize,
                search,
                follower,
                type,
                intentLevel
            });

            // 返回成功响应
            reply.success({
                data: {
                    list: students,
                    total,
                    page: parseInt(page, 10) || 1,
                    pageSize: parseInt(pageSize, 10) || 10
                },
                message: '获取学生列表成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            // 记录未知错误
            request.server.log.error(error);
            throw new INTERNAL_ERROR('获取学生列表失败');
        }
    },
    /**
     * 获取简易学员列表
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 简易学员列表
     */
    async getSimpleStudentList(request, reply) {
        try {
            const institutionId = request.user.institutionId;
            const { page, pageSize, search} = request.query;
            const currentPage = Math.max(Number(page), 1);
            const limit = Math.max(Number(pageSize), 1);
            const offset = (currentPage - 1) * limit;
            // 调用服务层获取简易学员列表
            const { students, total } = await studentService.getSimpleStudentList(request.server, {
                institutionId,
                limit,
                offset,
                search
            });
            // 返回成功响应
            reply.success({
                data: {
                    list: students,
                    total,
                    page: parseInt(page, 10) || 1,
                    pageSize: parseInt(pageSize, 10) || 10
                },
                message: '获取简易学员列表成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
            throw new INTERNAL_ERROR('获取简易学员列表失败');
        }
    },
    /**
     * 获取单个学生信息
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学生信息
     */
    async getStudentById(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            // 调用服务层获取学生信息
            const student = await studentService.getStudentById(request.server, {
                studentId,
                institutionId
            });

            // 返回成功响应
            reply.success({
                data: student,
                message: '获取学生信息成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            // 记录未知错误
            request.server.log.error(error);
            throw new INTERNAL_ERROR('获取学生信息失败');
        }
    },
    /**
     * 更新学生信息
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 更新后的学生信息
     */
    async updateStudent(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            const { name, gender, phone, age, birthday, email, balance, points, followUpPerson, followUpDate, source, referrer, address, idCard, school, intentionLevel, parentName, status, remark, type } = request.body;
        
            // 调用服务层更新学生信息
            const updatedStudent = await studentService.updateStudent(request.server, {
                studentId,
                institutionId,
                name, gender, phone, age, birthday, email, balance, points, followUpPerson, followUpDate, source, referrer, address, idCard, school, intentionLevel, parentName, status, remark, type
            });
            // 返回成功响应
            reply.success({
                message: '更新学生信息成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
            throw new INTERNAL_ERROR('更新学生信息失败');
        }
    },
    /**
     * 批量删除学员
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 删除后的学员列表  
     */
    async deleteStudent(request, reply) {
        try {
            const { studentIds } = request.body;
            const institutionId = request.user.institutionId;
            // 调用服务层批量删除学员
            const deletedStudents = await studentService.deleteStudent(request.server, {
                studentIds,
                institutionId
            });
            // 返回成功响应
            reply.success({
                message: '删除学员成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
            throw new INTERNAL_ERROR('删除学生信息失败');
        }
    },
    /**
     * 获取学生上课记录
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学生上课记录
     */
    async getClassesHistory(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            const { page = 1, pageSize = 10, startDate, endDate } = request.query;
            const skip = (page - 1) * pageSize;
            const take = pageSize;
            // 调用服务层获取学生上课记录
            const { list, total } = await studentService.getClassesHistory(request.server, {
                studentId,
                institutionId,
                skip,
                take,
                startDate,
                endDate
            });
            // 返回成功响应
            reply.success({
                data: {
                    list,
                    total,
                    page: parseInt(page, 10) || 1,
                    pageSize: parseInt(pageSize, 10) || 10
                },
                message: '获取学生上课记录成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 获取学生购买套餐
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学生购买套餐
     */
    async getProducts(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            const { status } = request.query;
            // 调用服务层获取学生购买套餐
            const result = await studentService.getProducts(request.server, {
                studentId,
                institutionId,
                status
            });
            // 返回成功响应
            reply.success({
                data: result,
                message: '获取学生购买套餐成功' 
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }   
            // 记录未知错误
                request.server.log.error(error);
            throw new INTERNAL_ERROR('获取学生购买套餐失败');
        }
    },
    /**
     * 获取学生购买记录
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学生购买记录
     */
    async getProductsRecords(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
           // 调用服务层获取学生购买记录
           const result = await studentService.getProductsRecords(request.server, {
            studentId,
            institutionId
           });
           // 返回成功响应
           reply.success({
            data: result,
            message: '获取学生购买记录成功'
           });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 获取学生考勤记录
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学生考勤记录
     */
    async getAttendance(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            const { page = 1, pageSize = 10, startDate, endDate } = request.query;
            const currentPage = Math.max(Number(page), 1);
            const limit = Math.max(Number(pageSize), 1);
            const offset = (currentPage - 1) * limit;
            // 调用服务层获取学生考勤记录
            const { list, total } = await studentService.getAttendance(request.server, {
                studentId,
                institutionId,
                limit,
                offset,
                startDate,
                endDate
            });
            // 返回成功响应
            reply.success({
                data: {
                    list,
                    total,
                    page: parseInt(page, 10) || 1,
                    pageSize: parseInt(pageSize, 10) || 10
                },
                message: '获取学生考勤记录成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 获取学员班级
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学员班级
     */
    async getClasses(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            // 调用服务层获取学员班级
            const result = await studentService.getClasses(request.server, {
                studentId,
                institutionId
            });
            // 返回成功响应
            reply.success({
                data: result,
                message: '获取学员班级成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 学员退出班级
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学员退出班级
     */
    async outClasses(request, reply) {
        try {
            const { classesId, studentId } = request.params;
            const institutionId = request.user.institutionId;
            // 调用服务层学员退出班级
            await studentService.outClasses(request.server, {
                classesId, studentId, institutionId });
            // 返回成功响应
            reply.success({
                message: '学员退出班级成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 获取学员跟进记录
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学员跟进记录
     */
    async getFollowRecords(request, reply) {
        try {
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            // 调用服务层获取学员跟进记录
            const result = await studentService.getFollowRecords(request.server, {
                studentId,
                institutionId
            });
            // 返回成功响应
            reply.success({
                data: result,
                message: '获取学员跟进记录成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 新增学员跟进记录
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 新增学员跟进记录
     */
    async addFollowRecords(request, reply) {
        try {
            const { studentId } = request.params;
            const institutionId = request.user.institutionId;
            const { followUpDate, nextFollowUpDate, followUpContent, intentLevel } = request.body;
            
            // 调用服务层新增学员跟进记录，使用当前用户ID作为跟进人ID
            await studentService.addFollowRecords(request.server, { 
                studentId, 
                institutionId, 
                followUpDate, 
                nextFollowUpDate, 
                followUpContent, 
                followUpUserId: request.user.id, // 使用当前登录用户作为跟进人
                intentLevel, 
                userId: request.user.id 
            });
            
            // 返回成功响应
            reply.success({
                message: '新增学员跟进记录成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
            reply.status(500).send({
                message: '新增学员跟进记录失败',
                code: 500
            });
        }
    },
    /**
     * 创建学生产品
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 创建学生产品
     */
    async createStudentProduct(request, reply) {
        try {
            const { studentId } = request.params;
            const institutionId = request.user.institutionId;
            const productData = request.body;
            const amountPaid = productData.prepaidAmount;
            // const balance = productData.amount - amountPaid;
            // 调用服务层创建学生产品
            await studentService.createStudentProduct(request.server, {...productData, amountPaid, studentId, institutionId, userId: request.user.id});
            // 返回成功响应
            reply.success({
                message: '创建学生产品成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 更新学生产品
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 更新学生产品
     */
    async updateStudentProduct(request, reply) {
        try {
            const { studentId, studentProductId } = request.params;
            const institutionId = request.user.institutionId;
            const { status, remainingCount, remarks } = request.body;
            // 调用服务层更新学生产品
            await studentService.updateStudentProduct(request.server, { studentId, studentProductId, institutionId, status, remainingCount, remarks, userId: request.user.id });
            // 返回成功响应
            reply.success({
                message: '更新学生产品成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 创建学员跟进人
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 创建学员跟进人
     */
    async createFollowUp(request, reply) {
        try {
            const { studentId } = request.params;
            const institutionId = request.user.institutionId;
            // 调用服务层创建学员跟进人
            const followUpDate = Number(new Date().getTime());
            await studentService.createFollowUp(request.server, { studentId, institutionId, followUpDate,userId: request.user.id });
            // 返回成功响应
            reply.success({
                message: '创建学员跟进人成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 获取学员套餐列表
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学员套餐列表
     */
    async getStudentProducts(request, reply) {
        try {
            const { page = 1, pageSize = 10, search, remainingTimesMin,
                remainingTimesMax, remainingDaysMin, remainingDaysMax, status
            } = request.query;
            const institutionId = request.user.institutionId;
            // 调用服务层获取学员套餐列表
            const { list, total } = await studentService.getStudentProducts(request.server, {
                institutionId,
                page,
                pageSize,
                search,
                remainingTimesMin,
                remainingTimesMax,
                remainingDaysMin,
                remainingDaysMax,
                status
            });
            // 返回成功响应
            reply.success({
                data: {
                    list,
                    total,
                    page: parseInt(page, 10) || 1,
                    pageSize: parseInt(pageSize, 10) || 10
                },
                message: '获取学员套餐列表成功'
            });
        }   
        catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 获取学员产品调整记录
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学员产品调整记录
     */
    async getStudentProductAdjustments(request, reply) {
        try {
            const { page = 1, pageSize = 10, search, teacherId, endTime, startTime } = request.query;
            const studentId = request.params.studentId;
            const institutionId = request.user.institutionId;
            // 调用服务层获取学员产品调整记录
            const { list, total } = await studentService.getStudentProductAdjustments(request.server, {
                studentId,
                institutionId,
                page,
                pageSize,
                search,
                teacherId,
                endTime,
                startTime
            });
            // 返回成功响应
            reply.success({
                data: {
                    list,
                    total,
                    page: parseInt(page, 10) || 1,
                    pageSize: parseInt(pageSize, 10) || 10
                },
                message: '获取学员产品调整记录成功'
            });
        } catch (error) {
            // 如果是已知错误类型，直接抛出
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            // 记录未知错误
            request.server.log.error(error);
        }
    },
    /**
     * 获取整体学员上课查询
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 学员上课记录
     */
    async getClassesQuery(request, reply) {
        try {
            const { page = 1, pageSize = 10, search, studentId, status, startDate, endDate } = request.query;
            const institutionId = request.user.institutionId;
            const skip = (page - 1) * pageSize;
            const take = pageSize;

            const { list, total } = await studentService.getClassesQuery(request.server, {
                institutionId,
                skip,
                take,
                search,
                studentId,
                status,
                startDate,
                endDate
            });

            reply.success({
                data: {
                    list,
                    total,
                    page: parseInt(page, 10) || 1,
                    pageSize: parseInt(pageSize, 10) || 10
                },
                message: '获取学员上课记录成功'
            });
        } catch (error) {
            request.log.error(error);
            throw new request.server.httpErrors.InternalServerError(error.message || '获取学员上课记录失败');
        }
    },
    // 获取意向学员列表
    async getIntentStudents(request, reply) {
        try {
            const data = await studentService.getIntentStudents(
                request.server,
                request.user,
                request.query
            );
            reply.success({
                data,
                message: '获取意向学员列表成功'
            });
        } catch (error) {
            request.log.error(error);
            throw new request.server.httpErrors.InternalServerError(error.message || '获取意向学员列表失败');
        }
    },
    // 获取考勤记录
    async getAttendanceRecords(request, reply) {
        try {
            const data = await studentService.getAttendanceRecords(
                request.server,
                request.user,
                request.query
            );
            reply.success({
                data,
                message: '获取学员考勤记录成功.'
            });
        } catch (error) {
            request.log.error(error);
            throw new request.server.httpErrors.InternalServerError(error.message || '获取学员考勤记录失败');
        }
    },
    /**
     * 学员产品退款
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<Object>} 退款结果
     */
    async refundProduct(request, reply) {
        try {
            const { studentId, productId } = request.params;
            const { refundReason, paymentMethod } = request.body;
            const institutionId = request.user.institutionId;

            await studentService.refundProduct(request.server, {
                studentId,
                productId,
                institutionId,
                refundReason,
                paymentMethod,
                userId: request.user.id
            });

            reply.success({
                message: '学员产品退款成功'
            });
        } catch (error) {
            request.log.error(error);
            throw new INTERNAL_ERROR(error.message || '学员产品退款失败');
        }
    },
    /**
     * 机构添加学员
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<void>}
     */
    async addStudent(request, reply) {
        try {
            const {
                name, gender, phone, birthday, source, sourceDesc, intention,
                referrer, follower, idCard, address, remarks, type
            } = request.body;

            await studentService.addStudent(request.server, {
                name,
                gender,
                phone,
                birthday: Number(birthday),
                sourceDesc,
                intention,
                follower,
                source,
                referrer,
                address,
                idCard,
                remarks,
                type,
                institutionId: request.user.institutionId,
                operatorId: request.user.id
            });

            reply.success({
                message: '添加学生成功'
            });
        } catch (error) {
            request.log.error({
                msg: '添加学生失败',
                error: error.message,
                stack: error.stack,
                body: request.body
            });

            // 如果是已知错误类型，直接抛出
            if (error instanceof BaseError) {
                throw error;
            }

            // 处理其他类型的错误
            throw new INTERNAL_ERROR(`添加学生失败: ${error.message}`);
        }
    },
    /**
     * 学员创建跟进人
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<void>}
     */
    async createFollowUpPerson(request, reply) {
        try {
            const { studentId } = request.body;
            const institutionId = request.user.institutionId;
            const followUpPersonId = request.user.id;

            await studentService.createFollowUpPerson(request.server, {
                studentId,
                institutionId,
                followUpPersonId
            });

            reply.success({
                message: '创建跟进人成功'
            });
        } catch (error) {
            request.log.error(error);
            throw new request.server.httpErrors.InternalServerError(error.message || '创建跟进人失败');
        }
    },
    /**
     * 注册学生
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<void>}
     */
    async registerStudent(request, reply) {
        try {
            const institutionId = request.params.institutionId;
            const { name, gender, phone } = request.body;

            const result = await studentService.registerStudent(request.server, {
                institutionId,
                name,
                gender,
                phone
            });

            reply.success({
                data: result,
                message: '创建学生成功'
            });
        } catch (error) {
            request.log.error(error);
            throw new request.server.httpErrors.InternalServerError(error.message || '创建学生失败');
        }
    },
    /**
     * 调整学生套餐内容
     * @param {Object} request - Fastify请求对象
     * @param {Object} reply - Fastify响应对象
     * @returns {Promise<void>}
     */
    async adjustStudentProduct(request, reply) {
        try {
            const studentProductId = request.params.studentProductId;
            const { remainingCount, remarks, remainingDays, status } = request.body;
            const institutionId = request.user.institutionId;
            const userId = request.user.id;

            await studentService.adjustStudentProduct(request.server, {
                studentProductId,
                remainingCount,
                remarks,
                remainingDays,
                status,
                institutionId,
                userId
            });

            reply.success({
                message: '更新学生套餐内容成功'
            });
        } catch (error) {
            request.log.error(error);
            if (error.code === 'P2025') {
                return reply.code(404).send({ message: '记录不存在或已被删除' });
            } else if (error.code === 'P2002') {
                return reply.code(409).send({ message: '记录冲突，可能已存在相同的记录' });
            } else if (error.code === 'P2003') {
                return reply.code(400).send({ message: '关联记录不存在' });
            }
            throw new request.server.httpErrors.InternalServerError(error.message || '更新学生套餐内容失败');
        }
    },
    /**
     * 获取跟进记录列表
     */
    async getFollowUpRecords(request, reply) {
        try {
            const result = await studentService.getFollowUpRecords({
                server: request.server,
                user: request.user,
                ...request.query
            });

            reply.success({
                data: result,
                message: '获取跟进记录成功'
            });
        } catch (error) {
            request.log.error(error);
            reply.status(500).send({
                message: '获取跟进记录失败',
                code: 500
            });
        }
    }
}; 

