import { addDays, format, parseISO } from 'date-fns';

/**
 * 获取指定日期内的周一到周日的日期
 * @param { string} startDateStr 开始日期
 * @param { string } endDateStr 结束日期
 * @param { number } targetDay -- 目标星期几(0: 周日, 1: 周一, 2: 周二, 3: 周三, 4: 周四, 5: 周五, 6: 周六)
 * @param { string } formatStr -- 日期格式(默认为 'yyyy-MM-dd HH:mm')
 * @returns { string[] } 包含周一到周日的日期的数组,格式为 'YYYY-MM-DD HH:mm'
 */

export function getWeekDayDatesInRange(startDateTs, endDateTs, targetDay, formatStr = 'yyyy-MM-dd HH:mm') {
    const startDate = new Date(startDateTs);
    const endDate = new Date(endDateTs);
    const dates = [];

    let currentDate = new Date(startDate);
    while (currentDate <= endDate) {
        if (currentDate.getDay() === targetDay) {
            dates.push(format(currentDate, formatStr));
        }
        currentDate.setDate(currentDate.getDate() + 1); // 增加一天
    }
    return dates;
}

/**
 * 获取从指定日期开始的n周内所有的周几日期
 * @param { number } startDateTs 开始日期
 * @param { number } weeks -- 周数
 * @param { number } targetDay -- 目标星期几(0: 周日, 1: 周一, 2: 周二, 3: 周三, 4: 周四, 5: 周五, 6: 周六)
 * @param { string } formatStr -- 日期格式(默认为 'yyyy-MM-dd HH:mm')
 * @param { number } lengths -- 一周的天数(默认为 7)
 * @returns { string[] }
 * 
*/
export function getTuesdaysInWeeks(startDateTs, weeks, targetDay, formatStr = 'yyyy-MM-dd HH:mm', lengths = 7) {
    const startDate = new Date(startDateTs);
    const startDay = startDate.getDay();
    const diffToTarget = (targetDay - startDay + lengths) % lengths; // 计算偏移量
    const firstTargetDate = new Date(startDate.getTime() + diffToTarget * 86400000); // 获取最近的目标日期

    return Array.from({ length: weeks }, (_, i) => 
        format(new Date(firstTargetDate.getTime() + i * lengths * 86400000), formatStr)
    );
}

/**
 * 获取从指定日期开始到结束日期内的所有天数
 * @param { number } startDateTs 开始日期
 * @param { number } endDateTs -- 结束日期
 * @param { number } dayily -- 增加的天数
 * @param { string } formatStr -- 日期格式(默认为 'yyyy-MM-dd HH:mm')
 * @returns { string[] }
 * 
*/
export function getDaysInRange(startDateTs, endDateTs, daily = 1, formatStr = 'yyyy-MM-dd HH:mm') {
    const startDate = new Date(startDateTs);
    const endDate = endDateTs ? new Date(endDateTs) : new Date(startDate.getTime() + daily * 24 * 60 * 60 * 1000);
    const dates = [];
    let currentDate = new Date(startDate);
    while (currentDate <= endDate) {
        dates.push(format(currentDate, formatStr));
        currentDate.setDate(currentDate.getDate() + 1); // 增加一天
    }
    return dates;
}