import { INTERNAL_ERROR, NOT_FOUND_ERROR, VALIDATION_ERROR } from '../errors/index.js';
import { coursesService } from '../services/coursesService.js';

/**
 * Courses Controller
 * Handles HTTP requests and responses for course-related endpoints
 */
export const coursesController = {
    /**
     * Get courses list
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async getCoursesList(request, reply) {
        try {
            const { page = 1, pageSize = 10, search, type } = request.query;
            const user = request.user;
            
            // Call service to get courses list
            const result = await coursesService.getCoursesList({
                page,
                pageSize,
                search,
                type,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: result,
                message: '获取课程列表成功'
            });
        } catch (error) {
            request.log.error({
                msg: '获取课程列表失败',
                error: error.message,
                stack: error.stack,
                query: request.query
            });
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('获取课程列表失败');
        }
    },
    
    /**
     * Get courses select list
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async getCoursesSelectList(request, reply) {
        try {
            const { id = true, name = true } = request.query;
            const user = request.user;
            
            // Call service to get courses select list
            const data = await coursesService.getCoursesSelectList({
                id,
                name,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data,
                message: '获取课程选择列表成功'
            });
        } catch (error) {
            request.log.error({
                msg: '获取课程选择列表失败',
                error: error.message,
                stack: error.stack,
                query: request.query
            });
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('获取课程选择列表失败');
        }
    },
    
    /**
     * Create course
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async createCourse(request, reply) {
        try {
            const courseData = request.body;
            const user = request.user;
            
            // Validate course data
            if (!courseData.name) {
                throw new VALIDATION_ERROR('课程名称不能为空');
            }
            
            // Call service to create course
            const result = await coursesService.createCourse({
                courseData,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: result,
                message: '创建课程成功'
            });
        } catch (error) {
            request.log.error({
                msg: '创建课程失败',
                error: error.message,
                stack: error.stack,
                body: request.body
            });
            
            if (error instanceof VALIDATION_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('创建课程失败');
        }
    },
    
    /**
     * Update course
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async updateCourse(request, reply) {
        try {
            const { courseId } = request.params;
            const courseData = request.body;
            const user = request.user;
            
            // Call service to update course
            const result = await coursesService.updateCourse({
                courseId,
                courseData,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: result,
                message: '更新课程成功'
            });
        } catch (error) {
            request.log.error({
                msg: '更新课程失败',
                error: error.message,
                stack: error.stack,
                params: request.params,
                body: request.body
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('更新课程失败');
        }
    },
    
    /**
     * Delete course
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async deleteCourse(request, reply) {
        try {
            const { courseId } = request.params;
            const user = request.user;
            
            // Call service to delete course
            await coursesService.deleteCourse({
                courseId,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                message: '删除课程成功'
            });
        } catch (error) {
            request.log.error({
                msg: '删除课程失败',
                error: error.message,
                stack: error.stack,
                params: request.params
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('删除课程失败');
        }
    },
    
    /**
     * Get course by ID
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async getCourseById(request, reply) {
        try {
            const { courseId } = request.params;
            const user = request.user;
            
            // Call service to get course by ID
            const course = await coursesService.getCourseById({
                courseId,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: course,
                message: '获取课程详情成功'
            });
        } catch (error) {
            request.log.error({
                msg: '获取课程详情失败',
                error: error.message,
                stack: error.stack,
                params: request.params
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('获取课程详情失败');
        }
    },
    
    /**
     * Associate course with product
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async associateCourseWithProduct(request, reply) {
        try {
            const { courseId } = request.params;
            const { productId } = request.body;
            const user = request.user;
            
            // Call service to associate course with product
            await coursesService.associateCourseWithProduct({
                courseId,
                productId,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                message: '关联课程与产品成功'
            });
        } catch (error) {
            request.log.error({
                msg: '关联课程与产品失败',
                error: error.message,
                stack: error.stack,
                params: request.params,
                body: request.body
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('关联课程与产品失败');
        }
    },
    
    /**
     * Dissociate course from product
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async dissociateCourseFromProduct(request, reply) {
        try {
            const { courseId, productId } = request.params;
            const user = request.user;
            
            // Call service to dissociate course from product
            await coursesService.dissociateCourseFromProduct({
                courseId,
                productId,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                message: '解除课程与产品关联成功'
            });
        } catch (error) {
            request.log.error({
                msg: '解除课程与产品关联失败',
                error: error.message,
                stack: error.stack,
                params: request.params
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('解除课程与产品关联失败');
        }
    }
};

export default coursesController;
