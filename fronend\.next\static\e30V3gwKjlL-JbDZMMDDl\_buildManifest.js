self.__BUILD_MANIFEST=function(e,r,t,s,_){return{__rewrites:{afterFiles:[{has:t,source:"/api/:path*",destination:t}],beforeFiles:[],fallback:[]},__routerFilterStatic:{numItems:38,errorRate:1e-4,numBits:729,numHashes:14,bitArray:[0,1,0,1,e,r,e,r,r,e,e,e,e,e,e,r,e,r,e,e,r,e,e,r,e,e,r,r,e,r,r,e,r,r,e,e,e,e,e,r,r,r,r,r,r,r,r,e,r,r,e,r,e,e,r,r,r,r,r,r,e,e,r,e,r,e,e,r,r,r,e,r,e,r,e,r,e,e,r,e,r,e,r,r,e,r,e,r,e,r,r,r,e,e,e,e,r,e,e,e,r,r,e,r,r,e,e,e,e,e,e,r,e,e,e,e,r,r,r,r,e,r,r,e,r,e,e,e,r,e,r,r,e,r,e,e,e,r,e,r,e,r,e,e,r,r,e,e,e,r,e,r,e,e,r,r,e,r,e,r,r,r,e,r,e,r,r,r,r,e,r,e,r,e,r,r,r,e,r,e,e,e,e,e,r,e,r,e,r,e,r,r,r,e,e,r,r,e,r,e,e,e,r,e,r,e,e,r,e,r,e,e,e,r,r,r,e,r,e,r,e,e,r,e,r,r,r,r,e,r,r,e,r,e,r,r,e,r,r,e,e,e,e,r,e,e,r,r,e,r,e,e,e,e,r,e,r,r,e,e,r,r,r,e,r,e,r,r,r,e,e,e,r,e,e,e,e,e,r,e,e,e,r,e,e,e,r,e,r,r,e,r,e,r,r,r,e,e,e,e,r,r,e,r,e,r,r,e,e,r,e,e,r,r,e,r,e,r,r,r,e,e,e,r,r,r,e,r,r,e,e,r,e,r,r,e,r,r,r,e,r,e,e,r,e,r,e,e,e,r,r,e,e,r,e,r,e,r,r,r,e,r,e,e,e,r,e,r,r,e,r,e,r,e,e,r,e,e,e,e,e,e,e,r,r,e,e,r,r,r,r,e,r,r,r,e,e,e,e,r,e,r,r,e,e,r,e,r,r,r,e,r,r,r,r,r,r,r,e,e,r,r,r,e,e,r,e,r,e,r,r,e,e,r,e,e,e,r,e,e,e,e,r,r,r,r,r,r,r,e,e,r,e,r,r,r,e,e,e,e,r,e,r,r,e,r,r,e,r,e,e,e,r,e,e,r,e,e,e,r,r,e,r,e,r,r,r,r,e,e,e,r,e,r,r,r,e,e,e,e,r,r,r,r,r,r,r,r,e,e,e,e,e,e,r,r,r,e,r,e,e,r,e,r,e,r,r,r,r,r,r,e,r,r,r,e,e,r,r,r,r,e,e,e,r,r,e,r,e,e,r,e,e,r,r,e,e,e,e,e,e,r,r,r,e,e,e,r,e,e,r,e,r,r,r,e,r,e,r,e,e,r,e,e,r,r,r,e,e,r,e,r,r,e,r,e,r,e,r,r,e,e,e,e,e,r,e,e,r,r,e,e,e,e,r,e,r,r,e,e,e,r,r,r,e,e,e,e,e,r,r,e,r,r,r,r,r,e,e,e,r,e,e,e,r,e,r,e,e,r,e,r,e,e,e,e,r,r,r,e,e,r,e,r,e,r,r,r,e,r,e,r,r,e,r,r,e,e,r,e,e,r,e,r,e,e,r,e,r,e,e,r,e,r,e,e,r,r,r,e,e,e,r,e,e,e,r,e,e,r,e,e,e,e,r,r,r,e,r,e,r,e,e,r,r,e,r,e,r]},__routerFilterDynamic:{numItems:3,errorRate:1e-4,numBits:58,numHashes:14,bitArray:[e,e,r,e,e,e,r,r,e,r,e,e,e,r,r,r,e,r,r,r,r,e,r,r,r,e,e,r,r,r,e,r,e,e,r,r,e,e,r,e,e,e,e,r,e,r,r,r,e,e,e,r,e,e,e,r,r,r]},"/_error":["static/chunks/pages/_error-e586b3427c65f9f5.js"],sortedPages:["/_app","/_error"]}}(1,0,void 0,1e-4,14),self.__BUILD_MANIFEST_CB&&self.__BUILD_MANIFEST_CB();