import { v4 as uuidv4 } from 'uuid';
import imageQueue from '../services/imageQueue.js';
export default async function (fastify, opts) {
    fastify.post('/aitools/generate-image', {
        schema: {
            tags: ['aitools'],
            summary: '图片生成',
            body: {
                type: 'object',
                required: ['prompt'],
                properties: {
                    prompt: { type: 'string' },
                    count: { type: 'number', default: 1 },
                    size: { type: 'string', default: '1024x1024' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const { prompt, count, size } = request.body

            // 处理机构的ai次数


            const taskId = uuidv4()

            await fastify.redisTask.set(
                taskId,
                JSON.stringify({ status: 'pending', taskId, prompt }),
                'EX',
                3600
            )
            await imageQueue.add(
                'image-generation',
                {
                    taskId,
                    prompt,
                    count,
                    size
                }
            )

            return reply.success({
                message: '图片生成任务已提交.',
                data: {
                    taskId
                }
                // data: image
            })
        }
    })
}
