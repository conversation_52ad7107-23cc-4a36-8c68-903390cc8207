import staffSchemas from "../schemas/staff.js"
import staffController from "../controllers/staffController.js"

export default function (fastify, options) {
    // 上传头像
    fastify.post('/staff/avatar', {
        schema: staffSchemas.uploadAvatarSchema,
        onRequest: [fastify.auth.authenticate],
        handler: staffController.uploadStaffAvatar
    })
    // 重置员工密码
    fastify.post('/staff/:staffId/resetPassword', {
        schema: staffSchemas.resetStaffPasswordSchema,
        onRequest: [fastify.auth.authenticate],
        handler: staffController.resetStaffPassword
    })
    // 更新员工状态
    fastify.put('/staff/:staffId/workingStatus', {
        schema: staffSchemas.updateStaffStatusSchema,
        onRequest: [fastify.auth.authenticate],
        handler: staffController.updateStaffStatus
    })
    // 更新员工信息
    fastify.put('/staff/:staffId', {
        schema: staffSchemas.updateStaffSchema,
        onRequest: [fastify.auth.authenticate],
        handler: staffController.updateStaff
    })
    // 删除员工
    fastify.delete('/staff/:staffId', {
        schema: staffSchemas.deleteStaffSchema,
        onRequest: [fastify.auth.authenticate],
        handler: staffController.deleteStaff
    })
    // 获取员工信息
    fastify.get('/staff/:staffId', {
        schema: staffSchemas.getStaffInfoSchema,
        onRequest: [fastify.auth.authenticate],
        handler: staffController.getStaffInfo
    })
    // 获取员工列表
    fastify.get('/staff', {
        schema: staffSchemas.getStaffListSchema,
        onRequest: [fastify.auth.authenticate],
        handler: staffController.getStaffList
    })
    // 创建员工
    fastify.post('/staff', {
        schema: staffSchemas.createStaffSchema,
        onRequest: [fastify.auth.authenticate],
        handler: staffController.createStaff
    })
}