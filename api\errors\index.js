// 基础错误类
export class BaseError extends Error {
    constructor(message, statusCode, errorCode) {
        super(message);
        this.statusCode = statusCode;
        this.errorCode = errorCode || this.constructor.name;
        this.name = this.constructor.name;
        Error.captureStackTrace(this, this.constructor);
    }
}

// 400 - 请求参数验证错误
export class VALIDATION_ERROR extends BaseError {
    constructor(message = '请求参数验证失败') {
        super(message, 400, 'VALIDATION_ERROR');
    }
}

// 401 - 未授权错误
export class UNAUTHORIZED_ERROR extends BaseError {
    constructor(message = '未授权访问') {
        super(message, 401, 'UNAUTHORIZED_ERROR');
    }
}

// 认证相关错误 (401)
export class AUTH_ERROR extends UNAUTHORIZED_ERROR {
    constructor(message = '认证失败') {
        super(message);
        this.errorCode = 'AUTH_ERROR';
    }
}

// 令牌过期错误 (401)
export class TOKEN_EXPIRED_ERROR extends UNAUTHORIZED_ERROR {
    constructor(message = '令牌已过期') {
        super(message);
        this.errorCode = 'TOKEN_EXPIRED_ERROR';
    }
}

// 令牌无效错误 (401)
export class TOKEN_INVALID_ERROR extends UNAUTHORIZED_ERROR {
    constructor(message = '令牌无效') {
        super(message);
        this.errorCode = 'TOKEN_INVALID_ERROR';
    }
}

// 403 - 禁止访问错误
export class FORBIDDEN_ERROR extends BaseError {
    constructor(message = '禁止访问') {
        super(message, 403, 'FORBIDDEN_ERROR');
    }
}

// 权限不足错误 (403)
export class PERMISSION_DENIED_ERROR extends FORBIDDEN_ERROR {
    constructor(message = '权限不足') {
        super(message);
        this.errorCode = 'PERMISSION_DENIED_ERROR';
    }
}

// 404 - 资源未找到错误
export class NOT_FOUND_ERROR extends BaseError {
    constructor(message = '资源未找到') {
        super(message, 404, 'NOT_FOUND_ERROR');
    }
}

// 409 - 资源冲突错误
export class CONFLICT_ERROR extends BaseError {
    constructor(message = '资源冲突') {
        super(message, 409, 'CONFLICT_ERROR');
    }
}

// 500 - 内部服务器错误
export class INTERNAL_ERROR extends BaseError {
    constructor(message = '服务器内部错误') {
        super(message, 500, 'INTERNAL_ERROR');
    }
}

// 错误处理中间件
export function errorHandler(error, request, reply) {
    // 如果是自定义错误类型
    if (error instanceof BaseError) {
        reply.status(error.statusCode).send({
            success: false,
            error: {
                message: error.message,
                code: error.errorCode
            }
        });
        return;
    }

    // 未知错误
    console.error('Unexpected error:', error);
    reply.status(500).send({
        success: false,
        error: {
            message: '服务器内部错误',
            code: 'INTERNAL_ERROR'
        }
    });
} 