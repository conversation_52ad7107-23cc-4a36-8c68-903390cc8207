"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8732],{30356:(e,a,r)=>{r.d(a,{C:()=>i,z:()=>n});var t=r(95155),l=r(12115),s=r(54059),o=r(9428),d=r(59434);let n=l.forwardRef((e,a)=>{let{className:r,...l}=e;return(0,t.jsx)(s.bL,{className:(0,d.cn)("grid gap-2",r),...l,ref:a})});n.displayName=s.bL.displayName;let i=l.forwardRef((e,a)=>{let{className:r,...l}=e;return(0,t.jsx)(s.q7,{ref:a,className:(0,d.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),...l,children:(0,t.jsx)(s.C1,{className:"flex items-center justify-center",children:(0,t.jsx)(o.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});i.displayName=s.q7.displayName},38732:(e,a,r)=>{r.r(a),r.d(a,{default:()=>j});var t=r(95155),l=r(54165),s=r(30285),o=r(62523),d=r(85057),n=r(59409),i=r(30356),c=r(88539),m=r(12115),u=r(48436),g=r(51154),h=r(57141),x=r(79484),b=r(63375),p=r(55594),f=r(95728);let y={name:"",phone:"",source:"",gender:"secret",sourceDesc:"",referrer:"",intention:"",follower:"",idCard:"",remarks:"",address:"",birthday:void 0},v=p.z.object({name:p.z.string().min(1,"姓名不能为空"),gender:p.z.enum(["secret","male","female"],{errorMap:()=>({message:"请选择性别"})}),phone:p.z.string().regex(/^1[3-9]\d{9}$/,"请输入正确的手机号"),idCard:p.z.string().regex(/^\d{17}[\dXx]$/,"请输入正确的身份证号"),birthday:p.z.number().min(0,"请选择生日"),source:p.z.string().optional(),sourceDesc:p.z.string().optional(),referrer:p.z.string().optional(),intention:p.z.string().optional(),follower:p.z.string().optional(),address:p.z.string().optional(),remarks:p.z.string().optional()}),j=function(e){let{open:a,onOpenChange:r,onSuccess:j}=e,[N]=(0,f.mo)(),[w,C]=(0,m.useState)(!1),[k,F]=(0,m.useState)(y),D=(0,m.useCallback)(()=>{F(y)},[]),M=(0,m.useCallback)((e,a)=>{F(r=>({...r,[e]:a}))},[]),_=(0,m.useCallback)(async()=>{try{let e={...k,follower:"all"===k.follower?"":k.follower,birthday:k.birthday?k.birthday.getTime():void 0},a=v.parse(e);C(!0);let t=await N(a);(null==t?void 0:t.code)===200&&(u.l.success("添加学员成功"),r(!1),null==j||j(),D())}catch(e){e instanceof p.z.ZodError?u.l.error(e.errors[0].message):(console.error("添加学员失败:",e),u.l.error("添加学员失败，请稍后重试"))}finally{C(!1)}},[k,N,r,j,D]),z=(0,m.useCallback)(()=>{D(),r(!1)},[D,r]),A=(0,m.useMemo)(()=>{let e=new Date;return{minDate:new Date(1900,0,1),maxDate:e,defaultMonth:e}},[]),R=(0,m.useMemo)(()=>Object.entries(h.x9).map(e=>{let[a,r]=e;return(0,t.jsx)(n.eb,{value:a,children:r.label},a)}),[]);return(0,t.jsx)(l.lG,{open:a,onOpenChange:z,children:(0,t.jsxs)(l.Cf,{className:"sm:max-w-[650px] p-4 rounded-lg shadow bg-white",children:[(0,t.jsx)(l.c7,{className:"mb-3",children:(0,t.jsx)(l.L3,{className:"text-xl font-bold text-gray-800",children:"新增学员"})}),(0,t.jsxs)("div",{className:"grid gap-4 max-h-[70vh] overflow-y-auto pr-3 dialog-content-scroll p-2",children:[(0,t.jsxs)("div",{className:"grid sm:grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)(d.J,{htmlFor:"name",className:"text-sm font-medium text-gray-700",children:["姓名 ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(o.p,{value:k.name,onChange:e=>M("name",e.target.value),id:"name",placeholder:"输入学员姓名",className:"h-9 border-gray-200 focus:ring-primary"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)(d.J,{htmlFor:"phone",className:"text-sm font-medium text-gray-700",children:["联系电话 ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(o.p,{value:k.phone,onChange:e=>M("phone",e.target.value),id:"phone",placeholder:"输入联系电话",className:"h-9 border-gray-200 focus:ring-primary"})]})]}),(0,t.jsxs)("div",{className:"grid sm:grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)(d.J,{htmlFor:"gender",className:"text-sm font-medium text-gray-700",children:["性别",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(i.z,{onValueChange:e=>M("gender",e),defaultValue:k.gender,className:"flex gap-4 pt-1",children:Object.entries(h.fb).map(e=>{let[a,r]=e;return(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(i.C,{value:a,id:a}),(0,t.jsx)(d.J,{htmlFor:a,className:"text-gray-600",children:r.label})]})})})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)(d.J,{htmlFor:"gender",className:"text-sm font-medium text-gray-700",children:["出生日期 ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(x.l,{id:"birthday",value:k.birthday,onChange:e=>M("birthday",e),minDate:A.minDate,maxDate:A.maxDate,defaultMonth:A.defaultMonth,placeholder:"选择日期"})]})]}),(0,t.jsxs)("div",{className:"grid sm:grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(d.J,{htmlFor:"source",className:"text-sm font-medium text-gray-700",children:"学员来源"}),(0,t.jsx)(o.p,{value:k.source,onChange:e=>M("source",e.target.value),id:"source",placeholder:"输入来源渠道",className:"h-9 border-gray-200 focus:ring-primary"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(d.J,{htmlFor:"sourceDesc",className:"text-sm font-medium text-gray-700",children:"来源描述"}),(0,t.jsx)(o.p,{value:k.sourceDesc,onChange:e=>M("sourceDesc",e.target.value),id:"sourceDesc",placeholder:"详细描述来源情况",className:"h-9 border-gray-200 focus:ring-primary"})]})]}),(0,t.jsxs)("div",{className:"grid sm:grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(d.J,{htmlFor:"referrer",className:"text-sm font-medium text-gray-700",children:"推荐人"}),(0,t.jsx)(o.p,{value:k.referrer,onChange:e=>M("referrer",e.target.value),id:"referrer",placeholder:"输入推荐人姓名",className:"h-9 border-gray-200 focus:ring-primary"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(d.J,{htmlFor:"intention",className:"text-sm font-medium text-gray-700",children:"意向等级"}),(0,t.jsxs)(n.l6,{onValueChange:e=>M("intention",e),children:[(0,t.jsx)(n.bq,{className:"h-9 border-gray-200",children:(0,t.jsx)(n.yv,{placeholder:"选择意向等级"})}),(0,t.jsx)(n.gC,{children:R})]})]})]}),(0,t.jsxs)("div",{className:"grid sm:grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(d.J,{htmlFor:"follower",className:"text-sm font-medium text-gray-700",children:"跟进人"}),(0,t.jsx)(b.A,{teacher:k.follower,setTeacher:e=>M("follower",e),placeholder:"选择跟进人",allOptionText:"暂不选择"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)(d.J,{htmlFor:"idcard",className:"text-sm font-medium text-gray-700",children:["身份证号",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(o.p,{value:k.idCard,onChange:e=>M("idCard",e.target.value),id:"idcard",placeholder:"输入身份证号码",className:"h-9 border-gray-200 focus:ring-primary"})]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(d.J,{htmlFor:"address",className:"text-sm font-medium text-gray-700",children:"家庭住址"}),(0,t.jsx)(o.p,{value:k.address,onChange:e=>M("address",e.target.value),id:"address",placeholder:"输入详细地址",className:"h-9 border-gray-200 focus:ring-primary"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(d.J,{htmlFor:"remarks",className:"text-sm font-medium text-gray-700",children:"备注"}),(0,t.jsx)(c.T,{value:k.remarks,onChange:e=>M("remarks",e.target.value),id:"remarks",placeholder:"添加备注信息",className:"min-h-[80px] border-gray-200 focus:ring-primary"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-3 mt-4",children:[(0,t.jsx)(s.$,{variant:"outline",onClick:z,className:"px-4 py-1 h-9 text-gray-600 hover:bg-gray-50",disabled:w,children:"取消"}),(0,t.jsx)(s.$,{type:"submit",className:"px-4 py-1 h-9",onClick:_,disabled:w,children:w?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"保存中..."]}):"保存"})]})]})})}},51154:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54059:(e,a,r)=>{r.d(a,{C1:()=>q,bL:()=>G,q7:()=>S});var t=r(12115),l=r(85185),s=r(6101),o=r(46081),d=r(63655),n=r(89196),i=r(5845),c=r(94315),m=r(11275),u=r(45503),g=r(28905),h=r(95155),x="Radio",[b,p]=(0,o.A)(x),[f,y]=b(x),v=t.forwardRef((e,a)=>{let{__scopeRadio:r,name:o,checked:n=!1,required:i,disabled:c,value:m="on",onCheck:u,form:g,...x}=e,[b,p]=t.useState(null),y=(0,s.s)(a,e=>p(e)),v=t.useRef(!1),j=!b||g||!!b.closest("form");return(0,h.jsxs)(f,{scope:r,checked:n,disabled:c,children:[(0,h.jsx)(d.sG.button,{type:"button",role:"radio","aria-checked":n,"data-state":C(n),"data-disabled":c?"":void 0,disabled:c,value:m,...x,ref:y,onClick:(0,l.m)(e.onClick,e=>{n||null==u||u(),j&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})}),j&&(0,h.jsx)(w,{control:b,bubbles:!v.current,name:o,value:m,checked:n,required:i,disabled:c,form:g,style:{transform:"translateX(-100%)"}})]})});v.displayName=x;var j="RadioIndicator",N=t.forwardRef((e,a)=>{let{__scopeRadio:r,forceMount:t,...l}=e,s=y(j,r);return(0,h.jsx)(g.C,{present:t||s.checked,children:(0,h.jsx)(d.sG.span,{"data-state":C(s.checked),"data-disabled":s.disabled?"":void 0,...l,ref:a})})});N.displayName=j;var w=e=>{let{control:a,checked:r,bubbles:l=!0,...s}=e,o=t.useRef(null),d=(0,u.Z)(r),n=(0,m.X)(a);return t.useEffect(()=>{let e=o.current,a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==r&&a){let t=new Event("click",{bubbles:l});a.call(e,r),e.dispatchEvent(t)}},[d,r,l]),(0,h.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:r,...s,tabIndex:-1,ref:o,style:{...e.style,...n,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function C(e){return e?"checked":"unchecked"}var k=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],F="RadioGroup",[D,M]=(0,o.A)(F,[n.RG,p]),_=(0,n.RG)(),z=p(),[A,R]=D(F),Y=t.forwardRef((e,a)=>{let{__scopeRadioGroup:r,name:t,defaultValue:l,value:s,required:o=!1,disabled:m=!1,orientation:u,dir:g,loop:x=!0,onValueChange:b,...p}=e,f=_(r),y=(0,c.jH)(g),[v,j]=(0,i.i)({prop:s,defaultProp:l,onChange:b});return(0,h.jsx)(A,{scope:r,name:t,required:o,disabled:m,value:v,onValueChange:j,children:(0,h.jsx)(n.bL,{asChild:!0,...f,orientation:u,dir:y,loop:x,children:(0,h.jsx)(d.sG.div,{role:"radiogroup","aria-required":o,"aria-orientation":u,"data-disabled":m?"":void 0,dir:y,...p,ref:a})})})});Y.displayName=F;var E="RadioGroupItem",J=t.forwardRef((e,a)=>{let{__scopeRadioGroup:r,disabled:o,...d}=e,i=R(E,r),c=i.disabled||o,m=_(r),u=z(r),g=t.useRef(null),x=(0,s.s)(a,g),b=i.value===d.value,p=t.useRef(!1);return t.useEffect(()=>{let e=e=>{k.includes(e.key)&&(p.current=!0)},a=()=>p.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",a),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",a)}},[]),(0,h.jsx)(n.q7,{asChild:!0,...m,focusable:!c,active:b,children:(0,h.jsx)(v,{disabled:c,required:i.required,checked:b,...u,...d,name:i.name,ref:x,onCheck:()=>i.onValueChange(d.value),onKeyDown:(0,l.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,l.m)(d.onFocus,()=>{var e;p.current&&(null===(e=g.current)||void 0===e||e.click())})})})});J.displayName=E;var L=t.forwardRef((e,a)=>{let{__scopeRadioGroup:r,...t}=e,l=z(r);return(0,h.jsx)(N,{...l,...t,ref:a})});L.displayName="RadioGroupIndicator";var G=Y,S=J,q=L},54165:(e,a,r)=>{r.d(a,{Cf:()=>g,Es:()=>x,HM:()=>m,L3:()=>b,c7:()=>h,lG:()=>n,rr:()=>p,zM:()=>i});var t=r(95155),l=r(12115),s=r(15452),o=r(54416),d=r(59434);let n=s.bL,i=s.l9,c=s.ZL,m=s.bm,u=l.forwardRef((e,a)=>{let{className:r,...l}=e;return(0,t.jsx)(s.hJ,{ref:a,className:(0,d.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...l})});u.displayName=s.hJ.displayName;let g=l.forwardRef((e,a)=>{let{className:r,children:l,...n}=e;return(0,t.jsxs)(c,{children:[(0,t.jsx)(u,{}),(0,t.jsxs)(s.UC,{ref:a,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...n,children:[l,(0,t.jsxs)(s.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});g.displayName=s.UC.displayName;let h=e=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...r})};h.displayName="DialogHeader";let x=e=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:(0,d.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...r})};x.displayName="DialogFooter";let b=l.forwardRef((e,a)=>{let{className:r,...l}=e;return(0,t.jsx)(s.hE,{ref:a,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",r),...l})});b.displayName=s.hE.displayName;let p=l.forwardRef((e,a)=>{let{className:r,...l}=e;return(0,t.jsx)(s.VY,{ref:a,className:(0,d.cn)("text-sm text-muted-foreground",r),...l})});p.displayName=s.VY.displayName},54416:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},57141:(e,a,r)=>{r.d(a,{C9:()=>l,DT:()=>d,I2:()=>o,IC:()=>u,N4:()=>c,fb:()=>g,lc:()=>t,oD:()=>n,u7:()=>m,uq:()=>s,x9:()=>i});let t={"limited-sessions":{label:"限次",color:"capitalize font-normal bg-blue-50 text-blue-700 hover:bg-blue-100 px-2.5 py-0.5 rounded-md"},"limited-time-and-count":{label:"时次限",color:"capitalize font-normal bg-violet-50 text-violet-700 hover:bg-violet-100 px-2.5 py-0.5 rounded-md"},default:{label:"未知套餐",color:"capitalize font-normal bg-slate-100 text-slate-700 hover:bg-slate-200 px-2.5 py-0.5 rounded-md"}},l={cash:{label:"现金",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},alipay:{label:"支付宝",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},wechat:{label:"微信",color:"bg-green-100 text-green-800 hover:bg-green-200"},card:{label:"银行卡",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},other:{label:"其他",color:"bg-slate-100 text-slate-800 hover:bg-slate-200"}},s=[{id:"wechat",label:"微信"},{id:"alipay",label:"支付宝"},{id:"card",label:"银行转账"},{id:"cash",label:"现金"},{id:"other",label:"其他"}],o={done:{label:"完成",color:"bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400"},arrears:{label:"欠费",color:"bg-amber-100 text-amber-800 hover:bg-amber-200 dark:bg-amber-900/30 dark:text-amber-400"},refunded:{label:"退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400"},default:{label:"未知",color:"bg-slate-100 text-slate-800 hover:bg-slate-200 dark:bg-slate-800/50 dark:text-slate-400"}},d={active:{badgeClass:"bg-emerald-50 text-emerald-700 hover:bg-emerald-100 border-emerald-200",dotClass:"bg-emerald-500",text:"正常"},expired:{badgeClass:"bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200",dotClass:"bg-amber-500",text:"已到期"},refunded:{badgeClass:"bg-red-50 text-red-700 hover:bg-red-100 border-red-200",dotClass:"bg-red-500",text:"已退款"},frozen:{badgeClass:"bg-slate-50 text-slate-700 hover:bg-slate-100 border-slate-200",dotClass:"bg-slate-500",text:"已冻结"},default:{badgeClass:"bg-slate-100 text-slate-700 hover:bg-slate-200 border-slate-200",dotClass:"bg-slate-400",text:"未知状态"}},n={all:{className:"bg-slate-100 text-slate-800 hover:bg-slate-200",text:"全部状态"},attendance:{className:"bg-green-100 text-green-800 hover:bg-green-200",text:"已考勤"},leave:{className:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200",text:"请假"},unattended:{className:"bg-orange-100 text-orange-800 hover:bg-orange-200",text:"未考勤"},default:{className:"bg-red-100 text-red-800 hover:bg-red-200",text:"缺勤"}},i={A:{label:"非常感兴趣",color:"bg-green-100 text-green-800 hover:bg-green-200"},B:{label:"比较感兴趣",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},C:{label:"一般意向",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},D:{label:"了解意向",color:"bg-orange-100 text-orange-800 hover:bg-orange-200"},E:{label:"不感兴趣",color:"bg-red-100 text-red-800 hover:bg-red-200"},default:{label:"未知",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},c={productSales:{label:"产品销售",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},courseSales:{label:"课程销售",color:"bg-green-100 text-green-800 hover:bg-green-200"},OverduePaymentCollection:{label:"逾期款项催收",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},lifestyleAndOffice:{label:"生活和办公",color:"bg-red-100 text-red-800 hover:bg-red-200"},socialAndCatering:{label:"社交和餐饮",color:"bg-purple-100 text-purple-800 hover:bg-purple-200"},refunded:{label:"退款",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"其他",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},m={completed:{label:"已完成",color:"bg-green-100 text-green-800 hover:bg-green-200"},pending:{label:"待审核",color:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200"},refunded:{label:"已退款",color:"bg-blue-100 text-blue-800 hover:bg-blue-200"},failed:{label:"审核未通过",color:"bg-red-100 text-red-800 hover:bg-red-200"},other:{label:"未知状态",color:"bg-slate-100 text-slate-700 hover:bg-slate-200"}},u={formal:{label:"正式学员",color:"text-blue-500"},graduated:{label:"历史学员",color:"text-green-500"},public:{label:"公海池",color:"text-red-500"},intent:{label:"意向学员",color:"text-yellow-500"}},g={secret:{label:"保密",color:"text-gray-500"},male:{label:"男",color:"text-blue-500"},female:{label:"女",color:"text-pink-500"}}},69074:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},79484:(e,a,r)=>{r.d(a,{l:()=>y});var t=r(95155),l=r(12115),s=r(86622),o=r(73168),d=r(24122),n=r(69074),i=r(30285),c=r(85511),m=r(14636),u=r(42355),g=r(13052);let h=["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"];function x(e){let{view:a,currentMonth:r,minDate:l,maxDate:s,onPrevious:o,onNext:d,onViewChange:n}=e,c=r.getFullYear(),m=r.getMonth(),x=Math.max(l.getFullYear(),c-6),b=Math.min(s.getFullYear(),x+11),p=()=>"calendar"===a?"month":"month"===a?"year":"calendar";return(0,t.jsx)("div",{className:"p-3 border-b",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(i.$,{variant:"ghost",size:"icon",onClick:o,className:"h-7 w-7 bg-transparent p-0",children:(0,t.jsx)(u.A,{className:"h-4 w-4"})}),(0,t.jsxs)(i.$,{variant:"ghost",onClick:()=>n(p()),className:"h-9 px-4 font-medium text-base hover:bg-transparent",children:["calendar"===a&&(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)("span",{className:"text-primary",children:h[m]}),(0,t.jsx)("span",{children:c})]}),"month"===a&&(0,t.jsx)("span",{children:c}),"year"===a&&(0,t.jsx)("span",{children:"".concat(x," - ").concat(b)})]}),(0,t.jsx)(i.$,{variant:"ghost",size:"icon",onClick:d,className:"h-7 w-7 bg-transparent p-0",children:(0,t.jsx)(g.A,{className:"h-4 w-4"})})]})})}var b=r(59434);function p(e){let{currentMonth:a,onSelectMonth:r}=e,l=a.getMonth();return(0,t.jsx)("div",{className:"p-3",children:(0,t.jsx)("div",{className:"grid grid-cols-3 gap-2",children:h.map((e,a)=>(0,t.jsx)(i.$,{variant:"outline",className:(0,b.cn)("h-10 w-full font-normal hover:bg-muted",a===l&&"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground"),onClick:()=>r(a),children:e},a))})})}function f(e){let{currentYear:a,minYear:r,maxYear:l,onSelectYear:s}=e,o=Math.max(r,a-6),d=Array.from({length:Math.min(l,o+11)-o+1},(e,a)=>o+a);return(0,t.jsx)("div",{className:"p-3",children:(0,t.jsx)("div",{className:"grid grid-cols-3 gap-2",children:d.map(e=>(0,t.jsx)(i.$,{variant:"outline",className:(0,b.cn)("h-10 w-full font-normal hover:bg-muted",e===a&&"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground"),onClick:()=>s(e),children:e},e))})})}function y(e){let{id:a,value:r,onChange:u,minDate:g,maxDate:h,defaultMonth:b,placeholder:y="选择日期"}=e,[v,j]=l.useState("calendar"),[N,w]=l.useState(()=>{var e;return r||b||(e=new Date,(0,s.e)(e,-18))});return(0,t.jsxs)(m.AM,{children:[(0,t.jsx)(m.Wv,{asChild:!0,children:(0,t.jsxs)(i.$,{variant:"outline",className:"w-full h-9 px-3 text-left font-normal border-gray-200 focus:ring-primary focus:border-primary justify-start hover:bg-gray-50",children:[(0,t.jsx)(n.A,{className:"mr-2 h-4 w-4 text-gray-500"}),r?(0,o.GP)(r,"yyyy年MM月dd日"):(0,t.jsx)("span",{className:"text-muted-foreground",children:y})]})}),(0,t.jsx)(m.hl,{className:"w-auto p-0",align:"start",children:(0,t.jsxs)("div",{className:"rounded-md border shadow-md bg-white",children:[(0,t.jsx)(x,{view:v,currentMonth:N,minDate:g,maxDate:h,onPrevious:()=>{let e=new Date(N);"calendar"===v?e.setMonth(e.getMonth()-1):e.setFullYear(e.getFullYear()-1),e.getFullYear()<g.getFullYear()||w(e)},onNext:()=>{let e=new Date(N);"calendar"===v?e.setMonth(e.getMonth()+1):e.setFullYear(e.getFullYear()+1),e.getFullYear()>h.getFullYear()||w(e)},onViewChange:j}),"month"===v?(0,t.jsx)(p,{currentMonth:N,onSelectMonth:e=>{let a=new Date(N);a.setMonth(e),w(a),j("calendar")}}):"year"===v?(0,t.jsx)(f,{currentYear:N.getFullYear(),minYear:g.getFullYear(),maxYear:h.getFullYear(),onSelectYear:e=>{let a=new Date(N);a.setFullYear(e),w(a),j("month")}}):(0,t.jsx)(c.V,{mode:"single",selected:r,onSelect:u,initialFocus:!0,locale:d.g,fromDate:g,toDate:h,month:N,onMonthChange:w,className:"rounded-md",classNames:{caption:"flex justify-center pt-2 relative items-center hidden",nav:"space-x-1 flex items-center hidden",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:"h-9 w-9 p-0 font-normal aria-selected:opacity-100",day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible"}})]})})]},a)}},85057:(e,a,r)=>{r.d(a,{J:()=>i});var t=r(95155),l=r(12115),s=r(40968),o=r(74466),d=r(59434);let n=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=l.forwardRef((e,a)=>{let{className:r,...l}=e;return(0,t.jsx)(s.b,{ref:a,className:(0,d.cn)(n(),r),...l})});i.displayName=s.b.displayName},85511:(e,a,r)=>{r.d(a,{V:()=>i});var t=r(95155);r(12115);var l=r(42355),s=r(13052),o=r(20081),d=r(59434),n=r(30285);function i(e){let{className:a,classNames:r,showOutsideDays:i=!0,...c}=e;return(0,t.jsx)(o.hv,{showOutsideDays:i,className:(0,d.cn)("p-3",a),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,d.cn)((0,n.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,d.cn)((0,n.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...r},components:{IconLeft:e=>{let{className:a,...r}=e;return(0,t.jsx)(l.A,{className:(0,d.cn)("h-4 w-4",a),...r})},IconRight:e=>{let{className:a,...r}=e;return(0,t.jsx)(s.A,{className:(0,d.cn)("h-4 w-4",a),...r})}},...c})}i.displayName="Calendar"},88539:(e,a,r)=>{r.d(a,{T:()=>o});var t=r(95155),l=r(12115),s=r(59434);let o=l.forwardRef((e,a)=>{let{className:r,...l}=e;return(0,t.jsx)("textarea",{className:(0,s.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:a,...l})});o.displayName="Textarea"}}]);