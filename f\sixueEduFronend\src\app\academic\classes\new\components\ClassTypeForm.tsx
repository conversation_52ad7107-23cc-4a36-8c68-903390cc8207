"use client"

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import type { z } from "zod"
import type { UseFormReturn } from "react-hook-form"
import type formSchema from "../schema/form"

interface ClassTypeFormProps {
  form: UseFormReturn<z.infer<typeof formSchema>>
}

export default function ClassTypeForm({ form }: ClassTypeFormProps) {
  return (
    <FormField
      control={form.control}
      name="type"
      render={({ field }) => (
        <FormItem className="space-y-3">
          <FormLabel className="text-sm font-medium text-slate-700">班级类型</FormLabel>
          <FormControl>
            <RadioGroup
              onValueChange={field.onChange}
              defaultValue={field.value}
              className="grid grid-cols-1 md:grid-cols-2 gap-4"
            >
              <FormItem className="flex items-start space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors">
                <FormControl>
                  <RadioGroupItem value="temporary" className="mt-0.5 text-slate-700" />
                </FormControl>
                <FormLabel className="font-normal text-slate-700 cursor-pointer w-full text-sm">
                  临时班级
                  <p className="text-xs text-slate-500 mt-1">适用于临时组织的短期课程</p>
                </FormLabel>
              </FormItem>
              <FormItem className="flex items-start space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors">
                <FormControl>
                  <RadioGroupItem value="fixed" className="mt-0.5 text-slate-700" />
                </FormControl>
                <FormLabel className="font-normal text-slate-700 cursor-pointer w-full text-sm">
                  固定班级
                  <p className="text-xs text-slate-500 mt-1">适用于长期固定的常规课程</p>
                </FormLabel>
              </FormItem>
            </RadioGroup>
          </FormControl>
          <FormMessage className="text-xs" />
        </FormItem>
      )}
    />
  )
}

