import fetch from 'node-fetch';
import uploadToQiniu from "../libs/qiniu.js";
import { v4 as uuidv4 } from 'uuid';

export async function generateImage(prompt, count = 1, size = '1024x1024') {
    try {
        const startTime = Date.now();
        const response = await fetch('https://qianfan.baidubce.com/v2/images/generations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.BAIDU_API_KEY}`
            },
            body: JSON.stringify({
                prompt,
                n: count,
                "model": "irag-1.0",
                size: size
            })
        })
        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.statusText}`);
        }

        const data = await response.json();
        if (!data.data?.[0]?.url) {
            throw new Error('未返回有效图片 URL');
        }
        // 生成图片时间
        const generateTime = Date.now() - startTime;
        console.log(`图片生成完成，用时 ${generateTime} 毫秒`);
        
        // uploadToQiniu
        const imageUrls = await Promise.all(data.data.map(async (item) => {
            const id = uuidv4();
            return await uploadToQiniu(item.url, id);
        }));
        const endTime = Date.now();
        const duration = endTime - startTime;
        console.log(`图片上传完成，用时 ${duration} 毫秒`);
         

        // console.log(imageUrls, "imageUrls")

        return {
            id: data.id,
            created: data.created,
            imageUrl: imageUrls
        };
    } catch (error) {
        throw new Error(`图像生成失败: ${error.message}`);
    }
}