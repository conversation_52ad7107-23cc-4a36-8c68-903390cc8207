import financeController from '../controllers/financeController.js';
import {
  buyingRecordSchema,
  teachingRecordSchema,
  salesOverviewSchema,
  salesrepOverviewSchema,
  studentOverviewSchema,
  educationOverviewSchema,
  studentFeesSchema,
  studentClassSummarySchema,
  refundRecordsSchema,
  institutionIncomeSchema
} from '../schemas/financeSchemas.js';

/**
 * Finance routes
 * @param {Object} fastify - Fastify instance
 * @param {Object} opts - Route options
 */
export default async function finance(fastify, opts) {
  // Make fastify instance available globally for the model
  global.fastify = fastify;

  // 购买记录
  fastify.get('/finance/buying-record', {
    schema: buyingRecordSchema,
    onRequest: [fastify.auth.authenticate],
    handler: financeController.getBuyingRecords
  });

  // 授课记录
  fastify.get('/finance/teaching-record', {
    schema: teachingRecordSchema,
    onRequest: [fastify.auth.authenticate],
    handler: financeController.getTeachingRecords
  });

  // 财务报表


  // 销售概览
  fastify.get('/finance/sales-overview', {
    schema: salesOverviewSchema,
    onRequest: [fastify.auth.authenticate],
    handler: financeController.getSalesOverview
  });

  // 根据销售人获取数据
  fastify.get('/finance/salesrep-overview', {
    schema: salesrepOverviewSchema,
    onRequest: [fastify.auth.authenticate],
    handler: financeController.getSalesrepOverview
  });

  //学员概览(获取学员年龄(当前时间-birthday)段范围(人数)、性别(人数), 学员课程(人数))
  fastify.get('/finance/student-overview', {
    schema: studentOverviewSchema,
    onRequest: [fastify.auth.authenticate],
    handler: financeController.getStudentOverview
  });

  //EducationOverview 整体概览
  fastify.get('/finance/education-overview', {
    schema: educationOverviewSchema,
    onRequest: [fastify.auth.authenticate],
    handler: financeController.getEducationOverview
  });

  // 学员费用
  fastify.get('/finance/student-fees', {
    schema: studentFeesSchema,
    onRequest: [fastify.auth.authenticate],
    handler: financeController.getStudentFees
  });

  // 学员消课汇总
  fastify.get('/finance/student-class-summary', {
    schema: studentClassSummarySchema,
    onRequest: [fastify.auth.authenticate],
    handler: financeController.getStudentClassSummary
  });

  // 获取退款记录
  fastify.get('/finance/refund-records', {
    schema: refundRecordsSchema,
    onRequest: [fastify.auth.authenticate],
    handler: financeController.getRefundRecords
  });


  // 机构收入
  fastify.get('/finance/institution-income', {
    schema: institutionIncomeSchema,
    onRequest: [fastify.auth.authenticate],
    handler: financeController.getInstitutionIncome
  });
}