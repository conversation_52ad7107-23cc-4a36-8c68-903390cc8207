import { INTERNAL_ERROR, NOT_FOUND_ERROR, VALIDATION_ERROR } from '../errors/index.js';
import { productsService } from '../services/productsService.js';

/**
 * Products Controller
 * Handles HTTP requests and responses for product-related endpoints
 */
export const productsController = {
    /**
     * Get products list
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async getProductsList(request, reply) {
        try {
            const { page = 1, pageSize = 10, search, type } = request.query;
            const user = request.user;
            
            // Call service to get products list
            const result = await productsService.getProductsList({
                page,
                pageSize,
                search,
                type,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: result,
                message: '获取产品列表成功'
            });
        } catch (error) {
            request.log.error({
                msg: '获取产品列表失败',
                error: error.message,
                stack: error.stack,
                query: request.query
            });
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('获取产品列表失败');
        }
    },
    
    /**
     * Get all active products
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async getAllActiveProducts(request, reply) {
        try {
            const user = request.user;
            
            // Call service to get all active products
            const products = await productsService.getAllActiveProducts({
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: products,
                message: '获取产品成功'
            });
        } catch (error) {
            request.log.error({
                msg: '获取产品失败',
                error: error.message,
                stack: error.stack
            });
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('获取产品失败');
        }
    },
    
    /**
     * Create product
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async createProduct(request, reply) {
        try {
            const productData = request.body;
            const user = request.user;
            
            // Validate product data
            if (!productData.name) {
                throw new VALIDATION_ERROR('产品名称不能为空');
            }
            
            if (productData.price === undefined || productData.price < 0) {
                throw new VALIDATION_ERROR('产品价格不能为空且必须大于等于0');
            }
            
            // Call service to create product
            const result = await productsService.createProduct({
                productData,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: result,
                message: '创建产品成功'
            });
        } catch (error) {
            request.log.error({
                msg: '创建产品失败',
                error: error.message,
                stack: error.stack,
                body: request.body
            });
            
            if (error instanceof VALIDATION_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('创建产品失败');
        }
    },
    
    /**
     * Update product
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async updateProduct(request, reply) {
        try {
            const { productId } = request.params;
            const productData = request.body;
            const user = request.user;
            
            // Call service to update product
            const result = await productsService.updateProduct({
                productId,
                productData,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: result,
                message: '更新产品成功'
            });
        } catch (error) {
            request.log.error({
                msg: '更新产品失败',
                error: error.message,
                stack: error.stack,
                params: request.params,
                body: request.body
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('更新产品失败');
        }
    },
    
    /**
     * Delete product
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async deleteProduct(request, reply) {
        try {
            const { productId } = request.params;
            const user = request.user;
            
            // Call service to delete product
            const result = await productsService.deleteProduct({
                productId,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: result,
                message: '删除产品成功'
            });
        } catch (error) {
            request.log.error({
                msg: '删除产品失败',
                error: error.message,
                stack: error.stack,
                params: request.params
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('删除产品失败');
        }
    },
    
    /**
     * Get product courses
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async getProductCourses(request, reply) {
        try {
            const { productId } = request.params;
            const user = request.user;
            
            // Call service to get product courses
            const courses = await productsService.getProductCourses({
                productId,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: courses,
                message: '获取套餐绑定的课程成功'
            });
        } catch (error) {
            request.log.error({
                msg: '获取套餐绑定的课程失败',
                error: error.message,
                stack: error.stack,
                params: request.params
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('获取套餐绑定的课程失败');
        }
    },
    
    /**
     * Update product courses
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async updateProductCourses(request, reply) {
        try {
            const { productId } = request.params;
            const { courseIds } = request.body;
            const user = request.user;
            
            // Call service to update product courses
            await productsService.updateProductCourses({
                productId,
                courseIds,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                message: '套餐绑定课程成功'
            });
        } catch (error) {
            request.log.error({
                msg: '套餐绑定课程失败',
                error: error.message,
                stack: error.stack,
                params: request.params,
                body: request.body
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('套餐绑定课程失败');
        }
    },
    
    /**
     * Get product by ID
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async getProductById(request, reply) {
        try {
            const { productId } = request.params;
            const user = request.user;
            
            // Call service to get product by ID
            const product = await productsService.getProductById({
                productId,
                institutionId: user.institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: product,
                message: '获取产品详情成功'
            });
        } catch (error) {
            request.log.error({
                msg: '获取产品详情失败',
                error: error.message,
                stack: error.stack,
                params: request.params
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('获取产品详情失败');
        }
    }
};

export default productsController;
