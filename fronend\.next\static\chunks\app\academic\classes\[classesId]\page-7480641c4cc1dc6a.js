(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1949],{26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var a=s(95155);s(12115);var r=s(74466),n=s(59434);let l=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)(l({variant:s}),t),...r})}},29899:(e,t,s)=>{"use strict";s.d(t,{default:()=>y});var a=s(95155),r=s(12115),n=s(66695),l=s(30285),i=s(26126),d=s(47863),c=s(66474),o=s(55028),m=s(91347);let u=(0,o.default)(()=>Promise.all([s.e(8737),s.e(4582),s.e(9613),s.e(7847),s.e(2464),s.e(9671)]).then(s.bind(s,68990)),{loadableGenerated:{webpack:()=>[68990]},ssr:!1}),x=e=>{let{classData:t}=e;if(!t)return(0,a.jsx)(a.Fragment,{children:"loading..."});let[s,o]=(0,r.useState)(!1),[x,f]=(0,r.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between p-2",children:[(0,a.jsx)(n.ZB,{className:"text-lg font-semibold",children:"班级信息"}),(0,a.jsx)("div",{className:"flex space-x-1",children:(0,a.jsx)(m.LQ,{permission:"class:update",children:(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>o(!0),children:"编辑"})})})]}),(0,a.jsx)(n.Wu,{className:"p-2 pt-0",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-2",children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 ",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"班级名称"}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"font-medium",children:t.name}),(0,a.jsx)(i.E,{variant:(e=>{switch(e){case" active":return"secondary";case"已结束":return"destructive";default:return"default"}})(t.status),className:"".concat("active"===t.status?"bg-green-100 text-green-800 hover:bg-green-200":""," font-normal"),children:"active"===t.status?"进行中":"已结束"})]})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"班级编号"}),(0,a.jsx)("p",{className:"font-medium",children:t.id})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"课程包"}),(0,a.jsx)("div",{className:"flex items-center space-x-1",children:(0,a.jsx)(i.E,{variant:"secondary",className:"font-normal",children:t.course.name})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"开课时间"}),(0,a.jsx)("p",{className:"font-medium",children:new Date(t.startDate).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"主讲教师"}),(0,a.jsx)("p",{className:"font-medium",children:t.teacher.name})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"备注"}),(0,a.jsx)("p",{className:"font-medium",children:t.remarks})]})}),(0,a.jsxs)("div",{className:"border-t pt-2 mt-1",children:[(0,a.jsxs)(l.$,{variant:"ghost",size:"sm",onClick:()=>{f(!x)},className:"flex items-center justify-center w-full text-sm text-muted-foreground",children:["班级设置 ",x?(0,a.jsx)(d.A,{className:"ml-1 h-4 w-4"}):(0,a.jsx)(c.A,{className:"ml-1 h-4 w-4"})]}),x&&(0,a.jsx)("div",{className:"mt-2 space-y-2 bg-muted/30 p-2 rounded-md",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"flex flex-col p-2 bg-card rounded-md",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"开放预约"}),t.isReserve?(0,a.jsx)(i.E,{variant:"default",className:"bg-green-100 text-green-800 hover:bg-green-200",children:"已开启"}):(0,a.jsx)(i.E,{variant:"secondary",className:"opacity-70",children:"未开启"})]}),t.isReserve&&(0,a.jsxs)("div",{className:"text-sm mt-2 space-y-1 text-muted-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"w-16 inline-block",children:"开始时间:"}),(0,a.jsx)("span",{className:"font-medium text-foreground",children:t.appointmentStartTime||"未设置"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"w-16 inline-block",children:"截止时间:"}),(0,a.jsx)("span",{className:"font-medium text-foreground",children:t.appointmentEndTime||"未设置"})]})]})]}),(0,a.jsx)("div",{className:"flex flex-col p-2 bg-card rounded-md",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"学员扫码考勤"}),t.isQRCodeAttendance?(0,a.jsx)(i.E,{variant:"default",className:"bg-green-100 text-green-800 hover:bg-green-200",children:"已开启"}):(0,a.jsx)(i.E,{variant:"secondary",className:"opacity-70",children:"未开启"})]})}),(0,a.jsx)("div",{className:"flex flex-col p-2 bg-card rounded-md ",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"系统自动考勤"}),t.isAutoCheckIn?(0,a.jsx)(i.E,{variant:"default",className:"bg-green-100 text-green-800 hover:bg-green-200",children:"已开启"}):(0,a.jsx)(i.E,{variant:"secondary",className:"opacity-70",children:"未开启"})]})}),(0,a.jsxs)("div",{className:"flex flex-col p-2 bg-card rounded-md ",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"开放请假"}),t.isOnLeave?(0,a.jsx)(i.E,{variant:"default",className:"bg-green-100 text-green-800 hover:bg-green-200",children:"已开启"}):(0,a.jsx)(i.E,{variant:"secondary",className:"opacity-70",children:"未开启"})]}),t.isOnLeave&&(0,a.jsx)("div",{className:"text-sm mt-2 text-muted-foreground",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"w-16 inline-block",children:"截止时间:"}),(0,a.jsx)("span",{className:"font-medium text-foreground",children:t.leaveDeadline||"未设置"})]})})]}),(0,a.jsx)("div",{className:"flex flex-col p-2 bg-card rounded-md ",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"显示周期数"}),t.isShowWeekCount?(0,a.jsx)(i.E,{variant:"default",className:"bg-green-100 text-green-800 hover:bg-green-200",children:"已开启"}):(0,a.jsx)(i.E,{variant:"secondary",className:"opacity-70",children:"未开启"})]})})]})})]})]})})]}),s&&(0,a.jsx)(u,{open:s,onOpenChange:o,classes:t})]})};var f=s(55733),v=s(35695),g=s(73168),h=s(24122),p=s(27893);let b=(0,o.default)(()=>Promise.all([s.e(8737),s.e(4582),s.e(5620),s.e(9613),s.e(7945),s.e(6874),s.e(9110),s.e(3611)]).then(s.bind(s,43611)),{loadableGenerated:{webpack:()=>[43611]},ssr:!1}),j=(0,o.default)(()=>s.e(5192).then(s.bind(s,15192)),{loadableGenerated:{webpack:()=>[15192]},ssr:!1}),N=(0,o.default)(()=>Promise.all([s.e(8737),s.e(4582),s.e(5620),s.e(9613),s.e(7945),s.e(814),s.e(9110),s.e(9184)]).then(s.bind(s,59184)),{loadableGenerated:{webpack:()=>[59184]},ssr:!1}),y=(0,r.memo)(()=>{let{classesId:e}=(0,v.useParams)(),[t,s]=(0,r.useState)(null),{data:l,isLoading:i}=(0,p.D_)(e);console.log(l," get classes detail data."),(0,r.useEffect)(()=>{if(l&&!i){let{header:e,students:t}=function(e){let t=[],s=new Map;return e.forEach(e=>{var a;e.StudentWeeklySchedule.forEach((t,a)=>{s.has(t.student.id)||s.set(t.student.id,{id:t.student.id,name:t.student.name,attendance:[]}),s.get(t.student.id).attendance[e.currentWeeks-1]=t.status||"unattended"}),t.push({id:e.id,title:"第".concat(e.currentWeeks,"周期"),date:(a=e.startDate,(0,g.GP)(new Date(a),"EEEE yyyy-MM-dd",{locale:h.g})),time:"".concat(e.startTime,"-").concat(e.endTime)})}),{header:t,students:Array.from(s.values())}}(null==l?void 0:l.classesSchedule);s({header:e,students:t})}},[l,i]);let d=(0,r.useMemo)(()=>[{id:"班级课节",label:"班级课节",content:(0,a.jsx)(b,{classesSchedule:null==l?void 0:l.classesSchedule})},{id:"出勤详细",label:"出勤详细",content:(0,a.jsx)(j,{attendanceData:t})},{id:"studentList",label:"学员列表",content:(0,a.jsx)(N,{students:null==l?void 0:l.students})}],[l,i,t]);return i?(0,a.jsx)("div",{children:"加载中..."}):(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"pt-6",children:(0,a.jsx)(x,{classData:l||{}})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"pt-6",children:(0,a.jsx)(f.Q,{defaultTab:"班级课节",tabs:d,variant:"underline"})})})]})})},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>d});var a=s(95155),r=s(12115),n=s(99708),l=s(74466),i=s(59434);let d=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:s,variant:r,size:l,asChild:c=!1,...o}=e,m=c?n.DX:"button";return(0,a.jsx)(m,{className:(0,i.cn)(d({variant:r,size:l,className:s})),ref:t,...o})});c.displayName="Button"},38996:(e,t,s)=>{Promise.resolve().then(s.bind(s,29899))},55733:(e,t,s)=>{"use strict";s.d(t,{Q:()=>x});var a=s(95155),r=s(12115),n=s(59434),l=s(26126),i=s(74466);let d=(0,i.F)("",{variants:{variant:{default:"border-b flex -mb-px space-x-6",underline:"relative flex overflow-x-auto",pill:"bg-muted p-1 rounded-lg flex mb-4",vertical:"w-48 shrink-0 border-r pr-4 flex flex-col space-y-1"}},defaultVariants:{variant:"default"}}),c=(0,i.F)("transition-colors",{variants:{variant:{default:"py-2 border-b-2 font-medium text-sm flex items-center gap-2",underline:"py-2 mr-8 font-medium text-sm transition-colors relative flex items-center gap-2",pill:"flex-1 py-1.5 px-3 text-sm font-medium rounded-md flex items-center justify-center gap-2",vertical:"flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md text-left"},state:{active:"",inactive:""}},compoundVariants:[{variant:"default",state:"active",className:"border-primary text-primary"},{variant:"default",state:"inactive",className:"border-transparent text-muted-foreground hover:text-foreground hover:border-border"},{variant:"underline",state:"active",className:"text-primary"},{variant:"underline",state:"inactive",className:"text-muted-foreground hover:text-foreground"},{variant:"pill",state:"active",className:"bg-background text-foreground shadow-sm"},{variant:"pill",state:"inactive",className:"text-muted-foreground hover:text-foreground"},{variant:"vertical",state:"active",className:"bg-accent text-accent-foreground"},{variant:"vertical",state:"inactive",className:"text-muted-foreground hover:bg-muted hover:text-foreground"}],defaultVariants:{variant:"default",state:"inactive"}}),o=(0,i.F)("",{variants:{variant:{default:"py-4",underline:"py-4",pill:"",vertical:"flex-1"}},defaultVariants:{variant:"default"}}),m=r.memo(e=>{let{tab:t,isActive:s,showBadges:r}=e;return s?t.content?(0,a.jsx)(a.Fragment,{children:t.content}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:t.label}),r&&void 0!==t.badge&&(0,a.jsx)(l.E,{variant:"default",children:t.badge})]}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:["这是 ",t.label," 标签页的内容区域。",r&&void 0!==t.badge&&" 您有 ".concat(t.badge," 个未读").concat(t.label,"。")]})]}):null},(e,t)=>!e.isActive&&!t.isActive||e.isActive===t.isActive&&e.tab.id===t.tab.id&&e.showBadges===t.showBadges);m.displayName="TabContent";let u=r.memo(e=>{let{tab:t,isActive:s,variant:r,showIcons:i,showBadges:d,onClick:o}=e;return(0,a.jsxs)("button",{onClick:o,disabled:t.disabled,className:(0,n.cn)(c({variant:r,state:s?"active":"inactive"}),t.disabled&&"opacity-50 cursor-not-allowed"),children:[i&&t.icon,(0,a.jsx)("span",{children:t.label}),d&&void 0!==t.badge&&(0,a.jsx)(l.E,{variant:s?"default":"secondary",className:"ml-1 px-1.5 py-0.5 h-5",children:t.badge}),"underline"===r&&s&&(0,a.jsx)("span",{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-primary"})]})});function x(e){let{tabs:t,defaultTab:s,onChange:l,className:i,variant:c="default",showIcons:x=!0,showBadges:f=!0}=e,v=r.useRef(!1),[g,h]=r.useState(""),p=r.useCallback(e=>{e!==g&&(h(e),null==l||l(e))},[g,l]);r.useEffect(()=>{if(!v.current){var e;v.current=!0,h(s||(null===(e=t[0])||void 0===e?void 0:e.id)||"")}},[s,t]);let b=r.useMemo(()=>t.reduce((e,t)=>(e.set(t.id,t),e),new Map),[t]),j=r.useMemo(()=>new Set(t.map(e=>e.id)),[t]),N=r.useRef(new Map);r.useEffect(()=>{Array.from(N.current.keys()).filter(e=>!j.has(e)).forEach(e=>N.current.delete(e)),t.forEach(e=>{N.current.has(e.id)||N.current.set(e.id,()=>p(e.id))})},[t,j,p]);let y=r.useCallback(e=>N.current.get(e)||(()=>p(e)),[p]),{isVertical:w,showUnderlineBorder:k}=r.useMemo(()=>({isVertical:"vertical"===c,showUnderlineBorder:"underline"===c}),[c]),E=r.useMemo(()=>{let e=b.get(g);return e?(0,a.jsx)(m,{tab:e,isActive:!0,showBadges:f},g):null},[g,b,f]);return r.useEffect(()=>{},[t.length,g]),(0,a.jsxs)("div",{className:(0,n.cn)(w?"flex gap-8":"w-full",i),children:[(0,a.jsxs)("div",{className:d({variant:c}),children:[k&&(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-border"}),t.map(e=>(0,a.jsx)(u,{tab:e,isActive:g===e.id,variant:c,showIcons:x,showBadges:f,onClick:y(e.id)},e.id))]}),(0,a.jsx)("div",{className:o({variant:c}),children:E})]})}u.displayName="TabButton"},59434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(52596),r=s(39688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},65436:(e,t,s)=>{"use strict";s.d(t,{G:()=>n,j:()=>r});var a=s(34540);let r=()=>(0,a.wA)(),n=a.d4},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>l,aR:()=>i,wL:()=>m});var a=s(95155),r=s(12115),n=s(59434);let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});l.displayName="Card";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...r})});i.displayName="CardHeader";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});d.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...r})});c.displayName="CardDescription";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...r})});o.displayName="CardContent";let m=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...r})});m.displayName="CardFooter"},91347:(e,t,s)=>{"use strict";s.d(t,{LQ:()=>n});var a=s(95155),r=s(65436);let n=e=>{let{permission:t,children:s,fallback:n=null,logic:l="any"}=e,i=(0,r.G)(e=>e.userPermissions.permissions);if(!t||Array.isArray(t)&&0===t.length)return(0,a.jsx)(a.Fragment,{children:s});let d=Array.isArray(t)?t:[t],c=!1;return("all"===l?d.every(e=>i.includes(e)):d.some(e=>i.includes(e)))?(0,a.jsx)(a.Fragment,{children:s}):(0,a.jsx)(a.Fragment,{children:n})};s(30285);var l=s(12115),i=s(74466),d=s(59434);let c=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}});l.forwardRef((e,t)=>{let{className:s,variant:r,...n}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,d.cn)(c({variant:r}),s),...n})}).displayName="Alert",l.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h5",{ref:t,className:(0,d.cn)("mb-1 font-medium leading-none tracking-tight",s),...r})}).displayName="AlertTitle",l.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,d.cn)("text-sm [&_p]:leading-relaxed",s),...r})}).displayName="AlertDescription"}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8687,4201,4540,3168,3974,9624,6315,7358],()=>t(38996)),_N_E=e.O()}]);