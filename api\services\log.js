/**
 * 日志服务
 */
export default  {
    /**
     * 获取操作日志列表
     * @param {Object} client - 数据库连接
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {string} params.userId - 用户ID
     * @param {string} params.operationType - 操作类型
     * @param {string} params.describe - 描述
     * @param {string} params.startTime - 开始时间
     * @param {string} params.endTime - 结束时间
     * @param {string} institutionId - 机构ID
     * @returns {Promise<Object>} 日志列表和总数
     */
    async getLogs(client, params, institutionId) {
        const { page = 1, pageSize = 10, userId, operationType, describe, startTime, endTime } = params;
        
        // 转换页码和每页数量为数字
        const pageNum = Number(page);
        const pageSizeNum = Number(pageSize);
        
        // 构建WHERE条件
        const conditions = ['"institutionId" = $1'];
        const queryParams = [institutionId];
        let paramIndex = 2;

        if (userId) {
            conditions.push(`ol."userId" = $${paramIndex}`);
            queryParams.push(userId);
            paramIndex++;
        }
        
        if (operationType) {
            conditions.push(`ol."operationType" = $${paramIndex}`);
            queryParams.push(operationType);
            paramIndex++;
        }
        
        if (describe) {
            conditions.push(`ol."describe" ILIKE $${paramIndex}`);
            queryParams.push(`%${describe}%`);
            paramIndex++;
        }
        
        if (startTime) {
            conditions.push(`ol."createdAt" >= $${paramIndex}`);
            queryParams.push(new Date(startTime));
            paramIndex++;
        }
        
        if (endTime) {
            conditions.push(`ol."createdAt" <= $${paramIndex}`);
            queryParams.push(new Date(endTime));
            paramIndex++;
        }
        
        const whereClause = conditions.join(' AND ');
        
        // 构建总数查询
        const countQuery = `
            SELECT COUNT(*) as total
            FROM "operation_log" as ol
            WHERE ${whereClause}
        `;
        
        // 构建数据查询，包含用户关联
        const dataQuery = `
            SELECT 
                ol.id,
                ol."operationType",
                ol."describe",
                ol."createdAt",
                u."name" as "userName"
            FROM 
                "operation_log" ol
            LEFT JOIN 
                "users" u ON ol."userId" = u.id
            WHERE 
                ${whereClause}
            ORDER BY 
                ol."createdAt" DESC
            OFFSET $${paramIndex}
            LIMIT $${paramIndex + 1}
        `;
        
        const paginationParams = [...queryParams, (pageNum - 1) * pageSizeNum, pageSizeNum];
        console.log(paginationParams);
        // 并行执行两个查询
        const [totalResult, logsResult] = await Promise.all([
            client.query(countQuery, queryParams),
            client.query(dataQuery, paginationParams)
        ]);
        
        const total = parseInt(totalResult.rows[0].total);
        
        return {
            list: logsResult.rows,
            total,
            page: pageNum,
            pageSize: pageSizeNum
        };
    }
}
