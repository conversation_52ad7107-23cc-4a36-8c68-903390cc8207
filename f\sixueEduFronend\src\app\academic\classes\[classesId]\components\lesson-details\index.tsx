import { DataTable } from "@/components/ui/data-table";
import { lessonDetailsColumns } from "./Columns";



export default function LessonDetails({ classesSchedule }: { classesSchedule: any[] }) {


  if (!classesSchedule) {
    return <>loading...</>;
  }
  const users = classesSchedule || [];

  return (
    <div>
      <DataTable
        key={"classes[id]lessonDetails"}
        columns={lessonDetailsColumns}
        data={users}
      />
    </div>
  )
}