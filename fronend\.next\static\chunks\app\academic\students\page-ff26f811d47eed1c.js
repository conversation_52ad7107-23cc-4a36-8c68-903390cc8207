(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9941],{18615:(e,a,d)=>{"use strict";d.d(a,{default:()=>h});var t=d(95155),s=d(55733),n=d(55028),l=d(91347),r=d(68056);let i=(0,n.default)(()=>Promise.all([d.e(8687),d.e(4201),d.e(8737),d.e(4582),d.e(5620),d.e(3168),d.e(9613),d.e(7945),d.e(791),d.e(7649),d.e(9624),d.e(9110),d.e(3870),d.e(178)]).then(d.bind(d,70178)),{loadableGenerated:{webpack:()=>[70178]},ssr:!1}),c=(0,n.default)(()=>Promise.all([d.e(8687),d.e(4201),d.e(8737),d.e(4582),d.e(5620),d.e(9613),d.e(7945),d.e(5761),d.e(6162),d.e(9624),d.e(9110),d.e(1219)]).then(d.bind(d,13520)),{loadableGenerated:{webpack:()=>[13520]},ssr:!1}),o=(0,n.default)(()=>Promise.all([d.e(8687),d.e(4201),d.e(8737),d.e(4582),d.e(5620),d.e(3168),d.e(9613),d.e(7945),d.e(81),d.e(7935),d.e(9624),d.e(9110),d.e(2206),d.e(5041)]).then(d.bind(d,5041)),{loadableGenerated:{webpack:()=>[5041]},ssr:!1}),u=(0,n.default)(()=>Promise.all([d.e(8687),d.e(4201),d.e(8737),d.e(4582),d.e(5620),d.e(3168),d.e(9613),d.e(7945),d.e(791),d.e(9624),d.e(9110),d.e(3870),d.e(3010)]).then(d.bind(d,73010)),{loadableGenerated:{webpack:()=>[73010]},ssr:!1});function h(){var e;let{hasPermission:a}=(0,r.J)()||{hasPermission:()=>!1},d=[{id:"info",key:"info",label:"在读学员",content:(0,t.jsx)(l.LQ,{permission:"student:read:active",children:(0,t.jsx)(i,{})}),hidden:!a("student:read:active")},{id:"package",key:"package",label:"学员套餐",content:(0,t.jsx)(l.LQ,{permission:"student:package:read",children:(0,t.jsx)(c,{})}),hidden:!a("student:package:read")},{id:"productAdjustments",key:"productAdjustments",label:"产品调整记录",content:(0,t.jsx)(l.LQ,{permission:"student:product:adjustment:read",children:(0,t.jsx)(o,{})}),hidden:!a("student:product:adjustment:read")},{id:"graduate",key:"graduate",label:"毕业学员",content:(0,t.jsx)(l.LQ,{permission:"student:read:graduated",children:(0,t.jsx)(u,{})}),hidden:!a("student:read:graduated")}];console.log(d);let n=d.filter(e=>!e.hidden);return(console.log(n),0===n.length)?(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("p",{className:"text-gray-500",children:"您没有查看学员管理的权限"})}):(0,t.jsx)("div",{className:"space-y-4 p-4",children:(0,t.jsx)(s.Q,{tabs:n,variant:"underline",defaultTab:(null===(e=n[0])||void 0===e?void 0:e.key)||"info"})})}},38107:(e,a,d)=>{Promise.resolve().then(d.bind(d,18615))}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,4540,6639,6315,7358],()=>a(38107)),_N_E=e.O()}]);