import { v4 as uuidv4 } from 'uuid';

/**
 * Bill service
 */
class BillService {
    constructor() {
        // 延迟初始化 prisma
        this._prisma = null;
    }

    // 获取 prisma 实例的 getter
    get prisma() {
        if (!this._prisma) {
            this._prisma = global.fastify?.prisma;
            if (!this._prisma) {
                throw new Error('Prisma client is not initialized');
            }
        }
        return this._prisma;
    }

    // 处理 BigInt 和 Decimal 类型
    _serializeData(data) {
        if (data === null || data === undefined) {
            return data;
        }
        if (typeof data === 'bigint') {
            return Number(data);
        }
        if (data instanceof Date) {
            return data.toISOString();
        }
        if (typeof data === 'object') {
            if (Array.isArray(data)) {
                return data.map(item => this._serializeData(item));
            }
            const result = {};
            for (const key in data) {
                result[key] = this._serializeData(data[key]);
            }
            return result;
        }
        return data;
    }

    /**
     * 获取账单列表
     */
    async getBills({ user, page, pageSize, search, startTime, endTime }) {
        const where = {
            institutionId: user.institutionId
        };

        if (search) {
            where.OR = [
                {
                    student: {
                        name: {
                            contains: search
                        }
                    }
                }
            ];
        }

        if (startTime && endTime) {
            where.paymentTime = {
                gte: BigInt(startTime),
                lte: BigInt(endTime)
            };
        }

        const [total, bills] = await Promise.all([
            this.prisma.bill.count({ where }),
            this.prisma.bill.findMany({
                where,
                include: {
                    student: {
                        select: {
                            id: true,
                            name: true,
                            phone: true
                        }
                    },
                    operator: {
                        select: {
                            id: true,
                            name: true
                        }
                    }
                },
                skip: (page - 1) * pageSize,
                take: pageSize,
                orderBy: {
                    createdAt: 'desc'
                }
            })
        ]);

        // 序列化数据
        const serializedBills = this._serializeData(bills);

        return {
            total,
            page,
            pageSize,
            list: serializedBills
        };
    }

    /**
     * 创建账单
     */
    async createBill({ user, ...data }) {
        const result = await this.prisma.bill.create({
            data: {
                // ...data,
                id: uuidv4(),
                amount: data.amount || 0,
                paymentMethod: data.paymentMethod || 'other',
                paymentTime: data.paymentTime || BigInt(Date.now()),
                billType: data.billType || 'other', 
                institutionId: user.institutionId,
                source: data.source || 'other',
                operatorId: data.operatorId || user.id,
                operatorTime: BigInt(Date.now()),
                remarks: data.remarks || '',
                status: data.status || 'completed'
            }
        });

        return this._serializeData(result);
    }

    /**
     * 获取年度数据
     */
    async getAnnualData({ user, type, startDate, endDate, label, year, month }) {
        const currentDate = new Date();
        const targetYear = year || currentDate.getFullYear();
        const targetMonth = month ? month - 1 : currentDate.getMonth();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth();
        
        // 确定日期范围
        let startTimestamp, endTimestamp;
        
        if (type === 'range' && startDate && endDate) {
            startTimestamp = new Date(startDate).getTime();
            endTimestamp = new Date(endDate).getTime();
        } else if (type === 'monthly') {
            startTimestamp = new Date(targetYear, targetMonth, 1).getTime();
            endTimestamp = new Date(targetYear, targetMonth + 1, 1).getTime();
        } else { // yearly
            startTimestamp = new Date(targetYear, 0, 1).getTime();
            endTimestamp = new Date(targetYear === currentYear ? 
                currentYear : currentMonth + 1, 1,
                targetYear + 1, 0, 1).getTime();
        }

        // 查询数据
        const bills = await this.prisma.bill.findMany({
            where: {
                institutionId: user.institutionId,
                paymentTime: { 
                    gte: BigInt(startTimestamp), 
                    lt: BigInt(endTimestamp) 
                },
                status: 'completed',
                ...(label !== 'all' && { billType: label })
            }
        });

        // 处理图表数据
        const { labels, datasets } = this._processChartData(
            bills, type, label, startTimestamp, endTimestamp,
            targetYear, targetMonth, currentYear, currentMonth
        );

        return {
            labels,
            datasets,
            metadata: { 
                year: targetYear, 
                month, 
                type, 
                isCurrentYear: targetYear === currentYear 
            }
        };
    }

    _processChartData(bills, type, label, startTimestamp, endTimestamp, year, month, currentYear, currentMonth) {
        // 生成标签
        let labels;
        
        if (type === 'range') {
            const start = new Date(startTimestamp);
            const end = new Date(endTimestamp);
            const daysInRange = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
            
            labels = Array.from({ length: daysInRange }, (_, i) => {
                const date = new Date(start);
                date.setDate(start.getDate() + i);
                return `${date.getMonth() + 1}-${date.getDate()}`;
            });
        } else if (type === 'monthly') {
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            labels = Array.from({ length: daysInMonth }, (_, i) => i + 1);
        } else { // yearly
            const monthLabels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
            labels = year === currentYear ? monthLabels.slice(0, currentMonth + 1) : monthLabels;
        }
        
        // 初始化数据集
        const datasets = [];
        const dataMap = {};
        
        if (label === 'all' || label === 'expense') {
            const expenseData = new Array(labels.length).fill(0);
            datasets.push({ label: '支出', data: expenseData });
            dataMap['expense'] = expenseData;
        }
        
        if (label === 'all' || label === 'income') {
            const incomeData = new Array(labels.length).fill(0);
            datasets.push({ label: '收入', data: incomeData });
            dataMap['income'] = incomeData;
        }
        
        // 填充数据
        bills.forEach(bill => {
            const billTime = Number(bill.paymentTime);
            const billAmount = Number(bill.amount);
            let index;
            
            if (type === 'range') {
                index = Math.floor((billTime - startTimestamp) / (1000 * 60 * 60 * 24));
            } else if (type === 'monthly') {
                index = new Date(billTime).getDate() - 1;
            } else { // yearly
                index = new Date(billTime).getMonth();
            }
            
            if (index >= 0 && index < labels.length && dataMap[bill.billType]) {
                dataMap[bill.billType][index] += billAmount;
            }
        });
        
        return { labels, datasets };
    }
}

// 创建并导出 billService 单例
export default new BillService();