import { ColumnDef } from "@tanstack/react-table"
import { PurchaseRecordSchedule } from "./type"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { TextDisplay } from "@/components/ui/text-display"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { PAYMENT_METHOD_MAP, PURCHASE_RECORD_TYPE_MAP } from "@/constants"
import { formatCurrency } from "@/utils/format/formatCurrency"
import ActionProductRefund from "@/components/student-info-dialog/components/package-table/actions/refund"
import dynamic from "next/dynamic"
const StudentInfoDialog = dynamic(() => import("@/components/student-info-dialog"), { ssr: false })



export const purchaseRecordColumns: ColumnDef<PurchaseRecordSchedule>[] = [
  {
    accessorKey: "studentName",
    header: '学员姓名',
    cell: ({ row }) => <div className="font-medium text-sm">{row.original.student?.name}</div>,
  },
  {
    accessorKey: "phone",
    header: '手机号码',
    cell: ({ row }) => <div className="text-sm text-muted-foreground">{row.original.student?.phone}</div>,
  },
  {
    accessorKey: "shapperName",
    header: '商品名称',
    cell: ({ row }) => <div className="font-medium text-sm whitespace-nowrap">{row.original.product?.name}</div>,
  },
  {
    accessorKey: "amount",
    header: '产品金额',
    cell: ({ row }) => <div className="font-medium text-sm text-emerald-600">{formatCurrency(row.original.amount)}</div>,
  },
  {
    accessorKey: "amountPaid",
    header: '实收金额',
    cell: ({ row }) => <div className="font-medium text-sm text-emerald-600">{formatCurrency(row.original.amountPaid)}</div>,
  },
  {
    accessorKey: "amountUnpaid",
    header: '未付金额',
    cell: ({ row }) => <div className="font-medium text-sm text-rose-600 ">{formatCurrency(row.original.amountUnpaid)}</div>,
  },
  {
    accessorKey: "paymentMethod",
    header: '支付方式',
    cell: ({ row }) => {
      const payment = String(row.getValue("paymentMethod") || "other").toLowerCase();
      const { label, color } = PAYMENT_METHOD_MAP[payment] || PAYMENT_METHOD_MAP.other;
      return (
        <Badge className={cn("font-normal border py-0.5", color)}>
          {label}
        </Badge>
      );
    },
  },
  {
    accessorKey: "paymentTime",
    header: '支付时间',
    cell: ({ row }) => {
      const date = row.original?.paymentTime
      if (date) {
        const dt = format(new Date(date), "yyyy-MM-dd HH:mm:ss", { locale: zhCN })
        return <div className="text-sm text-muted-foreground">{dt}</div>
      }
      return <div className="text-sm text-muted-foreground">-</div>
    },
  },
  {
    accessorKey: "status",
    header: '状态',
    cell: ({ row }) => {
      const status = String(row.getValue("status") || "other").toLowerCase();
      const { label, color } = PURCHASE_RECORD_TYPE_MAP[status] || PURCHASE_RECORD_TYPE_MAP.default;
      return (
        <Badge className={cn("font-normal border py-0.5", color)}>
          {label}
        </Badge>
      );
    },
  },
  {
    accessorKey: "giftCount",
    header: '赠送次数',
    cell: ({ row }) => <div className="text-sm">{row.original.giftCount}</div>,
  },
  {
    accessorKey: "giftDays",
    header: '赠送天数',
    cell: ({ row }) => <div className="text-sm">{row.original.giftDays}</div>,
  },
  {
    accessorKey: "salesRepName",
    header: '销售人员',
    cell: ({ row }) => <div className="text-sm">{row.original.salesRepresentative?.name}</div>,
  },
  {
    accessorKey: "remarks",
    header: '备注',
    cell: ({ row }) => <TextDisplay className="text-sm text-muted-foreground max-w-xs truncate">{row.original.remarks}</TextDisplay>,
  },
  {
    id: "actions",
    header: "操作",
    cell: ({ row }) => {
      const data = row.original
      return (
        <div className="flex justify-end">
          <StudentInfoDialog
            studentId={data.student?.id}
          />
          <ActionProductRefund
            studentId={data.student?.id || ''}
            studentProductId={data.studentProduct?.id || ''}
            remainingAmount={data.studentProduct?.remainingBalance || 0}
          />

        </div>
      )
    },
  },
]