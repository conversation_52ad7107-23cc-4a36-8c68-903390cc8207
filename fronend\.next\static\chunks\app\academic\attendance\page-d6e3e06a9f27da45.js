(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1862],{53706:(e,a,s)=>{Promise.resolve().then(s.bind(s,74524))},74524:(e,a,s)=>{"use strict";s.d(a,{default:()=>h});var n=s(95155);s(12115);var d=s(55733),t=s(55028),l=s(91347),r=s(68056);let i=(0,t.default)(()=>Promise.all([s.e(8687),s.e(4201),s.e(8737),s.e(4582),s.e(5620),s.e(3168),s.e(9613),s.e(7945),s.e(81),s.e(7935),s.e(9624),s.e(9110),s.e(2206),s.e(2144)]).then(s.bind(s,62144)),{loadableGenerated:{webpack:()=>[62144]},ssr:!1}),c=(0,t.default)(()=>Promise.all([s.e(8687),s.e(4201),s.e(8737),s.e(4582),s.e(5620),s.e(3168),s.e(9613),s.e(7945),s.e(81),s.e(7935),s.e(9624),s.e(9110),s.e(2206),s.e(8161)]).then(s.bind(s,18161)),{loadableGenerated:{webpack:()=>[18161]},ssr:!1});function h(){var e;let{hasPermission:a}=(0,r.J)()||{hasPermission:()=>!1},s=[{id:"attendance",key:"attendance",label:"考勤记录",content:(0,n.jsx)(l.LQ,{permission:"attendance:record:read",children:(0,n.jsx)(i,{})}),hidden:!a("attendance:record:read")},{id:"classesQuery",key:"classesQuery",label:"上课查询",content:(0,n.jsx)(l.LQ,{permission:"attendance:classes:read",children:(0,n.jsx)(c,{})}),hidden:!a("attendance:classes:read")}].filter(e=>!e.hidden);return 0===s.length?(0,n.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,n.jsx)("p",{className:"text-gray-500",children:"您没有查看考勤管理的权限"})}):(0,n.jsx)("div",{className:"space-y-4 p-4",children:(0,n.jsx)(d.Q,{tabs:s,variant:"underline",defaultTab:(null===(e=s[0])||void 0===e?void 0:e.key)||"attendance"})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,4540,6639,6315,7358],()=>a(53706)),_N_E=e.O()}]);