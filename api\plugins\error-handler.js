import fp from "fastify-plugin";
import { BaseError } from '../errors/index.js';

async function errorPlugin(fastify, options) {
    // 自定义错误处理
    fastify.setErrorHandler(function (error, request, reply) {
        // 如果是自定义错误类型
        if (error instanceof BaseError) {
            const errorResponse = {
                code: error.statusCode,
                message: error.message,
                data: null
            };

            // 开发环境下返回错误堆栈
            if (process.env.NODE_ENV === 'development' && error.stack) {
                errorResponse.stack = error.stack;
            }

            reply.status(error.statusCode).send(errorResponse);
            return;
        }

        // 其他错误
        const statusCode = error.statusCode || 500;
        const errorResponse = {
            code: statusCode,
            message: error.message || 'Internal Server Error',
            data: null
        };

        // 开发环境下返回错误堆栈
        if (process.env.NODE_ENV === 'development' && error.stack) {
            errorResponse.stack = error.stack;
        }

        // 发送错误响应
        reply.status(statusCode).send(errorResponse);
    })

    // 404 处理
    fastify.setNotFoundHandler(function (request, reply) {
        reply.status(404).send({
            code: 404,
            message: 'Resource not found',
            data: null
        });
    })

    // 统一响应格式的装饰器
    fastify.decorateReply('success', function ({message = 'Success', data = null}) {
        this.send({
            code: 200,
            message,
            data
        });
    })
}

export default fp(errorPlugin, {
    name: 'error-handler'
})