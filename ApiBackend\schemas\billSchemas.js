export const getBillsSchema = {
    tags: ['bills'],
    summary: '获取账单列表',
    querystring: {
        type: 'object',
        properties: {
            page: { type: 'number', default: 1 },
            pageSize: { type: 'number', default: 10 },
            startTime: { type: 'string' },
            endTime: { type: 'string' },
            source: { type: 'string' },
            operator: { type: 'string' },
            billType: { type: 'string' },
            status: { type: 'string' },
            paymentMethod: { type: 'string' }
        }
    }
}

export const createBillSchema = {
    tags: ['bills'],
    summary: '创建账单',
    body: {
        type: 'object',
        properties: {
            amount: { type: 'number' },
            paymentMethod: { type: 'string' },
            paymentTime: { type: 'string' },
            operator: { type: 'string' },
            studentId: { type: 'string' },
            productId: { type: 'string' },
            source: { type: 'string' },
            billType: {
                type: 'string',
                enum: ['income', 'expense', 'other']
            },
            remarks: { type: 'string' }
        },
        required: ['amount', 'billType']
    }
}

export const getAnnualDataSchema = {
    querystring: {
        type: 'object',
        properties: {
            type: { type: 'string', enum: ['monthly', 'yearly', 'range'], default: 'yearly' },
            startDate: { type: 'number' },
            endDate: { type: 'number' },
            label: { type: 'string', enum: ['income', 'expense', 'all'], default: 'all' },
            year: { type: 'integer', nullable: true },
            month: { type: 'integer', minimum: 1, maximum: 12, nullable: true }
        },
        additionalProperties: false
    }
} 