'use client';

import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';

export default function ClassDetailLoading() {
  return (
    <div className="p-6 space-y-6">
      {/* 班级信息骨架屏 */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex justify-between items-start">
              <div className="space-y-2">
                <Skeleton className="h-8 w-48" />
                <Skeleton className="h-4 w-64" />
              </div>
              <Skeleton className="h-10 w-24" />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              {Array(6).fill(0).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-6 w-full" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 标签页骨架屏 */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            {/* 标签栏 */}
            <div className="flex space-x-4 border-b">
              {Array(3).fill(0).map((_, i) => (
                <Skeleton key={i} className="h-10 w-24" />
              ))}
            </div>
            
            {/* 标签内容区域 */}
            <div className="space-y-4 mt-4">
              {/* 表格骨架屏 */}
              <div className="border rounded-md">
                {/* 表头 */}
                <div className="flex border-b p-3 bg-muted/30">
                  {Array(5).fill(0).map((_, i) => (
                    <Skeleton key={i} className="h-6 flex-1 mx-2" />
                  ))}
                </div>
                
                {/* 表格内容 */}
                {Array(6).fill(0).map((_, rowIndex) => (
                  <div key={rowIndex} className="flex border-b p-3">
                    {Array(5).fill(0).map((_, colIndex) => (
                      <Skeleton key={colIndex} className="h-6 flex-1 mx-2" />
                    ))}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
