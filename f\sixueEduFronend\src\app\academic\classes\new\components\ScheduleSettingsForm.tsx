"use client"

import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import WeekdaySelector from "./WeekdaySelector"
import type { z } from "zod"
import type { UseFormReturn } from "react-hook-form"
import type formSchema from "../schema/form"

interface ScheduleSettingsFormProps {
  form: UseFormReturn<z.infer<typeof formSchema>>
}

export default function ScheduleSettingsForm({ form }: ScheduleSettingsFormProps) {
  const recurrenceType = form.watch("recurrenceType")
  const endType = form.watch("endType")

  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="recurrenceType"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel className="text-sm font-medium text-slate-700">循环排课</FormLabel>
            <FormControl>
              <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-col space-y-2">
                <FormItem className="flex items-center space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors">
                  <FormControl>
                    <RadioGroupItem value="weekly" className="text-slate-700" />
                  </FormControl>
                  <FormLabel className="font-normal text-slate-700 cursor-pointer w-full text-sm">每周重复</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors">
                  <FormControl>
                    <RadioGroupItem value="daily" className="text-slate-700" />
                  </FormControl>
                  <FormLabel className="font-normal text-slate-700 cursor-pointer w-full text-sm">每天重复</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />

      {recurrenceType === "weekly" && (
        <div className="p-4 border rounded-md border-slate-200">
          <FormField
            control={form.control}
            name="weekdays"
            render={({ field }) => (
              <FormItem className="pt-0">
                <FormLabel className="text-sm text-slate-700 mb-3 block font-medium">选择上课日期</FormLabel>
                <FormControl>
                  <WeekdaySelector value={field.value || []} onChange={field.onChange} />
                </FormControl>
                <FormDescription className="mt-3 text-slate-500 text-xs">
                  请选择每周上课的日期并设置时间
                </FormDescription>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
        </div>
      )}

      {recurrenceType === "daily" && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="daily.startTime"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-slate-700">开始时间</FormLabel>
                <FormControl>
                  <input
                    type="time"
                    placeholder="选择开始时间"
                    className="w-full h-10 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"
                    value={field.value || ""}
                    onChange={(e) => field.onChange(e.target.value)}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="daily.endTime"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-slate-700">结束时间</FormLabel>
                <FormControl>
                  <input
                    type="time"
                    placeholder="选择结束时间"
                    className="w-full h-10 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"
                    value={field.value || ""}
                    onChange={(e) => field.onChange(e.target.value)}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
        </div>
      )}

      <div className="pt-2">
        <FormField
          control={form.control}
          name="endType"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel className="text-sm font-medium text-slate-700">结束方式</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={(res) => {
                    if (res === "number_of_times") {
                      form.setValue("endDate", undefined)
                    }
                    field.onChange(res)
                  }}
                  defaultValue={field.value}
                  className="flex flex-col space-y-2"
                >
                  <FormItem className="flex items-center space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors">
                    <FormControl>
                      <RadioGroupItem value="times" className="text-slate-700" />
                    </FormControl>
                    <FormLabel className="font-normal text-slate-700 cursor-pointer w-full text-sm">按时间</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-2 space-y-0 border border-slate-200 p-3 rounded-md hover:border-slate-300 transition-colors">
                    <FormControl>
                      <RadioGroupItem value="number_of_times" className="text-slate-700" />
                    </FormControl>
                    <FormLabel className="font-normal text-slate-700 cursor-pointer w-full text-sm">按次数</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-1">
        <FormField
          control={form.control}
          name="startDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-slate-700">开始日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <div
                      className={cn(
                        "w-full flex items-center justify-between text-sm font-normal",
                        "border-b border-slate-200 py-2 text-slate-700",
                        "hover:border-slate-300 transition-colors cursor-pointer",
                        !field.value && "text-slate-500",
                      )}
                    >
                      {field.value ? format(field.value, "yyyy年MM月dd日") : <span>选择日期</span>}
                      <CalendarIcon className="h-4 w-4 opacity-50" />
                    </div>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 shadow-sm rounded-md" align="start">
                  <Calendar
                    locale={zhCN}
                    mode="single"
                    selected={field.value}
                    onSelect={(date) => field.onChange(date)}
                    initialFocus
                    className="border-0"
                  />
                </PopoverContent>
              </Popover>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />

        {endType === "times" && (
          <FormField
            control={form.control}
            name="endDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-slate-700">结束日期</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <div
                        className={cn(
                          "w-full flex items-center justify-between text-sm font-normal",
                          "border-b border-slate-200 py-2 text-slate-700",
                          "hover:border-slate-300 transition-colors cursor-pointer",
                          !field.value && "text-slate-500",
                        )}
                      >
                        {field.value ? format(field.value, "yyyy年MM月dd日") : <span>选择日期</span>}
                        <CalendarIcon className="h-4 w-4 opacity-50" />
                      </div>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0 shadow-sm rounded-md" align="start">
                    <Calendar
                      locale={zhCN}
                      mode="single"
                      selected={field.value}
                      onSelect={(date) => field.onChange(date)}
                      initialFocus
                      className="border-0"
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
        )}

        {endType === "number_of_times" && (
          <FormField
            control={form.control}
            name="times"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-slate-700">课程次数</FormLabel>
                <FormControl>
                  <input
                    type="number"
                    placeholder="请输入课程次数"
                    className="w-full h-10 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"
                    value={field.value ?? ""}
                    onChange={(e) => field.onChange(Number.parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
        )}
      </div>
    </div>
  )
}

