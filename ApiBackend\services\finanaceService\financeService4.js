import financeModel from '../../models/financeModel.js';

/**
 * Additional finance service methods
 */
export default {
  /**
   * Get refund records data
   */
  async getRefundRecords({ user, startTime, endTime, page, pageSize, operator, search }) {
    const where = {
      institutionId: user.institutionId,
      ...(startTime && endTime ? { paymentTime: { gte: startTime, lte: endTime } } : {}),
      ...(operator ? { operatorId: operator } : {}),
      ...(search ? {
        student: {
          OR: [
            {
              name: {
                contains: search,
                mode: 'insensitive'
              }
            },
            {
              phone: {
                contains: search,
                mode: 'insensitive'
              }
            }
          ]
        }
      } : {})
    };

    const skip = (page - 1) * pageSize;
    const take = pageSize;

    const [total, list] = await Promise.all([
      financeModel.getRefundRecordsCount(where),
      financeModel.getRefundRecords(where, skip, take)
    ]);
    
    const serializedList = list.map(item => ({
      ...item,
      paymentTime: Number(item.paymentTime),
      amount: Number(item.amount),
    }));
    
    return {
      total,
      page,
      pageSize,
      list: serializedList
    };
  },

  /**
   * Get institution income data
   */
  async getInstitutionIncome({ user, startTime, endTime, checkType }) {
    const where = {
      institutionId: user.institutionId,
      ...(startTime && endTime ? { paymentTime: { gte: startTime, lte: endTime } } : {}),
    };

    const [total, list] = await Promise.all([
      financeModel.getBillCount(where),
      financeModel.getBills(where)
    ]);

    // Process the data based on checkType
    // This is a placeholder for the actual implementation
    return {
      total,
      list
    };
  },

  /**
   * Combine all finance service methods
   */
  combineServices() {
    const service1 = require('./financeService.js').default;
    const service2 = require('./financeService2.js').default;
    const service3 = require('./financeService3.js').default;
    const service4 = require('./financeService4.js').default;
    
    return {
      ...service1,
      ...service2,
      ...service3,
      ...service4
    };
  }
};
