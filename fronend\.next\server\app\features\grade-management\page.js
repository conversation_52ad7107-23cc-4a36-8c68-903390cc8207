(()=>{var e={};e.id=500,e.ids=[500],e.modules={1537:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var t=r(37413);r(61120);var a=r(4536),l=r.n(a),d=r(51465),i=r(61227),x=r(75234),c=r(72845),n=r(26373);let m=(0,n.A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var h=r(83799);let o=(0,n.A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var g=r(41382),p=r(91142);function u(){return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,t.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(l(),{href:"/",className:"flex items-center space-x-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 transition-colors",children:[(0,t.jsx)(d.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"font-medium",children:"返回首页"})]}),(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"成绩管理系统"})]})})}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-emerald-500 to-teal-600 py-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex justify-center mb-6",children:(0,t.jsx)("div",{className:"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center",children:(0,t.jsx)(i.A,{className:"w-10 h-10 text-white"})})}),(0,t.jsx)("h1",{className:"text-4xl font-bold text-white mb-4",children:"成绩管理"}),(0,t.jsx)("p",{className:"text-xl text-emerald-100 max-w-3xl mx-auto",children:"全面的学习成果跟踪与分析，科学评估学员学习进度，助力个性化教学"})]})})}),(0,t.jsx)("div",{className:"py-16 bg-white dark:bg-gray-800",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"核心功能特色"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"科学的成绩管理与分析工具"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 p-6 rounded-xl border border-emerald-200 dark:border-emerald-800",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-emerald-500 rounded-lg flex items-center justify-center mb-4",children:(0,t.jsx)(x.A,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"智能成绩录入"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"支持批量导入、手动录入等多种方式，自动校验数据准确性"})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4",children:(0,t.jsx)(c.A,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"深度数据分析"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"多维度成绩统计分析，生成详细的学习报告和趋势图表"})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 p-6 rounded-xl border border-yellow-200 dark:border-yellow-800",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center mb-4",children:(0,t.jsx)(m,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"排名与评级"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"自动计算班级排名，支持多种评级标准和奖励机制"})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-xl border border-purple-200 dark:border-purple-800",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mb-4",children:(0,t.jsx)(h.A,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"学习进步跟踪"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"跟踪学员学习轨迹，识别进步趋势和薄弱环节"})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 p-6 rounded-xl border border-red-200 dark:border-red-800",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mb-4",children:(0,t.jsx)(o,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"学习目标管理"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"设定个性化学习目标，跟踪目标达成情况"})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 p-6 rounded-xl border border-teal-200 dark:border-teal-800",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-teal-500 rounded-lg flex items-center justify-center mb-4",children:(0,t.jsx)(g.A,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"家长成绩通知"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"自动发送成绩报告给家长，保持家校沟通"})]})]})]})}),(0,t.jsx)("div",{className:"py-16 bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"成绩数据概览"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"实时掌握学员学习成果"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg text-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(i.A,{className:"w-6 h-6 text-emerald-600 dark:text-emerald-400"})}),(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"85.6"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"平均分"})]}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg text-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(h.A,{className:"w-6 h-6 text-blue-600 dark:text-blue-400"})}),(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"92%"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"及格率"})]}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg text-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(m,{className:"w-6 h-6 text-yellow-600 dark:text-yellow-400"})}),(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"68%"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"优秀率"})]}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg text-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(o,{className:"w-6 h-6 text-purple-600 dark:text-purple-400"})}),(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"76%"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"目标达成率"})]})]})]})}),(0,t.jsx)("div",{className:"py-16 bg-white dark:bg-gray-800",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"成绩分析功能"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"多维度的成绩数据可视化"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900 p-8 rounded-xl",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:"成绩趋势分析"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-300",children:"数学"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-emerald-500 h-2 rounded-full",style:{width:"88%"}})}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"88分"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-300",children:"语文"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:"82%"}})}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"82分"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-300",children:"英语"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-purple-500 h-2 rounded-full",style:{width:"90%"}})}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"90分"})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900 p-8 rounded-xl",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:"班级排名分布"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-300",children:"前10%"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-yellow-500 h-2 rounded-full",style:{width:"15%"}})}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"15人"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-300",children:"前50%"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-emerald-500 h-2 rounded-full",style:{width:"60%"}})}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"60人"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-300",children:"后50%"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-orange-500 h-2 rounded-full",style:{width:"25%"}})}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"25人"})]})]})]})]})]})]})}),(0,t.jsx)("div",{className:"py-16 bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"适用场景"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"满足各种教育机构的成绩管理需求"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"培训机构"}),(0,t.jsxs)("ul",{className:"space-y-3 text-gray-600 dark:text-gray-300",children:[(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"阶段性测试成绩管理"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"学习进度跟踪"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"个性化学习报告"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"家长成绩反馈"})]})]})]}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"学校教育"}),(0,t.jsxs)("ul",{className:"space-y-3 text-gray-600 dark:text-gray-300",children:[(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"期中期末成绩管理"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"班级成绩统计分析"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"学科成绩对比"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsx)("span",{children:"升学数据分析"})]})]})]})]})]})}),(0,t.jsx)("div",{className:"py-16 bg-gradient-to-r from-emerald-500 to-teal-600",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"开始使用成绩管理系统"}),(0,t.jsx)("p",{className:"text-xl text-emerald-100 mb-8",children:"科学管理学员成绩，助力个性化教学，提升教育质量"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)(l(),{href:"/dashboard",className:"inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-emerald-600 bg-white hover:bg-gray-50 transition-colors duration-200",children:"立即体验"}),(0,t.jsx)(l(),{href:"/features",className:"inline-flex items-center justify-center px-8 py-3 border border-white text-base font-medium rounded-md text-white hover:bg-white/10 transition-colors duration-200",children:"查看更多功能"})]})]})}),(0,t.jsx)("footer",{className:"bg-gray-800 dark:bg-gray-900",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"text-center text-gray-400",children:(0,t.jsx)("p",{children:"\xa9 2024 CardMees 教育管理平台. 保留所有权利。"})})})})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,s,r)=>{let{createProxy:t}=r(39844);e.exports=t("F:\\trae\\cardmees\\fronend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17720:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26373:(e,s,r)=>{"use strict";r.d(s,{A:()=>x});var t=r(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=(...e)=>e.filter((e,s,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===s).join(" ").trim();var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,t.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:i="",children:x,iconNode:c,...n},m)=>(0,t.createElement)("svg",{ref:m,...d,width:s,height:s,stroke:e,strokeWidth:a?24*Number(r)/Number(s):r,className:l("lucide",i),...n},[...c.map(([e,s])=>(0,t.createElement)(e,s)),...Array.isArray(x)?x:[x]])),x=(e,s)=>{let r=(0,t.forwardRef)(({className:r,...d},x)=>(0,t.createElement)(i,{ref:x,iconNode:s,className:l(`lucide-${a(e)}`,r),...d}));return r.displayName=`${e}`,r}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41382:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},51465:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},52568:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,85814,23))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61227:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72845:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},74075:e=>{"use strict";e.exports=require("zlib")},75234:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83799:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},83997:e=>{"use strict";e.exports=require("tty")},84629:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>d.a,__next_app__:()=>m,pages:()=>n,routeModule:()=>h,tree:()=>c});var t=r(65239),a=r(48088),l=r(88170),d=r.n(l),i=r(30893),x={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(x[e]=()=>i[e]);r.d(s,x);let c={children:["",{children:["features",{children:["grade-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1537)),"F:\\trae\\cardmees\\fronend\\src\\app\\features\\grade-management\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,n=["F:\\trae\\cardmees\\fronend\\src\\app\\features\\grade-management\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/features/grade-management/page",pathname:"/features/grade-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91142:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,7392,5814,3019],()=>r(84629));module.exports=t})();