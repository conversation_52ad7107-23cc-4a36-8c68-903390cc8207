import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Remove permissions from a role
 * This script removes permissions from a specific role
 * 
 * Usage:
 * node scripts/remove-role-permissions.js <roleCode> <permissionCode1,permissionCode2,...>
 * 
 * Example:
 * node scripts/remove-role-permissions.js teacher course:view,class:view,student:view
 * 
 * To remove all permissions from a role:
 * node scripts/remove-role-permissions.js teacher all
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.error('Usage: node scripts/remove-role-permissions.js <roleCode> <permissionCode1,permissionCode2,...>');
    process.exit(1);
  }
  
  const roleCode = args[0];
  const permissionInput = args[1];
  
  console.log(`Removing permissions from role: ${roleCode}`);
  
  // Get the role
  const role = await prisma.role.findUnique({
    where: { code: roleCode }
  });
  
  if (!role) {
    console.error(`Role with code ${roleCode} not found`);
    process.exit(1);
  }
  
  // Handle removing all permissions
  if (permissionInput === 'all') {
    const deletedCount = await prisma.rolePermission.deleteMany({
      where: { roleId: role.id }
    });
    
    console.log(`Removed all permissions (${deletedCount.count}) from role ${roleCode}`);
    return;
  }
  
  // Handle removing specific permissions
  const permissionCodes = permissionInput.split(',');
  console.log(`Permissions to remove: ${permissionCodes.join(', ')}`);
  
  // Get the permissions
  const permissions = await prisma.permission.findMany({
    where: {
      code: {
        in: permissionCodes
      }
    }
  });
  
  const foundPermissionCodes = permissions.map(p => p.code);
  const missingPermissionCodes = permissionCodes.filter(code => !foundPermissionCodes.includes(code));
  
  if (missingPermissionCodes.length > 0) {
    console.warn(`Warning: The following permissions were not found: ${missingPermissionCodes.join(', ')}`);
  }
  
  console.log(`Found ${permissions.length} permissions to remove`);
  
  // Remove role permissions
  const deletedCount = await prisma.rolePermission.deleteMany({
    where: {
      roleId: role.id,
      permissionId: {
        in: permissions.map(p => p.id)
      }
    }
  });
  
  console.log(`Successfully removed ${deletedCount.count} permissions from role ${roleCode}`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
