"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9184],{59184:(e,a,t)=>{t.r(a),t.d(a,{default:()=>N});var s=t(95155),r=t(12115),l=t(30285),d=t(9110),n=t(26126),i=t(46102),c=t(90010),o=t(34835),u=t(35695),m=t(48436),x=t(27893);let h=function(e){let{student:a}=e,{classesId:t}=(0,u.useParams)(),[r]=(0,x.Xd)();return(0,s.jsxs)(c.<PERSON>,{children:[(0,s.jsx)(i.Bc,{delayDuration:300,children:(0,s.jsxs)(i.m_,{children:[(0,s.jsx)(c.tv,{asChild:!0,children:(0,s.jsx)(i.k$,{asChild:!0,children:(0,s.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})})}),(0,s.jsx)(i.ZI,{side:"top",className:"font-medium text-xs px-3 py-1.5 bg-background border shadow-sm",children:(0,s.jsx)("p",{children:"退班"})})]})}),(0,s.jsxs)(c.EO,{children:[(0,s.jsxs)(c.wd,{children:[(0,s.jsx)(c.r7,{children:"确认退出班级"}),(0,s.jsxs)(c.$v,{children:["确定要将学生 ",a.student.name," 退出此班级吗？此操作不可撤销。"]})]}),(0,s.jsxs)(c.ck,{children:[(0,s.jsx)(c.Zr,{children:"取消"}),(0,s.jsx)(c.Rx,{onClick:()=>{r({classId:t,studentId:a.student.id}).then(e=>{m.l.success("学员退出班级成功.")})},children:"确认退出"})]})]})]})};var f=t(61809);let p=[{accessorKey:"name",header:"姓名",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"font-medium",children:a.original.student.name})}},{accessorKey:"phone",header:"手机号",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"capitalize",children:a.original.student.phone})}},{accessorKey:"studentType",header:"学生类型",cell:e=>{let{row:a}=e,t=a.original.student.type;return"formal"===t?(0,s.jsx)(n.E,{className:"capitalize",children:"正式"}):"graduated"===t?(0,s.jsx)(n.E,{variant:"destructive",className:"capitalize",children:"毕业"}):void 0}},{accessorKey:"joinDate",header:"进班日期",cell:e=>{let{row:a}=e;console.log(a.original);let t=new Date(a.getValue("joinDate"));return(0,s.jsx)("div",{children:t.toLocaleDateString("zh-CN")})}},{accessorKey:"operator",header:"操作人",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"capitalize",children:a.original.operator.name})}},{accessorKey:"operatorTime",header:"操作时间",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{children:(0,f.Y)(a.getValue("operatorTime"),"yyyy-MM-dd HH:mm:ss")})}},{accessorKey:"type",header:"类型",cell:e=>{let{row:a}=e,t=a.getValue("type");return"in"===t?(0,s.jsx)(n.E,{variant:"default",className:"capitalize",children:"在班"}):"out"===t?(0,s.jsx)(n.E,{variant:"destructive",className:"capitalize",children:"退班"}):void 0}},{id:"actions",header:"操作",cell:e=>{let{row:a}=e,t=a.original;return(0,s.jsx)("div",{className:"flex space-x-2",children:(0,s.jsx)(h,{student:t})})}}];var j=t(12318);let y=(0,t(55028).default)(()=>t.e(8711).then(t.bind(t,98711)),{loadableGenerated:{webpack:()=>[98711]},ssr:!1}),N=e=>{let{students:a}=e,t=(0,u.useParams)().classesId,n=a||[],[i,c]=r.useState(!1),[o]=(0,x.E4)(),h=async e=>{try{return await o({classId:t,studentIds:[e]}),m.l.success("学员添加成功."),!0}catch(e){return m.l.error("学员添加失败!"),!1}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"w-full space-y-4",children:[(0,s.jsxs)(l.$,{variant:"outline",onClick:()=>c(!0),className:"hover:bg-muted/50 transition-colors duration-200 border-border/50 hover:border-border flex items-center px-4 py-2 rounded-lg",children:[(0,s.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"新增学员"]}),(0,s.jsx)(d.b,{columns:p,data:n})]}),i&&(0,s.jsx)(y,{open:i,onOpenChange:c,students:a,handleSave:h})]})}},61809:(e,a,t)=>{t.d(a,{Y:()=>d});var s=t(44861),r=t(73168),l=t(24122);let d=function(e){let a,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"yyyy-MM-dd",d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(null==e)return d;try{if("string"==typeof e)a=new Date(e);else if("number"==typeof e)a=new Date(e);else{if(!(e instanceof Date))return d;a=e}if(!(0,s.f)(a))return d;return(0,r.GP)(a,t,{locale:l.g})}catch(e){return console.error("Date formatting error:",e),d}}},90010:(e,a,t)=>{t.d(a,{$v:()=>p,EO:()=>m,Lt:()=>i,Rx:()=>j,Zr:()=>y,ck:()=>h,r7:()=>f,tv:()=>c,wd:()=>x});var s=t(95155),r=t(12115),l=t(17649),d=t(59434),n=t(30285);let i=l.bL,c=l.l9,o=l.ZL,u=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.hJ,{className:(0,d.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r,ref:a})});u.displayName=l.hJ.displayName;let m=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsxs)(o,{children:[(0,s.jsx)(u,{}),(0,s.jsx)(l.UC,{ref:a,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...r})]})});m.displayName=l.UC.displayName;let x=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...t})};x.displayName="AlertDialogHeader";let h=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,d.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};h.displayName="AlertDialogFooter";let f=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.hE,{ref:a,className:(0,d.cn)("text-lg font-semibold",t),...r})});f.displayName=l.hE.displayName;let p=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.VY,{ref:a,className:(0,d.cn)("text-sm text-muted-foreground",t),...r})});p.displayName=l.VY.displayName;let j=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.rc,{ref:a,className:(0,d.cn)((0,n.r)(),t),...r})});j.displayName=l.rc.displayName;let y=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.ZD,{ref:a,className:(0,d.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",t),...r})});y.displayName=l.ZD.displayName}}]);