import { Switch } from '@/components/ui/switch'
import React, { useState } from 'react'
import { toast } from 'sonner' // 假设项目使用sonner进行通知

import useCourses from '@/hooks/useCourses'

interface ActionSwitchStatusCoursesProps {
  coursesId: string
  status: string
}

function ActionSwitchStatusCourses({
  coursesId,
  status
}: ActionSwitchStatusCoursesProps) {
  const { updateCourse } = useCourses()
  const [isLoading, setIsLoading] = useState(false)
  const [currentStatus, setCurrentStatus] = useState(status === 'active')

  const handleStatusChange = async (checked: boolean) => {
    try {
      setIsLoading(true)
      const newStatus = checked ? 'active' : 'inactive'
      await updateCourse(coursesId, { status: newStatus })
      setCurrentStatus(checked)
      toast.success(`课程状态已更新为${checked ? '激活' : '禁用'}`)
    } catch (error) {
      toast.error('更新课程状态失败')
      setCurrentStatus(!checked) // 恢复原始状态
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Switch
      key={coursesId}
      checked={currentStatus}
      disabled={isLoading}
      onCheckedChange={handleStatusChange}
      className="transition-colors disabled:opacity-50"
    />
  )
}

export default ActionSwitchStatusCourses