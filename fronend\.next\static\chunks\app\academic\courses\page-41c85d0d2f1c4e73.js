(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9129],{1209:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var t=a(95155),r=a(12115),l=a(54165),n=a(30285),i=a(62523),c=a(85057),d=a(80333),o=a(30356),u=a(88539),m=a(85339),x=a(47863),p=a(66474),h=a(59434),f=a(55594),g=a(78504),v=a(48436);let j=f.z.object({name:f.z.string().min(1,"课程名称不能为空"),duration:f.z.number().min(1,"课时长度不能为空"),type:f.z.enum(["group","one"])}),b=[{value:"group",label:"班课"},{value:"one",label:"一对一"}];function N(e){let{visible:s,onOk:a,onCancel:N,initialValues:y}=e,[w,C]=(0,r.useState)({type:"group",name:"",duration:0,teacher:"",targetAudience:"",isDirectSale:!1,price:0,deductionPerClass:0,isDeductOnAttendance:!0,isDeductOnLeave:!1,isDeductOnAbsence:!1,picture:"",isShow:!1,description:""}),[k]=(0,g.d)(),[A,S]=(0,r.useState)(!1),[D,z]=(0,r.useState)({});(0,r.useEffect)(()=>{y&&C(e=>({...e,...y}))},[y,s]);let P=(e,s)=>{C(a=>({...a,[e]:s})),D[e]&&z(s=>{let a={...s};return delete a[e],a})},F=()=>{try{return j.parse(w),z({}),!0}catch(e){if(e instanceof f.z.ZodError){let s={};e.errors.forEach(e=>{s[e.path[0]]=e.message}),z(s)}return!1}},J=async e=>{var s,a;let t=null===(a=e.target)||void 0===a?void 0:null===(s=a.files)||void 0===s?void 0:s[0];if(t){console.log(t,e.target.files,"file");let s=new FormData;s.append("file",t);try{let e=await k(s);(null==e?void 0:e.data)&&P("picture",(null==e?void 0:e.data.data)||"")}catch(e){v.l.error("上传图片失败",(null==e?void 0:e.message)||"上传图片失败")}}};return(0,t.jsx)(l.lG,{open:s,onOpenChange:()=>N(),children:(0,t.jsxs)(l.Cf,{className:"sm:max-w-[800px] p-4 max-h-[90vh] overflow-y-auto scrollbar-thin",children:[(0,t.jsxs)(l.L3,{className:"text-xl font-semibold",children:[y?"编辑":"新增","课程"]}),(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),F()&&a(w)},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid gap-6",children:[(0,t.jsxs)("div",{className:"grid sm:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-1 h-5 bg-primary rounded-full"}),(0,t.jsx)(c.J,{className:"text-base font-medium",children:"基本信息"})]}),(0,t.jsxs)("div",{className:"space-y-4 bg-muted/10 p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsxs)(c.J,{children:["课程类型",(0,t.jsx)("span",{className:"text-destructive",children:"*"})]}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:"(创建后不可修改)"})]}),(0,t.jsx)(o.z,{value:w.type,disabled:!!y,onValueChange:e=>P("type",e),className:"grid grid-cols-2 gap-2",children:b.map(e=>(0,t.jsxs)("div",{className:(0,h.cn)("flex items-center space-x-2 rounded-lg border p-3 cursor-pointer hover:bg-muted/50 transition-colors",w.type===e.value&&"border-primary bg-primary/5"),children:[(0,t.jsx)(o.C,{value:e.value,id:e.value}),(0,t.jsx)(c.J,{htmlFor:e.value,className:"cursor-pointer w-full",children:e.label})]},e.value))})]}),(0,t.jsxs)("div",{className:"space-y-1.5",children:[(0,t.jsxs)(c.J,{htmlFor:"name",children:["课程名称",(0,t.jsx)("span",{className:"text-destructive",children:"*"})]}),(0,t.jsx)(i.p,{id:"name",placeholder:"请输入课程名称",value:w.name,onChange:e=>P("name",e.target.value),className:(0,h.cn)(D.name&&"border-destructive")}),D.name&&(0,t.jsxs)("div",{className:"flex items-center text-xs text-destructive gap-1 mt-1",children:[(0,t.jsx)(m.A,{className:"h-3 w-3"}),D.name]})]}),(0,t.jsxs)("div",{className:"space-y-1.5",children:[(0,t.jsxs)(c.J,{htmlFor:"duration",children:["单课时长",(0,t.jsx)("span",{className:"text-destructive",children:"*"})]}),(0,t.jsx)(i.p,{id:"duration",placeholder:"请输入时长（分钟）",type:"number",value:w.duration,onChange:e=>P("duration",Number(e.target.value)),className:(0,h.cn)(D.duration&&"border-destructive")}),D.duration&&(0,t.jsxs)("div",{className:"flex items-center text-xs text-destructive gap-1 mt-1",children:[(0,t.jsx)(m.A,{className:"h-3 w-3"}),D.duration]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-1 h-5 bg-primary rounded-full"}),(0,t.jsx)(c.J,{className:"text-base font-medium",children:"教学信息"})]}),(0,t.jsx)("div",{className:"space-y-4 bg-muted/10 p-4 rounded-lg",children:(0,t.jsxs)("div",{className:"space-y-1.5",children:[(0,t.jsx)(c.J,{htmlFor:"targetAudience",children:"目标人群"}),(0,t.jsx)(i.p,{id:"targetAudience",placeholder:"请输入目标人群",value:w.targetAudience,onChange:e=>P("targetAudience",e.target.value)})]})})]})]}),(0,t.jsxs)("div",{className:"grid sm:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-1 h-5 bg-primary rounded-full"}),(0,t.jsx)(c.J,{className:"text-base font-medium",children:"销售设置"})]}),(0,t.jsxs)("div",{className:"space-y-4 bg-muted/10 p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 rounded-lg",children:[(0,t.jsx)(c.J,{htmlFor:"isDirectSale",className:"cursor-pointer",children:"是否直接售卖"}),(0,t.jsx)(d.d,{id:"isDirectSale",checked:w.isDirectSale,onCheckedChange:e=>P("isDirectSale",e)})]}),w.isDirectSale&&(0,t.jsxs)("div",{className:"space-y-1.5",children:[(0,t.jsx)(c.J,{htmlFor:"price",children:"课程单价"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(i.p,{id:"price",placeholder:"请输入课程单价",type:"number",value:w.price||"",onChange:e=>P("price",Number(e.target.value)),className:"pl-6"}),(0,t.jsx)("span",{className:"absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground",children:"\xa5"})]})]}),(0,t.jsxs)("div",{className:"space-y-1.5",children:[(0,t.jsx)(c.J,{htmlFor:"deductionPerClass",children:"每次扣除课时数"}),(0,t.jsx)(i.p,{id:"deductionPerClass",placeholder:"请输入扣除课时数",type:"number",value:w.deductionPerClass,onChange:e=>P("deductionPerClass",Number(e.target.value))})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-1 h-5 bg-primary rounded-full"}),(0,t.jsx)(c.J,{className:"text-base font-medium",children:"扣课规则"})]}),(0,t.jsxs)("div",{className:"space-y-3 bg-muted/10 p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 rounded-lg hover:bg-muted/20 transition-colors",children:[(0,t.jsx)(c.J,{htmlFor:"isDeductOnAttendance",className:"cursor-pointer",children:"到课扣课"}),(0,t.jsx)(d.d,{id:"isDeductOnAttendance",checked:w.isDeductOnAttendance,onCheckedChange:e=>P("isDeductOnAttendance",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 rounded-lg hover:bg-muted/20 transition-colors",children:[(0,t.jsx)(c.J,{htmlFor:"isDeductOnLeave",className:"cursor-pointer",children:"请假扣课"}),(0,t.jsx)(d.d,{id:"isDeductOnLeave",checked:w.isDeductOnLeave,onCheckedChange:e=>P("isDeductOnLeave",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 rounded-lg hover:bg-muted/20 transition-colors",children:[(0,t.jsx)(c.J,{htmlFor:"isDeductOnAbsence",className:"cursor-pointer",children:"缺勤扣课"}),(0,t.jsx)(d.d,{id:"isDeductOnAbsence",checked:w.isDeductOnAbsence,onCheckedChange:e=>P("isDeductOnAbsence",e)})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-1 h-5 bg-primary rounded-full"}),(0,t.jsx)(c.J,{className:"text-base font-medium",children:"课程详情"})]}),(0,t.jsx)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>S(!A),className:"flex items-center gap-1",children:A?(0,t.jsxs)(t.Fragment,{children:["收起详情",(0,t.jsx)(x.A,{className:"h-4 w-4"})]}):(0,t.jsxs)(t.Fragment,{children:["展开详情",(0,t.jsx)(p.A,{className:"h-4 w-4"})]})})]}),A&&(0,t.jsxs)("div",{className:"space-y-4 bg-muted/10 p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"picture",children:"课程封面图片"}),(0,t.jsx)(i.p,{id:"picture",type:"file",accept:"image/*",onChange:J,className:"cursor-pointer"}),w.picture&&(0,t.jsx)("div",{className:"mt-2 p-1 border rounded-lg inline-block",children:(0,t.jsx)("img",{src:w.picture,alt:"课程封面",className:"max-w-[200px] max-h-[150px] rounded"})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 rounded-lg hover:bg-muted/20 transition-colors",children:[(0,t.jsx)(c.J,{htmlFor:"isShow",className:"cursor-pointer",children:"展示到官网"}),(0,t.jsx)(d.d,{id:"isShow",checked:w.isShow,onCheckedChange:e=>P("isShow",e)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"description",children:"课程详情介绍"}),(0,t.jsx)(u.T,{id:"description",placeholder:"请输入课程详情介绍",value:w.description,onChange:e=>P("description",e.target.value),className:"min-h-[150px] resize-y"})]})]})]})]}),(0,t.jsxs)(l.Es,{className:"gap-2 pt-4 border-t mt-6",children:[(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:N,children:"取消"}),(0,t.jsx)(n.$,{type:"submit",className:"min-w-[100px]",children:"保存"})]})]})]})})}},14636:(e,s,a)=>{"use strict";a.d(s,{AM:()=>i,Wv:()=>c,hl:()=>d});var t=a(95155),r=a(12115),l=a(20547),n=a(59434);let i=l.bL,c=l.l9,d=r.forwardRef((e,s)=>{let{className:a,align:r="center",sideOffset:i=4,...c}=e;return(0,t.jsx)(l.ZL,{children:(0,t.jsx)(l.UC,{ref:s,align:r,sideOffset:i,className:(0,n.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...c})})});d.displayName=l.UC.displayName},26126:(e,s,a)=>{"use strict";a.d(s,{E:()=>i});var t=a(95155);a(12115);var r=a(74466),l=a(59434);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{className:(0,l.cn)(n({variant:a}),s),...r})}},28954:(e,s,a)=>{Promise.resolve().then(a.bind(a,54364))},30285:(e,s,a)=>{"use strict";a.d(s,{$:()=>d,r:()=>c});var t=a(95155),r=a(12115),l=a(99708),n=a(74466),i=a(59434);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,s)=>{let{className:a,variant:r,size:n,asChild:d=!1,...o}=e,u=d?l.DX:"button";return(0,t.jsx)(u,{className:(0,i.cn)(c({variant:r,size:n,className:a})),ref:s,...o})});d.displayName="Button"},30356:(e,s,a)=>{"use strict";a.d(s,{C:()=>d,z:()=>c});var t=a(95155),r=a(12115),l=a(54059),n=a(9428),i=a(59434);let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.bL,{className:(0,i.cn)("grid gap-2",a),...r,ref:s})});c.displayName=l.bL.displayName;let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.q7,{ref:s,className:(0,i.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),...r,children:(0,t.jsx)(l.C1,{className:"flex items-center justify-center",children:(0,t.jsx)(n.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});d.displayName=l.q7.displayName},48432:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>v});var t=a(95155),r=a(12115),l=a(42355),n=a(13052),i=a(5623),c=a(59434),d=a(30285);let o=e=>{let{className:s,...a}=e;return(0,t.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,c.cn)("mx-auto flex w-full justify-center",s),...a})};o.displayName="Pagination";let u=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("ul",{ref:s,className:(0,c.cn)("flex flex-row items-center gap-1",a),...r})});u.displayName="PaginationContent";let m=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("li",{ref:s,className:(0,c.cn)("",a),...r})});m.displayName="PaginationItem";let x=e=>{let{className:s,isActive:a,size:r="icon",...l}=e;return(0,t.jsx)("a",{"aria-current":a?"page":void 0,className:(0,c.cn)((0,d.r)({variant:a?"outline":"ghost",size:r}),s),...l})};x.displayName="PaginationLink";let p=e=>{let{className:s,...a}=e;return(0,t.jsxs)(x,{"aria-label":"Go to previous page",size:"default",className:(0,c.cn)("gap-1 pl-2.5",s),...a,children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"上一页"})]})};p.displayName="PaginationPrevious";let h=e=>{let{className:s,...a}=e;return(0,t.jsxs)(x,{"aria-label":"Go to next page",size:"default",className:(0,c.cn)("gap-1 pr-2.5",s),...a,children:[(0,t.jsx)("span",{children:"下一页"}),(0,t.jsx)(n.A,{className:"h-4 w-4"})]})};h.displayName="PaginationNext";let f=e=>{let{className:s,...a}=e;return(0,t.jsxs)("span",{"aria-hidden":!0,className:(0,c.cn)("flex h-9 w-9 items-center justify-center",s),...a,children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"更多页"})]})};f.displayName="PaginationEllipsis";var g=a(59409);function v(e){let{currentPage:s,pageSize:a,totalItems:r,onPageChange:i,onPageSizeChange:c}=e,d=Math.ceil(r/a),v=(()=>{let e=[];if(d<=5){for(let s=1;s<=d;s++)e.push(s);return e}e.push(1);let a=Math.max(2,s-1),t=Math.min(s+1,d-1);2===a&&(t=Math.min(a+2,d-1)),t===d-1&&(a=Math.max(t-2,2)),a>2&&e.push("ellipsis-start");for(let s=a;s<=t;s++)e.push(s);return t<d-1&&e.push("ellipsis-end"),d>1&&e.push(d),e})(),j=0===r?0:(s-1)*a+1,b=Math.min(s*a,r);return(0,t.jsxs)("div",{className:"flex flex-col gap-4 px-2 py-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,t.jsx)("span",{className:"text-muted-foreground/80",children:"显示"}),(0,t.jsxs)(g.l6,{value:a.toString(),onValueChange:e=>{c(Number(e))},children:[(0,t.jsx)(g.bq,{className:"w-[4.5rem] h-7 text-xs border-border/60 hover:bg-accent/30 transition-colors",children:(0,t.jsx)(g.yv,{})}),(0,t.jsx)(g.gC,{children:[10,20,30,50].map(e=>(0,t.jsx)(g.eb,{value:e.toString(),className:"text-xs",children:e},e))})]}),(0,t.jsx)("span",{className:"text-muted-foreground/80",children:"条"}),(0,t.jsx)("span",{className:"text-muted-foreground/40 mx-1.5",children:"|"}),r>0?(0,t.jsxs)("span",{className:"text-muted-foreground/80",children:[j,"-",b," / ",r," 条记录"]}):(0,t.jsx)("span",{className:"text-muted-foreground/80",children:"0 条记录"})]}),(0,t.jsx)(o,{children:(0,t.jsxs)(u,{className:"gap-1",children:[(0,t.jsx)(m,{children:(0,t.jsx)(p,{onClick:()=>i(Math.max(1,s-1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(1===s?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,t.jsx)(l.A,{className:"h-4 w-4 mr-1"})})}),v.map((e,a)=>"ellipsis-start"===e||"ellipsis-end"===e?(0,t.jsx)(m,{children:(0,t.jsx)(f,{className:"h-8 w-8 flex items-center justify-center text-xs"})},"ellipsis-".concat(a)):(0,t.jsx)(m,{children:(0,t.jsx)(x,{onClick:()=>i(e),isActive:s===e,className:"h-8 w-8 text-xs font-medium rounded-md transition-colors data-[active]:bg-primary data-[active]:text-primary-foreground hover:bg-accent/50 hover:text-accent-foreground/90","aria-label":"前往第 ".concat(e," 页"),children:e})},e)),(0,t.jsx)(m,{children:(0,t.jsx)(h,{onClick:()=>i(Math.min(d,s+1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(s===d||0===d?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,t.jsx)(n.A,{className:"h-4 w-4 ml-1"})})})]})})]})}},54165:(e,s,a)=>{"use strict";a.d(s,{Cf:()=>x,Es:()=>h,HM:()=>u,L3:()=>f,c7:()=>p,lG:()=>c,rr:()=>g,zM:()=>d});var t=a(95155),r=a(12115),l=a(15452),n=a(54416),i=a(59434);let c=l.bL,d=l.l9,o=l.ZL,u=l.bm,m=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.hJ,{ref:s,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r})});m.displayName=l.hJ.displayName;let x=r.forwardRef((e,s)=>{let{className:a,children:r,...c}=e;return(0,t.jsxs)(o,{children:[(0,t.jsx)(m,{}),(0,t.jsxs)(l.UC,{ref:s,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[r,(0,t.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});x.displayName=l.UC.displayName;let p=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...a})};p.displayName="DialogHeader";let h=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a})};h.displayName="DialogFooter";let f=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.hE,{ref:s,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});f.displayName=l.hE.displayName;let g=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.VY,{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",a),...r})});g.displayName=l.VY.displayName},54364:(e,s,a)=>{"use strict";a.d(s,{default:()=>D});var t=a(95155),r=a(12115),l=a(30285),n=a(62523),i=a(84616),c=a(91788),d=a(59409),o=a(48432),u=a(55028),m=a(74651),x=a(9110),p=a(26126),h=a(73069),f=a(89917),g=a(1209),v=a(48436),j=a(57001),b=a(91347);let N=function(e){let{courses:s}=e,[a]=(0,m.VD)(),[l,n]=(0,r.useState)(!1),i=async e=>{delete e.ProductCourse;try{await a({courseId:s.id,course:e}),v.l.success("编辑课程成功."),n(!1)}catch(e){v.l.error((null==e?void 0:e.message)||"编辑课程失败!")}};return(0,t.jsxs)(b.LQ,{permission:"course:delete",children:[(0,t.jsx)(j.p,{icon:f.A,tooltipText:"编辑课程",onClick:()=>n(!0)}),l&&(0,t.jsx)(g.default,{visible:l,onOk:i,onCancel:()=>n(!1),initialValues:s})]})};var y=a(62525);let w=function(e){let{coursesId:s}=e,[a]=(0,m.v5)();return(0,t.jsx)(b.LQ,{permission:"course:delete",children:(0,t.jsx)(j.p,{icon:y.A,tooltipText:"删除课程",onClick:()=>a(s)})})};var C=a(80333);let k=function(e){let{coursesId:s,status:a}=e,[l,{isLoading:n}]=(0,m.VD)(),[i,c]=(0,r.useState)("active"===a),d=async e=>{try{await l({courseId:s,course:{status:e?"active":"inactive"}}),c(e),v.l.success("课程状态已更新为".concat(e?"激活":"禁用"))}catch(s){v.l.error("更新课程状态失败"),c(!e)}};return(0,t.jsx)(C.d,{checked:i,disabled:n,onCheckedChange:d,className:"transition-colors disabled:opacity-50"},s)},A=[{accessorKey:"name",header:"课程名称",cell:e=>{let{row:s}=e;return(0,t.jsx)("div",{className:"font-medium text-slate-800",children:s.getValue("name")})}},{accessorKey:"type",header:"课程类型",cell:e=>{let{row:s}=e;return(0,t.jsx)(p.E,{variant:"group"===s.getValue("type")?"outline":"secondary",className:"font-normal px-3 py-1 rounded-full text-xs",children:"group"===s.getValue("type")?"班课":"一对一"})}},{accessorKey:"isDirectSale",header:"可售卖",cell:e=>{let{row:s}=e;return(0,t.jsx)(p.E,{variant:s.getValue("isDirectSale")?"default":"secondary",className:"font-normal px-3 py-1 rounded-full text-xs",children:s.getValue("isDirectSale")?"是":"否"})}},{accessorKey:"packages",header:"已绑定套餐",cell:e=>{let{row:s}=e,a=(0,r.useMemo)(()=>s.original.ProductCourse.map(e=>{var s;return(null==e?void 0:null===(s=e.product)||void 0===s?void 0:s.name)||""}).filter(Boolean).join("、"),[s.original.ProductCourse]);return(0,t.jsx)(h.c,{maxDisplayLength:4,className:"text-sm text-slate-600",children:a||"暂无绑定套餐"})}},{accessorKey:"deductionPerClass",header:"消课系数",cell:e=>{let{row:s}=e;return(0,t.jsx)("div",{className:"text-sm text-slate-600 text-center",children:s.getValue("deductionPerClass")})}},{accessorKey:"duration",header:"单课时长(分钟)",cell:e=>{let{row:s}=e;return(0,t.jsx)("div",{className:"text-sm text-slate-600 text-center",children:s.getValue("duration")})}},{accessorKey:"status",header:"状态",cell:e=>{let{row:s}=e;return(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsx)(k,{status:s.getValue("status"),coursesId:s.original.id})})}},{id:"actions",header:"操作",cell:e=>{let{row:s}=e,a=s.original;return(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N,{courses:a}),(0,t.jsx)(w,{coursesId:a.id})]})}}],S=(0,u.default)(()=>Promise.resolve().then(a.bind(a,1209)),{loadableGenerated:{webpack:()=>[1209]},ssr:!1});function D(){let[e,s]=(0,r.useState)(!1),[a,u]=(0,r.useState)(null),[p,h]=(0,r.useState)(""),[f,g]=(0,r.useState)(""),[j,N]=(0,r.useState)({currentPage:1,pageSize:10}),[y]=(0,m.AA)(),w=(0,r.useMemo)(()=>({search:p,page:j.currentPage,pageSize:j.pageSize,type:"all"===f?"":f}),[p,j,f]),{data:C,isLoading:k}=(0,m.p5)(w),D=async e=>{console.log(e,"保存课程数据");try{await y(e),s(!1),v.l.success("保存课程成功.")}catch(e){v.l.error((null==e?void 0:e.message)||"保存课程失败!")}};return(0,t.jsxs)("div",{className:"p-4 space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-4 border-b border-gray-100",children:[(0,t.jsx)("h2",{className:"text-xl font-medium text-gray-800",children:"课程管理"}),(0,t.jsx)(b.LQ,{permission:"course:create",children:(0,t.jsxs)(l.$,{onClick:()=>{u(null),s(!0)},className:"h-10 gap-1.5 ",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),"新增课程"]})})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:[(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-3 w-full sm:w-auto",children:[(0,t.jsx)("div",{className:"flex items-center gap-2 flex-1 sm:flex-none",children:(0,t.jsx)(n.p,{placeholder:"搜索课程名称",value:p,onChange:e=>h(e.target.value),className:"h-10 min-w-[180px] sm:max-w-[220px]"})}),(0,t.jsx)("div",{className:"flex items-center gap-2 flex-1 sm:flex-none",children:(0,t.jsxs)(d.l6,{value:f,onValueChange:g,children:[(0,t.jsx)(d.bq,{className:"h-10 min-w-[120px] sm:max-w-[180px]",children:(0,t.jsx)(d.yv,{placeholder:"课程类型"})}),(0,t.jsxs)(d.gC,{children:[(0,t.jsx)(d.eb,{value:"all",children:"全部类型"}),(0,t.jsx)(d.eb,{value:"group",children:"团体课"}),(0,t.jsx)(d.eb,{value:"one",children:"一对一"})]})]})})]}),(0,t.jsx)("div",{className:"flex items-center gap-3 self-end sm:self-auto",children:(0,t.jsxs)(l.$,{variant:"outline",size:"icon",className:"h-10 w-10 rounded-md border-gray-200 shadow-sm transition-all hover:border-gray-300 hover:bg-gray-50",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("span",{className:"sr-only",children:"导出"})]})})]}),(0,t.jsx)(x.b,{columns:A,data:(null==C?void 0:C.list)||[],pagination:!1,loading:k},"courseDataTable"),(0,t.jsx)(o.default,{currentPage:(null==C?void 0:C.page)||1,totalItems:(null==C?void 0:C.total)||0,pageSize:(null==C?void 0:C.pageSize)||10,onPageChange:e=>{N(s=>({...s,currentPage:e}))},onPageSizeChange:e=>{N(s=>({...s,pageSize:e}))}})]}),e&&(0,t.jsx)(S,{visible:e,initialValues:a,onOk:D,onCancel:()=>s(!1)})]})}},57001:(e,s,a)=>{"use strict";a.d(s,{p:()=>n});var t=a(95155),r=a(30285),l=a(46102);function n(e){let{icon:s,tooltipText:a,tooltipSide:n="top",tooltipAlign:i="center",delayDuration:c=300,variant:d="ghost",size:o="icon",className:u="h-8 w-8 hover:bg-muted",...m}=e;return(0,t.jsx)(l.Bc,{delayDuration:c,children:(0,t.jsxs)(l.m_,{children:[(0,t.jsx)(l.k$,{asChild:!0,children:(0,t.jsx)(r.$,{variant:d,size:o,className:u,...m,children:(0,t.jsx)(s,{className:"h-4 w-4 text-muted-foreground"})})}),(0,t.jsx)(l.ZI,{side:n,align:i,className:"font-medium text-xs px-3 py-1.5",children:(0,t.jsx)("p",{children:a})})]})})}},59434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>l});var t=a(52596),r=a(39688);function l(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}},62523:(e,s,a)=>{"use strict";a.d(s,{p:()=>n});var t=a(95155),r=a(12115),l=a(59434);let n=r.forwardRef((e,s)=>{let{className:a,type:r,...n}=e;return(0,t.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:s,...n})});n.displayName="Input"},65436:(e,s,a)=>{"use strict";a.d(s,{G:()=>l,j:()=>r});var t=a(34540);let r=()=>(0,t.wA)(),l=t.d4},73069:(e,s,a)=>{"use strict";a.d(s,{c:()=>m});var t=a(95155),r=a(12115),l=a(47863),n=a(66474),i=a(5196),c=a(24357),d=a(59434),o=a(14636),u=a(30285);function m(e){let{children:s,maxDisplayLength:a=15,className:m,popoverWidth:x="auto",showBorder:p=!1}=e,[h,f]=r.useState(!1),[g,v]=r.useState(!1),j=r.useMemo(()=>{if("string"==typeof s||"number"==typeof s)return s.toString();try{var e;let a=document.createElement("div");return a.innerHTML=(null==s?void 0:null===(e=s.props)||void 0===e?void 0:e.children)||"",a.textContent||""}catch(e){return console.warn("Could not extract text from children:",e),""}},[s]),b=r.useMemo(()=>{if("string"==typeof s||"number"==typeof s){let e=s.toString();return e.length>a?e.slice(0,a):e}return s},[s,a]),N=async()=>{try{await navigator.clipboard.writeText(j),v(!0),setTimeout(()=>v(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,t.jsxs)(o.AM,{open:h,onOpenChange:f,children:[(0,t.jsx)(o.Wv,{asChild:!0,children:(0,t.jsxs)(u.$,{variant:p?"outline":"ghost",role:"combobox","aria-expanded":h,className:(0,d.cn)("w-fit justify-between font-normal px-2 py-1 h-auto",!p&&"border-0 shadow-none",m),children:[(0,t.jsx)("span",{className:"mr-2 truncate",children:b}),h?(0,t.jsx)(l.A,{className:"h-4 w-4 shrink-0 opacity-50"}):(0,t.jsx)(n.A,{className:"h-4 w-4 shrink-0 opacity-50"})]})}),(0,t.jsx)(o.hl,{className:"p-0",align:"start",style:{width:x},children:(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2",children:[(0,t.jsx)("span",{className:"text-sm break-all",children:j}),(0,t.jsxs)(u.$,{variant:"ghost",size:"icon",className:"h-8 w-8 shrink-0",onClick:N,children:[g?(0,t.jsx)(i.A,{className:"h-4 w-4"}):(0,t.jsx)(c.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:g?"Copied":"Copy text"})]})]})})]})}},80333:(e,s,a)=>{"use strict";a.d(s,{d:()=>i});var t=a(95155),r=a(12115),l=a(4884),n=a(59434);let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.bL,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...r,ref:s,children:(0,t.jsx)(l.zi,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});i.displayName=l.bL.displayName},85057:(e,s,a)=>{"use strict";a.d(s,{J:()=>d});var t=a(95155),r=a(12115),l=a(40968),n=a(74466),i=a(59434);let c=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.b,{ref:s,className:(0,i.cn)(c(),a),...r})});d.displayName=l.b.displayName},88539:(e,s,a)=>{"use strict";a.d(s,{T:()=>n});var t=a(95155),r=a(12115),l=a(59434);let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:s,...r})});n.displayName="Textarea"},91347:(e,s,a)=>{"use strict";a.d(s,{LQ:()=>l});var t=a(95155),r=a(65436);let l=e=>{let{permission:s,children:a,fallback:l=null,logic:n="any"}=e,i=(0,r.G)(e=>e.userPermissions.permissions);if(!s||Array.isArray(s)&&0===s.length)return(0,t.jsx)(t.Fragment,{children:a});let c=Array.isArray(s)?s:[s],d=!1;return("all"===n?c.every(e=>i.includes(e)):c.some(e=>i.includes(e)))?(0,t.jsx)(t.Fragment,{children:a}):(0,t.jsx)(t.Fragment,{children:l})};a(30285);var n=a(12115),i=a(74466),c=a(59434);let d=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}});n.forwardRef((e,s)=>{let{className:a,variant:r,...l}=e;return(0,t.jsx)("div",{ref:s,role:"alert",className:(0,c.cn)(d({variant:r}),a),...l})}).displayName="Alert",n.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h5",{ref:s,className:(0,c.cn)("mb-1 font-medium leading-none tracking-tight",a),...r})}).displayName="AlertTitle",n.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,c.cn)("text-sm [&_p]:leading-relaxed",a),...r})}).displayName="AlertDescription"}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,8687,4201,8737,4540,4582,5620,9613,7945,5589,1859,9624,9110,6315,7358],()=>s(28954)),_N_E=e.O()}]);