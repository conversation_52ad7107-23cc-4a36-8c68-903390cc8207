/* src/components/editor/Canvas.tsx */
'use client';

import React ,{ useState, useEffect } from 'react';
import { useDrop, useDrag } from 'react-dnd';
import { Component } from '@/types/page';
import { clientSanitize } from '@/lib/client-sanitize';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';

export const Canvas = ({ tenantId, pageId, initialComponents }: any) => {
  const [components, setComponents] = useState<Component[]>(initialComponents);
  const [selectedComponent, setSelectedComponent] = useState<Component | null>(null);
  const [generatedUrl, setGeneratedUrl] = useState<string | null>(null);
  const [currentPageId, setCurrentPageId] = useState(pageId);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    setComponents(initialComponents);
    setCurrentPageId(pageId);
  }, [initialComponents, pageId]);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: { HTMLAttributes: { class: 'list-disc pl-6' } },
        orderedList: { HTMLAttributes: { class: 'list-decimal pl-6' } },
      }),
    ],
    content: selectedComponent && ['header', 'text'].includes(selectedComponent.type)
      ? selectedComponent.content
      : '',
    onUpdate: ({ editor }) => {
      if (selectedComponent && ['header', 'text'].includes(selectedComponent.type)) {
        // 只在内容变化时才更新
        const html = editor.getHTML();
        if (html !== selectedComponent.content) {
          handleChange('content', html);
        }
      }
    },
    editable: !!selectedComponent && ['header', 'text'].includes(selectedComponent.type),
  });

  useEffect(() => {
    if (editor && selectedComponent && ['header', 'text'].includes(selectedComponent.type)) {
      // 只有内容变化时才 setContent，避免无限循环
      if (editor.getHTML() !== selectedComponent.content) {
        editor.commands.setContent(selectedComponent.content || '', false);
      }
      editor.setOptions({ editable: true });
    } else if (editor) {
      editor.commands.clearContent();
      editor.setOptions({ editable: false });
    }
  }, [selectedComponent, editor]);

  const [, drop] = useDrop(() => ({
    accept: 'component',
    drop: (item: {
      type: Component['type'] | 'template';
      defaultContent: string;
      defaultStyles: Record<string, string>;
      defaultExtra?: Component['extra'] | { components: Component[] };
    }) => {
      if (item.type === 'template' && Array.isArray((item.defaultExtra as any)?.components)) {
        // 拖入模板时，批量插入模板内的所有组件
        const templateComponents = (item.defaultExtra as any).components as Component[];
        setComponents((prev) => [
          ...prev,
          ...templateComponents.map(comp => ({
            ...comp,
            id: Date.now().toString() + Math.random().toString().slice(2, 8), // 保证唯一
          })),
        ]);
      } else {
        // 普通组件
        const newComponent: Component = {
          id: Date.now().toString(),
          type: item.type as Component['type'],
          content: item.defaultContent,
          styles: {
            backgroundColor: item.defaultStyles.backgroundColor || 'transparent',
            color: item.defaultStyles.color || 'black',
            fontSize: item.defaultStyles.fontSize || '16px',
            padding: '12px',
            margin: '6px',
            borderRadius: '8px',
            opacity: '1',
          },
          extra: item.defaultExtra as Component['extra'],
        };
        setComponents((prev) => [...prev, newComponent]);
      }
    },
  }));

  // 拖动排序逻辑
  const moveComponent = (dragIndex: number, hoverIndex: number) => {
    setComponents((prevComponents) => {
      const updated = [...prevComponents];
      const [removed] = updated.splice(dragIndex, 1);
      updated.splice(hoverIndex, 0, removed);
      return updated;
    });
  };

  const handleSavePage = async () => {
    try {
      const response = await fetch('/api/save-page', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tenantId, pageId: currentPageId, components }),
      });
      const { pageId: savedPageId } = await response.json();
      const url = `${window.location.origin}/${tenantId}/preview/${savedPageId}`;
      setGeneratedUrl(url);
      setCurrentPageId(savedPageId);
    } catch (error) {
      console.error('Failed to save page:', error);
    }
  };

  const handleLoadPage = async (newPageId: string) => {
    try {
      const response = await fetch(`/api/get-page?tenantId=${tenantId}&pageId=${newPageId}`);
      const page = await response.json();
      setComponents(page.components);
      setCurrentPageId(newPageId);
      setGeneratedUrl(null);
    } catch (error) {
      console.error('Failed to load page:', error);
    }
  };

  const handleComponentClick = (component: Component) => {
    setSelectedComponent(component);
  };

  const handleChange = (
    field: 'content' | keyof Component['styles'] | keyof NonNullable<Component['extra']>,
    value: string | boolean | any
  ) => {
    if (!selectedComponent) return;

    if (field === 'backgroundImage' && typeof value === 'string' && value && !value.match(/^https?:\/\/.+/)) {
      return;
    }
    if (['padding', 'margin', 'borderRadius'].includes(field as string) && typeof value === 'string' && value && !value.match(/^\d+px$/)) {
      return;
    }
    if (field === 'opacity' && typeof value === 'string' && value && (parseFloat(value) < 0 || parseFloat(value) > 1)) {
      return;
    }
    if (field === 'transitionSpeed' && typeof value === 'string' && value && !value.match(/^\d+ms$/)) {
      return;
    }
    if (['link', 'cardImages.url', 'images.url'].includes(field) && typeof value === 'string' && value && !value.match(/^https?:\/\/.+/)) {
      return;
    }

    const updatedComponent = {
      ...selectedComponent,
      ...(field === 'content'
        ? { content: typeof value === 'string' ? clientSanitize(value) : value }
        : field in selectedComponent.styles
        ? { styles: { ...selectedComponent.styles, [field]: value as string } }
        : { extra: { ...selectedComponent.extra, [field]: value } }),
    };

    const updatedComponents = components.map((comp) =>
      comp.id === selectedComponent.id ? updatedComponent : comp
    );

    setComponents(updatedComponents);
    setSelectedComponent(updatedComponent);
  };

  const handleCarouselImageAdd = () => {
    if (!selectedComponent || selectedComponent.type !== 'carousel') return;
    const images = selectedComponent.extra?.images || [];
    const newImages = [...images, { url: 'https://via.placeholder.com/750x300?text=New+Slide' }];
    handleChange('images', newImages);
  };

  const handleCarouselImageUpdate = (index: number, key: 'url' | 'caption', value: string) => {
    if (!selectedComponent || selectedComponent.type !== 'carousel') return;
    const images = selectedComponent.extra?.images || [];
    const newImages = images.map((img, i) =>
      i === index ? { ...img, [key]: clientSanitize(value) } : img
    );
    handleChange('images', newImages);
  };

  const handleCarouselImageRemove = (index: number) => {
    if (!selectedComponent || selectedComponent.type !== 'carousel') return;
    const images = selectedComponent.extra?.images || [];
    const newImages = images.filter((_, i) => i !== index);
    handleChange('images', newImages);
  };

  const handleFormFieldAdd = () => {
    if (!selectedComponent || selectedComponent.type !== 'formInput') return;
    const formFields = selectedComponent.extra?.formFields || [];
    const newFields = [
      ...formFields,
      { id: Date.now().toString(), label: '新字段', type: 'text', placeholder: '请输入' },
    ];
    handleChange('formFields', newFields);
  };

  const handleFormFieldUpdate = (
    index: number,
    key: 'label' | 'type' | 'placeholder',
    value: string
  ) => {
    if (!selectedComponent || selectedComponent.type !== 'formInput') return;
    const formFields = selectedComponent.extra?.formFields || [];
    const newFields = formFields.map((field, i) =>
      i === index ? { ...field, [key]: clientSanitize(value) } : field
    );
    handleChange('formFields', newFields);
  };

  const handleFormFieldRemove = (index: number) => {
    if (!selectedComponent || selectedComponent.type !== 'formInput') return;
    const formFields = selectedComponent.extra?.formFields || [];
    const newFields = formFields.filter((_, i) => i !== index);
    handleChange('formFields', newFields);
  };

  const handleCardImageAdd = () => {
    if (!selectedComponent || selectedComponent.type !== 'card') return;
    const cardImages = selectedComponent.extra?.cardImages || [];
    const newImages = [...cardImages, { url: 'https://via.placeholder.com/100' }];
    handleChange('cardImages', newImages);
  };

  const handleCardImageUpdate = (index: number, key: 'url' | 'altText', value: string) => {
    if (!selectedComponent || selectedComponent.type !== 'card') return;
    const cardImages = selectedComponent.extra?.cardImages || [];
    const newImages = cardImages.map((img, i) =>
      i === index ? { ...img, [key]: clientSanitize(value) } : img
    );
    handleChange('cardImages', newImages);
  };

  const handleCardImageRemove = (index: number) => {
    if (!selectedComponent || selectedComponent.type !== 'card') return;
    const cardImages = selectedComponent.extra?.cardImages || [];
    const newImages = cardImages.filter((_, i) => i !== index);
    handleChange('cardImages', newImages);
  };

  // 渲染单个可拖拽组件
  const DraggableComponent = ({
    comp,
    index,
  }: {
    comp: Component;
    index: number;
  }) => {
    const ref = React.useRef<HTMLDivElement>(null);

    const [, drop] = useDrop({
      accept: 'canvas-component',
      hover(item: { index: number }, monitor) {
        if (!ref.current) return;
        const dragIndex = item.index;
        const hoverIndex = index;
        if (dragIndex === hoverIndex) return;
        moveComponent(dragIndex, hoverIndex);
        item.index = hoverIndex;
      },
    });

    const [{ isDragging }, drag] = useDrag({
      type: 'canvas-component',
      item: { index },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    drag(drop(ref));

    return (
      <div
        ref={ref}
        style={{ opacity: isDragging ? 0.5 : 1 }}
        onClick={() => handleComponentClick(comp)}
        className={`component cursor-pointer ${selectedComponent?.id === comp.id ? 'selected' : ''}`}
      >
        {renderComponent(comp)}
      </div>
    );
  };

  const renderComponent = (comp: Component) => {
    const style = {
      backgroundColor: comp.styles.backgroundColor,
      color: comp.styles.color,
      fontSize: comp.styles.fontSize,
      backgroundImage: comp.styles.backgroundImage ? `url(${clientSanitize(comp.styles.backgroundImage)})` : undefined,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      padding: comp.styles.padding,
      margin: comp.styles.margin,
      borderRadius: comp.styles.borderRadius,
      opacity: comp.styles.opacity,
    };

    // 新增：如果有 followText 占位符，进行替换
    let content = comp.content;
    if (comp.extra && typeof comp.extra.followText === 'string') {
      content = content.replace('{{followText}}', clientSanitize(comp.extra.followText));
    }

    switch (comp.type) {
      case 'header':
        return (
          <header style={style} className="component">
            <div dangerouslySetInnerHTML={{ __html: clientSanitize(content) }} />
          </header>
        );
      case 'text':
        return (
          <div style={style} className="component">
            <div dangerouslySetInnerHTML={{ __html: clientSanitize(content) }} />
          </div>
        );
      case 'image':
        return (
          <img
            src={clientSanitize(comp.content)}
            alt={comp.extra?.altText || ''}
            style={style}
            className="component w-full object-cover"
          />
        );
      case 'carousel':
        const images = comp.extra?.images || [];
        return (
          <div style={style} className="component relative w-full h-64 overflow-hidden">
            {images.map((img, index) => (
              <div
                key={index}
                className="absolute top-0 left-0 w-full h-full"
                style={{
                  transform: `translateX(${index * 100}%)`,
                  transition: `transform ${comp.extra?.transitionSpeed || '500ms'}`,
                }}
              >
                <img
                  src={clientSanitize(img.url)}
                  className="w-full h-full object-cover"
                  alt={img.caption}
                />
                {img.caption && (
                  <div className="absolute bottom-0 w-full bg-black bg-opacity-60 text-white text-center p-3">
                    {clientSanitize(img.caption)}
                  </div>
                )}
              </div>
            ))}
            {comp.extra?.showNav && (
              <>
                <button className="absolute left-3 top-1/2 transform -translate-y-1/2 carousel-nav">
                  ←
                </button>
                <button className="absolute right-3 top-1/2 transform -translate-y-1/2 carousel-nav">
                  →
                </button>
              </>
            )}
          </div>
        );
      case 'button':
        return (
          <a
            href={clientSanitize(comp.extra?.link || '#')}
            target={comp.extra?.linkTarget || '_self'}
            rel={comp.extra?.linkRel}
            style={style}
            className="component inline-block px-6 py-3 text-center"
          >
            {comp.extra?.label || clientSanitize(content)}
          </a>
        );
      case 'formInput':
        const formFields = comp.extra?.formFields || [];
        return (
          <div style={style} className="component space-y-4">
            {formFields.map((field) => (
              <div key={field.id} className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">{clientSanitize(field.label)}</label>
                <input
                  type={field.type}
                  placeholder={field.placeholder || '请输入'}
                  className="w-full"
                />
              </div>
            ))}
          </div>
        );
      case 'card':
        const cardImages = comp.extra?.cardImages || [];
        return (
          <div style={style} className="component space-y-3">
            <div className="flex flex-wrap gap-2">
              {cardImages.map((img, index) => (
                <img
                  key={index}
                  src={clientSanitize(img.url)}
                  className="w-20 h-20 rounded-md object-cover"
                  alt={img.altText}
                />
              ))}
            </div>
            <h3 className="text-lg font-semibold">{clientSanitize(comp.extra?.title || '')}</h3>
            {comp.extra?.subtitle && <h4 className="text-sm text-gray-600">{clientSanitize(comp.extra.subtitle)}</h4>}
            <p className="text-gray-700">{clientSanitize(comp.extra?.description || '')}</p>
          </div>
        );
      default:
        return <div style={style} className="component">{clientSanitize(content)}</div>;
    }
  };

  return (
    <div className="flex flex-1 min-h-screen bg-[var(--background)]">
      <div ref={drop} className="flex-1 p-6">
        <div className="flex justify-between items-center mb-6 bg-white p-4 rounded-lg shadow-sm">
          <h2 className="text-xl font-semibold text-gray-800">
            画布 - 租户: {tenantId} - 页面ID: {currentPageId}
          </h2>
          <div className="flex space-x-3">
            <input
              type="text"
              placeholder="输入页面ID加载"
              onChange={(e) => handleLoadPage(e.target.value)}
              className="w-48"
            />
            <button onClick={handleSavePage} className="button primary">
              保存并生成URL
            </button>
          </div>
        </div>
        <div className="space-y-4">
          {components.map((comp, idx) => (
            <DraggableComponent key={comp.id} comp={comp} index={idx} />
          ))}
        </div>
        {generatedUrl && (
          <div className="mt-6 p-4 bg-white rounded-lg shadow-sm">
            <p className="text-sm font-medium text-gray-700">生成的URL:</p>
            <input
              type="text"
              value={generatedUrl}
              readOnly
              className="mt-2 w-full"
              onClick={(e) => e.currentTarget.select()}
            />
            <a
              href={generatedUrl}
              target="_blank"
              className="mt-2 inline-block text-[var(--primary)] hover:underline"
            >
              打开预览
            </a>
          </div>
        )}
      </div>
      {selectedComponent && (
        <div className="w-80 p-6 bg-white shadow-lg overflow-y-auto">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">编辑组件</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">内容</label>
              {['header', 'text'].includes(selectedComponent.type) && isClient && editor ? (
                <div className="bg-white border rounded-lg p-3">
                  <div className="flex flex-wrap gap-2 mb-3">
                    <button
                      onClick={() => editor.chain().focus().toggleBold().run()}
                      className={`toolbar-button ${editor.isActive('bold') ? 'active' : ''}`}
                    >
                      粗体
                    </button>
                    <button
                      onClick={() => editor.chain().focus().toggleItalic().run()}
                      className={`toolbar-button ${editor.isActive('italic') ? 'active' : ''}`}
                    >
                      斜体
                    </button>
                    <button
                      onClick={() => editor.chain().focus().toggleUnderline().run()}
                      className={`toolbar-button ${editor.isActive('underline') ? 'active' : ''}`}
                    >
                      下划线
                    </button>
                    <button
                      onClick={() => {
                        const url = prompt('输入链接URL:');
                        if (url) editor.chain().focus().setLink({ href: url }).run();
                      }}
                      className={`toolbar-button ${editor.isActive('link') ? 'active' : ''}`}
                    >
                      链接
                    </button>
                    <button
                      onClick={() => editor.chain().focus().toggleBulletList().run()}
                      className={`toolbar-button ${editor.isActive('bulletList') ? 'active' : ''}`}
                    >
                      无序列表
                    </button>
                    <button
                      onClick={() => editor.chain().focus().toggleOrderedList().run()}
                      className={`toolbar-button ${editor.isActive('orderedList') ? 'active' : ''}`}
                    >
                      有序列表
                    </button>
                    <button
                      onClick={() => editor.chain().focus().unsetAllMarks().clearNodes().run()}
                      className="toolbar-button"
                    >
                      清除格式
                    </button>
                  </div>
                  <EditorContent editor={editor} className="prose prose-sm max-w-none" />
                </div>
              ) : (
                <textarea
                  value={selectedComponent.content}
                  onChange={(e) => handleChange('content', e.target.value)}
                  className="w-full"
                  rows={4}
                  placeholder="输入内容"
                />
              )}
            </div>
            {selectedComponent.extra?.followText !== undefined && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">关注引导文案</label>
                <input
                  type="text"
                  value={selectedComponent.extra.followText}
                  onChange={e => handleChange('followText', e.target.value)}
                  className="w-full"
                  placeholder="如：关注本公众号"
                />
              </div>
            )}
            {selectedComponent.type === 'carousel' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">轮播图片</label>
                  {selectedComponent.extra?.images?.map((img, index) => (
                    <div key={index} className="mb-3 border p-3 rounded-lg bg-gray-50">
                      <input
                        type="text"
                        value={img.url}
                        onChange={(e) => handleCarouselImageUpdate(index, 'url', e.target.value)}
                        className="mb-2"
                        placeholder="图片URL"
                      />
                      <input
                        type="text"
                        value={img.caption || ''}
                        onChange={(e) => handleCarouselImageUpdate(index, 'caption', e.target.value)}
                        className="mb-2"
                        placeholder="图片说明"
                      />
                      <button
                        onClick={() => handleCarouselImageRemove(index)}
                        className="button danger"
                      >
                        删除
                      </button>
                    </div>
                  ))}
                  <button
                    onClick={handleCarouselImageAdd}
                    className="button primary w-full"
                  >
                    添加图片
                  </button>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">过渡速度 (ms)</label>
                  <input
                    type="text"
                    value={selectedComponent.extra?.transitionSpeed || '500ms'}
                    onChange={(e) => handleChange('transitionSpeed', e.target.value)}
                    placeholder="e.g., 500ms"
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">显示导航</label>
                  <input
                    type="checkbox"
                    checked={selectedComponent.extra?.showNav || false}
                    onChange={(e) => handleChange('showNav', e.target.checked)}
                    className="h-4 w-4 text-[var(--primary)]"
                  />
                </div>
              </>
            )}
            {selectedComponent.type === 'button' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">标签</label>
                  <input
                    type="text"
                    value={selectedComponent.extra?.label || ''}
                    onChange={(e) => handleChange('label', e.target.value)}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">链接</label>
                  <input
                    type="text"
                    value={selectedComponent.extra?.link || ''}
                    onChange={(e) => handleChange('link', e.target.value)}
                    placeholder="https://example.com"
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">链接目标</label>
                  <select
                    value={selectedComponent.extra?.linkTarget || '_self'}
                    onChange={(e) => handleChange('linkTarget', e.target.value)}
                    className="w-full"
                  >
                    <option value="_self">当前窗口</option>
                    <option value="_blank">新窗口</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">链接Rel</label>
                  <input
                    type="text"
                    value={selectedComponent.extra?.linkRel || ''}
                    onChange={(e) => handleChange('linkRel', e.target.value)}
                    placeholder="e.g., nofollow"
                    className="w-full"
                  />
                </div>
              </>
            )}
            {selectedComponent.type === 'formInput' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">表单字段</label>
                  {selectedComponent.extra?.formFields?.map((field, index) => (
                    <div key={field.id} className="mb-3 border p-3 rounded-lg bg-gray-50">
                      <input
                        type="text"
                        value={field.label}
                        onChange={(e) => handleFormFieldUpdate(index, 'label', e.target.value)}
                        className="mb-2"
                        placeholder="字段标签"
                      />
                      <select
                        value={field.type}
                        onChange={(e) => handleFormFieldUpdate(index, 'type', e.target.value)}
                        className="mb-2"
                      >
                        <option value="text">文本</option>
                        <option value="email">邮箱</option>
                        <option value="tel">电话</option>
                      </select>
                      <input
                        type="text"
                        value={field.placeholder || ''}
                        onChange={(e) => handleFormFieldUpdate(index, 'placeholder', e.target.value)}
                        className="mb-2"
                        placeholder="占位文本"
                      />
                      <button
                        onClick={() => handleFormFieldRemove(index)}
                        className="button danger"
                      >
                        删除
                      </button>
                    </div>
                  ))}
                  <button
                    onClick={handleFormFieldAdd}
                    className="button primary w-full"
                  >
                    添加字段
                  </button>
                </div>
              </>
            )}
            {selectedComponent.type === 'image' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">替代文本</label>
                <input
                  type="text"
                  value={selectedComponent.extra?.altText || ''}
                  onChange={(e) => handleChange('altText', e.target.value)}
                  className="w-full"
                />
              </div>
            )}
            {selectedComponent.type === 'card' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">标题</label>
                  <input
                    type="text"
                    value={selectedComponent.extra?.title || ''}
                    onChange={(e) => handleChange('title', e.target.value)}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">副标题</label>
                  <input
                    type="text"
                    value={selectedComponent.extra?.subtitle || ''}
                    onChange={(e) => handleChange('subtitle', e.target.value)}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                  <textarea
                    value={selectedComponent.extra?.description || ''}
                    onChange={(e) => handleChange('description', e.target.value)}
                    className="w-full"
                    rows={3}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">卡片图片</label>
                  {selectedComponent.extra?.cardImages?.map((img, index) => (
                    <div key={index} className="mb-3 border p-3 rounded-lg bg-gray-50">
                      <input
                        type="text"
                        value={img.url}
                        onChange={(e) => handleCardImageUpdate(index, 'url', e.target.value)}
                        className="mb-2"
                        placeholder="图片URL"
                      />
                      <input
                        type="text"
                        value={img.altText || ''}
                        onChange={(e) => handleCardImageUpdate(index, 'altText', e.target.value)}
                        className="mb-2"
                        placeholder="替代文本"
                      />
                      <button
                        onClick={() => handleCardImageRemove(index)}
                        className="button danger"
                      >
                        删除
                      </button>
                    </div>
                  ))}
                  <button
                    onClick={handleCardImageAdd}
                    className="button primary w-full"
                  >
                    添加图片
                  </button>
                </div>
              </>
            )}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">背景颜色</label>
              <input
                type="color"
                value={selectedComponent.styles.backgroundColor || '#ffffff'}
                onChange={(e) => handleChange('backgroundColor', e.target.value)}
                className="w-full h-10 p-1 rounded-lg"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">背景图片 (URL)</label>
              <input
                type="text"
                value={selectedComponent.styles.backgroundImage || ''}
                onChange={(e) => handleChange('backgroundImage', e.target.value)}
                placeholder="https://example.com/image.jpg"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">内边距</label>
              <input
                type="text"
                value={selectedComponent.styles.padding || '12px'}
                onChange={(e) => handleChange('padding', e.target.value)}
                placeholder="e.g., 12px"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">外边距</label>
              <input
                type="text"
                value={selectedComponent.styles.margin || '6px'}
                onChange={(e) => handleChange('margin', e.target.value)}
                placeholder="e.g., 6px"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">圆角</label>
              <input
                type="text"
                value={selectedComponent.styles.borderRadius || '8px'}
                onChange={(e) => handleChange('borderRadius', e.target.value)}
                placeholder="e.g., 8px"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">透明度 (0-1)</label>
              <input
                type="text"
                value={selectedComponent.styles.opacity || '1'}
                onChange={(e) => handleChange('opacity', e.target.value)}
                placeholder="e.g., 0.8"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">文字颜色</label>
              <input
                type="color"
                value={selectedComponent.styles.color || '#000000'}
                onChange={(e) => handleChange('color', e.target.value)}
                className="w-full h-10 p-1 rounded-lg"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">字体大小</label>
              <input
                type="text"
                value={selectedComponent.styles.fontSize || '16px'}
                onChange={(e) => handleChange('fontSize', e.target.value)}
                placeholder="e.g., 16px"
                className="w-full"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};