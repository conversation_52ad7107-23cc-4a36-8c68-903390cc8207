export default async function (fastify, options) {
    fastify.get('/provinces', {
        schema: {
            tags: ['provinces'],
            querystring: {
                type: 'object',
                properties: {
                    province: { type: 'string' },
                    city: { type: 'string' },
                    area: { type: 'string' }
                }
            }
        }, 
        handler: async (request, reply) => {
            const { province, city, area } = request.query;
            
            try {
                // 获取省级数据
                if(!province && !city && !area) {
                    return await getAreasByLevel(fastify, 1, null, reply);
                }
                
                // 获取市级数据
                if(province && !city) {
                    const provinceData = await getAreaByNameAndLevel(fastify, province, 1, '省份');
                    if(!provinceData.success) return reply.status(400).send(provinceData.error);
                    return await getAreasByLevel(fastify, 2, String(provinceData.data.id), reply);
                }
                
                // 获取区/县级数据
                if(city && !area) {
                    const cityData = await getAreaByNameAndLevel(fastify, city, 2, '城市');
                    if(!cityData.success) return reply.status(400).send(cityData.error);
                    return await getAreasByLevel(fastify, 3, String(cityData.data.id), reply);
                }
                
                // 如果需要处理 area 参数的查询
                if(area && city) {
                    const areaData = await getAreaByNameAndLevel(fastify, area, 3, '区县');
                    if(!areaData.success) return reply.status(400).send(areaData.error);
                    // 如果需要获取街道等下级数据，可以在这里添加
                    return reply.success({
                        message: '获取区县数据成功.',
                        data: areaData.data
                    });
                }
                
                return reply.status(400).send({ message: '请提供有效的查询参数.' });
            } catch (error) {
                fastify.log.error(error);
                return reply.status(500).send({ message: '服务器内部错误!' });
            }
        }
    });
    
    // 通过名称和级别获取地理区域
    async function getAreaByNameAndLevel(fastify, name, level, typeName) {
        const result = await fastify.prisma.geographicArea.findFirst({
            where: {
                name,
                level
            },
            select: {
                id: true,
                name: true,
                code:true,
                postCode:true
                // level: true,
                // parentId: true
            }
        });
        
        if(!result) {
            return {
                success: false,
                error: { message: `${typeName}不存在` }
            };
        }
        
        return {
            success: true,
            data: result
        };
    }
    
    // 获取指定级别和父ID的地理区域列表
    async function getAreasByLevel(fastify, level, parentId, reply) {
        const whereClause = { level };
        if(parentId) whereClause.parentId = parentId;
        
        const areaList = await fastify.prisma.geographicArea.findMany({
            where: whereClause,
            select: {
                id: true,
                name: true,
                code:true,
                postCode:true
                // level: true,
                // parentId: true
            }
        });
        
        return reply.success({
            message: '获取地理区域数据成功.',
            data: areaList
        });
    }
}
