(()=>{var e={};e.id=2274,e.ids=[2274],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(49384),n=r(82348);function i(...e){return(0,n.QP)((0,s.$)(e))}},7276:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\1\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\1\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>c});var s=r(60687),n=r(43210),i=r(8730),o=r(24224),a=r(4780);let c=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...o},d)=>{let u=n?i.DX:"button";return(0,s.jsx)(u,{className:(0,a.cn)(c({variant:t,size:r,className:e})),ref:d,...o})});d.displayName="Button"},33873:e=>{"use strict";e.exports=require("path")},48935:(e,t,r)=>{Promise.resolve().then(r.bind(r,7276))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56397:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75913:(e,t,r)=>{"use strict";r(56397);var s=r(43210),n=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),i="undefined"!=typeof process&&process.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,r=t.name,s=void 0===r?"stylesheet":r,n=t.optimizeForSpeed,a=void 0===n?i:n;c(o(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",c("boolean"==typeof a,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=a,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;c(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){return c(o(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},t.replaceRule=function(e,t){this._optimizeForSpeed;var r=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(s){i||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}return e},t.deleteRule=function(e){this._serverSheet.deleteRule(e)},t.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},t.cssRules=function(){return this._serverSheet.cssRules},t.makeStyleTag=function(e,t,r){t&&c(o(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return r?n.insertBefore(s,r):n.appendChild(s),s},function(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},u={};function l(e,t){if(!t)return"jsx-"+e;var r=String(t),s=e+r;return u[s]||(u[s]="jsx-"+d(e+"-"+r)),u[s]}function f(e,t){var r=e+(t=t.replace(/\/style/gi,"\\/style"));return u[r]||(u[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[r]}var h=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,s=void 0===r?null:r,n=t.optimizeForSpeed,i=void 0!==n&&n;this._sheet=s||new a({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),s&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var r=this.getIdAndRules(e),s=r.styleId,n=r.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var i=n.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=i,this._instancesCounts[s]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var s=this._fromServer&&this._fromServer[r];s?(s.parentNode.removeChild(s),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],s=e[1];return n.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,s=e.id;if(r){var n=l(s,r);return{styleId:n,rules:Array.isArray(t)?t.map(function(e){return f(n,e)}):[f(n,t)]}}return{styleId:l(s),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),p=s.createContext(null);p.displayName="StyleSheetContext";n.default.useInsertionEffect||n.default.useLayoutEffect;var m=void 0;function g(e){var t=m||s.useContext(p);return t&&t.add(e),null}g.dynamic=function(e){return e.map(function(e){return l(e[0],e[1])}).join(" ")},t.style=g},76180:(e,t,r)=>{"use strict";e.exports=r(75913).style},79551:e=>{"use strict";e.exports=require("url")},79799:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),n=r(76180),i=r.n(n);let o=()=>(0,s.jsxs)("div",{className:"jsx-c6a6d1e14f488c28 relative w-[200px] h-[200px] mx-auto",children:[(0,s.jsxs)("svg",{viewBox:"0 0 200 200",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"jsx-c6a6d1e14f488c28 w-full h-full",children:[(0,s.jsx)("circle",{cx:"100",cy:"100",r:"100",fill:"#FFF8F0",className:"jsx-c6a6d1e14f488c28"}),(0,s.jsx)("path",{d:"M60 140c0-20 15-25 40-25s40 5 40 25-20 30-40 30-40-10-40-30z",fill:"#FFD59A",className:"jsx-c6a6d1e14f488c28"}),(0,s.jsx)("circle",{cx:"80",cy:"110",r:"10",fill:"#000",className:"jsx-c6a6d1e14f488c28"}),(0,s.jsx)("circle",{cx:"120",cy:"110",r:"10",fill:"#000",className:"jsx-c6a6d1e14f488c28"}),(0,s.jsx)("path",{d:"M100 125c-5 0-8 4-8 6s2 4 8 4 8-2 8-4-3-6-8-6z",fill:"#F472B6",className:"jsx-c6a6d1e14f488c28"}),(0,s.jsx)("path",{d:"M70 95c-10-10-10-30 5-35s20 10 25 10 15-15 30-5 10 30 0 40",stroke:"#444",strokeWidth:"4",strokeLinecap:"round",className:"jsx-c6a6d1e14f488c28"}),(0,s.jsx)("path",{d:"M85 70c-2-5-10-15-20-10s-5 20 0 25",stroke:"#D97706",strokeWidth:"4",strokeLinecap:"round",className:"jsx-c6a6d1e14f488c28"}),(0,s.jsx)("path",{d:"M115 70c2-5 10-15 20-10s5 20 0 25",stroke:"#D97706",strokeWidth:"4",strokeLinecap:"round",className:"jsx-c6a6d1e14f488c28"}),(0,s.jsx)("g",{className:"jsx-c6a6d1e14f488c28 tail-origin",children:(0,s.jsx)("path",{d:"M140 155 Q160 150 150 130",stroke:"#D97706",strokeWidth:"6",strokeLinecap:"round",fill:"none",className:"jsx-c6a6d1e14f488c28"})})]}),(0,s.jsx)(i(),{id:"c6a6d1e14f488c28",children:".tail-origin.jsx-c6a6d1e14f488c28{-webkit-transform-origin:140px 155px;-moz-transform-origin:140px 155px;-ms-transform-origin:140px 155px;-o-transform-origin:140px 155px;transform-origin:140px 155px;-webkit-animation:wag-tail.6s ease-in-out infinite;-moz-animation:wag-tail.6s ease-in-out infinite;-o-animation:wag-tail.6s ease-in-out infinite;animation:wag-tail.6s ease-in-out infinite}@-webkit-keyframes wag-tail{0%{-webkit-transform:rotate(10deg);transform:rotate(10deg)}50%{-webkit-transform:rotate(-15deg);transform:rotate(-15deg)}100%{-webkit-transform:rotate(10deg);transform:rotate(10deg)}}@-moz-keyframes wag-tail{0%{-moz-transform:rotate(10deg);transform:rotate(10deg)}50%{-moz-transform:rotate(-15deg);transform:rotate(-15deg)}100%{-moz-transform:rotate(10deg);transform:rotate(10deg)}}@-o-keyframes wag-tail{0%{-o-transform:rotate(10deg);transform:rotate(10deg)}50%{-o-transform:rotate(-15deg);transform:rotate(-15deg)}100%{-o-transform:rotate(10deg);transform:rotate(10deg)}}@keyframes wag-tail{0%{-webkit-transform:rotate(10deg);-moz-transform:rotate(10deg);-o-transform:rotate(10deg);transform:rotate(10deg)}50%{-webkit-transform:rotate(-15deg);-moz-transform:rotate(-15deg);-o-transform:rotate(-15deg);transform:rotate(-15deg)}100%{-webkit-transform:rotate(10deg);-moz-transform:rotate(10deg);-o-transform:rotate(10deg);transform:rotate(10deg)}}"})]});var a=r(29523),c=r(85814),d=r.n(c);function u(){return(0,s.jsxs)("main",{className:"flex flex-col items-center justify-center min-h-screen px-4 bg-white text-center",children:[(0,s.jsx)(o,{}),(0,s.jsx)("h1",{className:"text-3xl font-bold mt-6",children:"404 - 找不到页面"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2",children:"这只小狗迷路了，我们也找不到你要去的地方。"}),(0,s.jsx)(a.$,{asChild:!0,className:"mt-4",children:(0,s.jsx)(d(),{href:"/",children:"返回首页"})})]})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88255:(e,t,r)=>{Promise.resolve().then(r.bind(r,79799))},93069:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>f,tree:()=>d});var s=r(65239),n=r(48088),i=r(88170),o=r.n(i),a=r(30893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);r.d(t,c);let d={children:["",{children:["1",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7276)),"F:\\trae\\cardmees\\fronend\\src\\app\\1\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["F:\\trae\\cardmees\\fronend\\src\\app\\1\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/1/page",pathname:"/1",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7392,5814,3928,3019],()=>r(93069));module.exports=s})();