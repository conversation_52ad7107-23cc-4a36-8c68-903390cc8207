"use strict";exports.id=7605,exports.ids=[7605],exports.modules={27605:(e,t,r)=>{r.d(t,{Gb:()=>N,Jt:()=>g,Op:()=>D,hZ:()=>V,mN:()=>eA,xI:()=>B,xW:()=>S});var s=r(43210),a=e=>"checkbox"===e.type,i=e=>e instanceof Date,l=e=>null==e;let u=e=>"object"==typeof e;var n=e=>!l(e)&&!Array.isArray(e)&&u(e)&&!i(e),o=e=>n(e)&&e.target?a(e.target)?e.target.checked:e.target.value:e,d=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,f=(e,t)=>e.has(d(t)),c=e=>{let t=e.constructor&&e.constructor.prototype;return n(t)&&t.hasOwnProperty("isPrototypeOf")},y="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function m(e){let t;let r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(!(y&&(e instanceof Blob||s))&&(r||n(e))))return e;else if(t=r?[]:{},r||c(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=m(e[r]));else t=e;return t}var v=e=>Array.isArray(e)?e.filter(Boolean):[],h=e=>void 0===e,g=(e,t,r)=>{if(!t||!n(e))return r;let s=v(t.split(/[,[\].]+?/)).reduce((e,t)=>l(e)?e:e[t],e);return h(s)||s===e?h(e[t])?r:e[t]:s},b=e=>"boolean"==typeof e,p=e=>/^\w*$/.test(e),_=e=>v(e.replace(/["|']|\]/g,"").split(/\.|\[/)),V=(e,t,r)=>{let s=-1,a=p(t)?[t]:_(t),i=a.length,l=i-1;for(;++s<i;){let t=a[s],i=r;if(s!==l){let r=e[t];i=n(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}return e};let F={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},A={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},x={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},w=s.createContext(null),S=()=>s.useContext(w),D=e=>{let{children:t,...r}=e;return s.createElement(w.Provider,{value:r},t)};var k=(e,t,r,s=!0)=>{let a={defaultValues:t._defaultValues};for(let i in e)Object.defineProperty(a,i,{get:()=>(t._proxyFormState[i]!==A.all&&(t._proxyFormState[i]=!s||A.all),r&&(r[i]=!0),e[i])});return a},E=e=>n(e)&&!Object.keys(e).length,O=(e,t,r,s)=>{r(e);let{name:a,...i}=e;return E(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(e=>t[e]===(!s||A.all))},C=e=>Array.isArray(e)?e:[e],j=(e,t,r)=>!e||!t||e===t||C(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e)));function L(e){let t=s.useRef(e);t.current=e,s.useEffect(()=>{let r=!e.disabled&&t.current.subject&&t.current.subject.subscribe({next:t.current.next});return()=>{r&&r.unsubscribe()}},[e.disabled])}var T=e=>"string"==typeof e,U=(e,t,r,s,a)=>T(e)?(s&&t.watch.add(e),g(r,e,a)):Array.isArray(e)?e.map(e=>(s&&t.watch.add(e),g(r,e))):(s&&(t.watchAll=!0),r);let B=e=>e.render(function(e){let t=S(),{name:r,disabled:a,control:i=t.control,shouldUnregister:l}=e,u=f(i._names.array,r),n=function(e){let t=S(),{control:r=t.control,name:a,defaultValue:i,disabled:l,exact:u}=e||{},n=s.useRef(a);n.current=a,L({disabled:l,subject:r._subjects.values,next:e=>{j(n.current,e.name,u)&&d(m(U(n.current,r._names,e.values||r._formValues,!1,i)))}});let[o,d]=s.useState(r._getWatch(a,i));return s.useEffect(()=>r._removeUnmounted()),o}({control:i,name:r,defaultValue:g(i._formValues,r,g(i._defaultValues,r,e.defaultValue)),exact:!0}),d=function(e){let t=S(),{control:r=t.control,disabled:a,name:i,exact:l}=e||{},[u,n]=s.useState(r._formState),o=s.useRef(!0),d=s.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),f=s.useRef(i);return f.current=i,L({disabled:a,next:e=>o.current&&j(f.current,e.name,l)&&O(e,d.current,r._updateFormState)&&n({...r._formState,...e}),subject:r._subjects.state}),s.useEffect(()=>(o.current=!0,d.current.isValid&&r._updateValid(!0),()=>{o.current=!1}),[r]),s.useMemo(()=>k(u,r,d.current,!1),[u,r])}({control:i,name:r,exact:!0}),c=s.useRef(i.register(r,{...e.rules,value:n,...b(e.disabled)?{disabled:e.disabled}:{}})),y=s.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!g(d.errors,r)},isDirty:{enumerable:!0,get:()=>!!g(d.dirtyFields,r)},isTouched:{enumerable:!0,get:()=>!!g(d.touchedFields,r)},isValidating:{enumerable:!0,get:()=>!!g(d.validatingFields,r)},error:{enumerable:!0,get:()=>g(d.errors,r)}}),[d,r]),v=s.useMemo(()=>({name:r,value:n,...b(a)||d.disabled?{disabled:d.disabled||a}:{},onChange:e=>c.current.onChange({target:{value:o(e),name:r},type:F.CHANGE}),onBlur:()=>c.current.onBlur({target:{value:g(i._formValues,r),name:r},type:F.BLUR}),ref:e=>{let t=g(i._fields,r);t&&e&&(t._f.ref={focus:()=>e.focus(),select:()=>e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})}}),[r,i._formValues,a,d.disabled,n,i._fields]);return s.useEffect(()=>{let e=i._options.shouldUnregister||l,t=(e,t)=>{let r=g(i._fields,e);r&&r._f&&(r._f.mount=t)};if(t(r,!0),e){let e=m(g(i._options.defaultValues,r));V(i._defaultValues,r,e),h(g(i._formValues,r))&&V(i._formValues,r,e)}return u||i.register(r),()=>{(u?e&&!i._state.action:e)?i.unregister(r):t(r,!1)}},[r,i,u,l]),s.useEffect(()=>{i._updateDisabledField({disabled:a,fields:i._fields,name:r})},[a,r,i]),s.useMemo(()=>({field:v,formState:d,fieldState:y}),[v,d,y])}(e));var N=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},M=e=>({isOnSubmit:!e||e===A.onSubmit,isOnBlur:e===A.onBlur,isOnChange:e===A.onChange,isOnAll:e===A.all,isOnTouch:e===A.onTouched}),R=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let q=(e,t,r,s)=>{for(let a of r||Object.keys(e)){let r=g(e,a);if(r){let{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(q(i,t))break}else if(n(i)&&q(i,t))break}}};var P=(e,t,r)=>{let s=C(g(e,r));return V(s,"root",t[r]),V(e,r,s),e},I=e=>"file"===e.type,W=e=>"function"==typeof e,H=e=>{if(!y)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},$=e=>T(e),G=e=>"radio"===e.type,z=e=>e instanceof RegExp;let J={value:!1,isValid:!1},Z={value:!0,isValid:!0};var K=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!h(e[0].attributes.value)?h(e[0].value)||""===e[0].value?Z:{value:e[0].value,isValid:!0}:Z:J}return J};let Q={isValid:!1,value:null};var X=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,Q):Q;function Y(e,t,r="validate"){if($(e)||Array.isArray(e)&&e.every($)||b(e)&&!e)return{type:r,message:$(e)?e:"",ref:t}}var ee=e=>n(e)&&!z(e)?e:{value:e,message:""},et=async(e,t,r,s,i,u)=>{let{ref:o,refs:d,required:f,maxLength:c,minLength:y,min:m,max:v,pattern:p,validate:_,name:V,valueAsNumber:F,mount:A}=e._f,w=g(r,V);if(!A||t.has(V))return{};let S=d?d[0]:o,D=e=>{i&&S.reportValidity&&(S.setCustomValidity(b(e)?"":e||""),S.reportValidity())},k={},O=G(o),C=a(o),j=(F||I(o))&&h(o.value)&&h(w)||H(o)&&""===o.value||""===w||Array.isArray(w)&&!w.length,L=N.bind(null,V,s,k),U=(e,t,r,s=x.maxLength,a=x.minLength)=>{let i=e?t:r;k[V]={type:e?s:a,message:i,ref:o,...L(e?s:a,i)}};if(u?!Array.isArray(w)||!w.length:f&&(!(O||C)&&(j||l(w))||b(w)&&!w||C&&!K(d).isValid||O&&!X(d).isValid)){let{value:e,message:t}=$(f)?{value:!!f,message:f}:ee(f);if(e&&(k[V]={type:x.required,message:t,ref:S,...L(x.required,t)},!s))return D(t),k}if(!j&&(!l(m)||!l(v))){let e,t;let r=ee(v),a=ee(m);if(l(w)||isNaN(w)){let s=o.valueAsDate||new Date(w),i=e=>new Date(new Date().toDateString()+" "+e),l="time"==o.type,u="week"==o.type;T(r.value)&&w&&(e=l?i(w)>i(r.value):u?w>r.value:s>new Date(r.value)),T(a.value)&&w&&(t=l?i(w)<i(a.value):u?w<a.value:s<new Date(a.value))}else{let s=o.valueAsNumber||(w?+w:w);l(r.value)||(e=s>r.value),l(a.value)||(t=s<a.value)}if((e||t)&&(U(!!e,r.message,a.message,x.max,x.min),!s))return D(k[V].message),k}if((c||y)&&!j&&(T(w)||u&&Array.isArray(w))){let e=ee(c),t=ee(y),r=!l(e.value)&&w.length>+e.value,a=!l(t.value)&&w.length<+t.value;if((r||a)&&(U(r,e.message,t.message),!s))return D(k[V].message),k}if(p&&!j&&T(w)){let{value:e,message:t}=ee(p);if(z(e)&&!w.match(e)&&(k[V]={type:x.pattern,message:t,ref:o,...L(x.pattern,t)},!s))return D(t),k}if(_){if(W(_)){let e=Y(await _(w,r),S);if(e&&(k[V]={...e,...L(x.validate,e.message)},!s))return D(e.message),k}else if(n(_)){let e={};for(let t in _){if(!E(e)&&!s)break;let a=Y(await _[t](w,r),S,t);a&&(e={...a,...L(t,a.message)},D(a.message),s&&(k[V]=e))}if(!E(e)&&(k[V]={ref:S,...e},!s))return k}}return D(!0),k};function er(e,t){let r=Array.isArray(t)?t:p(t)?[t]:_(t),s=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,s=0;for(;s<r;)e=h(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,i=r[a];return s&&delete s[i],0!==a&&(n(s)&&E(s)||Array.isArray(s)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!h(e[t]))return!1;return!0}(s))&&er(e,r.slice(0,-1)),e}var es=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},ea=e=>l(e)||!u(e);function ei(e,t){if(ea(e)||ea(t))return e===t;if(i(e)&&i(t))return e.getTime()===t.getTime();let r=Object.keys(e),s=Object.keys(t);if(r.length!==s.length)return!1;for(let a of r){let r=e[a];if(!s.includes(a))return!1;if("ref"!==a){let e=t[a];if(i(r)&&i(e)||n(r)&&n(e)||Array.isArray(r)&&Array.isArray(e)?!ei(r,e):r!==e)return!1}}return!0}var el=e=>"select-multiple"===e.type,eu=e=>G(e)||a(e),en=e=>H(e)&&e.isConnected,eo=e=>{for(let t in e)if(W(e[t]))return!0;return!1};function ed(e,t={}){let r=Array.isArray(e);if(n(e)||r)for(let r in e)Array.isArray(e[r])||n(e[r])&&!eo(e[r])?(t[r]=Array.isArray(e[r])?[]:{},ed(e[r],t[r])):l(e[r])||(t[r]=!0);return t}var ef=(e,t)=>(function e(t,r,s){let a=Array.isArray(t);if(n(t)||a)for(let a in t)Array.isArray(t[a])||n(t[a])&&!eo(t[a])?h(r)||ea(s[a])?s[a]=Array.isArray(t[a])?ed(t[a],[]):{...ed(t[a])}:e(t[a],l(r)?{}:r[a],s[a]):s[a]=!ei(t[a],r[a]);return s})(e,t,ed(t)),ec=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>h(e)?e:t?""===e?NaN:e?+e:e:r&&T(e)?new Date(e):s?s(e):e;function ey(e){let t=e.ref;return I(t)?t.files:G(t)?X(e.refs).value:el(t)?[...t.selectedOptions].map(({value:e})=>e):a(t)?K(e.refs).value:ec(h(t.value)?e.ref.value:t.value,e)}var em=(e,t,r,s)=>{let a={};for(let r of e){let e=g(t,r);e&&V(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}},ev=e=>h(e)?e:z(e)?e.source:n(e)?z(e.value)?e.value.source:e.value:e;let eh="AsyncFunction";var eg=e=>!!e&&!!e.validate&&!!(W(e.validate)&&e.validate.constructor.name===eh||n(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===eh)),eb=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate);function ep(e,t,r){let s=g(e,r);if(s||p(r))return{error:s,name:r};let a=r.split(".");for(;a.length;){let s=a.join("."),i=g(t,s),l=g(e,s);if(i&&!Array.isArray(i)&&r!==s)break;if(l&&l.type)return{name:s,error:l};a.pop()}return{name:r}}var e_=(e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:(r?!s.isOnChange:!a.isOnChange)||e),eV=(e,t)=>!v(g(e,t)).length&&er(e,t);let eF={mode:A.onSubmit,reValidateMode:A.onChange,shouldFocusError:!0};function eA(e={}){let t=s.useRef(void 0),r=s.useRef(void 0),[u,d]=s.useState({isDirty:!1,isValidating:!1,isLoading:W(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,defaultValues:W(e.defaultValues)?void 0:e.defaultValues});t.current||(t.current={...function(e={}){let t,r={...eF,...e},s={submitCount:0,isDirty:!1,isLoading:W(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},u={},d=(n(r.defaultValues)||n(r.values))&&m(r.defaultValues||r.values)||{},c=r.shouldUnregister?{}:m(d),p={action:!1,mount:!1,watch:!1},_={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},x=0,w={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},S={values:es(),array:es(),state:es()},D=M(r.mode),k=M(r.reValidateMode),O=r.criteriaMode===A.all,j=e=>t=>{clearTimeout(x),x=setTimeout(e,t)},L=async e=>{if(!r.disabled&&(w.isValid||e)){let e=r.resolver?E((await J()).errors):await K(u,!0);e!==s.isValid&&S.state.next({isValid:e})}},B=(e,t)=>{!r.disabled&&(w.isValidating||w.validatingFields)&&((e||Array.from(_.mount)).forEach(e=>{e&&(t?V(s.validatingFields,e,t):er(s.validatingFields,e))}),S.state.next({validatingFields:s.validatingFields,isValidating:!E(s.validatingFields)}))},N=(e,t)=>{V(s.errors,e,t),S.state.next({errors:s.errors})},$=(e,t,r,s)=>{let a=g(u,e);if(a){let i=g(c,e,h(r)?g(d,e):r);h(i)||s&&s.defaultChecked||t?V(c,e,t?i:ey(a._f)):Y(e,i),p.mount&&L()}},G=(e,t,a,i,l)=>{let n=!1,o=!1,f={name:e};if(!r.disabled){let r=!!(g(u,e)&&g(u,e)._f&&g(u,e)._f.disabled);if(!a||i){w.isDirty&&(o=s.isDirty,s.isDirty=f.isDirty=Q(),n=o!==f.isDirty);let a=r||ei(g(d,e),t);o=!!(!r&&g(s.dirtyFields,e)),a||r?er(s.dirtyFields,e):V(s.dirtyFields,e,!0),f.dirtyFields=s.dirtyFields,n=n||w.dirtyFields&&!a!==o}if(a){let t=g(s.touchedFields,e);t||(V(s.touchedFields,e,a),f.touchedFields=s.touchedFields,n=n||w.touchedFields&&t!==a)}n&&l&&S.state.next(f)}return n?f:{}},z=(e,a,i,l)=>{let u=g(s.errors,e),n=w.isValid&&b(a)&&s.isValid!==a;if(r.delayError&&i?(t=j(()=>N(e,i)))(r.delayError):(clearTimeout(x),t=null,i?V(s.errors,e,i):er(s.errors,e)),(i?!ei(u,i):u)||!E(l)||n){let t={...l,...n&&b(a)?{isValid:a}:{},errors:s.errors,name:e};s={...s,...t},S.state.next(t)}},J=async e=>{B(e,!0);let t=await r.resolver(c,r.context,em(e||_.mount,u,r.criteriaMode,r.shouldUseNativeValidation));return B(e),t},Z=async e=>{let{errors:t}=await J(e);if(e)for(let r of e){let e=g(t,r);e?V(s.errors,r,e):er(s.errors,r)}else s.errors=t;return t},K=async(e,t,a={valid:!0})=>{for(let i in e){let l=e[i];if(l){let{_f:e,...u}=l;if(e){let u=_.array.has(e.name),n=l._f&&eg(l._f);n&&w.validatingFields&&B([i],!0);let o=await et(l,_.disabled,c,O,r.shouldUseNativeValidation&&!t,u);if(n&&w.validatingFields&&B([i]),o[e.name]&&(a.valid=!1,t))break;t||(g(o,e.name)?u?P(s.errors,o,e.name):V(s.errors,e.name,o[e.name]):er(s.errors,e.name))}E(u)||await K(u,t,a)}}return a.valid},Q=(e,t)=>!r.disabled&&(e&&t&&V(c,e,t),!ei(eA(),d)),X=(e,t,r)=>U(e,_,{...p.mount?c:h(t)?d:T(e)?{[e]:t}:t},r,t),Y=(e,t,r={})=>{let s=g(u,e),i=t;if(s){let r=s._f;r&&(r.disabled||V(c,e,ec(t,r)),i=H(r.ref)&&l(t)?"":t,el(r.ref)?[...r.ref.options].forEach(e=>e.selected=i.includes(e.value)):r.refs?a(r.ref)?r.refs.length>1?r.refs.forEach(e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(i)?!!i.find(t=>t===e.value):i===e.value)):r.refs[0]&&(r.refs[0].checked=!!i):r.refs.forEach(e=>e.checked=e.value===i):I(r.ref)?r.ref.value="":(r.ref.value=i,r.ref.type||S.values.next({name:e,values:{...c}})))}(r.shouldDirty||r.shouldTouch)&&G(e,i,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&eh(e)},ee=(e,t,r)=>{for(let s in t){let a=t[s],l=`${e}.${s}`,o=g(u,l);(_.array.has(e)||n(a)||o&&!o._f)&&!i(a)?ee(l,a,r):Y(l,a,r)}},ea=(e,t,r={})=>{let a=g(u,e),i=_.array.has(e),n=m(t);V(c,e,n),i?(S.array.next({name:e,values:{...c}}),(w.isDirty||w.dirtyFields)&&r.shouldDirty&&S.state.next({name:e,dirtyFields:ef(d,c),isDirty:Q(e,n)})):!a||a._f||l(n)?Y(e,n,r):ee(e,n,r),R(e,_)&&S.state.next({...s}),S.values.next({name:p.mount?e:void 0,values:{...c}})},eo=async e=>{p.mount=!0;let a=e.target,l=a.name,n=!0,d=g(u,l),f=e=>{n=Number.isNaN(e)||i(e)&&isNaN(e.getTime())||ei(e,g(c,l,e))};if(d){let i,y;let m=a.type?ey(d._f):o(e),v=e.type===F.BLUR||e.type===F.FOCUS_OUT,h=!eb(d._f)&&!r.resolver&&!g(s.errors,l)&&!d._f.deps||e_(v,g(s.touchedFields,l),s.isSubmitted,k,D),b=R(l,_,v);V(c,l,m),v?(d._f.onBlur&&d._f.onBlur(e),t&&t(0)):d._f.onChange&&d._f.onChange(e);let p=G(l,m,v,!1),A=!E(p)||b;if(v||S.values.next({name:l,type:e.type,values:{...c}}),h)return w.isValid&&("onBlur"===r.mode&&v?L():v||L()),A&&S.state.next({name:l,...b?{}:p});if(!v&&b&&S.state.next({...s}),r.resolver){let{errors:e}=await J([l]);if(f(m),n){let t=ep(s.errors,u,l),r=ep(e,u,t.name||l);i=r.error,l=r.name,y=E(e)}}else B([l],!0),i=(await et(d,_.disabled,c,O,r.shouldUseNativeValidation))[l],B([l]),f(m),n&&(i?y=!1:w.isValid&&(y=await K(u,!0)));n&&(d._f.deps&&eh(d._f.deps),z(l,y,i,p))}},ed=(e,t)=>{if(g(s.errors,t)&&e.focus)return e.focus(),1},eh=async(e,t={})=>{let a,i;let l=C(e);if(r.resolver){let t=await Z(h(e)?e:l);a=E(t),i=e?!l.some(e=>g(t,e)):a}else e?((i=(await Promise.all(l.map(async e=>{let t=g(u,e);return await K(t&&t._f?{[e]:t}:t)}))).every(Boolean))||s.isValid)&&L():i=a=await K(u);return S.state.next({...!T(e)||w.isValid&&a!==s.isValid?{}:{name:e},...r.resolver||!e?{isValid:a}:{},errors:s.errors}),t.shouldFocus&&!i&&q(u,ed,e?l:_.mount),i},eA=e=>{let t={...p.mount?c:d};return h(e)?t:T(e)?g(t,e):e.map(e=>g(t,e))},ex=(e,t)=>({invalid:!!g((t||s).errors,e),isDirty:!!g((t||s).dirtyFields,e),error:g((t||s).errors,e),isValidating:!!g(s.validatingFields,e),isTouched:!!g((t||s).touchedFields,e)}),ew=(e,t,r)=>{let a=(g(u,e,{_f:{}})._f||{}).ref,{ref:i,message:l,type:n,...o}=g(s.errors,e)||{};V(s.errors,e,{...o,...t,ref:a}),S.state.next({name:e,errors:s.errors,isValid:!1}),r&&r.shouldFocus&&a&&a.focus&&a.focus()},eS=(e,t={})=>{for(let a of e?C(e):_.mount)_.mount.delete(a),_.array.delete(a),t.keepValue||(er(u,a),er(c,a)),t.keepError||er(s.errors,a),t.keepDirty||er(s.dirtyFields,a),t.keepTouched||er(s.touchedFields,a),t.keepIsValidating||er(s.validatingFields,a),r.shouldUnregister||t.keepDefaultValue||er(d,a);S.values.next({values:{...c}}),S.state.next({...s,...t.keepDirty?{isDirty:Q()}:{}}),t.keepIsValid||L()},eD=({disabled:e,name:t,field:r,fields:s})=>{(b(e)&&p.mount||e||_.disabled.has(t))&&(e?_.disabled.add(t):_.disabled.delete(t),G(t,ey(r?r._f:g(s,t)._f),!1,!1,!0))},ek=(e,t={})=>{let s=g(u,e),a=b(t.disabled)||b(r.disabled);return V(u,e,{...s||{},_f:{...s&&s._f?s._f:{ref:{name:e}},name:e,mount:!0,...t}}),_.mount.add(e),s?eD({field:s,disabled:b(t.disabled)?t.disabled:r.disabled,name:e}):$(e,!0,t.value),{...a?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:ev(t.min),max:ev(t.max),minLength:ev(t.minLength),maxLength:ev(t.maxLength),pattern:ev(t.pattern)}:{},name:e,onChange:eo,onBlur:eo,ref:a=>{if(a){ek(e,t),s=g(u,e);let r=h(a.value)&&a.querySelectorAll&&a.querySelectorAll("input,select,textarea")[0]||a,i=eu(r),l=s._f.refs||[];(i?!l.find(e=>e===r):r!==s._f.ref)&&(V(u,e,{_f:{...s._f,...i?{refs:[...l.filter(en),r,...Array.isArray(g(d,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),$(e,!1,void 0,r))}else(s=g(u,e,{}))._f&&(s._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(f(_.array,e)&&p.action)&&_.unMount.add(e)}}},eE=()=>r.shouldFocusError&&q(u,ed,_.mount),eO=(e,t)=>async a=>{let i;a&&(a.preventDefault&&a.preventDefault(),a.persist&&a.persist());let l=m(c);if(_.disabled.size)for(let e of _.disabled)V(l,e,void 0);if(S.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await J();s.errors=e,l=t}else await K(u);if(er(s.errors,"root"),E(s.errors)){S.state.next({errors:{}});try{await e(l,a)}catch(e){i=e}}else t&&await t({...s.errors},a),eE(),setTimeout(eE);if(S.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:E(s.errors)&&!i,submitCount:s.submitCount+1,errors:s.errors}),i)throw i},eC=(e,t={})=>{let a=e?m(e):d,i=m(a),l=E(e),n=l?d:i;if(t.keepDefaultValues||(d=a),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([..._.mount,...Object.keys(ef(d,c))])))g(s.dirtyFields,e)?V(n,e,g(c,e)):ea(e,g(n,e));else{if(y&&h(e))for(let e of _.mount){let t=g(u,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(H(e)){let t=e.closest("form");if(t){t.reset();break}}}}u={}}c=r.shouldUnregister?t.keepDefaultValues?m(d):{}:m(n),S.array.next({values:{...n}}),S.values.next({values:{...n}})}_={mount:t.keepDirtyValues?_.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},p.mount=!w.isValid||!!t.keepIsValid||!!t.keepDirtyValues,p.watch=!!r.shouldUnregister,S.state.next({submitCount:t.keepSubmitCount?s.submitCount:0,isDirty:!l&&(t.keepDirty?s.isDirty:!!(t.keepDefaultValues&&!ei(e,d))),isSubmitted:!!t.keepIsSubmitted&&s.isSubmitted,dirtyFields:l?{}:t.keepDirtyValues?t.keepDefaultValues&&c?ef(d,c):s.dirtyFields:t.keepDefaultValues&&e?ef(d,e):t.keepDirty?s.dirtyFields:{},touchedFields:t.keepTouched?s.touchedFields:{},errors:t.keepErrors?s.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&s.isSubmitSuccessful,isSubmitting:!1})},ej=(e,t)=>eC(W(e)?e(c):e,t);return{control:{register:ek,unregister:eS,getFieldState:ex,handleSubmit:eO,setError:ew,_executeSchema:J,_getWatch:X,_getDirty:Q,_updateValid:L,_removeUnmounted:()=>{for(let e of _.unMount){let t=g(u,e);t&&(t._f.refs?t._f.refs.every(e=>!en(e)):!en(t._f.ref))&&eS(e)}_.unMount=new Set},_updateFieldArray:(e,t=[],a,i,l=!0,n=!0)=>{if(i&&a&&!r.disabled){if(p.action=!0,n&&Array.isArray(g(u,e))){let t=a(g(u,e),i.argA,i.argB);l&&V(u,e,t)}if(n&&Array.isArray(g(s.errors,e))){let t=a(g(s.errors,e),i.argA,i.argB);l&&V(s.errors,e,t),eV(s.errors,e)}if(w.touchedFields&&n&&Array.isArray(g(s.touchedFields,e))){let t=a(g(s.touchedFields,e),i.argA,i.argB);l&&V(s.touchedFields,e,t)}w.dirtyFields&&(s.dirtyFields=ef(d,c)),S.state.next({name:e,isDirty:Q(e,t),dirtyFields:s.dirtyFields,errors:s.errors,isValid:s.isValid})}else V(c,e,t)},_updateDisabledField:eD,_getFieldArray:e=>v(g(p.mount?c:d,e,r.shouldUnregister?g(d,e,[]):[])),_reset:eC,_resetDefaultValues:()=>W(r.defaultValues)&&r.defaultValues().then(e=>{ej(e,r.resetOptions),S.state.next({isLoading:!1})}),_updateFormState:e=>{s={...s,...e}},_disableForm:e=>{b(e)&&(S.state.next({disabled:e}),q(u,(t,r)=>{let s=g(u,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach(t=>{t.disabled=s._f.disabled||e}))},0,!1))},_subjects:S,_proxyFormState:w,_setErrors:e=>{s.errors=e,S.state.next({errors:s.errors,isValid:!1})},get _fields(){return u},get _formValues(){return c},get _state(){return p},set _state(value){p=value},get _defaultValues(){return d},get _names(){return _},set _names(value){_=value},get _formState(){return s},set _formState(value){s=value},get _options(){return r},set _options(value){r={...r,...value}}},trigger:eh,register:ek,handleSubmit:eO,watch:(e,t)=>W(e)?S.values.subscribe({next:r=>e(X(void 0,t),r)}):X(e,t,!0),setValue:ea,getValues:eA,reset:ej,resetField:(e,t={})=>{g(u,e)&&(h(t.defaultValue)?ea(e,m(g(d,e))):(ea(e,t.defaultValue),V(d,e,m(t.defaultValue))),t.keepTouched||er(s.touchedFields,e),t.keepDirty||(er(s.dirtyFields,e),s.isDirty=t.defaultValue?Q(e,m(g(d,e))):Q()),!t.keepError&&(er(s.errors,e),w.isValid&&L()),S.state.next({...s}))},clearErrors:e=>{e&&C(e).forEach(e=>er(s.errors,e)),S.state.next({errors:e?s.errors:{}})},unregister:eS,setError:ew,setFocus:(e,t={})=>{let r=g(u,e),s=r&&r._f;if(s){let e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&W(e.select)&&e.select())}},getFieldState:ex}}(e),formState:u});let c=t.current.control;return c._options=e,L({subject:c._subjects.state,next:e=>{O(e,c._proxyFormState,c._updateFormState,!0)&&d({...c._formState})}}),s.useEffect(()=>c._disableForm(e.disabled),[c,e.disabled]),s.useEffect(()=>{if(c._proxyFormState.isDirty){let e=c._getDirty();e!==u.isDirty&&c._subjects.state.next({isDirty:e})}},[c,u.isDirty]),s.useEffect(()=>{e.values&&!ei(e.values,r.current)?(c._reset(e.values,c._options.resetOptions),r.current=e.values,d(e=>({...e}))):c._resetDefaultValues()},[e.values,c]),s.useEffect(()=>{e.errors&&c._setErrors(e.errors)},[e.errors,c]),s.useEffect(()=>{c._state.mount||(c._updateValid(),c._state.mount=!0),c._state.watch&&(c._state.watch=!1,c._subjects.state.next({...c._formState})),c._removeUnmounted()}),s.useEffect(()=>{e.shouldUnregister&&c._subjects.values.next({values:c._getWatch()})},[e.shouldUnregister,c]),t.current.formState=k(u,c),t.current}}};