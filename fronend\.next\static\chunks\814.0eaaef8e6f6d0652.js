"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[814,7649],{12318:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},15452:(e,t,r)=>{r.d(t,{G$:()=>Y,Hs:()=>b,UC:()=>et,VY:()=>en,ZL:()=>Q,bL:()=>K,bm:()=>eo,hE:()=>er,hJ:()=>ee,l9:()=>X});var n=r(12115),o=r(85185),a=r(6101),l=r(46081),i=r(61285),s=r(5845),d=r(19178),u=r(25519),c=r(34378),p=r(28905),f=r(63655),g=r(92293),v=r(93795),m=r(38168),y=r(99708),h=r(95155),x="Dialog",[D,b]=(0,l.A)(x),[j,w]=D(x),R=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[p=!1,f]=(0,s.i)({prop:o,defaultProp:a,onChange:l});return(0,h.jsx)(j,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};R.displayName=x;var A="DialogTrigger",C=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=w(A,r),i=(0,a.s)(t,l.triggerRef);return(0,h.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":S(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});C.displayName=A;var N="DialogPortal",[I,O]=D(N,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=w(N,t);return(0,h.jsx)(I,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,h.jsx)(p.C,{present:r||l.open,children:(0,h.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=N;var k="DialogOverlay",F=n.forwardRef((e,t)=>{let r=O(k,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=w(k,e.__scopeDialog);return a.modal?(0,h.jsx)(p.C,{present:n||a.open,children:(0,h.jsx)(_,{...o,ref:t})}):null});F.displayName=k;var _=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(k,r);return(0,h.jsx)(v.A,{as:y.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,h.jsx)(f.sG.div,{"data-state":S(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",G=n.forwardRef((e,t)=>{let r=O(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=w(P,e.__scopeDialog);return(0,h.jsx)(p.C,{present:n||a.open,children:a.modal?(0,h.jsx)(L,{...o,ref:t}):(0,h.jsx)(M,{...o,ref:t})})});G.displayName=P;var L=n.forwardRef((e,t)=>{let r=w(P,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,m.Eq)(e)},[]),(0,h.jsx)(T,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),M=n.forwardRef((e,t)=>{let r=w(P,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,h.jsx)(T,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(l=r.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let i=t.target;(null===(l=r.triggerRef.current)||void 0===l?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),T=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=w(P,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,h.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":S(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)($,{titleId:c.titleId}),(0,h.jsx)(z,{contentRef:p,descriptionId:c.descriptionId})]})]})}),q="DialogTitle",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(q,r);return(0,h.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});B.displayName=q;var H="DialogDescription",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(H,r);return(0,h.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});V.displayName=H;var Z="DialogClose",U=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=w(Z,r);return(0,h.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function S(e){return e?"open":"closed"}U.displayName=Z;var W="DialogTitleWarning",[Y,J]=(0,l.q)(W,{contentName:P,titleName:q,docsSlug:"dialog"}),$=e=>{let{titleId:t}=e,r=J(W),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},z=e=>{let{contentRef:t,descriptionId:r}=e,o=J("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(a)},[a,t,r]),null},K=R,X=C,Q=E,ee=F,et=G,er=B,en=V,eo=U},17649:(e,t,r)=>{r.d(t,{UC:()=>P,VY:()=>T,ZD:()=>L,ZL:()=>F,bL:()=>E,hE:()=>M,hJ:()=>_,l9:()=>k,rc:()=>G});var n=r(12115),o=r(46081),a=r(6101),l=r(15452),i=r(85185),s=r(99708),d=r(95155),u="AlertDialog",[c,p]=(0,o.A)(u,[l.Hs]),f=(0,l.Hs)(),g=e=>{let{__scopeAlertDialog:t,...r}=e,n=f(t);return(0,d.jsx)(l.bL,{...n,...r,modal:!0})};g.displayName=u;var v=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=f(r);return(0,d.jsx)(l.l9,{...o,...n,ref:t})});v.displayName="AlertDialogTrigger";var m=e=>{let{__scopeAlertDialog:t,...r}=e,n=f(t);return(0,d.jsx)(l.ZL,{...n,...r})};m.displayName="AlertDialogPortal";var y=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=f(r);return(0,d.jsx)(l.hJ,{...o,...n,ref:t})});y.displayName="AlertDialogOverlay";var h="AlertDialogContent",[x,D]=c(h),b=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:o,...u}=e,c=f(r),p=n.useRef(null),g=(0,a.s)(t,p),v=n.useRef(null);return(0,d.jsx)(l.G$,{contentName:h,titleName:j,docsSlug:"alert-dialog",children:(0,d.jsx)(x,{scope:r,cancelRef:v,children:(0,d.jsxs)(l.UC,{role:"alertdialog",...c,...u,ref:g,onOpenAutoFocus:(0,i.m)(u.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=v.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(s.xV,{children:o}),(0,d.jsx)(O,{contentRef:p})]})})})});b.displayName=h;var j="AlertDialogTitle",w=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=f(r);return(0,d.jsx)(l.hE,{...o,...n,ref:t})});w.displayName=j;var R="AlertDialogDescription",A=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=f(r);return(0,d.jsx)(l.VY,{...o,...n,ref:t})});A.displayName=R;var C=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=f(r);return(0,d.jsx)(l.bm,{...o,...n,ref:t})});C.displayName="AlertDialogAction";var N="AlertDialogCancel",I=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:o}=D(N,r),i=f(r),s=(0,a.s)(t,o);return(0,d.jsx)(l.bm,{...i,...n,ref:s})});I.displayName=N;var O=e=>{let{contentRef:t}=e,r="`".concat(h,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(h,"` by passing a `").concat(R,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(h,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return n.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},E=g,k=v,F=m,_=y,P=b,G=C,L=I,M=w,T=A},34835:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])}}]);