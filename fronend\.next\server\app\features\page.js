(()=>{var e={};e.id=6944,e.ids=[6944],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,r,t)=>{let{createProxy:a}=t(39844);e.exports=a("F:\\trae\\cardmees\\fronend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17720:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26373:(e,r,t)=>{"use strict";t.d(r,{A:()=>d});var a=t(61120);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:s,className:n="",children:d,iconNode:l,...c},x)=>(0,a.createElement)("svg",{ref:x,...o,width:r,height:r,stroke:e,strokeWidth:s?24*Number(t)/Number(r):t,className:i("lucide",n),...c},[...l.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(d)?d:[d]])),d=(e,r)=>{let t=(0,a.forwardRef)(({className:t,...o},d)=>(0,a.createElement)(n,{ref:d,iconNode:r,className:i(`lucide-${s(e)}`,t),...o}));return t.displayName=`${e}`,t}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32127:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(26373).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},33873:e=>{"use strict";e.exports=require("path")},40918:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(26373).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41382:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(26373).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},47692:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var a=t(37413);t(61120);var s=t(4536),i=t.n(s),o=t(41382),n=t(40918),d=t(53148),l=t(65766),c=t(72845),x=t(32127),p=t(75234);let g=(0,t(26373).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),h=function(){let e=[{id:"student-management",title:"学员管理",description:"完整的学员档案管理，包括个人信息、学习进度、成绩记录等全方位跟踪",icon:o.A,color:"blue",gradient:"from-blue-500 to-indigo-600",bgColor:"bg-blue-100 dark:bg-blue-900/30",iconColor:"text-blue-600 dark:text-blue-400",features:["学员档案管理","学习进度跟踪","成绩记录统计","家长沟通记录","个性化学习计划"]},{id:"course-scheduling",title:"课程安排",description:"智能排课系统，支持教师、教室、时间的自动匹配，避免冲突，提高效率",icon:n.A,color:"green",gradient:"from-green-500 to-emerald-600",bgColor:"bg-green-100 dark:bg-green-900/30",iconColor:"text-green-600 dark:text-green-400",features:["智能排课算法","冲突检测提醒","教室资源管理","课程时间优化","批量排课操作"]},{id:"attendance-management",title:"考勤管理",description:"精准的考勤统计，支持多种签到方式，实时监控出勤率，生成详细报表",icon:d.A,color:"orange",gradient:"from-orange-500 to-red-600",bgColor:"bg-orange-100 dark:bg-orange-900/30",iconColor:"text-orange-600 dark:text-orange-400",features:["多种签到方式","实时出勤监控","考勤报表生成","异常考勤提醒","出勤率统计分析"]},{id:"financial-management",title:"财务管理",description:"完整的收支管理，学费收缴、工资发放、费用统计，财务状况一目了然",icon:l.A,color:"purple",gradient:"from-purple-500 to-pink-600",bgColor:"bg-purple-100 dark:bg-purple-900/30",iconColor:"text-purple-600 dark:text-purple-400",features:["学费收缴管理","工资发放系统","费用统计分析","财务报表生成","收支流水记录"]},{id:"grade-management",title:"成绩管理",description:"多维度成绩录入与分析，支持各类考试成绩管理，生成学习报告",icon:c.A,color:"teal",gradient:"from-teal-500 to-cyan-600",bgColor:"bg-teal-100 dark:bg-teal-900/30",iconColor:"text-teal-600 dark:text-teal-400",features:["成绩录入管理","多维度分析","学习报告生成","成绩趋势跟踪","排名统计功能"]},{id:"organization-management",title:"组织管理",description:"机构架构管理，部门设置，权限分配，让管理层级清晰有序",icon:x.A,color:"indigo",gradient:"from-indigo-500 to-blue-600",bgColor:"bg-indigo-100 dark:bg-indigo-900/30",iconColor:"text-indigo-600 dark:text-indigo-400",features:["机构架构设置","部门权限管理","员工角色分配","组织层级管理","权限控制系统"]}];return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50 backdrop-blur-sm bg-opacity-90 dark:bg-opacity-90",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-8 w-8 text-indigo-600 dark:text-indigo-400"}),(0,a.jsxs)("span",{className:"text-xl font-bold text-gray-800 dark:text-white",children:[(0,a.jsx)("span",{className:"text-indigo-600 dark:text-indigo-400",children:"Card"}),"Mees"]})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(i(),{href:"/",className:"text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors",children:"返回首页"}),(0,a.jsx)(i(),{href:"/dashboard",className:"px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-lg transition-colors",children:"进入系统"})]})]})})}),(0,a.jsx)("div",{className:"bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 dark:from-gray-800 dark:via-gray-900 dark:to-gray-800 py-16",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white sm:text-5xl",children:"功能模块详解"}),(0,a.jsx)("p",{className:"mt-4 text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"深入了解 CardMees 卡蜜助教的六大核心功能模块，为您的教育管理提供全方位解决方案"})]})}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:e.map((e,r)=>{let t=e.icon;return(0,a.jsx)("div",{className:"group relative",children:(0,a.jsxs)("div",{className:"relative p-8 bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-700",children:[(0,a.jsx)("div",{className:`absolute inset-0 bg-gradient-to-r ${e.gradient} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-300`}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[(0,a.jsx)("div",{className:`w-16 h-16 ${e.bgColor} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`,children:(0,a.jsx)(t,{className:`w-8 h-8 ${e.iconColor}`})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:e.description})]})]}),(0,a.jsx)("div",{className:"space-y-3 mb-6",children:e.features.map((r,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:`w-2 h-2 bg-gradient-to-r ${e.gradient} rounded-full`}),(0,a.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:r})]},t))}),(0,a.jsxs)(i(),{href:`/features/${e.id}`,className:`inline-flex items-center space-x-2 px-6 py-3 bg-gradient-to-r ${e.gradient} text-white rounded-lg hover:shadow-lg transition-all duration-300 group-hover:scale-105`,children:[(0,a.jsx)("span",{children:"查看详情"}),(0,a.jsx)(g,{className:"w-4 h-4 group-hover:translate-x-1 transition-transform duration-300"})]})]})]})},e.id)})})}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-indigo-600 to-purple-700 dark:from-indigo-800 dark:to-purple-900",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"准备开始使用 CardMees 了吗？"}),(0,a.jsx)("p",{className:"text-xl text-indigo-200 mb-8",children:"立即体验完整的教育管理解决方案"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(i(),{href:"/dashboard",className:"px-8 py-4 bg-white text-indigo-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors",children:"立即开始"}),(0,a.jsx)(i(),{href:"/",className:"px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-indigo-600 transition-colors",children:"了解更多"})]})]})})]})}},52568:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85814,23))},52585:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>l});var a=t(65239),s=t(48088),i=t(88170),o=t.n(i),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(r,d);let l={children:["",{children:["features",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,47692)),"F:\\trae\\cardmees\\fronend\\src\\app\\features\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["F:\\trae\\cardmees\\fronend\\src\\app\\features\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/features/page",pathname:"/features",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},53148:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(26373).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65766:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(26373).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},72845:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(26373).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},74075:e=>{"use strict";e.exports=require("zlib")},75234:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(26373).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,7392,5814,3019],()=>t(52585));module.exports=a})();