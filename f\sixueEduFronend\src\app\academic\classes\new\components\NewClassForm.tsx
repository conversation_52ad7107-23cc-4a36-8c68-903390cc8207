"use client"

import { useForm } from "react-hook-form"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import type { z } from "zod"
import { Form } from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useClasses } from "@/hooks/useClasses"
import { customToast } from "@/lib/toast"

import BasicInfoForm from "./BasicInfoForm"
import ScheduleSettingsForm from "./ScheduleSettingsForm"
import ClassTypeForm from "./ClassTypeForm"
import AdvancedOptionsForm from "./AdvancedOptionsForm"
import formSchema from "../schema/form"
import TeacherSelectForm from "./teacher-select-form"

export default function NewClassForm() {
  // 将 useClasses 钩子移到组件顶层
  const { createClasses } = useClasses()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      recurrenceType: "weekly",
      weekdays: [],
      endType: "number_of_times",
      maxStudentCount: "",
      type: "fixed",
      // 高级选项默认值
      reservation: {
        enabled: false,
        appointmentStartTime: 24, // 默认24小时
        appointmentEndTime: 1, // 默认1小时
      },
      attendance: {
        studentScan: false,
        autoSystem: false,
      },
      leave: {
        enabled: false,
        leaveDeadline: 2, // 默认2小时
      },
      isShowWeekCount: false,
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    const data = {
      endType: values.endType,
      startDate: values.startDate?.getTime(),
      endDate: values.endDate?.getTime(),
      name: values.name,
      courseId: values.courseId,
      daily: values.daily,
      teacherId: values.teacherId,
      classroomId: !values.classroom || values.classroom === "none" ? null : values.classroom,
      recurrenceType: values.recurrenceType,
      weekdays: values.weekdays,
      times: values.times,
      type: values.type,
      maxStudentCount: values.maxStudentCount,
      // 高级选项
      reservation: values.reservation,
      attendance: values.attendance,
      leave: values.leave,
      isShowWeekCount: values.isShowWeekCount,
    }

    createClasses(data).then((res) => {
      console.log(res)
      customToast.success("班级创建成功.")
    })
  }

  return (
    <div>
      <Card className="border border-slate-200 shadow-sm overflow-hidden rounded-md">
        <CardHeader className="bg-slate-50 px-6 py-4 border-b border-slate-200">
          <CardTitle className="text-lg font-medium text-slate-800">班级排课</CardTitle>
          <CardDescription className="text-slate-500 mt-1 text-sm">创建新的班级并安排课程时间</CardDescription>
        </CardHeader>
        <CardContent className="p-6 space-y-8">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* 基础信息表单 */}
              <section>
                <BasicInfoForm form={form} />
              </section>

              {/* 排课设置表单 */}
              <section>
                <ScheduleSettingsForm form={form} />
              </section>

              {/* 班级类型表单 */}
              <section>
                <ClassTypeForm form={form} />
              </section>

              {/* 教师选择 */}
              <section>
               <TeacherSelectForm form={form} />
              </section>

              {/* 高级选项表单 */}
              <section>
                <AdvancedOptionsForm form={form} />
              </section>

              <div className="pt-2">
                <Button
                  type="submit"
                  className="w-full h-10 text-sm font-medium bg-slate-800 hover:bg-slate-700 transition-colors rounded-md"
                >
                  创建班级
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
