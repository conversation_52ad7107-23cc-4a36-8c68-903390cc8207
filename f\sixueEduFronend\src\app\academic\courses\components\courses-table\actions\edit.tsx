'use client';
import React from 'react'
import { Pencil } from 'lucide-react';
import CourseForm from '../../course-form';
import useCourses from '@/hooks/useCourses';
import { customToast } from '@/lib/toast';
import { TooltipIconButton } from '@/components/ui/tooltip-icon-button';


function ActionEdit({ courses }: { courses: any }) {
  const { updateCourse } = useCourses()
  const [open, setOpen] = React.useState(false);
  const handleOk = (data: any) => {
    delete data.ProductCourse
    updateCourse(courses.id, data).then(res => {
      customToast.success('编辑课程成功.')
      setOpen(false)
    })

  }
  return (
    <>
      <TooltipIconButton
        icon={Pencil}
        tooltipText="编辑课程"
        onClick={() => setOpen(true)}
      />
      {
        open &&       <CourseForm
        visible={open}
        onOk={handleOk}
        onCancel={() => setOpen(false)}
        initialValues={courses}
      />
      }

    </>
  )
}

export default ActionEdit