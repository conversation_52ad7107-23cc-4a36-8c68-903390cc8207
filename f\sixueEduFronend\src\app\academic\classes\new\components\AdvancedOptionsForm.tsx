"use client"

import { useState } from "react"
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, ChevronUp, Settings } from "lucide-react"
import type { z } from "zod"
import type { UseFormReturn } from "react-hook-form"
import type formSchema from "../schema/form"

interface AdvancedOptionsFormProps {
  form: UseFormReturn<z.infer<typeof formSchema>>
}

export default function AdvancedOptionsForm({ form }: AdvancedOptionsFormProps) {
  const [advancedOptionsOpen, setAdvancedOptionsOpen] = useState(false)

  const isReservationEnabled = form.watch("reservation.enabled")
  const isLeaveEnabled = form.watch("leave.enabled")

  return (
    <Collapsible
      open={advancedOptionsOpen}
      onOpenChange={setAdvancedOptionsOpen}
      className="border border-slate-200 rounded-md shadow-sm overflow-hidden"
    >
      <CollapsibleTrigger className="flex w-full items-center justify-between p-3 text-left hover:bg-slate-50 transition-colors">
        <div className="flex items-center gap-2">
          <Settings className="h-4 w-4 text-slate-500" />
          <h3 className="text-sm font-medium text-slate-700">高级选项</h3>
        </div>
        <div className="h-6 w-6 flex items-center justify-center text-slate-400">
          {advancedOptionsOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent className="px-4 pb-4 pt-2 space-y-5 border-t border-slate-200">
        {/* 预约设置 */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm text-slate-700 mt-2">预约设置</h4>
          <FormField
            control={form.control}
            name="reservation.enabled"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between p-3 rounded-md border border-slate-200">
                <div>
                  <FormLabel className="text-sm font-medium text-slate-700">开放预约</FormLabel>
                  <FormDescription className="text-xs text-slate-500 mt-1">允许学员预约此班级的课程</FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    className="data-[state=checked]:bg-slate-700"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {isReservationEnabled && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3 pl-4 border-l border-slate-200">
              <FormField
                control={form.control}
                name="reservation.appointmentStartTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-slate-700">开放预约时间（小时）</FormLabel>
                    <FormDescription className="text-xs text-slate-500 mt-1">
                      课程开始前多少小时开放预约
                    </FormDescription>
                    <FormControl>
                      <input
                        type="number"
                        placeholder="如：24小时"
                        className="w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"
                        value={field.value ?? ""}
                        onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="reservation.appointmentEndTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-slate-700">截止预约时间（小时）</FormLabel>
                    <FormDescription className="text-xs text-slate-500 mt-1">
                      课程开始前多少小时停止预约
                    </FormDescription>
                    <FormControl>
                      <input
                        type="number"
                        placeholder="如：1小时"
                        className="w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"
                        value={field.value ?? ""}
                        onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>
          )}
        </div>

        <Separator className="bg-slate-200" />

        {/* 考勤选择 */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm text-slate-700">考勤选择</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <FormField
              control={form.control}
              name="attendance.studentScan"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3">
                  <div>
                    <FormLabel className="text-sm font-medium text-slate-700">学员扫码考勤</FormLabel>
                    <FormDescription className="text-xs text-slate-500 mt-1">
                      允许学员通过扫码进行课程签到
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-slate-700"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="attendance.autoSystem"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3">
                  <div>
                    <FormLabel className="text-sm font-medium text-slate-700">系统自动考勤</FormLabel>
                    <FormDescription className="text-xs text-slate-500 mt-1">系统自动完成考勤流程</FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-slate-700"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>

        <Separator className="bg-slate-200" />

        {/* 请假选项 */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm text-slate-700">请假选项</h4>
          <FormField
            control={form.control}
            name="leave.enabled"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between p-3 rounded-md border border-slate-200">
                <div>
                  <FormLabel className="text-sm font-medium text-slate-700">开放请假</FormLabel>
                  <FormDescription className="text-xs text-slate-500 mt-1">允许学员请假</FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    className="data-[state=checked]:bg-slate-700"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {isLeaveEnabled && (
            <div className="pl-4 border-l border-slate-200">
              <FormField
                control={form.control}
                name="leave.leaveDeadline"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-slate-700">请假截止时间（小时）</FormLabel>
                    <FormDescription className="text-xs text-slate-500 mt-1">
                      课程开始前多少小时停止请假
                    </FormDescription>
                    <FormControl>
                      <input
                        type="number"
                        placeholder="如：2小时"
                        className="w-full h-10 mt-1.5 text-sm text-slate-700 bg-transparent border-b border-slate-200 focus:border-slate-400 focus:outline-none px-0 py-1"
                        value={field.value ?? ""}
                        onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>
          )}
        </div>

        <Separator className="bg-slate-200" />

        {/* 周期数选项 */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm text-slate-700">周期设置</h4>
          <FormField
            control={form.control}
            name="isShowWeekCount"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between space-x-2 rounded-md border border-slate-200 p-3">
                <div>
                  <FormLabel className="text-sm font-medium text-slate-700">启用周期数</FormLabel>
                  <FormDescription className="text-xs text-slate-500 mt-1">
                    按周期划分课程，例如：第一周期、第二周期等
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    className="data-[state=checked]:bg-slate-700"
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}

