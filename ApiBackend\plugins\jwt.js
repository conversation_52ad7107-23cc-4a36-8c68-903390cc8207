import fp from "fastify-plugin";
import jwt from "@fastify/jwt";

async function jwtPlugin(fastify, options) {
    await fastify.register(jwt, {
        secret: fastify.config.JWT_SECRET,
        sign: {
            expiresIn: '24h',
            algorithm: 'HS256',
            issuer: 'sixue-api'
        },
        verify: {
            algorithms: ['HS256'],
            issuer: 'sixue-api'
        },
        decode: {
            complete: false
        }
    })

    fastify.decorate('generateToken', async(user) =>{
        if (!user || !user.id) {
            fastify.log.error({ actor: 'JWT', user }, 'Invalid user data for token generation');
            throw new Error('Invalid user data for token generation');
        }
        const payload = {
            id: user.id,
            account: user.account
        }
        try {
            return await fastify.jwt.sign(payload);
        } catch (error) {
            fastify.log.error({ actor: 'JWT', error: error.message, payload }, 'Token generation failed');
            throw new Error('generate token error');
        }
    })
    fastify.decorate('generateRefreshToken', async(user) =>{
        const payload = {
            ...user
        }
        try {
            return await fastify.jwt.sign(payload, { expiresIn: '7d' });
        } catch (error) {
            fastify.log.error({ actor: 'JWT' }, `generate refresh token error: ${error}`);
            throw new Error('generate refresh token error');
        }
    })
}

export default fp(jwtPlugin, {
    name: 'jwt-plugin',
    dependencies: ['env-plugin']
})