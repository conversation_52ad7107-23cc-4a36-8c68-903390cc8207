"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5620],{9428:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(19946).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},13052:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},28905:(e,n,r)=>{r.d(n,{C:()=>u});var t=r(12115),o=r(6101),a=r(52712),u=e=>{let{present:n,children:r}=e,u=function(e){var n,r;let[o,u]=t.useState(),l=t.useRef({}),d=t.useRef(e),s=t.useRef("none"),[c,f]=(n=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,n)=>{let t=r[e][n];return null!=t?t:e},n));return t.useEffect(()=>{let e=i(l.current);s.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let n=l.current,r=d.current;if(r!==e){let t=s.current,o=i(n);e?f("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?f("UNMOUNT"):r&&t!==o?f("ANIMATION_OUT"):f("UNMOUNT"),d.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let n;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,t=e=>{let t=i(l.current).includes(e.animationName);if(e.target===o&&t&&(f("ANIMATION_END"),!d.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(s.current=i(l.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",t),o.addEventListener("animationend",t),()=>{r.clearTimeout(n),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",t),o.removeEventListener("animationend",t)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:t.useCallback(e=>{e&&(l.current=getComputedStyle(e)),u(e)},[])}}(n),l="function"==typeof r?r({present:u.isPresent}):t.Children.only(r),d=(0,o.s)(u.ref,function(e){var n,r;let t=null===(n=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===n?void 0:n.get,o=t&&"isReactWarning"in t&&t.isReactWarning;return o?e.ref:(o=(t=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||u.isPresent?t.cloneElement(l,{ref:d}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}u.displayName="Presence"},42355:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(19946).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},48698:(e,n,r)=>{r.d(n,{H_:()=>e2,UC:()=>e5,YJ:()=>e9,q7:()=>e8,VF:()=>e7,JU:()=>e6,ZL:()=>e1,z6:()=>e3,hN:()=>e4,bL:()=>eQ,wv:()=>ne,Pb:()=>nn,G5:()=>nt,ZP:()=>nr,l9:()=>e0});var t=r(12115),o=r(85185),a=r(6101),u=r(46081),i=r(5845),l=r(63655),d=r(82284),s=r(94315),c=r(19178),f=r(92293),p=r(25519),v=r(61285),m=r(35152),h=r(34378),g=r(28905),w=r(89196),x=r(99708),y=r(39033),b=r(38168),C=r(93795),M=r(95155),R=["Enter"," "],j=["ArrowUp","PageDown","End"],D=["ArrowDown","PageUp","Home",...j],N={ltr:[...R,"ArrowRight"],rtl:[...R,"ArrowLeft"]},_={ltr:["ArrowLeft"],rtl:["ArrowRight"]},I="Menu",[k,E,T]=(0,d.N)(I),[P,O]=(0,u.A)(I,[T,m.Bk,w.RG]),A=(0,m.Bk)(),S=(0,w.RG)(),[F,L]=P(I),[G,K]=P(I),U=e=>{let{__scopeMenu:n,open:r=!1,children:o,dir:a,onOpenChange:u,modal:i=!0}=e,l=A(n),[d,c]=t.useState(null),f=t.useRef(!1),p=(0,y.c)(u),v=(0,s.jH)(a);return t.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",n,{capture:!0,once:!0}),document.addEventListener("pointermove",n,{capture:!0,once:!0})},n=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",n,{capture:!0}),document.removeEventListener("pointermove",n,{capture:!0})}},[]),(0,M.jsx)(m.bL,{...l,children:(0,M.jsx)(F,{scope:n,open:r,onOpenChange:p,content:d,onContentChange:c,children:(0,M.jsx)(G,{scope:n,onClose:t.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:v,modal:i,children:o})})})};U.displayName=I;var B=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=A(r);return(0,M.jsx)(m.Mz,{...o,...t,ref:n})});B.displayName="MenuAnchor";var V="MenuPortal",[X,q]=P(V,{forceMount:void 0}),W=e=>{let{__scopeMenu:n,forceMount:r,children:t,container:o}=e,a=L(V,n);return(0,M.jsx)(X,{scope:n,forceMount:r,children:(0,M.jsx)(g.C,{present:r||a.open,children:(0,M.jsx)(h.Z,{asChild:!0,container:o,children:t})})})};W.displayName=V;var H="MenuContent",[z,Z]=P(H),Y=t.forwardRef((e,n)=>{let r=q(H,e.__scopeMenu),{forceMount:t=r.forceMount,...o}=e,a=L(H,e.__scopeMenu),u=K(H,e.__scopeMenu);return(0,M.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.C,{present:t||a.open,children:(0,M.jsx)(k.Slot,{scope:e.__scopeMenu,children:u.modal?(0,M.jsx)(J,{...o,ref:n}):(0,M.jsx)($,{...o,ref:n})})})})}),J=t.forwardRef((e,n)=>{let r=L(H,e.__scopeMenu),u=t.useRef(null),i=(0,a.s)(n,u);return t.useEffect(()=>{let e=u.current;if(e)return(0,b.Eq)(e)},[]),(0,M.jsx)(Q,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),$=t.forwardRef((e,n)=>{let r=L(H,e.__scopeMenu);return(0,M.jsx)(Q,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),Q=t.forwardRef((e,n)=>{let{__scopeMenu:r,loop:u=!1,trapFocus:i,onOpenAutoFocus:l,onCloseAutoFocus:d,disableOutsidePointerEvents:s,onEntryFocus:v,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:R,disableOutsideScroll:N,..._}=e,I=L(H,r),k=K(H,r),T=A(r),P=S(r),O=E(r),[F,G]=t.useState(null),U=t.useRef(null),B=(0,a.s)(n,U,I.onContentChange),V=t.useRef(0),X=t.useRef(""),q=t.useRef(0),W=t.useRef(null),Z=t.useRef("right"),Y=t.useRef(0),J=N?C.A:t.Fragment,$=N?{as:x.DX,allowPinchZoom:!0}:void 0,Q=e=>{var n,r;let t=X.current+e,o=O().filter(e=>!e.disabled),a=document.activeElement,u=null===(n=o.find(e=>e.ref.current===a))||void 0===n?void 0:n.textValue,i=function(e,n,r){var t;let o=n.length>1&&Array.from(n).every(e=>e===n[0])?n[0]:n,a=(t=Math.max(r?e.indexOf(r):-1,0),e.map((n,r)=>e[(t+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let u=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==r?u:void 0}(o.map(e=>e.textValue),t,u),l=null===(r=o.find(e=>e.textValue===i))||void 0===r?void 0:r.ref.current;!function e(n){X.current=n,window.clearTimeout(V.current),""!==n&&(V.current=window.setTimeout(()=>e(""),1e3))}(t),l&&setTimeout(()=>l.focus())};t.useEffect(()=>()=>window.clearTimeout(V.current),[]),(0,f.Oh)();let ee=t.useCallback(e=>{var n,r;return Z.current===(null===(n=W.current)||void 0===n?void 0:n.side)&&function(e,n){return!!n&&function(e,n){let{x:r,y:t}=e,o=!1;for(let e=0,a=n.length-1;e<n.length;a=e++){let u=n[e].x,i=n[e].y,l=n[a].x,d=n[a].y;i>t!=d>t&&r<(l-u)*(t-i)/(d-i)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},n)}(e,null===(r=W.current)||void 0===r?void 0:r.area)},[]);return(0,M.jsx)(z,{scope:r,searchRef:X,onItemEnter:t.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:t.useCallback(e=>{var n;ee(e)||(null===(n=U.current)||void 0===n||n.focus(),G(null))},[ee]),onTriggerLeave:t.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:q,onPointerGraceIntentChange:t.useCallback(e=>{W.current=e},[]),children:(0,M.jsx)(J,{...$,children:(0,M.jsx)(p.n,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.m)(l,e=>{var n;e.preventDefault(),null===(n=U.current)||void 0===n||n.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,M.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:R,children:(0,M.jsx)(w.bL,{asChild:!0,...P,dir:k.dir,orientation:"vertical",loop:u,currentTabStopId:F,onCurrentTabStopIdChange:G,onEntryFocus:(0,o.m)(v,e=>{k.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,M.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eN(I.open),"data-radix-menu-content":"",dir:k.dir,...T,..._,ref:B,style:{outline:"none",..._.style},onKeyDown:(0,o.m)(_.onKeyDown,e=>{let n=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!r&&t&&Q(e.key));let o=U.current;if(e.target!==o||!D.includes(e.key))return;e.preventDefault();let a=O().filter(e=>!e.disabled).map(e=>e.ref.current);j.includes(e.key)&&a.reverse(),function(e){let n=document.activeElement;for(let r of e)if(r===n||(r.focus(),document.activeElement!==n))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(V.current),X.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,ek(e=>{let n=e.target,r=Y.current!==e.clientX;e.currentTarget.contains(n)&&r&&(Z.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});Y.displayName=H;var ee=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,M.jsx)(l.sG.div,{role:"group",...t,ref:n})});ee.displayName="MenuGroup";var en=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,M.jsx)(l.sG.div,{...t,ref:n})});en.displayName="MenuLabel";var er="MenuItem",et="menu.itemSelect",eo=t.forwardRef((e,n)=>{let{disabled:r=!1,onSelect:u,...i}=e,d=t.useRef(null),s=K(er,e.__scopeMenu),c=Z(er,e.__scopeMenu),f=(0,a.s)(n,d),p=t.useRef(!1);return(0,M.jsx)(ea,{...i,ref:f,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=d.current;if(!r&&e){let n=new CustomEvent(et,{bubbles:!0,cancelable:!0});e.addEventListener(et,e=>null==u?void 0:u(e),{once:!0}),(0,l.hO)(e,n),n.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:n=>{var r;null===(r=e.onPointerDown)||void 0===r||r.call(e,n),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var n;p.current||null===(n=e.currentTarget)||void 0===n||n.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=""!==c.searchRef.current;!r&&(!n||" "!==e.key)&&R.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eo.displayName=er;var ea=t.forwardRef((e,n)=>{let{__scopeMenu:r,disabled:u=!1,textValue:i,...d}=e,s=Z(er,r),c=S(r),f=t.useRef(null),p=(0,a.s)(n,f),[v,m]=t.useState(!1),[h,g]=t.useState("");return t.useEffect(()=>{let e=f.current;if(e){var n;g((null!==(n=e.textContent)&&void 0!==n?n:"").trim())}},[d.children]),(0,M.jsx)(k.ItemSlot,{scope:r,disabled:u,textValue:null!=i?i:h,children:(0,M.jsx)(w.q7,{asChild:!0,...c,focusable:!u,children:(0,M.jsx)(l.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...d,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,ek(e=>{u?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ek(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),eu=t.forwardRef((e,n)=>{let{checked:r=!1,onCheckedChange:t,...a}=e;return(0,M.jsx)(ev,{scope:e.__scopeMenu,checked:r,children:(0,M.jsx)(eo,{role:"menuitemcheckbox","aria-checked":e_(r)?"mixed":r,...a,ref:n,"data-state":eI(r),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!e_(r)||!r),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[el,ed]=P(ei,{value:void 0,onValueChange:()=>{}}),es=t.forwardRef((e,n)=>{let{value:r,onValueChange:t,...o}=e,a=(0,y.c)(t);return(0,M.jsx)(el,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,M.jsx)(ee,{...o,ref:n})})});es.displayName=ei;var ec="MenuRadioItem",ef=t.forwardRef((e,n)=>{let{value:r,...t}=e,a=ed(ec,e.__scopeMenu),u=r===a.value;return(0,M.jsx)(ev,{scope:e.__scopeMenu,checked:u,children:(0,M.jsx)(eo,{role:"menuitemradio","aria-checked":u,...t,ref:n,"data-state":eI(u),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});ef.displayName=ec;var ep="MenuItemIndicator",[ev,em]=P(ep,{checked:!1}),eh=t.forwardRef((e,n)=>{let{__scopeMenu:r,forceMount:t,...o}=e,a=em(ep,r);return(0,M.jsx)(g.C,{present:t||e_(a.checked)||!0===a.checked,children:(0,M.jsx)(l.sG.span,{...o,ref:n,"data-state":eI(a.checked)})})});eh.displayName=ep;var eg=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,M.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:n})});eg.displayName="MenuSeparator";var ew=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=A(r);return(0,M.jsx)(m.i3,{...o,...t,ref:n})});ew.displayName="MenuArrow";var ex="MenuSub",[ey,eb]=P(ex),eC=e=>{let{__scopeMenu:n,children:r,open:o=!1,onOpenChange:a}=e,u=L(ex,n),i=A(n),[l,d]=t.useState(null),[s,c]=t.useState(null),f=(0,y.c)(a);return t.useEffect(()=>(!1===u.open&&f(!1),()=>f(!1)),[u.open,f]),(0,M.jsx)(m.bL,{...i,children:(0,M.jsx)(F,{scope:n,open:o,onOpenChange:f,content:s,onContentChange:c,children:(0,M.jsx)(ey,{scope:n,contentId:(0,v.B)(),triggerId:(0,v.B)(),trigger:l,onTriggerChange:d,children:r})})})};eC.displayName=ex;var eM="MenuSubTrigger",eR=t.forwardRef((e,n)=>{let r=L(eM,e.__scopeMenu),u=K(eM,e.__scopeMenu),i=eb(eM,e.__scopeMenu),l=Z(eM,e.__scopeMenu),d=t.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:c}=l,f={__scopeMenu:e.__scopeMenu},p=t.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return t.useEffect(()=>p,[p]),t.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),c(null)}},[s,c]),(0,M.jsx)(B,{asChild:!0,...f,children:(0,M.jsx)(ea,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":i.contentId,"data-state":eN(r.open),...e,ref:(0,a.t)(n,i.onTriggerChange),onClick:n=>{var t;null===(t=e.onClick)||void 0===t||t.call(e,n),e.disabled||n.defaultPrevented||(n.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,ek(n=>{l.onItemEnter(n),n.defaultPrevented||e.disabled||r.open||d.current||(l.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ek(e=>{var n,t;p();let o=null===(n=r.content)||void 0===n?void 0:n.getBoundingClientRect();if(o){let n=null===(t=r.content)||void 0===t?void 0:t.dataset.side,a="right"===n,u=o[a?"left":"right"],i=o[a?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:i,y:o.top},{x:i,y:o.bottom},{x:u,y:o.bottom}],side:n}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,n=>{let t=""!==l.searchRef.current;if(!e.disabled&&(!t||" "!==n.key)&&N[u.dir].includes(n.key)){var o;r.onOpenChange(!0),null===(o=r.content)||void 0===o||o.focus(),n.preventDefault()}})})})});eR.displayName=eM;var ej="MenuSubContent",eD=t.forwardRef((e,n)=>{let r=q(H,e.__scopeMenu),{forceMount:u=r.forceMount,...i}=e,l=L(H,e.__scopeMenu),d=K(H,e.__scopeMenu),s=eb(ej,e.__scopeMenu),c=t.useRef(null),f=(0,a.s)(n,c);return(0,M.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.C,{present:u||l.open,children:(0,M.jsx)(k.Slot,{scope:e.__scopeMenu,children:(0,M.jsx)(Q,{id:s.contentId,"aria-labelledby":s.triggerId,...i,ref:f,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var n;d.isUsingKeyboardRef.current&&(null===(n=c.current)||void 0===n||n.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=e.currentTarget.contains(e.target),r=_[d.dir].includes(e.key);if(n&&r){var t;l.onOpenChange(!1),null===(t=s.trigger)||void 0===t||t.focus(),e.preventDefault()}})})})})})});function eN(e){return e?"open":"closed"}function e_(e){return"indeterminate"===e}function eI(e){return e_(e)?"indeterminate":e?"checked":"unchecked"}function ek(e){return n=>"mouse"===n.pointerType?e(n):void 0}eD.displayName=ej;var eE="DropdownMenu",[eT,eP]=(0,u.A)(eE,[O]),eO=O(),[eA,eS]=eT(eE),eF=e=>{let{__scopeDropdownMenu:n,children:r,dir:o,open:a,defaultOpen:u,onOpenChange:l,modal:d=!0}=e,s=eO(n),c=t.useRef(null),[f=!1,p]=(0,i.i)({prop:a,defaultProp:u,onChange:l});return(0,M.jsx)(eA,{scope:n,triggerId:(0,v.B)(),triggerRef:c,contentId:(0,v.B)(),open:f,onOpenChange:p,onOpenToggle:t.useCallback(()=>p(e=>!e),[p]),modal:d,children:(0,M.jsx)(U,{...s,open:f,onOpenChange:p,dir:o,modal:d,children:r})})};eF.displayName=eE;var eL="DropdownMenuTrigger",eG=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,disabled:t=!1,...u}=e,i=eS(eL,r),d=eO(r);return(0,M.jsx)(B,{asChild:!0,...d,children:(0,M.jsx)(l.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...u,ref:(0,a.t)(n,i.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{t||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eG.displayName=eL;var eK=e=>{let{__scopeDropdownMenu:n,...r}=e,t=eO(n);return(0,M.jsx)(W,{...t,...r})};eK.displayName="DropdownMenuPortal";var eU="DropdownMenuContent",eB=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...a}=e,u=eS(eU,r),i=eO(r),l=t.useRef(!1);return(0,M.jsx)(Y,{id:u.contentId,"aria-labelledby":u.triggerId,...i,...a,ref:n,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var n;l.current||null===(n=u.triggerRef.current)||void 0===n||n.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let n=e.detail.originalEvent,r=0===n.button&&!0===n.ctrlKey,t=2===n.button||r;(!u.modal||t)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eB.displayName=eU;var eV=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,M.jsx)(ee,{...o,...t,ref:n})});eV.displayName="DropdownMenuGroup";var eX=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,M.jsx)(en,{...o,...t,ref:n})});eX.displayName="DropdownMenuLabel";var eq=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,M.jsx)(eo,{...o,...t,ref:n})});eq.displayName="DropdownMenuItem";var eW=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,M.jsx)(eu,{...o,...t,ref:n})});eW.displayName="DropdownMenuCheckboxItem";var eH=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,M.jsx)(es,{...o,...t,ref:n})});eH.displayName="DropdownMenuRadioGroup";var ez=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,M.jsx)(ef,{...o,...t,ref:n})});ez.displayName="DropdownMenuRadioItem";var eZ=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,M.jsx)(eh,{...o,...t,ref:n})});eZ.displayName="DropdownMenuItemIndicator";var eY=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,M.jsx)(eg,{...o,...t,ref:n})});eY.displayName="DropdownMenuSeparator",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,M.jsx)(ew,{...o,...t,ref:n})}).displayName="DropdownMenuArrow";var eJ=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,M.jsx)(eR,{...o,...t,ref:n})});eJ.displayName="DropdownMenuSubTrigger";var e$=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,M.jsx)(eD,{...o,...t,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e$.displayName="DropdownMenuSubContent";var eQ=eF,e0=eG,e1=eK,e5=eB,e9=eV,e6=eX,e8=eq,e2=eW,e3=eH,e4=ez,e7=eZ,ne=eY,nn=e=>{let{__scopeDropdownMenu:n,children:r,open:t,onOpenChange:o,defaultOpen:a}=e,u=eO(n),[l=!1,d]=(0,i.i)({prop:t,defaultProp:a,onChange:o});return(0,M.jsx)(eC,{...u,open:l,onOpenChange:d,children:r})},nr=eJ,nt=e$},63655:(e,n,r)=>{r.d(n,{hO:()=>l,sG:()=>i});var t=r(12115),o=r(47650),a=r(99708),u=r(95155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,n)=>{let r=t.forwardRef((e,r)=>{let{asChild:t,...o}=e,i=t?a.DX:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(i,{...o,ref:r})});return r.displayName=`Primitive.${n}`,{...e,[n]:r}},{});function l(e,n){e&&o.flushSync(()=>e.dispatchEvent(n))}},89196:(e,n,r)=>{r.d(n,{RG:()=>b,bL:()=>k,q7:()=>E});var t=r(12115),o=r(85185),a=r(82284),u=r(6101),i=r(46081),l=r(61285),d=r(63655),s=r(39033),c=r(5845),f=r(94315),p=r(95155),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,w,x]=(0,a.N)(h),[y,b]=(0,i.A)(h,[x]),[C,M]=y(h),R=t.forwardRef((e,n)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(j,{...e,ref:n})})}));R.displayName=h;var j=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:i=!1,dir:l,currentTabStopId:h,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:x,onEntryFocus:y,preventScrollOnEntryFocus:b=!1,...M}=e,R=t.useRef(null),j=(0,u.s)(n,R),D=(0,f.jH)(l),[N=null,_]=(0,c.i)({prop:h,defaultProp:g,onChange:x}),[k,E]=t.useState(!1),T=(0,s.c)(y),P=w(r),O=t.useRef(!1),[A,S]=t.useState(0);return t.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(v,T),()=>e.removeEventListener(v,T)},[T]),(0,p.jsx)(C,{scope:r,orientation:a,dir:D,loop:i,currentTabStopId:N,onItemFocus:t.useCallback(e=>_(e),[_]),onItemShiftTab:t.useCallback(()=>E(!0),[]),onFocusableItemAdd:t.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>S(e=>e-1),[]),children:(0,p.jsx)(d.sG.div,{tabIndex:k||0===A?-1:0,"data-orientation":a,...M,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let n=!O.current;if(e.target===e.currentTarget&&n&&!k){let n=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(n),!n.defaultPrevented){let e=P().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===N),...e].filter(Boolean).map(e=>e.ref.current),b)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>E(!1))})})}),D="RovingFocusGroupItem",N=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:u=!1,tabStopId:i,...s}=e,c=(0,l.B)(),f=i||c,v=M(D,r),m=v.currentTabStopId===f,h=w(r),{onFocusableItemAdd:x,onFocusableItemRemove:y}=v;return t.useEffect(()=>{if(a)return x(),()=>y()},[a,x,y]),(0,p.jsx)(g.ItemSlot,{scope:r,id:f,focusable:a,active:u,children:(0,p.jsx)(d.sG.span,{tabIndex:m?0:-1,"data-orientation":v.orientation,...s,ref:n,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let n=function(e,n,r){var t;let o=(t=e.key,"rtl"!==r?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===n&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===n&&["ArrowUp","ArrowDown"].includes(o)))return _[o]}(e,v.orientation,v.dir);if(void 0!==n){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===n)r.reverse();else if("prev"===n||"next"===n){"prev"===n&&r.reverse();let t=r.indexOf(e.currentTarget);r=v.loop?function(e,n){return e.map((r,t)=>e[(n+t)%e.length])}(r,t+1):r.slice(t+1)}setTimeout(()=>I(r))}})})})});N.displayName=D;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let t of e)if(t===r||(t.focus({preventScroll:n}),document.activeElement!==r))return}var k=R,E=N}}]);