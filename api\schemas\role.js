export const roleGetListSchemas = {
    tags: ['roles'],
    summary: '获取所有角色列表',
    description: '获取所有角色列表',
    response: {
        200:{
            type: "object",
            properties: {
                code: { type: 'number' },
                message: { type: 'string' },
                data: {
                    type: "array",
                    items: {
                        type: "object",
                        properties: {
                            id: { type: "string" },
                            name: { type: "string" },
                            code: { type: "string" },
                            description: { type: "string" },
                            createdAt: { type: "string" },
                        }
                    }
                }
            }
        }
    }
}

export const roleCreateSchemas = {
    tags: ['roles'],
    summary: '创建新角色',
    description: '创建新角色',
    body: {
        type: 'object',
        required: ['name'],
        properties: {
            name: {
                type: 'string',
            },
            description: {
                type: 'string'
            }
        }
    },
    response: {
        200: {
            type: 'object',
            properties: {
                code: { type: 'number' },
                message: { type: 'string' },
                data: {
                    type: 'object',
                    properties: {
                        id: { type: "string" },
                        name: { type: "string" },
                        code: { type: "string" },
                        description: { type: "string" },
                        createdAt: { type: "string" },
                    }
                }
            }
        }
    }
}