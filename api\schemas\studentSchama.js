export const studentSchema = {
	tags: ['students'],
	summary: '获取学生列表',
	querystring: {
		type: 'object',
		properties: {
			page: { type: 'string', default: 1 },
			pageSize: { type: 'string', default: 10 },
			search: { type: 'string' },
			follower: { type: 'string' },
			intention: { type: 'string' },
			intentLevel: { type: 'string' },
			type: {
				type: 'string',
				enum: ['formal', 'intent', 'public', 'graduated', 'all'],
				default: 'formal'
			},
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'object',
					properties: {
						total: { type: 'number' },
						page: { type: 'number' },
						pageSize: { type: 'number' },
						list: {
							type: 'array',
							items: {
								type: 'object',
								properties: {
									id: { type: 'string' },
									name: { type: 'string' },
									phone: { type: 'number' },
									gender: { type: 'string' },
									address: { type: 'string' },
									remarks: { type: 'string' },
									type: { type: 'string' },
									intentLevel: { type: 'string' },
									followUpDate: { type: 'string' },
									birthday: { type: 'number' },
									status: { type: 'string' },
									source: { type: 'string' },
									sourceDesc: { type: 'string' },
									createdAt: { type: 'number' },
									followerId: { type: 'string' },
									followerName: { type: 'string' },
								}
							}
						}
					}
				}
			}
		}
	}
}
// 获取单个学生信息
export const studentByIdSchema = {
	tags: ['students'],
	summary: '获取单个学生信息',
	params: {
		type: 'object',
		properties: {
			studentId: { type: 'string' }
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'object',
					properties: {
						id: { type: 'string' },
						name: { type: 'string' },
						phone: { type: 'string' },
						gender: { type: 'string' },
						address: { type: 'string' },
						remarks: { type: 'string' },
						type: { type: 'string' },
						intentLevel: { type: 'string' },
						followUpDate: { type: 'string' },
						birthday: { type: 'number' },
						status: { type: 'string' },
						source: { type: 'string' },
						sourceDesc: { type: 'string' },
						createdAt: { type: 'string' },
						followerId: { type: 'string' },
						followerName: { type: 'string' }
					}
				}
			}
		}
	}
}
// 更新学生信息
export const updateStudentSchema = {
	tags: ['students'],
	summary: '更新学生信息',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: {
				type: 'string'
			}
		}
	},
	body: {
		type: 'object',
		properties: {
			name: { type: 'string' },
			gender: { type: 'string' },
			age: { type: 'string' },
			birthday: { type: 'string' },
			phone: { type: 'string' },
			email: { type: 'string' },
			balance: { type: 'string' },
			points: { type: 'string' },
			followUpPerson: { type: 'string' },
			followUpDate: { type: 'string' },
			source: { type: 'string' },
			referrer: { type: 'string' },
			address: { type: 'string' },
			idCard: { type: 'string' },
			school: { type: 'string' },
			intentionLevel: { type: 'string' },
			parentName: { type: 'string' },
			status: { type: 'string' },
			type: { type: 'string' },
			remark: { type: 'string' },
		},
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: { type: 'object' }
			}
		}
	}
}
// 批量删除学员
export const deleteStudentSchema = {
	tags: ['students'],
	summary: '批量删除学员',
	body: {
		type: 'object',
		properties: {
			studentIds: { type: 'array', items: { type: 'string' } }
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: { type: 'object' }
			}
		}
	}
}
// 获取学生上课记录
export const classesHistorySchema = {
	tags: ['students'],
	summary: '获取学生上课记录',
	params: {
		type: 'object',
		properties: {
			studentId: { type: 'string' }
		}
	},
	querystring: {
		type: 'object',
		properties: {
			page: { type: 'string', default: 1 },
			pageSize: { type: 'string', default: 10 },
			startDate: { type: 'string' },
			endDate: { type: 'string' }
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'object',
					properties: {
						list: {
							type: 'array', items: {
								type: 'object',
								properties: {
									id: { type: 'string' },
									status: { type: 'string' },
									scheduleId: { type: 'string' },
									subject: { type: 'string' },
									startDate: { type: 'string' },
									startTime: { type: 'string' },
									endTime: { type: 'string' },
									// classId: { type: 'string' },
									className: { type: 'string' },
									// courseId: { type: 'string' },
									courseName: { type: 'string' },
									// teacherId: { type: 'string' },
									teacherName: { type: 'string' }
								}
							}
						},
						total: { type: 'number' },
						page: { type: 'number' },
						pageSize: { type: 'number' }
					}
				}
			}
		}
	}
}
// 获取学生购买套餐
export const productsSchema = {
	tags: ['students'],
	summary: '获取学生购买套餐',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	querystring: {
		type: 'object',
		properties: {
			status: {
				type: 'string',
				default: ''
			}
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'array',
					items: {
						type: 'object',
						properties: {
							id: { type: 'string' },
							startDate: { type: 'number' },
							endDate: { type: 'number' },
							totalSessionCount: { type: 'number' },
							remainingSessionCount: { type: 'number' },
							enrollmentStatus: { type: 'string' },
							productId: { type: 'string' },
							productName: { type: 'string' },
							productPackageType: { type: 'string' },
						}
					}
				}
			}
		}
	}
}
// 获取学生购买记录
export const productsRecordsSchema = {
	tags: ['students'],
	summary: '获取学生购买记录',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'array',
					items: {
						type: 'object',
						properties: {
							id: { type: 'string' },
							amount: { type: 'number' },
							amountPaid: { type: 'number' },
							amountUnpaid: { type: 'number' },
							paymentTime: { type: 'number' },
							paymentMethod: { type: 'string' },
							discount: { type: 'number' },
							giftCount: { type: 'number' },
							giftDays: { type: 'number' },
							purchaseQuantity: { type: 'number' },
							status: { type: 'string' },
							createdAt: { type: 'string' },
							studentProductId: { type: 'string' },
							studentProductStartDate: { type: 'number' },
							studentProductEndDate: { type: 'number' },
							studentProductTotalSessionCount: { type: 'number' },
							studentProductRemainingSessionCount: { type: 'number' },
							studentProductEnrollmentStatus: { type: 'string' },
							studentProductPaymentStatus: { type: 'string' },
							studentProductPaymentTime: { type: 'string' },
							productId: { type: 'string' },
							productName: { type: 'string' },
							productPackageType: { type: 'string' }
						}
					}
				}
			}
		}
	}
}
// 获取学生考勤记录
export const attendanceSchema = {
	tags: ['students'],
	summary: '获取学生考勤记录',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	querystring: {
		type: 'object',
		properties: {
			page: { type: 'number', default: 1 },
			pageSize: { type: 'number', default: 10 },
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'object',
					properties: {
						list: {
							type: 'array',
							items: {
								type: 'object',
								properties: {
									id: { type: 'string' },
									status: { type: 'string' },
									attendanceCount: { type: 'number' },
									operatorTime: { type: 'number' },
									operatorName: { type: 'string' },
									studentId: { type: 'string' },
									studentName: { type: 'string' },
									productName: { type: 'string' },
									scheduleId: { type: 'string' },
									startDate: { type: 'number' },
									startTime: { type: 'string' },
									endTime: { type: 'string' },
									subject: { type: 'string' },
									courseName: { type: 'string' },
									classesName: { type: 'string' }
								}
							}
						},
						total: { type: 'number' },
						page: { type: 'number' },
						pageSize: { type: 'number' }
					}
				}
			}
		}
	}
}
// 获取学员班级
export const classesSchema = {
	tags: ['students'],
	summary: '获取学员班级',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'array',
					items: {
						type: 'object',
						properties: {
							id: { type: 'string' },
							joinDate: { type: 'number' },
							operatorTime: { type: 'number' },
							type: { type: 'string' },
							classesId: { type: 'string' },
							classesName: { type: 'string' },
							courseName: { type: 'string' },
							teacherName: { type: 'string' }
						}
					}
				}
			}
		}
	}
}

// 学员退出班级
export const outClassesSchema = {
	tags: ['students'],
	summary: '学员退出班级',
	params: {
		type: 'object',
		required: ['classesId', 'studentId'],
		properties: {
			classesId: { type: 'string' },
			studentId: { type: 'string' }
		}
	},

}

// 获取学员跟进记录
export const getFollowRecordsSchema = {
	tags: ['students'],
	summary: '获取学员跟进记录',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
}
// 新增学员跟进记录
export const addFollowRecordsSchema = {
	tags: ['students'],
	summary: '新增学员跟进记录',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	body: {
		type: 'object',
		properties: {
			nextFollowUpDate: { type: 'number' },
			followUpDate: { type: 'number' },
			followUpContent: { type: 'string' },
			followUpUserId: { type: 'string' },
			intentLevel: { type: 'string' },
		}
	}
}
// 创建学生产品
export const createStudentProductSchema = {
	tags: ['students'],
	summary: '创建学生产品',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	body: {
		type: 'object',
		properties: {
			productId: { type: 'string' },
			amount: { type: 'number' },
			prepaidAmount: { type: 'number' }, // 支付金额
			balance: { type: 'number' }, // 为0时，表示全额支付
			bonusLessons: { type: 'number' }, // 赠送课时
			dateTime: { type: 'number' }, // 支付时间
			remarks: { type: 'string', default: '' },
			payment: { type: 'string' }, // 支付方式
			salesRep: { type: 'string' }, // 销售代表
		}
	}
}
// 更新学生产品
export const updateStudentProductSchema = {
	tags: ['students'],
	summary: '更新学生产品',
	params: {
		type: 'object',
		required: ['studentId', 'studentProductId'],
		properties: {
			studentId: { type: 'string' },
			studentProductId: { type: 'string' }
		}
	},
	body: {
		type: 'object',
		properties: {
			status: { type: 'string' },
      totalCount: { type: 'string' },
		}
	}
}
// 创建学员跟进人
export const createFollowUpSchema = {
	tags: ['students'],
	summary: '创建学员跟进人',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	body: {
		type: 'object',
		properties: {
			followUpDate: { type: 'number' },
			followUpContent: { type: 'string' },
		}
	}
}
// 获取简易学员列表
export const simpleStudentListSchema = {
	tags: ['students'],
	summary: '获取简易学员列表',
	querystring: {
		type: 'object',
		properties: {
			page: { type: 'number', default: 1 },
			pageSize: { type: 'number', default: 10 },
			search: { type: 'string' },
		}
	}
}






export default {
	studentSchema,
	studentByIdSchema,
	updateStudentSchema,
	deleteStudentSchema,
	classesHistorySchema,
	productsSchema,
	productsRecordsSchema,
	attendanceSchema,
	classesSchema,
	outClassesSchema,
	getFollowRecordsSchema,
	addFollowRecordsSchema,
	createStudentProductSchema,
	updateStudentProductSchema
}

