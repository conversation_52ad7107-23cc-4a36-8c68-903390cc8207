import { studentController } from '../controllers/studentController.js';
import studentSchema from '../schemas/studentSchama.js';

export default async function (fastify, opts) {
    // 获取学员列表
    fastify.get('/students', {
        schema: studentSchema.studentSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getStudentList
    });

    // 获取简易学员列表
    fastify.get('/students/simple', {
        schema: studentSchema.simpleStudentListSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getSimpleStudentList
    });

    // 获取单个学生信息
    fastify.get('/students/:studentId', {
        schema: studentSchema.studentByIdSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getStudentById
    });

    // 更新学员
    fastify.put('/students/:studentId', {
        schema: studentSchema.updateStudentSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.updateStudent
    })

    // 批量删除学员
    fastify.delete('/students', {
        schema: studentSchema.deleteStudentSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.deleteStudent
    })

    // 获取学生上课记录
    fastify.get('/students/:studentId/classesHistory', {
        schema: studentSchema.classesHistorySchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getClassesHistory
    })

    // 获取学生购买套餐
    fastify.get('/students/:studentId/products', {
        schema: studentSchema.productsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getProducts
    })
    
    // 获取学生购买记录
    fastify.get('/students/:studentId/products/records', {
        schema: studentSchema.productsRecordsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getProductsRecords
    })

    // 获取学生考勤记录
    fastify.get('/students/:studentId/attendance', {
        schema: studentSchema.attendanceSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getAttendance
    })

    // 获取学员班级
    fastify.get('/students/:studentId/classes', {
        schema: studentSchema.classesSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getClasses
    })

    // 学员退出班级
    fastify.delete('/students/:studentId/classes/:classesId', {
        schema: studentSchema.outClassesSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.outClasses
    })

    // 获取学员跟进记录
    fastify.get('/students/:studentId/followRecords', {
        schema: studentSchema.getFollowRecordsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getFollowRecords
    })

    // 新增学员跟进记录
    fastify.post('/students/:studentId/followRecords', {
        schema: studentSchema.addFollowRecordsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.addFollowRecords
    })

    // 创建学生产品
    fastify.post('/students/:studentId/products', {
        schema: studentSchema.createStudentProductSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.createStudentProduct
    })

    // 更新学生产品
    fastify.put('/students/:studentId/products/:studentProductId', {
        schema: studentSchema.updateStudentProductSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.updateStudentProduct
    })

    // 获取学员套餐列表
    fastify.get('/students/products', {
        schema: studentSchema.studentProductsListSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getStudentProducts
    });

    // 获取整体学员产品调整记录
    fastify.get('/students/products-adjustments', {
        schema: studentSchema.getStudentProductAdjustmentsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getStudentProductAdjustments
    })

    // 获取整体学员上课查询
    fastify.get('/students/classes-query', {
        schema: studentSchema.getClassesQuerySchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getClassesQuery
    });

    // 下载学员套餐列表
    // fastify.get('/students/products/download', {
    //     schema: studentSchema.downloadStudentProductsSchema,
    //     onRequest: [fastify.auth.authenticate],
    //     handler: studentController.downloadStudentProducts
    // })

    // 调整学生套餐内容
    fastify.put('/students/products/:studentProductId', {
        schema: studentSchema.updateStudentProductSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.updateStudentProduct
    })

    // 获取学生产品调整记录
    fastify.get('/students/:studentId/products/adjustments', {
        schema: studentSchema.getStudentProductAdjustmentsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getStudentProductAdjustments
    })

    // 获取意向学员列表
    fastify.get('/students/intent', {
        schema: studentSchema.getIntentStudentsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getIntentStudents
    });

    // 获取考勤记录
    fastify.get('/students/attendanceRecords', {
        schema: studentSchema.getAttendanceRecordsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getAttendanceRecords
    });

    // 学员产品退款
    fastify.post('/students/:studentId/products/:productId/refund', {
        schema: studentSchema.refundProductSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.refundProduct
    });

    // 机构添加学员
    fastify.post('/students/add', {
        schema: studentSchema.addStudentSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.addStudent
    });

    // 注册学生
    fastify.post('/students/:institutionId/register', {
        schema: studentSchema.registerStudentSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.registerStudent
    });

    // 学员创建跟进人
    fastify.post('/students/follow-up', {
        schema: studentSchema.createFollowUpPersonSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.createFollowUpPerson
    });

    // 获取跟进记录
    fastify.get('/students/followRecords', {
        schema: {
            tags: ['students'],
            summary: '获取跟进记录',
            querystring: {
                type: 'object',
                properties: {
                    page: { type: 'number', default: 1 },
                    pageSize: { type: 'number', default: 10 },
                    teacherId: { type: 'string' },
                    search: { type: 'string' },
                    endTime: { type: 'number' },
                    startTime: { type: 'number' },
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const user = request.user;
            const { page = 1, pageSize = 10, teacherId, search,
                endTime, startTime } = request.query;
            const skip = (page - 1) * pageSize;
            const take = pageSize;
            const where = {
                institutionId: user.institutionId,
                ...(teacherId && { followUpUserId: teacherId }),
                ...(search && {
                    student: {
                        name: { contains: search, mode: 'insensitive' },
                        phone: { contains: search, mode: 'insensitive' }
                    }
                })
            }
            if (endTime || startTime) {
                where.followUpDate = {
                    lte: Number(endTime),
                    gte: Number(startTime),
                }
            }
            const [result, total] = await Promise.all([
                fastify.prisma.studentFollowRecords.findMany({
                    where,
                    select: {
                        id: true,
                        followUpDate: true,
                        followUpContent: true,
                        nextFollowUpDate: true,
                        followUpUser: {
                            select: {
                                id: true,
                                name: true,
                            }
                        },
                        student: {
                            select: {
                                id: true,
                                name: true,
                                phone: true,
                                gender: true,
                                birthday: true,
                            }
                        }
                    },
                    orderBy: {
                        followUpDate: 'desc',
                    },
                    skip,
                    take
                }),
                fastify.prisma.studentFollowRecords.count({
                    where
                })
            ])

            const serializedResult = result.map(item => ({
                ...item,
                followUpDate: Number(item.followUpDate),
                nextFollowUpDate: Number(item.nextFollowUpDate),
                student: {
                    ...item.student,
                    birthday: Number(item.student.birthday),
                }
            }))
            reply.success({
                data: {
                    list: serializedResult,
                    total: total,
                    page: page,
                    pageSize: pageSize
                },
                message: '获取跟进记录成功.'
            })
        }
    })
}
