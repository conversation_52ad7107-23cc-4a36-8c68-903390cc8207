"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3870],{14636:(e,t,a)=>{a.d(t,{AM:()=>i,Wv:()=>o,hl:()=>c});var s=a(95155),n=a(12115),r=a(20547),l=a(59434);let i=r.bL,o=r.l9,c=n.forwardRef((e,t)=>{let{className:a,align:n="center",sideOffset:i=4,...o}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{ref:t,align:n,sideOffset:i,className:(0,l.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...o})})});c.displayName=r.UC.displayName},45968:(e,t,a)=>{a.d(t,{C:()=>s});function s(e){if(!e)return"-";let t=new Date(e),a=new Date,s=a.getFullYear()-t.getFullYear(),n=a.getMonth()-t.getMonth(),r=a.getDate()-t.getDate();return(n<0||0===n&&r<0)&&s--,String(s)}},48432:(e,t,a)=>{a.r(t),a.d(t,{default:()=>j});var s=a(95155),n=a(12115),r=a(42355),l=a(13052),i=a(5623),o=a(59434),c=a(30285);let d=e=>{let{className:t,...a}=e;return(0,s.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,o.cn)("mx-auto flex w-full justify-center",t),...a})};d.displayName="Pagination";let u=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)("ul",{ref:t,className:(0,o.cn)("flex flex-row items-center gap-1",a),...n})});u.displayName="PaginationContent";let m=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)("li",{ref:t,className:(0,o.cn)("",a),...n})});m.displayName="PaginationItem";let x=e=>{let{className:t,isActive:a,size:n="icon",...r}=e;return(0,s.jsx)("a",{"aria-current":a?"page":void 0,className:(0,o.cn)((0,c.r)({variant:a?"outline":"ghost",size:n}),t),...r})};x.displayName="PaginationLink";let h=e=>{let{className:t,...a}=e;return(0,s.jsxs)(x,{"aria-label":"Go to previous page",size:"default",className:(0,o.cn)("gap-1 pl-2.5",t),...a,children:[(0,s.jsx)(r.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"上一页"})]})};h.displayName="PaginationPrevious";let p=e=>{let{className:t,...a}=e;return(0,s.jsxs)(x,{"aria-label":"Go to next page",size:"default",className:(0,o.cn)("gap-1 pr-2.5",t),...a,children:[(0,s.jsx)("span",{children:"下一页"}),(0,s.jsx)(l.A,{className:"h-4 w-4"})]})};p.displayName="PaginationNext";let f=e=>{let{className:t,...a}=e;return(0,s.jsxs)("span",{"aria-hidden":!0,className:(0,o.cn)("flex h-9 w-9 items-center justify-center",t),...a,children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"更多页"})]})};f.displayName="PaginationEllipsis";var g=a(59409);function j(e){let{currentPage:t,pageSize:a,totalItems:n,onPageChange:i,onPageSizeChange:o}=e,c=Math.ceil(n/a),j=(()=>{let e=[];if(c<=5){for(let t=1;t<=c;t++)e.push(t);return e}e.push(1);let a=Math.max(2,t-1),s=Math.min(t+1,c-1);2===a&&(s=Math.min(a+2,c-1)),s===c-1&&(a=Math.max(s-2,2)),a>2&&e.push("ellipsis-start");for(let t=a;t<=s;t++)e.push(t);return s<c-1&&e.push("ellipsis-end"),c>1&&e.push(c),e})(),N=0===n?0:(t-1)*a+1,v=Math.min(t*a,n);return(0,s.jsxs)("div",{className:"flex flex-col gap-4 px-2 py-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"显示"}),(0,s.jsxs)(g.l6,{value:a.toString(),onValueChange:e=>{o(Number(e))},children:[(0,s.jsx)(g.bq,{className:"w-[4.5rem] h-7 text-xs border-border/60 hover:bg-accent/30 transition-colors",children:(0,s.jsx)(g.yv,{})}),(0,s.jsx)(g.gC,{children:[10,20,30,50].map(e=>(0,s.jsx)(g.eb,{value:e.toString(),className:"text-xs",children:e},e))})]}),(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"条"}),(0,s.jsx)("span",{className:"text-muted-foreground/40 mx-1.5",children:"|"}),n>0?(0,s.jsxs)("span",{className:"text-muted-foreground/80",children:[N,"-",v," / ",n," 条记录"]}):(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"0 条记录"})]}),(0,s.jsx)(d,{children:(0,s.jsxs)(u,{className:"gap-1",children:[(0,s.jsx)(m,{children:(0,s.jsx)(h,{onClick:()=>i(Math.max(1,t-1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(1===t?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,s.jsx)(r.A,{className:"h-4 w-4 mr-1"})})}),j.map((e,a)=>"ellipsis-start"===e||"ellipsis-end"===e?(0,s.jsx)(m,{children:(0,s.jsx)(f,{className:"h-8 w-8 flex items-center justify-center text-xs"})},"ellipsis-".concat(a)):(0,s.jsx)(m,{children:(0,s.jsx)(x,{onClick:()=>i(e),isActive:t===e,className:"h-8 w-8 text-xs font-medium rounded-md transition-colors data-[active]:bg-primary data-[active]:text-primary-foreground hover:bg-accent/50 hover:text-accent-foreground/90","aria-label":"前往第 ".concat(e," 页"),children:e})},e)),(0,s.jsx)(m,{children:(0,s.jsx)(p,{onClick:()=>i(Math.min(c,t+1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(t===c||0===c?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,s.jsx)(l.A,{className:"h-4 w-4 ml-1"})})})]})})]})}},50228:(e,t,a)=>{a.d(t,{P:()=>r,s:()=>l});var s=a(95155),n=a(73069);let r=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e?(0,s.jsx)("div",{className:"text-sm ".concat(t?"text-muted-foreground":""),children:e}):(0,s.jsx)("div",{})},l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return e?(0,s.jsx)(n.c,{maxDisplayLength:t,children:e}):(0,s.jsx)("div",{className:"text-sm text-muted-foreground"})}},62523:(e,t,a)=>{a.d(t,{p:()=>l});var s=a(95155),n=a(12115),r=a(59434);let l=n.forwardRef((e,t)=>{let{className:a,type:n,...l}=e;return(0,s.jsx)("input",{type:n,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...l})});l.displayName="Input"},73069:(e,t,a)=>{a.d(t,{c:()=>m});var s=a(95155),n=a(12115),r=a(47863),l=a(66474),i=a(5196),o=a(24357),c=a(59434),d=a(14636),u=a(30285);function m(e){let{children:t,maxDisplayLength:a=15,className:m,popoverWidth:x="auto",showBorder:h=!1}=e,[p,f]=n.useState(!1),[g,j]=n.useState(!1),N=n.useMemo(()=>{if("string"==typeof t||"number"==typeof t)return t.toString();try{var e;let a=document.createElement("div");return a.innerHTML=(null==t?void 0:null===(e=t.props)||void 0===e?void 0:e.children)||"",a.textContent||""}catch(e){return console.warn("Could not extract text from children:",e),""}},[t]),v=n.useMemo(()=>{if("string"==typeof t||"number"==typeof t){let e=t.toString();return e.length>a?e.slice(0,a):e}return t},[t,a]),b=async()=>{try{await navigator.clipboard.writeText(N),j(!0),setTimeout(()=>j(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,s.jsxs)(d.AM,{open:p,onOpenChange:f,children:[(0,s.jsx)(d.Wv,{asChild:!0,children:(0,s.jsxs)(u.$,{variant:h?"outline":"ghost",role:"combobox","aria-expanded":p,className:(0,c.cn)("w-fit justify-between font-normal px-2 py-1 h-auto",!h&&"border-0 shadow-none",m),children:[(0,s.jsx)("span",{className:"mr-2 truncate",children:v}),p?(0,s.jsx)(r.A,{className:"h-4 w-4 shrink-0 opacity-50"}):(0,s.jsx)(l.A,{className:"h-4 w-4 shrink-0 opacity-50"})]})}),(0,s.jsx)(d.hl,{className:"p-0",align:"start",style:{width:x},children:(0,s.jsxs)("div",{className:"flex items-center gap-2 p-2",children:[(0,s.jsx)("span",{className:"text-sm break-all",children:N}),(0,s.jsxs)(u.$,{variant:"ghost",size:"icon",className:"h-8 w-8 shrink-0",onClick:b,children:[g?(0,s.jsx)(i.A,{className:"h-4 w-4"}):(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:g?"Copied":"Copy text"})]})]})})]})}},79181:(e,t,a)=>{a.d(t,{$:()=>n});var s=function(e){return e.Secret="secret",e.Male="male",e.Female="female",e}(s||{});function n(e){switch(Object.values(s).includes(e)?e:"secret"){case"male":return"男性";case"female":return"女性";case"secret":return"保密";default:return"未知"}}}}]);