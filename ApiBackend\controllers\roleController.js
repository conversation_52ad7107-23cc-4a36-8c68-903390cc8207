import { createError } from '@fastify/error';
import { v4 as uuidv4 } from 'uuid';
import { sendToSpecifiedUser } from '../libs/websocket.js';
import roleService from '../services/roleService.js';

const NOT_FOUND_ERROR = createError('NOT_FOUND_ERROR', '%s', 404);
const VALIDATION_ERROR = createError('VALIDATION_ERROR', '%s', 400);
const INTERNAL_ERROR = createError('INTERNAL_ERROR', '%s', 500);
const AUTH_ERROR = createError('AUTH_ERROR', '%s', 401);

/**
 * Role Controller
 * Handles operations related to roles and role permissions
 */
const roleController = {
  /**
   * Get all roles with pagination and search
   * @param {Object} request - Fastify request object
   * @param {Object} reply - Fastify reply object
   */
  async getAllRoles(request, reply) {
    const { page = 1, pageSize = 10, search } = request.query;
    const user = request.user;

    try {
      const result = await roleService.getAllRoles({
        page: Number(page),
        pageSize: Number(pageSize),
        search,
        institutionId: user.institutionId,
        fastify: request.server
      });

      reply.success({
        data: {
          list: result.roles,
          total: result.total,
          page,
          pageSize
        },
        message: '获取角色列表成功'
      });
    } catch (error) {
      request.log.error(error);
      throw new INTERNAL_ERROR(error.message || '获取角色列表失败');
    }
  },

  /**
   * Create a new role
   * @param {Object} request - Fastify request object
   * @param {Object} reply - Fastify reply object
   */
  async createRole(request, reply) {
    const { name, description } = request.body;
    const { id: userId, SYSTEM_ADMIN: is_super_admin } = request.user;

    try {
      const newRole = await roleService.createRole({
        name,
        description,
        userId,
        is_super_admin,
        fastify: request.server
      });

      reply.success({
        message: '角色创建成功',
        data: {
          ...newRole,
          isInstitutionRole: !is_super_admin
        }
      });
    } catch (error) {
      request.log.error(error);

      if (error instanceof VALIDATION_ERROR) {
        throw error;
      }

      throw new INTERNAL_ERROR(error.message || '创建角色失败');
    }
  },

  /**
   * Delete a role by ID
   * @param {Object} request - Fastify request object
   * @param {Object} reply - Fastify reply object
   */
  async deleteRole(request, reply) {
    const { roleId } = request.params;
    const user = request.user;

    try {
      const result = await roleService.deleteRole({
        roleId,
        userId: user.id,
        institutionId: user.institutionId,
        fastify: request.server
      });

      reply.success({ message: `角色"${result.name}"删除成功` });
    } catch (error) {
      request.log.error(error);

      if (error instanceof VALIDATION_ERROR || error instanceof AUTH_ERROR) {
        throw error;
      }

      throw new INTERNAL_ERROR(error.message || '删除角色失败');
    }
  },

  /**
   * Update a role by ID
   * @param {Object} request - Fastify request object
   * @param {Object} reply - Fastify reply object
   */
  async updateRole(request, reply) {
    const { roleId } = request.params;
    const { name, description } = request.body;
    const user = request.user;

    try {
      const result = await roleService.updateRole({
        roleId,
        name,
        description,
        userId: user.id,
        institutionId: user.institutionId,
        fastify: request.server
      });

      reply.success({ message: `角色 "${result.name}" 更新成功` });
    } catch (error) {
      request.log.error(error);

      if (error instanceof VALIDATION_ERROR || error instanceof AUTH_ERROR) {
        throw error;
      }

      throw new INTERNAL_ERROR(error.message || '角色更新失败');
    }
  },

  // ==================== Role Permission Management ====================

  /**
   * Get permissions for a role
   * @param {Object} request - Fastify request object
   * @param {Object} reply - Fastify reply object
   */
  async getRolePermissions(request, reply) {
    const { roleId } = request.params;

    try {
      // Get the role with its permissions
      const role = await request.server.prisma.role.findUnique({
        where: { id: roleId },
        include: {
          rolePermissions: {
            include: {
              permission: true
            }
          }
        }
      });

      if (!role) {
        throw new NOT_FOUND_ERROR('角色不存在');
      }

      // Format the response
      const permissions = role.rolePermissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        code: rp.permission.code,
        description: rp.permission.description
      }));

      reply.success({
        message: '获取角色权限成功',
        data: {
          role: {
            id: role.id,
            name: role.name,
            code: role.code,
            description: role.description
          },
          permissions
        }
      });
    } catch (error) {
      request.log.error(error);

      if (error instanceof NOT_FOUND_ERROR) {
        throw error;
      }

      throw new INTERNAL_ERROR(error.message || '获取角色权限失败');
    }
  },

  /**
   * Assign permissions to a role
   * @param {Object} request - Fastify request object
   * @param {Object} reply - Fastify reply object
   */
  async assignRolePermissions(request, reply) {
    const { roleId } = request.params;
    const { permissionIds, replaceExisting = false } = request.body;

    try {
      // Validate input
      if (!Array.isArray(permissionIds)) {
        throw new VALIDATION_ERROR('权限ID必须是数组');
      }

      // Get the role
      const role = await request.server.prisma.role.findUnique({
        where: { id: roleId }
      });

      if (!role) {
        throw new NOT_FOUND_ERROR('角色不存在');
      }

      // Get the permissions
      const permissions = await request.server.prisma.permission.findMany({
        where: {
          id: {
            in: permissionIds
          }
        }
      });

      if (permissions.length !== permissionIds.length) {
        const foundIds = permissions.map(p => p.id);
        const missingIds = permissionIds.filter(id => !foundIds.includes(id));
        throw new VALIDATION_ERROR(`以下权限ID不存在: ${missingIds.join(', ')}`);
      }

      // Begin transaction
      const result = await request.server.prisma.$transaction(async (prisma) => {
        // If replacing existing permissions, delete all current role permissions
        if (replaceExisting) {
          await prisma.rolePermission.deleteMany({
            where: { roleId }
          });
        } else {
          // Otherwise, only delete the ones that are being reassigned to avoid unique constraint errors
          await prisma.rolePermission.deleteMany({
            where: {
              roleId,
              permissionId: {
                in: permissionIds
              }
            }
          });
        }

        // Create new role permissions using createMany for better performance
        const createdRolePermissions = await prisma.rolePermission.createMany({
          data: permissionIds.map(permissionId => ({
            id: uuidv4(),
            roleId,
            permissionId
          })),
          skipDuplicates: true
        });

        return { count: createdRolePermissions.count };
      });

      reply.success({
        message: replaceExisting ? '角色权限已替换' : '角色权限已分配',
        data: {
          roleId,
          assignedPermissions: result.count
        }
      });
    } catch (error) {
      request.log.error(error);

      if (error instanceof NOT_FOUND_ERROR || error instanceof VALIDATION_ERROR) {
        throw error;
      }

      throw new INTERNAL_ERROR(error.message || '分配角色权限失败');
    }
  },

  /**
   * Remove permissions from a role
   * @param {Object} request - Fastify request object
   * @param {Object} reply - Fastify reply object
   */
  async removeRolePermissions(request, reply) {
    const { roleId } = request.params;
    const { permissionIds } = request.body;

    try {
      // Validate input
      if (!Array.isArray(permissionIds)) {
        throw new VALIDATION_ERROR('权限ID必须是数组');
      }

      // Get the role
      const role = await request.server.prisma.role.findUnique({
        where: { id: roleId }
      });

      if (!role) {
        throw new NOT_FOUND_ERROR('角色不存在');
      }

      // Remove role permissions
      const result = await request.server.prisma.rolePermission.deleteMany({
        where: {
          roleId,
          permissionId: {
            in: permissionIds
          }
        }
      });

      reply.success({
        message: '角色权限已移除',
        data: {
          roleId,
          removedPermissions: result.count
        }
      });
    } catch (error) {
      request.log.error(error);

      if (error instanceof NOT_FOUND_ERROR || error instanceof VALIDATION_ERROR) {
        throw error;
      }

      throw new INTERNAL_ERROR(error.message || '移除角色权限失败');
    }
  },

  /**
   * Update role permissions based on menu IDs
   * @param {Object} request - Fastify request object
   * @param {Object} reply - Fastify reply object
   */
  async updateRolePermissions(request, reply) {
    const user = request.user;
    const { roleId } = request.params;
    const { permissions } = request.body;

    try {
      // Begin transaction
      return await request.server.prisma.$transaction(async (prisma) => {
        // Check if role exists and belongs to user's institution
        const role = await prisma.role.findUnique({
          where: {
            id: roleId,
            institutionId: user.institutionId
          },
          select: {
            id: true,
            rolePermissions: {
              select: {
                permission: {
                  select: {
                    id: true,
                    code: true
                  }
                }
              }
            }
          }
        });

        if (!role) {
          throw new NOT_FOUND_ERROR('角色不存在');
        }

        // Get exactly the menus specified by IDs - no children included
        const menus = await prisma.menu.findMany({
          where: { id: { in: permissions } },
          select: { id: true, permissionCode: true }
        });

        // Validate all menu IDs exist
        if (menus.length !== permissions.length) {
          throw new VALIDATION_ERROR('部分菜单不存在');
        }

        // Get all current role permissions
        const currentRolePermissions = role.rolePermissions.map(rp => ({
          id: rp.permission.id,
          code: rp.permission.code
        }));

        // Get all permission codes from the explicitly selected menus only
        const selectedPermissionCodes = menus
          .map(menu => menu.permissionCode)
          .filter(Boolean);

        // Get all permission IDs that should be kept (those that match the selected menu permission codes)
        const permissionIdsToKeep = currentRolePermissions
          .filter(p => selectedPermissionCodes.includes(p.code))
          .map(p => p.id);

        // All permissions that aren't in the "keep" list should be deleted
        const permissionsToDelete = currentRolePermissions
          .filter(p => !permissionIdsToKeep.includes(p.id))
          .map(p => p.id);

        // Find permission codes that need to be added (in selected menus but not in current permissions)
        const currentPermissionCodes = currentRolePermissions.map(p => p.code);
        const permissionCodesToAdd = selectedPermissionCodes.filter(
          code => !currentPermissionCodes.includes(code)
        );

        // Get permission IDs for the new permission codes
        const newPermissions = await prisma.permission.findMany({
          where: {
            code: {
              in: permissionCodesToAdd
            }
          },
          select: { id: true }
        });

        // Log for debugging
        request.log.info({
          msg: 'Role permission update details',
          roleId,
          selectedMenuIds: permissions,
          selectedPermissionCodes,
          permissionsToDelete,
          permissionCodesToAdd
        });

        // Delete permissions no longer needed
        if (permissionsToDelete.length > 0) {
          await prisma.rolePermission.deleteMany({
            where: {
              roleId,
              permissionId: { in: permissionsToDelete }
            }
          });
        }

        // Create new role permissions
        if (newPermissions.length > 0) {
          await prisma.rolePermission.createMany({
            data: newPermissions.map(permission => ({
              id: uuidv4(),
              roleId,
              permissionId: permission.id
            })),
            skipDuplicates: true
          });
        }

        // Get users with this role to notify them
        const userRoles = await prisma.userRole.findMany({
          where: { roleId },
          select: { userId: true }
        });

        // Prepare notification payload
        const payload = { type: 'ROLE_PERMISSION_UPDATE' };

        // Send notifications to affected users
        try {
          for (const userRole of userRoles) {
            await sendToSpecifiedUser(request.server, userRole.userId, user.institutionId, payload);
          }
        } catch (error) {
          request.log.warn(`Failed to send notification to users: ${error.message}`);
        }

        return reply.success({
          message: '角色权限菜单更新成功',
          data: {
            roleId,
            updatedMenus: permissions.length,
            removedPermissions: permissionsToDelete.length,
            addedPermissions: newPermissions.length
          }
        });
      });
    } catch (error) {
      request.log.error(error);

      if (error instanceof NOT_FOUND_ERROR || error instanceof VALIDATION_ERROR) {
        throw error;
      }

      throw new INTERNAL_ERROR(error.message || '更新角色权限菜单失败');
    }
  }
};

export default roleController;
