(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4397],{14636:(e,s,a)=>{"use strict";a.d(s,{AM:()=>n,Wv:()=>d,hl:()=>c});var t=a(95155),r=a(12115),i=a(20547),l=a(59434);let n=i.bL,d=i.l9,c=r.forwardRef((e,s)=>{let{className:a,align:r="center",sideOffset:n=4,...d}=e;return(0,t.jsx)(i.ZL,{children:(0,t.jsx)(i.UC,{ref:s,align:r,sideOffset:n,className:(0,l.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...d})})});c.displayName=i.UC.displayName},22346:(e,s,a)=>{"use strict";a.d(s,{w:()=>n});var t=a(95155),r=a(12115),i=a(87489),l=a(59434);let n=r.forwardRef((e,s)=>{let{className:a,orientation:r="horizontal",decorative:n=!0,...d}=e;return(0,t.jsx)(i.b,{ref:s,decorative:n,orientation:r,className:(0,l.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",a),...d})});n.displayName=i.b.displayName},22745:(e,s,a)=>{"use strict";a.d(s,{default:()=>E});var t=a(95155),r=a(12115),i=a(84616),l=a(30285),n=a(60933),d=a(48436),c=a(62523),o=a(59409),m=a(48432),u=a(70639),x=a(9110),p=a(80333),h=a(91347),f=a(90010),g=a(46102),v=a(62525);let j=function(e){let{id:s}=e,[a]=(0,u.lY)(),i=(0,r.useCallback)(async()=>{if(s)try{await a(s),d.l.success("删除产品成功.")}catch(e){d.l.error((null==e?void 0:e.message)||"删除产品失败!")}},[s]);return(0,t.jsx)(h.LQ,{permission:"product:delete",children:(0,t.jsx)(g.Bc,{delayDuration:300,children:(0,t.jsxs)(g.m_,{children:[(0,t.jsx)(g.k$,{asChild:!0,children:(0,t.jsxs)(f.Lt,{children:[(0,t.jsx)(f.tv,{asChild:!0,children:(0,t.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-destructive/90 hover:text-destructive-foreground",children:(0,t.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground hover:text-white"})})}),(0,t.jsxs)(f.EO,{children:[(0,t.jsxs)(f.wd,{children:[(0,t.jsx)(f.r7,{children:"确认删除"}),(0,t.jsx)(f.$v,{children:"您确定要删除该套餐吗？此操作不可撤销。"})]}),(0,t.jsxs)(f.ck,{children:[(0,t.jsx)(f.Zr,{children:"取消"}),(0,t.jsx)(f.Rx,{className:"bg-destructive hover:bg-destructive/90",onClick:i,children:"删除"})]})]})]})}),(0,t.jsx)(g.ZI,{side:"top",className:"font-medium text-xs px-3 py-1.5 bg-background border shadow-sm",children:(0,t.jsx)("p",{children:"删除套餐"})})]})})})};var N=a(49992),b=a(55028),y=a(57001);let w=(0,b.default)(()=>Promise.resolve().then(a.bind(a,60933)),{loadableGenerated:{webpack:()=>[60933]},ssr:!1}),k=function(e){let{data:s}=e,[a]=(0,u.vM)(),[i,l]=(0,r.useState)(!1),n=async e=>{try{await a({id:s.id,product:e}),l(!1)}catch(e){console.error("更新产品失败:",e)}};return(0,t.jsxs)(h.LQ,{permission:"product:update",children:[(0,t.jsx)(y.p,{icon:N.A,tooltipText:"编辑套餐",onClick:()=>l(!0)}),i&&(0,t.jsx)(w,{visible:i,initialValues:s,onOk:n,onCancel:()=>l(!1)},"edit-product-form-".concat(s.id))]})};var C=a(28218);let L=(0,b.default)(()=>Promise.all([a.e(4977),a.e(3716)]).then(a.bind(a,51335)),{loadableGenerated:{webpack:()=>[51335]}}),T=function(e){let{data:s}=e,[a,i]=r.useState(!1);return(0,t.jsxs)(h.LQ,{permission:"product:update",children:[(0,t.jsx)(y.p,{icon:C.A,tooltipText:"绑定课程",onClick:()=>i(!0)}),a&&(0,t.jsx)(L,{open:a,onOpenChange:i,data:s})]})};var z=a(26126),A=a(50228);function R(e){let{id:s,status:a}=e,[r]=(0,u.vM)();return(0,t.jsx)(p.d,{defaultChecked:"active"===a,onCheckedChange:e=>{r({id:s,product:{status:e?"active":"inactive"}}).then(()=>{d.l.success("更新状态成功.")})}})}let S=[{accessorKey:"name",header:"套餐名称",cell:e=>{let{row:s}=e;return(0,t.jsx)("div",{className:"font-medium text-slate-800",children:s.getValue("name")})}},{accessorKey:"packageType",header:"套餐类型",cell:e=>{let{row:s}=e,a=s.getValue("packageType"),r=s.original.usageLimit||0;if("limited-sessions"===a)return(0,t.jsxs)(z.E,{variant:"outline",className:"font-normal px-3 py-1 rounded-full text-xs",children:["限次(",r,"/次)"]});if("limited-time-and-count"===a){let e="daily"===s.original.timeLimitType?"天":"月",a=s.original.timeLimitedUsage||0;return(0,t.jsxs)(z.E,{variant:"secondary",className:"font-normal px-3 py-1 rounded-full text-xs",children:["时次限(",a,"/",e,"、",r,"/次)"]})}return null}},{accessorKey:"price",header:"价格",cell:e=>{let{row:s}=e;return(0,t.jsxs)("div",{className:"text-sm text-slate-600",children:["\xa5",s.getValue("price")]})}},{accessorKey:"leaveCount",header:"请假总次数",cell:e=>{let{row:s}=e;return(0,t.jsx)("div",{className:"text-sm text-slate-600 text-center",children:s.getValue("leaveCount")})}},{accessorKey:"status",header:"状态",cell:e=>{let{row:s}=e,a=s.getValue("status");return(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsx)(R,{id:s.original.id,status:a})})}},{accessorKey:"createdAt",header:"创建时间",cell:e=>{let{row:s}=e;return(0,t.jsx)("div",{className:"text-sm text-slate-600",children:new Date(s.getValue("createdAt")).toLocaleDateString()})}},{accessorKey:"remark",header:"备注",cell:e=>{let{row:s}=e;return(0,A.s)(s.getValue("remark"))}},{id:"actions",header:"操作",cell:e=>{let{row:s}=e,a=s.original;return(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(k,{data:a}),(0,t.jsx)(T,{data:a}),(0,t.jsx)(j,{id:a.id})]})}}];var V=a(45964),P=a.n(V);function E(){let[e]=(0,u.Q$)(),[s,a]=(0,r.useState)("edit"),[p,f]=(0,r.useState)(!1),[g,v]=(0,r.useState)(),[j,N]=(0,r.useState)(""),[b,y]=(0,r.useState)(""),[w,k]=(0,r.useState)({currentPage:1,pageSize:10,total:0}),C=(0,r.useCallback)(P()(e=>{N(e)},500),[]),L=(0,r.useCallback)(e=>{let s=e.target.value;e.target.value=s,C(s)},[C]),T=(0,r.useMemo)(()=>({search:j,page:w.currentPage,pageSize:w.pageSize,type:"all"===b?"":b}),[j,w,b]),{data:z,isLoading:A}=(0,u.Kc)(T),R=async a=>{try{"add"===s&&(await e(a),f(!1),d.l.success("新增产品成功."))}catch(e){d.l.error((null==e?void 0:e.message)||"新增产品失败!")}};return(0,t.jsxs)("div",{className:"p-4 space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-6 rounded-lg bg-white p-5 shadow-sm transition-all",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-4 border-b border-gray-100",children:[(0,t.jsx)("h2",{className:"text-xl font-medium text-gray-800",children:"产品套餐管理"}),(0,t.jsx)(h.LQ,{permission:"product:create",children:(0,t.jsxs)(l.$,{onClick:()=>{a("add"),v(void 0),f(!0)},className:"h-10 gap-1.5 ",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),"新增套餐"]})})]}),(0,t.jsx)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-3 w-full sm:w-auto",children:[(0,t.jsx)("div",{className:"flex items-center gap-2 flex-1 sm:flex-none",children:(0,t.jsx)(c.p,{placeholder:"搜索套餐名称",defaultValue:j,onChange:L,className:"h-10 min-w-[180px] sm:max-w-[220px]"})}),(0,t.jsx)("div",{className:"flex items-center gap-2 flex-1 sm:flex-none",children:(0,t.jsxs)(o.l6,{value:b,onValueChange:y,children:[(0,t.jsx)(o.bq,{className:"h-10 min-w-[120px] sm:max-w-[180px]",children:(0,t.jsx)(o.yv,{placeholder:"套餐类型"})}),(0,t.jsxs)(o.gC,{children:[(0,t.jsx)(o.eb,{value:"all",children:"全部类型"}),(0,t.jsx)(o.eb,{value:"limited-sessions",children:"限次"}),(0,t.jsx)(o.eb,{value:"limited-time-and-count",children:"时次限"})]})]})})]})}),(0,t.jsx)(x.b,{data:(null==z?void 0:z.list)||[],columns:S,pagination:!1,loading:A}),(0,t.jsx)(m.default,{pageSize:(null==z?void 0:z.pageSize)||10,currentPage:(null==z?void 0:z.page)||1,totalItems:(null==z?void 0:z.total)||0,onPageChange:e=>{k(s=>({...s,currentPage:e}))},onPageSizeChange:e=>{k(s=>({...s,pageSize:e}))}})]}),p&&(0,t.jsx)(n.default,{visible:p,initialValues:g,onOk:R,onCancel:()=>f(!1)},"add product form")]})}},26126:(e,s,a)=>{"use strict";a.d(s,{E:()=>n});var t=a(95155);a(12115);var r=a(74466),i=a(59434);let l=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{className:(0,i.cn)(l({variant:a}),s),...r})}},30285:(e,s,a)=>{"use strict";a.d(s,{$:()=>c,r:()=>d});var t=a(95155),r=a(12115),i=a(99708),l=a(74466),n=a(59434);let d=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,s)=>{let{className:a,variant:r,size:l,asChild:c=!1,...o}=e,m=c?i.DX:"button";return(0,t.jsx)(m,{className:(0,n.cn)(d({variant:r,size:l,className:a})),ref:s,...o})});c.displayName="Button"},30356:(e,s,a)=>{"use strict";a.d(s,{C:()=>c,z:()=>d});var t=a(95155),r=a(12115),i=a(54059),l=a(9428),n=a(59434);let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(i.bL,{className:(0,n.cn)("grid gap-2",a),...r,ref:s})});d.displayName=i.bL.displayName;let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(i.q7,{ref:s,className:(0,n.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),...r,children:(0,t.jsx)(i.C1,{className:"flex items-center justify-center",children:(0,t.jsx)(l.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});c.displayName=i.q7.displayName},48432:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>v});var t=a(95155),r=a(12115),i=a(42355),l=a(13052),n=a(5623),d=a(59434),c=a(30285);let o=e=>{let{className:s,...a}=e;return(0,t.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,d.cn)("mx-auto flex w-full justify-center",s),...a})};o.displayName="Pagination";let m=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("ul",{ref:s,className:(0,d.cn)("flex flex-row items-center gap-1",a),...r})});m.displayName="PaginationContent";let u=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("li",{ref:s,className:(0,d.cn)("",a),...r})});u.displayName="PaginationItem";let x=e=>{let{className:s,isActive:a,size:r="icon",...i}=e;return(0,t.jsx)("a",{"aria-current":a?"page":void 0,className:(0,d.cn)((0,c.r)({variant:a?"outline":"ghost",size:r}),s),...i})};x.displayName="PaginationLink";let p=e=>{let{className:s,...a}=e;return(0,t.jsxs)(x,{"aria-label":"Go to previous page",size:"default",className:(0,d.cn)("gap-1 pl-2.5",s),...a,children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"上一页"})]})};p.displayName="PaginationPrevious";let h=e=>{let{className:s,...a}=e;return(0,t.jsxs)(x,{"aria-label":"Go to next page",size:"default",className:(0,d.cn)("gap-1 pr-2.5",s),...a,children:[(0,t.jsx)("span",{children:"下一页"}),(0,t.jsx)(l.A,{className:"h-4 w-4"})]})};h.displayName="PaginationNext";let f=e=>{let{className:s,...a}=e;return(0,t.jsxs)("span",{"aria-hidden":!0,className:(0,d.cn)("flex h-9 w-9 items-center justify-center",s),...a,children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"更多页"})]})};f.displayName="PaginationEllipsis";var g=a(59409);function v(e){let{currentPage:s,pageSize:a,totalItems:r,onPageChange:n,onPageSizeChange:d}=e,c=Math.ceil(r/a),v=(()=>{let e=[];if(c<=5){for(let s=1;s<=c;s++)e.push(s);return e}e.push(1);let a=Math.max(2,s-1),t=Math.min(s+1,c-1);2===a&&(t=Math.min(a+2,c-1)),t===c-1&&(a=Math.max(t-2,2)),a>2&&e.push("ellipsis-start");for(let s=a;s<=t;s++)e.push(s);return t<c-1&&e.push("ellipsis-end"),c>1&&e.push(c),e})(),j=0===r?0:(s-1)*a+1,N=Math.min(s*a,r);return(0,t.jsxs)("div",{className:"flex flex-col gap-4 px-2 py-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,t.jsx)("span",{className:"text-muted-foreground/80",children:"显示"}),(0,t.jsxs)(g.l6,{value:a.toString(),onValueChange:e=>{d(Number(e))},children:[(0,t.jsx)(g.bq,{className:"w-[4.5rem] h-7 text-xs border-border/60 hover:bg-accent/30 transition-colors",children:(0,t.jsx)(g.yv,{})}),(0,t.jsx)(g.gC,{children:[10,20,30,50].map(e=>(0,t.jsx)(g.eb,{value:e.toString(),className:"text-xs",children:e},e))})]}),(0,t.jsx)("span",{className:"text-muted-foreground/80",children:"条"}),(0,t.jsx)("span",{className:"text-muted-foreground/40 mx-1.5",children:"|"}),r>0?(0,t.jsxs)("span",{className:"text-muted-foreground/80",children:[j,"-",N," / ",r," 条记录"]}):(0,t.jsx)("span",{className:"text-muted-foreground/80",children:"0 条记录"})]}),(0,t.jsx)(o,{children:(0,t.jsxs)(m,{className:"gap-1",children:[(0,t.jsx)(u,{children:(0,t.jsx)(p,{onClick:()=>n(Math.max(1,s-1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(1===s?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,t.jsx)(i.A,{className:"h-4 w-4 mr-1"})})}),v.map((e,a)=>"ellipsis-start"===e||"ellipsis-end"===e?(0,t.jsx)(u,{children:(0,t.jsx)(f,{className:"h-8 w-8 flex items-center justify-center text-xs"})},"ellipsis-".concat(a)):(0,t.jsx)(u,{children:(0,t.jsx)(x,{onClick:()=>n(e),isActive:s===e,className:"h-8 w-8 text-xs font-medium rounded-md transition-colors data-[active]:bg-primary data-[active]:text-primary-foreground hover:bg-accent/50 hover:text-accent-foreground/90","aria-label":"前往第 ".concat(e," 页"),children:e})},e)),(0,t.jsx)(u,{children:(0,t.jsx)(h,{onClick:()=>n(Math.min(c,s+1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(s===c||0===c?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,t.jsx)(l.A,{className:"h-4 w-4 ml-1"})})})]})})]})}},50228:(e,s,a)=>{"use strict";a.d(s,{P:()=>i,s:()=>l});var t=a(95155),r=a(73069);let i=function(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e?(0,t.jsx)("div",{className:"text-sm ".concat(s?"text-muted-foreground":""),children:e}):(0,t.jsx)("div",{})},l=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return e?(0,t.jsx)(r.c,{maxDisplayLength:s,children:e}):(0,t.jsx)("div",{className:"text-sm text-muted-foreground"})}},54165:(e,s,a)=>{"use strict";a.d(s,{Cf:()=>x,Es:()=>h,HM:()=>m,L3:()=>f,c7:()=>p,lG:()=>d,rr:()=>g,zM:()=>c});var t=a(95155),r=a(12115),i=a(15452),l=a(54416),n=a(59434);let d=i.bL,c=i.l9,o=i.ZL,m=i.bm,u=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(i.hJ,{ref:s,className:(0,n.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r})});u.displayName=i.hJ.displayName;let x=r.forwardRef((e,s)=>{let{className:a,children:r,...d}=e;return(0,t.jsxs)(o,{children:[(0,t.jsx)(u,{}),(0,t.jsxs)(i.UC,{ref:s,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...d,children:[r,(0,t.jsxs)(i.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});x.displayName=i.UC.displayName;let p=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...a})};p.displayName="DialogHeader";let h=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a})};h.displayName="DialogFooter";let f=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(i.hE,{ref:s,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});f.displayName=i.hE.displayName;let g=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(i.VY,{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",a),...r})});g.displayName=i.VY.displayName},57001:(e,s,a)=>{"use strict";a.d(s,{p:()=>l});var t=a(95155),r=a(30285),i=a(46102);function l(e){let{icon:s,tooltipText:a,tooltipSide:l="top",tooltipAlign:n="center",delayDuration:d=300,variant:c="ghost",size:o="icon",className:m="h-8 w-8 hover:bg-muted",...u}=e;return(0,t.jsx)(i.Bc,{delayDuration:d,children:(0,t.jsxs)(i.m_,{children:[(0,t.jsx)(i.k$,{asChild:!0,children:(0,t.jsx)(r.$,{variant:c,size:o,className:m,...u,children:(0,t.jsx)(s,{className:"h-4 w-4 text-muted-foreground"})})}),(0,t.jsx)(i.ZI,{side:l,align:n,className:"font-medium text-xs px-3 py-1.5",children:(0,t.jsx)("p",{children:a})})]})})}},59434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>i});var t=a(52596),r=a(39688);function i(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}},60933:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var t=a(95155),r=a(12115),i=a(62177),l=a(90221),n=a(55594),d=a(30285),c=a(54165),o=a(62523),m=a(85057),u=a(88539),x=a(30356),p=a(22346),h=a(80333),f=a(59409),g=a(47863),v=a(66474);let j=e=>{let{isOpen:s,toggleCollapsible:a,form:r}=e,l=(0,i.xW)(),n=r||l;return(0,t.jsxs)("div",{className:"w-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between cursor-pointer hover:bg-muted/20 p-2 rounded-md transition-colors",onClick:a,children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"附加信息"}),(0,t.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:e=>e.stopPropagation(),children:s?(0,t.jsx)(g.A,{className:"h-4 w-4"}):(0,t.jsx)(v.A,{className:"h-4 w-4"})})]}),s&&(0,t.jsxs)("div",{className:"space-y-4 pt-2 pb-1 px-2 bg-muted/10 rounded-md",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"套餐图标"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{var e;return null===(e=document.getElementById("icon-upload"))||void 0===e?void 0:e.click()},children:"选择文件"}),(0,t.jsx)("input",{id:"icon-upload",type:"file",className:"hidden",onChange:e=>{var s;let a=null===(s=e.target.files)||void 0===s?void 0:s[0];a&&n&&n.setValue("icon",a)}}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:n&&n.watch("icon")?n.watch("icon").name:"未选择文件"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"套餐图片"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{var e;return null===(e=document.getElementById("image-upload"))||void 0===e?void 0:e.click()},children:"选择文件"}),(0,t.jsx)("input",{id:"image-upload",type:"file",className:"hidden",onChange:e=>{var s;let a=null===(s=e.target.files)||void 0===s?void 0:s[0];a&&n&&n.setValue("image",a)}}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:n&&n.watch("image")?n.watch("image").name:"未选择文件"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"套餐介绍"}),(0,t.jsx)(u.T,{placeholder:"输入套餐详细介绍",rows:3,className:"resize-none",...n&&{...n.register("description"),value:n.watch("description")||""}})]})]})]})},N=n.Ik({name:n.Yj().min(1,{message:"套餐名称不能为空"}),price:n.ai().min(0,{message:"价格不能为负数"}),leaveCount:n.ai().min(0,{message:"请假次数不能为负数"}),isShow:n.zM().default(!1),packageType:n.k5(["limited-sessions","limited-time-and-count"]),usageLimit:n.ai().optional(),timeLimitedUsage:n.ai().optional(),timeLimitType:n.k5(["daily","monthly"]).optional(),validTimeRange:n.k5(["purchase-date","consumption-date"]).optional(),remarks:n.Yj().optional(),icon:n.bz().optional(),image:n.bz().optional(),description:n.Yj().optional()}).refine(e=>"limited-sessions"!==e.packageType||void 0!==e.usageLimit&&e.usageLimit>0,{message:"限次套餐必须设置有效的次数",path:["usageLimit"]}).refine(e=>"limited-time-and-count"!==e.packageType||void 0!==e.timeLimitedUsage&&e.timeLimitedUsage>0&&void 0!==e.timeLimitType,{message:"限时套餐必须设置有效的时长和单位",path:["timeLimitedUsage"]}).refine(e=>"limited-time-and-count"!==e.packageType||void 0!==e.validTimeRange,{message:"时次限套餐必须选择时长开始方式",path:["validTimeRange"]}),b=e=>{let{visible:s,initialValues:a,onOk:n,onCancel:g}=e,[v,b]=(0,r.useState)(!1),[y,w]=(0,r.useState)(!1),{register:k,handleSubmit:C,setValue:L,watch:T,reset:z,formState:{errors:A},control:R}=(0,i.mN)({resolver:(0,l.u)(N),defaultValues:{name:"",price:0,leaveCount:0,isShow:!1,packageType:"limited-sessions",usageLimit:1,timeLimitedUsage:1,timeLimitType:"daily",validTimeRange:"purchase-date",remarks:"",description:""}}),S=T("packageType"),V=T("isShow");(0,r.useEffect)(()=>{a&&z({name:a.name||"",price:Number(a.price)||0,leaveCount:Number(a.leaveCount)||0,isShow:a.isShow||!1,packageType:a.packageType||"limited-sessions",usageLimit:Number(a.usageLimit)||1,timeLimitedUsage:Number(a.timeLimitedUsage)||30,timeLimitType:a.timeLimitType||"daily",validTimeRange:a.validTimeRange||"purchase-date",remarks:a.remarks||"",description:a.description||""})},[a,z]);let P=async e=>{try{w(!0),"limited-sessions"===e.packageType&&(delete e.timeLimitedUsage,delete e.timeLimitType,delete e.validTimeRange),n(e)}catch(e){console.error("提交表单时出错:",e)}finally{w(!1)}};return(0,t.jsx)(c.lG,{open:s,onOpenChange:g,children:(0,t.jsxs)(c.Cf,{className:"sm:max-w-[640px]",children:[(0,t.jsx)(c.c7,{children:(0,t.jsx)(c.L3,{className:"text-xl",children:a?"编辑套餐":"新增套餐"})}),(0,t.jsxs)("form",{onSubmit:C(P),children:[(0,t.jsxs)("div",{className:"grid gap-6 py-4 px-2 max-h-[60vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"套餐名称"}),(0,t.jsx)(o.p,{placeholder:"输入套餐名称",...k("name")}),A.name&&(0,t.jsx)("p",{className:"text-xs text-destructive",children:A.name.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"套餐价格"}),(0,t.jsx)(o.p,{type:"number",placeholder:"0.00",...k("price",{valueAsNumber:!0,onChange:e=>{L("price",""===e.target.value?0:Number.parseFloat(e.target.value))}})}),A.price&&(0,t.jsx)("p",{className:"text-xs text-destructive",children:A.price.message})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"请假次数"}),(0,t.jsx)(o.p,{type:"number",placeholder:"请输入请假次数",...k("leaveCount",{valueAsNumber:!0,onChange:e=>{L("leaveCount",""===e.target.value?0:Number.parseInt(e.target.value))}})}),A.leaveCount&&(0,t.jsx)("p",{className:"text-xs text-destructive",children:A.leaveCount.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"是否显示在官网"}),(0,t.jsxs)("div",{className:"flex items-center h-10 px-3 rounded-md border",children:[(0,t.jsx)(h.d,{checked:V,onCheckedChange:e=>L("isShow",e)}),(0,t.jsx)("span",{className:"ml-3 text-sm",children:V?"显示":"不显示"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"套餐类别"}),(0,t.jsxs)(x.z,{disabled:!!a,value:S,onValueChange:e=>L("packageType",e),className:"grid gap-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 p-2 border rounded-md",children:[(0,t.jsx)(x.C,{value:"limited-sessions",id:"limited-sessions"}),(0,t.jsx)(m.J,{htmlFor:"limited-sessions",className:"cursor-pointer",children:"限次套餐（消费课时/次）"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 p-2 border rounded-md",children:[(0,t.jsx)(x.C,{value:"limited-time-and-count",id:"limited-time-and-count"}),(0,t.jsx)(m.J,{htmlFor:"limited-time-and-count",className:"cursor-pointer",children:"时次限套餐（限时限次）"})]})]}),A.packageType&&(0,t.jsx)("p",{className:"text-xs text-destructive",children:A.packageType.message})]}),(0,t.jsxs)("div",{className:"grid gap-4",children:["limited-time-and-count"===S&&(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 animate-in fade-in duration-300",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"时长"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(o.p,{type:"number",placeholder:"时长",className:"w-full",...k("timeLimitedUsage",{valueAsNumber:!0,onChange:e=>{L("timeLimitedUsage",""===e.target.value?0:Number.parseInt(e.target.value))}})}),(0,t.jsxs)(f.l6,{value:T("timeLimitType"),onValueChange:e=>L("timeLimitType",e),children:[(0,t.jsx)(f.bq,{className:"w-24",children:(0,t.jsx)(f.yv,{placeholder:"单位"})}),(0,t.jsxs)(f.gC,{children:[(0,t.jsx)(f.eb,{value:"daily",children:"天"}),(0,t.jsx)(f.eb,{value:"monthly",children:"月"})]})]})]}),A.timeLimitedUsage&&(0,t.jsx)("p",{className:"text-xs text-destructive",children:A.timeLimitedUsage.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"时长开始"}),(0,t.jsxs)(x.z,{value:T("validTimeRange"),disabled:!!a,onValueChange:e=>L("validTimeRange",e),className:"flex space-x-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.C,{value:"purchase-date",id:"purchase-date"}),(0,t.jsx)(m.J,{htmlFor:"purchase-date",className:"cursor-pointer",children:"购买日算起"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.C,{value:"consumption-date",id:"consumption-date"}),(0,t.jsx)(m.J,{htmlFor:"consumption-date",className:"cursor-pointer",children:"消费日算起"})]})]}),A.validTimeRange&&(0,t.jsx)("p",{className:"text-xs text-destructive",children:A.validTimeRange.message})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"消课次数"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(o.p,{type:"number",placeholder:"次数",className:"w-full",...k("usageLimit",{valueAsNumber:!0,onChange:e=>{L("usageLimit",""===e.target.value?0:Number.parseInt(e.target.value))}})}),(0,t.jsx)("div",{className:"flex items-center justify-center px-3 border rounded-md whitespace-nowrap",children:"次"})]}),A.usageLimit&&(0,t.jsx)("p",{className:"text-xs text-destructive",children:A.usageLimit.message})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"套餐备注"}),(0,t.jsx)(u.T,{placeholder:"输入套餐备注信息",rows:3,className:"resize-none",...k("remarks")}),A.remarks&&(0,t.jsx)("p",{className:"text-xs text-destructive",children:A.remarks.message})]}),(0,t.jsx)(p.w,{className:"my-2"}),(0,t.jsx)(j,{isOpen:v,toggleCollapsible:()=>{b(!v)},form:{register:k,setValue:L,watch:T,control:R}})]}),(0,t.jsxs)(c.Es,{className:"mt-4",children:[(0,t.jsx)(d.$,{type:"button",variant:"outline",onClick:g,children:"取消"}),(0,t.jsx)(d.$,{type:"submit",disabled:y,children:y?"处理中...":"保存"})]})]})]})})}},61307:(e,s,a)=>{Promise.resolve().then(a.bind(a,22745))},62523:(e,s,a)=>{"use strict";a.d(s,{p:()=>l});var t=a(95155),r=a(12115),i=a(59434);let l=r.forwardRef((e,s)=>{let{className:a,type:r,...l}=e;return(0,t.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:s,...l})});l.displayName="Input"},65436:(e,s,a)=>{"use strict";a.d(s,{G:()=>i,j:()=>r});var t=a(34540);let r=()=>(0,t.wA)(),i=t.d4},73069:(e,s,a)=>{"use strict";a.d(s,{c:()=>u});var t=a(95155),r=a(12115),i=a(47863),l=a(66474),n=a(5196),d=a(24357),c=a(59434),o=a(14636),m=a(30285);function u(e){let{children:s,maxDisplayLength:a=15,className:u,popoverWidth:x="auto",showBorder:p=!1}=e,[h,f]=r.useState(!1),[g,v]=r.useState(!1),j=r.useMemo(()=>{if("string"==typeof s||"number"==typeof s)return s.toString();try{var e;let a=document.createElement("div");return a.innerHTML=(null==s?void 0:null===(e=s.props)||void 0===e?void 0:e.children)||"",a.textContent||""}catch(e){return console.warn("Could not extract text from children:",e),""}},[s]),N=r.useMemo(()=>{if("string"==typeof s||"number"==typeof s){let e=s.toString();return e.length>a?e.slice(0,a):e}return s},[s,a]),b=async()=>{try{await navigator.clipboard.writeText(j),v(!0),setTimeout(()=>v(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,t.jsxs)(o.AM,{open:h,onOpenChange:f,children:[(0,t.jsx)(o.Wv,{asChild:!0,children:(0,t.jsxs)(m.$,{variant:p?"outline":"ghost",role:"combobox","aria-expanded":h,className:(0,c.cn)("w-fit justify-between font-normal px-2 py-1 h-auto",!p&&"border-0 shadow-none",u),children:[(0,t.jsx)("span",{className:"mr-2 truncate",children:N}),h?(0,t.jsx)(i.A,{className:"h-4 w-4 shrink-0 opacity-50"}):(0,t.jsx)(l.A,{className:"h-4 w-4 shrink-0 opacity-50"})]})}),(0,t.jsx)(o.hl,{className:"p-0",align:"start",style:{width:x},children:(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2",children:[(0,t.jsx)("span",{className:"text-sm break-all",children:j}),(0,t.jsxs)(m.$,{variant:"ghost",size:"icon",className:"h-8 w-8 shrink-0",onClick:b,children:[g?(0,t.jsx)(n.A,{className:"h-4 w-4"}):(0,t.jsx)(d.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:g?"Copied":"Copy text"})]})]})})]})}},80333:(e,s,a)=>{"use strict";a.d(s,{d:()=>n});var t=a(95155),r=a(12115),i=a(4884),l=a(59434);let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(i.bL,{className:(0,l.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...r,ref:s,children:(0,t.jsx)(i.zi,{className:(0,l.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});n.displayName=i.bL.displayName},85057:(e,s,a)=>{"use strict";a.d(s,{J:()=>c});var t=a(95155),r=a(12115),i=a(40968),l=a(74466),n=a(59434);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(i.b,{ref:s,className:(0,n.cn)(d(),a),...r})});c.displayName=i.b.displayName},88539:(e,s,a)=>{"use strict";a.d(s,{T:()=>l});var t=a(95155),r=a(12115),i=a(59434);let l=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:s,...r})});l.displayName="Textarea"},90010:(e,s,a)=>{"use strict";a.d(s,{$v:()=>f,EO:()=>u,Lt:()=>d,Rx:()=>g,Zr:()=>v,ck:()=>p,r7:()=>h,tv:()=>c,wd:()=>x});var t=a(95155),r=a(12115),i=a(17649),l=a(59434),n=a(30285);let d=i.bL,c=i.l9,o=i.ZL,m=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(i.hJ,{className:(0,l.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r,ref:s})});m.displayName=i.hJ.displayName;let u=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsxs)(o,{children:[(0,t.jsx)(m,{}),(0,t.jsx)(i.UC,{ref:s,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...r})]})});u.displayName=i.UC.displayName;let x=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-2 text-center sm:text-left",s),...a})};x.displayName="AlertDialogHeader";let p=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a})};p.displayName="AlertDialogFooter";let h=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(i.hE,{ref:s,className:(0,l.cn)("text-lg font-semibold",a),...r})});h.displayName=i.hE.displayName;let f=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(i.VY,{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",a),...r})});f.displayName=i.VY.displayName;let g=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(i.rc,{ref:s,className:(0,l.cn)((0,n.r)(),a),...r})});g.displayName=i.rc.displayName;let v=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(i.ZD,{ref:s,className:(0,l.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",a),...r})});v.displayName=i.ZD.displayName},91347:(e,s,a)=>{"use strict";a.d(s,{LQ:()=>i});var t=a(95155),r=a(65436);let i=e=>{let{permission:s,children:a,fallback:i=null,logic:l="any"}=e,n=(0,r.G)(e=>e.userPermissions.permissions);if(!s||Array.isArray(s)&&0===s.length)return(0,t.jsx)(t.Fragment,{children:a});let d=Array.isArray(s)?s:[s],c=!1;return("all"===l?d.every(e=>n.includes(e)):d.some(e=>n.includes(e)))?(0,t.jsx)(t.Fragment,{children:a}):(0,t.jsx)(t.Fragment,{children:i})};a(30285);var l=a(12115),n=a(74466),d=a(59434);let c=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}});l.forwardRef((e,s)=>{let{className:a,variant:r,...i}=e;return(0,t.jsx)("div",{ref:s,role:"alert",className:(0,d.cn)(c({variant:r}),a),...i})}).displayName="Alert",l.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h5",{ref:s,className:(0,d.cn)("mb-1 font-medium leading-none tracking-tight",a),...r})}).displayName="AlertTitle",l.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,d.cn)("text-sm [&_p]:leading-relaxed",a),...r})}).displayName="AlertDescription"}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,8687,4201,8737,4540,4582,5620,9613,7945,5589,5602,279,9624,9110,6315,7358],()=>s(61307)),_N_E=e.O()}]);