{"name": "apiback<PERSON>", "version": "1.0.0", "description": "This project was bootstrapped with Fastify-CLI.", "main": "app.js", "type": "module", "directories": {"test": "test"}, "scripts": {"test": "node --test test/**/*.test.js", "start": "fastify start -l info app.js", "service": "node scripts/service.js", "dev": "concurrently \"fastify start -w -l info -P app.js\" \"node services/imageQueue.js\"", "init": "node test/initData.js", "seed": "node scripts/seed.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@bull-board/api": "^6.9.2", "@bull-board/fastify": "^6.9.2", "@fastify/auth": "^5.0.2", "@fastify/autoload": "^6.0.0", "@fastify/cors": "^10.1.0", "@fastify/env": "^5.0.2", "@fastify/jwt": "^9.0.4", "@fastify/multipart": "^9.0.3", "@fastify/postgres": "^6.0.2", "@fastify/redis": "^7.0.2", "@fastify/sensible": "^6.0.0", "@fastify/swagger": "^9.4.2", "@fastify/swagger-ui": "^5.2.2", "@fastify/websocket": "^11.0.2", "@msgpack/msgpack": "^3.1.1", "@prisma/client": "^6.4.1", "@sapphire/snowflake": "^3.5.5", "bcryptjs": "^3.0.2", "bullmq": "^5.49.1", "cuid": "^3.0.0", "date-fns": "^4.1.0", "fast-json-stringify": "^6.0.1", "fastify": "^5.0.0", "fastify-cli": "^7.3.0", "fastify-plugin": "^5.0.0", "nanoid": "^5.1.2", "node-fetch": "^3.3.2", "pg": "^8.13.3", "pino-pretty": "^13.0.0", "qiniu": "^7.14.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "concurrently": "^9.1.2", "prisma": "^6.5.0"}}