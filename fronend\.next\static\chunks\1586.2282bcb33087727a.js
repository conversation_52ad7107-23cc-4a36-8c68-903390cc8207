"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1586,8432],{5040:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(19946).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5623:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},19946:(e,a,r)=>{r.d(a,{A:()=>o});var t=r(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=function(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return a.filter((e,a,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===a).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,t.forwardRef)((e,a)=>{let{color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:m,...u}=e;return(0,t.createElement)("svg",{ref:a,...l,width:s,height:s,stroke:r,strokeWidth:o?24*Number(i)/Number(s):i,className:n("lucide",c),...u},[...m.map(e=>{let[a,r]=e;return(0,t.createElement)(a,r)}),...Array.isArray(d)?d:[d]])}),o=(e,a)=>{let r=(0,t.forwardRef)((r,l)=>{let{className:o,...c}=r;return(0,t.createElement)(i,{ref:l,iconNode:a,className:n("lucide-".concat(s(e)),o),...c})});return r.displayName="".concat(e),r}},26353:(e,a,r)=>{r.r(a),r.d(a,{default:()=>h});var t=r(95155),s=r(12115),n=r(47924),l=r(5040),i=r(39785),o=r(9110),c=r(62523),d=r(85057),m=r(48432);let u=[{accessorKey:"studentName",header:"学员姓名",cell:e=>{let{row:a}=e;return(0,t.jsx)("div",{className:"font-medium",children:a.original.student.name})}},{accessorKey:"productName",header:"套餐名称",cell:e=>{let{row:a}=e;return(0,t.jsx)("div",{className:"font-medium",children:a.original.product.name})}},{accessorKey:"packageType",header:"套餐类型",cell:e=>{let{row:a}=e,r=a.original.product.packageType;return"limited-sessions"===r?(0,t.jsxs)("div",{className:"font-medium",children:["限次(",a.original.totalSessionCount,"次)"]}):"limited-time-and-count"===r?(0,t.jsxs)("div",{className:"font-medium",children:["时次限(",a.original.totalSessionCount,"次/)"]}):void 0}},{accessorKey:"amountPaid",header:"套餐金额(实收)",cell:e=>{let{row:a}=e;return(0,t.jsx)("div",{className:"font-medium",children:a.original.amountPaid})}},{accessorKey:"remainingAmount",header:"剩余费用",cell:e=>{let{row:a}=e;return(0,t.jsx)("div",{className:"font-medium",children:a.original.remainingBalance})}},{accessorKey:"consumptionExpenses",header:"消费费用",cell:e=>{let{row:a}=e;return(0,t.jsxs)("div",{className:"font-medium",children:[" ",(a.original.amountPaid-a.original.remainingBalance).toFixed(2)]})}}];var x=r(71159);let h=function(){let[e,a]=(0,s.useState)(""),[r,h]=(0,s.useState)({page:1,pageSize:10}),p=(0,s.useMemo)(()=>({search:e,page:r.page,pageSize:r.pageSize}),[e,r.page,r.pageSize]),{data:g,isLoading:f}=(0,x.Q_)(p),v=(0,s.useMemo)(()=>g?{remainingSessionCount:"object"==typeof g.remainingCount?Object.values(g.remainingSessionCount).reduce((e,a)=>e+(Number(a)||0),0):Number(g.remainingSessionCount)||0,remainingBalance:"object"==typeof g.remainingAmount?Object.values(g.remainingBalance).reduce((e,a)=>e+(Number(a)||0),0):Number(g.remainingBalance)||0}:{remainingSessionCount:0,remainingBalance:0},[g]),N=(0,s.useCallback)(()=>{h(e=>({...e,page:1}))},[]),j=(0,s.useCallback)(e=>{h(a=>({...a,page:e}))},[]),b=(0,s.useCallback)(e=>{h(a=>({...a,pageSize:e,page:1}))},[]);return(0,t.jsxs)("div",{className:"space-y-6 animate-in",children:[(0,t.jsx)("div",{className:"flex flex-wrap gap-3 items-center",children:(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)("div",{className:"relative w-full",children:[(0,t.jsx)(n.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(c.p,{type:"text",placeholder:"搜索学员姓名",value:e,onChange:e=>a(e.target.value),onKeyDown:e=>"Enter"===e.key&&N(),className:"pl-9 h-10 bg-background transition-all focus-visible:ring-1 focus-visible:ring-primary/50 w-full"})]})})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)("div",{className:"bg-background rounded-lg transition-all duration-200 group hover:shadow-md hover:translate-y-[-2px] p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"rounded-full bg-primary/10 p-2 group-hover:bg-primary/20 transition-colors",children:(0,t.jsx)(l.A,{className:"h-5 w-5 text-primary/70 group-hover:text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{className:"text-sm text-muted-foreground group-hover:text-foreground",children:"学员剩余总次数"}),(0,t.jsxs)("div",{className:"text-2xl font-bold tracking-tight mt-1",children:[Number(v.remainingSessionCount).toLocaleString()," 次"]})]})]})}),(0,t.jsx)("div",{className:"bg-background rounded-lg transition-all duration-200 group hover:shadow-md hover:translate-y-[-2px] p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"rounded-full bg-primary/10 p-2 group-hover:bg-primary/20 transition-colors",children:(0,t.jsx)(i.A,{className:"h-5 w-5 text-primary/70 group-hover:text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{className:"text-sm text-muted-foreground group-hover:text-foreground",children:"学员剩余总余额"}),(0,t.jsxs)("div",{className:"text-2xl font-bold tracking-tight mt-1",children:["\xa5 ",Number(v.remainingBalance).toLocaleString()]})]})]})})]}),(0,t.jsx)("div",{className:"rounded-md overflow-hidden border-0",children:(0,t.jsx)(o.b,{columns:u,data:(null==g?void 0:g.list)||[],pagination:!1,loading:f})}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)(m.default,{totalItems:(null==g?void 0:g.total)||0,currentPage:(null==g?void 0:g.page)||1,pageSize:(null==g?void 0:g.pageSize)||10,onPageChange:j,onPageSizeChange:b})})]})}},39785:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(19946).A)("Wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]])},40968:(e,a,r)=>{r.d(a,{b:()=>i});var t=r(12115),s=r(63655),n=r(95155),l=t.forwardRef((e,a)=>(0,n.jsx)(s.sG.label,{...e,ref:a,onMouseDown:a=>{var r;a.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));l.displayName="Label";var i=l},47863:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(19946).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},47924:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},48432:(e,a,r)=>{r.r(a),r.d(a,{default:()=>v});var t=r(95155),s=r(12115),n=r(42355),l=r(13052),i=r(5623),o=r(59434),c=r(30285);let d=e=>{let{className:a,...r}=e;return(0,t.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,o.cn)("mx-auto flex w-full justify-center",a),...r})};d.displayName="Pagination";let m=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("ul",{ref:a,className:(0,o.cn)("flex flex-row items-center gap-1",r),...s})});m.displayName="PaginationContent";let u=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("li",{ref:a,className:(0,o.cn)("",r),...s})});u.displayName="PaginationItem";let x=e=>{let{className:a,isActive:r,size:s="icon",...n}=e;return(0,t.jsx)("a",{"aria-current":r?"page":void 0,className:(0,o.cn)((0,c.r)({variant:r?"outline":"ghost",size:s}),a),...n})};x.displayName="PaginationLink";let h=e=>{let{className:a,...r}=e;return(0,t.jsxs)(x,{"aria-label":"Go to previous page",size:"default",className:(0,o.cn)("gap-1 pl-2.5",a),...r,children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"上一页"})]})};h.displayName="PaginationPrevious";let p=e=>{let{className:a,...r}=e;return(0,t.jsxs)(x,{"aria-label":"Go to next page",size:"default",className:(0,o.cn)("gap-1 pr-2.5",a),...r,children:[(0,t.jsx)("span",{children:"下一页"}),(0,t.jsx)(l.A,{className:"h-4 w-4"})]})};p.displayName="PaginationNext";let g=e=>{let{className:a,...r}=e;return(0,t.jsxs)("span",{"aria-hidden":!0,className:(0,o.cn)("flex h-9 w-9 items-center justify-center",a),...r,children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"更多页"})]})};g.displayName="PaginationEllipsis";var f=r(59409);function v(e){let{currentPage:a,pageSize:r,totalItems:s,onPageChange:i,onPageSizeChange:o}=e,c=Math.ceil(s/r),v=(()=>{let e=[];if(c<=5){for(let a=1;a<=c;a++)e.push(a);return e}e.push(1);let r=Math.max(2,a-1),t=Math.min(a+1,c-1);2===r&&(t=Math.min(r+2,c-1)),t===c-1&&(r=Math.max(t-2,2)),r>2&&e.push("ellipsis-start");for(let a=r;a<=t;a++)e.push(a);return t<c-1&&e.push("ellipsis-end"),c>1&&e.push(c),e})(),N=0===s?0:(a-1)*r+1,j=Math.min(a*r,s);return(0,t.jsxs)("div",{className:"flex flex-col gap-4 px-2 py-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,t.jsx)("span",{className:"text-muted-foreground/80",children:"显示"}),(0,t.jsxs)(f.l6,{value:r.toString(),onValueChange:e=>{o(Number(e))},children:[(0,t.jsx)(f.bq,{className:"w-[4.5rem] h-7 text-xs border-border/60 hover:bg-accent/30 transition-colors",children:(0,t.jsx)(f.yv,{})}),(0,t.jsx)(f.gC,{children:[10,20,30,50].map(e=>(0,t.jsx)(f.eb,{value:e.toString(),className:"text-xs",children:e},e))})]}),(0,t.jsx)("span",{className:"text-muted-foreground/80",children:"条"}),(0,t.jsx)("span",{className:"text-muted-foreground/40 mx-1.5",children:"|"}),s>0?(0,t.jsxs)("span",{className:"text-muted-foreground/80",children:[N,"-",j," / ",s," 条记录"]}):(0,t.jsx)("span",{className:"text-muted-foreground/80",children:"0 条记录"})]}),(0,t.jsx)(d,{children:(0,t.jsxs)(m,{className:"gap-1",children:[(0,t.jsx)(u,{children:(0,t.jsx)(h,{onClick:()=>i(Math.max(1,a-1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(1===a?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,t.jsx)(n.A,{className:"h-4 w-4 mr-1"})})}),v.map((e,r)=>"ellipsis-start"===e||"ellipsis-end"===e?(0,t.jsx)(u,{children:(0,t.jsx)(g,{className:"h-8 w-8 flex items-center justify-center text-xs"})},"ellipsis-".concat(r)):(0,t.jsx)(u,{children:(0,t.jsx)(x,{onClick:()=>i(e),isActive:a===e,className:"h-8 w-8 text-xs font-medium rounded-md transition-colors data-[active]:bg-primary data-[active]:text-primary-foreground hover:bg-accent/50 hover:text-accent-foreground/90","aria-label":"前往第 ".concat(e," 页"),children:e})},e)),(0,t.jsx)(u,{children:(0,t.jsx)(p,{onClick:()=>i(Math.min(c,a+1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(a===c||0===c?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,t.jsx)(l.A,{className:"h-4 w-4 ml-1"})})})]})})]})}},62523:(e,a,r)=>{r.d(a,{p:()=>l});var t=r(95155),s=r(12115),n=r(59434);let l=s.forwardRef((e,a)=>{let{className:r,type:s,...l}=e;return(0,t.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:a,...l})});l.displayName="Input"},66474:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(19946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},85057:(e,a,r)=>{r.d(a,{J:()=>c});var t=r(95155),s=r(12115),n=r(40968),l=r(74466),i=r(59434);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)(n.b,{ref:a,className:(0,i.cn)(o(),r),...s})});c.displayName=n.b.displayName}}]);