(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2274],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>o,t:()=>i});var n=r(12115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=s(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():s(e[t],null)}}}}function o(...e){return n.useCallback(i(...e),e)}},8927:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n=r(95155),s=r(11518),i=r.n(s);let o=()=>(0,n.jsxs)("div",{className:"jsx-c6a6d1e14f488c28 relative w-[200px] h-[200px] mx-auto",children:[(0,n.jsxs)("svg",{viewBox:"0 0 200 200",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"jsx-c6a6d1e14f488c28 w-full h-full",children:[(0,n.jsx)("circle",{cx:"100",cy:"100",r:"100",fill:"#FFF8F0",className:"jsx-c6a6d1e14f488c28"}),(0,n.jsx)("path",{d:"M60 140c0-20 15-25 40-25s40 5 40 25-20 30-40 30-40-10-40-30z",fill:"#FFD59A",className:"jsx-c6a6d1e14f488c28"}),(0,n.jsx)("circle",{cx:"80",cy:"110",r:"10",fill:"#000",className:"jsx-c6a6d1e14f488c28"}),(0,n.jsx)("circle",{cx:"120",cy:"110",r:"10",fill:"#000",className:"jsx-c6a6d1e14f488c28"}),(0,n.jsx)("path",{d:"M100 125c-5 0-8 4-8 6s2 4 8 4 8-2 8-4-3-6-8-6z",fill:"#F472B6",className:"jsx-c6a6d1e14f488c28"}),(0,n.jsx)("path",{d:"M70 95c-10-10-10-30 5-35s20 10 25 10 15-15 30-5 10 30 0 40",stroke:"#444",strokeWidth:"4",strokeLinecap:"round",className:"jsx-c6a6d1e14f488c28"}),(0,n.jsx)("path",{d:"M85 70c-2-5-10-15-20-10s-5 20 0 25",stroke:"#D97706",strokeWidth:"4",strokeLinecap:"round",className:"jsx-c6a6d1e14f488c28"}),(0,n.jsx)("path",{d:"M115 70c2-5 10-15 20-10s5 20 0 25",stroke:"#D97706",strokeWidth:"4",strokeLinecap:"round",className:"jsx-c6a6d1e14f488c28"}),(0,n.jsx)("g",{className:"jsx-c6a6d1e14f488c28 tail-origin",children:(0,n.jsx)("path",{d:"M140 155 Q160 150 150 130",stroke:"#D97706",strokeWidth:"6",strokeLinecap:"round",fill:"none",className:"jsx-c6a6d1e14f488c28"})})]}),(0,n.jsx)(i(),{id:"c6a6d1e14f488c28",children:".tail-origin.jsx-c6a6d1e14f488c28{-webkit-transform-origin:140px 155px;-moz-transform-origin:140px 155px;-ms-transform-origin:140px 155px;-o-transform-origin:140px 155px;transform-origin:140px 155px;-webkit-animation:wag-tail.6s ease-in-out infinite;-moz-animation:wag-tail.6s ease-in-out infinite;-o-animation:wag-tail.6s ease-in-out infinite;animation:wag-tail.6s ease-in-out infinite}@-webkit-keyframes wag-tail{0%{-webkit-transform:rotate(10deg);transform:rotate(10deg)}50%{-webkit-transform:rotate(-15deg);transform:rotate(-15deg)}100%{-webkit-transform:rotate(10deg);transform:rotate(10deg)}}@-moz-keyframes wag-tail{0%{-moz-transform:rotate(10deg);transform:rotate(10deg)}50%{-moz-transform:rotate(-15deg);transform:rotate(-15deg)}100%{-moz-transform:rotate(10deg);transform:rotate(10deg)}}@-o-keyframes wag-tail{0%{-o-transform:rotate(10deg);transform:rotate(10deg)}50%{-o-transform:rotate(-15deg);transform:rotate(-15deg)}100%{-o-transform:rotate(10deg);transform:rotate(10deg)}}@keyframes wag-tail{0%{-webkit-transform:rotate(10deg);-moz-transform:rotate(10deg);-o-transform:rotate(10deg);transform:rotate(10deg)}50%{-webkit-transform:rotate(-15deg);-moz-transform:rotate(-15deg);-o-transform:rotate(-15deg);transform:rotate(-15deg)}100%{-webkit-transform:rotate(10deg);-moz-transform:rotate(10deg);-o-transform:rotate(10deg);transform:rotate(10deg)}}"})]});var a=r(30285),l=r(6874),u=r.n(l);function d(){return(0,n.jsxs)("main",{className:"flex flex-col items-center justify-center min-h-screen px-4 bg-white text-center",children:[(0,n.jsx)(o,{}),(0,n.jsx)("h1",{className:"text-3xl font-bold mt-6",children:"404 - 找不到页面"}),(0,n.jsx)("p",{className:"text-muted-foreground mt-2",children:"这只小狗迷路了，我们也找不到你要去的地方。"}),(0,n.jsx)(a.$,{asChild:!0,className:"mt-4",children:(0,n.jsx)(u(),{href:"/",children:"返回首页"})})]})}},11518:(e,t,r)=>{"use strict";e.exports=r(82269).style},18649:(e,t,r)=>{Promise.resolve().then(r.bind(r,8927))},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>u,r:()=>l});var n=r(95155),s=r(12115),i=r(99708),o=r(74466),a=r(59434);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=s.forwardRef((e,t)=>{let{className:r,variant:s,size:o,asChild:u=!1,...d}=e,c=u?i.DX:"button";return(0,n.jsx)(c,{className:(0,a.cn)(l({variant:s,size:o,className:r})),ref:t,...d})});u.displayName="Button"},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var n=r(52596),s=r(39688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,n.$)(t))}},68375:()=>{},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var n=r(52596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,o=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:a}=t,l=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let i=s(t)||s(n);return o[e][i]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,l,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...u}[t]):({...a,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},82269:(e,t,r)=>{"use strict";var n=r(49509);r(68375);var s=r(12115),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),o=void 0!==n&&n.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,r=t.name,n=void 0===r?"stylesheet":r,s=t.optimizeForSpeed,i=void 0===s?o:s;u(a(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",u("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){u("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),u(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;if(u(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){if(u(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},t.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(n){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];u(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},t.deleteRule=function(e){if("undefined"==typeof window){this._serverSheet.deleteRule(e);return}if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];u(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},t.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},t.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},t.makeStyleTag=function(e,t,r){t&&u(a(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var s=document.head||document.getElementsByTagName("head")[0];return r?s.insertBefore(n,r):s.appendChild(n),n},function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function u(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},c={};function f(e,t){if(!t)return"jsx-"+e;var r=String(t),n=e+r;return c[n]||(c[n]="jsx-"+d(e+"-"+r)),c[n]}function h(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return c[r]||(c[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),c[r]}var m=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,n=void 0===r?null:r,s=t.optimizeForSpeed,i=void 0!==s&&s;this._sheet=n||new l({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),n&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),n=r.styleId,s=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var i=s.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=i,this._instancesCounts[n]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],n=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,n=e.id;if(r){var s=f(n,r);return{styleId:s,rules:Array.isArray(t)?t.map(function(e){return h(s,e)}):[h(s,t)]}}return{styleId:f(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),p=s.createContext(null);p.displayName="StyleSheetContext";var g=i.default.useInsertionEffect||i.default.useLayoutEffect,v="undefined"!=typeof window?new m:void 0;function y(e){var t=v||s.useContext(p);return t&&("undefined"==typeof window?t.add(e):g(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}y.dynamic=function(e){return e.map(function(e){return f(e[0],e[1])}).join(" ")},t.style=y},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>o,xV:()=>l});var n=r(12115),s=r(6101),i=r(95155),o=n.forwardRef((e,t)=>{let{children:r,...s}=e,o=n.Children.toArray(r),l=o.find(u);if(l){let e=l.props.children,r=o.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(a,{...s,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,i.jsx)(a,{...s,ref:t,children:r})});o.displayName="Slot";var a=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),o=function(e,t){let r={...t};for(let n in t){let s=e[n],i=t[n];/^on[A-Z]/.test(n)?s&&i?r[n]=(...e)=>{i(...e),s(...e)}:s&&(r[n]=s):"style"===n?r[n]={...s,...i}:"className"===n&&(r[n]=[s,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(o.ref=t?(0,s.t)(t,e):e),n.cloneElement(r,o)}return n.Children.count(r)>1?n.Children.only(null):null});a.displayName="SlotClone";var l=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function u(e){return n.isValidElement(e)&&e.type===l}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,6315,7358],()=>t(18649)),_N_E=e.O()}]);