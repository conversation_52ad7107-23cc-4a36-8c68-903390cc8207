import { Badge } from "@/components/ui/badge"
import { ColumnDef } from "@tanstack/react-table"
import { Student } from "./type"
import OutClass from "./actions/out"
import { formatDate } from "@/utils/table/formatDate"


export const StudentListColumns: ColumnDef<Student>[] = [
    {
      accessorKey: "name",
      header: '姓名',
      cell: ({ row }) => <div className="font-medium">{row.original.student.name}</div>,
    },
    {
      accessorKey: "phone",
      header: "手机号",
      cell: ({ row }) => <div className="capitalize">{row.original.student.phone}</div>,
    },
    {
        accessorKey: "studentType",
        header: "学生类型",
        cell: ({ row }) => {
            const type = row.original.student.type
            if(type ==='formal'){
                return <Badge className="capitalize">正式</Badge>
            }else if(type ==='graduated'){
                return <Badge variant="destructive" className="capitalize">毕业</Badge>
            }
        },
      },
    {
      accessorKey: "joinDate",
      header: "进班日期",
      cell: ({ row }) => {
        console.log(row.original)
        const date = new Date(row.getValue("joinDate"))
        return <div>{date.toLocaleDateString("zh-CN")}</div>
      }
    },
    {
      accessorKey: "operator",
      header: "操作人",
      cell: ({ row }) => <div className="capitalize">{row.original.operator.name}</div>,
    },
    {
        accessorKey: "operatorTime",
        header: "操作时间",
        cell: ({ row }) => {
          
          return <div>{formatDate(row.getValue("operatorTime"), "yyyy-MM-dd HH:mm:ss")}</div>
        }
      },
      {
        accessorKey: "type",
        header: "类型",
        cell: ({ row }) => {
            const type = row.getValue("type")
            if(type ==='in'){
                return <Badge variant="default" className="capitalize">在班</Badge>
            }else if(type ==='out'){
                return <Badge variant="destructive" className="capitalize">退班</Badge>
            }
        },
      },
    {
      id: "actions",
      header: "操作",
      cell: ({ row }) => {
        const student = row.original
        return (
          <div className="flex space-x-2">
              <OutClass student={student} />
          </div>
        )
      },
    },
  ]