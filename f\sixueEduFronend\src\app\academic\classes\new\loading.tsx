'use client';

import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

export default function NewClassLoading() {
  return (
    <div>
      <Card className="border border-slate-200 shadow-sm overflow-hidden rounded-md">
        <CardHeader className="bg-slate-50 px-6 py-4 border-b border-slate-200">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-64 mt-1" />
        </CardHeader>
        <CardContent className="p-6 space-y-8">
          {/* 基础信息表单骨架屏 */}
          <section className="space-y-4">
            <Skeleton className="h-5 w-40 mb-3" />
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
              <Skeleton className="h-10" />
              <Skeleton className="h-10" />
              <Skeleton className="h-10" />
              <Skeleton className="h-10" />
            </div>
          </section>

          {/* 排课设置骨架屏 */}
          <section className="space-y-4">
            <Skeleton className="h-5 w-40 mb-3" />
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
              <Skeleton className="h-10" />
              <Skeleton className="h-10" />
            </div>
            <Skeleton className="h-24" />
          </section>

          {/* 班级类型骨架屏 */}
          <section className="space-y-4">
            <Skeleton className="h-5 w-40 mb-3" />
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
              <Skeleton className="h-10" />
              <Skeleton className="h-10" />
            </div>
          </section>

          {/* 教师选择骨架屏 */}
          <section className="space-y-4">
            <Skeleton className="h-5 w-40 mb-3" />
            <Skeleton className="h-10" />
          </section>

          {/* 高级选项骨架屏 */}
          <section className="space-y-4">
            <Skeleton className="h-5 w-40 mb-3" />
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
              <Skeleton className="h-10" />
              <Skeleton className="h-10" />
              <Skeleton className="h-10" />
            </div>
          </section>

          <Skeleton className="h-10 w-full mt-4" />
        </CardContent>
      </Card>
    </div>
  );
}
