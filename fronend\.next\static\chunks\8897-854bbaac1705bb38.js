(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5342,8897],{4217:(e,t,r)=>{var n=r(36713),o=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(o,""):e}},5623:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>l});var n=r(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(l(...e),e)}},7985:(e,t,r)=>{e.exports="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g},10255:(e,t,r)=>{"use strict";function n(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return n}}),r(95155),r(47650),r(85744),r(20589)},12318:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},17828:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return n}});let n=(0,r(64054).createAsyncLocalStorage)()},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(12115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:u,className:s="",children:c,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:r,strokeWidth:u?24*Number(a)/Number(o):a,className:l("lucide",s),...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),u=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:u,...s}=r;return(0,n.createElement)(a,{ref:i,iconNode:t,className:l("lucide-".concat(o(e)),u),...s})});return r.displayName="".concat(e),r}},20547:(e,t,r)=>{"use strict";r.d(t,{UC:()=>q,ZL:()=>$,bL:()=>B,l9:()=>V});var n=r(12115),o=r(85185),l=r(6101),i=r(46081),a=r(19178),u=r(92293),s=r(25519),c=r(61285),d=r(35152),f=r(34378),p=r(28905),v=r(63655),y=r(99708),h=r(5845),g=r(38168),b=r(93795),m=r(95155),x="Popover",[j,O]=(0,i.A)(x,[d.Bk]),w=(0,d.Bk)(),[C,P]=j(x),A=e=>{let{__scopePopover:t,children:r,open:o,defaultOpen:l,onOpenChange:i,modal:a=!1}=e,u=w(t),s=n.useRef(null),[f,p]=n.useState(!1),[v=!1,y]=(0,h.i)({prop:o,defaultProp:l,onChange:i});return(0,m.jsx)(d.bL,{...u,children:(0,m.jsx)(C,{scope:t,contentId:(0,c.B)(),triggerRef:s,open:v,onOpenChange:y,onOpenToggle:n.useCallback(()=>y(e=>!e),[y]),hasCustomAnchor:f,onCustomAnchorAdd:n.useCallback(()=>p(!0),[]),onCustomAnchorRemove:n.useCallback(()=>p(!1),[]),modal:a,children:r})})};A.displayName=x;var k="PopoverAnchor";n.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,l=P(k,r),i=w(r),{onCustomAnchorAdd:a,onCustomAnchorRemove:u}=l;return n.useEffect(()=>(a(),()=>u()),[a,u]),(0,m.jsx)(d.Mz,{...i,...o,ref:t})}).displayName=k;var _="PopoverTrigger",R=n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,i=P(_,r),a=w(r),u=(0,l.s)(t,i.triggerRef),s=(0,m.jsx)(v.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":z(i.open),...n,ref:u,onClick:(0,o.m)(e.onClick,i.onOpenToggle)});return i.hasCustomAnchor?s:(0,m.jsx)(d.Mz,{asChild:!0,...a,children:s})});R.displayName=_;var E="PopoverPortal",[N,S]=j(E,{forceMount:void 0}),D=e=>{let{__scopePopover:t,forceMount:r,children:n,container:o}=e,l=P(E,t);return(0,m.jsx)(N,{scope:t,forceMount:r,children:(0,m.jsx)(p.C,{present:r||l.open,children:(0,m.jsx)(f.Z,{asChild:!0,container:o,children:n})})})};D.displayName=E;var F="PopoverContent",M=n.forwardRef((e,t)=>{let r=S(F,e.__scopePopover),{forceMount:n=r.forceMount,...o}=e,l=P(F,e.__scopePopover);return(0,m.jsx)(p.C,{present:n||l.open,children:l.modal?(0,m.jsx)(T,{...o,ref:t}):(0,m.jsx)(L,{...o,ref:t})})});M.displayName=F;var T=n.forwardRef((e,t)=>{let r=P(F,e.__scopePopover),i=n.useRef(null),a=(0,l.s)(t,i),u=n.useRef(!1);return n.useEffect(()=>{let e=i.current;if(e)return(0,g.Eq)(e)},[]),(0,m.jsx)(b.A,{as:y.DX,allowPinchZoom:!0,children:(0,m.jsx)(W,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),u.current||null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;u.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),L=n.forwardRef((e,t)=>{let r=P(F,e.__scopePopover),o=n.useRef(!1),l=n.useRef(!1);return(0,m.jsx)(W,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(i=r.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var n,i;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(l.current=!0));let a=t.target;(null===(i=r.triggerRef.current)||void 0===i?void 0:i.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),W=n.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:l,disableOutsidePointerEvents:i,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:v,...y}=e,h=P(F,r),g=w(r);return(0,u.Oh)(),(0,m.jsx)(s.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:l,children:(0,m.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:v,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:p,onDismiss:()=>h.onOpenChange(!1),children:(0,m.jsx)(d.UC,{"data-state":z(h.open),role:"dialog",id:h.contentId,...g,...y,ref:t,style:{...y.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),I="PopoverClose";function z(e){return e?"open":"closed"}n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,l=P(I,r);return(0,m.jsx)(v.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})}).displayName=I,n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=w(r);return(0,m.jsx)(d.i3,{...o,...n,ref:t})}).displayName="PopoverArrow";var B=A,V=R,$=D,q=M},20570:(e,t,r)=>{var n=r(24376),o=Object.prototype,l=o.hasOwnProperty,i=o.toString,a=n?n.toStringTag:void 0;e.exports=function(e){var t=l.call(e,a),r=e[a];try{e[a]=void 0;var n=!0}catch(e){}var o=i.call(e);return n&&(t?e[a]=r:delete e[a]),o}},24357:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},24376:(e,t,r)=>{e.exports=r(82500).Symbol},36645:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let n=r(88229)._(r(67357));function o(e,t){var r;let o={};"function"==typeof e&&(o.loader=e);let l={...o,...t};return(0,n.default)({...l,modules:null==(r=l.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},36713:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},36815:(e,t,r)=>{var n=r(4217),o=r(67460),l=r(70771),i=0/0,a=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,s=/^0o[0-7]+$/i,c=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(l(e))return i;if(o(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=o(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=n(e);var r=u.test(e);return r||s.test(e)?c(e.slice(2),r?2:8):a.test(e)?i:+e}},45964:(e,t,r)=>{var n=r(67460),o=r(76685),l=r(36815),i=Math.max,a=Math.min;e.exports=function(e,t,r){var u,s,c,d,f,p,v=0,y=!1,h=!1,g=!0;if("function"!=typeof e)throw TypeError("Expected a function");function b(t){var r=u,n=s;return u=s=void 0,v=t,d=e.apply(n,r)}function m(e){var r=e-p,n=e-v;return void 0===p||r>=t||r<0||h&&n>=c}function x(){var e,r,n,l=o();if(m(l))return j(l);f=setTimeout(x,(e=l-p,r=l-v,n=t-e,h?a(n,c-r):n))}function j(e){return(f=void 0,g&&u)?b(e):(u=s=void 0,d)}function O(){var e,r=o(),n=m(r);if(u=arguments,s=this,p=r,n){if(void 0===f)return v=e=p,f=setTimeout(x,t),y?b(e):d;if(h)return clearTimeout(f),f=setTimeout(x,t),b(p)}return void 0===f&&(f=setTimeout(x,t)),d}return t=l(t)||0,n(r)&&(y=!!r.leading,c=(h="maxWait"in r)?i(l(r.maxWait)||0,t):c,g="trailing"in r?!!r.trailing:g),O.cancel=function(){void 0!==f&&clearTimeout(f),v=0,u=p=s=f=void 0},O.flush=function(){return void 0===f?d:j(o())},O}},47863:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},47924:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},48611:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},55028:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var n=r(36645),o=r.n(n)},62146:(e,t,r)=>{"use strict";function n(e){let{reason:t,children:r}=e;return r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return n}}),r(45262)},64054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{bindSnapshot:function(){return i},createAsyncLocalStorage:function(){return l},createSnapshot:function(){return a}});let r=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class n{disable(){throw r}getStore(){}run(){throw r}exit(){throw r}enterWith(){throw r}static bind(e){return e}}let o="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function l(){return o?new o:new n}function i(e){return o?o.bind(e):n.bind(e)}function a(){return o?o.snapshot():function(e,...t){return e(...t)}}},64439:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},66474:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},67357:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(95155),o=r(12115),l=r(62146);function i(e){return{default:e&&"default"in e?e.default:e}}r(10255);let a={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},u=function(e){let t={...a,...e},r=(0,o.lazy)(()=>t.loader().then(i)),u=t.loading;function s(e){let i=u?(0,n.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,a=!t.ssr||!!t.loading,s=a?o.Suspense:o.Fragment,c=t.ssr?(0,n.jsxs)(n.Fragment,{children:[null,(0,n.jsx)(r,{...e})]}):(0,n.jsx)(l.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(s,{...a?{fallback:i}:{},children:c})}return s.displayName="LoadableComponent",s}},67460:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},70771:(e,t,r)=>{var n=r(98233),o=r(48611);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var n=r(52596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:a}=t,u=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let l=o(t)||o(n);return i[e][l]}),s=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return l(e,u,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...s}[t]):({...a,...s})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},76685:(e,t,r)=>{var n=r(82500);e.exports=function(){return n.Date.now()}},82500:(e,t,r)=>{var n=r(7985),o="object"==typeof self&&self&&self.Object===Object&&self;e.exports=n||o||Function("return this")()},85744:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return n.workAsyncStorageInstance}});let n=r(17828)},98233:(e,t,r)=>{var n=r(24376),o=r(20570),l=r(64439),i=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":i&&i in Object(e)?o(e):l(e)}},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i,xV:()=>u});var n=r(12115),o=r(6101),l=r(95155),i=n.forwardRef((e,t)=>{let{children:r,...o}=e,i=n.Children.toArray(r),u=i.find(s);if(u){let e=u.props.children,r=i.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(a,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,l.jsx)(a,{...o,ref:t,children:r})});i.displayName="Slot";var a=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),i=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{l(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(i.ref=t?(0,o.t)(t,e):e),n.cloneElement(r,i)}return n.Children.count(r)>1?n.Children.only(null):null});a.displayName="SlotClone";var u=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function s(e){return n.isValidElement(e)&&e.type===u}}}]);