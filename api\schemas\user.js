export const userRegisterSchema = {
    tags: ['user'],
    summary: '用户注册',
    description: '通过账号密码注册',
    body: {
        type: 'object',
        required: ['account', 'password',],
        properties: {
            account: {
                type: 'string',
                minLength: 3,
                maxLength: 20,
                description: '账号'
            },
            password: {
                type: 'string',
                minLength: 6,
                description: '密码'
            },
            name: {
                type: 'string',
                description: '用户名称'
            },
        }
    },
    response: {
        200: {
            type: 'object',
            properties: {
                code: { type: 'number' },
                message: { type: 'string' },
                data: {
                    type: 'object',
                    properties: {
                        accessToken: { type: 'string' },
                    }
                }
            }
        }
    }
}

export const userLoginSchema = {
    tags: ['user'],
    summary: '用户登录',
    description: '通过账号密码登录',
    body: {
        type: 'object',
        required: ['account', 'password'],
        properties: {
            account: {
                type: 'string',
                minLength: 3,
                maxLength: 20,
                description: '账号'
            },
            password: {
                type: 'string',
                minLength: 6,
                description: '密码'
            }
        }
    },
    response: {
        200: {
            type: 'object',
            properties: {
                code: { type: 'number' },
                message: { type: 'string' },
                data: {
                    type: 'object',
                    properties: {
                        user: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                account: { type: 'string' },
                                name: { type: 'string' },
                                notification_count: { type: 'number' },
                                institution_subjectName: { type: 'string' },
                                institution_name: { type: 'string' },
                                institution_logo: { type: 'string' },

                            }
                        },
                        accessToken: { type: 'string' },
                        refreshToken: { type: 'string' }
                    }
                }
            }
        }
    }
}


export const userRoleMenus = {
    tags: ['user'],
    summary: '用户角色菜单',
    description: '用户角色菜单',
    response: {
        200: {
            type: 'object',
            properties: {
                code: { type: 'number' },
                message: { type: 'string' },
                data: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            path: { type: 'string' },
                            component: { type: 'string' },
                            icon: { type: 'string' },
                            sort: { type: 'number' },
                            children: { type: 'array' }
                        }
                    }
                }
            }
        }
    }
}