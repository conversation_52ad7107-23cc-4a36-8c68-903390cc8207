import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import { AUTH_ERROR, INTERNAL_ERROR, NOT_FOUND_ERROR, VALIDATION_ERROR } from '../errors/index.js';
import { executeQuery } from '../utils/db-utils.js';
import { formatUserData, validatePassword } from '../scripts/userUtils.js';

/**
 * Authentication Service
 * Contains business logic for authentication-related operations
 */
export const authService = {
    /**
     * User login
     * @param {Object} params - Parameters
     * @param {string} params.account - User account
     * @param {string} params.password - User password
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} Login result
     */
    async login({ account, password, fastify }) {
        try {
            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Query user information
                const userResult = await executeQuery(client, fastify,
                    `SELECT
                        u.id, u.account, u.password, u.name,u.phone, u.avatar,
                        u.active, u."createdAt", u."updatedAt",
                        i.id AS "institutionId", i.name AS "institutionName",
                        i.logo AS "institutionLogo", i."subjectName" AS "institutionSubjectName",
                        (SELECT COUNT(*) FROM user_notifications un
                         WHERE un."userId" = u.id AND un.status = 'unread') AS "notificationCount"
                     FROM users u
                     LEFT JOIN user_institution ui ON ui."userId" = u.id
                     LEFT JOIN institutions i ON i.id = ui."institutionId"
                     WHERE u.account = $1`,
                    [account],
                    { queryName: 'getUserByAccount' }
                );

                // Check if user exists
                if (userResult.rows.length === 0) {
                    throw new NOT_FOUND_ERROR('账号不存在');
                }

                // Get user data
                const user = userResult.rows[0];

                // Check if user is active
                console.log(user," user")
                if (!user.active) {
                    throw new AUTH_ERROR('账号已被禁用');
                }

                // Verify password
                const validPassword = await bcrypt.compare(password, user.password);
                if (!validPassword) {
                    throw new AUTH_ERROR('密码错误');
                }

                // Clear existing tokens for this user before generating new ones
                fastify.log.info({
                    msg: '清除用户现有token记录',
                    userId: user.id,
                    account: user.account
                });

                // 1. Delete all existing refresh tokens for this user from database
                await executeQuery(client, fastify,
                    `DELETE FROM user_tokens WHERE "userId" = $1`,
                    [user.id],
                    { queryName: 'clearExistingTokens' }
                );

                // 2. Clear Redis token hash to invalidate any existing access tokens
                const tokenHashKey = `user:${user.id}:tokenHash`;
                await fastify.redis.del(tokenHashKey);

                // 3. Clear user auth cache
                const cacheKey = `user:${user.id}:authData`;
                await fastify.redis.del(cacheKey);

                // Generate new tokens
                const [accessToken, refreshToken] = await Promise.all([
                    fastify.generateToken({
                        id: user.id,
                        account: user.account,
                        name: user.name,
                        institutionId: user.institutionId,
                        hasInstitution: user.institutionId ? true : false
                    }),
                    fastify.generateRefreshToken({
                        id: user.id,
                        account: user.account
                    })
                ]);

                // Store new refresh token
                const tokenId = uuidv4();
                await executeQuery(client, fastify,
                    `INSERT INTO user_tokens (id, "userId", token, type, "expiresAt", "createdAt", "updatedAt")
                     VALUES ($1, $2, $3, 'refresh', NOW() + INTERVAL '7 days', NOW(), NOW())`,
                    [tokenId, user.id, refreshToken],
                    { queryName: 'storeRefreshToken' }
                );

                // Store new token hash in Redis for auth middleware verification
                const currentTokenHash = crypto.createHash('sha256').update(accessToken).digest('hex');
                await fastify.redis.set(tokenHashKey, currentTokenHash, 'EX', 86400); // 24 hours

                fastify.log.info({
                    msg: '用户登录成功，已清除旧token并生成新token',
                    userId: user.id,
                    account: user.account
                });

                // Commit transaction
                await client.query('COMMIT');

                // Remove sensitive information
                delete user.password;

                // Format user data
                const formattedUser = formatUserData(user);

                // Return result
                return {
                    user: formattedUser,
                    accessToken,
                    refreshToken
                };
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR || error instanceof AUTH_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`登录失败: ${error.message}`);
        }
    },

    /**
     * User logout
     * @param {Object} params - Parameters
     * @param {string} params.userId - User ID
     * @param {string} params.token - Access token
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<void>}
     */
    async logout({ userId, token, fastify }) {
        try {
            // First, invalidate the token in Redis which is the most critical part
            // This will prevent the token from being used even if database operations fail
            if (token && userId) {
                try {
                    // Invalidate token in Redis with a short timeout
                    const tokenHashKey = `user:${userId}:tokenHash`;
                    const cacheKey = `user:${userId}:authData`;

                    // Generate a new random hash to invalidate the current token
                    const randomHash = crypto.randomBytes(16).toString('hex');

                    // Use multi to ensure atomic operations
                    await fastify.redis.multi()
                        .set(tokenHashKey, randomHash, 'EX', 86400) // 24 hours
                        .del(cacheKey)
                        .exec();

                    fastify.log.info({
                        msg: '令牌已在Redis中成功吊销',
                        userId
                    });
                } catch (redisError) {
                    // Log Redis error but continue with database operations
                    fastify.log.error({
                        msg: 'Redis吊销令牌失败',
                        error: redisError.message,
                        userId
                    });
                }
            }

            // Now handle database operations
            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Set statement timeout to prevent long-running queries
                await client.query('SET statement_timeout = 5000'); // 5 seconds timeout

                // Delete all user tokens (both refresh and access tokens)
                await executeQuery(client, fastify,
                    `DELETE FROM user_tokens
                     WHERE "userId" = $1`,
                    [userId],
                    {
                        queryName: 'deleteAllUserTokens',
                        timeout: 3000 // 3 seconds timeout
                    }
                );

                // 注意：不再使用数据库黑名单，Redis token hash失效已足够
                fastify.log.info({
                    msg: '所有用户令牌已从数据库中删除，Redis中的token hash已失效',
                    userId
                });

                // Reset statement timeout
                await client.query('SET statement_timeout = 0');

                // Commit transaction
                await client.query('COMMIT');

                fastify.log.info({
                    msg: '用户登出成功',
                    userId
                });
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK').catch(rollbackError => {
                    fastify.log.error({
                        msg: '事务回滚失败',
                        error: rollbackError.message,
                        userId
                    });
                });

                // Since we've already invalidated the token in Redis, we can log the error but don't need to throw
                fastify.log.error({
                    msg: '数据库操作失败，但令牌已在Redis中吊销',
                    error: error.message,
                    userId
                });
            } finally {
                // Reset statement timeout before releasing connection
                try {
                    await client.query('SET statement_timeout = 0');
                } catch (timeoutError) {
                    fastify.log.error({
                        msg: '重置语句超时失败',
                        error: timeoutError.message
                    });
                }

                // Release database connection
                client.release();
            }

            // Return success even if some operations failed, as long as the token was invalidated in Redis
            return;
        } catch (error) {
            fastify.log.error({
                msg: '登出过程中发生错误',
                error: error.message,
                stack: error.stack,
                userId
            });
            throw new INTERNAL_ERROR(`登出失败: ${error.message}`);
        }
    },

    /**
     * Get current user info
     * @param {Object} params - Parameters
     * @param {string} params.userId - User ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} User info
     */
    async getCurrentUser({ userId, fastify }) {
        try {
            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Query user information
                const userResult = await executeQuery(client, fastify,
                    `SELECT
                        u.id, u.account, u.name, u.phone, u.avatar,
                        u.active,  u."createdAt", u."updatedAt",
                        i.id AS "institutionId", i.name AS "institutionName",
                        i.logo AS "institutionLogo", i."subjectName" AS "institutionSubjectName",
                        (SELECT COUNT(*) FROM user_notifications un
                         WHERE un."userId" = u.id AND un.status = 'unread') AS "notificationCount",
                        (SELECT json_agg(r.name) FROM roles r
                         JOIN user_roles ur ON r.id = ur."roleId"
                         WHERE ur."userId" = u.id) AS roles
                     FROM users u
                     LEFT JOIN user_institution ui ON ui."userId" = u.id
                     LEFT JOIN institutions i ON i.id = ui."institutionId"
                     WHERE u.id = $1`,
                    [userId],
                    { queryName: 'getUserById' }
                );

                // Check if user exists
                if (userResult.rows.length === 0) {
                    throw new NOT_FOUND_ERROR('用户不存在');
                }

                // Get user data
                const user = userResult.rows[0];

                // Get user permissions
                const permissionsResult = await executeQuery(client, fastify,
                    `SELECT DISTINCT p.code
                     FROM permissions p
                     JOIN role_permissions rp ON p.id = rp."permissionId"
                     JOIN roles r ON rp."roleId" = r.id
                     JOIN user_roles ur ON r.id = ur."roleId"
                     WHERE ur."userId" = $1`,
                    [userId],
                    { queryName: 'getUserPermissions' }
                );

                // Format user data
                const formattedUser = formatUserData(user);

                // Add permissions
                formattedUser.permissions = permissionsResult.rows.map(row => row.code);

                return formattedUser;
            } finally {
                client.release();
            }
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`获取用户信息失败: ${error.message}`);
        }
    },

    /**
     * Update current user info
     * @param {Object} params - Parameters
     * @param {string} params.userId - User ID
     * @param {Object} params.userData - User data
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<void>}
     */
    async updateCurrentUser({ userId, userData, fastify }) {
        try {
            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Check if user exists
                const userResult = await executeQuery(client, fastify,
                    `SELECT id FROM users WHERE id = $1`,
                    [userId],
                    { queryName: 'checkUserExists' }
                );

                if (userResult.rows.length === 0) {
                    throw new NOT_FOUND_ERROR('用户不存在');
                }

                // Extract user data
                const { name, email, phone, avatar } = userData;

                // Validate email if provided
                if (email) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(email)) {
                        throw new VALIDATION_ERROR('邮箱格式不正确');
                    }

                    // Check if email is already used
                    const emailResult = await executeQuery(client, fastify,
                        `SELECT id FROM users WHERE email = $1 AND id != $2`,
                        [email, userId],
                        { queryName: 'checkEmailExists' }
                    );

                    if (emailResult.rows.length > 0) {
                        throw new VALIDATION_ERROR('邮箱已被使用');
                    }
                }

                // Validate phone if provided
                if (phone) {
                    const phoneRegex = /^1[3-9]\d{9}$/;
                    if (!phoneRegex.test(phone)) {
                        throw new VALIDATION_ERROR('手机号格式不正确');
                    }

                    // Check if phone is already used
                    const phoneResult = await executeQuery(client, fastify,
                        `SELECT id FROM users WHERE phone = $1 AND id != $2`,
                        [phone, userId],
                        { queryName: 'checkPhoneExists' }
                    );

                    if (phoneResult.rows.length > 0) {
                        throw new VALIDATION_ERROR('手机号已被使用');
                    }
                }

                // Update user
                await executeQuery(client, fastify,
                    `UPDATE users SET
                        name = COALESCE($1, name),
                        email = COALESCE($2, email),
                        phone = COALESCE($3, phone),
                        avatar = COALESCE($4, avatar),
                        "updatedAt" = NOW()
                     WHERE id = $5`,
                    [name, email, phone, avatar, userId],
                    { queryName: 'updateUser' }
                );

                // Commit transaction
                await client.query('COMMIT');
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR || error instanceof VALIDATION_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`更新用户信息失败: ${error.message}`);
        }
    },

    /**
     * Change password
     * @param {Object} params - Parameters
     * @param {string} params.userId - User ID
     * @param {string} params.oldPassword - Old password
     * @param {string} params.newPassword - New password
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<void>}
     */
    async changePassword({ userId, oldPassword, newPassword, fastify }) {
        try {
            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Get user password
                const userResult = await executeQuery(client, fastify,
                    `SELECT password FROM users WHERE id = $1`,
                    [userId],
                    { queryName: 'getUserPassword' }
                );

                if (userResult.rows.length === 0) {
                    throw new NOT_FOUND_ERROR('用户不存在');
                }

                // Verify old password
                const validPassword = await bcrypt.compare(oldPassword, userResult.rows[0].password);
                if (!validPassword) {
                    throw new AUTH_ERROR('原密码错误');
                }

                // Validate new password
                if (!validatePassword(newPassword)) {
                    throw new VALIDATION_ERROR('新密码不符合要求，密码长度至少为8位，且必须包含字母和数字');
                }

                // Hash new password
                const salt = await bcrypt.genSalt(10);
                const hashedPassword = await bcrypt.hash(newPassword, salt);

                // Update password
                await executeQuery(client, fastify,
                    `UPDATE users SET
                        password = $1,
                        "updatedAt" = NOW()
                     WHERE id = $2`,
                    [hashedPassword, userId],
                    { queryName: 'updatePassword' }
                );

                // Delete refresh tokens
                await executeQuery(client, fastify,
                    `DELETE FROM user_tokens
                     WHERE "userId" = $1 AND type = 'refresh'`,
                    [userId],
                    { queryName: 'deleteRefreshTokens' }
                );

                // Commit transaction
                await client.query('COMMIT');
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR || error instanceof AUTH_ERROR || error instanceof VALIDATION_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`修改密码失败: ${error.message}`);
        }
    },

    /**
     * Reset password
     * @param {Object} params - Parameters
     * @param {string} params.userId - User ID
     * @param {string} params.adminUserId - Admin user ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} Reset result
     */
    async resetPassword({ userId, adminUserId, fastify }) {
        try {
            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Check if user exists
                const userResult = await executeQuery(client, fastify,
                    `SELECT id, account FROM users WHERE id = $1`,
                    [userId],
                    { queryName: 'checkUserExists' }
                );

                if (userResult.rows.length === 0) {
                    throw new NOT_FOUND_ERROR('用户不存在');
                }

                // Check if admin has permission
                const permissionResult = await executeQuery(client, fastify,
                    `SELECT COUNT(*) AS count
                     FROM permissions p
                     JOIN role_permissions rp ON p.id = rp."permissionId"
                     JOIN roles r ON rp."roleId" = r.id
                     JOIN user_roles ur ON r.id = ur."roleId"
                     WHERE ur."userId" = $1 AND p.code = 'system:user:reset-password'`,
                    [adminUserId],
                    { queryName: 'checkResetPasswordPermission' }
                );

                if (parseInt(permissionResult.rows[0].count) === 0) {
                    throw new AUTH_ERROR('没有重置密码权限');
                }

                // Generate random password
                const randomPassword = Math.random().toString(36).slice(-8) + Math.floor(Math.random() * 10);

                // Hash password
                const salt = await bcrypt.genSalt(10);
                const hashedPassword = await bcrypt.hash(randomPassword, salt);

                // Update password
                await executeQuery(client, fastify,
                    `UPDATE users SET
                        password = $1,
                        "updatedAt" = NOW()
                     WHERE id = $2`,
                    [hashedPassword, userId],
                    { queryName: 'resetPassword' }
                );

                // Delete refresh tokens
                await executeQuery(client, fastify,
                    `DELETE FROM user_tokens
                     WHERE "userId" = $1 AND type = 'refresh'`,
                    [userId],
                    { queryName: 'deleteRefreshTokens' }
                );

                // Commit transaction
                await client.query('COMMIT');

                // Return result
                return {
                    account: userResult.rows[0].account,
                    password: randomPassword
                };
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR || error instanceof AUTH_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`重置密码失败: ${error.message}`);
        }
    },

    /**
     * Register new user
     * @param {Object} params - Parameters
     * @param {Object} params.userData - User data
     * @param {string} params.adminUserId - Admin user ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} Register result
     */
    async register({ userData, adminUserId, fastify }) {
        try {
            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Check if admin has permission
                const permissionResult = await executeQuery(client, fastify,
                    `SELECT COUNT(*) AS count
                     FROM permissions p
                     JOIN role_permissions rp ON p.id = rp."permissionId"
                     JOIN roles r ON rp."roleId" = r.id
                     JOIN user_roles ur ON r.id = ur."roleId"
                     WHERE ur."userId" = $1 AND p.code = 'system:user:create'`,
                    [adminUserId],
                    { queryName: 'checkCreateUserPermission' }
                );

                if (parseInt(permissionResult.rows[0].count) === 0) {
                    throw new AUTH_ERROR('没有创建用户权限');
                }

                // Extract user data
                const {
                    account, name, password, email, phone,
                    institutionId, roleIds = []
                } = userData;

                // Validate account
                if (!account || account.length < 4) {
                    throw new VALIDATION_ERROR('账号长度至少为4位');
                }

                // Check if account exists
                const accountResult = await executeQuery(client, fastify,
                    `SELECT id FROM users WHERE account = $1`,
                    [account],
                    { queryName: 'checkAccountExists' }
                );

                if (accountResult.rows.length > 0) {
                    throw new VALIDATION_ERROR('账号已存在');
                }

                // Validate password
                if (!validatePassword(password)) {
                    throw new VALIDATION_ERROR('密码不符合要求，密码长度至少为8位，且必须包含字母和数字');
                }

                // Validate email if provided
                if (email) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(email)) {
                        throw new VALIDATION_ERROR('邮箱格式不正确');
                    }

                    // Check if email is already used
                    const emailResult = await executeQuery(client, fastify,
                        `SELECT id FROM users WHERE email = $1`,
                        [email],
                        { queryName: 'checkEmailExists' }
                    );

                    if (emailResult.rows.length > 0) {
                        throw new VALIDATION_ERROR('邮箱已被使用');
                    }
                }

                // Validate phone if provided
                if (phone) {
                    const phoneRegex = /^1[3-9]\d{9}$/;
                    if (!phoneRegex.test(phone)) {
                        throw new VALIDATION_ERROR('手机号格式不正确');
                    }

                    // Check if phone is already used
                    const phoneResult = await executeQuery(client, fastify,
                        `SELECT id FROM users WHERE phone = $1`,
                        [phone],
                        { queryName: 'checkPhoneExists' }
                    );

                    if (phoneResult.rows.length > 0) {
                        throw new VALIDATION_ERROR('手机号已被使用');
                    }
                }

                // Hash password
                const salt = await bcrypt.genSalt(10);
                const hashedPassword = await bcrypt.hash(password, salt);

                // Generate user ID
                const userId = uuidv4();

                // Create user
                await executeQuery(client, fastify,
                    `INSERT INTO users (
                        id, account, password, name, email, phone,
                        status, "createdAt", "updatedAt"
                     ) VALUES (
                        $1, $2, $3, $4, $5, $6,
                        'active', NOW(), NOW()
                     )`,
                    [userId, account, hashedPassword, name, email, phone],
                    { queryName: 'createUser' }
                );

                // Associate user with institution if provided
                if (institutionId) {
                    await executeQuery(client, fastify,
                        `INSERT INTO user_institution (
                            id, "userId", "institutionId", "isAdmin", "createdAt"
                         ) VALUES (
                            $1, $2, $3, false, NOW()
                         )`,
                        [uuidv4(), userId, institutionId],
                        { queryName: 'associateUserWithInstitution' }
                    );
                }

                // Associate user with roles if provided
                if (roleIds.length > 0) {
                    for (const roleId of roleIds) {
                        await executeQuery(client, fastify,
                            `INSERT INTO user_roles (
                                id, "userId", "roleId", "createdAt"
                             ) VALUES (
                                $1, $2, $3, NOW()
                             )`,
                            [uuidv4(), userId, roleId],
                            { queryName: 'associateUserWithRole' }
                        );
                    }
                }

                // Commit transaction
                await client.query('COMMIT');

                // Return result
                return {
                    id: userId,
                    account,
                    name
                };
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            if (error instanceof AUTH_ERROR || error instanceof VALIDATION_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`注册用户失败: ${error.message}`);
        }
    },

    /**
     * Refresh token
     * @param {Object} params - Parameters
     * @param {string} params.refreshToken - Refresh token
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} New tokens
     */
    async refreshToken({ refreshToken, fastify }) {
        try {
            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Verify refresh token
                const tokenResult = await executeQuery(client, fastify,
                    `SELECT t.*, u.id, u.account, u.name, u.active, i.id AS "institutionId"
                     FROM user_tokens t
                     JOIN users u ON t."userId" = u.id
                     LEFT JOIN user_institution ui ON ui."userId" = u.id
                     LEFT JOIN institutions i ON i.id = ui."institutionId"
                     WHERE t.token = $1 AND t.type = 'refresh' AND t."expiresAt" > NOW()`,
                    [refreshToken],
                    { queryName: 'verifyRefreshToken' }
                );

                if (tokenResult.rows.length === 0) {
                    throw new AUTH_ERROR('无效的刷新令牌');
                }

                const tokenData = tokenResult.rows[0];

                // Check if user is active
                if (!tokenData.active) {
                    throw new AUTH_ERROR('账号已被禁用');
                }

                // Generate new tokens
                const [newAccessToken, newRefreshToken] = await Promise.all([
                    fastify.generateToken({
                        id: tokenData.id,
                        account: tokenData.account,
                        name: tokenData.name,
                        institutionId: tokenData.institutionId,
                        hasInstitution: tokenData.institutionId ? true : false
                    }),
                    fastify.generateRefreshToken({
                        id: tokenData.id,
                        account: tokenData.account
                    })
                ]);

                // Delete old refresh token
                await executeQuery(client, fastify,
                    `DELETE FROM user_tokens WHERE token = $1`,
                    [refreshToken],
                    { queryName: 'deleteOldRefreshToken' }
                );

                // Store new refresh token
                const tokenId = uuidv4();
                await executeQuery(client, fastify,
                    `INSERT INTO user_tokens (id, "userId", token, type, "expiresAt", "createdAt", "updatedAt")
                     VALUES ($1, $2, $3, 'refresh', NOW() + INTERVAL '7 days', NOW(), NOW())`,
                    [tokenId, tokenData.id, newRefreshToken],
                    { queryName: 'storeNewRefreshToken' }
                );

                // Store new token hash in Redis for auth middleware verification
                const tokenHashKey = `user:${tokenData.id}:tokenHash`;
                const currentTokenHash = crypto.createHash('sha256').update(newAccessToken).digest('hex');
                await fastify.redis.set(tokenHashKey, currentTokenHash, 'EX', 86400); // 24 hours

                // Commit transaction
                await client.query('COMMIT');

                // Return new tokens
                return {
                    accessToken: newAccessToken,
                    refreshToken: newRefreshToken
                };
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            if (error instanceof AUTH_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`刷新令牌失败: ${error.message}`);
        }
    }
};

export default authService;
