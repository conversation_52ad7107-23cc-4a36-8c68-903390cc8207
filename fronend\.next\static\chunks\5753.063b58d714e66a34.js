"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5753],{14636:(e,t,a)=>{a.d(t,{AM:()=>i,Wv:()=>o,hl:()=>d});var s=a(95155),r=a(12115),n=a(20547),l=a(59434);let i=n.bL,o=n.l9,d=r.forwardRef((e,t)=>{let{className:a,align:r="center",sideOffset:i=4,...o}=e;return(0,s.jsx)(n.ZL,{children:(0,s.jsx)(n.UC,{ref:t,align:r,sideOffset:i,className:(0,l.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...o})})});d.displayName=n.UC.displayName},30285:(e,t,a)=>{a.d(t,{$:()=>d,r:()=>o});var s=a(95155),r=a(12115),n=a(99708),l=a(74466),i=a(59434);let o=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,t)=>{let{className:a,variant:r,size:l,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(o({variant:r,size:l,className:a})),ref:t,...c})});d.displayName="Button"},48432:(e,t,a)=>{a.r(t),a.d(t,{default:()=>v});var s=a(95155),r=a(12115),n=a(42355),l=a(13052),i=a(5623),o=a(59434),d=a(30285);let c=e=>{let{className:t,...a}=e;return(0,s.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,o.cn)("mx-auto flex w-full justify-center",t),...a})};c.displayName="Pagination";let u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("ul",{ref:t,className:(0,o.cn)("flex flex-row items-center gap-1",a),...r})});u.displayName="PaginationContent";let m=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("li",{ref:t,className:(0,o.cn)("",a),...r})});m.displayName="PaginationItem";let x=e=>{let{className:t,isActive:a,size:r="icon",...n}=e;return(0,s.jsx)("a",{"aria-current":a?"page":void 0,className:(0,o.cn)((0,d.r)({variant:a?"outline":"ghost",size:r}),t),...n})};x.displayName="PaginationLink";let h=e=>{let{className:t,...a}=e;return(0,s.jsxs)(x,{"aria-label":"Go to previous page",size:"default",className:(0,o.cn)("gap-1 pl-2.5",t),...a,children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"上一页"})]})};h.displayName="PaginationPrevious";let p=e=>{let{className:t,...a}=e;return(0,s.jsxs)(x,{"aria-label":"Go to next page",size:"default",className:(0,o.cn)("gap-1 pr-2.5",t),...a,children:[(0,s.jsx)("span",{children:"下一页"}),(0,s.jsx)(l.A,{className:"h-4 w-4"})]})};p.displayName="PaginationNext";let f=e=>{let{className:t,...a}=e;return(0,s.jsxs)("span",{"aria-hidden":!0,className:(0,o.cn)("flex h-9 w-9 items-center justify-center",t),...a,children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"更多页"})]})};f.displayName="PaginationEllipsis";var g=a(59409);function v(e){let{currentPage:t,pageSize:a,totalItems:r,onPageChange:i,onPageSizeChange:o}=e,d=Math.ceil(r/a),v=(()=>{let e=[];if(d<=5){for(let t=1;t<=d;t++)e.push(t);return e}e.push(1);let a=Math.max(2,t-1),s=Math.min(t+1,d-1);2===a&&(s=Math.min(a+2,d-1)),s===d-1&&(a=Math.max(s-2,2)),a>2&&e.push("ellipsis-start");for(let t=a;t<=s;t++)e.push(t);return s<d-1&&e.push("ellipsis-end"),d>1&&e.push(d),e})(),b=0===r?0:(t-1)*a+1,j=Math.min(t*a,r);return(0,s.jsxs)("div",{className:"flex flex-col gap-4 px-2 py-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"显示"}),(0,s.jsxs)(g.l6,{value:a.toString(),onValueChange:e=>{o(Number(e))},children:[(0,s.jsx)(g.bq,{className:"w-[4.5rem] h-7 text-xs border-border/60 hover:bg-accent/30 transition-colors",children:(0,s.jsx)(g.yv,{})}),(0,s.jsx)(g.gC,{children:[10,20,30,50].map(e=>(0,s.jsx)(g.eb,{value:e.toString(),className:"text-xs",children:e},e))})]}),(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"条"}),(0,s.jsx)("span",{className:"text-muted-foreground/40 mx-1.5",children:"|"}),r>0?(0,s.jsxs)("span",{className:"text-muted-foreground/80",children:[b,"-",j," / ",r," 条记录"]}):(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"0 条记录"})]}),(0,s.jsx)(c,{children:(0,s.jsxs)(u,{className:"gap-1",children:[(0,s.jsx)(m,{children:(0,s.jsx)(h,{onClick:()=>i(Math.max(1,t-1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(1===t?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,s.jsx)(n.A,{className:"h-4 w-4 mr-1"})})}),v.map((e,a)=>"ellipsis-start"===e||"ellipsis-end"===e?(0,s.jsx)(m,{children:(0,s.jsx)(f,{className:"h-8 w-8 flex items-center justify-center text-xs"})},"ellipsis-".concat(a)):(0,s.jsx)(m,{children:(0,s.jsx)(x,{onClick:()=>i(e),isActive:t===e,className:"h-8 w-8 text-xs font-medium rounded-md transition-colors data-[active]:bg-primary data-[active]:text-primary-foreground hover:bg-accent/50 hover:text-accent-foreground/90","aria-label":"前往第 ".concat(e," 页"),children:e})},e)),(0,s.jsx)(m,{children:(0,s.jsx)(p,{onClick:()=>i(Math.min(d,t+1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(t===d||0===d?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,s.jsx)(l.A,{className:"h-4 w-4 ml-1"})})})]})})]})}},50228:(e,t,a)=>{a.d(t,{P:()=>n,s:()=>l});var s=a(95155),r=a(73069);let n=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e?(0,s.jsx)("div",{className:"text-sm ".concat(t?"text-muted-foreground":""),children:e}):(0,s.jsx)("div",{})},l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return e?(0,s.jsx)(r.c,{maxDisplayLength:t,children:e}):(0,s.jsx)("div",{className:"text-sm text-muted-foreground"})}},62523:(e,t,a)=>{a.d(t,{p:()=>l});var s=a(95155),r=a(12115),n=a(59434);let l=r.forwardRef((e,t)=>{let{className:a,type:r,...l}=e;return(0,s.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...l})});l.displayName="Input"},73069:(e,t,a)=>{a.d(t,{c:()=>m});var s=a(95155),r=a(12115),n=a(47863),l=a(66474),i=a(5196),o=a(24357),d=a(59434),c=a(14636),u=a(30285);function m(e){let{children:t,maxDisplayLength:a=15,className:m,popoverWidth:x="auto",showBorder:h=!1}=e,[p,f]=r.useState(!1),[g,v]=r.useState(!1),b=r.useMemo(()=>{if("string"==typeof t||"number"==typeof t)return t.toString();try{var e;let a=document.createElement("div");return a.innerHTML=(null==t?void 0:null===(e=t.props)||void 0===e?void 0:e.children)||"",a.textContent||""}catch(e){return console.warn("Could not extract text from children:",e),""}},[t]),j=r.useMemo(()=>{if("string"==typeof t||"number"==typeof t){let e=t.toString();return e.length>a?e.slice(0,a):e}return t},[t,a]),N=async()=>{try{await navigator.clipboard.writeText(b),v(!0),setTimeout(()=>v(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,s.jsxs)(c.AM,{open:p,onOpenChange:f,children:[(0,s.jsx)(c.Wv,{asChild:!0,children:(0,s.jsxs)(u.$,{variant:h?"outline":"ghost",role:"combobox","aria-expanded":p,className:(0,d.cn)("w-fit justify-between font-normal px-2 py-1 h-auto",!h&&"border-0 shadow-none",m),children:[(0,s.jsx)("span",{className:"mr-2 truncate",children:j}),p?(0,s.jsx)(n.A,{className:"h-4 w-4 shrink-0 opacity-50"}):(0,s.jsx)(l.A,{className:"h-4 w-4 shrink-0 opacity-50"})]})}),(0,s.jsx)(c.hl,{className:"p-0",align:"start",style:{width:x},children:(0,s.jsxs)("div",{className:"flex items-center gap-2 p-2",children:[(0,s.jsx)("span",{className:"text-sm break-all",children:b}),(0,s.jsxs)(u.$,{variant:"ghost",size:"icon",className:"h-8 w-8 shrink-0",onClick:N,children:[g?(0,s.jsx)(i.A,{className:"h-4 w-4"}):(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:g?"Copied":"Copy text"})]})]})})]})}},85753:(e,t,a)=>{a.r(t),a.d(t,{default:()=>y});var s=a(95155),r=a(9110),n=a(12115),l=a(55028),i=a(50228),o=a(26126);let d=(0,l.default)(()=>a.e(5236).then(a.bind(a,55236)),{loadableGenerated:{webpack:()=>[55236]},ssr:!1}),c=(0,l.default)(()=>a.e(3443).then(a.bind(a,3443)),{loadableGenerated:{webpack:()=>[3443]},ssr:!1}),u=(0,l.default)(()=>a.e(312).then(a.bind(a,70312)),{loadableGenerated:{webpack:()=>[70312]},ssr:!1}),m=[{accessorKey:"name",header:"员工名称",cell:e=>{let{row:t}=e;return(0,i.P)(t.getValue("name"))}},{accessorKey:"position",header:"岗位",cell:e=>{let{row:t}=e,a=t.original.userRoles;return(0,s.jsx)(s.Fragment,{children:a.map(e=>{let{role:t}=e;return(0,s.jsx)(o.E,{variant:"secondary",className:"px-2 py-0.5 text-[11px] bg-secondary/40 font-medium",children:t.name},"".concat(t.id))})})}},{accessorKey:"phone",header:"手机号码",cell:e=>{let{row:t}=e;return(0,i.P)(t.getValue("phone"))}},{accessorKey:"account",header:"登录账号",cell:e=>{let{row:t}=e;return(0,i.P)(t.getValue("account"))}},{accessorKey:"status",header:"在职状态",cell:e=>{let{row:t}=e,a=t.original.userInstitutions;return console.log(a[0].status,"item[0].status"),(0,s.jsx)(o.E,{variant:a[0].status?"default":"destructive",className:"px-2.5 py-0.5 text-xs font-medium ".concat(a[0].status?"bg-green-500/10 text-green-600 hover:bg-green-500/15":"bg-red-500/10 text-red-600 hover:bg-red-500/15"),children:a[0].status?"在职":"离职"})}},{accessorKey:"actions",header:"操作",cell:e=>{let{row:t}=e,a=t.original;return(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)(d,{staff:a}),(0,s.jsx)(c,{staff:a}),(0,s.jsx)(u,{staff:a})]})}}];var x=a(30285),h=a(12318),p=a(48436),f=a(48432),g=a(62523),v=a(13515),b=a(45964),j=a.n(b);let N=(0,l.default)(()=>Promise.all([a.e(5488),a.e(8679)]).then(a.bind(a,88679)),{loadableGenerated:{webpack:()=>[88679]},ssr:!1}),y=function(){let[e,t]=(0,n.useState)(!1),[a,l]=(0,n.useState)(""),[i,o]=(0,n.useState)({page:1,pageSize:10}),d=(0,n.useMemo)(()=>({...i,search:a}),[i,a]),{data:c,isLoading:u}=(0,v.US)(d),[b]=(0,v.dU)(),y=async e=>{try{let a={...e,roleId:e.roles[0]};await b(a).unwrap(),t(!1),p.l.success("添加员工成功")}catch(e){p.l.error((null==e?void 0:e.message)||"添加员工失败")}},w=(0,n.useCallback)(j()(e=>{l(e),o(e=>({...e,page:1}))},300),[]),k=e=>{w(e)};return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,s.jsx)("div",{className:"relative flex-1 max-w-md",children:(0,s.jsx)("div",{className:"flex",children:(0,s.jsx)(g.p,{value:a,onChange:e=>k(e.target.value),placeholder:"搜索岗位名称",className:"pr-10 h-10"})})}),(0,s.jsx)("div",{className:"flex flex-1 justify-end",children:(0,s.jsxs)(x.$,{onClick:()=>t(!0),size:"sm",className:"h-9 px-3 font-medium",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"新增员工"]})})]}),(0,s.jsx)(r.b,{columns:m,data:(null==c?void 0:c.list)||[],pagination:!1,loading:u}),(0,s.jsx)(f.default,{pageSize:(null==c?void 0:c.pageSize)||10,totalItems:(null==c?void 0:c.total)||0,currentPage:(null==c?void 0:c.page)||1,onPageChange:e=>o(t=>({...t,page:e})),onPageSizeChange:e=>o(t=>({...t,page:1,pageSize:e}))}),e&&(0,s.jsx)(N,{open:e,onOpenChange:t,staff:null,mode:"create",onSubmit:y,loading:u})]})}}}]);