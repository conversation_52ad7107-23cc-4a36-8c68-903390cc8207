import financeModel from '../../models/financeModel.js';
import {
  parseISO,
  fromUnixTime,
  format,
  startOfDay,
  endOfDay,
  addDays,
  differenceInDays,
  startOfMonth,
  endOfMonth,
  addMonths,
  isBefore,
  isValid,
  getTime,
} from 'date-fns';

/**
 * Additional finance service methods
 */
export default {
  /**
   * Get salesrep overview data
   */
  async getSalesrepOverview({ user, teacher, startTime, endTime, page, pageSize }) {
    // 构建查询条件
    const where = {
      institutionId: user.institutionId,
      ...(teacher && { salesRepresentativeId: teacher }),
    };

    // 添加时间范围过滤
    if (startTime && endTime) {
      where.paymentTime = {
        gte: parseInt(startTime, 10),
        lte: parseInt(endTime, 10)
      };
    } else {
      let queryEndTime = new Date();
      let queryStartTime = new Date();
      queryStartTime.setDate(queryStartTime.getDate() - 7);
      where.paymentTime = {
        gte: queryStartTime.getTime(),
        lte: queryEndTime.getTime()
      };
    }

    // 查询数据库所有所需字段
    const [result, refund] = await Promise.all([
      financeModel.getSalesrepRecords(where),
      financeModel.getRefundRecords({
        institutionId: user.institutionId,
        ...(teacher && { operatorId: teacher }),
      })
    ]);

    // 安全地解析和转换数据
    const transactions = result.map((item) => ({
      ...item,
      // 安全地解析paymentTime为数字，如果解析失败则默认为0
      paymentTime: parseInt(item.paymentTime, 10) || 0,
      // 确保数值用于计算
      amountPaid: parseFloat(item.amountPaid || 0), // 已支付金额
    }));

    const refundTransactions = refund.map((item) => ({
      ...item,
      paymentTime: parseInt(item.paymentTime, 10) || 0,
      amount: parseFloat(item.amount || 0),
    }));

    // 按销售代表名称分组数据
    const newData = new Map();
    for(const item of transactions) {
      const key = item.salesRepresentative.name;
      if(!newData.has(key)) {
        newData.set(key, {
          amountPaid: 0,
          amountPaidCount: 0,
          refundAmount: 0,
          refundCount: 0,
        });
      }
      const getData = newData.get(key);
      if(item.status === 'done') {
        getData.amountPaid += item.amountPaid;
        getData.amountPaidCount += 1;
      }
    }
    
    // 处理退款
    for(const item of refundTransactions) {
      const key = item.operator.name;
      if(!newData.has(key)) {
        newData.set(key, {
          amountPaid: 0,
          amountPaidCount: 0,
          refundAmount: 0,
          refundCount: 0,
        });
      }
      const getData = newData.get(key);
      if(item.status === 'approved') {  
        getData.refundAmount += item.amount;
        getData.refundCount += 1;
      }
    }
    
    const sortedEntries = Array.from(newData.entries())
      .sort((a, b) => b[1].amountPaid - a[1].amountPaid);
    
    // 格式化数据以响应
    const dataLabels = [];
    const amountPaid = [];
    const refundAmount = [];
    const amountPaidCount = [];
    const refundCount = [];

    for(const [name, data] of sortedEntries) {
      dataLabels.push(name);
      amountPaid.push(Number(data.amountPaid.toFixed(2)));
      refundAmount.push(Number(data.refundAmount.toFixed(2)));
      amountPaidCount.push(data.amountPaidCount);
      refundCount.push(data.refundCount);
    }

    return {
      datasets: {
        dataLabels,
        amountPaid,
        refundAmount,
        amountPaidCount,
        refundCount
      }
    };
  },

  /**
   * Get student overview data
   */
  async getStudentOverview(user) {
    const result = await financeModel.getStudentOverview(user.institutionId);
    
    const ageMap = new Map();
    const genderMap = new Map();
    const productMap = new Map();
    const courseMap = new Map();
    
    result.forEach(item => {
      const birthday = item.birthday ? new Date(Number(item.birthday)) : null;
      const age = birthday ? Number(new Date().getFullYear() - birthday.getFullYear()) : 0;
      ageMap.set(age, (ageMap.get(age) || 0) + 1);
      
      if (item.gender) {
        const gender = item.gender === 'male' ? '男' : item.gender === 'female' ? '女' : '保密';
        genderMap.set(gender, (genderMap.get(gender) || 0) + 1);
      }
      
      item.StudentProduct.forEach(item => {
        productMap.set(item.product.name, (productMap.get(item.product.name) || 0) + 1);
      });
      
      item.StudentProduct.forEach(item => {
        item.StudentWeeklySchedule.forEach(item => {
          if (item.classesSchedule && item.classesSchedule.courses) {
            courseMap.set(item.classesSchedule.courses.name, (courseMap.get(item.classesSchedule.courses.name) || 0) + 1);
          }
        });
      });
    });
    
    const ageArray = Array.from(ageMap.entries());
    const genderArray = Array.from(genderMap.entries());
    const productArray = Array.from(productMap.entries());
    const courseArray = Array.from(courseMap.entries());
    
    ageArray.sort((a, b) => a[0] - b[0]);
    genderArray.sort((a, b) => a[0] - b[0]);
    productArray.sort((a, b) => a[0] - b[0]);
    courseArray.sort((a, b) => a[0] - b[0]);

    const ageArrays = ageArray.map(item => {
      return {
        age: item[0],
        count: item[1]
      };
    });
    
    const genderArrays = genderArray.map(item => {
      return {
        gender: item[0],
        count: item[1]
      };
    });
    
    const productArrays = productArray.map(item => {
      return {
        product: item[0],
        count: item[1]
      };
    });
    
    const courseArrays = courseArray.map(item => {
      return {
        course: item[0],
        count: item[1]
      };
    });

    return {
      age: ageArrays,
      gender: genderArrays,
      product: productArrays,
      course: courseArrays
    };
  },

  /**
   * Get education overview data
   */
  async getEducationOverview({ user, startTime, endTime, checkType }) {
    // =================== 基础数据统计 ===================
    // 获取学生人数和课程人数
    const [studentCount, courseCount] = await Promise.all([
      financeModel.getStudentCount(user.institutionId),
      financeModel.getCourseCount(user.institutionId)
    ]);

    // 获取今日上课人数、考勤人数
    const today = startOfDay(new Date());
    const todayStart = getTime(today);
    const todayEnd = getTime(endOfDay(today));

    // 获取今日统计数据
    const [todayCount, todayAttendanceCount] = await Promise.all([
      financeModel.countSchedules(user.institutionId, todayStart, todayEnd),
      financeModel.countSchedules(user.institutionId, todayStart, todayEnd, 'attendance')
    ]);

    // =================== 日期解析与处理 ===================
    // 解析时间戳为日期对象
    const parseTimestamp = (timestamp) => {
      if (!timestamp) return new Date();

      const numTimestamp = Number(timestamp);
      if (isNaN(numTimestamp)) return new Date();

      // Handle milliseconds (13 digits) or seconds (10 digits)
      const date = String(numTimestamp).length <= 10
        ? fromUnixTime(numTimestamp)
        : new Date(numTimestamp);

      return isValid(date) ? date : new Date();
    };

    // 解析输入的起止日期
    let startDate = parseTimestamp(startTime);
    let endDate = parseTimestamp(endTime);

    // 确保日期有效性
    if (isBefore(endDate, startDate)) {
      [startDate, endDate] = [endDate, startDate];
    }

    // =================== 时间范围数据生成 ===================
    let timeRangeData = [];

    // 日视图数据生成
    if (checkType === 'daily') {
      timeRangeData = await this._generateDailyData(user.institutionId, startDate, endDate);
    } else if (checkType === 'monthly') {
      timeRangeData = await this._generateMonthlyData(user.institutionId, startDate, endDate);
    }

    // =================== 课程数据统计 ===================
    // 获取日期范围内课程人数
    const classesScheduleResult = await financeModel.getClassesScheduleData(
      user.institutionId,
      getTime(startDate),
      getTime(endDate)
    );

    // 汇总课程数据
    const courseMap = new Map();
    classesScheduleResult.forEach(item => {
      if (item.courses) {
        const dateKey = item.courses.name;
        if (!courseMap.has(dateKey)) {
          courseMap.set(dateKey, {
            course: item.courses.name,
            count: 0
          });
        }
        courseMap.set(dateKey, {
          course: item.courses.name,
          count: Number(courseMap.get(dateKey).count || 0) + Number(item.StudentWeeklySchedule.length)
        });
      }
    });
    
    const courseArray = Array.from(courseMap.values());

    return {
      count: {
        student: studentCount,
        course: courseCount,
        today: todayCount,
        todayAttendance: todayAttendanceCount,
      },
      timeRange: timeRangeData,
      course: courseArray
    };
  },
};
