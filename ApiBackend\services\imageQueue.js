import { Queue, Worker } from "bullmq";
import { redisTask, redisConfig } from "../config/redis.js";
import { generateImage, generateImageVariation } from "./aitools.js";

// 队列配置
const queueOptions = {
  connection: redisConfig,
  defaultJobOptions: {
    attempts: 5, // 增加重试次数
    backoff: {
      type: 'exponential',
      delay: 1000
    }, // 指数退避策略
    removeOnComplete: {
      age: 3600, // 完成的任务保留1小时后自动清理
      count: 1000 // 最多保留1000个完成的任务
    },
    removeOnFail: {
      age: 3600 * 24 * 7 // 失败任务保留7天
    },
    lockDuration: 300000, // 锁持续时间5分钟
    timeout: 600000, // 任务超时时间10分钟
  },
  // 限流配置
  limiter: {
    max: 10, // 增加并发数以提高吞吐量
    duration: 1000, // 每秒处理的任务数
    groupKey: 'image-generation' // 按任务类型分组限流
  },
  // 优化内存使用
  streams: {
    events: {
      maxLen: 10000 // 限制事件流长度
    }
  }
};

const imageQueue = new Queue('image-generation', queueOptions);

// 添加错误处理
imageQueue.on('error', (error) => {
  console.error('队列错误:', error);
});

imageQueue.on('failed', (job, error) => {
  console.error(`任务 ${job.id} 失败:`, error);
});

// Worker配置
const workerOptions = {
  connection: redisConfig,
  concurrency: 8, // 增加并发数以提高吞吐量
  lockDuration: 300000, // 锁持续时间5分钟
  stalledInterval: 30000, // 检查卡住任务的间隔
  maxStalledCount: 2, // 增加最大卡住次数
  drainDelay: 5, // 队列排空检查间隔（秒）
  autorun: true, // 自动运行
  metrics: {
    maxDataPoints: 100 // 限制指标数据点数量
  }
};

// 创建Worker处理任务
const worker = new Worker(
  'image-generation',
  async (job) => {
    const { taskId, type = 'image-generation' } = job.data;
    console.log(`开始处理图像任务 ${job.id}, taskId: ${taskId}, type: ${type}`);

    const startTime = Date.now();

    try {
      // 更新任务状态为处理中，使用管道批处理Redis命令
      await redisTask.multi()
        .set(
          `task:${taskId}`,
          JSON.stringify({ status: 'processing', taskId, progress: 0, type }),
          'EX',
          3600
        )
        .set(
          `task:${taskId}:start`,
          startTime.toString(),
          'EX',
          3600
        )
        .exec();

      // 定期更新进度，保持锁活跃，使用更短的间隔
      const progressInterval = setInterval(async () => {
        try {
          // 计算已经过去的时间百分比（假设平均任务时间为2分钟）
          const elapsed = Date.now() - startTime;
          const progressPercent = Math.min(Math.floor((elapsed / 120000) * 100), 95);

          await job.updateProgress(progressPercent);
        } catch (e) {
          console.error('更新进度失败:', e);
        }
      }, 5000); // 更频繁地更新进度

      // 根据任务类型执行不同的处理
      let result;
      if (type === 'image-generation') {
        const { prompt, count, size, style } = job.data;
        // 执行图片生成，添加超时控制
        result = await Promise.race([
          generateImage(prompt, count, size, style),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('图片生成超时')), 540000) // 9分钟超时
          )
        ]);
      } else if (type === 'image-variation') {
        const { imageUrl, count, variationStrength } = job.data;
        // 执行图片变体生成，添加超时控制
        result = await Promise.race([
          generateImageVariation(imageUrl, count, variationStrength),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('图片变体生成超时')), 540000) // 9分钟超时
          )
        ]);
      } else {
        throw new Error(`未知的图像处理类型: ${type}`);
      }

      // 清除进度更新定时器
      clearInterval(progressInterval);

      // 计算处理时间
      const processingTime = Date.now() - startTime;

      // 更新任务状态为完成，使用管道批处理Redis命令
      await redisTask.multi()
        .set(
          `task:${taskId}`,
          JSON.stringify({
            status: 'completed',
            taskId,
            result,
            processingTime,
            type
          }),
          'EX',
          3600
        )
        .set(
          `task:${taskId}:end`,
          Date.now().toString(),
          'EX',
          3600
        )
        .exec();

      console.log(`图像任务 ${job.id} 完成，处理时间: ${processingTime}ms`);
      return result;
    } catch (error) {
      console.error(`图像任务 ${job.id} 失败:`, error);

      // 更新任务状态为失败
      await redisTask.set(
        `task:${taskId}`,
        JSON.stringify({
          status: 'failed',
          taskId,
          error: error.message,
          stack: error.stack,
          processingTime: Date.now() - startTime,
          type
        }),
        'EX',
        3600
      );

      // 重新抛出错误，让BullMQ处理重试
      throw error;
    }
  },
  workerOptions
);

// 添加Worker错误处理
worker.on('error', (error) => {
  console.error('Worker错误:', error);
});

worker.on('failed', (job, error) => {
  console.error(`Worker处理任务 ${job?.id} 失败:`, error);
});

worker.on('completed', (job) => {
  console.log(`Worker完成任务 ${job.id}`);
});

export default imageQueue;