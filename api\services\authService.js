import bcrypt from 'bcryptjs';
import { AUTH_ERROR, NOT_FOUND_ERROR } from '../errors/index.js';

/**
 * 认证服务
 */
export const authService = {
    /**
     * 用户登录
     * @param {Object} server - Fastify服务器实例
     * @param {string} account - 用户账号
     * @param {string} password - 用户密码
     * @returns {Promise<Object>} 登录结果，包含用户信息和令牌
     */
    async login(server, account, password) {
        // 获取数据库连接池
        const client = await server.pg.connect();
        
        try {
            // 查询用户信息
            const result = await client.query(`
                SELECT 
                    u.id, u.account, u.password, u.name,
                    i.name AS institution_name, i.logo as institution_logo, i."subjectName" as "institution_subjectName",
                    (SELECT COUNT(*) FROM user_notifications un WHERE un."userId" = u.id AND un.status = 'unread') as notification_count
                FROM users u
                LEFT JOIN user_institution ui ON ui."userId" = u.id
                LEFT JOIN institutions i ON i.id = ui."institutionId"
                WHERE u.account = $1
            `, [account]);

            // 用户不存在
            if (result.rows.length === 0) {
                throw new NOT_FOUND_ERROR('账号不存在');
            }

            // 获取用户数据
            const user = result.rows[0];

            // 验证密码
            const validPassword = await bcrypt.compare(password, user.password);
            if (!validPassword) {
                throw new AUTH_ERROR('密码错误');
            }

            // 生成令牌
            const [token, refreshToken] = await Promise.all([
                server.generateToken({
                    id: user.id,
                    account: user.account,
                    name: user.name,
                    hasInstitution: user.institution_name ? true : false,
                }),
                server.generateRefreshToken({
                    id: user.id,
                    account: user.account
                })
            ]);

            // 删除敏感信息
            delete user.password;

            // 返回结果
            return {
                user,
                accessToken: token,
                refreshToken
            };
        } finally {
            // 释放数据库连接
            client.release();
        }
    }
}; 