(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1548],{40438:(s,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>d});var r=a(95155),l=a(68856),c=a(66695);function d(){return(0,r.jsx)("div",{children:(0,r.jsxs)(c.Zp,{className:"border border-slate-200 shadow-sm overflow-hidden rounded-md",children:[(0,r.jsx)(c.aR,{className:"bg-slate-50 px-6 py-4 border-b border-slate-200",children:(0,r.jsx)(l.E,{className:"h-6 w-32"})}),(0,r.jsxs)(c.<PERSON>,{className:"p-6 space-y-8",children:[(0,r.jsxs)("section",{className:"space-y-4",children:[(0,r.jsx)(l.<PERSON>,{className:"h-5 w-40 mb-3"}),(0,r.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-2",children:[(0,r.jsx)(l.E,{className:"h-10"}),(0,r.jsx)(l.E,{className:"h-10"}),(0,r.jsx)(l.E,{className:"h-10"}),(0,r.jsx)(l.E,{className:"h-10"})]})]}),(0,r.jsxs)("section",{className:"space-y-4",children:[(0,r.jsx)(l.E,{className:"h-5 w-40 mb-3"}),(0,r.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-2",children:[(0,r.jsx)(l.E,{className:"h-10"}),(0,r.jsx)(l.E,{className:"h-10"})]}),(0,r.jsx)(l.E,{className:"h-24"})]}),(0,r.jsxs)("section",{className:"space-y-4",children:[(0,r.jsx)(l.E,{className:"h-5 w-40 mb-3"}),(0,r.jsx)(l.E,{className:"h-10"})]}),(0,r.jsxs)("section",{className:"space-y-4",children:[(0,r.jsx)(l.E,{className:"h-5 w-40 mb-3"}),(0,r.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-2",children:[(0,r.jsx)(l.E,{className:"h-10"}),(0,r.jsx)(l.E,{className:"h-10"}),(0,r.jsx)(l.E,{className:"h-10"})]})]}),(0,r.jsx)(l.E,{className:"h-10 w-full mt-4"})]})]})})}},59434:(s,e,a)=>{"use strict";a.d(e,{cn:()=>c});var r=a(52596),l=a(39688);function c(){for(var s=arguments.length,e=Array(s),a=0;a<s;a++)e[a]=arguments[a];return(0,l.QP)((0,r.$)(e))}},66695:(s,e,a)=>{"use strict";a.d(e,{BT:()=>m,Wu:()=>n,ZB:()=>i,Zp:()=>d,aR:()=>t,wL:()=>o});var r=a(95155),l=a(12115),c=a(59434);let d=l.forwardRef((s,e)=>{let{className:a,...l}=s;return(0,r.jsx)("div",{ref:e,className:(0,c.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...l})});d.displayName="Card";let t=l.forwardRef((s,e)=>{let{className:a,...l}=s;return(0,r.jsx)("div",{ref:e,className:(0,c.cn)("flex flex-col space-y-1.5 p-6",a),...l})});t.displayName="CardHeader";let i=l.forwardRef((s,e)=>{let{className:a,...l}=s;return(0,r.jsx)("div",{ref:e,className:(0,c.cn)("text-2xl font-semibold leading-none tracking-tight",a),...l})});i.displayName="CardTitle";let m=l.forwardRef((s,e)=>{let{className:a,...l}=s;return(0,r.jsx)("div",{ref:e,className:(0,c.cn)("text-sm text-muted-foreground",a),...l})});m.displayName="CardDescription";let n=l.forwardRef((s,e)=>{let{className:a,...l}=s;return(0,r.jsx)("div",{ref:e,className:(0,c.cn)("p-6 pt-0",a),...l})});n.displayName="CardContent";let o=l.forwardRef((s,e)=>{let{className:a,...l}=s;return(0,r.jsx)("div",{ref:e,className:(0,c.cn)("flex items-center p-6 pt-0",a),...l})});o.displayName="CardFooter"},68856:(s,e,a)=>{"use strict";a.d(e,{E:()=>c});var r=a(95155),l=a(59434);function c(s){let{className:e,...a}=s;return(0,r.jsx)("div",{className:(0,l.cn)("animate-pulse rounded-md bg-muted",e),...a})}},83271:(s,e,a)=>{Promise.resolve().then(a.bind(a,40438))}},s=>{var e=e=>s(s.s=e);s.O(0,[4277,6315,7358],()=>e(83271)),_N_E=s.O()}]);