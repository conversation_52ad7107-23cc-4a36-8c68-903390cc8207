(()=>{var t={};t.id=5105,t.ids=[5105],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:t=>{"use strict";t.exports=require("assert")},15158:(t,e,i)=>{Promise.resolve().then(i.bind(i,80559))},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21288:(t,e,i)=>{"use strict";let s;i.r(e),i.d(e,{default:()=>rh});var a=i(60687),r=i(43210),n=i(30596),o=i(58869),l=i(41312),h=i(82080),d=i(62688);let c=(0,d.A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);var u=i(48730);let f=(0,d.A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),g=(0,d.A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),p=(0,d.A)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);var m=i(89422),x=i(40228),b=i(44493);function _(t){return t+.5|0}let y=(t,e,i)=>Math.max(Math.min(t,i),e);function v(t){return y(_(2.55*t),0,255)}function M(t){return y(_(255*t),0,255)}function w(t){return y(_(t/2.55)/100,0,1)}function k(t){return y(_(100*t),0,100)}let P={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},C=[..."0123456789ABCDEF"],S=t=>C[15&t],D=t=>C[(240&t)>>4]+C[15&t],O=t=>(240&t)>>4==(15&t),A=t=>O(t.r)&&O(t.g)&&O(t.b)&&O(t.a),T=(t,e)=>t<255?e(t):"",L=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function E(t,e,i){let s=e*Math.min(i,1-i),a=(e,a=(e+t/30)%12)=>i-s*Math.max(Math.min(a-3,9-a,1),-1);return[a(0),a(8),a(4)]}function R(t,e,i){let s=(s,a=(s+t/60)%6)=>i-i*e*Math.max(Math.min(a,4-a,1),0);return[s(5),s(3),s(1)]}function F(t,e,i){let s;let a=E(t,1,.5);for(e+i>1&&(s=1/(e+i),e*=s,i*=s),s=0;s<3;s++)a[s]*=1-e-i,a[s]+=e;return a}function j(t){let e,i,s;let a=t.r/255,r=t.g/255,n=t.b/255,o=Math.max(a,r,n),l=Math.min(a,r,n),h=(o+l)/2;if(o!==l)s=o-l,i=h>.5?s/(2-o-l):s/(o+l),e=60*(e=a===o?(r-n)/s+6*(r<n):r===o?(n-a)/s+2:(a-r)/s+4)+.5;return[0|e,i||0,h]}function I(t,e,i,s){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,s)).map(M)}function N(t){return(t%360+360)%360}let z={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},V={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},B=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,W=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,H=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function $(t,e,i){if(t){let s=j(t);s[e]=Math.max(0,Math.min(s[e]+s[e]*i,0===e?360:1)),t.r=(s=I(E,s,void 0,void 0))[0],t.g=s[1],t.b=s[2]}}function U(t,e){return t?Object.assign(e||{},t):t}function Y(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=M(t[3]))):(e=U(t,{r:0,g:0,b:0,a:1})).a=M(e.a),e}class q{constructor(t){let e;if(t instanceof q)return t;let i=typeof t;"object"===i?e=Y(t):"string"===i&&(e=function(t){var e,i=t.length;return"#"===t[0]&&(4===i||5===i?e={r:255&17*P[t[1]],g:255&17*P[t[2]],b:255&17*P[t[3]],a:5===i?17*P[t[4]]:255}:(7===i||9===i)&&(e={r:P[t[1]]<<4|P[t[2]],g:P[t[3]]<<4|P[t[4]],b:P[t[5]]<<4|P[t[6]],a:9===i?P[t[7]]<<4|P[t[8]]:255})),e}(t)||function(t){s||((s=function(){let t,e,i,s,a;let r={},n=Object.keys(V),o=Object.keys(z);for(t=0;t<n.length;t++){for(e=0,s=a=n[t];e<o.length;e++)i=o[e],a=a.replace(i,z[i]);i=parseInt(V[s],16),r[a]=[i>>16&255,i>>8&255,255&i]}return r}()).transparent=[0,0,0,0]);let e=s[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(t)||function(t){return"r"===t.charAt(0)?function(t){let e,i,s;let a=B.exec(t),r=255;if(a){if(a[7]!==e){let t=+a[7];r=a[8]?v(t):y(255*t,0,255)}return e=+a[1],i=+a[3],s=+a[5],e=255&(a[2]?v(e):y(e,0,255)),{r:e,g:i=255&(a[4]?v(i):y(i,0,255)),b:s=255&(a[6]?v(s):y(s,0,255)),a:r}}}(t):function(t){let e;let i=L.exec(t),s=255;if(!i)return;i[5]!==e&&(s=i[6]?v(+i[5]):M(+i[5]));let a=N(+i[2]),r=+i[3]/100,n=+i[4]/100;return{r:(e="hwb"===i[1]?I(F,a,r,n):"hsv"===i[1]?I(R,a,r,n):I(E,a,r,n))[0],g:e[1],b:e[2],a:s}}(t)}(t)),this._rgb=e,this._valid=!!e}get valid(){return this._valid}get rgb(){var t=U(this._rgb);return t&&(t.a=w(t.a)),t}set rgb(t){this._rgb=Y(t)}rgbString(){var t;return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${w(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0}hexString(){var t,e;return this._valid?(e=A(t=this._rgb)?S:D,t?"#"+e(t.r)+e(t.g)+e(t.b)+T(t.a,e):void 0):void 0}hslString(){return this._valid?function(t){if(!t)return;let e=j(t),i=e[0],s=k(e[1]),a=k(e[2]);return t.a<255?`hsla(${i}, ${s}%, ${a}%, ${w(t.a)})`:`hsl(${i}, ${s}%, ${a}%)`}(this._rgb):void 0}mix(t,e){if(t){let i;let s=this.rgb,a=t.rgb,r=e===i?.5:e,n=2*r-1,o=s.a-a.a,l=((n*o==-1?n:(n+o)/(1+n*o))+1)/2;i=1-l,s.r=255&l*s.r+i*a.r+.5,s.g=255&l*s.g+i*a.g+.5,s.b=255&l*s.b+i*a.b+.5,s.a=r*s.a+(1-r)*a.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,i){let s=H(w(t.r)),a=H(w(t.g)),r=H(w(t.b));return{r:M(W(s+i*(H(w(e.r))-s))),g:M(W(a+i*(H(w(e.g))-a))),b:M(W(r+i*(H(w(e.b))-r))),a:t.a+i*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new q(this.rgb)}alpha(t){return this._rgb.a=M(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=_(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return $(this._rgb,2,t),this}darken(t){return $(this._rgb,2,-t),this}saturate(t){return $(this._rgb,1,t),this}desaturate(t){return $(this._rgb,1,-t),this}rotate(t){var e,i;return(i=j(e=this._rgb))[0]=N(i[0]+t),e.r=(i=I(E,i,void 0,void 0))[0],e.g=i[1],e.b=i[2],this}}function X(){}let Z=(()=>{let t=0;return()=>t++})();function G(t){return null==t}function K(t){if(Array.isArray&&Array.isArray(t))return!0;let e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function J(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function Q(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function tt(t,e){return Q(t)?t:e}function te(t,e){return void 0===t?e:t}let ti=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:+t/e,ts=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function ta(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function tr(t,e,i,s){let a,r,n;if(K(t)){if(r=t.length,s)for(a=r-1;a>=0;a--)e.call(i,t[a],a);else for(a=0;a<r;a++)e.call(i,t[a],a)}else if(J(t))for(a=0,r=(n=Object.keys(t)).length;a<r;a++)e.call(i,t[n[a]],n[a])}function tn(t,e){let i,s,a,r;if(!t||!e||t.length!==e.length)return!1;for(i=0,s=t.length;i<s;++i)if(a=t[i],r=e[i],a.datasetIndex!==r.datasetIndex||a.index!==r.index)return!1;return!0}function to(t){if(K(t))return t.map(to);if(J(t)){let e=Object.create(null),i=Object.keys(t),s=i.length,a=0;for(;a<s;++a)e[i[a]]=to(t[i[a]]);return e}return t}function tl(t){return -1===["__proto__","prototype","constructor"].indexOf(t)}function th(t,e,i,s){if(!tl(t))return;let a=e[t],r=i[t];J(a)&&J(r)?td(a,r,s):e[t]=to(r)}function td(t,e,i){let s;let a=K(e)?e:[e],r=a.length;if(!J(t))return t;let n=(i=i||{}).merger||th;for(let e=0;e<r;++e){if(!J(s=a[e]))continue;let r=Object.keys(s);for(let e=0,a=r.length;e<a;++e)n(r[e],t,s,i)}return t}function tc(t,e){return td(t,e,{merger:tu})}function tu(t,e,i){if(!tl(t))return;let s=e[t],a=i[t];J(s)&&J(a)?tc(s,a):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=to(a))}let tf={"":t=>t,x:t=>t.x,y:t=>t.y};function tg(t,e){return(tf[e]||(tf[e]=function(t){let e=function(t){let e=t.split("."),i=[],s="";for(let t of e)(s+=t).endsWith("\\")?s=s.slice(0,-1)+".":(i.push(s),s="");return i}(t);return t=>{for(let i of e){if(""===i)break;t=t&&t[i]}return t}}(e)))(t)}function tp(t){return t.charAt(0).toUpperCase()+t.slice(1)}let tm=t=>void 0!==t,tx=t=>"function"==typeof t,tb=(t,e)=>{if(t.size!==e.size)return!1;for(let i of t)if(!e.has(i))return!1;return!0},t_=Math.PI,ty=2*t_,tv=ty+t_,tM=Number.POSITIVE_INFINITY,tw=t_/180,tk=t_/2,tP=t_/4,tC=2*t_/3,tS=Math.log10,tD=Math.sign;function tO(t,e,i){return Math.abs(t-e)<i}function tA(t){let e=Math.round(t),i=Math.pow(10,Math.floor(tS(t=tO(t,e,t/1e3)?e:t))),s=t/i;return(s<=1?1:s<=2?2:s<=5?5:10)*i}function tT(t){return"symbol"!=typeof t&&("object"!=typeof t||null===t||!!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t))&&!isNaN(parseFloat(t))&&isFinite(t)}function tL(t,e,i){let s,a,r;for(s=0,a=t.length;s<a;s++)isNaN(r=t[s][i])||(e.min=Math.min(e.min,r),e.max=Math.max(e.max,r))}function tE(t){return t_/180*t}function tR(t){if(!Q(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function tF(t,e){let i=e.x-t.x,s=e.y-t.y,a=Math.sqrt(i*i+s*s),r=Math.atan2(s,i);return r<-.5*t_&&(r+=ty),{angle:r,distance:a}}function tj(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function tI(t,e){return(t-e+tv)%ty-t_}function tN(t){return(t%ty+ty)%ty}function tz(t,e,i,s){let a=tN(t),r=tN(e),n=tN(i),o=tN(r-a),l=tN(n-a),h=tN(a-r),d=tN(a-n);return a===r||a===n||s&&r===n||o>l&&h<d}function tV(t,e,i){return Math.max(e,Math.min(i,t))}function tB(t,e,i,s=1e-6){return t>=Math.min(e,i)-s&&t<=Math.max(e,i)+s}function tW(t,e,i){let s;i=i||(i=>t[i]<e);let a=t.length-1,r=0;for(;a-r>1;)i(s=r+a>>1)?r=s:a=s;return{lo:r,hi:a}}let tH=(t,e,i,s)=>tW(t,i,s?s=>{let a=t[s][e];return a<i||a===i&&t[s+1][e]===i}:s=>t[s][e]<i),t$=(t,e,i)=>tW(t,i,s=>t[s][e]>=i),tU=["push","pop","shift","splice","unshift"];function tY(t,e){let i=t._chartjs;if(!i)return;let s=i.listeners,a=s.indexOf(e);-1!==a&&s.splice(a,1),!(s.length>0)&&(tU.forEach(e=>{delete t[e]}),delete t._chartjs)}function tq(t){let e=new Set(t);return e.size===t.length?t:Array.from(e)}let tX="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function tZ(t,e){let i=[],s=!1;return function(...a){i=a,s||(s=!0,tX.call(window,()=>{s=!1,t.apply(e,i)}))}}let tG=t=>"start"===t?"left":"end"===t?"right":"center",tK=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2,tJ=(t,e,i,s)=>t===(s?"left":"right")?i:"center"===t?(e+i)/2:e;function tQ(t,e,i){let s=e.length,a=0,r=s;if(t._sorted){let{iScale:n,vScale:o,_parsed:l}=t,h=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,d=n.axis,{min:c,max:u,minDefined:f,maxDefined:g}=n.getUserBounds();if(f){if(a=Math.min(tH(l,d,c).lo,i?s:tH(e,d,n.getPixelForValue(c)).lo),h){let t=l.slice(0,a+1).reverse().findIndex(t=>!G(t[o.axis]));a-=Math.max(0,t)}a=tV(a,0,s-1)}if(g){let t=Math.max(tH(l,n.axis,u,!0).hi+1,i?0:tH(e,d,n.getPixelForValue(u),!0).hi+1);if(h){let e=l.slice(t-1).findIndex(t=>!G(t[o.axis]));t+=Math.max(0,e)}r=tV(t,a,s)-a}else r=s-a}return{start:a,count:r}}function t0(t){let{xScale:e,yScale:i,_scaleRanges:s}=t,a={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!s)return t._scaleRanges=a,!0;let r=s.xmin!==e.min||s.xmax!==e.max||s.ymin!==i.min||s.ymax!==i.max;return Object.assign(s,a),r}let t1=t=>0===t||1===t,t2=(t,e,i)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*ty/i)),t5=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*ty/i)+1,t4={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*tk)+1,easeOutSine:t=>Math.sin(t*tk),easeInOutSine:t=>-.5*(Math.cos(t_*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>t1(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(-Math.pow(2,-10*(2*t-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>t1(t)?t:t2(t,.075,.3),easeOutElastic:t=>t1(t)?t:t5(t,.075,.3),easeInOutElastic:t=>t1(t)?t:t<.5?.5*t2(2*t,.1125,.45):.5+.5*t5(2*t-1,.1125,.45),easeInBack:t=>t*t*(2.70158*t-1.70158),easeOutBack:t=>(t-=1)*t*(2.70158*t********)+1,easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-t4.easeOutBounce(1-t),easeOutBounce:t=>t<.36363636363636365?7.5625*t*t:t<.7272727272727273?7.5625*(t-=.5454545454545454)*t+.75:t<.9090909090909091?7.5625*(t-=.8181818181818182)*t+.9375:7.5625*(t-=.9545454545454546)*t+.984375,easeInOutBounce:t=>t<.5?.5*t4.easeInBounce(2*t):.5*t4.easeOutBounce(2*t-1)+.5};function t8(t){if(t&&"object"==typeof t){let e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function t3(t){return t8(t)?t:new q(t)}function t6(t){return t8(t)?t:new q(t).saturate(.5).darken(.1).hexString()}let t9=["x","y","borderWidth","radius","tension"],t7=["color","borderColor","backgroundColor"],et=new Map;function ee(t,e,i){return(function(t,e){let i=t+JSON.stringify(e=e||{}),s=et.get(i);return s||(s=new Intl.NumberFormat(t,e),et.set(i,s)),s})(e,i).format(t)}let ei={values:t=>K(t)?t:""+t,numeric(t,e,i){let s;if(0===t)return"0";let a=this.chart.options.locale,r=t;if(i.length>1){var n,o;let e,a=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(a<1e-4||a>1e15)&&(s="scientific"),n=t,Math.abs(e=(o=i).length>3?o[2].value-o[1].value:o[1].value-o[0].value)>=1&&n!==Math.floor(n)&&(e=n-Math.floor(n)),r=e}let l=tS(Math.abs(r)),h=isNaN(l)?1:Math.max(Math.min(-1*Math.floor(l),20),0),d={notation:s,minimumFractionDigits:h,maximumFractionDigits:h};return Object.assign(d,this.options.ticks.format),ee(t,a,d)},logarithmic(t,e,i){return 0===t?"0":[1,2,3,5,10,15].includes(i[e].significand||t/Math.pow(10,Math.floor(tS(t))))||e>.8*i.length?ei.numeric.call(this,t,e,i):""}};var es={formatters:ei};let ea=Object.create(null),er=Object.create(null);function en(t,e){if(!e)return t;let i=e.split(".");for(let e=0,s=i.length;e<s;++e){let s=i[e];t=t[s]||(t[s]=Object.create(null))}return t}function eo(t,e,i){return"string"==typeof e?td(en(t,e),i):td(en(t,""),e)}class el{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>t6(e.backgroundColor),this.hoverBorderColor=(t,e)=>t6(e.borderColor),this.hoverColor=(t,e)=>t6(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return eo(this,t,e)}get(t){return en(this,t)}describe(t,e){return eo(er,t,e)}override(t,e){return eo(ea,t,e)}route(t,e,i,s){let a=en(this,t),r=en(this,i),n="_"+e;Object.defineProperties(a,{[n]:{value:a[e],writable:!0},[e]:{enumerable:!0,get(){let t=this[n],e=r[s];return J(t)?Object.assign({},e,t):te(t,e)},set(t){this[n]=t}}})}apply(t){t.forEach(t=>t(this))}}var eh=new el({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:t7},numbers:{type:"number",properties:t9}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:es.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function ed(t,e,i,s,a){let r=e[a];return r||(r=e[a]=t.measureText(a).width,i.push(a)),r>s&&(s=r),s}function ec(t,e,i){let s=t.currentDevicePixelRatio,a=0!==i?Math.max(i/2,.5):0;return Math.round((e-a)*s)/s+a}function eu(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function ef(t,e,i,s){eg(t,e,i,s,null)}function eg(t,e,i,s,a){let r,n,o,l,h,d,c,u;let f=e.pointStyle,g=e.rotation,p=e.radius,m=(g||0)*tw;if(f&&"object"==typeof f&&("[object HTMLImageElement]"===(r=f.toString())||"[object HTMLCanvasElement]"===r)){t.save(),t.translate(i,s),t.rotate(m),t.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),t.restore();return}if(!isNaN(p)&&!(p<=0)){switch(t.beginPath(),f){default:a?t.ellipse(i,s,a/2,p,0,0,ty):t.arc(i,s,p,0,ty),t.closePath();break;case"triangle":d=a?a/2:p,t.moveTo(i+Math.sin(m)*d,s-Math.cos(m)*p),m+=tC,t.lineTo(i+Math.sin(m)*d,s-Math.cos(m)*p),m+=tC,t.lineTo(i+Math.sin(m)*d,s-Math.cos(m)*p),t.closePath();break;case"rectRounded":h=.516*p,n=Math.cos(m+tP)*(l=p-h),c=Math.cos(m+tP)*(a?a/2-h:l),o=Math.sin(m+tP)*l,u=Math.sin(m+tP)*(a?a/2-h:l),t.arc(i-c,s-o,h,m-t_,m-tk),t.arc(i+u,s-n,h,m-tk,m),t.arc(i+c,s+o,h,m,m+tk),t.arc(i-u,s+n,h,m+tk,m+t_),t.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,d=a?a/2:l,t.rect(i-d,s-l,2*d,2*l);break}m+=tP;case"rectRot":c=Math.cos(m)*(a?a/2:p),n=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+u,s-n),t.lineTo(i+c,s+o),t.lineTo(i-u,s+n),t.closePath();break;case"crossRot":m+=tP;case"cross":c=Math.cos(m)*(a?a/2:p),n=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-n),t.lineTo(i-u,s+n);break;case"star":c=Math.cos(m)*(a?a/2:p),n=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-n),t.lineTo(i-u,s+n),m+=tP,c=Math.cos(m)*(a?a/2:p),n=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-n),t.lineTo(i-u,s+n);break;case"line":n=a?a/2:Math.cos(m)*p,o=Math.sin(m)*p,t.moveTo(i-n,s-o),t.lineTo(i+n,s+o);break;case"dash":t.moveTo(i,s),t.lineTo(i+Math.cos(m)*(a?a/2:p),s+Math.sin(m)*p);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function ep(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function em(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function ex(t){t.restore()}function eb(t,e,i,s,a){if(!e)return t.lineTo(i.x,i.y);if("middle"===a){let s=(e.x+i.x)/2;t.lineTo(s,e.y),t.lineTo(s,i.y)}else"after"===a!=!!s?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function e_(t,e,i,s){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(s?e.cp1x:e.cp2x,s?e.cp1y:e.cp2y,s?i.cp2x:i.cp1x,s?i.cp2y:i.cp1y,i.x,i.y)}function ey(t,e,i,s,a,r={}){let n,o;let l=K(e)?e:[e],h=r.strokeWidth>0&&""!==r.strokeColor;for(t.save(),t.font=a.string,r.translation&&t.translate(r.translation[0],r.translation[1]),G(r.rotation)||t.rotate(r.rotation),r.color&&(t.fillStyle=r.color),r.textAlign&&(t.textAlign=r.textAlign),r.textBaseline&&(t.textBaseline=r.textBaseline),n=0;n<l.length;++n)o=l[n],r.backdrop&&function(t,e){let i=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=i}(t,r.backdrop),h&&(r.strokeColor&&(t.strokeStyle=r.strokeColor),G(r.strokeWidth)||(t.lineWidth=r.strokeWidth),t.strokeText(o,i,s,r.maxWidth)),t.fillText(o,i,s,r.maxWidth),function(t,e,i,s,a){if(a.strikethrough||a.underline){let r=t.measureText(s),n=e-r.actualBoundingBoxLeft,o=e+r.actualBoundingBoxRight,l=i-r.actualBoundingBoxAscent,h=i+r.actualBoundingBoxDescent,d=a.strikethrough?(l+h)/2:h;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=a.decorationWidth||2,t.moveTo(n,d),t.lineTo(o,d),t.stroke()}}(t,i,s,o,r),s+=Number(a.lineHeight);t.restore()}function ev(t,e){let{x:i,y:s,w:a,h:r,radius:n}=e;t.arc(i+n.topLeft,s+n.topLeft,n.topLeft,1.5*t_,t_,!0),t.lineTo(i,s+r-n.bottomLeft),t.arc(i+n.bottomLeft,s+r-n.bottomLeft,n.bottomLeft,t_,tk,!0),t.lineTo(i+a-n.bottomRight,s+r),t.arc(i+a-n.bottomRight,s+r-n.bottomRight,n.bottomRight,tk,0,!0),t.lineTo(i+a,s+n.topRight),t.arc(i+a-n.topRight,s+n.topRight,n.topRight,0,-tk,!0),t.lineTo(i+n.topLeft,s)}let eM=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,ew=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,ek=t=>+t||0;function eP(t,e){let i={},s=J(e),a=s?Object.keys(e):e,r=J(t)?s?i=>te(t[i],t[e[i]]):e=>t[e]:()=>t;for(let t of a)i[t]=ek(r(t));return i}function eC(t){return eP(t,{top:"y",right:"x",bottom:"y",left:"x"})}function eS(t){return eP(t,["topLeft","topRight","bottomLeft","bottomRight"])}function eD(t){let e=eC(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function eO(t,e){t=t||{},e=e||eh.font;let i=te(t.size,e.size);"string"==typeof i&&(i=parseInt(i,10));let s=te(t.style,e.style);s&&!(""+s).match(ew)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);let a={family:te(t.family,e.family),lineHeight:function(t,e){let i=(""+t).match(eM);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}(te(t.lineHeight,e.lineHeight),i),size:i,style:s,weight:te(t.weight,e.weight),string:""};return a.string=!a||G(a.size)||G(a.family)?null:(a.style?a.style+" ":"")+(a.weight?a.weight+" ":"")+a.size+"px "+a.family,a}function eA(t,e,i,s){let a,r,n,o=!0;for(a=0,r=t.length;a<r;++a)if(void 0!==(n=t[a])&&(void 0!==e&&"function"==typeof n&&(n=n(e),o=!1),void 0!==i&&K(n)&&(n=n[i%n.length],o=!1),void 0!==n))return s&&!o&&(s.cacheable=!1),n}function eT(t,e){return Object.assign(Object.create(t),e)}function eL(t,e=[""],i,s,a=()=>t[0]){let r=i||t;return void 0===s&&(s=eB("_fallback",t)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:r,_fallback:s,_getTarget:a,override:i=>eL([i,...t],e,r,s)},{deleteProperty:(e,i)=>(delete e[i],delete e._keys,delete t[0][i],!0),get:(i,s)=>eI(i,s,()=>(function(t,e,i,s){let a;for(let r of e)if(void 0!==(a=eB(eF(r,t),i)))return ej(t,a)?ez(i,s,t,a):a})(s,e,t,i)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>eW(t).includes(e),ownKeys:t=>eW(t),set(t,e,i){let s=t._storage||(t._storage=a());return t[e]=s[e]=i,delete t._keys,!0}})}function eE(t,e,i,s){return new Proxy({_cacheable:!1,_proxy:t,_context:e,_subProxy:i,_stack:new Set,_descriptors:eR(t,s),setContext:e=>eE(t,e,i,s),override:a=>eE(t.override(a),e,i,s)},{deleteProperty:(e,i)=>(delete e[i],delete t[i],!0),get:(t,e,i)=>eI(t,e,()=>(function(t,e,i){let{_proxy:s,_context:a,_subProxy:r,_descriptors:n}=t,o=s[e];return tx(o)&&n.isScriptable(e)&&(o=function(t,e,i,s){let{_proxy:a,_context:r,_subProxy:n,_stack:o}=i;if(o.has(t))throw Error("Recursion detected: "+Array.from(o).join("->")+"->"+t);o.add(t);let l=e(r,n||s);return o.delete(t),ej(t,l)&&(l=ez(a._scopes,a,t,l)),l}(e,o,t,i)),K(o)&&o.length&&(o=function(t,e,i,s){let{_proxy:a,_context:r,_subProxy:n,_descriptors:o}=i;if(void 0!==r.index&&s(t))return e[r.index%e.length];if(J(e[0])){let i=e,s=a._scopes.filter(t=>t!==i);for(let l of(e=[],i)){let i=ez(s,a,t,l);e.push(eE(i,r,n&&n[t],o))}}return e}(e,o,t,n.isIndexable)),ej(e,o)&&(o=eE(o,a,r&&r[e],n)),o})(t,e,i)),getOwnPropertyDescriptor:(e,i)=>e._descriptors.allKeys?Reflect.has(t,i)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,i),getPrototypeOf:()=>Reflect.getPrototypeOf(t),has:(e,i)=>Reflect.has(t,i),ownKeys:()=>Reflect.ownKeys(t),set:(e,i,s)=>(t[i]=s,delete e[i],!0)})}function eR(t,e={scriptable:!0,indexable:!0}){let{_scriptable:i=e.scriptable,_indexable:s=e.indexable,_allKeys:a=e.allKeys}=t;return{allKeys:a,scriptable:i,indexable:s,isScriptable:tx(i)?i:()=>i,isIndexable:tx(s)?s:()=>s}}let eF=(t,e)=>t?t+tp(e):e,ej=(t,e)=>J(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function eI(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];let s=i();return t[e]=s,s}let eN=(t,e)=>!0===t?e:"string"==typeof t?tg(e,t):void 0;function ez(t,e,i,s){var a;let r=e._rootScopes,n=tx(a=e._fallback)?a(i,s):a,o=[...t,...r],l=new Set;l.add(s);let h=eV(l,o,i,n||i,s);return null!==h&&(void 0===n||n===i||null!==(h=eV(l,o,n,h,s)))&&eL(Array.from(l),[""],r,n,()=>(function(t,e,i){let s=t._getTarget();e in s||(s[e]={});let a=s[e];return K(a)&&J(i)?i:a||{}})(e,i,s))}function eV(t,e,i,s,a){for(;i;)i=function(t,e,i,s,a){for(let n of e){let e=eN(i,n);if(e){var r;t.add(e);let n=tx(r=e._fallback)?r(i,a):r;if(void 0!==n&&n!==i&&n!==s)return n}else if(!1===e&&void 0!==s&&i!==s)return null}return!1}(t,e,i,s,a);return i}function eB(t,e){for(let i of e){if(!i)continue;let e=i[t];if(void 0!==e)return e}}function eW(t){let e=t._keys;return e||(e=t._keys=function(t){let e=new Set;for(let i of t)for(let t of Object.keys(i).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function eH(t,e,i,s){let a,r,n;let{iScale:o}=t,{key:l="r"}=this._parsing,h=Array(s);for(a=0;a<s;++a)n=e[r=a+i],h[a]={r:o.parse(tg(n,l),r)};return h}let e$=Number.EPSILON||1e-14,eU=(t,e)=>e<t.length&&!t[e].skip&&t[e],eY=t=>"x"===t?"y":"x";function eq(t,e,i){return Math.max(Math.min(t,i),e)}function eX(){return"undefined"!=typeof window&&"undefined"!=typeof document}function eZ(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function eG(t,e,i){let s;return"string"==typeof t?(s=parseInt(t,10),-1!==t.indexOf("%")&&(s=s/100*e.parentNode[i])):s=t,s}let eK=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),eJ=["top","right","bottom","left"];function eQ(t,e,i){let s={};i=i?"-"+i:"";for(let a=0;a<4;a++){let r=eJ[a];s[r]=parseFloat(t[e+"-"+r+i])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}let e0=(t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot);function e1(t,e){if("native"in t)return t;let{canvas:i,currentDevicePixelRatio:s}=e,a=eK(i),r="border-box"===a.boxSizing,n=eQ(a,"padding"),o=eQ(a,"border","width"),{x:l,y:h,box:d}=function(t,e){let i,s;let a=t.touches,r=a&&a.length?a[0]:t,{offsetX:n,offsetY:o}=r,l=!1;if(e0(n,o,t.target))i=n,s=o;else{let t=e.getBoundingClientRect();i=r.clientX-t.left,s=r.clientY-t.top,l=!0}return{x:i,y:s,box:l}}(t,i),c=n.left+(d&&o.left),u=n.top+(d&&o.top),{width:f,height:g}=e;return r&&(f-=n.width+o.width,g-=n.height+o.height),{x:Math.round((l-c)/f*i.width/s),y:Math.round((h-u)/g*i.height/s)}}let e2=t=>Math.round(10*t)/10;function e5(t,e,i){let s=e||1,a=Math.floor(t.height*s),r=Math.floor(t.width*s);t.height=Math.floor(t.height),t.width=Math.floor(t.width);let n=t.canvas;return n.style&&(i||!n.style.height&&!n.style.width)&&(n.style.height=`${t.height}px`,n.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==s||n.height!==a||n.width!==r)&&(t.currentDevicePixelRatio=s,n.height=a,n.width=r,t.ctx.setTransform(s,0,0,s,0,0),!0)}let e4=function(){let t=!1;try{let e={get passive(){return t=!0,!1}};eX()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function e8(t,e){let i=eK(t).getPropertyValue(e),s=i&&i.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function e3(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function e6(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:"middle"===s?i<.5?t.y:e.y:"after"===s?i<1?t.y:e.y:i>0?e.y:t.y}}function e9(t,e,i,s){let a={x:t.cp2x,y:t.cp2y},r={x:e.cp1x,y:e.cp1y},n=e3(t,a,i),o=e3(a,r,i),l=e3(r,e,i),h=e3(n,o,i),d=e3(o,l,i);return e3(h,d,i)}function e7(t,e,i){var s;return t?(s=i,{x:t=>e+e+s-t,setWidth(t){s=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function it(t,e){let i,s;("ltr"===e||"rtl"===e)&&(s=[(i=t.canvas.style).getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=s)}function ie(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function ii(t){return"angle"===t?{between:tz,compare:tI,normalize:tN}:{between:tB,compare:(t,e)=>t-e,normalize:t=>t}}function is({start:t,end:e,count:i,loop:s,style:a}){return{start:t%i,end:e%i,loop:s&&(e-t+1)%i==0,style:a}}function ia(t,e,i){let s,a,r;if(!i)return[t];let{property:n,start:o,end:l}=i,h=e.length,{compare:d,between:c,normalize:u}=ii(n),{start:f,end:g,loop:p,style:m}=function(t,e,i){let s;let{property:a,start:r,end:n}=i,{between:o,normalize:l}=ii(a),h=e.length,{start:d,end:c,loop:u}=t;if(u){for(d+=h,c+=h,s=0;s<h&&o(l(e[d%h][a]),r,n);++s)d--,c--;d%=h,c%=h}return c<d&&(c+=h),{start:d,end:c,loop:u,style:t.style}}(t,e,i),x=[],b=!1,_=null,y=()=>c(o,r,s)&&0!==d(o,r),v=()=>0===d(l,s)||c(l,r,s),M=()=>b||y(),w=()=>!b||v();for(let t=f,i=f;t<=g;++t)!(a=e[t%h]).skip&&(s=u(a[n]))!==r&&(b=c(s,o,l),null===_&&M()&&(_=0===d(s,o)?t:i),null!==_&&w()&&(x.push(is({start:_,end:t,loop:p,count:h,style:m})),_=null),i=t,r=s);return null!==_&&x.push(is({start:_,end:g,loop:p,count:h,style:m})),x}function ir(t,e){let i=[],s=t.segments;for(let a=0;a<s.length;a++){let r=ia(s[a],t.points,e);r.length&&i.push(...r)}return i}function io(t,e,i,s){return s&&s.setContext&&i?function(t,e,i,s){let a=t._chart.getContext(),r=il(t.options),{_datasetIndex:n,options:{spanGaps:o}}=t,l=i.length,h=[],d=r,c=e[0].start,u=c;function f(t,e,s,a){let r=o?-1:1;if(t!==e){for(t+=l;i[t%l].skip;)t-=r;for(;i[e%l].skip;)e+=r;t%l!=e%l&&(h.push({start:t%l,end:e%l,loop:s,style:a}),d=a,c=e%l)}}for(let t of e){let e;let r=i[(c=o?c:t.start)%l];for(u=c+1;u<=t.end;u++){let o=i[u%l];(function(t,e){if(!e)return!1;let i=[],s=function(t,e){return t8(e)?(i.includes(e)||i.push(e),i.indexOf(e)):e};return JSON.stringify(t,s)!==JSON.stringify(e,s)})(e=il(s.setContext(eT(a,{type:"segment",p0:r,p1:o,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:n}))),d)&&f(c,u-1,t.loop,d),r=o,d=e}c<u-1&&f(c,u-1,t.loop,d)}return h}(t,e,i,s):e}function il(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}class ih{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){let a=e.listeners[s],r=e.duration;a.forEach(s=>s({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(i-e.start,r)}))}_refresh(){!this._request&&(this._running=!0,this._request=tX.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,s)=>{let a;if(!i.running||!i.items.length)return;let r=i.items,n=r.length-1,o=!1;for(;n>=0;--n)(a=r[n])._active?(a._total>i.duration&&(i.duration=a._total),a.tick(t),o=!0):(r[n]=r[r.length-1],r.pop());o&&(s.draw(),this._notify(s,i,t,"progress")),r.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=r.length}),this._lastDate=t,0===e&&(this._running=!1)}_getAnims(t){let e=this._charts,i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){let e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((t,e)=>Math.max(t,e._duration),0),this._refresh())}running(t){if(!this._running)return!1;let e=this._charts.get(t);return!!e&&!!e.running&&!!e.items.length}stop(t){let e=this._charts.get(t);if(!e||!e.items.length)return;let i=e.items,s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var id=new ih;let ic="transparent",iu={boolean:(t,e,i)=>i>.5?e:t,color(t,e,i){let s=t3(t||ic),a=s.valid&&t3(e||ic);return a&&a.valid?a.mix(s,i).hexString():e},number:(t,e,i)=>t+(e-t)*i};class ig{constructor(t,e,i,s){let a=e[i];s=eA([t.to,s,a,t.from]);let r=eA([t.from,a,s]);this._active=!0,this._fn=t.fn||iu[t.type||typeof r],this._easing=t4[t.easing]||t4.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=r,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);let s=this._target[this._prop],a=i-this._start,r=this._duration-a;this._start=i,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=a,this._loop=!!t.loop,this._to=eA([t.to,e,s,t.from]),this._from=eA([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){let e;let i=t-this._start,s=this._duration,a=this._prop,r=this._from,n=this._loop,o=this._to;if(this._active=r!==o&&(n||i<s),!this._active){this._target[a]=o,this._notify(!0);return}if(i<0){this._target[a]=r;return}e=i/s%2,e=n&&e>1?2-e:e,e=this._easing(Math.min(1,Math.max(0,e))),this._target[a]=this._fn(r,o,e)}wait(){let t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){let e=t?"res":"rej",i=this._promises||[];for(let t=0;t<i.length;t++)i[t][e]()}}class ip{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!J(t))return;let e=Object.keys(eh.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(s=>{let a=t[s];if(!J(a))return;let r={};for(let t of e)r[t]=a[t];(K(a.properties)&&a.properties||[s]).forEach(t=>{t!==s&&i.has(t)||i.set(t,r)})})}_animateOptions(t,e){let i=e.options,s=function(t,e){if(!e)return;let i=t.options;if(!i){t.options=e;return}return i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}})),i}(t,i);if(!s)return[];let a=this._createAnimations(s,i);return i.$shared&&(function(t,e){let i=[],s=Object.keys(e);for(let e=0;e<s.length;e++){let a=t[s[e]];a&&a.active()&&i.push(a.wait())}return Promise.all(i)})(t.options.$animations,i).then(()=>{t.options=i},()=>{}),a}_createAnimations(t,e){let i;let s=this._properties,a=[],r=t.$animations||(t.$animations={}),n=Object.keys(e),o=Date.now();for(i=n.length-1;i>=0;--i){let l=n[i];if("$"===l.charAt(0))continue;if("options"===l){a.push(...this._animateOptions(t,e));continue}let h=e[l],d=r[l],c=s.get(l);if(d){if(c&&d.active()){d.update(c,h,o);continue}d.cancel()}if(!c||!c.duration){t[l]=h;continue}r[l]=d=new ig(c,t,l,h),a.push(d)}return a}update(t,e){if(0===this._properties.size){Object.assign(t,e);return}let i=this._createAnimations(t,e);if(i.length)return id.add(this._chart,i),!0}}function im(t,e){let i=t&&t.options||{},s=i.reverse,a=void 0===i.min?e:0,r=void 0===i.max?e:0;return{start:s?r:a,end:s?a:r}}function ix(t,e){let i,s;let a=[],r=t._getSortedDatasetMetas(e);for(i=0,s=r.length;i<s;++i)a.push(r[i].index);return a}function ib(t,e,i,s={}){let a,r,n,o;let l=t.keys,h="single"===s.mode;if(null===e)return;let d=!1;for(a=0,r=l.length;a<r;++a){if((n=+l[a])===i){if(d=!0,s.all)continue;break}Q(o=t.values[n])&&(h||0===e||tD(e)===tD(o))&&(e+=o)}return d||s.all?e:0}function i_(t,e){let i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function iy(t,e,i,s){for(let a of e.getMatchingVisibleMetas(s).reverse()){let e=t[a.index];if(i&&e>0||!i&&e<0)return a.index}return null}function iv(t,e){let i;let{chart:s,_cachedMeta:a}=t,r=s._stacks||(s._stacks={}),{iScale:n,vScale:o,index:l}=a,h=n.axis,d=o.axis,c=`${n.id}.${o.id}.${a.stack||a.type}`,u=e.length;for(let t=0;t<u;++t){let s=e[t],{[h]:n,[d]:u}=s;(i=(s._stacks||(s._stacks={}))[d]=function(t,e,i){let s=t[e]||(t[e]={});return s[i]||(s[i]={})}(r,c,n))[l]=u,i._top=iy(i,o,!0,a.type),i._bottom=iy(i,o,!1,a.type),(i._visualValues||(i._visualValues={}))[l]=u}}function iM(t,e){let i=t.scales;return Object.keys(i).filter(t=>i[t].axis===e).shift()}function iw(t,e){let i=t.controller.index,s=t.vScale&&t.vScale.axis;if(s)for(let a of e=e||t._parsed){let t=a._stacks;if(!t||void 0===t[s]||void 0===t[s][i])return;delete t[s][i],void 0!==t[s]._visualValues&&void 0!==t[s]._visualValues[i]&&delete t[s]._visualValues[i]}}let ik=t=>"reset"===t||"none"===t,iP=(t,e)=>e?t:Object.assign({},t),iC=(t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:ix(i,!0),values:null};class iS{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=i_(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&iw(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(t,e,i,s)=>"x"===t?e:"r"===t?s:i,a=e.xAxisID=te(i.xAxisID,iM(t,"x")),r=e.yAxisID=te(i.yAxisID,iM(t,"y")),n=e.rAxisID=te(i.rAxisID,iM(t,"r")),o=e.indexAxis,l=e.iAxisID=s(o,a,r,n),h=e.vAxisID=s(o,r,a,n);e.xScale=this.getScaleForId(a),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(n),e.iScale=this.getScaleForId(l),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&tY(this._data,this),t._stacked&&iw(t)}_dataCheck(){let t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(J(e)){let t=this._cachedMeta;this._data=function(t,e){let i,s,a;let{iScale:r,vScale:n}=e,o="x"===r.axis?"x":"y",l="x"===n.axis?"x":"y",h=Object.keys(t),d=Array(h.length);for(i=0,s=h.length;i<s;++i)a=h[i],d[i]={[o]:a,[l]:t[a]};return d}(e,t)}else if(i!==e){if(i){tY(i,this);let t=this._cachedMeta;iw(t),t._parsed=[]}e&&Object.isExtensible(e)&&function(t,e){if(t._chartjs){t._chartjs.listeners.push(e);return}Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),tU.forEach(e=>{let i="_onData"+tp(e),s=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){let a=s.apply(this,e);return t._chartjs.listeners.forEach(t=>{"function"==typeof t[i]&&t[i](...e)}),a}})})}(e,this),this._syncList=[],this._data=e}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let e=this._cachedMeta,i=this.getDataset(),s=!1;this._dataCheck();let a=e._stacked;e._stacked=i_(e.vScale,e),e.stack!==i.stack&&(s=!0,iw(e),e.stack=i.stack),this._resyncElements(t),(s||a!==e._stacked)&&(iv(this,e._parsed),e._stacked=i_(e.vScale,e))}configure(){let t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){let i,s,a;let{_cachedMeta:r,_data:n}=this,{iScale:o,_stacked:l}=r,h=o.axis,d=0===t&&e===n.length||r._sorted,c=t>0&&r._parsed[t-1];if(!1===this._parsing)r._parsed=n,r._sorted=!0,a=n;else{a=K(n[t])?this.parseArrayData(r,n,t,e):J(n[t])?this.parseObjectData(r,n,t,e):this.parsePrimitiveData(r,n,t,e);let o=()=>null===s[h]||c&&s[h]<c[h];for(i=0;i<e;++i)r._parsed[i+t]=s=a[i],d&&(o()&&(d=!1),c=s);r._sorted=d}l&&iv(this,a)}parsePrimitiveData(t,e,i,s){let a,r;let{iScale:n,vScale:o}=t,l=n.axis,h=o.axis,d=n.getLabels(),c=n===o,u=Array(s);for(a=0;a<s;++a)r=a+i,u[a]={[l]:c||n.parse(d[r],r),[h]:o.parse(e[r],r)};return u}parseArrayData(t,e,i,s){let a,r,n;let{xScale:o,yScale:l}=t,h=Array(s);for(a=0;a<s;++a)n=e[r=a+i],h[a]={x:o.parse(n[0],r),y:l.parse(n[1],r)};return h}parseObjectData(t,e,i,s){let a,r,n;let{xScale:o,yScale:l}=t,{xAxisKey:h="x",yAxisKey:d="y"}=this._parsing,c=Array(s);for(a=0;a<s;++a)n=e[r=a+i],c[a]={x:o.parse(tg(n,h),r),y:l.parse(tg(n,d),r)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){let s=this.chart,a=this._cachedMeta,r=e[t.axis];return ib({keys:ix(s,!0),values:e._stacks[t.axis]._visualValues},r,a.index,{mode:i})}updateRangeFromParsed(t,e,i,s){let a=i[e.axis],r=null===a?NaN:a,n=s&&i._stacks[e.axis];s&&n&&(s.values=n,r=ib(s,a,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){let i,s;let a=this._cachedMeta,r=a._parsed,n=a._sorted&&t===a.iScale,o=r.length,l=this._getOtherScale(t),h=iC(e,a,this.chart),d={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:c,max:u}=function(t){let{min:e,max:i,minDefined:s,maxDefined:a}=t.getUserBounds();return{min:s?e:Number.NEGATIVE_INFINITY,max:a?i:Number.POSITIVE_INFINITY}}(l);function f(){let e=(s=r[i])[l.axis];return!Q(s[t.axis])||c>e||u<e}for(i=0;i<o&&(f()||(this.updateRangeFromParsed(d,t,s,h),!n));++i);if(n){for(i=o-1;i>=0;--i)if(!f()){this.updateRangeFromParsed(d,t,s,h);break}}return d}getAllParsedValues(t){let e,i,s;let a=this._cachedMeta._parsed,r=[];for(e=0,i=a.length;e<i;++e)Q(s=a[e][t.axis])&&r.push(s);return r}getMaxOverflow(){return!1}getLabelAndValue(t){let e=this._cachedMeta,i=e.iScale,s=e.vScale,a=this.getParsed(t);return{label:i?""+i.getLabelForValue(a[i.axis]):"",value:s?""+s.getLabelForValue(a[s.axis]):""}}_update(t){var e;let i,s,a,r;let n=this._cachedMeta;this.update(t||"default"),J(e=te(this.options.clip,function(t,e,i){if(!1===i)return!1;let s=im(t,i),a=im(e,i);return{top:a.end,right:s.end,bottom:a.start,left:s.start}}(n.xScale,n.yScale,this.getMaxOverflow())))?(i=e.top,s=e.right,a=e.bottom,r=e.left):i=s=a=r=e,n._clip={top:i,right:s,bottom:a,left:r,disabled:!1===e}}update(t){}draw(){let t;let e=this._ctx,i=this.chart,s=this._cachedMeta,a=s.data||[],r=i.chartArea,n=[],o=this._drawStart||0,l=this._drawCount||a.length-o,h=this.options.drawActiveElementsOnTop;for(s.dataset&&s.dataset.draw(e,r,o,l),t=o;t<o+l;++t){let i=a[t];!i.hidden&&(i.active&&h?n.push(i):i.draw(e,r))}for(t=0;t<n.length;++t)n[t].draw(e,r)}getStyle(t,e){let i=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){var s;let a;let r=this.getDataset();if(t>=0&&t<this._cachedMeta.data.length){let e=this._cachedMeta.data[t];(a=e.$context||(e.$context=eT(this.getContext(),{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"}))).parsed=this.getParsed(t),a.raw=r.data[t],a.index=a.dataIndex=t}else(a=this.$context||(this.$context=eT(this.chart.getContext(),{active:!1,dataset:void 0,datasetIndex:s=this.index,index:s,mode:"default",type:"dataset"}))).dataset=r,a.index=a.datasetIndex=this.index;return a.active=!!e,a.mode=i,a}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){let s="active"===e,a=this._cachedDataOpts,r=t+"-"+e,n=a[r],o=this.enableOptionSharing&&tm(i);if(n)return iP(n,o);let l=this.chart.config,h=l.datasetElementScopeKeys(this._type,t),d=s?[`${t}Hover`,"hover",t,""]:[t,""],c=l.getOptionScopes(this.getDataset(),h),u=Object.keys(eh.elements[t]),f=l.resolveNamedOptions(c,u,()=>this.getContext(i,s,e),d);return f.$shared&&(f.$shared=o,a[r]=Object.freeze(iP(f,o))),f}_resolveAnimations(t,e,i){let s;let a=this.chart,r=this._cachedDataOpts,n=`animation-${e}`,o=r[n];if(o)return o;if(!1!==a.options.animation){let a=this.chart.config,r=a.datasetAnimationScopeKeys(this._type,e),n=a.getOptionScopes(this.getDataset(),r);s=a.createResolver(n,this.getContext(t,i,e))}let l=new ip(a,s&&s.animations);return s&&s._cacheable&&(r[n]=Object.freeze(l)),l}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||ik(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){let i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,a=this.getSharedOptions(i),r=this.includeOptions(e,a)||a!==s;return this.updateSharedOptions(a,e,i),{sharedOptions:a,includeOptions:r}}updateElement(t,e,i,s){ik(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!ik(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;let a=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(a)||a})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let e=this._data,i=this._cachedMeta.data;for(let[t,e,i]of this._syncList)this[t](e,i);this._syncList=[];let s=i.length,a=e.length,r=Math.min(a,s);r&&this.parse(0,r),a>s?this._insertElements(s,a-s,t):a<s&&this._removeElements(a,s-a)}_insertElements(t,e,i=!0){let s;let a=this._cachedMeta,r=a.data,n=t+e,o=t=>{for(t.length+=e,s=t.length-1;s>=n;s--)t[s]=t[s-e]};for(o(r),s=t;s<n;++s)r[s]=new this.dataElementType;this._parsing&&o(a._parsed),this.parse(t,e),i&&this.updateElements(r,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){let i=this._cachedMeta;if(this._parsing){let s=i._parsed.splice(t,e);i._stacked&&iw(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);let i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function iD(t,e,i,s){return K(t)?function(t,e,i,s){let a=i.parse(t[0],s),r=i.parse(t[1],s),n=Math.min(a,r),o=Math.max(a,r),l=n,h=o;Math.abs(n)>Math.abs(o)&&(l=o,h=n),e[i.axis]=h,e._custom={barStart:l,barEnd:h,start:a,end:r,min:n,max:o}}(t,e,i,s):e[i.axis]=i.parse(t,s),e}function iO(t,e,i,s){let a,r,n,o;let l=t.iScale,h=t.vScale,d=l.getLabels(),c=l===h,u=[];for(a=i,r=i+s;a<r;++a)o=e[a],(n={})[l.axis]=c||l.parse(d[a],a),u.push(iD(o,n,h,a));return u}function iA(t){return t&&void 0!==t.barStart&&void 0!==t.barEnd}function iT(t,e,i,s){var a,r,n;return t=s?iL((a=t,r=e,n=i,t=a===r?n:a===n?r:a),i,e):iL(t,e,i)}function iL(t,e,i){return"start"===t?e:"end"===t?i:t}class iE extends iS{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,i,s){return iO(t,e,i,s)}parseArrayData(t,e,i,s){return iO(t,e,i,s)}parseObjectData(t,e,i,s){let a,r,n,o;let{iScale:l,vScale:h}=t,{xAxisKey:d="x",yAxisKey:c="y"}=this._parsing,u="x"===l.axis?d:c,f="x"===h.axis?d:c,g=[];for(a=i,r=i+s;a<r;++a)o=e[a],(n={})[l.axis]=l.parse(tg(o,u),a),g.push(iD(tg(o,f),n,h,a));return g}updateRangeFromParsed(t,e,i,s){super.updateRangeFromParsed(t,e,i,s);let a=i._custom;a&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,a.min),t.max=Math.max(t.max,a.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let{iScale:e,vScale:i}=this._cachedMeta,s=this.getParsed(t),a=s._custom,r=iA(a)?"["+a.start+", "+a.end+"]":""+i.getLabelForValue(s[i.axis]);return{label:""+e.getLabelForValue(s[e.axis]),value:r}}initialize(){this.enableOptionSharing=!0,super.initialize(),this._cachedMeta.stack=this.getDataset().stack}update(t){let e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,s){let a="reset"===s,{index:r,_cachedMeta:{vScale:n}}=this,o=n.getBasePixel(),l=n.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:c}=this._getSharedOptions(e,s);for(let u=e;u<e+i;u++){let e=this.getParsed(u),i=a||G(e[n.axis])?{base:o,head:o}:this._calculateBarValuePixels(u),f=this._calculateBarIndexPixels(u,h),g=(e._stacks||{})[n.axis],p={horizontal:l,base:i.base,enableBorderRadius:!g||iA(e._custom)||r===g._top||r===g._bottom,x:l?i.head:f.center,y:l?f.center:i.head,height:l?f.size:Math.abs(i.size),width:l?Math.abs(i.size):f.size};c&&(p.options=d||this.resolveDataElementOptions(u,t[u].active?"active":s));let m=p.options||t[u].options;(function(t,e,i,s){let a,r,n,o,l,h=e.borderSkipped,d={};if(!h){t.borderSkipped=d;return}if(!0===h){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:c,end:u,reverse:f,top:g,bottom:p}=(t.horizontal?(a=t.base>t.x,r="left",n="right"):(a=t.base<t.y,r="bottom",n="top"),a?(o="end",l="start"):(o="start",l="end"),{start:r,end:n,reverse:a,top:o,bottom:l});"middle"===h&&i&&(t.enableBorderRadius=!0,(i._top||0)===s?h=g:(i._bottom||0)===s?h=p:(d[iT(p,c,u,f)]=!0,h=g)),d[iT(h,c,u,f)]=!0,t.borderSkipped=d})(p,m,g,r),function(t,{inflateAmount:e},i){t.inflateAmount="auto"===e?.33*(1===i):e}(p,m,h.ratio),this.updateElement(t[u],u,p,s)}}_getStacks(t,e){let{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter(t=>t.controller.options.grouped),a=i.options.stacked,r=[],n=this._cachedMeta.controller.getParsed(e),o=n&&n[i.axis],l=t=>{let e=t._parsed.find(t=>t[i.axis]===o),s=e&&e[t.vScale.axis];if(G(s)||isNaN(s))return!0};for(let i of s)if(!(void 0!==e&&l(i))&&((!1===a||-1===r.indexOf(i.stack)||void 0===a&&void 0===i.stack)&&r.push(i.stack),i.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,i){let s=this._getStacks(t,i),a=void 0!==e?s.indexOf(e):-1;return -1===a?s.length-1:a}_getRuler(){let t,e;let i=this.options,s=this._cachedMeta,a=s.iScale,r=[];for(t=0,e=s.data.length;t<e;++t)r.push(a.getPixelForValue(this.getParsed(t)[a.axis],t));let n=i.barThickness;return{min:n||function(t){let e,i,s,a;let r=t.iScale,n=function(t,e){if(!t._cache.$bar){let i=t.getMatchingVisibleMetas(e),s=[];for(let e=0,a=i.length;e<a;e++)s=s.concat(i[e].controller.getAllParsedValues(t));t._cache.$bar=tq(s.sort((t,e)=>t-e))}return t._cache.$bar}(r,t.type),o=r._length,l=()=>{32767!==s&&-32768!==s&&(tm(a)&&(o=Math.min(o,Math.abs(s-a)||o)),a=s)};for(e=0,i=n.length;e<i;++e)s=r.getPixelForValue(n[e]),l();for(e=0,a=void 0,i=r.ticks.length;e<i;++e)s=r.getPixelForTick(e),l();return o}(s),pixels:r,start:a._startPixel,end:a._endPixel,stackCount:this._getStackCount(),scale:a,grouped:i.grouped,ratio:n?1:i.categoryPercentage*i.barPercentage}}_calculateBarValuePixels(t){let e,i;let{_cachedMeta:{vScale:s,_stacked:a,index:r},options:{base:n,minBarLength:o}}=this,l=n||0,h=this.getParsed(t),d=h._custom,c=iA(d),u=h[s.axis],f=0,g=a?this.applyStack(s,h,a):u;g!==u&&(f=g-u,g=u),c&&(u=d.barStart,g=d.barEnd-d.barStart,0!==u&&tD(u)!==tD(d.barEnd)&&(f=0),f+=u);let p=G(n)||c?f:n,m=s.getPixelForValue(p);if(Math.abs(i=(e=this.chart.getDataVisibility(t)?s.getPixelForValue(f+g):m)-m)<o){var x;i=(0!==(x=i)?tD(x):(s.isHorizontal()?1:-1)*(s.min>=l?1:-1))*o,u===l&&(m-=i/2);let t=s.getPixelForDecimal(0),n=s.getPixelForDecimal(1),d=Math.min(t,n);e=(m=Math.max(Math.min(m,Math.max(t,n)),d))+i,a&&!c&&(h._stacks[s.axis]._visualValues[r]=s.getValueForPixel(e)-s.getValueForPixel(m))}if(m===s.getPixelForValue(l)){let t=tD(i)*s.getLineWidthForValue(l)/2;m+=t,i-=t}return{size:i,base:m,head:e,center:e+i/2}}_calculateBarIndexPixels(t,e){let i,s;let a=e.scale,r=this.options,n=r.skipNull,o=te(r.maxBarThickness,1/0);if(e.grouped){let a=n?this._getStackCount(t):e.stackCount,l="flex"===r.barThickness?function(t,e,i,s){let a=e.pixels,r=a[t],n=t>0?a[t-1]:null,o=t<a.length-1?a[t+1]:null,l=i.categoryPercentage;null===n&&(n=r-(null===o?e.end-e.start:o-r)),null===o&&(o=r+r-n);let h=r-(r-Math.min(n,o))/2*l;return{chunk:Math.abs(o-n)/2*l/s,ratio:i.barPercentage,start:h}}(t,e,r,a):function(t,e,i,s){let a,r;let n=i.barThickness;return G(n)?(a=e.min*i.categoryPercentage,r=i.barPercentage):(a=n*s,r=1),{chunk:a/s,ratio:r,start:e.pixels[t]-a/2}}(t,e,r,a),h=this._getStackIndex(this.index,this._cachedMeta.stack,n?t:void 0);i=l.start+l.chunk*h+l.chunk/2,s=Math.min(o,l.chunk*l.ratio)}else i=a.getPixelForValue(this.getParsed(t)[a.axis],t),s=Math.min(o,e.min*e.ratio);return{base:i-s/2,head:i+s/2,center:i,size:s}}draw(){let t=this._cachedMeta,e=t.vScale,i=t.data,s=i.length,a=0;for(;a<s;++a)null===this.getParsed(a)[e.axis]||i[a].hidden||i[a].draw(this._ctx)}}class iR extends iS{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,s){let a=super.parsePrimitiveData(t,e,i,s);for(let t=0;t<a.length;t++)a[t]._custom=this.resolveDataElementOptions(t+i).radius;return a}parseArrayData(t,e,i,s){let a=super.parseArrayData(t,e,i,s);for(let t=0;t<a.length;t++){let s=e[i+t];a[t]._custom=te(s[2],this.resolveDataElementOptions(t+i).radius)}return a}parseObjectData(t,e,i,s){let a=super.parseObjectData(t,e,i,s);for(let t=0;t<a.length;t++){let s=e[i+t];a[t]._custom=te(s&&s.r&&+s.r,this.resolveDataElementOptions(t+i).radius)}return a}getMaxOverflow(){let t=this._cachedMeta.data,e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:a}=e,r=this.getParsed(t),n=s.getLabelForValue(r.x),o=a.getLabelForValue(r.y),l=r._custom;return{label:i[t]||"",value:"("+n+", "+o+(l?", "+l:"")+")"}}update(t){let e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,s){let a="reset"===s,{iScale:r,vScale:n}=this._cachedMeta,{sharedOptions:o,includeOptions:l}=this._getSharedOptions(e,s),h=r.axis,d=n.axis;for(let c=e;c<e+i;c++){let e=t[c],i=!a&&this.getParsed(c),u={},f=u[h]=a?r.getPixelForDecimal(.5):r.getPixelForValue(i[h]),g=u[d]=a?n.getBasePixel():n.getPixelForValue(i[d]);u.skip=isNaN(f)||isNaN(g),l&&(u.options=o||this.resolveDataElementOptions(c,e.active?"active":s),a&&(u.options.radius=0)),this.updateElement(e,c,u,s)}}resolveDataElementOptions(t,e){let i=this.getParsed(t),s=super.resolveDataElementOptions(t,e);s.$shared&&(s=Object.assign({},s,{$shared:!1}));let a=s.radius;return"active"!==e&&(s.radius=0),s.radius+=te(i&&i._custom,a),s}}class iF extends iS{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,a)=>{let r=t.getDatasetMeta(0).controller.getStyle(a);return{text:e,fillStyle:r.backgroundColor,strokeStyle:r.borderColor,fontColor:s,lineWidth:r.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){let i=this.getDataset().data,s=this._cachedMeta;if(!1===this._parsing)s._parsed=i;else{let a,r,n=t=>+i[t];if(J(i[t])){let{key:t="value"}=this._parsing;n=e=>+tg(i[e],t)}for(a=t,r=t+e;a<r;++a)s._parsed[a]=n(a)}}_getRotation(){return tE(this.options.rotation-90)}_getCircumference(){return tE(this.options.circumference)}_getRotationExtents(){let t=ty,e=-ty;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){let s=this.chart.getDatasetMeta(i).controller,a=s._getRotation(),r=s._getCircumference();t=Math.min(t,a),e=Math.max(e,a+r)}return{rotation:t,circumference:e-t}}update(t){let{chartArea:e}=this.chart,i=this._cachedMeta,s=i.data,a=this.getMaxBorderWidth()+this.getMaxOffset(s)+this.options.spacing,r=Math.max((Math.min(e.width,e.height)-a)/2,0),n=Math.min(ti(this.options.cutout,r),1),o=this._getRingWeight(this.index),{circumference:l,rotation:h}=this._getRotationExtents(),{ratioX:d,ratioY:c,offsetX:u,offsetY:f}=function(t,e,i){let s=1,a=1,r=0,n=0;if(e<ty){let o=t+e,l=Math.cos(t),h=Math.sin(t),d=Math.cos(o),c=Math.sin(o),u=(e,s,a)=>tz(e,t,o,!0)?1:Math.max(s,s*i,a,a*i),f=(e,s,a)=>tz(e,t,o,!0)?-1:Math.min(s,s*i,a,a*i),g=u(0,l,d),p=u(tk,h,c),m=f(t_,l,d),x=f(t_+tk,h,c);s=(g-m)/2,a=(p-x)/2,r=-(g+m)/2,n=-(p+x)/2}return{ratioX:s,ratioY:a,offsetX:r,offsetY:n}}(h,l,n),g=Math.max(Math.min((e.width-a)/d,(e.height-a)/c)/2,0),p=ts(this.options.radius,g),m=Math.max(p*n,0),x=(p-m)/this._getVisibleDatasetWeightTotal();this.offsetX=u*p,this.offsetY=f*p,i.total=this.calculateTotal(),this.outerRadius=p-x*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-x*o,0),this.updateElements(s,0,s.length,t)}_circumference(t,e){let i=this.options,s=this._cachedMeta,a=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||null===s._parsed[t]||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*a/ty)}updateElements(t,e,i,s){let a;let r="reset"===s,n=this.chart,o=n.chartArea,l=n.options.animation,h=(o.left+o.right)/2,d=(o.top+o.bottom)/2,c=r&&l.animateScale,u=c?0:this.innerRadius,f=c?0:this.outerRadius,{sharedOptions:g,includeOptions:p}=this._getSharedOptions(e,s),m=this._getRotation();for(a=0;a<e;++a)m+=this._circumference(a,r);for(a=e;a<e+i;++a){let e=this._circumference(a,r),i=t[a],n={x:h+this.offsetX,y:d+this.offsetY,startAngle:m,endAngle:m+e,circumference:e,outerRadius:f,innerRadius:u};p&&(n.options=g||this.resolveDataElementOptions(a,i.active?"active":s)),m+=e,this.updateElement(i,a,n,s)}}calculateTotal(){let t;let e=this._cachedMeta,i=e.data,s=0;for(t=0;t<i.length;t++){let a=e._parsed[t];null!==a&&!isNaN(a)&&this.chart.getDataVisibility(t)&&!i[t].hidden&&(s+=Math.abs(a))}return s}calculateCircumference(t){let e=this._cachedMeta.total;return e>0&&!isNaN(t)?Math.abs(t)/e*ty:0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],a=ee(e._parsed[t],i.options.locale);return{label:s[t]||"",value:a}}getMaxBorderWidth(t){let e,i,s,a,r,n=0,o=this.chart;if(!t){for(e=0,i=o.data.datasets.length;e<i;++e)if(o.isDatasetVisible(e)){t=(s=o.getDatasetMeta(e)).data,a=s.controller;break}}if(!t)return 0;for(e=0,i=t.length;e<i;++e)"inner"!==(r=a.resolveDataElementOptions(e)).borderAlign&&(n=Math.max(n,r.borderWidth||0,r.hoverBorderWidth||0));return n}getMaxOffset(t){let e=0;for(let i=0,s=t.length;i<s;++i){let t=this.resolveDataElementOptions(i);e=Math.max(e,t.offset||0,t.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(te(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class ij extends iS{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let e=this._cachedMeta,{dataset:i,data:s=[],_dataset:a}=e,r=this.chart._animationsDisabled,{start:n,count:o}=tQ(e,s,r);this._drawStart=n,this._drawCount=o,t0(e)&&(n=0,o=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!a._decimated,i.points=s;let l=this.resolveDatasetElementOptions(t);this.options.showLine||(l.borderWidth=0),l.segment=this.options.segment,this.updateElement(i,void 0,{animated:!r,options:l},t),this.updateElements(s,n,o,t)}updateElements(t,e,i,s){let a="reset"===s,{iScale:r,vScale:n,_stacked:o,_dataset:l}=this._cachedMeta,{sharedOptions:h,includeOptions:d}=this._getSharedOptions(e,s),c=r.axis,u=n.axis,{spanGaps:f,segment:g}=this.options,p=tT(f)?f:Number.POSITIVE_INFINITY,m=this.chart._animationsDisabled||a||"none"===s,x=e+i,b=t.length,_=e>0&&this.getParsed(e-1);for(let i=0;i<b;++i){let f=t[i],b=m?f:{};if(i<e||i>=x){b.skip=!0;continue}let y=this.getParsed(i),v=G(y[u]),M=b[c]=r.getPixelForValue(y[c],i),w=b[u]=a||v?n.getBasePixel():n.getPixelForValue(o?this.applyStack(n,y,o):y[u],i);b.skip=isNaN(M)||isNaN(w)||v,b.stop=i>0&&Math.abs(y[c]-_[c])>p,g&&(b.parsed=y,b.raw=l.data[i]),d&&(b.options=h||this.resolveDataElementOptions(i,f.active?"active":s)),m||this.updateElement(f,i,b,s),_=y}}getMaxOverflow(){let t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];return s.length?Math.max(i,s[0].size(this.resolveDataElementOptions(0)),s[s.length-1].size(this.resolveDataElementOptions(s.length-1)))/2:i}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}class iI extends iS{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,a)=>{let r=t.getDatasetMeta(0).controller.getStyle(a);return{text:e,fillStyle:r.backgroundColor,strokeStyle:r.borderColor,fontColor:s,lineWidth:r.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],a=ee(e._parsed[t].r,i.options.locale);return{label:s[t]||"",value:a}}parseObjectData(t,e,i,s){return eH.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){let t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((t,i)=>{let s=this.getParsed(i).r;!isNaN(s)&&this.chart.getDataVisibility(i)&&(s<e.min&&(e.min=s),s>e.max&&(e.max=s))}),e}_updateRadius(){let t=this.chart,e=t.chartArea,i=t.options,s=Math.max(Math.min(e.right-e.left,e.bottom-e.top)/2,0),a=Math.max(i.cutoutPercentage?s/100*i.cutoutPercentage:1,0),r=(s-a)/t.getVisibleDatasetCount();this.outerRadius=s-r*this.index,this.innerRadius=this.outerRadius-r}updateElements(t,e,i,s){let a;let r="reset"===s,n=this.chart,o=n.options.animation,l=this._cachedMeta.rScale,h=l.xCenter,d=l.yCenter,c=l.getIndexAngle(0)-.5*t_,u=c,f=360/this.countVisibleElements();for(a=0;a<e;++a)u+=this._computeAngle(a,s,f);for(a=e;a<e+i;a++){let e=t[a],i=u,g=u+this._computeAngle(a,s,f),p=n.getDataVisibility(a)?l.getDistanceFromCenterForValue(this.getParsed(a).r):0;u=g,r&&(o.animateScale&&(p=0),o.animateRotate&&(i=g=c));let m={x:h,y:d,innerRadius:0,outerRadius:p,startAngle:i,endAngle:g,options:this.resolveDataElementOptions(a,e.active?"active":s)};this.updateElement(e,a,m,s)}}countVisibleElements(){let t=this._cachedMeta,e=0;return t.data.forEach((t,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?tE(this.resolveDataElementOptions(t,e).angle||i):0}}class iN extends iF{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}class iz extends iS{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){let e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,s){return eH.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta,i=e.dataset,s=e.data||[],a=e.iScale.getLabels();if(i.points=s,"resize"!==t){let e=this.resolveDatasetElementOptions(t);this.options.showLine||(e.borderWidth=0);let r={_loop:!0,_fullLoop:a.length===s.length,options:e};this.updateElement(i,void 0,r,t)}this.updateElements(s,0,s.length,t)}updateElements(t,e,i,s){let a=this._cachedMeta.rScale,r="reset"===s;for(let n=e;n<e+i;n++){let e=t[n],i=this.resolveDataElementOptions(n,e.active?"active":s),o=a.getPointPositionForValue(n,this.getParsed(n).r),l=r?a.xCenter:o.x,h=r?a.yCenter:o.y,d={x:l,y:h,angle:o.angle,skip:isNaN(l)||isNaN(h),options:i};this.updateElement(e,n,d,s)}}}class iV extends iS{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:a}=e,r=this.getParsed(t),n=s.getLabelForValue(r.x),o=a.getLabelForValue(r.y);return{label:i[t]||"",value:"("+n+", "+o+")"}}update(t){let e=this._cachedMeta,{data:i=[]}=e,s=this.chart._animationsDisabled,{start:a,count:r}=tQ(e,i,s);if(this._drawStart=a,this._drawCount=r,t0(e)&&(a=0,r=i.length),this.options.showLine){this.datasetElementType||this.addElements();let{dataset:a,_dataset:r}=e;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!r._decimated,a.points=i;let n=this.resolveDatasetElementOptions(t);n.segment=this.options.segment,this.updateElement(a,void 0,{animated:!s,options:n},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(i,a,r,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,i,s){let a="reset"===s,{iScale:r,vScale:n,_stacked:o,_dataset:l}=this._cachedMeta,h=this.resolveDataElementOptions(e,s),d=this.getSharedOptions(h),c=this.includeOptions(s,d),u=r.axis,f=n.axis,{spanGaps:g,segment:p}=this.options,m=tT(g)?g:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||a||"none"===s,b=e>0&&this.getParsed(e-1);for(let h=e;h<e+i;++h){let e=t[h],i=this.getParsed(h),g=x?e:{},_=G(i[f]),y=g[u]=r.getPixelForValue(i[u],h),v=g[f]=a||_?n.getBasePixel():n.getPixelForValue(o?this.applyStack(n,i,o):i[f],h);g.skip=isNaN(y)||isNaN(v)||_,g.stop=h>0&&Math.abs(i[u]-b[u])>m,p&&(g.parsed=i,g.raw=l.data[h]),c&&(g.options=d||this.resolveDataElementOptions(h,e.active?"active":s)),x||this.updateElement(e,h,g,s),b=i}this.updateSharedOptions(d,s,h)}getMaxOverflow(){let t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let t=0;for(let i=e.length-1;i>=0;--i)t=Math.max(t,e[i].size(this.resolveDataElementOptions(i))/2);return t>0&&t}let i=t.dataset,s=i.options&&i.options.borderWidth||0;return e.length?Math.max(s,e[0].size(this.resolveDataElementOptions(0)),e[e.length-1].size(this.resolveDataElementOptions(e.length-1)))/2:s}}var iB=Object.freeze({__proto__:null,BarController:iE,BubbleController:iR,DoughnutController:iF,LineController:ij,PieController:iN,PolarAreaController:iI,RadarController:iz,ScatterController:iV});function iW(){throw Error("This method is not implemented: Check that a complete date adapter is provided.")}class iH{static override(t){Object.assign(iH.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return iW()}parse(){return iW()}format(){return iW()}add(){return iW()}diff(){return iW()}startOf(){return iW()}endOf(){return iW()}}var i$={_date:iH};function iU(t,e,i,s,a){let r=t.getSortedVisibleDatasetMetas(),n=i[e];for(let t=0,i=r.length;t<i;++t){let{index:i,data:o}=r[t],{lo:l,hi:h}=function(t,e,i,s){let{controller:a,data:r,_sorted:n}=t,o=a._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(o&&e===o.axis&&"r"!==e&&n&&r.length){let n=o._reversePixels?t$:tH;if(s){if(a._sharedOptions){let t=r[0],s="function"==typeof t.getRange&&t.getRange(e);if(s){let t=n(r,e,i-s),a=n(r,e,i+s);return{lo:t.lo,hi:a.hi}}}}else{let s=n(r,e,i);if(l){let{vScale:e}=a._cachedMeta,{_parsed:i}=t,r=i.slice(0,s.lo+1).reverse().findIndex(t=>!G(t[e.axis]));s.lo-=Math.max(0,r);let n=i.slice(s.hi).findIndex(t=>!G(t[e.axis]));s.hi+=Math.max(0,n)}return s}}return{lo:0,hi:r.length-1}}(r[t],e,n,a);for(let t=l;t<=h;++t){let e=o[t];e.skip||s(e,i,t)}}}function iY(t,e,i,s,a){let r=[];return(a||t.isPointInArea(e))&&iU(t,i,e,function(i,n,o){(a||ep(i,t.chartArea,0))&&i.inRange(e.x,e.y,s)&&r.push({element:i,datasetIndex:n,index:o})},!0),r}function iq(t,e,i,s,a,r){let n;return r||t.isPointInArea(e)?"r"!==i||s?function(t,e,i,s,a,r){let n=[],o=function(t){let e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,s){return Math.sqrt(Math.pow(e?Math.abs(t.x-s.x):0,2)+Math.pow(i?Math.abs(t.y-s.y):0,2))}}(i),l=Number.POSITIVE_INFINITY;return iU(t,i,e,function(i,h,d){let c=i.inRange(e.x,e.y,a);if(s&&!c)return;let u=i.getCenterPoint(a);if(!(r||t.isPointInArea(u))&&!c)return;let f=o(e,u);f<l?(n=[{element:i,datasetIndex:h,index:d}],l=f):f===l&&n.push({element:i,datasetIndex:h,index:d})}),n}(t,e,i,s,a,r):(n=[],iU(t,i,e,function(t,i,s){let{startAngle:r,endAngle:o}=t.getProps(["startAngle","endAngle"],a),{angle:l}=tF(t,{x:e.x,y:e.y});tz(l,r,o)&&n.push({element:t,datasetIndex:i,index:s})}),n):[]}function iX(t,e,i,s,a){let r=[],n="x"===i?"inXRange":"inYRange",o=!1;return(iU(t,i,e,(t,s,l)=>{t[n]&&t[n](e[i],a)&&(r.push({element:t,datasetIndex:s,index:l}),o=o||t.inRange(e.x,e.y,a))}),s&&!o)?[]:r}var iZ={modes:{index(t,e,i,s){let a=e1(e,t),r=i.axis||"x",n=i.includeInvisible||!1,o=i.intersect?iY(t,a,r,s,n):iq(t,a,r,!1,s,n),l=[];return o.length?(t.getSortedVisibleDatasetMetas().forEach(t=>{let e=o[0].index,i=t.data[e];i&&!i.skip&&l.push({element:i,datasetIndex:t.index,index:e})}),l):[]},dataset(t,e,i,s){let a=e1(e,t),r=i.axis||"xy",n=i.includeInvisible||!1,o=i.intersect?iY(t,a,r,s,n):iq(t,a,r,!1,s,n);if(o.length>0){let e=o[0].datasetIndex,i=t.getDatasetMeta(e).data;o=[];for(let t=0;t<i.length;++t)o.push({element:i[t],datasetIndex:e,index:t})}return o},point(t,e,i,s){let a=e1(e,t);return iY(t,a,i.axis||"xy",s,i.includeInvisible||!1)},nearest(t,e,i,s){let a=e1(e,t),r=i.axis||"xy",n=i.includeInvisible||!1;return iq(t,a,r,i.intersect,s,n)},x(t,e,i,s){let a=e1(e,t);return iX(t,a,"x",i.intersect,s)},y(t,e,i,s){let a=e1(e,t);return iX(t,a,"y",i.intersect,s)}}};let iG=["left","top","right","bottom"];function iK(t,e){return t.filter(t=>t.pos===e)}function iJ(t,e){return t.filter(t=>-1===iG.indexOf(t.pos)&&t.box.axis===e)}function iQ(t,e){return t.sort((t,i)=>{let s=e?i:t,a=e?t:i;return s.weight===a.weight?s.index-a.index:s.weight-a.weight})}function i0(t,e,i,s){return Math.max(t[i],e[i])+Math.max(t[s],e[s])}function i1(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function i2(t,e,i,s){let a,r,n,o,l,h;let d=[];for(a=0,r=t.length,l=0;a<r;++a){(o=(n=t[a]).box).update(n.width||e.w,n.height||e.h,function(t,e){let i=e.maxPadding;return function(t){let s={left:0,top:0,right:0,bottom:0};return t.forEach(t=>{s[t]=Math.max(e[t],i[t])}),s}(t?["left","right"]:["top","bottom"])}(n.horizontal,e));let{same:r,other:c}=function(t,e,i,s){let{pos:a,box:r}=i,n=t.maxPadding;if(!J(a)){i.size&&(t[a]-=i.size);let e=s[i.stack]||{size:0,count:1};e.size=Math.max(e.size,i.horizontal?r.height:r.width),i.size=e.size/e.count,t[a]+=i.size}r.getPadding&&i1(n,r.getPadding());let o=Math.max(0,e.outerWidth-i0(n,t,"left","right")),l=Math.max(0,e.outerHeight-i0(n,t,"top","bottom")),h=o!==t.w,d=l!==t.h;return t.w=o,t.h=l,i.horizontal?{same:h,other:d}:{same:d,other:h}}(e,i,n,s);l|=r&&d.length,h=h||c,o.fullSize||d.push(n)}return l&&i2(d,e,i,s)||h}function i5(t,e,i,s,a){t.top=i,t.left=e,t.right=e+s,t.bottom=i+a,t.width=s,t.height=a}function i4(t,e,i,s){let a=i.padding,{x:r,y:n}=e;for(let o of t){let t=o.box,l=s[o.stack]||{count:1,placed:0,weight:1},h=o.stackWeight/l.weight||1;if(o.horizontal){let s=e.w*h,r=l.size||t.height;tm(l.start)&&(n=l.start),t.fullSize?i5(t,a.left,n,i.outerWidth-a.right-a.left,r):i5(t,e.left+l.placed,n,s,r),l.start=n,l.placed+=s,n=t.bottom}else{let s=e.h*h,n=l.size||t.width;tm(l.start)&&(r=l.start),t.fullSize?i5(t,r,a.top,n,i.outerHeight-a.bottom-a.top):i5(t,r,e.top+l.placed,n,s),l.start=r,l.placed+=s,r=t.right}}e.x=r,e.y=n}var i8={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){let i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,s){if(!t)return;let a=eD(t.options.layout.padding),r=Math.max(e-a.width,0),n=Math.max(i-a.height,0),o=function(t){let e=function(t){let e,i,s,a,r,n;let o=[];for(e=0,i=(t||[]).length;e<i;++e)s=t[e],({position:a,options:{stack:r,stackWeight:n=1}}=s),o.push({index:e,box:s,pos:a,horizontal:s.isHorizontal(),weight:s.weight,stack:r&&a+r,stackWeight:n});return o}(t),i=iQ(e.filter(t=>t.box.fullSize),!0),s=iQ(iK(e,"left"),!0),a=iQ(iK(e,"right")),r=iQ(iK(e,"top"),!0),n=iQ(iK(e,"bottom")),o=iJ(e,"x"),l=iJ(e,"y");return{fullSize:i,leftAndTop:s.concat(r),rightAndBottom:a.concat(l).concat(n).concat(o),chartArea:iK(e,"chartArea"),vertical:s.concat(a).concat(l),horizontal:r.concat(n).concat(o)}}(t.boxes),l=o.vertical,h=o.horizontal;tr(t.boxes,t=>{"function"==typeof t.beforeLayout&&t.beforeLayout()});let d=Object.freeze({outerWidth:e,outerHeight:i,padding:a,availableWidth:r,availableHeight:n,vBoxMaxWidth:r/2/(l.reduce((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1,0)||1),hBoxMaxHeight:n/2}),c=Object.assign({},a);i1(c,eD(s));let u=Object.assign({maxPadding:c,w:r,h:n,x:a.left,y:a.top},a),f=function(t,e){let i,s,a;let r=function(t){let e={};for(let i of t){let{stack:t,pos:s,stackWeight:a}=i;if(!t||!iG.includes(s))continue;let r=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=a}return e}(t),{vBoxMaxWidth:n,hBoxMaxHeight:o}=e;for(i=0,s=t.length;i<s;++i){let{fullSize:s}=(a=t[i]).box,l=r[a.stack],h=l&&a.stackWeight/l.weight;a.horizontal?(a.width=h?h*n:s&&e.availableWidth,a.height=o):(a.width=n,a.height=h?h*o:s&&e.availableHeight)}return r}(l.concat(h),d);i2(o.fullSize,u,d,f),i2(l,u,d,f),i2(h,u,d,f)&&i2(l,u,d,f),function(t){let e=t.maxPadding;function i(i){let s=Math.max(e[i]-t[i],0);return t[i]+=s,s}t.y+=i("top"),t.x+=i("left"),i("right"),i("bottom")}(u),i4(o.leftAndTop,u,d,f),u.x+=u.w,u.y+=u.h,i4(o.rightAndBottom,u,d,f),t.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},tr(o.chartArea,e=>{let i=e.box;Object.assign(i,t.chartArea),i.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class i3{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}}class i6 extends i3{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}let i9="$chartjs",i7={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},st=t=>null===t||""===t,se=!!e4&&{passive:!0};function si(t,e){for(let i of t)if(i===e||i.contains(e))return!0}function ss(t,e,i){let s=t.canvas,a=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||si(i.addedNodes,s))&&!si(i.removedNodes,s);e&&i()});return a.observe(document,{childList:!0,subtree:!0}),a}function sa(t,e,i){let s=t.canvas,a=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||si(i.removedNodes,s))&&!si(i.addedNodes,s);e&&i()});return a.observe(document,{childList:!0,subtree:!0}),a}let sr=new Map,sn=0;function so(){let t=window.devicePixelRatio;t!==sn&&(sn=t,sr.forEach((e,i)=>{i.currentDevicePixelRatio!==t&&e()}))}function sl(t,e,i){let s=t.canvas,a=s&&eZ(s);if(!a)return;let r=tZ((t,e)=>{let s=a.clientWidth;i(t,e),s<a.clientWidth&&i()},window),n=new ResizeObserver(t=>{let e=t[0],i=e.contentRect.width,s=e.contentRect.height;(0!==i||0!==s)&&r(i,s)});return n.observe(a),sr.size||window.addEventListener("resize",so),sr.set(t,r),n}function sh(t,e,i){if(i&&i.disconnect(),"resize"===e)sr.delete(t),sr.size||window.removeEventListener("resize",so)}function sd(t,e,i){let s=t.canvas,a=tZ(e=>{null!==t.ctx&&i(function(t,e){let i=i7[t.type]||t.type,{x:s,y:a}=e1(t,e);return{type:i,chart:e,native:t,x:void 0!==s?s:null,y:void 0!==a?a:null}}(e,t))},t);return s&&s.addEventListener(e,a,se),a}class sc extends i3{acquireContext(t,e){let i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(function(t,e){let i=t.style,s=t.getAttribute("height"),a=t.getAttribute("width");if(t[i9]={initial:{height:s,width:a,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",st(a)){let e=e8(t,"width");void 0!==e&&(t.width=e)}if(st(s)){if(""===t.style.height)t.height=t.width/(e||2);else{let e=e8(t,"height");void 0!==e&&(t.height=e)}}}(t,e),i):null}releaseContext(t){let e=t.canvas;if(!e[i9])return!1;let i=e[i9].initial;["height","width"].forEach(t=>{let s=i[t];G(s)?e.removeAttribute(t):e.setAttribute(t,s)});let s=i.style||{};return Object.keys(s).forEach(t=>{e.style[t]=s[t]}),e.width=e.width,delete e[i9],!0}addEventListener(t,e,i){this.removeEventListener(t,e);let s=t.$proxies||(t.$proxies={}),a={attach:ss,detach:sa,resize:sl}[e]||sd;s[e]=a(t,e,i)}removeEventListener(t,e){let i=t.$proxies||(t.$proxies={}),s=i[e];s&&((({attach:sh,detach:sh,resize:sh})[e]||function(t,e,i){t&&t.canvas&&t.canvas.removeEventListener(e,i,se)})(t,e,s),i[e]=void 0)}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return function(t,e,i,s){let a=eK(t),r=eQ(a,"margin"),n=eG(a.maxWidth,t,"clientWidth")||tM,o=eG(a.maxHeight,t,"clientHeight")||tM,l=function(t,e,i){let s,a;if(void 0===e||void 0===i){let r=t&&eZ(t);if(r){let t=r.getBoundingClientRect(),n=eK(r),o=eQ(n,"border","width"),l=eQ(n,"padding");e=t.width-l.width-o.width,i=t.height-l.height-o.height,s=eG(n.maxWidth,r,"clientWidth"),a=eG(n.maxHeight,r,"clientHeight")}else e=t.clientWidth,i=t.clientHeight}return{width:e,height:i,maxWidth:s||tM,maxHeight:a||tM}}(t,e,i),{width:h,height:d}=l;if("content-box"===a.boxSizing){let t=eQ(a,"border","width"),e=eQ(a,"padding");h-=e.width+t.width,d-=e.height+t.height}return h=Math.max(0,h-r.width),d=Math.max(0,s?h/s:d-r.height),h=e2(Math.min(h,n,l.maxWidth)),d=e2(Math.min(d,o,l.maxHeight)),h&&!d&&(d=e2(h/2)),(void 0!==e||void 0!==i)&&s&&l.height&&d>l.height&&(h=e2(Math.floor((d=l.height)*s))),{width:h,height:d}}(t,e,i,s)}isAttached(t){let e=t&&eZ(t);return!!(e&&e.isConnected)}}class su{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return tT(this.x)&&tT(this.y)}getProps(t,e){let i=this.$animations;if(!e||!i)return this;let s={};return t.forEach(t=>{s[t]=i[t]&&i[t].active()?i[t]._to:this[t]}),s}}function sf(t,e,i,s,a){let r,n,o;let l=te(s,0),h=Math.min(te(a,t.length),t.length),d=0;for(i=Math.ceil(i),a&&(i=(r=a-s)/Math.floor(r/i)),o=l;o<0;)o=Math.round(l+ ++d*i);for(n=Math.max(l,0);n<h;n++)n===o&&(e.push(t[n]),o=Math.round(l+ ++d*i))}let sg=t=>"left"===t?"right":"right"===t?"left":t,sp=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i,sm=(t,e)=>Math.min(e||t,t);function sx(t,e){let i=[],s=t.length/e,a=t.length,r=0;for(;r<a;r+=s)i.push(t[Math.floor(r)]);return i}function sb(t){return t.drawTicks?t.tickLength:0}function s_(t,e){if(!t.display)return 0;let i=eO(t.font,e),s=eD(t.padding);return(K(t.text)?t.text.length:1)*i.lineHeight+s.height}class sy extends su{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=tt(t,Number.POSITIVE_INFINITY),e=tt(e,Number.NEGATIVE_INFINITY),i=tt(i,Number.POSITIVE_INFINITY),s=tt(s,Number.NEGATIVE_INFINITY),{min:tt(t,i),max:tt(e,s),minDefined:Q(t),maxDefined:Q(e)}}getMinMax(t){let e,{min:i,max:s,minDefined:a,maxDefined:r}=this.getUserBounds();if(a&&r)return{min:i,max:s};let n=this.getMatchingVisibleMetas();for(let o=0,l=n.length;o<l;++o)e=n[o].controller.getMinMax(this,t),a||(i=Math.min(i,e.min)),r||(s=Math.max(s,e.max));return i=r&&i>s?s:i,s=a&&i>s?i:s,{min:tt(i,tt(s,i)),max:tt(s,tt(i,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){ta(this.options.beforeUpdate,[this])}update(t,e,i){let{beginAtZero:s,grace:a,ticks:r}=this.options,n=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function(t,e,i){let{min:s,max:a}=t,r=ts(e,(a-s)/2),n=(t,e)=>i&&0===t?0:t+e;return{min:n(s,-Math.abs(r)),max:n(a,r)}}(this,a,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let o=n<this.ticks.length;this._convertTicksToLabels(o?sx(this.ticks,n):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||"auto"===r.source)&&(this.ticks=function(t,e){let i=t.options.ticks,s=function(t){let e=t.options.offset,i=t._tickSize();return Math.floor(Math.min(t._length/i+ +!e,t._maxLength/i))}(t),a=Math.min(i.maxTicksLimit||s,s),r=i.major.enabled?function(t){let e,i;let s=[];for(e=0,i=t.length;e<i;e++)t[e].major&&s.push(e);return s}(e):[],n=r.length,o=r[0],l=r[n-1],h=[];if(n>a)return function(t,e,i,s){let a,r=0,n=i[0];for(a=0,s=Math.ceil(s);a<t.length;a++)a===n&&(e.push(t[a]),n=i[++r*s])}(e,h,r,n/a),h;let d=function(t,e,i){let s=function(t){let e,i;let s=t.length;if(s<2)return!1;for(i=t[0],e=1;e<s;++e)if(t[e]-t[e-1]!==i)return!1;return i}(t),a=e.length/i;if(!s)return Math.max(a,1);let r=function(t){let e;let i=[],s=Math.sqrt(t);for(e=1;e<s;e++)t%e==0&&(i.push(e),i.push(t/e));return s===(0|s)&&i.push(s),i.sort((t,e)=>t-e).pop(),i}(s);for(let t=0,e=r.length-1;t<e;t++){let e=r[t];if(e>a)return e}return Math.max(a,1)}(r,e,a);if(n>0){let t,i;let s=n>1?Math.round((l-o)/(n-1)):null;for(sf(e,h,d,G(s)?0:o-s,o),t=0,i=n-1;t<i;t++)sf(e,h,d,r[t],r[t+1]);return sf(e,h,d,l,G(s)?e.length:l+s),h}return sf(e,h,d),h}(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),o&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){ta(this.options.afterUpdate,[this])}beforeSetDimensions(){ta(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){ta(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),ta(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){ta(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){let e,i,s;let a=this.options.ticks;for(e=0,i=t.length;e<i;e++)(s=t[e]).label=ta(a.callback,[s.value,e,t],this)}afterTickToLabelConversion(){ta(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){ta(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let t,e,i;let s=this.options,a=s.ticks,r=sm(this.ticks.length,s.ticks.maxTicksLimit),n=a.minRotation||0,o=a.maxRotation,l=n;if(!this._isVisible()||!a.display||n>=o||r<=1||!this.isHorizontal()){this.labelRotation=n;return}let h=this._getLabelSizes(),d=h.widest.width,c=h.highest.height,u=tV(this.chart.width-d,0,this.maxWidth);d+6>(t=s.offset?this.maxWidth/r:u/(r-1))&&(t=u/(r-(s.offset?.5:1)),e=this.maxHeight-sb(s.grid)-a.padding-s_(s.title,this.chart.options.font),i=Math.sqrt(d*d+c*c),l=Math.max(n,Math.min(o,l=180/t_*Math.min(Math.asin(tV((h.highest.height+6)/t,-1,1)),Math.asin(tV(e/i,-1,1))-Math.asin(tV(c/i,-1,1)))))),this.labelRotation=l}afterCalculateLabelRotation(){ta(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){ta(this.options.beforeFit,[this])}fit(){let t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:a}}=this,r=this._isVisible(),n=this.isHorizontal();if(r){let r=s_(s,e.options.font);if(n?(t.width=this.maxWidth,t.height=sb(a)+r):(t.height=this.maxHeight,t.width=sb(a)+r),i.display&&this.ticks.length){let{first:e,last:s,widest:a,highest:r}=this._getLabelSizes(),o=2*i.padding,l=tE(this.labelRotation),h=Math.cos(l),d=Math.sin(l);if(n){let e=i.mirror?0:d*a.width+h*r.height;t.height=Math.min(this.maxHeight,t.height+e+o)}else{let e=i.mirror?0:h*a.width+d*r.height;t.width=Math.min(this.maxWidth,t.width+e+o)}this._calculatePadding(e,s,d,h)}}this._handleMargins(),n?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){let{ticks:{align:a,padding:r},position:n}=this.options,o=0!==this.labelRotation,l="top"!==n&&"x"===this.axis;if(this.isHorizontal()){let n=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1),d=0,c=0;o?l?(d=s*t.width,c=i*e.height):(d=i*t.height,c=s*e.width):"start"===a?c=e.width:"end"===a?d=t.width:"inner"!==a&&(d=t.width/2,c=e.width/2),this.paddingLeft=Math.max((d-n+r)*this.width/(this.width-n),0),this.paddingRight=Math.max((c-h+r)*this.width/(this.width-h),0)}else{let i=e.height/2,s=t.height/2;"start"===a?(i=0,s=t.height):"end"===a&&(i=e.height,s=0),this.paddingTop=i+r,this.paddingBottom=s+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){ta(this.options.afterFit,[this])}isHorizontal(){let{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)G(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){let e=this.options.ticks.sampleSize,i=this.ticks;e<i.length&&(i=sx(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){let s,a,r,n,o,l,h,d,c,u,f;let{ctx:g,_longestTextCache:p}=this,m=[],x=[],b=Math.floor(e/sm(e,i)),_=0,y=0;for(s=0;s<e;s+=b){if(n=t[s].label,g.font=l=(o=this._resolveTickFontOptions(s)).string,h=p[l]=p[l]||{data:{},gc:[]},d=o.lineHeight,c=u=0,G(n)||K(n)){if(K(n))for(a=0,r=n.length;a<r;++a)G(f=n[a])||K(f)||(c=ed(g,h.data,h.gc,c,f),u+=d)}else c=ed(g,h.data,h.gc,c,n),u=d;m.push(c),x.push(u),_=Math.max(c,_),y=Math.max(u,y)}tr(p,t=>{let i;let s=t.gc,a=s.length/2;if(a>e){for(i=0;i<a;++i)delete t.data[s[i]];s.splice(0,a)}});let v=m.indexOf(_),M=x.indexOf(y),w=t=>({width:m[t]||0,height:x[t]||0});return{first:w(0),last:w(e-1),widest:w(v),highest:w(M),widths:m,heights:x}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);let e=this._startPixel+t*this._length;return tV(this._alignToPixels?ec(this.chart,e,0):e,-32768,32767)}getDecimalForPixel(t){let e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){let e=this.ticks||[];if(t>=0&&t<e.length){let i=e[t];return i.$context||(i.$context=eT(this.getContext(),{tick:i,index:t,type:"tick"}))}return this.$context||(this.$context=eT(this.chart.getContext(),{scale:this,type:"scale"}))}_tickSize(){let t=this.options.ticks,e=tE(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),a=this._getLabelSizes(),r=t.autoSkipPadding||0,n=a?a.widest.width+r:0,o=a?a.highest.height+r:0;return this.isHorizontal()?o*i>n*s?n/i:o/s:o*s<n*i?o/i:n/s}_isVisible(){let t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){let e,i,s,a,r,n,o,l,h,d,c,u;let f=this.axis,g=this.chart,p=this.options,{grid:m,position:x,border:b}=p,_=m.offset,y=this.isHorizontal(),v=this.ticks.length+ +!!_,M=sb(m),w=[],k=b.setContext(this.getContext()),P=k.display?k.width:0,C=P/2,S=function(t){return ec(g,t,P)};if("top"===x)e=S(this.bottom),n=this.bottom-M,l=e-C,d=S(t.top)+C,u=t.bottom;else if("bottom"===x)e=S(this.top),d=t.top,u=S(t.bottom)-C,n=e+C,l=this.top+M;else if("left"===x)e=S(this.right),r=this.right-M,o=e-C,h=S(t.left)+C,c=t.right;else if("right"===x)e=S(this.left),h=t.left,c=S(t.right)-C,r=e+C,o=this.left+M;else if("x"===f){if("center"===x)e=S((t.top+t.bottom)/2+.5);else if(J(x)){let t=Object.keys(x)[0],i=x[t];e=S(this.chart.scales[t].getPixelForValue(i))}d=t.top,u=t.bottom,l=(n=e+C)+M}else if("y"===f){if("center"===x)e=S((t.left+t.right)/2);else if(J(x)){let t=Object.keys(x)[0],i=x[t];e=S(this.chart.scales[t].getPixelForValue(i))}o=(r=e-C)-M,h=t.left,c=t.right}let D=te(p.ticks.maxTicksLimit,v),O=Math.max(1,Math.ceil(v/D));for(i=0;i<v;i+=O){let t=this.getContext(i),e=m.setContext(t),f=b.setContext(t),p=e.lineWidth,x=e.color,v=f.dash||[],M=f.dashOffset,k=e.tickWidth,P=e.tickColor,C=e.tickBorderDash||[],S=e.tickBorderDashOffset;void 0!==(s=function(t,e,i){let s;let a=t.ticks.length,r=Math.min(e,a-1),n=t._startPixel,o=t._endPixel,l=t.getPixelForTick(r);if(!i||(s=1===a?Math.max(l-n,o-l):0===e?(t.getPixelForTick(1)-l)/2:(l-t.getPixelForTick(r-1))/2,!((l+=r<e?s:-s)<n-1e-6)&&!(l>o+1e-6)))return l}(this,i,_))&&(a=ec(g,s,p),y?r=o=h=c=a:n=l=d=u=a,w.push({tx1:r,ty1:n,tx2:o,ty2:l,x1:h,y1:d,x2:c,y2:u,width:p,color:x,borderDash:v,borderDashOffset:M,tickWidth:k,tickColor:P,tickBorderDash:C,tickBorderDashOffset:S}))}return this._ticksLength=v,this._borderValue=e,w}_computeLabelItems(t){let e,i,s,a,r,n,o,l,h,d,c;let u=this.axis,f=this.options,{position:g,ticks:p}=f,m=this.isHorizontal(),x=this.ticks,{align:b,crossAlign:_,padding:y,mirror:v}=p,M=sb(f.grid),w=M+y,k=v?-y:w,P=-tE(this.labelRotation),C=[],S="middle";if("top"===g)r=this.bottom-k,n=this._getXAxisLabelAlignment();else if("bottom"===g)r=this.top+k,n=this._getXAxisLabelAlignment();else if("left"===g){let t=this._getYAxisLabelAlignment(M);n=t.textAlign,a=t.x}else if("right"===g){let t=this._getYAxisLabelAlignment(M);n=t.textAlign,a=t.x}else if("x"===u){if("center"===g)r=(t.top+t.bottom)/2+w;else if(J(g)){let t=Object.keys(g)[0],e=g[t];r=this.chart.scales[t].getPixelForValue(e)+w}n=this._getXAxisLabelAlignment()}else if("y"===u){if("center"===g)a=(t.left+t.right)/2-w;else if(J(g)){let t=Object.keys(g)[0],e=g[t];a=this.chart.scales[t].getPixelForValue(e)}n=this._getYAxisLabelAlignment(M).textAlign}"y"===u&&("start"===b?S="top":"end"===b&&(S="bottom"));let D=this._getLabelSizes();for(e=0,i=x.length;e<i;++e){let t;s=x[e].label;let u=p.setContext(this.getContext(e));o=this.getPixelForTick(e)+p.labelOffset,h=(l=this._resolveTickFontOptions(e)).lineHeight;let f=(d=K(s)?s.length:1)/2,b=u.color,y=u.textStrokeColor,M=u.textStrokeWidth,w=n;if(m?(a=o,"inner"===n&&(w=e===i-1?this.options.reverse?"left":"right":0===e?this.options.reverse?"right":"left":"center"),c="top"===g?"near"===_||0!==P?-d*h+h/2:"center"===_?-D.highest.height/2-f*h+h:-D.highest.height+h/2:"near"===_||0!==P?h/2:"center"===_?D.highest.height/2-f*h:D.highest.height-d*h,v&&(c*=-1),0===P||u.showLabelBackdrop||(a+=h/2*Math.sin(P))):(r=o,c=(1-d)*h/2),u.showLabelBackdrop){let s=eD(u.backdropPadding),a=D.heights[e],r=D.widths[e],o=c-s.top,l=0-s.left;switch(S){case"middle":o-=a/2;break;case"bottom":o-=a}switch(n){case"center":l-=r/2;break;case"right":l-=r;break;case"inner":e===i-1?l-=r:e>0&&(l-=r/2)}t={left:l,top:o,width:r+s.width,height:a+s.height,color:u.backdropColor}}C.push({label:s,font:l,textOffset:c,options:{rotation:P,color:b,strokeColor:y,strokeWidth:M,textAlign:w,textBaseline:S,translation:[a,r],backdrop:t}})}return C}_getXAxisLabelAlignment(){let{position:t,ticks:e}=this.options;if(-tE(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){let e,i;let{position:s,ticks:{crossAlign:a,mirror:r,padding:n}}=this.options,o=this._getLabelSizes(),l=t+n,h=o.widest.width;return"left"===s?r?(i=this.right+n,"near"===a?e="left":"center"===a?(e="center",i+=h/2):(e="right",i+=h)):(i=this.right-l,"near"===a?e="right":"center"===a?(e="center",i-=h/2):(e="left",i=this.left)):"right"===s?r?(i=this.left+n,"near"===a?e="right":"center"===a?(e="center",i-=h/2):(e="left",i-=h)):(i=this.left+l,"near"===a?e="left":"center"===a?(e="center",i+=h/2):(e="right",i=this.right)):e="right",{textAlign:e,x:i}}_computeLabelArea(){if(this.options.ticks.mirror)return;let t=this.chart,e=this.options.position;return"left"===e||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){let{ctx:t,options:{backgroundColor:e},left:i,top:s,width:a,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,a,r),t.restore())}getLineWidthForValue(t){let e=this.options.grid;if(!this._isVisible()||!e.display)return 0;let i=this.ticks.findIndex(e=>e.value===t);return i>=0?e.setContext(this.getContext(i)).lineWidth:0}drawGrid(t){let e,i;let s=this.options.grid,a=this.ctx,r=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t)),n=(t,e,i)=>{i.width&&i.color&&(a.save(),a.lineWidth=i.width,a.strokeStyle=i.color,a.setLineDash(i.borderDash||[]),a.lineDashOffset=i.borderDashOffset,a.beginPath(),a.moveTo(t.x,t.y),a.lineTo(e.x,e.y),a.stroke(),a.restore())};if(s.display)for(e=0,i=r.length;e<i;++e){let t=r[e];s.drawOnChartArea&&n({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),s.drawTicks&&n({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){let t,e,i,s;let{chart:a,ctx:r,options:{border:n,grid:o}}=this,l=n.setContext(this.getContext()),h=n.display?l.width:0;if(!h)return;let d=o.setContext(this.getContext(0)).lineWidth,c=this._borderValue;this.isHorizontal()?(t=ec(a,this.left,h)-h/2,e=ec(a,this.right,d)+d/2,i=s=c):(i=ec(a,this.top,h)-h/2,s=ec(a,this.bottom,d)+d/2,t=e=c),r.save(),r.lineWidth=l.width,r.strokeStyle=l.color,r.beginPath(),r.moveTo(t,i),r.lineTo(e,s),r.stroke(),r.restore()}drawLabels(t){if(!this.options.ticks.display)return;let e=this.ctx,i=this._computeLabelArea();for(let s of(i&&em(e,i),this.getLabelItems(t))){let t=s.options,i=s.font;ey(e,s.label,0,s.textOffset,i,t)}i&&ex(e)}drawTitle(){let t;let{ctx:e,options:{position:i,title:s,reverse:a}}=this;if(!s.display)return;let r=eO(s.font),n=eD(s.padding),o=s.align,l=r.lineHeight/2;"bottom"===i||"center"===i||J(i)?(l+=n.bottom,K(s.text)&&(l+=r.lineHeight*(s.text.length-1))):l+=n.top;let{titleX:h,titleY:d,maxWidth:c,rotation:u}=function(t,e,i,s){let a,r,n;let{top:o,left:l,bottom:h,right:d,chart:c}=t,{chartArea:u,scales:f}=c,g=0,p=h-o,m=d-l;if(t.isHorizontal()){if(r=tK(s,l,d),J(i)){let t=Object.keys(i)[0],s=i[t];n=f[t].getPixelForValue(s)+p-e}else n="center"===i?(u.bottom+u.top)/2+p-e:sp(t,i,e);a=d-l}else{if(J(i)){let t=Object.keys(i)[0],s=i[t];r=f[t].getPixelForValue(s)-m+e}else r="center"===i?(u.left+u.right)/2-m+e:sp(t,i,e);n=tK(s,h,o),g="left"===i?-tk:tk}return{titleX:r,titleY:n,maxWidth:a,rotation:g}}(this,l,i,o);ey(e,s.text,0,0,r,{color:s.color,maxWidth:c,rotation:u,textAlign:(t=tG(o),(a&&"right"!==i||!a&&"right"===i)&&(t=sg(t)),t),textBaseline:"middle",translation:[h,d]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){let t=this.options,e=t.ticks&&t.ticks.z||0,i=te(t.grid&&t.grid.z,-1),s=te(t.border&&t.border.z,0);return this._isVisible()&&this.draw===sy.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){let e,i;let s=this.chart.getSortedVisibleDatasetMetas(),a=this.axis+"AxisID",r=[];for(e=0,i=s.length;e<i;++e){let i=s[e];i[a]!==this.id||t&&i.type!==t||r.push(i)}return r}_resolveTickFontOptions(t){return eO(this.options.ticks.setContext(this.getContext(t)).font)}_maxDigits(){let t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class sv{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){var e;let i;let s=Object.getPrototypeOf(t);"id"in(e=s)&&"defaults"in e&&(i=this.register(s));let a=this.items,r=t.id,n=this.scope+"."+r;if(!r)throw Error("class does not have id: "+t);return r in a||(a[r]=t,function(t,e,i){let s=td(Object.create(null),[i?eh.get(i):{},eh.get(e),t.defaults]);eh.set(e,s),t.defaultRoutes&&function(t,e){Object.keys(e).forEach(i=>{let s=i.split("."),a=s.pop(),r=[t].concat(s).join("."),n=e[i].split("."),o=n.pop(),l=n.join(".");eh.route(r,a,l,o)})}(e,t.defaultRoutes),t.descriptors&&eh.describe(e,t.descriptors)}(t,n,i),this.override&&eh.override(t.id,t.overrides)),n}get(t){return this.items[t]}unregister(t){let e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in eh[s]&&(delete eh[s][i],this.override&&delete ea[i])}}class sM{constructor(){this.controllers=new sv(iS,"datasets",!0),this.elements=new sv(su,"elements"),this.plugins=new sv(Object,"plugins"),this.scales=new sv(sy,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(e=>{let s=i||this._getRegistryForType(e);i||s.isForType(e)||s===this.plugins&&e.id?this._exec(t,s,e):tr(e,e=>{let s=i||this._getRegistryForType(e);this._exec(t,s,e)})})}_exec(t,e,i){let s=tp(t);ta(i["before"+s],[],i),e[t](i),ta(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){let i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){let s=e.get(t);if(void 0===s)throw Error('"'+t+'" is not a registered '+i+".");return s}}var sw=new sM;class sk{constructor(){this._init=[]}notify(t,e,i,s){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));let a=s?this._descriptors(t).filter(s):this._descriptors(t),r=this._notify(a,t,e,i);return"afterDestroy"===e&&(this._notify(a,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,i,s){for(let a of(s=s||{},t)){let t=a.plugin;if(!1===ta(t[i],[e,s,a.options],t)&&s.cancelable)return!1}return!0}invalidate(){G(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;let e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){let i=t&&t.config,s=te(i.options&&i.options.plugins,{}),a=function(t){let e={},i=[],s=Object.keys(sw.plugins.items);for(let t=0;t<s.length;t++)i.push(sw.getPlugin(s[t]));let a=t.plugins||[];for(let t=0;t<a.length;t++){let s=a[t];-1===i.indexOf(s)&&(i.push(s),e[s.id]=!0)}return{plugins:i,localIds:e}}(i);return!1!==s||e?function(t,{plugins:e,localIds:i},s,a){let r=[],n=t.getContext();for(let l of e){var o;let e=l.id,h=(o=s[e],a||!1!==o?!0===o?{}:o:null);null!==h&&r.push({plugin:l,options:function(t,{plugin:e,local:i},s,a){let r=t.pluginScopeKeys(e),n=t.getOptionScopes(s,r);return i&&e.defaults&&n.push(e.defaults),t.createResolver(n,a,[""],{scriptable:!1,indexable:!1,allKeys:!0})}(t.config,{plugin:l,local:i[e]},h,n)})}return r}(t,a,s,e):[]}_notifyStateChanges(t){let e=this._oldCache||[],i=this._cache,s=(t,e)=>t.filter(t=>!e.some(e=>t.plugin.id===e.plugin.id));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}}function sP(t,e){let i=eh.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function sC(t){if("x"===t||"y"===t||"r"===t)return t}function sS(t,...e){if(sC(t))return t;for(let s of e){var i;let e=s.axis||("top"===(i=s.position)||"bottom"===i?"x":"left"===i||"right"===i?"y":void 0)||t.length>1&&sC(t[0].toLowerCase());if(e)return e}throw Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function sD(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function sO(t){let e=t.options||(t.options={});e.plugins=te(e.plugins,{}),e.scales=function(t,e){let i=ea[t.type]||{scales:{}},s=e.scales||{},a=sP(t.type,e),r=Object.create(null);return Object.keys(s).forEach(e=>{let n=s[e];if(!J(n))return console.error(`Invalid scale configuration for scale: ${e}`);if(n._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${e}`);let o=sS(e,n,function(t,e){if(e.data&&e.data.datasets){let i=e.data.datasets.filter(e=>e.xAxisID===t||e.yAxisID===t);if(i.length)return sD(t,"x",i[0])||sD(t,"y",i[0])}return{}}(e,t),eh.scales[n.type]),l=o===a?"_index_":"_value_",h=i.scales||{};r[e]=tc(Object.create(null),[{axis:o},n,h[o],h[l]])}),t.data.datasets.forEach(i=>{let a=i.type||t.type,n=i.indexAxis||sP(a,e),o=(ea[a]||{}).scales||{};Object.keys(o).forEach(t=>{let e;let a=(e=t,"_index_"===t?e=n:"_value_"===t&&(e="x"===n?"y":"x"),e),l=i[a+"AxisID"]||a;r[l]=r[l]||Object.create(null),tc(r[l],[{axis:a},s[l],o[t]])})}),Object.keys(r).forEach(t=>{let e=r[t];tc(e,[eh.scales[e.type],eh.scale])}),r}(t,e)}function sA(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}let sT=new Map,sL=new Set;function sE(t,e){let i=sT.get(t);return i||(i=e(),sT.set(t,i),sL.add(i)),i}let sR=(t,e,i)=>{let s=tg(e,i);void 0!==s&&t.add(s)};class sF{constructor(t){this._config=function(t){return(t=t||{}).data=sA(t.data),sO(t),t}(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=sA(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){let t=this._config;this.clearCache(),sO(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return sE(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return sE(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return sE(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){let e=t.id,i=this.type;return sE(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){let i=this._scopeCache,s=i.get(t);return(!s||e)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){let{options:s,type:a}=this,r=this._cachedScopes(t,i),n=r.get(e);if(n)return n;let o=new Set;e.forEach(e=>{t&&(o.add(t),e.forEach(e=>sR(o,t,e))),e.forEach(t=>sR(o,s,t)),e.forEach(t=>sR(o,ea[a]||{},t)),e.forEach(t=>sR(o,eh,t)),e.forEach(t=>sR(o,er,t))});let l=Array.from(o);return 0===l.length&&l.push(Object.create(null)),sL.has(e)&&r.set(e,l),l}chartOptionScopes(){let{options:t,type:e}=this;return[t,ea[e]||{},eh.datasets[e]||{},{type:e},eh,er]}resolveNamedOptions(t,e,i,s=[""]){let a={$shared:!0},{resolver:r,subPrefixes:n}=sj(this._resolverCache,t,s),o=r;if(function(t,e){let{isScriptable:i,isIndexable:s}=eR(t);for(let a of e){let e=i(a),r=s(a),n=(r||e)&&t[a];if(e&&(tx(n)||sI(n))||r&&K(n))return!0}return!1}(r,e)){a.$shared=!1,i=tx(i)?i():i;let e=this.createResolver(t,i,n);o=eE(r,i,e)}for(let t of e)a[t]=o[t];return a}createResolver(t,e,i=[""],s){let{resolver:a}=sj(this._resolverCache,t,i);return J(e)?eE(a,e,void 0,s):a}}function sj(t,e,i){let s=t.get(e);s||(s=new Map,t.set(e,s));let a=i.join(),r=s.get(a);return r||(r={resolver:eL(e,i),subPrefixes:i.filter(t=>!t.toLowerCase().includes("hover"))},s.set(a,r)),r}let sI=t=>J(t)&&Object.getOwnPropertyNames(t).some(e=>tx(t[e])),sN=["top","bottom","left","right","chartArea"];function sz(t,e){return"top"===t||"bottom"===t||-1===sN.indexOf(t)&&"x"===e}function sV(t,e){return function(i,s){return i[t]===s[t]?i[e]-s[e]:i[t]-s[t]}}function sB(t){let e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),ta(i&&i.onComplete,[t],e)}function sW(t){let e=t.chart,i=e.options.animation;ta(i&&i.onProgress,[t],e)}function sH(t){return eX()&&"string"==typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}let s$={},sU=t=>{let e=sH(t);return Object.values(s$).filter(t=>t.canvas===e).pop()};function sY(t,e,i){return t.options.clip?t[i]:e[i]}class sq{static defaults=eh;static instances=s$;static overrides=ea;static registry=sw;static version="4.4.8";static getChart=sU;static register(...t){sw.add(...t),sX()}static unregister(...t){sw.remove(...t),sX()}constructor(t,e){let i=this.config=new sF(e),s=sH(t),a=sU(s);if(a)throw Error("Canvas is already in use. Chart with ID '"+a.id+"' must be destroyed before the canvas with ID '"+a.canvas.id+"' can be reused.");let r=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||(!eX()||"undefined"!=typeof OffscreenCanvas&&s instanceof OffscreenCanvas?i6:sc)),this.platform.updateConfig(i);let n=this.platform.acquireContext(s,r.aspectRatio),o=n&&n.canvas,l=o&&o.height,h=o&&o.width;if(this.id=Z(),this.ctx=n,this.canvas=o,this.width=h,this.height=l,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new sk,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function(t,e){let i;return function(...s){return e?(clearTimeout(i),i=setTimeout(t,e,s)):t.apply(this,s),e}}(t=>this.update(t),r.resizeDelay||0),this._dataChanges=[],s$[this.id]=this,!n||!o){console.error("Failed to create chart: can't acquire context from the given item");return}id.listen(this,"complete",sB),id.listen(this,"progress",sW),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:a}=this;return G(t)?e&&a?a:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return sw}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():e5(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return eu(this.canvas,this.ctx),this}stop(){return id.stop(this),this}resize(t,e){id.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){let i=this.options,s=this.canvas,a=i.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(s,t,e,a),n=i.devicePixelRatio||this.platform.getDevicePixelRatio(),o=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,e5(this,n,!0)&&(this.notifyPlugins("resize",{size:r}),ta(i.onResize,[this,r],this),this.attached&&this._doResize(o)&&this.render())}ensureScalesHaveIDs(){tr(this.options.scales||{},(t,e)=>{t.id=e})}buildOrUpdateScales(){let t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce((t,e)=>(t[e]=!1,t),{}),a=[];e&&(a=a.concat(Object.keys(e).map(t=>{let i=e[t],s=sS(t,i),a="r"===s,r="x"===s;return{options:i,dposition:a?"chartArea":r?"bottom":"left",dtype:a?"radialLinear":r?"category":"linear"}}))),tr(a,e=>{let a=e.options,r=a.id,n=sS(r,a),o=te(a.type,e.dtype);(void 0===a.position||sz(a.position,n)!==sz(e.dposition))&&(a.position=e.dposition),s[r]=!0;let l=null;r in i&&i[r].type===o?l=i[r]:i[(l=new(sw.getScale(o))({id:r,type:o,ctx:this.ctx,chart:this})).id]=l,l.init(a,t)}),tr(s,(t,e)=>{t||delete i[e]}),tr(i,t=>{i8.configure(this,t,t.options),i8.addBox(this,t)})}_updateMetasets(){let t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((t,e)=>t.index-e.index),i>e){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(sV("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((t,i)=>{0===e.filter(e=>e===t._dataset).length&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){let t,e;let i=[],s=this.data.datasets;for(this._removeUnreferencedMetasets(),t=0,e=s.length;t<e;t++){let e=s[t],a=this.getDatasetMeta(t),r=e.type||this.config.type;if(a.type&&a.type!==r&&(this._destroyDatasetMeta(t),a=this.getDatasetMeta(t)),a.type=r,a.indexAxis=e.indexAxis||sP(r,this.options),a.order=e.order||0,a.index=t,a.label=""+e.label,a.visible=this.isDatasetVisible(t),a.controller)a.controller.updateIndex(t),a.controller.linkScales();else{let e=sw.getController(r),{datasetElementType:s,dataElementType:n}=eh.datasets[r];Object.assign(e,{dataElementType:sw.getElement(n),datasetElementType:s&&sw.getElement(s)}),a.controller=new e(this,t),i.push(a.controller)}}return this._updateMetasets(),i}_resetElements(){tr(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let e=this.config;e.update();let i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;let a=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let t=0,e=this.data.datasets.length;t<e;t++){let{controller:e}=this.getDatasetMeta(t),i=!s&&-1===a.indexOf(e);e.buildOrUpdateElements(i),r=Math.max(+e.getMaxOverflow(),r)}r=this._minPadding=i.layout.autoPadding?r:0,this._updateLayout(r),s||tr(a,t=>{t.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(sV("z","_idx"));let{_active:n,_lastEvent:o}=this;o?this._eventHandler(o,!0):n.length&&this._updateHoverStyles(n,n,!0),this.render()}_updateScales(){tr(this.scales,t=>{i8.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options;tb(new Set(Object.keys(this._listeners)),new Set(t.events))&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this;for(let{method:e,start:i,count:s}of this._getUniformDataChanges()||[])!function(t,e,i){for(let s of Object.keys(t)){let a=+s;if(a>=e){let r=t[s];delete t[s],(i>0||a>e)&&(t[a+i]=r)}}}(t,i,"_removeElements"===e?-s:s)}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let e=this.data.datasets.length,i=e=>new Set(t.filter(t=>t[0]===e).map((t,e)=>e+","+t.splice(1).join(","))),s=i(0);for(let t=1;t<e;t++)if(!tb(s,i(t)))return;return Array.from(s).map(t=>t.split(",")).map(t=>({method:t[1],start:+t[2],count:+t[3]}))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;i8.update(this,this.width,this.height,t);let e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],tr(this.boxes,t=>{(!i||"chartArea"!==t.position)&&(t.configure&&t.configure(),this._layers.push(...t._layers()))},this),this._layers.forEach((t,e)=>{t._idx=e}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,tx(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){let i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",s)&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(id.has(this)?this.attached&&!id.running(this)&&id.start(this):(this.draw(),sB({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:t,height:e}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(t,e)}if(this.clear(),this.width<=0||this.height<=0||!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;let e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let e,i;let s=this._sortedMetasets,a=[];for(e=0,i=s.length;e<i;++e){let i=s[e];(!t||i.visible)&&a.push(i)}return a}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;let t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let e=this.ctx,i=t._clip,s=!i.disabled,a=function(t,e){let{xScale:i,yScale:s}=t;return i&&s?{left:sY(i,e,"left"),right:sY(i,e,"right"),top:sY(s,e,"top"),bottom:sY(s,e,"bottom")}:e}(t,this.chartArea),r={meta:t,index:t.index,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetDraw",r)&&(s&&em(e,{left:!1===i.left?0:a.left-i.left,right:!1===i.right?this.width:a.right+i.right,top:!1===i.top?0:a.top-i.top,bottom:!1===i.bottom?this.height:a.bottom+i.bottom}),t.controller.draw(),s&&ex(e),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(t){return ep(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){let a=iZ.modes[e];return"function"==typeof a?a(this,t,i,s):[]}getDatasetMeta(t){let e=this.data.datasets[t],i=this._metasets,s=i.filter(t=>t&&t._dataset===e).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=eT(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let e=this.data.datasets[t];if(!e)return!1;let i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){let s=i?"show":"hide",a=this.getDatasetMeta(t),r=a.controller._resolveAnimations(void 0,s);tm(e)?(a.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),r.update(a,{visible:i}),this.update(e=>e.datasetIndex===t?s:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){let e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),id.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),eu(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete s$[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},s=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};tr(this.options.events,t=>i(t,s))}bindResponsiveEvents(){let t;this._responsiveListeners||(this._responsiveListeners={});let e=this._responsiveListeners,i=this.platform,s=(t,s)=>{i.addEventListener(this,t,s),e[t]=s},a=(t,s)=>{e[t]&&(i.removeEventListener(this,t,s),delete e[t])},r=(t,e)=>{this.canvas&&this.resize(t,e)},n=()=>{a("attach",n),this.attached=!0,this.resize(),s("resize",r),s("detach",t)};t=()=>{this.attached=!1,a("resize",r),this._stop(),this._resize(0,0),s("attach",n)},i.isAttached(this.canvas)?n():t()}unbindEvents(){tr(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},tr(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){let s,a,r;let n=i?"set":"remove";for("dataset"===e&&this.getDatasetMeta(t[0].datasetIndex).controller["_"+n+"DatasetHoverStyle"](),a=0,r=t.length;a<r;++a){let e=(s=t[a])&&this.getDatasetMeta(s.datasetIndex).controller;e&&e[n+"HoverStyle"](s.element,s.datasetIndex,s.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let e=this._active||[],i=t.map(({datasetIndex:t,index:e})=>{let i=this.getDatasetMeta(t);if(!i)throw Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[e],index:e}});tn(i,e)||(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return 1===this._plugins._cache.filter(e=>e.plugin.id===t).length}_updateHoverStyles(t,e,i){let s=this.options.hover,a=(t,e)=>t.filter(t=>!e.some(e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)),r=a(e,t),n=i?t:a(t,e);r.length&&this.updateHoverStyle(r,s.mode,!1),n.length&&s.mode&&this.updateHoverStyle(n,s.mode,!0)}_eventHandler(t,e){let i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=e=>(e.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",i,s))return;let a=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(a||i.changed)&&this.render(),this}_handleEvent(t,e,i){var s;let{_active:a=[],options:r}=this,n=this._getActiveElements(t,a,i,e),o="mouseup"===t.type||"click"===t.type||"contextmenu"===t.type,l=(s=this._lastEvent,i&&"mouseout"!==t.type?o?s:t:null);i&&(this._lastEvent=null,ta(r.onHover,[t,n,this],this),o&&ta(r.onClick,[t,n,this],this));let h=!tn(n,a);return(h||e)&&(this._active=n,this._updateHoverStyles(n,a,e)),this._lastEvent=l,h}_getActiveElements(t,e,i,s){if("mouseout"===t.type)return[];if(!i)return e;let a=this.options.hover;return this.getElementsAtEventForMode(t,a.mode,a,s)}}function sX(){return tr(sq.instances,t=>t._plugins.invalidate())}function sZ(t,e,i,s){return{x:i+t*Math.cos(e),y:s+t*Math.sin(e)}}function sG(t,e,i,s,a,r){let{x:n,y:o,startAngle:l,pixelMargin:h,innerRadius:d}=e,c=Math.max(e.outerRadius+s+i-h,0),u=d>0?d+s+i+h:0,f=0,g=a-l;if(s){let t=c>0?c-s:0,e=((d>0?d-s:0)+t)/2;f=(g-(0!==e?g*e/(e+s):g))/2}let p=Math.max(.001,g*c-i/t_)/c,m=(g-p)/2,x=l+m+f,b=a-m-f,{outerStart:_,outerEnd:y,innerStart:v,innerEnd:M}=function(t,e,i,s){let a=eP(t.options.borderRadius,["outerStart","outerEnd","innerStart","innerEnd"]),r=(i-e)/2,n=Math.min(r,s*e/2),o=t=>{let e=(i-Math.min(r,t))*s/2;return tV(t,0,Math.min(r,e))};return{outerStart:o(a.outerStart),outerEnd:o(a.outerEnd),innerStart:tV(a.innerStart,0,n),innerEnd:tV(a.innerEnd,0,n)}}(e,u,c,b-x),w=c-_,k=c-y,P=x+_/w,C=b-y/k,S=u+v,D=u+M,O=x+v/S,A=b-M/D;if(t.beginPath(),r){let e=(P+C)/2;if(t.arc(n,o,c,P,e),t.arc(n,o,c,e,C),y>0){let e=sZ(k,C,n,o);t.arc(e.x,e.y,y,C,b+tk)}let i=sZ(D,b,n,o);if(t.lineTo(i.x,i.y),M>0){let e=sZ(D,A,n,o);t.arc(e.x,e.y,M,b+tk,A+Math.PI)}let s=(b-M/u+(x+v/u))/2;if(t.arc(n,o,u,b-M/u,s,!0),t.arc(n,o,u,s,x+v/u,!0),v>0){let e=sZ(S,O,n,o);t.arc(e.x,e.y,v,O+Math.PI,x-tk)}let a=sZ(w,x,n,o);if(t.lineTo(a.x,a.y),_>0){let e=sZ(w,P,n,o);t.arc(e.x,e.y,_,x-tk,P)}}else{t.moveTo(n,o);let e=Math.cos(P)*c+n,i=Math.sin(P)*c+o;t.lineTo(e,i);let s=Math.cos(C)*c+n,a=Math.sin(C)*c+o;t.lineTo(s,a)}t.closePath()}class sK extends su{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){let{angle:s,distance:a}=tF(this.getProps(["x","y"],i),{x:t,y:e}),{startAngle:r,endAngle:n,innerRadius:o,outerRadius:l,circumference:h}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),d=(this.options.spacing+this.options.borderWidth)/2,c=te(h,n-r),u=tz(s,r,n)&&r!==n,f=c>=ty||u,g=tB(a,o+d,l+d);return f&&g}getCenterPoint(t){let{x:e,y:i,startAngle:s,endAngle:a,innerRadius:r,outerRadius:n}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:o,spacing:l}=this.options,h=(s+a)/2,d=(r+n+l+o)/2;return{x:e+Math.cos(h)*d,y:i+Math.sin(h)*d}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){let{options:e,circumference:i}=this,s=(e.offset||0)/4,a=(e.spacing||0)/2,r=e.circular;if(this.pixelMargin=.33*("inner"===e.borderAlign),this.fullCircles=i>ty?Math.floor(i/ty):0,0===i||this.innerRadius<0||this.outerRadius<0)return;t.save();let n=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(n)*s,Math.sin(n)*s);let o=s*(1-Math.sin(Math.min(t_,i||0)));t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,function(t,e,i,s,a){let{fullCircles:r,startAngle:n,circumference:o}=e,l=e.endAngle;if(r){sG(t,e,i,s,l,a);for(let e=0;e<r;++e)t.fill();isNaN(o)||(l=n+(o%ty||ty))}sG(t,e,i,s,l,a),t.fill()}(t,this,o,a,r),function(t,e,i,s,a){let{fullCircles:r,startAngle:n,circumference:o,options:l}=e,{borderWidth:h,borderJoinStyle:d,borderDash:c,borderDashOffset:u}=l,f="inner"===l.borderAlign;if(!h)return;t.setLineDash(c||[]),t.lineDashOffset=u,f?(t.lineWidth=2*h,t.lineJoin=d||"round"):(t.lineWidth=h,t.lineJoin=d||"bevel");let g=e.endAngle;if(r){sG(t,e,i,s,g,a);for(let e=0;e<r;++e)t.stroke();isNaN(o)||(g=n+(o%ty||ty))}f&&function(t,e,i){let{startAngle:s,pixelMargin:a,x:r,y:n,outerRadius:o,innerRadius:l}=e,h=a/o;t.beginPath(),t.arc(r,n,o,s-h,i+h),l>a?(h=a/l,t.arc(r,n,l,i+h,s-h,!0)):t.arc(r,n,a,i+tk,s-tk),t.closePath(),t.clip()}(t,e,g),r||(sG(t,e,i,s,g,a),t.stroke())}(t,this,o,a,r),t.restore()}}function sJ(t,e,i=e){t.lineCap=te(i.borderCapStyle,e.borderCapStyle),t.setLineDash(te(i.borderDash,e.borderDash)),t.lineDashOffset=te(i.borderDashOffset,e.borderDashOffset),t.lineJoin=te(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=te(i.borderWidth,e.borderWidth),t.strokeStyle=te(i.borderColor,e.borderColor)}function sQ(t,e,i){t.lineTo(i.x,i.y)}function s0(t,e,i={}){let s=t.length,{start:a=0,end:r=s-1}=i,{start:n,end:o}=e,l=Math.max(a,n),h=Math.min(r,o);return{count:s,start:l,loop:e.loop,ilen:h<l&&!(a<n&&r<n||a>o&&r>o)?s+h-l:h-l}}function s1(t,e,i,s){let a,r,n;let{points:o,options:l}=e,{count:h,start:d,loop:c,ilen:u}=s0(o,i,s),f=l.stepped?eb:l.tension||"monotone"===l.cubicInterpolationMode?e_:sQ,{move:g=!0,reverse:p}=s||{};for(a=0;a<=u;++a)!(r=o[(d+(p?u-a:a))%h]).skip&&(g?(t.moveTo(r.x,r.y),g=!1):f(t,n,r,p,l.stepped),n=r);return c&&f(t,n,r=o[(d+(p?u:0))%h],p,l.stepped),!!c}function s2(t,e,i,s){let a,r,n,o,l,h;let d=e.points,{count:c,start:u,ilen:f}=s0(d,i,s),{move:g=!0,reverse:p}=s||{},m=0,x=0,b=t=>(u+(p?f-t:t))%c,_=()=>{o!==l&&(t.lineTo(m,l),t.lineTo(m,o),t.lineTo(m,h))};for(g&&(r=d[b(0)],t.moveTo(r.x,r.y)),a=0;a<=f;++a){if((r=d[b(a)]).skip)continue;let e=r.x,i=r.y,s=0|e;s===n?(i<o?o=i:i>l&&(l=i),m=(x*m+e)/++x):(_(),t.lineTo(e,i),n=s,x=0,o=l=i),h=i}_()}function s5(t){let e=t.options,i=e.borderDash&&e.borderDash.length;return t._decimated||t._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||i?s1:s2}let s4="function"==typeof Path2D;class s8 extends su{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){let i=this.options;if((i.tension||"monotone"===i.cubicInterpolationMode)&&!i.stepped&&!this._pointsUpdated){let s=i.spanGaps?this._loop:this._fullLoop;(function(t,e,i,s,a){let r,n,o,l;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){let i,s,a;let r=eY(e),n=t.length,o=Array(n).fill(0),l=Array(n),h=eU(t,0);for(i=0;i<n;++i)if(s=a,a=h,h=eU(t,i+1),a){if(h){let t=h[e]-a[e];o[i]=0!==t?(h[r]-a[r])/t:0}l[i]=s?h?tD(o[i-1])!==tD(o[i])?0:(o[i-1]+o[i])/2:o[i-1]:o[i]}(function(t,e,i){let s,a,r,n,o;let l=t.length,h=eU(t,0);for(let d=0;d<l-1;++d){if(o=h,h=eU(t,d+1),o&&h){if(tO(e[d],0,e$)){i[d]=i[d+1]=0;continue}!((n=Math.pow(s=i[d]/e[d],2)+Math.pow(a=i[d+1]/e[d],2))<=9)&&(r=3/Math.sqrt(n),i[d]=s*r*e[d],i[d+1]=a*r*e[d])}}})(t,o,l),function(t,e,i="x"){let s,a,r;let n=eY(i),o=t.length,l=eU(t,0);for(let h=0;h<o;++h){if(a=r,r=l,l=eU(t,h+1),!r)continue;let o=r[i],d=r[n];a&&(s=(o-a[i])/3,r[`cp1${i}`]=o-s,r[`cp1${n}`]=d-s*e[h]),l&&(s=(l[i]-o)/3,r[`cp2${i}`]=o+s,r[`cp2${n}`]=d+s*e[h])}}(t,l,e)}(t,a);else{let i=s?t[t.length-1]:t[0];for(r=0,n=t.length;r<n;++r)l=function(t,e,i,s){let a=t.skip?e:t,r=i.skip?e:i,n=tj(e,a),o=tj(r,e),l=n/(n+o),h=o/(n+o);l=isNaN(l)?0:l,h=isNaN(h)?0:h;let d=s*l,c=s*h;return{previous:{x:e.x-d*(r.x-a.x),y:e.y-d*(r.y-a.y)},next:{x:e.x+c*(r.x-a.x),y:e.y+c*(r.y-a.y)}}}(i,o=t[r],t[Math.min(r+1,n-+!s)%n],e.tension),o.cp1x=l.previous.x,o.cp1y=l.previous.y,o.cp2x=l.next.x,o.cp2y=l.next.y,i=o}e.capBezierPoints&&function(t,e){let i,s,a,r,n;let o=ep(t[0],e);for(i=0,s=t.length;i<s;++i)n=r,r=o,o=i<s-1&&ep(t[i+1],e),r&&(a=t[i],n&&(a.cp1x=eq(a.cp1x,e.left,e.right),a.cp1y=eq(a.cp1y,e.top,e.bottom)),o&&(a.cp2x=eq(a.cp2x,e.left,e.right),a.cp2y=eq(a.cp2y,e.top,e.bottom)))}(t,i)})(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function(t,e){let i=t.points,s=t.options.spanGaps,a=i.length;if(!a)return[];let r=!!t._loop,{start:n,end:o}=function(t,e,i,s){let a=0,r=e-1;if(i&&!s)for(;a<e&&!t[a].skip;)a++;for(;a<e&&t[a].skip;)a++;for(a%=e,i&&(r+=a);r>a&&t[r%e].skip;)r--;return{start:a,end:r%=e}}(i,a,r,s);if(!0===s)return io(t,[{start:n,end:o,loop:r}],i,e);let l=o<n?o+a:o,h=!!t._fullLoop&&0===n&&o===a-1;return io(t,function(t,e,i,s){let a;let r=t.length,n=[],o=e,l=t[e];for(a=e+1;a<=i;++a){let i=t[a%r];i.skip||i.stop?l.skip||(s=!1,n.push({start:e%r,end:(a-1)%r,loop:s}),e=o=i.stop?a:null):(o=a,l.skip&&(e=a)),l=i}return null!==o&&n.push({start:e%r,end:o%r,loop:s}),n}(i,n,l,h),i,e)}(this,this.options.segment))}first(){let t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){let t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){let i,s;let a=this.options,r=t[e],n=this.points,o=ir(this,{property:e,start:r,end:r});if(!o.length)return;let l=[],h=a.stepped?e6:a.tension||"monotone"===a.cubicInterpolationMode?e9:e3;for(i=0,s=o.length;i<s;++i){let{start:s,end:d}=o[i],c=n[s],u=n[d];if(c===u){l.push(c);continue}let f=Math.abs((r-c[e])/(u[e]-c[e])),g=h(c,u,f,a.stepped);g[e]=t[e],l.push(g)}return 1===l.length?l[0]:l}pathSegment(t,e,i){return s5(this)(t,this,e,i)}path(t,e,i){let s=this.segments,a=s5(this),r=this._loop;for(let n of(e=e||0,i=i||this.points.length-e,s))r&=a(t,this,n,{start:e,end:e+i-1});return!!r}draw(t,e,i,s){let a=this.options||{};(this.points||[]).length&&a.borderWidth&&(t.save(),function(t,e,i,s){if(s4&&!e.options.segment){let a;(a=e._path)||(a=e._path=new Path2D,e.path(a,i,s)&&a.closePath()),sJ(t,e.options),t.stroke(a)}else!function(t,e,i,s){let{segments:a,options:r}=e,n=s5(e);for(let o of a)sJ(t,r,o.style),t.beginPath(),n(t,e,o,{start:i,end:i+s-1})&&t.closePath(),t.stroke()}(t,e,i,s)}(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function s3(t,e,i,s){let a=t.options,{[i]:r}=t.getProps([i],s);return Math.abs(e-r)<a.radius+a.hitRadius}class s6 extends su{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){let s=this.options,{x:a,y:r}=this.getProps(["x","y"],i);return Math.pow(t-a,2)+Math.pow(e-r,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return s3(this,t,"x",e)}inYRange(t,e){return s3(this,t,"y",e)}getCenterPoint(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){let e=(t=t||this.options||{}).radius||0,i=(e=Math.max(e,e&&t.hoverRadius||0))&&t.borderWidth||0;return(e+i)*2}draw(t,e){let i=this.options;!this.skip&&!(i.radius<.1)&&ep(this,e,this.size(i)/2)&&(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,ef(t,i,this.x,this.y))}getRange(){let t=this.options||{};return t.radius+t.hitRadius}}function s9(t,e){let i,s,a,r,n;let{x:o,y:l,base:h,width:d,height:c}=t.getProps(["x","y","base","width","height"],e);return t.horizontal?(n=c/2,i=Math.min(o,h),s=Math.max(o,h),a=l-n,r=l+n):(i=o-(n=d/2),s=o+n,a=Math.min(l,h),r=Math.max(l,h)),{left:i,top:a,right:s,bottom:r}}function s7(t,e,i,s){return t?0:tV(e,i,s)}function at(t,e,i,s){let a=null===e,r=null===i,n=t&&!(a&&r)&&s9(t,s);return n&&(a||tB(e,n.left,n.right))&&(r||tB(i,n.top,n.bottom))}function ae(t,e){t.rect(e.x,e.y,e.w,e.h)}function ai(t,e,i={}){let s=t.x!==i.x?-e:0,a=t.y!==i.y?-e:0,r=(t.x+t.w!==i.x+i.w?e:0)-s,n=(t.y+t.h!==i.y+i.h?e:0)-a;return{x:t.x+s,y:t.y+a,w:t.w+r,h:t.h+n,radius:t.radius}}class as extends su{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){var e;let{inflateAmount:i,options:{borderColor:s,backgroundColor:a}}=this,{inner:r,outer:n}=function(t){let e=s9(t),i=e.right-e.left,s=e.bottom-e.top,a=function(t,e,i){let s=t.options.borderWidth,a=t.borderSkipped,r=eC(s);return{t:s7(a.top,r.top,0,i),r:s7(a.right,r.right,0,e),b:s7(a.bottom,r.bottom,0,i),l:s7(a.left,r.left,0,e)}}(t,i/2,s/2),r=function(t,e,i){let{enableBorderRadius:s}=t.getProps(["enableBorderRadius"]),a=t.options.borderRadius,r=eS(a),n=Math.min(e,i),o=t.borderSkipped,l=s||J(a);return{topLeft:s7(!l||o.top||o.left,r.topLeft,0,n),topRight:s7(!l||o.top||o.right,r.topRight,0,n),bottomLeft:s7(!l||o.bottom||o.left,r.bottomLeft,0,n),bottomRight:s7(!l||o.bottom||o.right,r.bottomRight,0,n)}}(t,i/2,s/2);return{outer:{x:e.left,y:e.top,w:i,h:s,radius:r},inner:{x:e.left+a.l,y:e.top+a.t,w:i-a.l-a.r,h:s-a.t-a.b,radius:{topLeft:Math.max(0,r.topLeft-Math.max(a.t,a.l)),topRight:Math.max(0,r.topRight-Math.max(a.t,a.r)),bottomLeft:Math.max(0,r.bottomLeft-Math.max(a.b,a.l)),bottomRight:Math.max(0,r.bottomRight-Math.max(a.b,a.r))}}}}(this),o=(e=n.radius).topLeft||e.topRight||e.bottomLeft||e.bottomRight?ev:ae;t.save(),(n.w!==r.w||n.h!==r.h)&&(t.beginPath(),o(t,ai(n,i,r)),t.clip(),o(t,ai(r,-i,n)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),o(t,ai(r,i)),t.fillStyle=a,t.fill(),t.restore()}inRange(t,e,i){return at(this,t,e,i)}inXRange(t,e){return at(this,t,null,e)}inYRange(t,e){return at(this,null,t,e)}getCenterPoint(t){let{x:e,y:i,base:s,horizontal:a}=this.getProps(["x","y","base","horizontal"],t);return{x:a?(e+s)/2:e,y:a?i:(i+s)/2}}getRange(t){return"x"===t?this.width/2:this.height/2}}var aa=Object.freeze({__proto__:null,ArcElement:sK,BarElement:as,LineElement:s8,PointElement:s6});let ar=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],an=ar.map(t=>t.replace("rgb(","rgba(").replace(")",", 0.5)"));function ao(t){return ar[t%ar.length]}function al(t){return an[t%an.length]}function ah(t){let e;for(e in t)if(t[e].borderColor||t[e].backgroundColor)return!0;return!1}function ad(t){if(t._decimated){let e=t._data;delete t._decimated,delete t._data,Object.defineProperty(t,"data",{configurable:!0,enumerable:!0,writable:!0,value:e})}}function ac(t){t.data.datasets.forEach(t=>{ad(t)})}function au(t,e,i,s){if(s)return;let a=e[t],r=i[t];return"angle"===t&&(a=tN(a),r=tN(r)),{property:t,start:a,end:r}}function af(t,e,i){for(;e>t;e--){let t=i[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function ag(t,e,i,s){return t&&e?s(t[i],e[i]):t?t[i]:e?e[i]:0}function ap(t,e){let i=[],s=!1;return K(t)?(s=!0,i=t):i=function(t,e){let{x:i=null,y:s=null}=t||{},a=e.points,r=[];return e.segments.forEach(({start:t,end:e})=>{e=af(t,e,a);let n=a[t],o=a[e];null!==s?(r.push({x:n.x,y:s}),r.push({x:o.x,y:s})):null!==i&&(r.push({x:i,y:n.y}),r.push({x:i,y:o.y}))}),r}(t,e),i.length?new s8({points:i,options:{tension:0},_loop:s,_fullLoop:s}):null}function am(t){return t&&!1!==t.fill}class ax{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){let{x:s,y:a,radius:r}=this;return e=e||{start:0,end:ty},t.arc(s,a,r,e.end,e.start,!0),!i.bounds}interpolate(t){let{x:e,y:i,radius:s}=this,a=t.angle;return{x:e+Math.cos(a)*s,y:i+Math.sin(a)*s,angle:a}}}function ab(t,e,i){let s=function(t){var e;let{chart:i,fill:s,line:a}=t;if(Q(s))return function(t,e){let i=t.getDatasetMeta(e);return i&&t.isDatasetVisible(e)?i.dataset:null}(i,s);if("stack"===s)return function(t){let{scale:e,index:i,line:s}=t,a=[],r=s.segments,n=s.points,o=function(t,e){let i=[],s=t.getMatchingVisibleMetas("line");for(let t=0;t<s.length;t++){let a=s[t];if(a.index===e)break;a.hidden||i.unshift(a.dataset)}return i}(e,i);o.push(ap({x:null,y:e.bottom},s));for(let t=0;t<r.length;t++){let e=r[t];for(let t=e.start;t<=e.end;t++)(function(t,e,i){let s=[];for(let a=0;a<i.length;a++){let{first:r,last:n,point:o}=function(t,e,i){let s=t.interpolate(e,"x");if(!s)return{};let a=s[i],r=t.segments,n=t.points,o=!1,l=!1;for(let t=0;t<r.length;t++){let e=r[t],s=n[e.start][i],h=n[e.end][i];if(tB(a,s,h)){o=a===s,l=a===h;break}}return{first:o,last:l,point:s}}(i[a],e,"x");if(o&&(!r||!n)){if(r)s.unshift(o);else if(t.push(o),!n)break}}t.push(...s)})(a,n[t],o)}return new s8({points:a,options:{}})}(t);if("shape"===s)return!0;let r=((e=t).scale||{}).getPointPositionForValue?function(t){let e;let{scale:i,fill:s}=t,a=i.options,r=i.getLabels().length,n=a.reverse?i.max:i.min,o="start"===s?n:"end"===s?i.options.reverse?i.min:i.max:J(s)?s.value:i.getBaseValue(),l=[];if(a.grid.circular){let t=i.getPointPositionForValue(0,n);return new ax({x:t.x,y:t.y,radius:i.getDistanceFromCenterForValue(o)})}for(let t=0;t<r;++t)l.push(i.getPointPositionForValue(t,o));return l}(e):function(t){let e;let{scale:i={},fill:s}=t,a=(e=null,"start"===s?e=i.bottom:"end"===s?e=i.top:J(s)?e=i.getPixelForValue(s.value):i.getBasePixel&&(e=i.getBasePixel()),e);if(Q(a)){let t=i.isHorizontal();return{x:t?a:null,y:t?null:a}}return null}(e);return r instanceof ax?r:ap(r,a)}(e),{line:a,scale:r,axis:n}=e,o=a.options,l=o.fill,h=o.backgroundColor,{above:d=h,below:c=h}=l||{};s&&a.points.length&&(em(t,i),function(t,e){let{line:i,target:s,above:a,below:r,area:n,scale:o}=e,l=i._loop?"angle":e.axis;t.save(),"x"===l&&r!==a&&(a_(t,s,n.top),ay(t,{line:i,target:s,color:a,scale:o,property:l}),t.restore(),t.save(),a_(t,s,n.bottom)),ay(t,{line:i,target:s,color:r,scale:o,property:l}),t.restore()}(t,{line:a,target:s,above:d,below:c,area:i,scale:r,axis:n}),ex(t))}function a_(t,e,i){let{segments:s,points:a}=e,r=!0,n=!1;for(let o of(t.beginPath(),s)){let{start:s,end:l}=o,h=a[s],d=a[af(s,l,a)];r?(t.moveTo(h.x,h.y),r=!1):(t.lineTo(h.x,i),t.lineTo(h.x,h.y)),(n=!!e.pathSegment(t,o,{move:n}))?t.closePath():t.lineTo(d.x,i)}t.lineTo(e.first().x,i),t.closePath(),t.clip()}function ay(t,e){let{line:i,target:s,property:a,color:r,scale:n}=e;for(let{source:e,target:o,start:l,end:h}of function(t,e,i){let s=t.segments,a=t.points,r=e.points,n=[];for(let t of s){let{start:s,end:o}=t;o=af(s,o,a);let l=au(i,a[s],a[o],t.loop);if(!e.segments){n.push({source:t,target:l,start:a[s],end:a[o]});continue}for(let s of ir(e,l)){let e=au(i,r[s.start],r[s.end],s.loop);for(let r of ia(t,a,e))n.push({source:r,target:s,start:{[i]:ag(l,e,"start",Math.max)},end:{[i]:ag(l,e,"end",Math.min)}})}}return n}(i,s,a)){let d;let{style:{backgroundColor:c=r}={}}=e,u=!0!==s;t.save(),t.fillStyle=c,function(t,e,i){let{top:s,bottom:a}=e.chart.chartArea,{property:r,start:n,end:o}=i||{};"x"===r&&(t.beginPath(),t.rect(n,s,o-n,a-s),t.clip())}(t,n,u&&au(a,l,h)),t.beginPath();let f=!!i.pathSegment(t,e);if(u){f?t.closePath():av(t,s,h,a);let e=!!s.pathSegment(t,o,{move:f,reverse:!0});(d=f&&e)||av(t,s,l,a)}t.closePath(),t.fill(d?"evenodd":"nonzero"),t.restore()}}function av(t,e,i,s){let a=e.interpolate(i,s);a&&t.lineTo(a.x,a.y)}let aM=(t,e)=>{let{boxHeight:i=e,boxWidth:s=e}=t;return t.usePointStyle&&(i=Math.min(i,e),s=t.pointStyleWidth||Math.min(s,e)),{boxWidth:s,boxHeight:i,itemHeight:Math.max(e,i)}},aw=(t,e)=>null!==t&&null!==e&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class ak extends su{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let t=this.options.labels||{},e=ta(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(e=>t.filter(e,this.chart.data))),t.sort&&(e=e.sort((e,i)=>t.sort(e,i,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){let t,e;let{options:i,ctx:s}=this;if(!i.display){this.width=this.height=0;return}let a=i.labels,r=eO(a.font),n=r.size,o=this._computeTitleHeight(),{boxWidth:l,itemHeight:h}=aM(a,n);s.font=r.string,this.isHorizontal()?(t=this.maxWidth,e=this._fitRows(o,n,l,h)+10):(e=this.maxHeight,t=this._fitCols(o,r,l,h)+10),this.width=Math.min(t,i.maxWidth||this.maxWidth),this.height=Math.min(e,i.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){let{ctx:a,maxWidth:r,options:{labels:{padding:n}}}=this,o=this.legendHitBoxes=[],l=this.lineWidths=[0],h=s+n,d=t;a.textAlign="left",a.textBaseline="middle";let c=-1,u=-h;return this.legendItems.forEach((t,f)=>{let g=i+e/2+a.measureText(t.text).width;(0===f||l[l.length-1]+g+2*n>r)&&(d+=h,l[l.length-(f>0?0:1)]=0,u+=h,c++),o[f]={left:0,top:u,row:c,width:g,height:s},l[l.length-1]+=g+n}),d}_fitCols(t,e,i,s){let{ctx:a,maxHeight:r,options:{labels:{padding:n}}}=this,o=this.legendHitBoxes=[],l=this.columnSizes=[],h=r-t,d=n,c=0,u=0,f=0,g=0;return this.legendItems.forEach((t,r)=>{var p,m,x,b,_,y,v,M,w,k,P,C;let S,D;let{itemWidth:O,itemHeight:A}=(p=i,m=e,x=a,b=t,_=s,{itemWidth:(y=b,v=p,M=m,w=x,(S=y.text)&&"string"!=typeof S&&(S=S.reduce((t,e)=>t.length>e.length?t:e)),v+M.size/2+w.measureText(S).width),itemHeight:(k=_,P=b,C=m.lineHeight,D=k,"string"!=typeof P.text&&(D=aP(P,C)),D)});r>0&&u+A+2*n>h&&(d+=c+n,l.push({width:c,height:u}),f+=c+n,g++,c=u=0),o[r]={left:f,top:u,col:g,width:O,height:A},c=Math.max(c,O),u+=A+n}),d+=c,l.push({width:c,height:u}),d}adjustHitBoxes(){if(!this.options.display)return;let t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:a}}=this,r=e7(a,this.left,this.width);if(this.isHorizontal()){let a=0,n=tK(i,this.left+s,this.right-this.lineWidths[a]);for(let o of e)a!==o.row&&(a=o.row,n=tK(i,this.left+s,this.right-this.lineWidths[a])),o.top+=this.top+t+s,o.left=r.leftForLtr(r.x(n),o.width),n+=o.width+s}else{let a=0,n=tK(i,this.top+t+s,this.bottom-this.columnSizes[a].height);for(let o of e)o.col!==a&&(a=o.col,n=tK(i,this.top+t+s,this.bottom-this.columnSizes[a].height)),o.top=n,o.left+=this.left+s,o.left=r.leftForLtr(r.x(o.left),o.width),n+=o.height+s}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){let t=this.ctx;em(t,this),this._draw(),ex(t)}}_draw(){let t;let{options:e,columnSizes:i,lineWidths:s,ctx:a}=this,{align:r,labels:n}=e,o=eh.color,l=e7(e.rtl,this.left,this.width),h=eO(n.font),{padding:d}=n,c=h.size,u=c/2;this.drawTitle(),a.textAlign=l.textAlign("left"),a.textBaseline="middle",a.lineWidth=.5,a.font=h.string;let{boxWidth:f,boxHeight:g,itemHeight:p}=aM(n,c),m=function(t,e,i){if(isNaN(f)||f<=0||isNaN(g)||g<0)return;a.save();let s=te(i.lineWidth,1);if(a.fillStyle=te(i.fillStyle,o),a.lineCap=te(i.lineCap,"butt"),a.lineDashOffset=te(i.lineDashOffset,0),a.lineJoin=te(i.lineJoin,"miter"),a.lineWidth=s,a.strokeStyle=te(i.strokeStyle,o),a.setLineDash(te(i.lineDash,[])),n.usePointStyle){let r={radius:g*Math.SQRT2/2,pointStyle:i.pointStyle,rotation:i.rotation,borderWidth:s};eg(a,r,l.xPlus(t,f/2),e+u,n.pointStyleWidth&&f)}else{let r=e+Math.max((c-g)/2,0),n=l.leftForLtr(t,f),o=eS(i.borderRadius);a.beginPath(),Object.values(o).some(t=>0!==t)?ev(a,{x:n,y:r,w:f,h:g,radius:o}):a.rect(n,r,f,g),a.fill(),0!==s&&a.stroke()}a.restore()},x=function(t,e,i){ey(a,i.text,t,e+p/2,h,{strikethrough:i.hidden,textAlign:l.textAlign(i.textAlign)})},b=this.isHorizontal(),_=this._computeTitleHeight();t=b?{x:tK(r,this.left+d,this.right-s[0]),y:this.top+d+_,line:0}:{x:this.left+d,y:tK(r,this.top+_+d,this.bottom-i[0].height),line:0},it(this.ctx,e.textDirection);let y=p+d;this.legendItems.forEach((o,c)=>{a.strokeStyle=o.fontColor,a.fillStyle=o.fontColor;let g=a.measureText(o.text).width,p=l.textAlign(o.textAlign||(o.textAlign=n.textAlign)),v=f+u+g,M=t.x,w=t.y;if(l.setWidth(this.width),b?c>0&&M+v+d>this.right&&(w=t.y+=y,t.line++,M=t.x=tK(r,this.left+d,this.right-s[t.line])):c>0&&w+y>this.bottom&&(M=t.x=M+i[t.line].width+d,t.line++,w=t.y=tK(r,this.top+_+d,this.bottom-i[t.line].height)),m(l.x(M),w,o),M=tJ(p,M+f+u,b?M+v:this.right,e.rtl),x(l.x(M),w,o),b)t.x+=v+d;else if("string"!=typeof o.text){let e=h.lineHeight;t.y+=aP(o,e)+d}else t.y+=y}),ie(this.ctx,e.textDirection)}drawTitle(){let t;let e=this.options,i=e.title,s=eO(i.font),a=eD(i.padding);if(!i.display)return;let r=e7(e.rtl,this.left,this.width),n=this.ctx,o=i.position,l=s.size/2,h=a.top+l,d=this.left,c=this.width;if(this.isHorizontal())c=Math.max(...this.lineWidths),t=this.top+h,d=tK(e.align,d,this.right-c);else{let i=this.columnSizes.reduce((t,e)=>Math.max(t,e.height),0);t=h+tK(e.align,this.top,this.bottom-i-e.labels.padding-this._computeTitleHeight())}let u=tK(o,d,d+c);n.textAlign=r.textAlign(tG(o)),n.textBaseline="middle",n.strokeStyle=i.color,n.fillStyle=i.color,n.font=s.string,ey(n,i.text,u,t,s)}_computeTitleHeight(){let t=this.options.title,e=eO(t.font),i=eD(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,a;if(tB(t,this.left,this.right)&&tB(e,this.top,this.bottom)){for(i=0,a=this.legendHitBoxes;i<a.length;++i)if(tB(t,(s=a[i]).left,s.left+s.width)&&tB(e,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(t){var e,i;let s=this.options;if(e=t.type,i=s,("mousemove"!==e&&"mouseout"!==e||!i.onHover&&!i.onLeave)&&(!i.onClick||"click"!==e&&"mouseup"!==e))return;let a=this._getLegendItemAt(t.x,t.y);if("mousemove"===t.type||"mouseout"===t.type){let e=this._hoveredItem,i=aw(e,a);e&&!i&&ta(s.onLeave,[t,e,this],this),this._hoveredItem=a,a&&!i&&ta(s.onHover,[t,a,this],this)}else a&&ta(s.onClick,[t,a,this],this)}}function aP(t,e){return e*(t.text?t.text.length:0)}class aC extends su{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){let i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;let s=K(i.text)?i.text.length:1;this._padding=eD(i.padding);let a=s*eO(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=a:this.width=a}isHorizontal(){let t=this.options.position;return"top"===t||"bottom"===t}_drawArgs(t){let e,i,s;let{top:a,left:r,bottom:n,right:o,options:l}=this,h=l.align,d=0;return this.isHorizontal()?(i=tK(h,r,o),s=a+t,e=o-r):("left"===l.position?(i=r+t,s=tK(h,n,a),d=-.5*t_):(i=o-t,s=tK(h,a,n),d=.5*t_),e=n-a),{titleX:i,titleY:s,maxWidth:e,rotation:d}}draw(){let t=this.ctx,e=this.options;if(!e.display)return;let i=eO(e.font),s=i.lineHeight/2+this._padding.top,{titleX:a,titleY:r,maxWidth:n,rotation:o}=this._drawArgs(s);ey(t,e.text,0,0,i,{color:e.color,maxWidth:n,rotation:o,textAlign:tG(e.align),textBaseline:"middle",translation:[a,r]})}}let aS=new WeakMap,aD={average(t){let e,i;if(!t.length)return!1;let s=new Set,a=0,r=0;for(e=0,i=t.length;e<i;++e){let i=t[e].element;if(i&&i.hasValue()){let t=i.tooltipPosition();s.add(t.x),a+=t.y,++r}}return 0!==r&&0!==s.size&&{x:[...s].reduce((t,e)=>t+e)/s.size,y:a/r}},nearest(t,e){let i,s,a;if(!t.length)return!1;let r=e.x,n=e.y,o=Number.POSITIVE_INFINITY;for(i=0,s=t.length;i<s;++i){let s=t[i].element;if(s&&s.hasValue()){let t=tj(e,s.getCenterPoint());t<o&&(o=t,a=s)}}if(a){let t=a.tooltipPosition();r=t.x,n=t.y}return{x:r,y:n}}};function aO(t,e){return e&&(K(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function aA(t){return("string"==typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function aT(t,e){let i=t.chart.ctx,{body:s,footer:a,title:r}=t,{boxWidth:n,boxHeight:o}=e,l=eO(e.bodyFont),h=eO(e.titleFont),d=eO(e.footerFont),c=r.length,u=a.length,f=s.length,g=eD(e.padding),p=g.height,m=0,x=s.reduce((t,e)=>t+e.before.length+e.lines.length+e.after.length,0);x+=t.beforeBody.length+t.afterBody.length,c&&(p+=c*h.lineHeight+(c-1)*e.titleSpacing+e.titleMarginBottom),x&&(p+=f*(e.displayColors?Math.max(o,l.lineHeight):l.lineHeight)+(x-f)*l.lineHeight+(x-1)*e.bodySpacing),u&&(p+=e.footerMarginTop+u*d.lineHeight+(u-1)*e.footerSpacing);let b=0,_=function(t){m=Math.max(m,i.measureText(t).width+b)};return i.save(),i.font=h.string,tr(t.title,_),i.font=l.string,tr(t.beforeBody.concat(t.afterBody),_),b=e.displayColors?n+2+e.boxPadding:0,tr(s,t=>{tr(t.before,_),tr(t.lines,_),tr(t.after,_)}),b=0,i.font=d.string,tr(t.footer,_),i.restore(),{width:m+=g.width,height:p}}function aL(t,e,i){let s=i.yAlign||e.yAlign||function(t,e){let{y:i,height:s}=e;return i<s/2?"top":i>t.height-s/2?"bottom":"center"}(t,i);return{xAlign:i.xAlign||e.xAlign||function(t,e,i,s){let{x:a,width:r}=i,{width:n,chartArea:{left:o,right:l}}=t,h="center";return"center"===s?h=a<=(o+l)/2?"left":"right":a<=r/2?h="left":a>=n-r/2&&(h="right"),function(t,e,i,s){let{x:a,width:r}=s,n=i.caretSize+i.caretPadding;if("left"===t&&a+r+n>e.width||"right"===t&&a-r-n<0)return!0}(h,t,e,i)&&(h="center"),h}(t,e,i,s),yAlign:s}}function aE(t,e,i,s){let{caretSize:a,caretPadding:r,cornerRadius:n}=t,{xAlign:o,yAlign:l}=i,h=a+r,{topLeft:d,topRight:c,bottomLeft:u,bottomRight:f}=eS(n),g=function(t,e){let{x:i,width:s}=t;return"right"===e?i-=s:"center"===e&&(i-=s/2),i}(e,o),p=function(t,e,i){let{y:s,height:a}=t;return"top"===e?s+=i:"bottom"===e?s-=a+i:s-=a/2,s}(e,l,h);return"center"===l?"left"===o?g+=h:"right"===o&&(g-=h):"left"===o?g-=Math.max(d,u)+a:"right"===o&&(g+=Math.max(c,f)+a),{x:tV(g,0,s.width-e.width),y:tV(p,0,s.height-e.height)}}function aR(t,e,i){let s=eD(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-s.right:t.x+s.left}function aF(t,e){let i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}let aj={beforeTitle:X,title(t){if(t.length>0){let e=t[0],i=e.chart.data.labels,s=i?i.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(s>0&&e.dataIndex<s)return i[e.dataIndex]}return""},afterTitle:X,beforeBody:X,beforeLabel:X,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");let i=t.formattedValue;return G(i)||(e+=i),e},labelColor(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:X,afterBody:X,beforeFooter:X,footer:X,afterFooter:X};function aI(t,e,i,s){let a=t[e].call(i,s);return void 0===a?aj[e].call(i,s):a}class aN extends su{static positioners=aD;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,a=new ip(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(a)),a}getContext(){return this.$context||(this.$context=eT(this.chart.getContext(),{tooltip:this,tooltipItems:this._tooltipItems,type:"tooltip"}))}getTitle(t,e){let{callbacks:i}=e,s=aI(i,"beforeTitle",this,t),a=aI(i,"title",this,t),r=aI(i,"afterTitle",this,t),n=[];return n=aO(n,aA(s)),n=aO(n,aA(a)),n=aO(n,aA(r))}getBeforeBody(t,e){return aO([],aA(aI(e.callbacks,"beforeBody",this,t)))}getBody(t,e){let{callbacks:i}=e,s=[];return tr(t,t=>{let e={before:[],lines:[],after:[]},a=aF(i,t);aO(e.before,aA(aI(a,"beforeLabel",this,t))),aO(e.lines,aI(a,"label",this,t)),aO(e.after,aA(aI(a,"afterLabel",this,t))),s.push(e)}),s}getAfterBody(t,e){return aO([],aA(aI(e.callbacks,"afterBody",this,t)))}getFooter(t,e){let{callbacks:i}=e,s=aI(i,"beforeFooter",this,t),a=aI(i,"footer",this,t),r=aI(i,"afterFooter",this,t),n=[];return n=aO(n,aA(s)),n=aO(n,aA(a)),n=aO(n,aA(r))}_createItems(t){let e,i;let s=this._active,a=this.chart.data,r=[],n=[],o=[],l=[];for(e=0,i=s.length;e<i;++e)l.push(function(t,e){let{element:i,datasetIndex:s,index:a}=e,r=t.getDatasetMeta(s).controller,{label:n,value:o}=r.getLabelAndValue(a);return{chart:t,label:n,parsed:r.getParsed(a),raw:t.data.datasets[s].data[a],formattedValue:o,dataset:r.getDataset(),dataIndex:a,datasetIndex:s,element:i}}(this.chart,s[e]));return t.filter&&(l=l.filter((e,i,s)=>t.filter(e,i,s,a))),t.itemSort&&(l=l.sort((e,i)=>t.itemSort(e,i,a))),tr(l,e=>{let i=aF(t.callbacks,e);r.push(aI(i,"labelColor",this,e)),n.push(aI(i,"labelPointStyle",this,e)),o.push(aI(i,"labelTextColor",this,e))}),this.labelColors=r,this.labelPointStyles=n,this.labelTextColors=o,this.dataPoints=l,l}update(t,e){let i;let s=this.options.setContext(this.getContext()),a=this._active,r=[];if(a.length){let t=aD[s.position].call(this,a,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);let e=this._size=aT(this,s),n=Object.assign({},t,e),o=aL(this.chart,s,n),l=aE(s,n,o,this.chart);this.xAlign=o.xAlign,this.yAlign=o.yAlign,i={opacity:1,x:l.x,y:l.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(i={opacity:0});this._tooltipItems=r,this.$context=void 0,i&&this._resolveAnimations().update(this,i),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){let a=this.getCaretPosition(t,i,s);e.lineTo(a.x1,a.y1),e.lineTo(a.x2,a.y2),e.lineTo(a.x3,a.y3)}getCaretPosition(t,e,i){let s,a,r,n,o,l;let{xAlign:h,yAlign:d}=this,{caretSize:c,cornerRadius:u}=i,{topLeft:f,topRight:g,bottomLeft:p,bottomRight:m}=eS(u),{x:x,y:b}=t,{width:_,height:y}=e;return"center"===d?(o=b+y/2,"left"===h?(a=(s=x)-c,n=o+c,l=o-c):(a=(s=x+_)+c,n=o-c,l=o+c),r=s):(a="left"===h?x+Math.max(f,p)+c:"right"===h?x+_-Math.max(g,m)-c:this.caretX,"top"===d?(o=(n=b)-c,s=a-c,r=a+c):(o=(n=b+y)+c,s=a+c,r=a-c),l=n),{x1:s,x2:a,x3:r,y1:n,y2:o,y3:l}}drawTitle(t,e,i){let s,a,r;let n=this.title,o=n.length;if(o){let l=e7(i.rtl,this.x,this.width);for(r=0,t.x=aR(this,i.titleAlign,i),e.textAlign=l.textAlign(i.titleAlign),e.textBaseline="middle",s=eO(i.titleFont),a=i.titleSpacing,e.fillStyle=i.titleColor,e.font=s.string;r<o;++r)e.fillText(n[r],l.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+a,r+1===o&&(t.y+=i.titleMarginBottom-a)}}_drawColorBox(t,e,i,s,a){let r=this.labelColors[i],n=this.labelPointStyles[i],{boxHeight:o,boxWidth:l}=a,h=eO(a.bodyFont),d=aR(this,"left",a),c=s.x(d),u=o<h.lineHeight?(h.lineHeight-o)/2:0,f=e.y+u;if(a.usePointStyle){let e={radius:Math.min(l,o)/2,pointStyle:n.pointStyle,rotation:n.rotation,borderWidth:1},i=s.leftForLtr(c,l)+l/2,h=f+o/2;t.strokeStyle=a.multiKeyBackground,t.fillStyle=a.multiKeyBackground,ef(t,e,i,h),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,ef(t,e,i,h)}else{t.lineWidth=J(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;let e=s.leftForLtr(c,l),i=s.leftForLtr(s.xPlus(c,1),l-2),n=eS(r.borderRadius);Object.values(n).some(t=>0!==t)?(t.beginPath(),t.fillStyle=a.multiKeyBackground,ev(t,{x:e,y:f,w:l,h:o,radius:n}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),ev(t,{x:i,y:f+1,w:l-2,h:o-2,radius:n}),t.fill()):(t.fillStyle=a.multiKeyBackground,t.fillRect(e,f,l,o),t.strokeRect(e,f,l,o),t.fillStyle=r.backgroundColor,t.fillRect(i,f+1,l-2,o-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){let s,a,r,n,o,l;let{body:h}=this,{bodySpacing:d,bodyAlign:c,displayColors:u,boxHeight:f,boxWidth:g,boxPadding:p}=i,m=eO(i.bodyFont),x=m.lineHeight,b=0,_=e7(i.rtl,this.x,this.width),y=function(i){e.fillText(i,_.x(t.x+b),t.y+x/2),t.y+=x+d},v=_.textAlign(c);for(e.textAlign=c,e.textBaseline="middle",e.font=m.string,t.x=aR(this,v,i),e.fillStyle=i.bodyColor,tr(this.beforeBody,y),b=u&&"right"!==v?"center"===c?g/2+p:g+2+p:0,r=0,o=h.length;r<o;++r){for(s=h[r],e.fillStyle=this.labelTextColors[r],tr(s.before,y),a=s.lines,u&&a.length&&(this._drawColorBox(e,t,r,_,i),x=Math.max(m.lineHeight,f)),n=0,l=a.length;n<l;++n)y(a[n]),x=m.lineHeight;tr(s.after,y)}b=0,x=m.lineHeight,tr(this.afterBody,y),t.y-=d}drawFooter(t,e,i){let s,a;let r=this.footer,n=r.length;if(n){let o=e7(i.rtl,this.x,this.width);for(t.x=aR(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=o.textAlign(i.footerAlign),e.textBaseline="middle",s=eO(i.footerFont),e.fillStyle=i.footerColor,e.font=s.string,a=0;a<n;++a)e.fillText(r[a],o.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){let{xAlign:a,yAlign:r}=this,{x:n,y:o}=t,{width:l,height:h}=i,{topLeft:d,topRight:c,bottomLeft:u,bottomRight:f}=eS(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(n+d,o),"top"===r&&this.drawCaret(t,e,i,s),e.lineTo(n+l-c,o),e.quadraticCurveTo(n+l,o,n+l,o+c),"center"===r&&"right"===a&&this.drawCaret(t,e,i,s),e.lineTo(n+l,o+h-f),e.quadraticCurveTo(n+l,o+h,n+l-f,o+h),"bottom"===r&&this.drawCaret(t,e,i,s),e.lineTo(n+u,o+h),e.quadraticCurveTo(n,o+h,n,o+h-u),"center"===r&&"left"===a&&this.drawCaret(t,e,i,s),e.lineTo(n,o+d),e.quadraticCurveTo(n,o,n+d,o),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){let e=this.chart,i=this.$animations,s=i&&i.x,a=i&&i.y;if(s||a){let i=aD[t.position].call(this,this._active,this._eventPosition);if(!i)return;let r=this._size=aT(this,t),n=Object.assign({},i,this._size),o=aL(e,t,n),l=aE(t,n,o,e);(s._to!==l.x||a._to!==l.y)&&(this.xAlign=o.xAlign,this.yAlign=o.yAlign,this.width=r.width,this.height=r.height,this.caretX=i.x,this.caretY=i.y,this._resolveAnimations().update(this,l))}}_willRender(){return!!this.opacity}draw(t){let e=this.options.setContext(this.getContext()),i=this.opacity;if(!i)return;this._updateAnimationTarget(e);let s={width:this.width,height:this.height},a={x:this.x,y:this.y};i=.001>Math.abs(i)?0:i;let r=eD(e.padding),n=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&n&&(t.save(),t.globalAlpha=i,this.drawBackground(a,t,s,e),it(t,e.textDirection),a.y+=r.top,this.drawTitle(a,t,e),this.drawBody(a,t,e),this.drawFooter(a,t,e),ie(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){let i=this._active,s=t.map(({datasetIndex:t,index:e})=>{let i=this.chart.getDatasetMeta(t);if(!i)throw Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:i.data[e],index:e}}),a=!tn(i,s),r=this._positionChanged(s,e);(a||r)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let s=this.options,a=this._active||[],r=this._getActiveElements(t,a,e,i),n=this._positionChanged(r,t),o=e||!tn(r,a)||n;return o&&(this._active=r,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),o}_getActiveElements(t,e,i,s){let a=this.options;if("mouseout"===t.type)return[];if(!s)return e.filter(t=>this.chart.data.datasets[t.datasetIndex]&&void 0!==this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index));let r=this.chart.getElementsAtEventForMode(t,a.mode,a,i);return a.reverse&&r.reverse(),r}_positionChanged(t,e){let{caretX:i,caretY:s,options:a}=this,r=aD[a.position].call(this,t,e);return!1!==r&&(i!==r.x||s!==r.y)}}var az=Object.freeze({__proto__:null,Colors:{id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(t,e,i){let s;if(!i.enabled)return;let{data:{datasets:a},options:r}=t.config,{elements:n}=r,o=ah(a)||r&&(r.borderColor||r.backgroundColor)||n&&ah(n)||"rgba(0,0,0,0.1)"!==eh.borderColor||"rgba(0,0,0,0.1)"!==eh.backgroundColor;if(!i.forceOverride&&o)return;let l=(s=0,(e,i)=>{var a,r,n;let o=t.getDatasetMeta(i).controller;o instanceof iF?(a=s,e.backgroundColor=e.data.map(()=>ao(a++)),s=a):o instanceof iI?(r=s,e.backgroundColor=e.data.map(()=>al(r++)),s=r):o&&(e.borderColor=ao(n=s),e.backgroundColor=al(n),s=++n)});a.forEach(l)}},Decimation:{id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(t,e,i)=>{if(!i.enabled){ac(t);return}let s=t.width;t.data.datasets.forEach((e,a)=>{let r;let{_data:n,indexAxis:o}=e,l=t.getDatasetMeta(a),h=n||e.data;if("y"===eA([o,t.options.indexAxis])||!l.controller.supportsDecimation)return;let d=t.scales[l.xAxisID];if("linear"!==d.type&&"time"!==d.type||t.options.parsing)return;let{start:c,count:u}=function(t,e){let i;let s=e.length,a=0,{iScale:r}=t,{min:n,max:o,minDefined:l,maxDefined:h}=r.getUserBounds();return l&&(a=tV(tH(e,r.axis,n).lo,0,s-1)),i=h?tV(tH(e,r.axis,o).hi+1,a,s)-a:s-a,{start:a,count:i}}(l,h);if(u<=(i.threshold||4*s)){ad(e);return}switch(G(n)&&(e._data=h,delete e.data,Object.defineProperty(e,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(t){this._data=t}})),i.algorithm){case"lttb":r=function(t,e,i,s,a){let r,n,o,l,h;let d=a.samples||s;if(d>=i)return t.slice(e,e+i);let c=[],u=(i-2)/(d-2),f=0,g=e+i-1,p=e;for(r=0,c[f++]=t[p];r<d-2;r++){let s,a=0,d=0,g=Math.floor((r+1)*u)+1+e,m=Math.min(Math.floor((r+2)*u)+1,i)+e,x=m-g;for(s=g;s<m;s++)a+=t[s].x,d+=t[s].y;a/=x,d/=x;let b=Math.floor(r*u)+1+e,_=Math.min(Math.floor((r+1)*u)+1,i)+e,{x:y,y:v}=t[p];for(o=l=-1,s=b;s<_;s++)(l=.5*Math.abs((y-a)*(t[s].y-v)-(y-t[s].x)*(d-v)))>o&&(o=l,n=t[s],h=s);c[f++]=n,p=h}return c[f++]=t[g],c}(h,c,u,s,i);break;case"min-max":r=function(t,e,i,s){let a,r,n,o,l,h,d,c,u,f,g=0,p=0,m=[],x=t[e].x,b=t[e+i-1].x-x;for(a=e;a<e+i;++a){n=((r=t[a]).x-x)/b*s,o=r.y;let e=0|n;if(e===l)o<u?(u=o,h=a):o>f&&(f=o,d=a),g=(p*g+r.x)/++p;else{let i=a-1;if(!G(h)&&!G(d)){let e=Math.min(h,d),s=Math.max(h,d);e!==c&&e!==i&&m.push({...t[e],x:g}),s!==c&&s!==i&&m.push({...t[s],x:g})}a>0&&i!==c&&m.push(t[i]),m.push(r),l=e,p=0,u=f=o,h=d=c=a}}return m}(h,c,u,s);break;default:throw Error(`Unsupported decimation algorithm '${i.algorithm}'`)}e._decimated=r})},destroy(t){ac(t)}},Filler:{id:"filler",afterDatasetsUpdate(t,e,i){let s,a,r,n;let o=(t.data.datasets||[]).length,l=[];for(a=0;a<o;++a)r=(s=t.getDatasetMeta(a)).dataset,n=null,r&&r.options&&r instanceof s8&&(n={visible:t.isDatasetVisible(a),index:a,fill:function(t,e,i){var s,a,r,n;let o=function(t){let e=t.options,i=e.fill,s=te(i&&i.target,i);return void 0===s&&(s=!!e.backgroundColor),!1!==s&&null!==s&&(!0===s?"origin":s)}(t);if(J(o))return!isNaN(o.value)&&o;let l=parseFloat(o);return Q(l)&&Math.floor(l)===l?(s=o[0],a=e,r=l,n=i,("-"===s||"+"===s)&&(r=a+r),r!==a&&!(r<0)&&!(r>=n)&&r):["origin","start","end","stack","shape"].indexOf(o)>=0&&o}(r,a,o),chart:t,axis:s.controller.options.indexAxis,scale:s.vScale,line:r}),s.$filler=n,l.push(n);for(a=0;a<o;++a)(n=l[a])&&!1!==n.fill&&(n.fill=function(t,e,i){let s;let a=t[e].fill,r=[e];if(!i)return a;for(;!1!==a&&-1===r.indexOf(a);){if(!Q(a))return a;if(!(s=t[a]))break;if(s.visible)return a;r.push(a),a=s.fill}return!1}(l,a,i.propagate))},beforeDraw(t,e,i){let s="beforeDraw"===i.drawTime,a=t.getSortedVisibleDatasetMetas(),r=t.chartArea;for(let e=a.length-1;e>=0;--e){let i=a[e].$filler;i&&(i.line.updateControlPoints(r,i.axis),s&&i.fill&&ab(t.ctx,i,r))}},beforeDatasetsDraw(t,e,i){if("beforeDatasetsDraw"!==i.drawTime)return;let s=t.getSortedVisibleDatasetMetas();for(let e=s.length-1;e>=0;--e){let i=s[e].$filler;am(i)&&ab(t.ctx,i,t.chartArea)}},beforeDatasetDraw(t,e,i){let s=e.meta.$filler;am(s)&&"beforeDatasetDraw"===i.drawTime&&ab(t.ctx,s,t.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}},Legend:{id:"legend",_element:ak,start(t,e,i){let s=t.legend=new ak({ctx:t.ctx,options:i,chart:t});i8.configure(t,s,i),i8.addBox(t,s)},stop(t){i8.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){let s=t.legend;i8.configure(t,s,i),s.options=i},afterUpdate(t){let e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){let s=e.datasetIndex,a=i.chart;a.isDatasetVisible(s)?(a.hide(s),e.hidden=!0):(a.show(s),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){let e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:s,textAlign:a,color:r,useBorderRadius:n,borderRadius:o}}=t.legend.options;return t._getSortedDatasetMetas().map(t=>{let l=t.controller.getStyle(i?0:void 0),h=eD(l.borderWidth);return{text:e[t.index].label,fillStyle:l.backgroundColor,fontColor:r,hidden:!t.visible,lineCap:l.borderCapStyle,lineDash:l.borderDash,lineDashOffset:l.borderDashOffset,lineJoin:l.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:l.borderColor,pointStyle:s||l.pointStyle,rotation:l.rotation,textAlign:a||l.textAlign,borderRadius:n&&(o||l.borderRadius),datasetIndex:t.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}},SubTitle:{id:"subtitle",start(t,e,i){let s=new aC({ctx:t.ctx,options:i,chart:t});i8.configure(t,s,i),i8.addBox(t,s),aS.set(t,s)},stop(t){i8.removeBox(t,aS.get(t)),aS.delete(t)},beforeUpdate(t,e,i){let s=aS.get(t);i8.configure(t,s,i),s.options=i},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},Title:{id:"title",_element:aC,start(t,e,i){!function(t,e){let i=new aC({ctx:t.ctx,options:e,chart:t});i8.configure(t,i,e),i8.addBox(t,i),t.titleBlock=i}(t,i)},stop(t){let e=t.titleBlock;i8.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,i){let s=t.titleBlock;i8.configure(t,s,i),s.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},Tooltip:{id:"tooltip",_element:aN,positioners:aD,afterInit(t,e,i){i&&(t.tooltip=new aN({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){let e=t.tooltip;if(e&&e._willRender()){let i={tooltip:e};if(!1===t.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0}))return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){let i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:aj},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]}});let aV=(t,e,i,s)=>("string"==typeof e?(i=t.push(e)-1,s.unshift({index:i,label:e})):isNaN(e)&&(i=null),i),aB=(t,e)=>null===t?null:tV(Math.round(t),0,e);function aW(t){let e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class aH extends sy{static id="category";static defaults={ticks:{callback:aW}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let e=this._addedLabels;if(e.length){let t=this.getLabels();for(let{index:i,label:s}of e)t[i]===s&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(G(t))return null;let i=this.getLabels();return aB(e=isFinite(e)&&i[e]===t?e:function(t,e,i,s){let a=t.indexOf(e);return -1===a?aV(t,e,i,s):a!==t.lastIndexOf(e)?i:a}(i,t,te(e,t),this._addedLabels),i.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),{min:i,max:s}=this.getMinMax(!0);"ticks"!==this.options.bounds||(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){let t=this.min,e=this.max,i=this.options.offset,s=[],a=this.getLabels();a=0===t&&e===a.length-1?a:a.slice(t,e+1),this._valueRange=Math.max(a.length-+!i,1),this._startValue=this.min-.5*!!i;for(let i=t;i<=e;i++)s.push({value:i});return s}getLabelForValue(t){return aW.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function a$(t,e,{horizontal:i,minRotation:s}){let a=tE(s),r=(i?Math.sin(a):Math.cos(a))||.001,n=.75*e*(""+t).length;return Math.min(e/r,n)}class aU extends sy{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return G(t)||("number"==typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){let{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds(),{min:s,max:a}=this,r=t=>s=e?s:t,n=t=>a=i?a:t;if(t){let t=tD(s),e=tD(a);t<0&&e<0?n(0):t>0&&e>0&&r(0)}if(s===a){let e=0===a?1:Math.abs(.05*a);n(a+e),t||r(s-e)}this.min=s,this.max=a}getTickLimit(){let t;let{maxTicksLimit:e,stepSize:i}=this.options.ticks;return i?(t=Math.ceil(this.max/i)-Math.floor(this.min/i)+1)>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${t} ticks. Limiting to 1000.`),t=1e3):(t=this.computeTickLimit(),e=e||11),e&&(t=Math.min(e,t)),t}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let t=this.options,e=t.ticks,i=this.getTickLimit(),s=function(t,e){let i,s,a,r;let n=[],{bounds:o,step:l,min:h,max:d,precision:c,count:u,maxTicks:f,maxDigits:g,includeBounds:p}=t,m=l||1,x=f-1,{min:b,max:_}=e,y=!G(h),v=!G(d),M=!G(u),w=(_-b)/(g+1),k=tA((_-b)/x/m)*m;if(k<1e-14&&!y&&!v)return[{value:b},{value:_}];(r=Math.ceil(_/k)-Math.floor(b/k))>x&&(k=tA(r*k/x/m)*m),G(c)||(k=Math.ceil(k*(i=Math.pow(10,c)))/i),"ticks"===o?(s=Math.floor(b/k)*k,a=Math.ceil(_/k)*k):(s=b,a=_),y&&v&&l&&function(t,e){let i=Math.round(t);return i-e<=t&&i+e>=t}((d-h)/l,k/1e3)?(r=Math.round(Math.min((d-h)/k,f)),k=(d-h)/r,s=h,a=d):M?(s=y?h:s,k=((a=v?d:a)-s)/(r=u-1)):r=tO(r=(a-s)/k,Math.round(r),k/1e3)?Math.round(r):Math.ceil(r);let P=Math.max(tR(k),tR(s));s=Math.round(s*(i=Math.pow(10,G(c)?P:c)))/i,a=Math.round(a*i)/i;let C=0;for(y&&(p&&s!==h?(n.push({value:h}),s<h&&C++,tO(Math.round((s+C*k)*i)/i,h,a$(h,w,t))&&C++):s<h&&C++);C<r;++C){let t=Math.round((s+C*k)*i)/i;if(v&&t>d)break;n.push({value:t})}return v&&p&&a!==d?n.length&&tO(n[n.length-1].value,d,a$(d,w,t))?n[n.length-1].value=d:n.push({value:d}):v&&a!==d||n.push({value:a}),n}({maxTicks:i=Math.max(2,i),bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&tL(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}configure(){let t=this.ticks,e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){let s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return ee(t,this.chart.options.locale,this.options.ticks.format)}}class aY extends aU{static id="linear";static defaults={ticks:{callback:es.formatters.numeric}};determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=Q(t)?t:0,this.max=Q(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){let t=this.isHorizontal(),e=t?this.width:this.height,i=tE(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001;return Math.ceil(e/Math.min(40,this._resolveTickFontOptions(0).lineHeight/s))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}let aq=t=>Math.floor(tS(t)),aX=(t,e)=>Math.pow(10,aq(t)+e);function aZ(t){return 1==t/Math.pow(10,aq(t))}function aG(t,e,i){let s=Math.pow(10,i),a=Math.floor(t/s);return Math.ceil(e/s)-a}class aK extends sy{static id="logarithmic";static defaults={ticks:{callback:es.formatters.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){let i=aU.prototype.parse.apply(this,[t,e]);if(0===i){this._zero=!0;return}return Q(i)&&i>0?i:null}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=Q(t)?Math.max(0,t):null,this.max=Q(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!Q(this._userMin)&&(this.min=t===aX(this.min,0)?aX(this.min,-1):aX(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),i=this.min,s=this.max,a=e=>i=t?i:e,r=t=>s=e?s:t;i===s&&(i<=0?(a(1),r(10)):(a(aX(i,-1)),r(aX(s,1)))),i<=0&&a(aX(s,-1)),s<=0&&r(aX(i,1)),this.min=i,this.max=s}buildTicks(){let t=this.options,e=function(t,{min:e,max:i}){e=tt(t.min,e);let s=[],a=aq(e),r=function(t,e){let i=aq(e-t);for(;aG(t,e,i)>10;)i++;for(;10>aG(t,e,i);)i--;return Math.min(i,aq(t))}(e,i),n=r<0?Math.pow(10,Math.abs(r)):1,o=Math.pow(10,r),l=a>r?Math.pow(10,a):0,h=Math.round((e-l)*n)/n,d=Math.floor((e-l)/o/10)*o*10,c=Math.floor((h-d)/Math.pow(10,r)),u=tt(t.min,Math.round((l+d+c*Math.pow(10,r))*n)/n);for(;u<i;)s.push({value:u,major:aZ(u),significand:c}),c>=10?c=c<15?15:20:c++,c>=20&&(c=2,n=++r>=0?1:n),u=Math.round((l+d+c*Math.pow(10,r))*n)/n;let f=tt(t.max,u);return s.push({value:f,major:aZ(f),significand:c}),s}({min:this._userMin,max:this._userMax},this);return"ticks"===t.bounds&&tL(e,this,"value"),t.reverse?(e.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),e}getLabelForValue(t){return void 0===t?"0":ee(t,this.chart.options.locale,this.options.ticks.format)}configure(){let t=this.min;super.configure(),this._startValue=tS(t),this._valueRange=tS(this.max)-tS(t)}getPixelForValue(t){return((void 0===t||0===t)&&(t=this.min),null===t||isNaN(t))?NaN:this.getPixelForDecimal(t===this.min?0:(tS(t)-this._startValue)/this._valueRange)}getValueForPixel(t){let e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function aJ(t){let e=t.ticks;if(e.display&&t.display){let t=eD(e.backdropPadding);return te(e.font&&e.font.size,eh.font.size)+t.height}return 0}function aQ(t,e,i,s,a){return t===s||t===a?{start:e-i/2,end:e+i/2}:t<s||t>a?{start:e-i,end:e}:{start:e,end:e+i}}function a0(t,e,i,s){let{ctx:a}=t;if(i)a.arc(t.xCenter,t.yCenter,e,0,ty);else{let i=t.getPointPosition(0,e);a.moveTo(i.x,i.y);for(let r=1;r<s;r++)i=t.getPointPosition(r,e),a.lineTo(i.x,i.y)}}class a1 extends aU{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:es.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:t=>t,padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let t=this._padding=eD(aJ(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!1);this.min=Q(t)&&!isNaN(t)?t:0,this.max=Q(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/aJ(this.options))}generateTickLabels(t){aU.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((t,e)=>{let i=ta(this.options.pointLabels.callback,[t,e],this);return i||0===i?i:""}).filter((t,e)=>this.chart.getDataVisibility(e))}fit(){let t=this.options;t.display&&t.pointLabels.display?function(t){let e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},i=Object.assign({},e),s=[],a=[],r=t._pointLabels.length,n=t.options.pointLabels,o=n.centerPointLabels?t_/r:0;for(let d=0;d<r;d++){var l,h;let r=n.setContext(t.getPointLabelContext(d));a[d]=r.padding;let c=t.getPointPosition(d,t.drawingArea+a[d],o),u=eO(r.font),f=(l=t.ctx,h=K(h=t._pointLabels[d])?h:[h],{w:function(t,e,i,s){let a,r,n,o,l;let h=(s=s||{}).data=s.data||{},d=s.garbageCollect=s.garbageCollect||[];s.font!==e&&(h=s.data={},d=s.garbageCollect=[],s.font=e),t.save(),t.font=e;let c=0,u=i.length;for(a=0;a<u;a++)if(null==(o=i[a])||K(o)){if(K(o))for(r=0,n=o.length;r<n;r++)null==(l=o[r])||K(l)||(c=ed(t,h,d,c,l))}else c=ed(t,h,d,c,o);t.restore();let f=d.length/2;if(f>i.length){for(a=0;a<f;a++)delete h[d[a]];d.splice(0,f)}return c}(l,u.string,h),h:h.length*u.lineHeight});s[d]=f;let g=tN(t.getIndexAngle(d)+o),p=Math.round(180/t_*g);(function(t,e,i,s,a){let r=Math.abs(Math.sin(i)),n=Math.abs(Math.cos(i)),o=0,l=0;s.start<e.l?(o=(e.l-s.start)/r,t.l=Math.min(t.l,e.l-o)):s.end>e.r&&(o=(s.end-e.r)/r,t.r=Math.max(t.r,e.r+o)),a.start<e.t?(l=(e.t-a.start)/n,t.t=Math.min(t.t,e.t-l)):a.end>e.b&&(l=(a.end-e.b)/n,t.b=Math.max(t.b,e.b+l))})(i,e,g,aQ(p,c.x,f.w,0,180),aQ(p,c.y,f.h,90,270))}t.setCenterPoint(e.l-i.l,i.r-e.r,e.t-i.t,i.b-e.b),t._pointLabelItems=function(t,e,i){let s;let a=[],r=t._pointLabels.length,n=t.options,{centerPointLabels:o,display:l}=n.pointLabels,h={extra:aJ(n)/2,additionalAngle:o?t_/r:0};for(let n=0;n<r;n++){h.padding=i[n],h.size=e[n];let r=function(t,e,i){var s,a,r,n,o,l,h;let d=t.drawingArea,{extra:c,additionalAngle:u,padding:f,size:g}=i,p=t.getPointPosition(e,d+c+f,u),m=Math.round(180/t_*tN(p.angle+tk)),x=(s=p.y,a=g.h,90===(r=m)||270===r?s-=a/2:(r>270||r<90)&&(s-=a),s),b=0===(n=m)||180===n?"center":n<180?"left":"right",_=(o=p.x,l=g.w,"right"===(h=b)?o-=l:"center"===h&&(o-=l/2),o);return{visible:!0,x:p.x,y:x,textAlign:b,left:_,top:x,right:_+g.w,bottom:x+g.h}}(t,n,h);a.push(r),"auto"===l&&(r.visible=function(t,e){if(!e)return!0;let{left:i,top:s,right:a,bottom:r}=t;return!(ep({x:i,y:s},e)||ep({x:i,y:r},e)||ep({x:a,y:s},e)||ep({x:a,y:r},e))}(r,s),r.visible&&(s=r))}return a}(t,s,a)}(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,s){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,s))}getIndexAngle(t){return tN(t*(ty/(this._pointLabels.length||1))+tE(this.options.startAngle||0))}getDistanceFromCenterForValue(t){if(G(t))return NaN;let e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(G(t))return NaN;let e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){let e=this._pointLabels||[];if(t>=0&&t<e.length){let i=e[t];return eT(this.getContext(),{label:i,index:t,type:"pointLabel"})}}getPointPosition(t,e,i=0){let s=this.getIndexAngle(t)-tk+i;return{x:Math.cos(s)*e+this.xCenter,y:Math.sin(s)*e+this.yCenter,angle:s}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){let{left:e,top:i,right:s,bottom:a}=this._pointLabelItems[t];return{left:e,top:i,right:s,bottom:a}}drawBackground(){let{backgroundColor:t,grid:{circular:e}}=this.options;if(t){let i=this.ctx;i.save(),i.beginPath(),a0(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){let t,e,i;let s=this.ctx,a=this.options,{angleLines:r,grid:n,border:o}=a,l=this._pointLabels.length;if(a.pointLabels.display&&function(t,e){let{ctx:i,options:{pointLabels:s}}=t;for(let a=e-1;a>=0;a--){let e=t._pointLabelItems[a];if(!e.visible)continue;let r=s.setContext(t.getPointLabelContext(a));!function(t,e,i){let{left:s,top:a,right:r,bottom:n}=i,{backdropColor:o}=e;if(!G(o)){let i=eS(e.borderRadius),l=eD(e.backdropPadding);t.fillStyle=o;let h=s-l.left,d=a-l.top,c=r-s+l.width,u=n-a+l.height;Object.values(i).some(t=>0!==t)?(t.beginPath(),ev(t,{x:h,y:d,w:c,h:u,radius:i}),t.fill()):t.fillRect(h,d,c,u)}}(i,r,e);let n=eO(r.font),{x:o,y:l,textAlign:h}=e;ey(i,t._pointLabels[a],o,l+n.lineHeight/2,n,{color:r.color,textAlign:h,textBaseline:"middle"})}}(this,l),n.display&&this.ticks.forEach((t,i)=>{if(0!==i||0===i&&this.min<0){e=this.getDistanceFromCenterForValue(t.value);let s=this.getContext(i),a=n.setContext(s),r=o.setContext(s);!function(t,e,i,s,a){let r=t.ctx,n=e.circular,{color:o,lineWidth:l}=e;(n||s)&&o&&l&&!(i<0)&&(r.save(),r.strokeStyle=o,r.lineWidth=l,r.setLineDash(a.dash||[]),r.lineDashOffset=a.dashOffset,r.beginPath(),a0(t,i,n,s),r.closePath(),r.stroke(),r.restore())}(this,a,e,l,r)}}),r.display){for(s.save(),t=l-1;t>=0;t--){let n=r.setContext(this.getPointLabelContext(t)),{color:o,lineWidth:l}=n;l&&o&&(s.lineWidth=l,s.strokeStyle=o,s.setLineDash(n.borderDash),s.lineDashOffset=n.borderDashOffset,e=this.getDistanceFromCenterForValue(a.reverse?this.min:this.max),i=this.getPointPosition(t,e),s.beginPath(),s.moveTo(this.xCenter,this.yCenter),s.lineTo(i.x,i.y),s.stroke())}s.restore()}}drawBorder(){}drawLabels(){let t,e;let i=this.ctx,s=this.options,a=s.ticks;if(!a.display)return;let r=this.getIndexAngle(0);i.save(),i.translate(this.xCenter,this.yCenter),i.rotate(r),i.textAlign="center",i.textBaseline="middle",this.ticks.forEach((r,n)=>{if(0===n&&this.min>=0&&!s.reverse)return;let o=a.setContext(this.getContext(n)),l=eO(o.font);if(t=this.getDistanceFromCenterForValue(this.ticks[n].value),o.showLabelBackdrop){i.font=l.string,e=i.measureText(r.label).width,i.fillStyle=o.backdropColor;let s=eD(o.backdropPadding);i.fillRect(-e/2-s.left,-t-l.size/2-s.top,e+s.width,l.size+s.height)}ey(i,r.label,0,-t,l,{color:o.color,strokeColor:o.textStrokeColor,strokeWidth:o.textStrokeWidth})}),i.restore()}drawTitle(){}}let a2={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},a5=Object.keys(a2);function a4(t,e){return t-e}function a8(t,e){if(G(e))return null;let i=t._adapter,{parser:s,round:a,isoWeekday:r}=t._parseOpts,n=e;return("function"==typeof s&&(n=s(n)),Q(n)||(n="string"==typeof s?i.parse(n,s):i.parse(n)),null===n)?null:(a&&(n="week"===a&&(tT(r)||!0===r)?i.startOf(n,"isoWeek",r):i.startOf(n,a)),+n)}function a3(t,e,i,s){let a=a5.length;for(let r=a5.indexOf(t);r<a-1;++r){let t=a2[a5[r]],a=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((i-e)/(a*t.size))<=s)return a5[r]}return a5[a-1]}function a6(t,e,i){if(i){if(i.length){let{lo:s,hi:a}=tW(i,e);t[i[s]>=e?i[s]:i[a]]=!0}}else t[e]=!0}function a9(t,e,i){let s,a;let r=[],n={},o=e.length;for(s=0;s<o;++s)n[a=e[s]]=s,r.push({value:a,major:!1});return 0!==o&&i?function(t,e,i,s){let a,r;let n=t._adapter,o=+n.startOf(e[0].value,s),l=e[e.length-1].value;for(a=o;a<=l;a=+n.add(a,1,s))(r=i[a])>=0&&(e[r].major=!0);return e}(t,r,n,i):r}class a7 extends sy{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){let i=t.time||(t.time={}),s=this._adapter=new i$._date(t.adapters.date);s.init(e),tc(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:a8(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,e=this._adapter,i=t.time.unit||"day",{min:s,max:a,minDefined:r,maxDefined:n}=this.getUserBounds();function o(t){r||isNaN(t.min)||(s=Math.min(s,t.min)),n||isNaN(t.max)||(a=Math.max(a,t.max))}r&&n||(o(this._getLabelBounds()),("ticks"!==t.bounds||"labels"!==t.ticks.source)&&o(this.getMinMax(!1))),s=Q(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),a=Q(a)&&!isNaN(a)?a:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,a-1),this.max=Math.max(s+1,a)}_getLabelBounds(){let t=this.getLabelTimestamps(),e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){let t=this.options,e=t.time,i=t.ticks,s="labels"===i.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);let a=this.min,r=function(t,e,i){let s=0,a=t.length;for(;s<a&&t[s]<e;)s++;for(;a>s&&t[a-1]>i;)a--;return s>0||a<t.length?t.slice(s,a):t}(s,a,this.max);return this._unit=e.unit||(i.autoSkip?a3(e.minUnit,this.min,this.max,this._getLabelCapacity(a)):function(t,e,i,s,a){for(let r=a5.length-1;r>=a5.indexOf(i);r--){let i=a5[r];if(a2[i].common&&t._adapter.diff(a,s,i)>=e-1)return i}return a5[i?a5.indexOf(i):0]}(this,r.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?function(t){for(let e=a5.indexOf(t)+1,i=a5.length;e<i;++e)if(a2[a5[e]].common)return a5[e]}(this._unit):void 0,this.initOffsets(s),t.reverse&&r.reverse(),a9(this,r,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e,i,s=0,a=0;this.options.offset&&t.length&&(e=this.getDecimalForValue(t[0]),s=1===t.length?1-e:(this.getDecimalForValue(t[1])-e)/2,i=this.getDecimalForValue(t[t.length-1]),a=1===t.length?i:(i-this.getDecimalForValue(t[t.length-2]))/2);let r=t.length<3?.5:.25;s=tV(s,0,r),a=tV(a,0,r),this._offsets={start:s,end:a,factor:1/(s+1+a)}}_generate(){let t,e;let i=this._adapter,s=this.min,a=this.max,r=this.options,n=r.time,o=n.unit||a3(n.minUnit,s,a,this._getLabelCapacity(s)),l=te(r.ticks.stepSize,1),h="week"===o&&n.isoWeekday,d=tT(h)||!0===h,c={},u=s;if(d&&(u=+i.startOf(u,"isoWeek",h)),u=+i.startOf(u,d?"day":o),i.diff(a,s,o)>1e5*l)throw Error(s+" and "+a+" are too far apart with stepSize of "+l+" "+o);let f="data"===r.ticks.source&&this.getDataTimestamps();for(t=u,e=0;t<a;t=+i.add(t,l,o),e++)a6(c,t,f);return(t===a||"ticks"===r.bounds||1===e)&&a6(c,t,f),Object.keys(c).sort(a4).map(t=>+t)}getLabelForValue(t){let e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){let i=this.options.time.displayFormats,s=this._unit,a=e||i[s];return this._adapter.format(t,a)}_tickFormatFunction(t,e,i,s){let a=this.options,r=a.ticks.callback;if(r)return ta(r,[t,e,i],this);let n=a.time.displayFormats,o=this._unit,l=this._majorUnit,h=o&&n[o],d=l&&n[l],c=i[e],u=l&&d&&c&&c.major;return this._adapter.format(t,s||(u?d:h))}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)(s=t[e]).label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){let e=this.options.ticks,i=this.ctx.measureText(t).width,s=tE(this.isHorizontal()?e.maxRotation:e.minRotation),a=Math.cos(s),r=Math.sin(s),n=this._resolveTickFontOptions(0).size;return{w:i*a+n*r,h:i*r+n*a}}_getLabelCapacity(t){let e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,a=this._tickFormatFunction(t,0,a9(this,[t],this._majorUnit),s),r=this._getLabelSize(a),n=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return n>0?n:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;let s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(t=0,e=s.length;t<e;++t)i=i.concat(s[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){let t,e;let i=this._cache.labels||[];if(i.length)return i;let s=this.getLabels();for(t=0,e=s.length;t<e;++t)i.push(a8(this,s[t]));return this._cache.labels=this._normalized?i:this.normalize(i)}normalize(t){return tq(t.sort(a4))}}function rt(t,e,i){let s,a,r,n,o=0,l=t.length-1;i?(e>=t[o].pos&&e<=t[l].pos&&({lo:o,hi:l}=tH(t,"pos",e)),{pos:s,time:r}=t[o],{pos:a,time:n}=t[l]):(e>=t[o].time&&e<=t[l].time&&({lo:o,hi:l}=tH(t,"time",e)),{time:s,pos:r}=t[o],{time:a,pos:n}=t[l]);let h=a-s;return h?r+(n-r)*(e-s)/h:r}class re extends a7{static id="timeseries";static defaults=a7.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=rt(e,this.min),this._tableRange=rt(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){let e,i,s;let{min:a,max:r}=this,n=[],o=[];for(e=0,i=t.length;e<i;++e)(s=t[e])>=a&&s<=r&&n.push(s);if(n.length<2)return[{time:a,pos:0},{time:r,pos:1}];for(e=0,i=n.length;e<i;++e)Math.round((n[e+1]+n[e-1])/2)!==(s=n[e])&&o.push({time:s,pos:e/(i-1)});return o}_generate(){let t=this.min,e=this.max,i=super.getDataTimestamps();return i.includes(t)&&i.length||i.splice(0,0,t),i.includes(e)&&1!==i.length||i.push(e),i.sort((t,e)=>t-e)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;let e=this.getDataTimestamps(),i=this.getLabelTimestamps();return t=e.length&&i.length?this.normalize(e.concat(i)):e.length?e:i,t=this._cache.all=t}getDecimalForValue(t){return(rt(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return rt(this._table,i*this._tableRange+this._minPos,!0)}}var ri=Object.freeze({__proto__:null,CategoryScale:aH,LinearScale:aY,LogarithmicScale:aK,RadialLinearScale:a1,TimeScale:a7,TimeSeriesScale:re});sq.register(iB,aa,az,ri);let rs=({type:t="bar",data:e,labels:i,title:s="",options:n={},height:o=300,horizontal:l=!1,colors:h=["#36A2EB","#FF6384","#FFCE56","#4BC0C0","#9966FF","#FF9F40","#4CAF50","#E91E63","#2196F3","#FFC107","#00BCD4","#795548","#607D8B","#F44336","#3F51B5","#009688","#673AB7","#CDDC39","#FF9800","#FF5722","#9C27B0","#8BC34A","#FFEB3B","#FFC107","#F4F61A","#FF4081"],onClick:d=null})=>{let c=(0,r.useRef)(null),u=(0,r.useRef)(null),f="bar"===t&&l?"horizontalBar":t;return(0,r.useEffect)(()=>{let a;if(!c.current)return;u.current&&u.current.destroy();let r=c.current.getContext("2d");if(!r)return;let o=t=>{if(t<=h.length)return h;let e=[...h];for(;e.length<t;){let t=h[e.length%h.length],i=.7-.1*Math.floor(e.length/h.length);e.push(t.replace(")",`, ${i})`).replace("rgb","rgba"))}return e},l=(e,i)=>{if(Array.isArray(e)&&"number"==typeof e[0])return{label:`Dataset ${i+1}`,data:e,backgroundColor:h[i%h.length],borderColor:"rgba(255, 255, 255, 0.6)",borderWidth:1};let s={label:e.label||`Dataset ${i+1}`,hidden:e.hidden,type:e.type||t,data:e.values||e.data||[],borderWidth:e.borderWidth||1};return"line"===t?{...s,backgroundColor:e.backgroundColor||"rgba(0, 0, 0, 0)",borderColor:e.borderColor||h[i%h.length],pointBackgroundColor:e.pointBackgroundColor||h[i%h.length],tension:e.tension||.4,fill:void 0!==e.fill&&e.fill}:{...s,backgroundColor:e.backgroundColor||("pie"===t||"doughnut"===t?o(e.values?.length||1).map(t=>t):h[i%h.length]),borderColor:e.borderColor||"rgba(255, 255, 255, 0.6)"}};return u.current=new sq(r,{type:f,plugins:[],data:{labels:i,datasets:a=(t=>Array.isArray(t)&&t.length>0&&"object"==typeof t[0]&&"values"in t[0])(e)?e.map((t,e)=>l(t,e)):Array.isArray(e[0])?e.map((t,e)=>l(t,e)):[l({label:s,values:e},0)]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!!s,text:s,font:{size:16}},legend:{display:a.length>1||a[0].label&&"Dataset 1"!==a[0].label,position:"top"},tooltip:{mode:"index",intersect:!1}},scales:"pie"!==t&&"doughnut"!==t?{y:{beginAtZero:!0,grid:{drawBorder:!1},ticks:{padding:10}},x:{grid:{display:!1},ticks:{padding:10}}}:void 0,onClick:d,...n}}),()=>{u.current&&u.current.destroy()}},[e,i,s,n,o,l,h,d,t,f]),(0,a.jsx)("div",{style:{height:`${o}px`,width:"100%"},children:(0,a.jsx)("canvas",{ref:c})})};var ra=i(29523),rr=i(85814),rn=i.n(rr);let ro={labels:["周一","周二","周三","周四","周五","周六","周日"],datasets:[{label:"销售数据",values:[4500,3800,5200,4900,6300,5800,7200],borderColor:"#38bdf8",backgroundColor:"rgba(56, 189, 248, 0.1)",borderWidth:2},{label:"退款数据",values:[800,600,950,700,1200,900,1100],borderColor:"#f43f5e",backgroundColor:"rgba(244, 63, 94, 0.1)",borderWidth:2}]},rl=[{id:"1",amount:1200,type:"income",method:"wechat",time:"2023-06-01 14:30",description:"学员课程购买"},{id:"2",amount:800,type:"expense",method:"alipay",time:"2023-06-02 10:15",description:"教材采购"},{id:"3",amount:1500,type:"income",method:"card",time:"2023-06-03 16:45",description:"学员课程购买"},{id:"4",amount:350,type:"expense",method:"cash",time:"2023-06-04 09:20",description:"办公用品"}];function rh(){let t=(0,n.G)(t=>t.user),e=(0,r.useMemo)(()=>({students:256,courses:48,teachers:12,totalIncome:128500,totalExpense:45600,netIncome:82900,attendanceRate:92,upcomingClasses:18}),[]);return(0,a.jsxs)("div",{className:"max-w-7xl mx-auto p-4 md:p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg shadow-lg p-4 md:p-6 text-white",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"h-6 w-6"}),(0,a.jsxs)("h1",{className:"text-xl md:text-2xl font-bold",children:["欢迎回来，",t?.name||"用户","！"]})]}),(0,a.jsx)("p",{className:"mt-2 opacity-90",children:"感谢使用 Cardmees 财务管理系统"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)(b.Zp,{className:"bg-white transition-all duration-200 group hover:shadow-md hover:translate-y-[-2px]",children:[(0,a.jsx)(b.aR,{className:"pb-2 border-b border-transparent group-hover:border-primary/10 bg-background group-hover:bg-blue-50/50 dark:group-hover:bg-blue-950/10",children:(0,a.jsxs)(b.ZB,{className:"flex items-center text-sm font-medium text-muted-foreground group-hover:text-blue-600",children:[(0,a.jsx)(l.A,{className:"mr-2 h-4 w-4 text-blue-500/70 group-hover:text-blue-500"}),"学员管理"]})}),(0,a.jsx)(b.Wu,{className:"pt-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-xs text-muted-foreground mb-1",children:"总学员数"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.students})]}),(0,a.jsx)(ra.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,a.jsx)(rn(),{href:"/academic/students",className:"text-blue-600 hover:text-blue-700",children:"查看详情"})})]})})]}),(0,a.jsxs)(b.Zp,{className:"bg-white transition-all duration-200 group hover:shadow-md hover:translate-y-[-2px]",children:[(0,a.jsx)(b.aR,{className:"pb-2 border-b border-transparent group-hover:border-primary/10 bg-background group-hover:bg-green-50/50 dark:group-hover:bg-green-950/10",children:(0,a.jsxs)(b.ZB,{className:"flex items-center text-sm font-medium text-muted-foreground group-hover:text-green-600",children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4 text-green-500/70 group-hover:text-green-500"}),"课程管理"]})}),(0,a.jsx)(b.Wu,{className:"pt-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-xs text-muted-foreground mb-1",children:"课程总数"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.courses})]}),(0,a.jsx)(ra.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,a.jsx)(rn(),{href:"/academic/schedule",className:"text-green-600 hover:text-green-700",children:"查看课表"})})]})})]}),(0,a.jsxs)(b.Zp,{className:"bg-white transition-all duration-200 group hover:shadow-md hover:translate-y-[-2px]",children:[(0,a.jsx)(b.aR,{className:"pb-2 border-b border-transparent group-hover:border-primary/10 bg-background group-hover:bg-emerald-50/50 dark:group-hover:bg-emerald-950/10",children:(0,a.jsxs)(b.ZB,{className:"flex items-center text-sm font-medium text-muted-foreground group-hover:text-emerald-600",children:[(0,a.jsx)(c,{className:"mr-2 h-4 w-4 text-emerald-500/70 group-hover:text-emerald-500"}),"财务收入"]})}),(0,a.jsx)(b.Wu,{className:"pt-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-xs text-muted-foreground mb-1",children:"净收入"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-emerald-600",children:["\xa5",e.netIncome.toLocaleString()]})]}),(0,a.jsx)(ra.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,a.jsx)(rn(),{href:"/finance/cashier",className:"text-emerald-600 hover:text-emerald-700",children:"财务管理"})})]})})]}),(0,a.jsxs)(b.Zp,{className:"bg-white transition-all duration-200 group hover:shadow-md hover:translate-y-[-2px]",children:[(0,a.jsx)(b.aR,{className:"pb-2 border-b border-transparent group-hover:border-primary/10 bg-background group-hover:bg-amber-50/50 dark:group-hover:bg-amber-950/10",children:(0,a.jsxs)(b.ZB,{className:"flex items-center text-sm font-medium text-muted-foreground group-hover:text-amber-600",children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4 text-amber-500/70 group-hover:text-amber-500"}),"即将开始"]})}),(0,a.jsx)(b.Wu,{className:"pt-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-xs text-muted-foreground mb-1",children:"今日课程"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-amber-600",children:e.upcomingClasses})]}),(0,a.jsx)(ra.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,a.jsx)(rn(),{href:"/academic/schedule",className:"text-amber-600 hover:text-amber-700",children:"查看详情"})})]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)(b.Zp,{className:"lg:col-span-2",children:[(0,a.jsx)(b.aR,{className:"flex-row items-center border-b border-transparent pb-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)(b.ZB,{className:"flex items-center text-lg",children:[(0,a.jsx)(f,{className:"mr-2 h-5 w-5 text-primary/70"}),"销售趋势分析"]}),(0,a.jsx)(b.BT,{children:"近期销售与退款数据分析"})]})}),(0,a.jsx)(b.Wu,{className:"pt-4",children:(0,a.jsx)(rs,{height:300,type:"line",data:ro.datasets,labels:ro.labels,title:""})})]}),(0,a.jsxs)(b.Zp,{children:[(0,a.jsxs)(b.aR,{children:[(0,a.jsxs)(b.ZB,{className:"flex items-center text-lg",children:[(0,a.jsx)(g,{className:"mr-2 h-5 w-5 text-primary/70"}),"最近账单"]}),(0,a.jsx)(b.BT,{children:"最近的收支记录"})]}),(0,a.jsx)(b.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[rl.map(t=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-slate-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:`p-2 rounded-full ${"income"===t.type?"bg-emerald-100":"bg-rose-100"}`,children:"income"===t.type?(0,a.jsx)(p,{className:"h-4 w-4 text-emerald-600"}):(0,a.jsx)(m.A,{className:"h-4 w-4 text-rose-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:t.description}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:t.time})]})]}),(0,a.jsxs)("div",{className:`font-medium ${"income"===t.type?"text-emerald-600":"text-rose-600"}`,children:["income"===t.type?"+":"-","\xa5",t.amount]})]},t.id)),(0,a.jsx)(ra.$,{variant:"outline",size:"sm",className:"w-full",asChild:!0,children:(0,a.jsx)(rn(),{href:"/finance/cashier",children:"查看全部账单"})})]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)(rd,{icon:(0,a.jsx)(l.A,{className:"h-5 w-5"}),title:"学员管理",description:"管理学员信息、考勤和套餐",href:"/academic/students",color:"blue"}),(0,a.jsx)(rd,{icon:(0,a.jsx)(x.A,{className:"h-5 w-5"}),title:"课程表",description:"查看和管理课程安排",href:"/academic/schedule",color:"green"}),(0,a.jsx)(rd,{icon:(0,a.jsx)(g,{className:"h-5 w-5"}),title:"财务管理",description:"账单管理和财务统计",href:"/finance/cashier",color:"emerald"}),(0,a.jsx)(rd,{icon:(0,a.jsx)(f,{className:"h-5 w-5"}),title:"数据分析",description:"销售数据和业务分析",href:"/data/sales",color:"amber"})]})]})}function rd({icon:t,title:e,description:i,href:s,color:r="blue"}){return(0,a.jsx)(rn(),{href:s,children:(0,a.jsxs)("div",{className:`bg-white border border-gray-100 rounded-lg p-4 transition-all duration-200 hover:shadow-md ${{blue:"hover:border-blue-200 hover:bg-blue-50/50",green:"hover:border-green-200 hover:bg-green-50/50",emerald:"hover:border-emerald-200 hover:bg-emerald-50/50",amber:"hover:border-amber-200 hover:bg-amber-50/50"}[r]}`,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("div",{className:{blue:"text-blue-500",green:"text-green-500",emerald:"text-emerald-500",amber:"text-amber-500"}[r],children:t}),(0,a.jsx)("h4",{className:"font-medium text-gray-800",children:e})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:i})]})})}},21820:t=>{"use strict";t.exports=require("os")},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:t=>{"use strict";t.exports=require("path")},38718:(t,e,i)=>{Promise.resolve().then(i.bind(i,21288))},40228:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},44493:(t,e,i)=>{"use strict";i.d(e,{BT:()=>h,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>c});var s=i(60687),a=i(43210),r=i(4780);let n=a.forwardRef(({className:t,...e},i)=>(0,s.jsx)("div",{ref:i,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...e}));n.displayName="Card";let o=a.forwardRef(({className:t,...e},i)=>(0,s.jsx)("div",{ref:i,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",t),...e}));o.displayName="CardHeader";let l=a.forwardRef(({className:t,...e},i)=>(0,s.jsx)("div",{ref:i,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",t),...e}));l.displayName="CardTitle";let h=a.forwardRef(({className:t,...e},i)=>(0,s.jsx)("div",{ref:i,className:(0,r.cn)("text-sm text-muted-foreground",t),...e}));h.displayName="CardDescription";let d=a.forwardRef(({className:t,...e},i)=>(0,s.jsx)("div",{ref:i,className:(0,r.cn)("p-6 pt-0",t),...e}));d.displayName="CardContent";let c=a.forwardRef(({className:t,...e},i)=>(0,s.jsx)("div",{ref:i,className:(0,r.cn)("flex items-center p-6 pt-0",t),...e}));c.displayName="CardFooter"},48730:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},58869:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},61435:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>h});var s=i(65239),a=i(48088),r=i(88170),n=i.n(r),o=i(30893),l={};for(let t in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>o[t]);i.d(e,l);let h={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,80559)),"F:\\trae\\cardmees\\fronend\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(i.bind(i,63144)),"F:\\trae\\cardmees\\fronend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(i.bind(i,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["F:\\trae\\cardmees\\fronend\\src\\app\\dashboard\\page.tsx"],c={require:i,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63144:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>o,metadata:()=>n});var s=i(37413),a=i(24597),r=i(36733);let n={title:"CardMees",description:"CardMees Application"};function o({children:t}){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{}),(0,s.jsxs)("div",{className:"flex h-[calc(100vh)]",children:[(0,s.jsx)(r.default,{}),(0,s.jsx)("main",{className:"flex-1 p-8 pt-16 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto bg-white rounded-lg shadow-sm p-6",children:t})})]})]})}},74075:t=>{"use strict";t.exports=require("zlib")},79551:t=>{"use strict";t.exports=require("url")},80559:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>s});let s=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\dashboard\\page.tsx","default")},81630:t=>{"use strict";t.exports=require("http")},82080:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},83997:t=>{"use strict";t.exports=require("tty")},89422:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},94735:t=>{"use strict";t.exports=require("events")}};var e=require("../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),s=e.X(0,[4447,7392,5814,3928,3443,3019,9879],()=>i(61435));module.exports=s})();