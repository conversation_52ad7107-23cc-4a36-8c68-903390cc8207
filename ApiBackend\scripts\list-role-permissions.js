import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * List permissions assigned to a role
 * This script lists all permissions assigned to a specific role
 * 
 * Usage:
 * node scripts/list-role-permissions.js <roleCode>
 * 
 * Example:
 * node scripts/list-role-permissions.js teacher
 * 
 * To list all roles and their permissions:
 * node scripts/list-role-permissions.js all
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length < 1) {
    console.error('Usage: node scripts/list-role-permissions.js <roleCode>');
    process.exit(1);
  }
  
  const roleCode = args[0];
  
  // List all roles and their permissions
  if (roleCode === 'all') {
    const roles = await prisma.role.findMany({
      include: {
        rolePermissions: {
          include: {
            permission: true
          }
        }
      }
    });
    
    console.log('=== All Roles and Their Permissions ===');
    
    for (const role of roles) {
      console.log(`\nRole: ${role.name} (${role.code})`);
      console.log(`Description: ${role.description || 'N/A'}`);
      console.log('Permissions:');
      
      if (role.rolePermissions.length === 0) {
        console.log('  No permissions assigned');
      } else {
        role.rolePermissions.forEach(rp => {
          console.log(`  - ${rp.permission.name} (${rp.permission.code})`);
        });
      }
    }
    
    return;
  }
  
  // Get the role with its permissions
  const role = await prisma.role.findUnique({
    where: { code: roleCode },
    include: {
      rolePermissions: {
        include: {
          permission: true
        }
      }
    }
  });
  
  if (!role) {
    console.error(`Role with code ${roleCode} not found`);
    process.exit(1);
  }
  
  console.log(`=== Permissions for Role: ${role.name} (${role.code}) ===`);
  console.log(`Description: ${role.description || 'N/A'}`);
  console.log(`Total permissions: ${role.rolePermissions.length}`);
  console.log('\nPermissions:');
  
  if (role.rolePermissions.length === 0) {
    console.log('  No permissions assigned');
  } else {
    // Group permissions by category (first part of the code before the colon)
    const permissionsByCategory = {};
    
    role.rolePermissions.forEach(rp => {
      const permission = rp.permission;
      const category = permission.code.split(':')[0];
      
      if (!permissionsByCategory[category]) {
        permissionsByCategory[category] = [];
      }
      
      permissionsByCategory[category].push(permission);
    });
    
    // Display permissions by category
    for (const [category, permissions] of Object.entries(permissionsByCategory)) {
      console.log(`\n${category.toUpperCase()}:`);
      
      permissions.forEach(permission => {
        console.log(`  - ${permission.name} (${permission.code})`);
      });
    }
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
