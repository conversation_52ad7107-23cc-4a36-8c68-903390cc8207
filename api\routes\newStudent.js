import { studentController } from '../controllers/studentController.js';
import studentSchema from '../schemas/studentSchama.js';

export default async function (fastify, opts) {
    // 获取学员列表
    fastify.get('/newStudent', {
        schema: studentSchema.studentSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getStudentList
    });

    // 获取简易学员列表
    fastify.get('/newStudent/simple', {
        schema: studentSchema.simpleStudentListSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getSimpleStudentList
    });

    // 获取单个学生信息
    fastify.get('/newStudent/:studentId', {
        schema: studentSchema.studentByIdSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getStudentById
    });

    // 更新学员
    fastify.put('/newStudent/:studentId', {
        schema: studentSchema.updateStudentSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.updateStudent
    })

    // 批量删除学员
    fastify.delete('/newStudent', {
        schema: studentSchema.deleteStudentSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.deleteStudent
    })

    // 获取学生上课记录
    fastify.get('/newStudent/:studentId/classesHistory', {
        schema: studentSchema.classesHistorySchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getClassesHistory
    })

    // 获取学生购买套餐
    fastify.get('/newStudent/:studentId/products', {
        schema: studentSchema.productsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getProducts
    })
    
    // 获取学生购买记录
    fastify.get('/newStudent/:studentId/products/records', {
        schema: studentSchema.productsRecordsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getProductsRecords
    })

    // 获取学生考勤记录
    fastify.get('/newStudent/:studentId/attendance', {
        schema: studentSchema.attendanceSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getAttendance
    })

    // 获取学员班级
    fastify.get('/newStudent/:studentId/classes', {
        schema: studentSchema.classesSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getClasses
    })

    // 学员退出班级
    fastify.delete('/newStudent/:studentId/classes/:classesId', {
        schema: studentSchema.outClassesSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.outClasses
    })

    // 获取学员跟进记录
    fastify.get('/newStudent/:studentId/followRecords', {
        schema: studentSchema.getFollowRecordsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.getFollowRecords
    })

    // 新增学员跟进记录
    fastify.post('/newStudent/:studentId/followRecords', {
        schema: studentSchema.addFollowRecordsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.addFollowRecords
    })

    // 创建学生产品
    fastify.post('/newStudent/:studentId/products', {
        schema: studentSchema.createStudentProductSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.createStudentProduct
    })

    // 更新学生产品
    fastify.put('/newStudent/:studentId/products/:studentProductId', {
        schema: studentSchema.updateStudentProductSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.updateStudentProduct
    })

    // 创建学员跟进人
    fastify.post('/newStudent/:studentId/followUps', {
        schema: studentSchema.createFollowUpSchema,
        onRequest: [fastify.auth.authenticate],
        handler: studentController.createFollowUp
    })
}
