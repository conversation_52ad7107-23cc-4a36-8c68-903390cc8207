import { PrismaClient } from '@prisma/client'
import { hash } from 'bcrypt'

const prisma = new PrismaClient()

async function main() {
  try {
    // 清理现有数据
    await prisma.rolePermission.deleteMany()
    await prisma.userRole.deleteMany()
    await prisma.permission.deleteMany()
    await prisma.role.deleteMany()
    await prisma.user.deleteMany()

    // 创建权限
    const permissions = await Promise.all([
      prisma.permission.create({
        data: {
          name: 'user:create',
          description: '创建用户权限'
        }
      }),
      prisma.permission.create({
        data: {
          name: 'user:read',
          description: '读取用户权限'
        }
      }),
      prisma.permission.create({
        data: {
          name: 'user:update',
          description: '更新用户权限'
        }
      }),
      prisma.permission.create({
        data: {
          name: 'user:delete',
          description: '删除用户权限'
        }
      })
    ])

    // 创建角色
    const adminRole = await prisma.role.create({
      data: {
        name: 'admin',
        rolePermissions: {
          create: permissions.map(permission => ({
            permissionId: permission.id
          }))
        }
      }
    })

    const userRole = await prisma.role.create({
      data: {
        name: 'user',
        rolePermissions: {
          create: [
            { permissionId: permissions[1].id } // 只有读取权限
          ]
        }
      }
    })

    // 创建管理员用户
    const adminPassword = await hash('admin123', 10)
    const admin = await prisma.user.create({
      data: {
        account: 'admin',
        password: adminPassword,
        name: '系统管理员',
        userRoles: {
          create: [
            { roleId: adminRole.id }
          ]
        }
      }
    })

    console.log('数据库初始化完成!')

  } catch (error) {
    console.error('种子脚本执行出错:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main()