-- 设置客户端编码为 UTF8
SET client_encoding = 'UTF8';

-- 清理现有数据
DELETE FROM "user_institution";
DELETE FROM "operations";
DELETE FROM "menus";
DELETE FROM "role_permissions";
DELETE FROM "user_roles";
DELETE FROM "permissions";
DELETE FROM "roles";
DELETE FROM "institutions";
DELETE FROM "users";

-- 创建用户数据
INSERT INTO "users" (id, account, name, password, "createdAt", "updatedAt") VALUES
('1', 'admin', '系统管理员', '$2b$10$OeXm0OaFBJSABvnj7yGEzeVbvNBt7koITX5vDRpjImCO6oQd9Yni6', NOW(), NOW()),
('2', 'user', '普通用户', '$2b$10$OeXm0OaFBJSABvnj7yGEzeVbvNBt7koITX5vDRpjImCO6oQd9Yni6', NOW(), NOW());

-- 创建角色数据
INSERT INTO "roles" (id, name, code, description, "createdAt", "updatedAt") VALUES
('1', '超级管理员', 'SUPER_ADMIN', '系统超级管理员', NOW(), NOW()),
('2', '机构管理员', 'ORG_ADMIN', '机构管理员', NOW(), NOW()),
('3', '普通用户', 'USER', '普通用户', NOW(), NOW());

-- 创建权限数据
INSERT INTO "permissions" (id, name, code, type, description, "createdAt", "updatedAt") VALUES
-- 系统管理模块权限
('1', '系统管理菜单', 'SYSTEM_MANAGE', 'menu', '系统管理模块', NOW(), NOW()),
('2', '用户管理菜单', 'USER_MANAGE', 'menu', '用户管理模块', NOW(), NOW()),
('3', '角色管理菜单', 'ROLE_MANAGE', 'menu', '角色管理模块', NOW(), NOW()),
('4', '机构管理菜单', 'ORG_MANAGE', 'menu', '机构管理模块', NOW(), NOW()),
('5', '字典管理菜单', 'DICT_MANAGE', 'menu', '字典管理模块', NOW(), NOW()),

-- 用户管理子模块权限
('6', '用户列表菜单', 'USER_LIST', 'menu', '用户列表页面', NOW(), NOW()),
('7', '用户详情菜单', 'USER_DETAIL', 'menu', '用户详情页面', NOW(), NOW()),
('8', '用户设置菜单', 'USER_SETTING', 'menu', '用户设置页面', NOW(), NOW()),

-- 角色管理子模块权限
('9', '角色列表菜单', 'ROLE_LIST', 'menu', '角色列表页面', NOW(), NOW()),
('10', '角色详情菜单', 'ROLE_DETAIL', 'menu', '角色详情页面', NOW(), NOW()),
('11', '权限配置菜单', 'PERM_CONFIG', 'menu', '权限配置页面', NOW(), NOW()),

-- 机构管理子模块权限
('12', '机构列表菜单', 'ORG_LIST', 'menu', '机构列表页面', NOW(), NOW()),
('13', '机构详情菜单', 'ORG_DETAIL', 'menu', '机构详情页面', NOW(), NOW()),
('14', '机构审核菜单', 'ORG_AUDIT', 'menu', '机构审核页面', NOW(), NOW()),

-- 字典管理子模块权限
('15', '字典列表菜单', 'DICT_LIST', 'menu', '字典列表页面', NOW(), NOW()),
('16', '字典配置菜单', 'DICT_CONFIG', 'menu', '字典配置页面', NOW(), NOW()),

-- 用户相关操作权限
('20', '创建用户权限', 'USER_CREATE', 'operation', '创建用户操作', NOW(), NOW()),
('21', '编辑用户权限', 'USER_EDIT', 'operation', '编辑用户操作', NOW(), NOW()),
('22', '删除用户权限', 'USER_DELETE', 'operation', '删除用户操作', NOW(), NOW()),
('23', '导入用户权限', 'USER_IMPORT', 'operation', '导入用户操作', NOW(), NOW()),
('24', '导出用户权限', 'USER_EXPORT', 'operation', '导出用户操作', NOW(), NOW()),

-- 角色相关操作权限
('25', '创建角色权限', 'ROLE_CREATE', 'operation', '创建角色操作', NOW(), NOW()),
('26', '编辑角色权限', 'ROLE_EDIT', 'operation', '编辑角色操作', NOW(), NOW()),
('27', '删除角色权限', 'ROLE_DELETE', 'operation', '删除角色操作', NOW(), NOW()),
('28', '分配权限操作', 'PERM_ASSIGN', 'operation', '分配权限操作', NOW(), NOW()),

-- 机构相关操作权限
('29', '创建机构权限', 'ORG_CREATE', 'operation', '创建机构操作', NOW(), NOW()),
('30', '编辑机构权限', 'ORG_EDIT', 'operation', '编辑机构操作', NOW(), NOW()),
('31', '删除机构权限', 'ORG_DELETE', 'operation', '删除机构操作', NOW(), NOW()),
('32', '审核机构权限', 'ORG_AUDIT_OP', 'operation', '审核机构操作', NOW(), NOW());

-- 创建菜单数据
INSERT INTO "menus" (id, name, path, component, icon, sort, "permissionId", "parentId", hidden, "createdAt", "updatedAt") VALUES
-- 一级菜单
('1', '系统管理', '/system', 'Layout', 'setting', 1, '1', NULL, false, NOW(), NOW()),
('2', '机构管理', '/org', 'Layout', 'apartment', 2, '4', NULL, false, NOW(), NOW()),

-- 系统管理二级菜单
('3', '用户管理', 'user', 'system/user/index', 'user', 1, '2', '1', false, NOW(), NOW()),
('4', '角色管理', 'role', 'system/role/index', 'security-scan', 2, '3', '1', false, NOW(), NOW()),
('5', '字典管理', 'dict', 'system/dict/index', 'book', 3, '5', '1', false, NOW(), NOW()),

-- 用户管理三级菜单
('6', '用户列表', 'list', 'system/user/list/index', 'team', 1, '6', '3', false, NOW(), NOW()),
('7', '用户详情', 'detail', 'system/user/detail/index', 'profile', 2, '7', '3', false, NOW(), NOW()),
('8', '用户设置', 'setting', 'system/user/setting/index', 'tool', 3, '8', '3', false, NOW(), NOW()),

-- 角色管理三级菜单
('9', '角色列表', 'list', 'system/role/list/index', 'safety-certificate', 1, '9', '4', false, NOW(), NOW()),
('10', '角色详情', 'detail', 'system/role/detail/index', 'file-text', 2, '10', '4', false, NOW(), NOW()),
('11', '权限配置', 'permission', 'system/role/permission/index', 'key', 3, '11', '4', false, NOW(), NOW()),

-- 机构管理二级菜单
('12', '机构列表', 'list', 'org/list/index', 'cluster', 1, '12', '2', false, NOW(), NOW()),
('13', '机构详情', 'detail', 'org/detail/index', 'profile', 2, '13', '2', false, NOW(), NOW()),
('14', '机构审核', 'audit', 'org/audit/index', 'audit', 3, '14', '2', false, NOW(), NOW()),

-- 字典管理二级菜单
('15', '字典列表', 'list', 'system/dict/list/index', 'ordered-list', 1, '15', '5', false, NOW(), NOW()),
('16', '字典配置', 'config', 'system/dict/config/index', 'setting', 2, '16', '5', false, NOW(), NOW());

-- 创建操作权限数据
INSERT INTO "operations" (id, name, code, "permissionId", "createdAt", "updatedAt") VALUES
-- 用户相关操作
('1', '创建用户', 'USER_CREATE', '20', NOW(), NOW()),
('2', '编辑用户', 'USER_EDIT', '21', NOW(), NOW()),
('3', '删除用户', 'USER_DELETE', '22', NOW(), NOW()),
('4', '导入用户', 'USER_IMPORT', '23', NOW(), NOW()),
('5', '导出用户', 'USER_EXPORT', '24', NOW(), NOW()),

-- 角色相关操作
('6', '创建角色', 'ROLE_CREATE', '25', NOW(), NOW()),
('7', '编辑角色', 'ROLE_EDIT', '26', NOW(), NOW()),
('8', '删除角色', 'ROLE_DELETE', '27', NOW(), NOW()),
('9', '分配权限', 'PERM_ASSIGN', '28', NOW(), NOW()),

-- 机构相关操作
('10', '创建机构', 'ORG_CREATE', '29', NOW(), NOW()),
('11', '编辑机构', 'ORG_EDIT', '30', NOW(), NOW()),
('12', '删除机构', 'ORG_DELETE', '31', NOW(), NOW()),
('13', '审核机构', 'ORG_AUDIT_OP', '32', NOW(), NOW());

-- 创建角色-权限关联
INSERT INTO "role_permissions" (id, "roleId", "permissionId", "createdAt", "updatedAt") VALUES
-- 超级管理员权限
('1', '1', '1', NOW(), NOW()),
('2', '1', '2', NOW(), NOW()),
('3', '1', '3', NOW(), NOW()),
('4', '1', '4', NOW(), NOW()),
('5', '1', '5', NOW(), NOW()),
('6', '1', '6', NOW(), NOW()),
('7', '1', '7', NOW(), NOW()),
('11', '1', '8', NOW(), NOW()),
('12', '1', '9', NOW(), NOW()),
('13', '1', '10', NOW(), NOW()),
('14', '1', '11', NOW(), NOW()),
('15', '1', '12', NOW(), NOW()),
('16', '1', '13', NOW(), NOW()),
-- 机构管理员权限
('8', '2', '4', NOW(), NOW()),
('9', '2', '5', NOW(), NOW()),
('10', '2', '6', NOW(), NOW()),
('17', '2', '12', NOW(), NOW()),
('18', '2', '13', NOW(), NOW());

-- 创建用户-角色关联
INSERT INTO "user_roles" (id, "userId", "roleId", "createdAt", "updatedAt") VALUES
('1', '1', '1', NOW(), NOW()), -- admin -> 超级管理员
('2', '2', '3', NOW(), NOW()); -- user -> 普通用户

-- 创建机构数据
INSERT INTO "institutions" (id, name, "subjectName", uscc, logo, introduce, phone, "telePhone", "managerName", "userId", "createdAt", "updatedAt") VALUES
('1', '测试机构1', '测试主体1', '91110000123456789A', 'logo1.png', '测试机构1介绍', '13800138001', '010-12345678', '管理人1', '1', NOW(), NOW()),
('2', '测试机构2', '测试主体2', '91110000123456789B', 'logo2.png', '测试机构2介绍', '13800138002', '010-12345679', '管理人2', '2', NOW(), NOW());

-- 创建用户-机构关联
INSERT INTO "user_institution" (id, "userId", "institutionId", "createdAt", "updatedAt") VALUES
('1', '1', '1', NOW(), NOW()),
('2', '2', '2', NOW(), NOW());