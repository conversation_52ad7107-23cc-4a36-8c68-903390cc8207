"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7695],{67695:(e,t,r)=>{r.r(t),r.d(t,{default:()=>l});var a=r(12115);let l=a.forwardRef(function(e,t){let{title:r,titleId:l,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},n),r?a.createElement("title",{id:l},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))})}}]);