(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2485],{14636:(e,a,t)=>{"use strict";t.d(a,{AM:()=>i,Wv:()=>o,hl:()=>c});var s=t(95155),r=t(12115),n=t(20547),l=t(59434);let i=n.bL,o=n.l9,c=r.forwardRef((e,a)=>{let{className:t,align:r="center",sideOffset:i=4,...o}=e;return(0,s.jsx)(n.ZL,{children:(0,s.jsx)(n.UC,{ref:a,align:r,sideOffset:i,className:(0,l.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...o})})});c.displayName=n.UC.displayName},30285:(e,a,t)=>{"use strict";t.d(a,{$:()=>c,r:()=>o});var s=t(95155),r=t(12115),n=t(99708),l=t(74466),i=t(59434);let o=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,a)=>{let{className:t,variant:r,size:l,asChild:c=!1,...d}=e,u=c?n.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(o({variant:r,size:l,className:t})),ref:a,...d})});c.displayName="Button"},35046:(e,a,t)=>{Promise.resolve().then(t.bind(t,71361))},48432:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>v});var s=t(95155),r=t(12115),n=t(42355),l=t(13052),i=t(5623),o=t(59434),c=t(30285);let d=e=>{let{className:a,...t}=e;return(0,s.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,o.cn)("mx-auto flex w-full justify-center",a),...t})};d.displayName="Pagination";let u=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("ul",{ref:a,className:(0,o.cn)("flex flex-row items-center gap-1",t),...r})});u.displayName="PaginationContent";let m=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("li",{ref:a,className:(0,o.cn)("",t),...r})});m.displayName="PaginationItem";let x=e=>{let{className:a,isActive:t,size:r="icon",...n}=e;return(0,s.jsx)("a",{"aria-current":t?"page":void 0,className:(0,o.cn)((0,c.r)({variant:t?"outline":"ghost",size:r}),a),...n})};x.displayName="PaginationLink";let h=e=>{let{className:a,...t}=e;return(0,s.jsxs)(x,{"aria-label":"Go to previous page",size:"default",className:(0,o.cn)("gap-1 pl-2.5",a),...t,children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"上一页"})]})};h.displayName="PaginationPrevious";let p=e=>{let{className:a,...t}=e;return(0,s.jsxs)(x,{"aria-label":"Go to next page",size:"default",className:(0,o.cn)("gap-1 pr-2.5",a),...t,children:[(0,s.jsx)("span",{children:"下一页"}),(0,s.jsx)(l.A,{className:"h-4 w-4"})]})};p.displayName="PaginationNext";let f=e=>{let{className:a,...t}=e;return(0,s.jsxs)("span",{"aria-hidden":!0,className:(0,o.cn)("flex h-9 w-9 items-center justify-center",a),...t,children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"更多页"})]})};f.displayName="PaginationEllipsis";var g=t(59409);function v(e){let{currentPage:a,pageSize:t,totalItems:r,onPageChange:i,onPageSizeChange:o}=e,c=Math.ceil(r/t),v=(()=>{let e=[];if(c<=5){for(let a=1;a<=c;a++)e.push(a);return e}e.push(1);let t=Math.max(2,a-1),s=Math.min(a+1,c-1);2===t&&(s=Math.min(t+2,c-1)),s===c-1&&(t=Math.max(s-2,2)),t>2&&e.push("ellipsis-start");for(let a=t;a<=s;a++)e.push(a);return s<c-1&&e.push("ellipsis-end"),c>1&&e.push(c),e})(),b=0===r?0:(a-1)*t+1,j=Math.min(a*t,r);return(0,s.jsxs)("div",{className:"flex flex-col gap-4 px-2 py-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"显示"}),(0,s.jsxs)(g.l6,{value:t.toString(),onValueChange:e=>{o(Number(e))},children:[(0,s.jsx)(g.bq,{className:"w-[4.5rem] h-7 text-xs border-border/60 hover:bg-accent/30 transition-colors",children:(0,s.jsx)(g.yv,{})}),(0,s.jsx)(g.gC,{children:[10,20,30,50].map(e=>(0,s.jsx)(g.eb,{value:e.toString(),className:"text-xs",children:e},e))})]}),(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"条"}),(0,s.jsx)("span",{className:"text-muted-foreground/40 mx-1.5",children:"|"}),r>0?(0,s.jsxs)("span",{className:"text-muted-foreground/80",children:[b,"-",j," / ",r," 条记录"]}):(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"0 条记录"})]}),(0,s.jsx)(d,{children:(0,s.jsxs)(u,{className:"gap-1",children:[(0,s.jsx)(m,{children:(0,s.jsx)(h,{onClick:()=>i(Math.max(1,a-1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(1===a?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,s.jsx)(n.A,{className:"h-4 w-4 mr-1"})})}),v.map((e,t)=>"ellipsis-start"===e||"ellipsis-end"===e?(0,s.jsx)(m,{children:(0,s.jsx)(f,{className:"h-8 w-8 flex items-center justify-center text-xs"})},"ellipsis-".concat(t)):(0,s.jsx)(m,{children:(0,s.jsx)(x,{onClick:()=>i(e),isActive:a===e,className:"h-8 w-8 text-xs font-medium rounded-md transition-colors data-[active]:bg-primary data-[active]:text-primary-foreground hover:bg-accent/50 hover:text-accent-foreground/90","aria-label":"前往第 ".concat(e," 页"),children:e})},e)),(0,s.jsx)(m,{children:(0,s.jsx)(p,{onClick:()=>i(Math.min(c,a+1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(a===c||0===c?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,s.jsx)(l.A,{className:"h-4 w-4 ml-1"})})})]})})]})}},59434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>n});var s=t(52596),r=t(39688);function n(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}},62523:(e,a,t)=>{"use strict";t.d(a,{p:()=>l});var s=t(95155),r=t(12115),n=t(59434);let l=r.forwardRef((e,a)=>{let{className:t,type:r,...l}=e;return(0,s.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...l})});l.displayName="Input"},71361:(e,a,t)=>{"use strict";t.d(a,{default:()=>y});var s=t(95155),r=t(12115),n=t(9110),l=t(73069),i=t(55028);let o=(0,i.default)(()=>t.e(7953).then(t.bind(t,47953)),{loadableGenerated:{webpack:()=>[47953]},ssr:!1}),c=(0,i.default)(()=>t.e(8951).then(t.bind(t,18951)),{loadableGenerated:{webpack:()=>[18951]},ssr:!1}),d=[{accessorKey:"name",header:"教室名称",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"font-medium text-slate-800",children:a.original.name})}},{accessorKey:"address",header:"所属地址",cell:e=>{let{row:a}=e,t=a.original;return(0,s.jsx)("div",{className:"flex items-center gap-1",children:(0,s.jsx)(l.c,{className:"text-slate-700",children:"".concat(null==t?void 0:t.province).concat(null==t?void 0:t.city).concat(t.district).concat(t.address)})})}},{accessorKey:"capacity",header:"容纳人数",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"text-slate-700",children:a.original.capacity})}},{id:"actions",header:"操作",cell:e=>{let{row:a}=e,t=a.original;return(0,s.jsx)("div",{className:"flex space-x-1",children:(0,s.jsxs)("div",{className:"flex items-center gap-1 justify-end",children:[(0,s.jsx)(o,{classRoom:t}),(0,s.jsx)(c,{id:t.id})]})})}}];var u=t(48432),m=t(62523),x=t(12318),h=t(47924),p=t(30285),f=t(48436),g=t(12519),v=t(45964),b=t.n(v);let j=(0,i.default)(()=>t.e(5756).then(t.bind(t,95756)),{loadableGenerated:{webpack:()=>[95756]},ssr:!1}),N=function(){let[e,a]=(0,r.useState)({currentPage:1,pageSize:10,totalItems:0}),[t,l]=(0,r.useState)(""),[i,o]=(0,r.useState)(""),[c,v]=(0,r.useState)(!1),N=(0,r.useMemo)(()=>b()(e=>{o(e),a(e=>({...e,currentPage:1}))},300),[]),y=(0,r.useCallback)(e=>{let a=e.target.value;l(a),N(a)},[N]);(0,r.useEffect)(()=>()=>{N.cancel()},[N]);let w=(0,r.useMemo)(()=>({page:e.currentPage,pageSize:e.pageSize,search:i}),[e.currentPage,e.pageSize,i]),{data:k,isLoading:C}=(0,g.KN)(w),[P]=(0,g.YC)(),S=(0,r.useCallback)(async e=>{try{await P(e),f.l.success("添加教室成功"),v(!1)}catch(e){f.l.error((null==e?void 0:e.message)||"教室添加失败！")}},[P]),z=(0,r.useCallback)(e=>{a(a=>({...a,currentPage:e}))},[]),A=(0,r.useCallback)(e=>{a(a=>({...a,pageSize:e,currentPage:1}))},[]);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-4 border-b border-gray-100",children:[(0,s.jsx)("h2",{className:"text-xl font-medium text-gray-800",children:"教室管理"}),(0,s.jsxs)(p.$,{onClick:()=>v(!0),className:"h-10 gap-1.5",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),"新建教室"]})]}),(0,s.jsx)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:(0,s.jsx)("div",{className:"flex flex-wrap items-center gap-3 w-full sm:w-auto",children:(0,s.jsxs)("div",{className:"relative flex-1 w-full sm:w-auto flex",children:[(0,s.jsx)(h.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 pointer-events-none"}),(0,s.jsx)(m.p,{type:"search",placeholder:"搜索教室...",className:"pl-10 h-10 w-full sm:w-[250px] lg:w-[320px] rounded-md border-gray-200 shadow-sm transition-all hover:border-gray-300",value:t,onChange:y})]})})}),(0,s.jsx)(n.b,{columns:d,data:(null==k?void 0:k.list)||[],pagination:!1,loading:C}),(0,s.jsx)(u.default,{currentPage:(null==k?void 0:k.page)||1,pageSize:(null==k?void 0:k.pageSize)||10,totalItems:(null==k?void 0:k.total)||0,onPageChange:z,onPageSizeChange:A}),c&&(0,s.jsx)(j,{open:c,onOpenChange:v,handleSave:S})]})};function y(){return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(N,{})})}},73069:(e,a,t)=>{"use strict";t.d(a,{c:()=>m});var s=t(95155),r=t(12115),n=t(47863),l=t(66474),i=t(5196),o=t(24357),c=t(59434),d=t(14636),u=t(30285);function m(e){let{children:a,maxDisplayLength:t=15,className:m,popoverWidth:x="auto",showBorder:h=!1}=e,[p,f]=r.useState(!1),[g,v]=r.useState(!1),b=r.useMemo(()=>{if("string"==typeof a||"number"==typeof a)return a.toString();try{var e;let t=document.createElement("div");return t.innerHTML=(null==a?void 0:null===(e=a.props)||void 0===e?void 0:e.children)||"",t.textContent||""}catch(e){return console.warn("Could not extract text from children:",e),""}},[a]),j=r.useMemo(()=>{if("string"==typeof a||"number"==typeof a){let e=a.toString();return e.length>t?e.slice(0,t):e}return a},[a,t]),N=async()=>{try{await navigator.clipboard.writeText(b),v(!0),setTimeout(()=>v(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,s.jsxs)(d.AM,{open:p,onOpenChange:f,children:[(0,s.jsx)(d.Wv,{asChild:!0,children:(0,s.jsxs)(u.$,{variant:h?"outline":"ghost",role:"combobox","aria-expanded":p,className:(0,c.cn)("w-fit justify-between font-normal px-2 py-1 h-auto",!h&&"border-0 shadow-none",m),children:[(0,s.jsx)("span",{className:"mr-2 truncate",children:j}),p?(0,s.jsx)(n.A,{className:"h-4 w-4 shrink-0 opacity-50"}):(0,s.jsx)(l.A,{className:"h-4 w-4 shrink-0 opacity-50"})]})}),(0,s.jsx)(d.hl,{className:"p-0",align:"start",style:{width:x},children:(0,s.jsxs)("div",{className:"flex items-center gap-2 p-2",children:[(0,s.jsx)("span",{className:"text-sm break-all",children:b}),(0,s.jsxs)(u.$,{variant:"ghost",size:"icon",className:"h-8 w-8 shrink-0",onClick:N,children:[g?(0,s.jsx)(i.A,{className:"h-4 w-4"}):(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:g?"Copied":"Copy text"})]})]})})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,8687,4201,8737,4540,4582,5620,9613,7945,8897,9624,9110,6315,7358],()=>a(35046)),_N_E=e.O()}]);