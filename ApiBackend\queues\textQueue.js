/**
 * Text generation queue for BullMQ
 * Handles text generation tasks
 */

import { Queue } from "bullmq";
import { redisConfig } from "../config/redis.js";
import { defaultQueueOptions } from "./queueConfig.js";

// Queue configuration with specific settings for text generation
const queueOptions = {
  ...defaultQueueOptions,
  limiter: {
    max: 5,
    duration: 1000,
    groupKey: 'text-generation'
  }
};

// Create the queue
const textQueue = new Queue('text-generation', queueOptions);

// Add error handling for the queue
textQueue.on('error', (error) => {
  console.error('Text generation queue error:', error);
});

textQueue.on('failed', (job, error) => {
  console.error(`Text generation job ${job.id} failed:`, error);
});

/**
 * Add a text generation job to the queue
 * @param {string} taskId - The ID of the task
 * @param {string} prompt - The prompt for text generation
 * @param {number} maxTokens - Maximum number of tokens to generate
 * @param {number} temperature - Temperature for text generation
 * @param {string} model - Model to use for text generation
 * @returns {Promise<Job>} - The created job
 */
export async function addTextGenerationJob(taskId, prompt, maxTokens, temperature, model) {
  try {
    return await textQueue.add('text-generation', {
      taskId,
      type: 'text-generation',
      prompt,
      maxTokens,
      temperature,
      model
    });
  } catch (error) {
    console.error('Error adding text generation job:', error);
    throw error;
  }
}

export default textQueue;
