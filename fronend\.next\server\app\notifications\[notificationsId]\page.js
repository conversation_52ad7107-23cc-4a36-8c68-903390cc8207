(()=>{var e={};e.id=7023,e.ids=[7023],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39907:(e,t,r)=>{"use strict";r.d(t,{w:()=>l});var s=r(60687),a=r(43210),i=r(14163),n="horizontal",o=["horizontal","vertical"],c=a.forwardRef((e,t)=>{var r;let{decorative:a,orientation:c=n,...d}=e,l=(r=c,o.includes(r))?c:n;return(0,s.jsx)(i.sG.div,{"data-orientation":l,...a?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...d,ref:t})});c.displayName="Separator";var d=r(4780);let l=a.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...a},i)=>(0,s.jsx)(c,{ref:i,decorative:r,orientation:t,className:(0,d.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...a}));l.displayName=c.displayName},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},59659:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(60687),a=r(76869),i=r(16189),n=r(40228),o=r(58869),c=r(39907),d=r(58876),l=r(85726);let p=function(){let{notificationsId:e}=(0,i.useParams)(),{data:t,isLoading:r}=(0,d.gG)(e);return(console.log(t),r)?(0,s.jsxs)("div",{className:"space-y-6 p-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.E,{className:"h-8 w-3/4"}),(0,s.jsx)(l.E,{className:"h-4 w-1/4"})]}),(0,s.jsx)(c.w,{className:"my-6"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(l.E,{className:"h-4 w-full"}),(0,s.jsx)(l.E,{className:"h-4 w-full"}),(0,s.jsx)(l.E,{className:"h-4 w-3/4"})]})]}):t?(0,s.jsxs)("div",{className:"space-y-6 p-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-gray-900 mb-4",children:t.title}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-6 text-sm text-muted-foreground",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,s.jsx)(n.A,{className:"h-4 w-4 opacity-70"}),(0,s.jsx)("time",{dateTime:t.createdAt,children:(0,a.GP)(new Date(t.createdAt),"yyyy-MM-dd HH:mm:ss")})]}),t.creatorName&&(0,s.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,s.jsx)(o.A,{className:"h-4 w-4 opacity-70"}),(0,s.jsx)("span",{children:t.creatorName})]})]})]}),(0,s.jsx)(c.w,{className:"my-6"}),(0,s.jsx)("div",{className:"text-gray-800 leading-relaxed",children:(0,s.jsx)("div",{className:"prose prose-slate max-w-none",children:t.content})})]}):(0,s.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:"未找到通知详情"})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64453:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\notifications\\\\[notificationsId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\notifications\\[notificationsId]\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},76361:(e,t,r)=>{Promise.resolve().then(r.bind(r,59659))},78347:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let d={children:["",{children:["notifications",{children:["[notificationsId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,64453)),"F:\\trae\\cardmees\\fronend\\src\\app\\notifications\\[notificationsId]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94764)),"F:\\trae\\cardmees\\fronend\\src\\app\\notifications\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["F:\\trae\\cardmees\\fronend\\src\\app\\notifications\\[notificationsId]\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/notifications/[notificationsId]/page",pathname:"/notifications/[notificationsId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85726:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(60687),a=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...t})}},86089:(e,t,r)=>{Promise.resolve().then(r.bind(r,64453))},94735:e=>{"use strict";e.exports=require("events")},94764:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>n});var s=r(37413),a=r(24597),i=r(36733);let n={title:"CardMees",description:"CardMees Application"};function o({children:e}){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{}),(0,s.jsxs)("div",{className:"flex h-[calc(100vh)]",children:[(0,s.jsx)(i.default,{}),(0,s.jsx)("main",{className:"flex-1 p-8 pt-16 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400",children:(0,s.jsx)("div",{className:"max-w-8xl mx-auto bg-white rounded-lg shadow-sm",children:e})})]})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7392,5814,3928,3443,6869,3019,9879],()=>r(78347));module.exports=s})();