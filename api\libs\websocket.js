

// 发送给指定用户
export async function sendToSpecifiedUser(fastify, userId, institutionId, payload) {
    console.log('发送给指定用户', userId, institutionId)
    const clients = fastify.websocketServer.clients;
    for (const client of clients) {
        if (client.userId === userId && client.instId === institutionId) {
            client.send(JSON.stringify(payload));
        }
    }
}

// 发送给所有用户
export async function sendToAllUsers(fastify, payload) {
    const clients = fastify.websocketServer.clients;
    for (const client of clients) {
        client.send(JSON.stringify(payload));
    }
}
// 发送给机构内所有用户
export async function sendToAllUsersInInstitution(fastify, institutionId, payload) {
    const clients = fastify.websocketServer.clients;
    for (const client of clients) {
        if (client.instId === institutionId) {
            client.send(JSON.stringify(payload));
        }
    }
}

// 发送给机构内所有用户，不包括指定用户
export async function sendToAllUsersInInstitutionExceptUser(fastify, institutionId, userId, payload) {

    const clients = fastify.websocketServer.clients;
    for (const client of clients) {
        if (client.instId === institutionId && client.userId !== userId) {
            client.send(JSON.stringify(payload));
        }
    }
}





