import fetch from 'node-fetch';
import uploadToQiniu from "../libs/qiniu.js";
import { v4 as uuidv4 } from 'uuid';

/**
 * 生成图像
 * @param {string} prompt - 提示词
 * @param {number} count - 生成图片数量
 * @param {string} size - 图片尺寸
 * @param {string} style - 图片风格
 * @returns {Promise<Object>} 生成结果
 */
export async function generateImage(prompt, count = 1, size = '1024x1024', style = 'realistic') {
    try {
        const startTime = Date.now();

        // 根据风格调整提示词
        let stylePrompt = prompt;
        if (style === 'cartoon') {
            stylePrompt = `${prompt}, cartoon style, animated, vibrant colors`;
        } else if (style === 'artistic') {
            stylePrompt = `${prompt}, artistic style, oil painting, masterpiece, detailed`;
        } else if (style === 'sketch') {
            stylePrompt = `${prompt}, sketch style, pencil drawing, black and white, detailed lines`;
        }

        const response = await fetch('https://qianfan.baidubce.com/v2/images/generations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.BAIDU_API_KEY}`
            },
            body: JSON.stringify({
                prompt: stylePrompt,
                n: count,
                model: "irag-1.0",
                size: size
            })
        });

        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.statusText}`);
        }

        const data = await response.json();
        if (!data.data?.[0]?.url) {
            throw new Error('未返回有效图片 URL');
        }

        // 生成图片时间
        const generateTime = Date.now() - startTime;
        console.log(`图片生成完成，用时 ${generateTime} 毫秒`);

        // 上传到七牛云
        const imageUrls = await Promise.all(data.data.map(async (item) => {
            const id = uuidv4();
            return await uploadToQiniu(item.url, id);
        }));

        const endTime = Date.now();
        const duration = endTime - startTime;
        console.log(`图片上传完成，用时 ${duration} 毫秒`);

        return {
            id: data.id,
            created: data.created,
            imageUrl: imageUrls,
            style: style
        };
    } catch (error) {
        throw new Error(`图像生成失败: ${error.message}`);
    }
}

/**
 * 生成图像变体
 * @param {string} imageUrl - 原始图片URL
 * @param {number} count - 生成变体数量
 * @param {number} variationStrength - 变化强度 (0-1)
 * @returns {Promise<Object>} 生成结果
 */
export async function generateImageVariation(imageUrl, count = 1, variationStrength = 0.5) {
    try {
        const startTime = Date.now();

        // 下载原始图像
        const imageResponse = await fetch(imageUrl);
        if (!imageResponse.ok) {
            throw new Error(`下载原始图像失败: ${imageResponse.statusText}`);
        }

        // 获取图像数据
        const imageBuffer = await imageResponse.buffer();

        // 调用百度文心千帆图像变体API
        const response = await fetch('https://qianfan.baidubce.com/v2/images/variations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.BAIDU_API_KEY}`
            },
            body: JSON.stringify({
                image: imageBuffer.toString('base64'),
                n: count,
                model: "irag-1.0",
                variation_strength: variationStrength
            })
        });

        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.statusText}`);
        }

        const data = await response.json();
        if (!data.data?.[0]?.url) {
            throw new Error('未返回有效图片 URL');
        }

        // 生成图片时间
        const generateTime = Date.now() - startTime;
        console.log(`图片变体生成完成，用时 ${generateTime} 毫秒`);

        // 上传到七牛云
        const imageUrls = await Promise.all(data.data.map(async (item) => {
            const id = uuidv4();
            return await uploadToQiniu(item.url, id);
        }));

        const endTime = Date.now();
        const duration = endTime - startTime;
        console.log(`图片变体上传完成，用时 ${duration} 毫秒`);

        return {
            id: data.id,
            created: data.created,
            originalImage: imageUrl,
            variationStrength: variationStrength,
            imageUrl: imageUrls
        };
    } catch (error) {
        throw new Error(`图像变体生成失败: ${error.message}`);
    }
}