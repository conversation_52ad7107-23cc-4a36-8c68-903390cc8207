import { INTERNAL_ERROR, NOT_FOUND_ERROR } from '../errors/index.js';
import { classesService } from '../services/classesService.js';

/**
 * Classes Controller
 * Handles HTTP requests and responses for class-related endpoints
 */
export const classesController = {
    /**
     * Get classes list
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async getClassesList(request, reply) {
        try {
            const { page = 1, pageSize = 10, search, status } = request.query;
            const institutionId = request.user.institutionId;
            
            // Call service to get classes list
            const result = await classesService.getClassesList({
                institutionId,
                page,
                pageSize,
                search,
                status,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: result,
                message: '获取班级列表成功'
            });
        } catch (error) {
            request.log.error({
                msg: '获取班级列表失败',
                error: error.message,
                stack: error.stack,
                query: request.query
            });
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('获取班级列表失败');
        }
    },
    
    /**
     * Get class by ID
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async getClassById(request, reply) {
        try {
            const { classId } = request.params;
            const institutionId = request.user.institutionId;
            
            // Call service to get class by ID
            const classData = await classesService.getClassById({
                classId,
                institutionId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: classData,
                message: '获取班级信息成功'
            });
        } catch (error) {
            request.log.error({
                msg: '获取班级信息失败',
                error: error.message,
                stack: error.stack,
                params: request.params
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('获取班级信息失败');
        }
    },
    
    /**
     * Create class
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async createClass(request, reply) {
        try {
            const classData = request.body;
            const institutionId = request.user.institutionId;
            const userId = request.user.id;
            
            // Call service to create class
            const result = await classesService.createClass({
                classData,
                institutionId,
                userId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                data: result,
                message: '创建班级成功'
            });
        } catch (error) {
            request.log.error({
                msg: '创建班级失败',
                error: error.message,
                stack: error.stack,
                body: request.body
            });
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('创建班级失败');
        }
    },
    
    /**
     * Update class
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async updateClass(request, reply) {
        try {
            const { classId } = request.params;
            const classData = request.body;
            const institutionId = request.user.institutionId;
            const userId = request.user.id;
            
            // Call service to update class
            await classesService.updateClass({
                classId,
                classData,
                institutionId,
                userId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                message: '更新班级成功'
            });
        } catch (error) {
            request.log.error({
                msg: '更新班级失败',
                error: error.message,
                stack: error.stack,
                params: request.params,
                body: request.body
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('更新班级失败');
        }
    },
    
    /**
     * Delete class
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async deleteClass(request, reply) {
        try {
            const { classId } = request.params;
            const institutionId = request.user.institutionId;
            const userId = request.user.id;
            
            // Call service to delete class
            await classesService.deleteClass({
                classId,
                institutionId,
                userId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                message: '删除班级成功'
            });
        } catch (error) {
            request.log.error({
                msg: '删除班级失败',
                error: error.message,
                stack: error.stack,
                params: request.params
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('删除班级失败');
        }
    },
    
    /**
     * Add student to class
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async addStudentToClass(request, reply) {
        try {
            const { classId } = request.params;
            const { studentId } = request.body;
            const institutionId = request.user.institutionId;
            const userId = request.user.id;
            
            // Call service to add student to class
            await classesService.addStudentToClass({
                classId,
                studentId,
                institutionId,
                userId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                message: '添加学生到班级成功'
            });
        } catch (error) {
            request.log.error({
                msg: '添加学生到班级失败',
                error: error.message,
                stack: error.stack,
                params: request.params,
                body: request.body
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('添加学生到班级失败');
        }
    },
    
    /**
     * Remove student from class
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async removeStudentFromClass(request, reply) {
        try {
            const { classId, studentId } = request.params;
            const institutionId = request.user.institutionId;
            const userId = request.user.id;
            
            // Call service to remove student from class
            await classesService.removeStudentFromClass({
                classId,
                studentId,
                institutionId,
                userId,
                fastify: request.server
            });
            
            // Return success response
            return reply.success({
                message: '从班级移除学生成功'
            });
        } catch (error) {
            request.log.error({
                msg: '从班级移除学生失败',
                error: error.message,
                stack: error.stack,
                params: request.params
            });
            
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            
            if (error instanceof INTERNAL_ERROR) {
                throw error;
            }
            
            throw new INTERNAL_ERROR('从班级移除学生失败');
        }
    }
};

export default classesController;
