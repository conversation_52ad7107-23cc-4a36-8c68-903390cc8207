"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2144],{62144:(e,a,l)=>{l.r(a),l.d(a,{default:()=>f});var s=l(95155),t=l(12115),r=l(9110),n=l(73168),c=l(24122),d=l(50228);let i=[{accessorKey:"name",header:"姓名",cell:e=>{let{row:a}=e;return(0,d.P)(a.original.student.name)}},{accessorKey:"courseName",header:"课程名称",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"px-2 py-1 text-sm rounded-md bg-blue-50 text-blue-700 inline-block",children:a.original.classesSchedule.courses.name})}},{accessorKey:"className",header:"班级名称",cell:e=>{let{row:a}=e;return(0,d.s)(a.original.classesSchedule.classes.name,4)}},{accessorKey:"startTime",header:"上课时间",cell:e=>{let{row:a}=e,l=a.original.classesSchedule.startDate,t=a.original.classesSchedule.startTime,r=a.original.classesSchedule.endTime;return(0,s.jsx)("div",{className:"text-gray-600 text-sm font-light",children:"".concat((0,n.GP)(new Date(l),"yyyy-MM-dd(EEE)",{locale:c.g})," ").concat(t,"-").concat(r)})}},{accessorKey:"teacher",header:"授课老师",cell:e=>{let{row:a}=e;return(0,s.jsxs)("div",{className:"text-gray-700 flex items-center gap-1",children:[(0,s.jsx)("span",{className:"w-2 h-2 rounded-full bg-emerald-400"}),a.original.classesSchedule.teacher.name]})}},{accessorKey:"productName",header:"套餐名称",cell:e=>{var a;let{row:l}=e;return(0,d.P)(null===(a=l.original.product)||void 0===a?void 0:a.name)}},{accessorKey:"attendanceCount",header:"消课次数",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"font-medium text-slate-900",children:a.original.attendanceCount})}},{accessorKey:"attendanceAmount",header:"消课金额",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"font-medium text-amber-600",children:a.original.attendanceAmount})}},{accessorKey:"operatorTime",header:"操作时间",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"text-xs text-gray-500",children:(0,n.GP)(new Date(a.original.operatorTime),"yyyy-MM-dd HH:mm:ss")})}},{accessorKey:"operatorName",header:"操作人",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"text-gray-600 text-sm",children:a.original.operator.name})}}];var o=l(48432),m=l(62523),u=l(47924),h=l(1482),x=l(66695),g=l(82007),j=l(58012),p=l(63375),v=l(45964),y=l.n(v),b=l(68856),N=l(95728);function f(){let[e,a]=(0,t.useState)(1),[l,n]=(0,t.useState)(10),[c,d]=(0,t.useState)(""),[v,f]=(0,t.useState)("last7days"),[w,S]=(0,t.useState)(void 0),[C,K]=(0,t.useState)("all"),k=(0,t.useCallback)(y()(e=>{d(e),a(1)},500),[]),A=(0,t.useMemo)(()=>{let{startTime:a,endTime:s}=(0,g.A)(v,w);return{page:e,pageSize:l,search:c,teacherId:"all"!==C?C:"",...a&&{startTime:a},...s&&{endTime:s}}},[e,l,c,C,v,w]),{data:T,isLoading:P}=(0,N.sq)(A);return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(x.Zp,{className:"border-slate-200",children:(0,s.jsx)(x.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-end md:justify-between",children:[(0,s.jsx)("div",{className:"w-full md:w-auto md:flex-1 md:max-w-md",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,s.jsx)(m.p,{type:"text",placeholder:"搜索学员姓名",onChange:e=>k(e.target.value),className:"pl-10 bg-slate-50 border-slate-200 focus:bg-white transition-colors"})]})}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 text-slate-500"}),(0,s.jsx)("span",{className:"text-sm text-slate-500 hidden sm:inline",children:"筛选："})]}),(0,s.jsx)(t.Suspense,{fallback:(0,s.jsx)(b.E,{className:"w-16 h-4 rounded-md"}),children:(0,s.jsx)(p.A,{teacher:C,setTeacher:K,width:"w-[140px]"})}),(0,s.jsx)(j.A,{timeRange:v,onTimeRangeChange:f,dateRange:w,onDateRangeChange:S})]})]})})}),(0,s.jsx)(r.b,{pagination:!1,loading:P,columns:i,data:(null==T?void 0:T.list)||[]},"studentAttendanceTable"),(0,s.jsx)(o.default,{currentPage:(null==T?void 0:T.page)||1,totalItems:(null==T?void 0:T.total)||0,pageSize:(null==T?void 0:T.pageSize)||10,onPageChange:a,onPageSizeChange:n})]})}},63375:(e,a,l)=>{l.d(a,{A:()=>d});var s=l(95155),t=l(12115),r=l(59409),n=l(59434),c=l(6658);let d=(0,t.memo)(function(e){let{teacher:a,setTeacher:l,width:d="w-full",placeholder:i="选择人员",className:o="",showAllOption:m=!0,allOptionText:u="全部人员",allOptionValue:h="all",teacherList:x,disabled:g=!1}=e,{data:j,isLoading:p,error:v}=(0,c.X)(),y=(0,t.useCallback)(e=>{l(e)},[l]),b=(0,t.useCallback)(()=>(0,n.cn)("h-9 ".concat(d),o),[d,o]),N=x||j;return(0,s.jsxs)(r.l6,{value:a,onValueChange:y,disabled:g||p,children:[(0,s.jsx)(r.bq,{className:b(),children:(0,s.jsx)(r.yv,{placeholder:i})}),(0,s.jsxs)(r.gC,{children:[v&&(0,s.jsx)(r.eb,{value:"error",disabled:!0,children:String(v)}),p&&(0,s.jsx)(r.eb,{value:"loading",disabled:!0,children:"加载中..."}),!p&&!v&&(0,s.jsxs)(s.Fragment,{children:[m&&(0,s.jsx)(r.eb,{value:h,children:u}),null==N?void 0:N.map(e=>(0,s.jsx)(r.eb,{value:e.id,children:e.name},e.id))]})]})]})})}}]);