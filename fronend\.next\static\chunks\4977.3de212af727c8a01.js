"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4977],{10081:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},22436:(e,t,r)=>{var n=r(12115),l="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,u=n.useEffect,i=n.useLayoutEffect,o=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!l(e,r)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),l=n[0].inst,d=n[1];return i(function(){l.value=r,l.getSnapshot=t,c(l)&&d({inst:l})},[e,r,t]),u(function(){return c(l)&&d({inst:l}),e(function(){c(l)&&d({inst:l})})},[e]),o(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:d},47924:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},49033:(e,t,r)=>{e.exports=r(22436)},77740:(e,t,r)=>{r.d(t,{uB:()=>P});var n=/[\\\/_+.#"@\[\(\{&]/,l=/[\\\/_+.#"@\[\(\{&]/g,a=/[\s-]/,u=/[\s-]/g;function i(e){return e.toLowerCase().replace(u," ")}var o=r(15452),c=r(12115),d=r(63655),s=r(61285),f=r(49033),v='[cmdk-group=""]',m='[cmdk-group-items=""]',p='[cmdk-item=""]',h="".concat(p,':not([aria-disabled="true"])'),g="cmdk-item-select",b="data-value",E=(e,t,r)=>(function(e,t,r){return function e(t,r,i,o,c,d,s){if(d===r.length)return c===t.length?1:.99;var f=`${c},${d}`;if(void 0!==s[f])return s[f];for(var v,m,p,h,g=o.charAt(d),b=i.indexOf(g,c),E=0;b>=0;)(v=e(t,r,i,o,b+1,d+1,s))>E&&(b===c?v*=1:n.test(t.charAt(b-1))?(v*=.8,(p=t.slice(c,b-1).match(l))&&c>0&&(v*=Math.pow(.999,p.length))):a.test(t.charAt(b-1))?(v*=.9,(h=t.slice(c,b-1).match(u))&&c>0&&(v*=Math.pow(.999,h.length))):(v*=.17,c>0&&(v*=Math.pow(.999,b-c))),t.charAt(b)!==r.charAt(d)&&(v*=.9999)),(v<.1&&i.charAt(b-1)===o.charAt(d+1)||o.charAt(d+1)===o.charAt(d)&&i.charAt(b-1)!==o.charAt(d))&&.1*(m=e(t,r,i,o,b+1,d+2,s))>v&&(v=.1*m),v>E&&(E=v),b=i.indexOf(g,b+1);return s[f]=E,E}(e=r&&r.length>0?`${e+" "+r.join(" ")}`:e,t,i(e),i(t),0,0,{})})(e,t,r),w=c.createContext(void 0),y=()=>c.useContext(w),S=c.createContext(void 0),k=()=>c.useContext(S),C=c.createContext(void 0),x=c.forwardRef((e,t)=>{let r=O(()=>{var t,r;return{search:"",value:null!=(r=null!=(t=e.value)?t:e.defaultValue)?r:"",filtered:{count:0,items:new Map,groups:new Set}}}),n=O(()=>new Set),l=O(()=>new Map),a=O(()=>new Map),u=O(()=>new Set),i=q(e),{label:o,children:f,value:y,onValueChange:k,filter:C,shouldFilter:x,loop:A,disablePointerSelection:R=!1,vimBindings:I=!0,...M}=e,D=(0,s.B)(),L=(0,s.B)(),P=(0,s.B)(),j=c.useRef(null),B=K();G(()=>{if(void 0!==y){let e=y.trim();r.current.value=e,F.emit()}},[y]),G(()=>{B(6,J)},[]);let F=c.useMemo(()=>({subscribe:e=>(u.current.add(e),()=>u.current.delete(e)),snapshot:()=>r.current,setState:(e,t,n)=>{var l,a,u;if(!Object.is(r.current[e],t)){if(r.current[e]=t,"search"===e)H(),z(),B(1,$);else if("value"===e&&(n||B(5,J),(null==(l=i.current)?void 0:l.value)!==void 0)){null==(u=(a=i.current).onValueChange)||u.call(a,null!=t?t:"");return}F.emit()}},emit:()=>{u.current.forEach(e=>e())}}),[]),_=c.useMemo(()=>({value:(e,t,n)=>{var l;t!==(null==(l=a.current.get(e))?void 0:l.value)&&(a.current.set(e,{value:t,keywords:n}),r.current.filtered.items.set(e,U(t,n)),B(2,()=>{z(),F.emit()}))},item:(e,t)=>(n.current.add(e),t&&(l.current.has(t)?l.current.get(t).add(e):l.current.set(t,new Set([e]))),B(3,()=>{H(),z(),r.current.value||$(),F.emit()}),()=>{a.current.delete(e),n.current.delete(e),r.current.filtered.items.delete(e);let t=W();B(4,()=>{H(),(null==t?void 0:t.getAttribute("id"))===e&&$(),F.emit()})}),group:e=>(l.current.has(e)||l.current.set(e,new Set),()=>{a.current.delete(e),l.current.delete(e)}),filter:()=>i.current.shouldFilter,label:o||e["aria-label"],getDisablePointerSelection:()=>i.current.disablePointerSelection,listId:D,inputId:P,labelId:L,listInnerRef:j}),[]);function U(e,t){var n,l;let a=null!=(l=null==(n=i.current)?void 0:n.filter)?l:E;return e?a(e,r.current.search,t):0}function z(){if(!r.current.search||!1===i.current.shouldFilter)return;let e=r.current.filtered.items,t=[];r.current.filtered.groups.forEach(r=>{let n=l.current.get(r),a=0;n.forEach(t=>{a=Math.max(e.get(t),a)}),t.push([r,a])});let n=j.current;Z().sort((t,r)=>{var n,l;let a=t.getAttribute("id"),u=r.getAttribute("id");return(null!=(n=e.get(u))?n:0)-(null!=(l=e.get(a))?l:0)}).forEach(e=>{let t=e.closest(m);t?t.appendChild(e.parentElement===t?e:e.closest("".concat(m," > *"))):n.appendChild(e.parentElement===n?e:e.closest("".concat(m," > *")))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{var t;let r=null==(t=j.current)?void 0:t.querySelector("".concat(v,"[").concat(b,'="').concat(encodeURIComponent(e[0]),'"]'));null==r||r.parentElement.appendChild(r)})}function $(){let e=Z().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(b);F.setState("value",t||void 0)}function H(){var e,t,u,o;if(!r.current.search||!1===i.current.shouldFilter){r.current.filtered.count=n.current.size;return}r.current.filtered.groups=new Set;let c=0;for(let l of n.current){let n=U(null!=(t=null==(e=a.current.get(l))?void 0:e.value)?t:"",null!=(o=null==(u=a.current.get(l))?void 0:u.keywords)?o:[]);r.current.filtered.items.set(l,n),n>0&&c++}for(let[e,t]of l.current)for(let n of t)if(r.current.filtered.items.get(n)>0){r.current.filtered.groups.add(e);break}r.current.filtered.count=c}function J(){var e,t,r;let n=W();n&&((null==(e=n.parentElement)?void 0:e.firstChild)===n&&(null==(r=null==(t=n.closest(v))?void 0:t.querySelector('[cmdk-group-heading=""]'))||r.scrollIntoView({block:"nearest"})),n.scrollIntoView({block:"nearest"}))}function W(){var e;return null==(e=j.current)?void 0:e.querySelector("".concat(p,'[aria-selected="true"]'))}function Z(){var e;return Array.from((null==(e=j.current)?void 0:e.querySelectorAll(h))||[])}function Q(e){let t=Z()[e];t&&F.setState("value",t.getAttribute(b))}function T(e){var t;let r=W(),n=Z(),l=n.findIndex(e=>e===r),a=n[l+e];null!=(t=i.current)&&t.loop&&(a=l+e<0?n[n.length-1]:l+e===n.length?n[0]:n[l+e]),a&&F.setState("value",a.getAttribute(b))}function X(e){let t=W(),r=null==t?void 0:t.closest(v),n;for(;r&&!n;)n=null==(r=e>0?function(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}(r,v):function(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}(r,v))?void 0:r.querySelector(h);n?F.setState("value",n.getAttribute(b)):T(e)}let Y=()=>Q(Z().length-1),ee=e=>{e.preventDefault(),e.metaKey?Y():e.altKey?X(1):T(1)},et=e=>{e.preventDefault(),e.metaKey?Q(0):e.altKey?X(-1):T(-1)};return c.createElement(d.sG.div,{ref:t,tabIndex:-1,...M,"cmdk-root":"",onKeyDown:e=>{var t;if(null==(t=M.onKeyDown)||t.call(M,e),!e.defaultPrevented)switch(e.key){case"n":case"j":I&&e.ctrlKey&&ee(e);break;case"ArrowDown":ee(e);break;case"p":case"k":I&&e.ctrlKey&&et(e);break;case"ArrowUp":et(e);break;case"Home":e.preventDefault(),Q(0);break;case"End":e.preventDefault(),Y();break;case"Enter":if(!e.nativeEvent.isComposing&&229!==e.keyCode){e.preventDefault();let t=W();if(t){let e=new Event(g);t.dispatchEvent(e)}}}}},c.createElement("label",{"cmdk-label":"",htmlFor:_.inputId,id:_.labelId,style:N},o),V(e,e=>c.createElement(S.Provider,{value:F},c.createElement(w.Provider,{value:_},e))))}),A=c.forwardRef((e,t)=>{var r,n;let l=(0,s.B)(),a=c.useRef(null),u=c.useContext(C),i=y(),o=q(e),f=null!=(n=null==(r=o.current)?void 0:r.forceMount)?n:null==u?void 0:u.forceMount;G(()=>{if(!f)return i.item(l,null==u?void 0:u.id)},[f]);let v=F(l,a,[e.value,e.children,a],e.keywords),m=k(),p=B(e=>e.value&&e.value===v.current),h=B(e=>!!f||!1===i.filter()||!e.search||e.filtered.items.get(l)>0);function b(){var e,t;E(),null==(t=(e=o.current).onSelect)||t.call(e,v.current)}function E(){m.setState("value",v.current,!0)}if(c.useEffect(()=>{let t=a.current;if(!(!t||e.disabled))return t.addEventListener(g,b),()=>t.removeEventListener(g,b)},[h,e.onSelect,e.disabled]),!h)return null;let{disabled:w,value:S,onSelect:x,forceMount:A,keywords:R,...I}=e;return c.createElement(d.sG.div,{ref:j([a,t]),...I,id:l,"cmdk-item":"",role:"option","aria-disabled":!!w,"aria-selected":!!p,"data-disabled":!!w,"data-selected":!!p,onPointerMove:w||i.getDisablePointerSelection()?void 0:E,onClick:w?void 0:b},e.children)}),R=c.forwardRef((e,t)=>{let{heading:r,children:n,forceMount:l,...a}=e,u=(0,s.B)(),i=c.useRef(null),o=c.useRef(null),f=(0,s.B)(),v=y(),m=B(e=>!!l||!1===v.filter()||!e.search||e.filtered.groups.has(u));G(()=>v.group(u),[]),F(u,i,[e.value,e.heading,o]);let p=c.useMemo(()=>({id:u,forceMount:l}),[l]);return c.createElement(d.sG.div,{ref:j([i,t]),...a,"cmdk-group":"",role:"presentation",hidden:!m||void 0},r&&c.createElement("div",{ref:o,"cmdk-group-heading":"","aria-hidden":!0,id:f},r),V(e,e=>c.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?f:void 0},c.createElement(C.Provider,{value:p},e))))}),I=c.forwardRef((e,t)=>{let{alwaysRender:r,...n}=e,l=c.useRef(null),a=B(e=>!e.search);return r||a?c.createElement(d.sG.div,{ref:j([l,t]),...n,"cmdk-separator":"",role:"separator"}):null}),M=c.forwardRef((e,t)=>{let{onValueChange:r,...n}=e,l=null!=e.value,a=k(),u=B(e=>e.search),i=B(e=>e.value),o=y(),s=c.useMemo(()=>{var e;let t=null==(e=o.listInnerRef.current)?void 0:e.querySelector("".concat(p,"[").concat(b,'="').concat(encodeURIComponent(i),'"]'));return null==t?void 0:t.getAttribute("id")},[]);return c.useEffect(()=>{null!=e.value&&a.setState("search",e.value)},[e.value]),c.createElement(d.sG.input,{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":o.listId,"aria-labelledby":o.labelId,"aria-activedescendant":s,id:o.inputId,type:"text",value:l?e.value:u,onChange:e=>{l||a.setState("search",e.target.value),null==r||r(e.target.value)}})}),D=c.forwardRef((e,t)=>{let{children:r,label:n="Suggestions",...l}=e,a=c.useRef(null),u=c.useRef(null),i=y();return c.useEffect(()=>{if(u.current&&a.current){let e=u.current,t=a.current,r,n=new ResizeObserver(()=>{r=requestAnimationFrame(()=>{let r=e.offsetHeight;t.style.setProperty("--cmdk-list-height",r.toFixed(1)+"px")})});return n.observe(e),()=>{cancelAnimationFrame(r),n.unobserve(e)}}},[]),c.createElement(d.sG.div,{ref:j([a,t]),...l,"cmdk-list":"",role:"listbox","aria-label":n,id:i.listId},V(e,e=>c.createElement("div",{ref:j([u,i.listInnerRef]),"cmdk-list-sizer":""},e)))}),L=c.forwardRef((e,t)=>{let{open:r,onOpenChange:n,overlayClassName:l,contentClassName:a,container:u,...i}=e;return c.createElement(o.bL,{open:r,onOpenChange:n},c.createElement(o.ZL,{container:u},c.createElement(o.hJ,{"cmdk-overlay":"",className:l}),c.createElement(o.UC,{"aria-label":e.label,"cmdk-dialog":"",className:a},c.createElement(x,{ref:t,...i}))))}),P=Object.assign(x,{List:D,Item:A,Input:M,Group:R,Separator:I,Dialog:L,Empty:c.forwardRef((e,t)=>B(e=>0===e.filtered.count)?c.createElement(d.sG.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:c.forwardRef((e,t)=>{let{progress:r,children:n,label:l="Loading...",...a}=e;return c.createElement(d.sG.div,{ref:t,...a,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":l},V(e,e=>c.createElement("div",{"aria-hidden":!0},e)))})});function q(e){let t=c.useRef(e);return G(()=>{t.current=e}),t}var G="undefined"==typeof window?c.useEffect:c.useLayoutEffect;function O(e){let t=c.useRef();return void 0===t.current&&(t.current=e()),t}function j(e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}}function B(e){let t=k(),r=()=>e(t.snapshot());return(0,f.useSyncExternalStore)(t.subscribe,r,r)}function F(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],l=c.useRef(),a=y();return G(()=>{var u;let i=(()=>{var e;for(let t of r){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():l.current}})(),o=n.map(e=>e.trim());a.value(e,i,o),null==(u=t.current)||u.setAttribute(b,i),l.current=i}),l}var K=()=>{let[e,t]=c.useState(),r=O(()=>new Map);return G(()=>{r.current.forEach(e=>e()),r.current=new Map},[e]),(e,n)=>{r.current.set(e,n),t({})}};function V(e,t){let r,{asChild:n,children:l}=e;return n&&c.isValidElement(l)?c.cloneElement("function"==typeof(r=l.type)?r(l.props):"render"in r?r.render(l.props):l,{ref:l.ref},t(l.props.children)):t(l)}var N={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}}}]);