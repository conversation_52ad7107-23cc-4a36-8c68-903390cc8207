"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5565],{35565:(e,t,r)=>{r.r(t),r.d(t,{default:()=>l});var n=r(12115);let l=n.forwardRef(function(e,t){let{title:r,titleId:l,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},a),r?n.createElement("title",{id:l},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"}))})}}]);