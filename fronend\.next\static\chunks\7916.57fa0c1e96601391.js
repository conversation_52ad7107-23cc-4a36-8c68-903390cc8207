"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7916],{32919:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},47916:(e,t,a)=>{a.r(t),a.d(t,{default:()=>h});var r=a(95155);a(12115);var s=a(30285),c=a(46102),l=a(48436);let n=(0,a(19946).A)("LockOpen",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]]);var o=a(32919),d=a(65436),i=a(95728);let h=function(e){let{studentProduct:t}=e,{studentId:a}=(0,d.G)(e=>e.currentStudent),h="frozen"===t.enrollmentStatus,[u,{isLoading:x}]=(0,i.Mk)(),m=async()=>{let e=h?"解冻":"冻结";try{await u({studentId:a,studentProductId:t.id,data:{status:h?"active":"frozen"}}).unwrap(),l.l.success("".concat(e,"成功"))}catch(t){var r;l.l.error("".concat(e,"失败: ").concat((null==t?void 0:null===(r=t.data)||void 0===r?void 0:r.message)||"操作未成功")),console.error("调整学员套餐状态出错:",t)}};return(0,r.jsx)(c.Bc,{delayDuration:300,children:(0,r.jsxs)(c.m_,{children:[(0,r.jsx)(c.k$,{asChild:!0,children:(0,r.jsx)(s.$,{onClick:m,variant:"ghost",size:"icon",disabled:x,className:"h-8 w-8 transition-colors ".concat(h?"text-amber-500 hover:bg-amber-50 hover:text-amber-600":"text-slate-500 hover:bg-slate-50 hover:text-slate-700"),children:h?(0,r.jsx)(n,{className:"h-4 w-4"}):(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(c.ZI,{side:"top",className:"font-medium text-xs px-3 py-1.5",children:h?"解冻套餐":"冻结套餐"})]})})}}}]);