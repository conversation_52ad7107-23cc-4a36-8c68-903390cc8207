(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7786],{6101:(e,t,n)=>{"use strict";n.d(t,{s:()=>l,t:()=>s});var r=n(12115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let n=!1,r=e.map(e=>{let r=a(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():a(e[t],null)}}}}function l(...e){return r.useCallback(s(...e),e)}},19946:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:a=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:d="",children:c,iconNode:u,...f}=e;return(0,r.createElement)("svg",{ref:t,...l,width:a,height:a,stroke:n,strokeWidth:i?24*Number(o)/Number(a):o,className:s("lucide",d),...f},[...u.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),i=(e,t)=>{let n=(0,r.forwardRef)((n,l)=>{let{className:i,...d}=n;return(0,r.createElement)(o,{ref:l,iconNode:t,className:s("lucide-".concat(a(e)),i),...d})});return n.displayName="".concat(e),n}},27600:(e,t,n)=>{"use strict";n.d(t,{A:()=>h,AuthProvider:()=>f});var r=n(95155),a=n(65436),s=n(75521),l=n(98553),o=n(35695),i=n(12115),d=n(32422);let c=(0,i.createContext)(void 0),u=["/","/login","/404","/unauthorized","/1","/notifications/list","/features","/features/course-scheduling"];function f(e){let{children:t}=e,n=(0,a.G)(e=>e.user),[f,h]=(0,i.useState)([]),[m,p]=(0,i.useState)(!0),[g,x]=(0,i.useState)(!1),v=(0,a.j)(),b=(0,o.useRouter)(),y=(0,o.usePathname)(),j=(0,a.G)(e=>e.userMenus.menus),w=(0,a.G)(e=>e.userPermissions.permissions);console.log(w,"userPermissions");let{data:N,isLoading:k}=(0,s.DQ)({},{skip:!n.isAuthenticated||!n.token||((null==j?void 0:j.length)||0)>0}),{data:C,isLoading:A}=(0,l.H)({},{skip:!n.isAuthenticated||!n.token||((null==w?void 0:w.length)||0)>0}),I=(0,i.useCallback)((e,t)=>{if(!e)return!1;if(e===t)return!0;let n=e.split("/").filter(Boolean),r=t.split("/").filter(Boolean);if(1>=Math.abs(n.length-r.length)){if(r.length===n.length+1)for(let e=0;e<r.length;e++){let t=[...r];if(t.splice(e,1),n.join("/")===t.join("/"))return!0}if(r.length===n.length&&r.length>1){let e=0,t=0;for(let a=0;a<n.length;a++)n[a]!==r[a]?e++:t++;if(1===e&&t>0)return!0}}if(e.replace(/\/\d+(?=\/|$)/g,"")===t.replace(/\/\d+(?=\/|$)/g,""))return!0;if(e.includes("["))try{let n=e.replace(/\/\[\.\.\..*?\]/g,"/(.+)").replace(/\/\[.*?\]/g,"/([^/]+)");return new RegExp("^".concat(n,"$")).test(t)}catch(e){console.error("路径匹配正则表达式错误:",e)}return!1},[]),z=(0,i.useCallback)((e,t)=>{for(let n of e)if(I(n.path,t)||n.children&&n.children.length>0&&z(n.children,t))return!0;return!1},[I]),P=(0,i.useMemo)(()=>e=>!!u.includes(e)||!!n.isAuthenticated&&z(f,e),[n.isAuthenticated,f,z]);async function R(){let e=n.token||localStorage.getItem("token");console.log(e,"user token"),await fetch("".concat("http://127.0.0.1:3001/api/","/api/auth/logout"),{method:"POST",headers:{Authorization:"Bearer ".concat(e)}})}(0,i.useEffect)(()=>{j.length>0?(h(j),p(!1),x(!0)):N?(h(N),v({type:"userMenus/setUserMenus",payload:N}),p(!1),x(!0)):p(k)},[j,N,k,v]),console.log(C,"permissionsData"),(0,i.useEffect)(()=>{C&&v((0,d.jb)({permissions:C}))},[C]),(0,i.useEffect)(()=>{if(g&&!m&&y&&!P(y)){if(console.log("没有权限访问该页面",y),n.isAuthenticated){let e=setTimeout(()=>{P(y)||b.push("/404")},100);return()=>clearTimeout(e)}b.push("/login?redirect=".concat(encodeURIComponent(y)))}},[y,m,g,n.isAuthenticated,b,P]);let S=(0,i.useCallback)(async()=>{try{await R()}catch(e){console.error("登出API调用失败:",e)}finally{v({type:"user/clearUserInfo"}),v({type:"userMenus/clearMenus"}),v({type:"userPermissions/clearPermissions"}),localStorage.removeItem("userInfo"),localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("refreshToken"),sessionStorage.removeItem("token"),sessionStorage.removeItem("refresh_token"),sessionStorage.removeItem("refreshToken"),localStorage.removeItem("persist:root"),b.replace("/login")}},[v,b]);return(0,r.jsx)(c.Provider,{value:{menus:f,loading:m,checkAccess:P,logout:S},children:t})}let h=()=>{let e=(0,i.useContext)(c);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},27786:(e,t,n)=>{Promise.resolve().then(n.bind(n,94615)),Promise.resolve().then(n.bind(n,28941))},28941:(e,t,n)=>{"use strict";n.d(t,{default:()=>g});var r=n(95155),a=n(12115),s=n(6874),l=n.n(s),o=n(35695),i=n(59434),d=n(47863),c=n(66474),u=n(13052),f=n(42355);let h={ChartBarIcon:(0,a.lazy)(()=>n.e(5500).then(n.bind(n,5500)).then(e=>({default:e.default}))),ClipboardDocumentCheckIcon:(0,a.lazy)(()=>n.e(7740).then(n.bind(n,57740)).then(e=>({default:e.default}))),DocumentTextIcon:(0,a.lazy)(()=>n.e(2975).then(n.bind(n,92975)).then(e=>({default:e.default}))),CalendarDaysIcon:(0,a.lazy)(()=>n.e(6884).then(n.bind(n,26884)).then(e=>({default:e.default}))),BookOpenIcon:(0,a.lazy)(()=>n.e(7248).then(n.bind(n,67248)).then(e=>({default:e.default}))),ReceiptPercentIcon:(0,a.lazy)(()=>n.e(7456).then(n.bind(n,37456)).then(e=>({default:e.default}))),BanknotesIcon:(0,a.lazy)(()=>n.e(1174).then(n.bind(n,71174)).then(e=>({default:e.default}))),BellIcon:(0,a.lazy)(()=>Promise.resolve().then(n.bind(n,58828)).then(e=>({default:e.default}))),InboxIcon:(0,a.lazy)(()=>n.e(6073).then(n.bind(n,26073)).then(e=>({default:e.default}))),PaperAirplaneIcon:(0,a.lazy)(()=>n.e(4049).then(n.bind(n,94049)).then(e=>({default:e.default}))),AcademicCapIcon:(0,a.lazy)(()=>n.e(4648).then(n.bind(n,94648)).then(e=>({default:e.default}))),UserGroupIcon:(0,a.lazy)(()=>n.e(4219).then(n.bind(n,64219)).then(e=>({default:e.default}))),BuildingOfficeIcon:(0,a.lazy)(()=>n.e(5565).then(n.bind(n,35565)).then(e=>({default:e.default}))),ChartPieIcon:(0,a.lazy)(()=>n.e(4123).then(n.bind(n,24123)).then(e=>({default:e.default}))),ShoppingBagIcon:(0,a.lazy)(()=>n.e(527).then(n.bind(n,90527)).then(e=>({default:e.default}))),BriefcaseIcon:(0,a.lazy)(()=>n.e(7781).then(n.bind(n,17781)).then(e=>({default:e.default}))),TagIcon:(0,a.lazy)(()=>n.e(4393).then(n.bind(n,44393)).then(e=>({default:e.default}))),CurrencyDollarIcon:(0,a.lazy)(()=>n.e(8960).then(n.bind(n,28960)).then(e=>({default:e.default}))),InformationCircleIcon:(0,a.lazy)(()=>n.e(7695).then(n.bind(n,67695)).then(e=>({default:e.default})))},m=e=>{let{name:t,className:n="h-6 w-6",fallback:s=null,...l}=e,[o,i]=(0,a.useState)(null),[d,c]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{h[t]?(i(h[t]),c(!1)):(console.warn('图标 "'.concat(t,'" 未在预设映射中定义')),c(!0))},[t]),d||!o)?s||(0,r.jsx)("span",{className:n}):(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("span",{className:n}),children:(0,r.jsx)(o,{className:n,"aria-hidden":"true",...l})})};var p=n(65436);let g=a.memo(()=>{let[e,t]=(0,a.useState)(!1),[n,s]=(0,a.useState)([]),h=(0,o.usePathname)(),g=(0,p.G)(e=>e.userMenus.menus)||[],x=(0,a.useMemo)(()=>g,[g]);(0,a.useEffect)(()=>{let e=((e,t)=>{let n=[],r=(e,a,s)=>{for(let s of e){var l;if(s.path===t)return n.push(...a),!0;if((null===(l=s.children)||void 0===l?void 0:l.length)&&r(s.children,[...a,s.id],s.path||""))return!0}return!1};return r(e,[],""),n})(x,h);e.length>0&&s(t=>Array.from(new Set([...t,...e])))},[h,x]);let v=(0,a.useCallback)(e=>{s(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},[]),b=(0,a.useCallback)(e=>!!e&&(h===e||h.startsWith(e+"/")),[h]),y=(0,a.useCallback)(function(t){var a;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=t.children&&t.children.length>0,u=n.includes(t.id),f=b(t.path),h=(0,r.jsxs)("div",{className:"flex items-center flex-1 min-w-0",children:[(0,r.jsx)("div",{className:(0,i.cn)("flex-shrink-0 w-5 h-5",f&&"text-blue-600",e&&"mx-auto"),children:(0,r.jsx)(m,{name:t.icon,className:(0,i.cn)("h-5 w-5",f&&"text-blue-600")})}),!e&&(0,r.jsx)("span",{className:(0,i.cn)("ml-3 truncate",f&&"font-medium text-blue-600"),children:t.name})]});return(0,r.jsxs)("div",{className:(0,i.cn)("w-full",s>0&&"pl-4"),children:[t.path?(0,r.jsx)(l(),{href:t.path,className:(0,i.cn)("flex items-center w-full p-2 rounded-lg transition-colors duration-200","hover:bg-gray-100",f?"bg-blue-50 text-blue-600":"text-gray-700 hover:text-gray-900",e&&"justify-center"),children:h}):(0,r.jsx)("button",{onClick:()=>v(t.id),className:(0,i.cn)("flex items-center w-full p-2 rounded-lg transition-colors duration-200","hover:bg-gray-100 text-gray-700 hover:text-gray-900",e&&"justify-center",u&&"bg-gray-50"),children:(0,r.jsxs)("div",{className:"flex items-center justify-between w-full",children:[h,!e&&o&&(0,r.jsx)("div",{className:"flex-shrink-0 ml-2",children:u?(0,r.jsx)(d.A,{className:"w-4 h-4"}):(0,r.jsx)(c.A,{className:"w-4 h-4"})})]})}),o&&u&&!e&&(0,r.jsx)("div",{className:"mt-1 space-y-1 animate-slideDown",children:null===(a=t.children)||void 0===a?void 0:a.map(e=>y(e,s+1))})]},t.id)},[n,b,e,v]);return(0,r.jsx)("nav",{className:(0,i.cn)("bg-white shadow-xl transition-all duration-300 ease-in-out border-r border-gray-200 relative group",e?"w-16":"w-64"),children:(0,r.jsxs)("div",{className:(0,i.cn)("sticky top-0 flex flex-col h-full ",e?"py-2 px-1 pt-16":"p-4 pt-16"),children:[(0,r.jsx)("div",{className:"flex-1 space-y-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent",children:x.map(e=>y(e))}),(0,r.jsx)("button",{onClick:()=>t(!e),className:(0,i.cn)("absolute top-1/2 -right-3 w-6 h-6 bg-white border border-gray-200 rounded-full","flex items-center justify-center text-gray-500 hover:text-gray-700","transform -translate-y-1/2 shadow-sm hover:shadow transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"),children:e?(0,r.jsx)(u.A,{className:"w-4 h-4"}):(0,r.jsx)(f.A,{className:"w-4 h-4"})})]})})})},30285:(e,t,n)=>{"use strict";n.d(t,{$:()=>d,r:()=>i});var r=n(95155),a=n(12115),s=n(99708),l=n(74466),o=n(59434);let i=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:n,variant:a,size:l,asChild:d=!1,...c}=e,u=d?s.DX:"button";return(0,r.jsx)(u,{className:(0,o.cn)(i({variant:a,size:l,className:n})),ref:t,...c})});d.displayName="Button"},35695:(e,t,n)=>{"use strict";var r=n(18999);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},44838:(e,t,n)=>{"use strict";n.d(t,{I:()=>f,SQ:()=>h,_2:()=>m,hO:()=>p,lp:()=>g,mB:()=>x,rI:()=>c,ty:()=>u});var r=n(95155),a=n(12115),s=n(48698),l=n(13052),o=n(5196),i=n(9428),d=n(59434);let c=s.bL,u=s.l9,f=s.YJ;s.ZL,s.Pb,s.z6,a.forwardRef((e,t)=>{let{className:n,inset:a,children:o,...i}=e;return(0,r.jsxs)(s.ZP,{ref:t,className:(0,d.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a&&"pl-8",n),...i,children:[o,(0,r.jsx)(l.A,{className:"ml-auto"})]})}).displayName=s.ZP.displayName,a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)(s.G5,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...a})}).displayName=s.G5.displayName;let h=a.forwardRef((e,t)=>{let{className:n,sideOffset:a=4,...l}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{ref:t,sideOffset:a,className:(0,d.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...l})})});h.displayName=s.UC.displayName;let m=a.forwardRef((e,t)=>{let{className:n,inset:a,...l}=e;return(0,r.jsx)(s.q7,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a&&"pl-8",n),...l})});m.displayName=s.q7.displayName;let p=a.forwardRef((e,t)=>{let{className:n,children:a,checked:l,...i}=e;return(0,r.jsxs)(s.H_,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n),checked:l,...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),a]})});p.displayName=s.H_.displayName,a.forwardRef((e,t)=>{let{className:n,children:a,...l}=e;return(0,r.jsxs)(s.hN,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n),...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(i.A,{className:"h-2 w-2 fill-current"})})}),a]})}).displayName=s.hN.displayName;let g=a.forwardRef((e,t)=>{let{className:n,inset:a,...l}=e;return(0,r.jsx)(s.JU,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",n),...l})});g.displayName=s.JU.displayName;let x=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)(s.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",n),...a})});x.displayName=s.wv.displayName},47863:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},58828:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var r=n(12115);let a=r.forwardRef(function(e,t){let{title:n,titleId:a,...s}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},s),n?r.createElement("title",{id:a},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))})},59434:(e,t,n)=>{"use strict";n.d(t,{cn:()=>s});var r=n(52596),a=n(39688);function s(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.QP)((0,r.$)(t))}},65436:(e,t,n)=>{"use strict";n.d(t,{G:()=>s,j:()=>a});var r=n(34540);let a=()=>(0,r.wA)(),s=r.d4},66474:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},74466:(e,t,n)=>{"use strict";n.d(t,{F:()=>l});var r=n(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=r.$,l=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return s(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:l,defaultVariants:o}=t,i=Object.keys(l).map(e=>{let t=null==n?void 0:n[e],r=null==o?void 0:o[e];if(null===t)return null;let s=a(t)||a(r);return l[e][s]}),d=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return s(e,i,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...a}=t;return Object.entries(a).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...o,...d}[t]):({...o,...d})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},94615:(e,t,n)=>{"use strict";n.d(t,{default:()=>m});var r=n(95155),a=n(12115),s=n(6874),l=n.n(s),o=n(58828),i=n(65436),d=n(44838),c=n(30285),u=n(34540),f=n(10407),h=n(27600);function m(){let{data:e}=(0,f.K)(),t=(0,i.G)(e=>e.user),n=(0,i.G)(e=>e.notificationsUnreadCount),s=(0,u.wA)(),{logout:m}=(0,h.A)();return(0,a.useEffect)(()=>{s({type:"notificationsUnreadCount/setUnreadCount",payload:{count:e}})},[e]),console.log(t,"userssss"),(0,r.jsx)("header",{className:"fixed top-0 left-0 right-0 h-14 z-50 bg-blue-500",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-full px-4 text-white",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsx)(l(),{href:"#",className:"text-lg font-semibold",children:t.institutionName?t.institutionName:"蜜卡"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsx)(c.$,{variant:"ghost",size:"icon",asChild:!0,className:"relative",children:(0,r.jsxs)(l(),{href:"/notifications/list",children:[(0,r.jsx)(o.default,{className:"h-5 w-5"}),n.count>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 flex items-center justify-center min-w-[18px] h-[18px] px-1 text-[10px] font-medium rounded-full bg-red-500 text-white border border-blue-500",children:n.count>99?"99+":n.count})]})}),(0,r.jsxs)(d.rI,{children:[(0,r.jsx)(d.ty,{asChild:!0,children:(0,r.jsx)(c.$,{variant:"ghost",className:"flex items-center space-x-2 h-8 p-0 hover:bg-transparent focus:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 group",children:(0,r.jsx)("div",{className:"flex flex-col items-start ml-2",children:(0,r.jsx)("span",{className:"hidden sm:inline-block font-medium text-sm leading-tight max-w-[100px] truncate group-hover:text-blue-100 transition-colors",children:t.name||"用户"})})})}),(0,r.jsxs)(d.SQ,{align:"end",className:"w-56",children:[(0,r.jsx)(d.lp,{children:(0,r.jsx)("div",{className:"flex flex-col space-y-1",children:(0,r.jsx)("p",{className:"text-sm font-medium",children:t.name||"用户"})})}),(0,r.jsx)(d.mB,{}),(0,r.jsxs)(d.I,{children:[(0,r.jsx)(d._2,{onClick:()=>console.log("个人信息"),children:"个人信息"}),(0,r.jsx)(d._2,{onClick:()=>console.log("账号设置"),children:"账号设置"})]}),(0,r.jsx)(d.mB,{}),(0,r.jsx)(d._2,{onClick:m,className:"text-destructive focus:text-destructive",children:"退出登录"})]})]})]})]})})}},99708:(e,t,n)=>{"use strict";n.d(t,{DX:()=>l,xV:()=>i});var r=n(12115),a=n(6101),s=n(95155),l=r.forwardRef((e,t)=>{let{children:n,...a}=e,l=r.Children.toArray(n),i=l.find(d);if(i){let e=i.props.children,n=l.map(t=>t!==i?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,s.jsx)(o,{...a,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,s.jsx)(o,{...a,ref:t,children:n})});l.displayName="Slot";var o=r.forwardRef((e,t)=>{let{children:n,...s}=e;if(r.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n),l=function(e,t){let n={...t};for(let r in t){let a=e[r],s=t[r];/^on[A-Z]/.test(r)?a&&s?n[r]=(...e)=>{s(...e),a(...e)}:a&&(n[r]=a):"style"===r?n[r]={...a,...s}:"className"===r&&(n[r]=[a,s].filter(Boolean).join(" "))}return{...e,...n}}(s,n.props);return n.type!==r.Fragment&&(l.ref=t?(0,a.t)(t,e):e),r.cloneElement(n,l)}return r.Children.count(n)>1?r.Children.only(null):null});o.displayName="SlotClone";var i=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});function d(e){return r.isValidElement(e)&&e.type===i}}}]);