'use client';

import React, { useEffect, useState, useMemo } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import ClassInfo from './classes-info';
import { CustomTabs, type TabItem } from '@/components/ui/custom-tabs';
import { useClasses } from '@/hooks/useClasses';
import { useParams } from 'next/navigation';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import dynamic from 'next/dynamic';
import type {ClassSchedule, AttendanceData, StudentAttendance, Header} from '../Type';

// Dynamic import components, disable SSR
const DynamicLessonDetails = dynamic(() => import('./lesson-details'), { ssr: false });
const DynamicAttendance = dynamic(() => import('./attendance'), { ssr: false });
const DynamicStudentList = dynamic(() => import('./student-list'), { ssr: false });

function formatDate(date: string): string {
  return format(new Date(date), "EEEE yyyy-MM-dd", { locale: zhCN });
}

function attendanceHeader(data: ClassSchedule[]): AttendanceData {
  const header: Header[] = [];
  const students = new Map<string, StudentAttendance>();

  data.forEach((item) => {
    item.StudentWeeklySchedule.forEach((student, index) => {
      if (!students.has(student.student.id)) {
        students.set(student.student.id, {
          id: student.student.id,
          name: student.student.name,
          attendance: [],
        });
      }
      const studentAttendance = students.get(student.student.id)!;
      studentAttendance.attendance[item.currentWeeks - 1] = student.status || 'unattended';
    });

    header.push({
      id: item.id,
      title: `第${item.currentWeeks}周期`,
      date: formatDate(item.startDate),
      time: `${item.startTime}-${item.endTime}`,
    });
  });

  return {
    header,
    students: Array.from(students.values()),
  };
}

const ClassDetailView: React.FC = React.memo(() => {
  
  const { getClassesByIdDetail } = useClasses();
  const { classesId } = useParams() as { classesId: string };
  
  const [classesData, setClassesData] = useState<any>(null);
  const [attendanceData, setAttendanceData] = useState<AttendanceData | null>(null);
  
  const mediaTabsWithIcons: TabItem[] = useMemo(() => [
    { id: "班级课节", label: "班级课节", content: <DynamicLessonDetails classesSchedule={classesData?.classesSchedule} /> },
    { id: "出勤详细", label: "出勤详细", content: <DynamicAttendance attendanceData={attendanceData} /> },
    { id: "studentList", label: "学员列表", content: <DynamicStudentList students={classesData?.students} /> },
  ], [classesData, attendanceData]);

  useEffect(() => {
    if (classesId) {
      getClassesByIdDetail(classesId).then((res) => {
        const { header, students } = attendanceHeader(res.data.classesSchedule);
        setAttendanceData({ header, students });
        setClassesData(res.data);
      }).catch((error) => {
        console.error('Error fetching class details:', error);
      });
    }
  }, [classesId]);

  if (!classesData || !attendanceData) {
    return <div>加载中...</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardContent className="pt-6">
          <ClassInfo classData={classesData} />
        </CardContent>
      </Card>
      <Card>
        <CardContent className="pt-6">
          <CustomTabs  
            defaultTab='班级课节'
            tabs={mediaTabsWithIcons}
            variant='underline'
          />
        </CardContent>
      </Card>
    </div>
  );
});

export default ClassDetailView;
