/**
 * Build a menu tree from a flat list of menu items
 * @param {Array} menuItems - Flat list of menu items
 * @param {string|null} parentId - Parent ID to filter by (null for root items)
 * @returns {Array} Menu tree
 */
function buildMenuTree(menuItems, parentId = null) {
  // Handle empty or invalid input
  if (!menuItems || !Array.isArray(menuItems) || menuItems.length === 0) {
    return [];
  }

  // Log for debugging
  console.log(`Building menu tree with ${menuItems.length} items, parentId: ${parentId}`);

  // Create a map of parent IDs to their children for faster lookup
  const childrenMap = {};
  menuItems.forEach(item => {
    const parent = item.parentId || null;
    if (!childrenMap[parent]) {
      childrenMap[parent] = [];
    }
    childrenMap[parent].push(item);
  });

  // Log for debugging
  console.log(`Parent-child map created with ${Object.keys(childrenMap).length} parent entries`);

  // Function to build tree recursively using the map
  function buildTree(currentParentId) {
    // Get children for current parent
    const children = childrenMap[currentParentId] || [];

    // Sort children by sort order
    const sortedChildren = [...children].sort((a, b) => {
      const sortA = typeof a.sort === 'number' ? a.sort : 0;
      const sortB = typeof b.sort === 'number' ? b.sort : 0;
      return sortA - sortB;
    });

    // Build tree recursively
    return sortedChildren.map(item => {
      // Create a copy of the item to avoid modifying the original
      const newItem = { ...item };

      // Find children recursively
      const itemChildren = buildTree(item.id);

      // Add children array (even if empty)
      newItem.children = itemChildren;

      return newItem;
    });
  }

  // Start building the tree from the root
  return buildTree(parentId);
}

export default buildMenuTree;