# CardMees Frontend Style Guide

This style guide follows Google TypeScript style guidelines and provides standardized conventions for the Card<PERSON>ees frontend project.

## File and Folder Naming

### File Naming
- Use **kebab-case** (lowercase with hyphens) for all file names
  - ✅ `user-profile.tsx`
  - ❌ `userProfile.tsx` or `UserProfile.tsx`
- Component files should match their component name but in kebab-case
  - Component: `UserProfile` → File: `user-profile.tsx`
- Test files should end with `.test.ts` or `.test.tsx`
  - `user-profile.test.tsx`

### Folder Naming
- Use **kebab-case** for folder names
  - ✅ `user-profiles/`
  - ❌ `userProfiles/` or `UserProfiles/`
- Feature folders should be descriptive and singular
  - ✅ `enrollment/`, `academic/`, `organization/`
  - ❌ `enrollments/`, `academics/`, `organizations/`

## Code Organization

### Import Order
Organize imports in the following order, separated by blank lines:
1. Third-party library imports
2. Application code imports
3. Relative path imports

```typescript
// 1. Third-party libraries
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

// 2. Application code imports
import { useAppSelector } from '@/stores/hooks';
import { formatDate } from '@/utils/format/format-date';

// 3. Relative path imports
import { StudentCard } from './student-card';
import styles from './styles.module.css';
```

### File Structure
Each file should contain only one concept (component, hook, utility, etc.)

## Naming Conventions

### Component Naming
- Use **PascalCase** for React components and their type definitions
  - ✅ `StudentProfile`, `HeaderNavigation`
  - ❌ `studentProfile`, `headerNavigation`

### Function Naming
- Use **camelCase** for function names
  - ✅ `getUserData()`, `formatCurrency()`
  - ❌ `GetUserData()`, `format_currency()`
- Use descriptive verbs as prefixes for functions
  - ✅ `getUser()`, `fetchData()`, `calculateTotal()`
  - ❌ `user()`, `data()`, `total()`

### Variable Naming
- Use **camelCase** for variables and object properties
  - ✅ `userData`, `isLoading`
  - ❌ `UserData`, `is_loading`
- Use **CONSTANT_CASE** for constants
  - ✅ `MAX_RETRY_COUNT`, `API_BASE_URL`
  - ❌ `maxRetryCount`, `apiBaseUrl`

### Interface and Type Naming
- Use **PascalCase** for interface and type names
  - ✅ `UserData`, `StudentProfileProps`
  - ❌ `userData`, `studentProfileProps`
- Do not use the `I` prefix for interfaces
  - ✅ `UserData`
  - ❌ `IUserData`

## Coding Style

### Semicolons and Commas
- Always use semicolons at the end of statements
- Use trailing commas in multi-line object and array literals

### Quotes
- Use single quotes for string literals
  - ✅ `'string value'`
  - ❌ `"string value"`
- Use template literals for string interpolation
  - ✅ `` `Hello, ${name}` ``
  - ❌ `'Hello, ' + name`

### Spacing and Indentation
- Use 2 spaces for indentation
- Add spaces inside curly braces
  - ✅ `{ key: value }`
  - ❌ `{key:value}`

### Comments
- Use JSDoc style comments for exported functions and classes
- Use `//` for single-line comments
- Explain why, not what the code does

## TypeScript Best Practices

### Type Declarations
- Always declare function return types
- Avoid using `any` type, use `unknown` when necessary
- Use type aliases with `type` instead of `interface` for simple type definitions
- Use `readonly` for immutable properties

### Null and Undefined
- Use optional chaining (`?.`) and nullish coalescing (`??`) operators
- Use `undefined` instead of `null` when possible

### Async/Await
- Prefer `async/await` over Promise chains
- Always handle errors in async functions

## Component Guidelines

### Functional Components
- Use functional components with hooks instead of class components
- Define components using the `React.FC` type with explicit props interface

```typescript
interface ButtonProps {
  label: string;
  onClick: () => void;
  disabled?: boolean;
}

const Button: React.FC<ButtonProps> = ({ label, onClick, disabled = false }) => {
  return (
    <button onClick={onClick} disabled={disabled}>
      {label}
    </button>
  );
};
```

### Props
- Define prop types using interfaces
- Use destructuring for props
- Provide default values for optional props

## State Management

### Hooks
- Use appropriate hooks for state management
- Optimize with `useCallback` and `useMemo` when necessary

### Redux
- Follow Redux Toolkit patterns
- Use slice pattern for organizing reducers and actions
