"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4512],{24357:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},74512:(e,t,a)=>{a.r(t),a.d(t,{default:()=>r});var s=a(95155),n=a(57001),c=a(24357),l=a(12115),d=a(27893);let i=(0,a(55028).default)(()=>a.e(2807).then(a.bind(a,2807)),{loadableGenerated:{webpack:()=>[2807]},ssr:!1}),r=e=>{let{classes:t}=e,[a,r]=(0,l.useState)(!1),[h]=(0,d.EV)(),p=async e=>{await h({classesId:t.id,data:e})};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.p,{icon:c.A,tooltipText:"复制或追加",onClick:()=>r(!0)}),a&&(0,s.jsx)(i,{open:a,onOpenChange:e=>{r(e)},classes:t,onSubmit:p})]})}}}]);