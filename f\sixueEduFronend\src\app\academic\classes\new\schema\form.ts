import { z } from "zod"

// 假设这是现有的表单模式
const formSchema = z.object({
  name: z.string().min(1, "请输入班级名称"),
  courseId: z.string().min(1, "请选择课程包"),
  teacherId: z.string().min(1, "请选择主讲老师"),
  classroom: z.null().optional(), // 教室可以为空
  recurrenceType: z.enum(["weekly", "daily"]),
  
  // 周重复模式的日期选择
  weekdays: z.array(
    z.object({
      day: z.number(),
      startTime: z.string(),
      endTime: z.string(),
    })
  ).optional(),
  
  // 每天重复的时间设置
  daily: z.object({
    startTime: z.string().optional(),
    endTime: z.string().optional(),
  }).optional(),

  endType: z.enum(["times", "number_of_times"]),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  times: z.number().optional(),
  maxStudentCount: z.string().min(1, "请输入最大人数"),
  type: z.enum(["temporary", "fixed"]),

  // 新增高级选项
  reservation: z.object({
    enabled: z.boolean(),
    appointmentStartTime: z.number().min(0, "不能小于0"),
    appointmentEndTime: z.number().min(0, "不能小于0"),
  }),
  
  attendance: z.object({
    studentScan: z.boolean(),
    autoSystem: z.boolean(),
  }),
  
  leave: z.object({
    enabled: z.boolean(),
    leaveDeadline: z.number().min(0, "不能小于0"),
  }),
  
  isShowWeekCount: z.boolean(),
});

export default formSchema;