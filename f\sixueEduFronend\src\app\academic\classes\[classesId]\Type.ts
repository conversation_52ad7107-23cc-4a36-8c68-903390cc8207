interface Student {
  id: string;
  name: string;
}

interface StudentSchedule {
  student: Student;
  status?: string;
}

export interface ClassSchedule {
  id: string;
  currentWeeks: number;
  startDate: string;
  startTime: string;
  endTime: string;
  StudentWeeklySchedule: StudentSchedule[];
}

export interface AttendanceData {
  header: Header[];
  students: StudentAttendance[];
}

export interface Header {
  id: string;
  title: string;
  date: string;
  time: string;
}


export interface StudentAttendance {
  id: string;
  name: string;
  attendance: string[];
}