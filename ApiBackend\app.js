'use strict'

import Fastify from 'fastify'
import { join, dirname } from 'node:path'
import { fileURLToPath } from 'node:url'
import AutoLoad from '@fastify/autoload'
import authMiddleware from './middleware/auth.js'
import multipart from '@fastify/multipart'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Pass --options via CLI arguments in command to enable these options.
const options = {
  logger: {
    prettyPrint: true,
    level: 'info'
  },

}

export default async function(fastify, opts) {

  // 注册插件
  await fastify.register(import('./plugins/env.js'))



  await fastify.register(import('./plugins/jwt.js'))
  await fastify.register(import('./plugins/postgres.js'))
  await fastify.register(import('./plugins/prisma-connector.js'))
  await fastify.register(import('./plugins/cors.js'))
  await fastify.register(import('./plugins/redis.js'))
  await fastify.register(import('./plugins/swagger.js'))
  await fastify.register(import('./plugins/error-handler.js'))
  await fastify.register(import('./plugins/bullmq.js'))
  await fastify.register(import('@fastify/websocket'))
  await fastify.register(multipart, {
    limits: {
      fileSize: 5 * 1024 * 1024, // 限制文件大小为 5MB
    }
  })
  // 注册认证中间件
  await fastify.register(authMiddleware)

  // console.log(fastify.config)
  // 自动加载路由
  await fastify.register(AutoLoad, {
    dir: join(__dirname, 'routes'),
    options: {
      prefix: '/api'
    }
  })

  return fastify
}

const _options = options
export { _options as options }



