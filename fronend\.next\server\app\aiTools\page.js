(()=>{var e={};e.id=4572,e.ids=[4572],e.modules={625:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>f,tree:()=>d});var n=r(65239),a=r(48088),s=r(88170),i=r.n(s),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["aiTools",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,53226)),"F:\\trae\\cardmees\\fronend\\src\\app\\aiTools\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,59413)),"F:\\trae\\cardmees\\fronend\\src\\app\\aiTools\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"F:\\trae\\cardmees\\fronend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["F:\\trae\\cardmees\\fronend\\src\\app\\aiTools\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/aiTools/page",pathname:"/aiTools",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>f,eb:()=>h,gC:()=>x,l6:()=>u,yv:()=>c});var n=r(60687),a=r(43210),s=r(72951),i=r(78272),o=r(3589),l=r(13964),d=r(4780);let u=s.bL;s.YJ;let c=s.WT,f=a.forwardRef(({className:e,children:t,...r},a)=>(0,n.jsxs)(s.l9,{ref:a,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,(0,n.jsx)(s.In,{asChild:!0,children:(0,n.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));f.displayName=s.l9.displayName;let m=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.PP,{ref:r,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(o.A,{className:"h-4 w-4"})}));m.displayName=s.PP.displayName;let p=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.wn,{ref:r,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(i.A,{className:"h-4 w-4"})}));p.displayName=s.wn.displayName;let x=a.forwardRef(({className:e,children:t,position:r="popper",...a},i)=>(0,n.jsx)(s.ZL,{children:(0,n.jsxs)(s.UC,{ref:i,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...a,children:[(0,n.jsx)(m,{}),(0,n.jsx)(s.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,n.jsx)(p,{})]})}));x.displayName=s.UC.displayName,a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.JU,{ref:r,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=s.JU.displayName;let h=a.forwardRef(({className:e,children:t,...r},a)=>(0,n.jsxs)(s.q7,{ref:a,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,n.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,n.jsx)(s.VF,{children:(0,n.jsx)(l.A,{className:"h-4 w-4"})})}),(0,n.jsx)(s.p4,{children:t})]}));h.displayName=s.q7.displayName,a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.wv,{ref:r,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=s.wv.displayName},16709:(e,t,r)=>{"use strict";r.d(t,{Q:()=>m});var n=r(60687),a=r(43210),s=r(4780),i=r(96834),o=r(24224);let l=(0,o.F)("",{variants:{variant:{default:"border-b flex -mb-px space-x-6",underline:"relative flex overflow-x-auto",pill:"bg-muted p-1 rounded-lg flex mb-4",vertical:"w-48 shrink-0 border-r pr-4 flex flex-col space-y-1"}},defaultVariants:{variant:"default"}}),d=(0,o.F)("transition-colors",{variants:{variant:{default:"py-2 border-b-2 font-medium text-sm flex items-center gap-2",underline:"py-2 mr-8 font-medium text-sm transition-colors relative flex items-center gap-2",pill:"flex-1 py-1.5 px-3 text-sm font-medium rounded-md flex items-center justify-center gap-2",vertical:"flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md text-left"},state:{active:"",inactive:""}},compoundVariants:[{variant:"default",state:"active",className:"border-primary text-primary"},{variant:"default",state:"inactive",className:"border-transparent text-muted-foreground hover:text-foreground hover:border-border"},{variant:"underline",state:"active",className:"text-primary"},{variant:"underline",state:"inactive",className:"text-muted-foreground hover:text-foreground"},{variant:"pill",state:"active",className:"bg-background text-foreground shadow-sm"},{variant:"pill",state:"inactive",className:"text-muted-foreground hover:text-foreground"},{variant:"vertical",state:"active",className:"bg-accent text-accent-foreground"},{variant:"vertical",state:"inactive",className:"text-muted-foreground hover:bg-muted hover:text-foreground"}],defaultVariants:{variant:"default",state:"inactive"}}),u=(0,o.F)("",{variants:{variant:{default:"py-4",underline:"py-4",pill:"",vertical:"flex-1"}},defaultVariants:{variant:"default"}}),c=a.memo(({tab:e,isActive:t,showBadges:r})=>t?e.content?(0,n.jsx)(n.Fragment,{children:e.content}):(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("h3",{className:"text-lg font-medium",children:e.label}),r&&void 0!==e.badge&&(0,n.jsx)(i.E,{variant:"default",children:e.badge})]}),(0,n.jsxs)("p",{className:"text-muted-foreground",children:["这是 ",e.label," 标签页的内容区域。",r&&void 0!==e.badge&&` 您有 ${e.badge} 个未读${e.label}。`]})]}):null,(e,t)=>!e.isActive&&!t.isActive||e.isActive===t.isActive&&e.tab.id===t.tab.id&&e.showBadges===t.showBadges);c.displayName="TabContent";let f=a.memo(({tab:e,isActive:t,variant:r,showIcons:a,showBadges:o,onClick:l})=>(0,n.jsxs)("button",{onClick:l,disabled:e.disabled,className:(0,s.cn)(d({variant:r,state:t?"active":"inactive"}),e.disabled&&"opacity-50 cursor-not-allowed"),children:[a&&e.icon,(0,n.jsx)("span",{children:e.label}),o&&void 0!==e.badge&&(0,n.jsx)(i.E,{variant:t?"default":"secondary",className:"ml-1 px-1.5 py-0.5 h-5",children:e.badge}),"underline"===r&&t&&(0,n.jsx)("span",{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-primary"})]}));function m({tabs:e,defaultTab:t,onChange:r,className:i,variant:o="default",showIcons:d=!0,showBadges:m=!0}){let p=a.useRef(!1),[x,h]=a.useState(""),v=a.useCallback(e=>{e!==x&&(h(e),r?.(e))},[x,r]);a.useEffect(()=>{p.current||(p.current=!0,h(t||e[0]?.id||""))},[t,e]);let g=a.useMemo(()=>e.reduce((e,t)=>(e.set(t.id,t),e),new Map),[e]),b=a.useMemo(()=>new Set(e.map(e=>e.id)),[e]),y=a.useRef(new Map);a.useEffect(()=>{Array.from(y.current.keys()).filter(e=>!b.has(e)).forEach(e=>y.current.delete(e)),e.forEach(e=>{y.current.has(e.id)||y.current.set(e.id,()=>v(e.id))})},[e,b,v]);let j=a.useCallback(e=>y.current.get(e)||(()=>v(e)),[v]),{isVertical:w,showUnderlineBorder:N}=a.useMemo(()=>({isVertical:"vertical"===o,showUnderlineBorder:"underline"===o}),[o]),R=a.useMemo(()=>{let e=g.get(x);return e?(0,n.jsx)(c,{tab:e,isActive:!0,showBadges:m},x):null},[x,g,m]);return a.useEffect(()=>{},[e.length,x]),(0,n.jsxs)("div",{className:(0,s.cn)(w?"flex gap-8":"w-full",i),children:[(0,n.jsxs)("div",{className:l({variant:o}),children:[N&&(0,n.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-border"}),e.map(e=>(0,n.jsx)(f,{tab:e,isActive:x===e.id,variant:o,showIcons:d,showBadges:m,onClick:j(e.id)},e.id))]}),(0,n.jsx)("div",{className:u({variant:o}),children:R})]})}f.displayName="TabButton"},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var n=r(60687),a=r(43210),s=r(4780);let i=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("textarea",{className:(0,s.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...t}));i.displayName="Textarea"},47076:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ei});var n=r(60687),a=r(16709),s=r(43210),i=r.t(s,2),o=r(15079),l=r(34729),d=r(29523);function u(e,[t,r]){return Math.min(r,Math.max(t,e))}function c(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function f(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function m(...e){return t=>{let r=!1,n=e.map(e=>{let n=f(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():f(e[t],null)}}}}function p(...e){return s.useCallback(m(...e),e)}function x(e,t=[]){let r=[],a=()=>{let t=r.map(e=>s.createContext(e));return function(r){let n=r?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return a.scopeName=e,[function(t,a){let i=s.createContext(a),o=r.length;r=[...r,a];let l=t=>{let{scope:r,children:a,...l}=t,d=r?.[e]?.[o]||i,u=s.useMemo(()=>l,Object.values(l));return(0,n.jsx)(d.Provider,{value:u,children:a})};return l.displayName=t+"Provider",[l,function(r,n){let l=n?.[e]?.[o]||i,d=s.useContext(l);if(d)return d;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let a=r(e)[`__scope${n}`];return{...t,...a}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(a,...t)]}var h=globalThis?.document?s.useLayoutEffect:()=>{},v=i[" useInsertionEffect ".trim().toString()]||h,g=(Symbol("RADIX:SYNC_STATE"),s.createContext(void 0));function b(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){var a;let e,i;let o=(a=r,(i=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(i=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),l=function(e,t){let r={...t};for(let n in t){let a=e[n],s=t[n];/^on[A-Z]/.test(n)?a&&s?r[n]=(...e)=>{s(...e),a(...e)}:a&&(r[n]=a):"style"===n?r[n]={...a,...s}:"className"===n&&(r[n]=[a,s].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==s.Fragment&&(l.ref=t?m(t,o):o),s.cloneElement(r,l)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...i}=e,o=s.Children.toArray(a),l=o.find(j);if(l){let e=l.props.children,a=o.map(t=>t!==l?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...i,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...i,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}r(51215);var y=Symbol("radix.slottable");function j(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===y}var w=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=b(`Primitive.${t}`),a=s.forwardRef((e,a)=>{let{asChild:s,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(s?r:t,{...i,ref:a})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),N=new WeakMap;function R(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=S(t),a=n>=0?n:r+n;return a<0||a>=r?-1:a}(e,t);return -1===r?void 0:e[r]}function S(e){return e!=e||0===e?0:Math.trunc(e)}var C=["PageUp","PageDown"],P=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],E={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},M="Slider",[A,_,k]=function(e){let t=e+"CollectionProvider",[r,a]=x(t),[i,o]=r(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:r}=e,a=s.useRef(null),o=s.useRef(new Map).current;return(0,n.jsx)(i,{scope:t,itemMap:o,collectionRef:a,children:r})};l.displayName=t;let d=e+"CollectionSlot",u=b(d),c=s.forwardRef((e,t)=>{let{scope:r,children:a}=e,s=p(t,o(d,r).collectionRef);return(0,n.jsx)(u,{ref:s,children:a})});c.displayName=d;let f=e+"CollectionItemSlot",m="data-radix-collection-item",h=b(f),v=s.forwardRef((e,t)=>{let{scope:r,children:a,...i}=e,l=s.useRef(null),d=p(t,l),u=o(f,r);return s.useEffect(()=>(u.itemMap.set(l,{ref:l,...i}),()=>void u.itemMap.delete(l))),(0,n.jsx)(h,{[m]:"",ref:d,children:a})});return v.displayName=f,[{Provider:l,Slot:c,ItemSlot:v},function(t){let r=o(e+"CollectionConsumer",t);return s.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${m}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},a]}(M),[D,I]=x(M,[k]),[$,z]=D(M),F=s.forwardRef((e,t)=>{let{name:r,min:a=0,max:i=100,step:o=1,orientation:l="horizontal",disabled:d=!1,minStepsBetweenThumbs:f=0,defaultValue:m=[a],value:p,onValueChange:x=()=>{},onValueCommit:h=()=>{},inverted:g=!1,form:b,...y}=e,j=s.useRef(new Set),w=s.useRef(0),N="horizontal"===l,[R=[],S]=function({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[a,i,o]=function({defaultProp:e,onChange:t}){let[r,n]=s.useState(e),a=s.useRef(r),i=s.useRef(t);return v(()=>{i.current=t},[t]),s.useEffect(()=>{a.current!==r&&(i.current?.(r),a.current=r)},[r,a]),[r,n,i]}({defaultProp:t,onChange:r}),l=void 0!==e,d=l?e:a;{let t=s.useRef(void 0!==e);s.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,n])}return[d,s.useCallback(t=>{if(l){let r="function"==typeof t?t(e):t;r!==e&&o.current?.(r)}else i(t)},[l,e,i,o])]}({prop:p,defaultProp:m,onChange:e=>{let t=[...j.current];t[w.current]?.focus(),x(e)}}),E=s.useRef(R);function M(e,t,{commit:r}={commit:!1}){let n=(String(o).split(".")[1]||"").length,s=u(function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-a)/o)*o+a,n),[a,i]);S((e=[])=>{let n=function(e=[],t,r){let n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,s,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,f*o))return e;{w.current=n.indexOf(s);let t=String(n)!==String(e);return t&&r&&h(n),t?n:e}})}return(0,n.jsx)($,{scope:e.__scopeSlider,name:r,disabled:d,min:a,max:i,valueIndexToChangeRef:w,thumbs:j.current,values:R,orientation:l,form:b,children:(0,n.jsx)(A.Provider,{scope:e.__scopeSlider,children:(0,n.jsx)(A.Slot,{scope:e.__scopeSlider,children:(0,n.jsx)(N?T:O,{"aria-disabled":d,"data-disabled":d?"":void 0,...y,ref:t,onPointerDown:c(y.onPointerDown,()=>{d||(E.current=R)}),min:a,max:i,inverted:g,onSlideStart:d?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(R,e);M(e,t)},onSlideMove:d?void 0:function(e){M(e,w.current)},onSlideEnd:d?void 0:function(){let e=E.current[w.current];R[w.current]!==e&&h(R)},onHomeKeyDown:()=>!d&&M(a,0,{commit:!0}),onEndKeyDown:()=>!d&&M(i,R.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:t})=>{if(!d){let r=C.includes(e.key)||e.shiftKey&&P.includes(e.key),n=w.current;M(R[n]+o*(r?10:1)*t,n,{commit:!0})}}})})})})});F.displayName=M;var[V,q]=D(M,{startEdge:"left",endEdge:"right",size:"width",direction:1}),T=s.forwardRef((e,t)=>{let{min:r,max:a,dir:i,inverted:o,onSlideStart:l,onSlideMove:d,onSlideEnd:u,onStepKeyDown:c,...f}=e,[m,x]=s.useState(null),h=p(t,e=>x(e)),v=s.useRef(void 0),b=function(e){let t=s.useContext(g);return e||t||"ltr"}(i),y="ltr"===b,j=y&&!o||!y&&o;function w(e){let t=v.current||m.getBoundingClientRect(),n=Z([0,t.width],j?[r,a]:[a,r]);return v.current=t,n(e-t.left)}return(0,n.jsx)(V,{scope:e.__scopeSlider,startEdge:j?"left":"right",endEdge:j?"right":"left",direction:j?1:-1,size:"width",children:(0,n.jsx)(B,{dir:b,"data-orientation":"horizontal",...f,ref:h,style:{...f.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=w(e.clientX);l?.(t)},onSlideMove:e=>{let t=w(e.clientX);d?.(t)},onSlideEnd:()=>{v.current=void 0,u?.()},onStepKeyDown:e=>{let t=E[j?"from-left":"from-right"].includes(e.key);c?.({event:e,direction:t?-1:1})}})})}),O=s.forwardRef((e,t)=>{let{min:r,max:a,inverted:i,onSlideStart:o,onSlideMove:l,onSlideEnd:d,onStepKeyDown:u,...c}=e,f=s.useRef(null),m=p(t,f),x=s.useRef(void 0),h=!i;function v(e){let t=x.current||f.current.getBoundingClientRect(),n=Z([0,t.height],h?[a,r]:[r,a]);return x.current=t,n(e-t.top)}return(0,n.jsx)(V,{scope:e.__scopeSlider,startEdge:h?"bottom":"top",endEdge:h?"top":"bottom",size:"height",direction:h?1:-1,children:(0,n.jsx)(B,{"data-orientation":"vertical",...c,ref:m,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=v(e.clientY);o?.(t)},onSlideMove:e=>{let t=v(e.clientY);l?.(t)},onSlideEnd:()=>{x.current=void 0,d?.()},onStepKeyDown:e=>{let t=E[h?"from-bottom":"from-top"].includes(e.key);u?.({event:e,direction:t?-1:1})}})})}),B=s.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:a,onSlideMove:s,onSlideEnd:i,onHomeKeyDown:o,onEndKeyDown:l,onStepKeyDown:d,...u}=e,f=z(M,r);return(0,n.jsx)(w.span,{...u,ref:t,onKeyDown:c(e.onKeyDown,e=>{"Home"===e.key?(o(e),e.preventDefault()):"End"===e.key?(l(e),e.preventDefault()):C.concat(P).includes(e.key)&&(d(e),e.preventDefault())}),onPointerDown:c(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),f.thumbs.has(t)?t.focus():a(e)}),onPointerMove:c(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&s(e)}),onPointerUp:c(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),i(e))})})}),U="SliderTrack",J=s.forwardRef((e,t)=>{let{__scopeSlider:r,...a}=e,s=z(U,r);return(0,n.jsx)(w.span,{"data-disabled":s.disabled?"":void 0,"data-orientation":s.orientation,...a,ref:t})});J.displayName=U;var L="SliderRange",H=s.forwardRef((e,t)=>{let{__scopeSlider:r,...a}=e,i=z(L,r),o=q(L,r),l=p(t,s.useRef(null)),d=i.values.length,u=i.values.map(e=>X(e,i.min,i.max)),c=d>1?Math.min(...u):0,f=100-Math.max(...u);return(0,n.jsx)(w.span,{"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,...a,ref:l,style:{...e.style,[o.startEdge]:c+"%",[o.endEdge]:f+"%"}})});H.displayName=L;var K="SliderThumb",W=s.forwardRef((e,t)=>{let r=_(e.__scopeSlider),[a,i]=s.useState(null),o=p(t,e=>i(e)),l=s.useMemo(()=>a?r().findIndex(e=>e.ref.current===a):-1,[r,a]);return(0,n.jsx)(Y,{...e,ref:o,index:l})}),Y=s.forwardRef((e,t)=>{let{__scopeSlider:r,index:a,name:i,...o}=e,l=z(K,r),d=q(K,r),[u,f]=s.useState(null),m=p(t,e=>f(e)),x=!u||l.form||!!u.closest("form"),v=function(e){let[t,r]=s.useState(void 0);return h(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,a;if(!Array.isArray(t)||!t.length)return;let s=t[0];if("borderBoxSize"in s){let e=s.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,a=t.blockSize}else n=e.offsetWidth,a=e.offsetHeight;r({width:n,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(u),g=l.values[a],b=void 0===g?0:X(g,l.min,l.max),y=function(e,t){return t>2?`Value ${e+1} of ${t}`:2===t?["Minimum","Maximum"][e]:void 0}(a,l.values.length),j=v?.[d.size],N=j?function(e,t,r){let n=e/2,a=Z([0,50],[0,n]);return(n-a(t)*r)*r}(j,b,d.direction):0;return s.useEffect(()=>{if(u)return l.thumbs.add(u),()=>{l.thumbs.delete(u)}},[u,l.thumbs]),(0,n.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[d.startEdge]:`calc(${b}% + ${N}px)`},children:[(0,n.jsx)(A.ItemSlot,{scope:e.__scopeSlider,children:(0,n.jsx)(w.span,{role:"slider","aria-label":e["aria-label"]||y,"aria-valuemin":l.min,"aria-valuenow":g,"aria-valuemax":l.max,"aria-orientation":l.orientation,"data-orientation":l.orientation,"data-disabled":l.disabled?"":void 0,tabIndex:l.disabled?void 0:0,...o,ref:m,style:void 0===g?{display:"none"}:e.style,onFocus:c(e.onFocus,()=>{l.valueIndexToChangeRef.current=a})})}),x&&(0,n.jsx)(G,{name:i??(l.name?l.name+(l.values.length>1?"[]":""):void 0),form:l.form,value:g},a)]})});W.displayName=K;var G=s.forwardRef(({__scopeSlider:e,value:t,...r},a)=>{let i=s.useRef(null),o=p(i,a),l=function(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return s.useEffect(()=>{let e=i.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(l!==t&&r){let n=new Event("input",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[l,t]),(0,n.jsx)(w.input,{style:{display:"none"},...r,ref:o,defaultValue:t})});function X(e,t,r){return u(100/(r-t)*(e-t),[0,100])}function Z(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}G.displayName="RadioBubbleInput";var Q=r(4780);let ee=s.forwardRef(({className:e,...t},r)=>(0,n.jsxs)(F,{ref:r,className:(0,Q.cn)("relative flex w-full touch-none select-none items-center",e),...t,children:[(0,n.jsx)(J,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,n.jsx)(H,{className:"absolute h-full bg-primary"})}),(0,n.jsx)(W,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));ee.displayName=F.displayName;var et=r(71669),er=r(63442),en=r(27605),ea=r(45880);let es=function(){let[e,t]=(0,s.useState)([]),[r,a]=(0,s.useState)(!1),i=[{label:"1024 x 1024",value:"1024x1024"},{label:"768 x 1024",value:"768x1024"},{label:"1536 x 2048",value:"1536x2048"},{label:"576 x 1024",value:"576x1024"},{label:"1152 x 2048",value:"1152x2048"}],u=ea.Ik({prompt:ea.Yj().max(200,{message:"提示词不能超过200字"}).min(1,{message:"请输入提示词"}),size:ea.Yj().default("1024x1024"),count:ea.ai().min(1).max(4).default(1)}),c=(0,en.mN)({resolver:(0,er.u)(u),defaultValues:{prompt:"",size:"1024x1024",count:1}});async function f(e){a(!0),console.log(e),setTimeout(()=>{t(Array(e.count).fill("https://plus.unsplash.com/premium_photo-1683910982837-81f0671bbb0d")),a(!1)},2e3)}return(0,n.jsxs)("div",{className:"container py-6 space-y-8",children:[(0,n.jsx)("div",{className:"mx-auto max-w-3xl",children:(0,n.jsx)(et.lV,{...c,children:(0,n.jsxs)("form",{onSubmit:c.handleSubmit(f),className:"space-y-6",children:[(0,n.jsx)(et.zB,{control:c.control,name:"prompt",render:({field:e})=>(0,n.jsxs)(et.eI,{children:[(0,n.jsx)(et.lR,{children:"提示词"}),(0,n.jsx)(et.MJ,{children:(0,n.jsx)(l.T,{placeholder:"描述您想要生成的图片...",className:"resize-none min-h-[100px]",maxLength:200,...e})}),(0,n.jsxs)("div",{className:"text-xs text-right text-muted-foreground",children:[e.value.length,"/200"]}),(0,n.jsx)(et.C5,{})]})}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsx)(et.zB,{control:c.control,name:"size",render:({field:e})=>(0,n.jsxs)(et.eI,{children:[(0,n.jsx)(et.lR,{children:"尺寸"}),(0,n.jsxs)(o.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,n.jsx)(et.MJ,{children:(0,n.jsx)(o.bq,{children:(0,n.jsx)(o.yv,{placeholder:"选择图片尺寸"})})}),(0,n.jsx)(o.gC,{children:i.map(e=>(0,n.jsx)(o.eb,{value:e.value,children:e.label},e.value))})]}),(0,n.jsx)(et.C5,{})]})}),(0,n.jsx)(et.zB,{control:c.control,name:"count",render:({field:e})=>(0,n.jsxs)(et.eI,{children:[(0,n.jsxs)(et.lR,{children:["生成数量: ",e.value]}),(0,n.jsx)(et.MJ,{children:(0,n.jsx)(ee,{min:1,max:4,step:1,defaultValue:[e.value],onValueChange:t=>e.onChange(t[0]),className:"pt-2"})}),(0,n.jsx)(et.C5,{})]})})]}),(0,n.jsx)(d.$,{type:"submit",className:"w-full",disabled:r,children:r?"生成中...":"生成图片"})]})})}),r&&(0,n.jsx)("div",{className:"flex justify-center items-center py-10",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}),e.length>0&&!r&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h3",{className:"text-lg font-medium",children:"生成结果"}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:e.map((e,t)=>(0,n.jsxs)("div",{className:"overflow-hidden rounded-lg border",children:[(0,n.jsx)("img",{src:e,alt:`Generated image ${t+1}`,className:"w-full h-auto object-cover aspect-square"}),(0,n.jsxs)("div",{className:"p-3 flex justify-between bg-muted/20",children:[(0,n.jsx)(d.$,{variant:"outline",size:"sm",children:"下载"}),(0,n.jsx)(d.$,{variant:"outline",size:"sm",children:"分享"})]})]},t))})]})]})},ei=function(){let e=[{id:"copywriting",label:"文案"},{id:"image",label:"图片",content:(0,n.jsx)(es,{})}];return(0,n.jsx)("div",{className:"space-y-4 p-4",children:(0,n.jsx)(a.Q,{defaultTab:"copywriting",tabs:e})})}},53226:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\trae\\\\cardmees\\\\fronend\\\\src\\\\app\\\\aiTools\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\trae\\cardmees\\fronend\\src\\app\\aiTools\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>i});var n=r(37413),a=r(24597),s=r(36733);let i={title:"CardMees",description:"CardMees Application"};function o({children:e}){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.default,{}),(0,n.jsxs)("div",{className:"flex h-[calc(100vh)]",children:[(0,n.jsx)(s.default,{}),(0,n.jsx)("main",{className:"flex-1 p-8 pt-16 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400",children:(0,n.jsx)("div",{className:"max-w-8xl mx-auto bg-white rounded-lg shadow-sm",children:e})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63442:(e,t,r)=>{"use strict";r.d(t,{u:()=>d});var n=r(27605);let a=(e,t,r)=>{if(e&&"reportValidity"in e){let a=(0,n.Jt)(r,t);e.setCustomValidity(a&&a.message||""),e.reportValidity()}},s=(e,t)=>{for(let r in t.fields){let n=t.fields[r];n&&n.ref&&"reportValidity"in n.ref?a(n.ref,r,e):n&&n.refs&&n.refs.forEach(t=>a(t,r,e))}},i=(e,t)=>{t.shouldUseNativeValidation&&s(e,t);let r={};for(let a in e){let s=(0,n.Jt)(t.fields,a),i=Object.assign(e[a]||{},{ref:s&&s.ref});if(o(t.names||Object.keys(e),a)){let e=Object.assign({},(0,n.Jt)(r,a));(0,n.hZ)(e,"root",i),(0,n.hZ)(r,a,e)}else(0,n.hZ)(r,a,i)}return r},o=(e,t)=>{let r=l(t);return e.some(e=>l(e).match(`^${r}\\.\\d+`))};function l(e){return e.replace(/\]|\[/g,"")}function d(e,t,r){return void 0===r&&(r={}),function(a,o,l){try{return Promise.resolve(function(n,i){try{var o=Promise.resolve(e["sync"===r.mode?"parse":"parseAsync"](a,t)).then(function(e){return l.shouldUseNativeValidation&&s({},l),{errors:{},values:r.raw?Object.assign({},a):e}})}catch(e){return i(e)}return o&&o.then?o.then(void 0,i):o}(0,function(e){if(Array.isArray(null==e?void 0:e.errors))return{values:{},errors:i(function(e,t){for(var r={};e.length;){var a=e[0],s=a.code,i=a.message,o=a.path.join(".");if(!r[o]){if("unionErrors"in a){var l=a.unionErrors[0].errors[0];r[o]={message:l.message,type:l.code}}else r[o]={message:i,type:s}}if("unionErrors"in a&&a.unionErrors.forEach(function(t){return t.errors.forEach(function(t){return e.push(t)})}),t){var d=r[o].types,u=d&&d[a.code];r[o]=(0,n.Gb)(o,t,r,s,u?[].concat(u,a.message):a.message)}e.shift()}return r}(e.errors,!l.shouldUseNativeValidation&&"all"===l.criteriaMode),l)};throw e}))}catch(e){return Promise.reject(e)}}}},71669:(e,t,r)=>{"use strict";r.d(t,{C5:()=>g,MJ:()=>h,Rr:()=>v,eI:()=>p,lR:()=>x,lV:()=>d,zB:()=>c});var n=r(60687),a=r(43210),s=r(8730),i=r(27605),o=r(4780),l=r(80013);let d=i.Op,u=a.createContext({}),c=({...e})=>(0,n.jsx)(u.Provider,{value:{name:e.name},children:(0,n.jsx)(i.xI,{...e})}),f=()=>{let e=a.useContext(u),t=a.useContext(m),{getFieldState:r,formState:n}=(0,i.xW)(),s=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...s}},m=a.createContext({}),p=a.forwardRef(({className:e,...t},r)=>{let s=a.useId();return(0,n.jsx)(m.Provider,{value:{id:s},children:(0,n.jsx)("div",{ref:r,className:(0,o.cn)("space-y-2",e),...t})})});p.displayName="FormItem";let x=a.forwardRef(({className:e,...t},r)=>{let{error:a,formItemId:s}=f();return(0,n.jsx)(l.J,{ref:r,className:(0,o.cn)(a&&"text-destructive",e),htmlFor:s,...t})});x.displayName="FormLabel";let h=a.forwardRef(({...e},t)=>{let{error:r,formItemId:a,formDescriptionId:i,formMessageId:o}=f();return(0,n.jsx)(s.DX,{ref:t,id:a,"aria-describedby":r?`${i} ${o}`:`${i}`,"aria-invalid":!!r,...e})});h.displayName="FormControl";let v=a.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:a}=f();return(0,n.jsx)("p",{ref:r,id:a,className:(0,o.cn)("text-sm text-muted-foreground",e),...t})});v.displayName="FormDescription";let g=a.forwardRef(({className:e,children:t,...r},a)=>{let{error:s,formMessageId:i}=f(),l=s?String(s?.message??""):t;return l?(0,n.jsx)("p",{ref:a,id:i,className:(0,o.cn)("text-sm font-medium text-destructive",e),...r,children:l}):null});g.displayName="FormMessage"},74075:e=>{"use strict";e.exports=require("zlib")},76245:(e,t,r)=>{Promise.resolve().then(r.bind(r,47076))},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var n=r(60687),a=r(43210),s=r(78148),i=r(24224),o=r(4780);let l=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.b,{ref:r,className:(0,o.cn)(l(),e),...t}));d.displayName=s.b.displayName},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85973:(e,t,r)=>{Promise.resolve().then(r.bind(r,53226))},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var n=r(60687);r(43210);var a=r(24224),s=r(4780);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,n.jsx)("div",{className:(0,s.cn)(i({variant:t}),e),...r})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,7392,5814,3928,3443,2951,1991,7605,3019,9879],()=>r(625));module.exports=n})();