"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3716],{51335:(e,l,s)=>{s.r(l),s.d(l,{default:()=>_});var a=s(95155),r=s(54165),t=s(12115),n=s(10081),d=s(54416),i=s(5196),o=s(59434),c=s(30285),u=s(77740),m=s(47924);let p=t.forwardRef((e,l)=>{let{className:s,...r}=e;return(0,a.jsx)(u.uB,{ref:l,className:(0,o.cn)("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",s),...r})});p.displayName=u.uB.displayName;let f=t.forwardRef((e,l)=>{let{className:s,...r}=e;return(0,a.jsxs)("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),(0,a.jsx)(u.uB.Input,{ref:l,className:(0,o.cn)("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",s),...r})]})});f.displayName=u.uB.Input.displayName;let x=t.forwardRef((e,l)=>{let{className:s,...r}=e;return(0,a.jsx)(u.uB.List,{ref:l,className:(0,o.cn)("max-h-[300px] overflow-y-auto overflow-x-hidden",s),...r})});x.displayName=u.uB.List.displayName;let h=t.forwardRef((e,l)=>(0,a.jsx)(u.uB.Empty,{ref:l,className:"py-6 text-center text-sm",...e}));h.displayName=u.uB.Empty.displayName;let g=t.forwardRef((e,l)=>{let{className:s,...r}=e;return(0,a.jsx)(u.uB.Group,{ref:l,className:(0,o.cn)("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",s),...r})});g.displayName=u.uB.Group.displayName;let j=t.forwardRef((e,l)=>{let{className:s,...r}=e;return(0,a.jsx)(u.uB.Separator,{ref:l,className:(0,o.cn)("-mx-1 h-px bg-border",s),...r})});j.displayName=u.uB.Separator.displayName;let b=t.forwardRef((e,l)=>{let{className:s,...r}=e;return(0,a.jsx)(u.uB.Item,{ref:l,className:(0,o.cn)("relative flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",s),...r})});b.displayName=u.uB.Item.displayName;var y=s(14636),N=s(26126);function v(e){let{options:l,selected:s,onChange:r,placeholder:u="选择选项...",emptyPlaceholder:m="没有找到选项",searchPlaceholder:v="搜索选项...",disabled:w=!1,maxSelected:k,selectedItemsDisplay:C="badges",className:B,showSelectAll:_=!1,showCheckboxes:E=!0}=e,[I,R]=t.useState(!1),[S,A]=t.useState(""),L=t.useMemo(()=>{let e={};return l.forEach(l=>{let s=l.group||"default";e[s]||(e[s]=[]),e[s].push(l)}),e},[l]),M=t.useMemo(()=>Object.keys(L).filter(e=>"default"!==e),[L]),D=t.useCallback(e=>{let l;if(!w){if(s.includes(e))l=s.filter(l=>l!==e);else{if(void 0!==k&&s.length>=k)return;l=[...s,e]}r(l)}},[w,k,r,s]),O=t.useCallback(e=>{w||r(s.filter(l=>l!==e))},[w,r,s]),G=t.useCallback(()=>{if(!w){if(s.length===l.filter(e=>!e.disabled).length)r([]);else{let e=l.filter(e=>!e.disabled).map(e=>e.value);void 0!==k&&e.length>k?r(e.slice(0,k)):r(e)}}},[w,k,r,l,s]);t.useEffect(()=>{I||A("")},[I]);let $=t.useMemo(()=>{if(!S)return L;let e={};return Object.keys(L).forEach(l=>{let s=L[l].filter(e=>e.label.toLowerCase().includes(S.toLowerCase()));s.length>0&&(e[l]=s)}),e},[L,S]),z=t.useMemo(()=>{if(0===s.length)return u;if("count"===C)return"已选择 ".concat(s.length," 项").concat(k?" (最多 ".concat(k," 项)"):"");if("labels"===C){let e=s.map(e=>{let s=l.find(l=>l.value===e);return(null==s?void 0:s.label)||e});return e.length>2?"".concat(e.slice(0,2).join(", ")," 等 ").concat(e.length," 项"):e.join(", ")}return u},[k,l,u,s,C]),F=t.useMemo(()=>{let e=l.filter(e=>!e.disabled);return e.length>0&&e.every(e=>s.includes(e.value))},[l,s]);return(0,a.jsxs)(y.AM,{open:I,onOpenChange:R,children:[(0,a.jsx)(y.Wv,{asChild:!0,children:(0,a.jsxs)(c.$,{variant:"outline",role:"combobox","aria-expanded":I,className:(0,o.cn)("w-full justify-between",w&&"cursor-not-allowed opacity-50",B),onClick:e=>{if(w){e.preventDefault();return}},children:[(0,a.jsx)("span",{className:"truncate",children:z}),(0,a.jsx)(n.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,a.jsx)(y.hl,{className:"w-full p-0",align:"start",children:(0,a.jsxs)(p,{children:[(0,a.jsx)(f,{placeholder:v,value:S,onValueChange:A}),"badges"===C&&s.length>0&&(0,a.jsx)("div",{className:"p-2 border-b",children:(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:s.map(e=>{let s=l.find(l=>l.value===e);return(0,a.jsxs)(N.E,{variant:"secondary",className:"flex items-center gap-1",children:[(null==s?void 0:s.label)||e,(0,a.jsxs)("button",{className:"ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2",onKeyDown:l=>{"Enter"===l.key&&O(e)},onMouseDown:e=>{e.preventDefault(),e.stopPropagation()},onClick:()=>O(e),children:[(0,a.jsx)(d.A,{className:"h-3 w-3 text-muted-foreground hover:text-foreground"}),(0,a.jsxs)("span",{className:"sr-only",children:["移除 ",(null==s?void 0:s.label)||e]})]})]},e)})})}),(0,a.jsxs)(x,{children:[(0,a.jsx)(h,{children:m}),_&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(b,{onSelect:G,className:"cursor-pointer",children:[E&&(0,a.jsx)("div",{className:(0,o.cn)("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",F?"bg-primary text-primary-foreground":"opacity-50"),children:F&&(0,a.jsx)(i.A,{className:"h-3 w-3"})}),(0,a.jsx)("span",{children:F?"取消全选":"全选"})]}),(0,a.jsx)(j,{})]}),$.default&&$.default.length>0&&(0,a.jsx)(g,{children:$.default.map(e=>(0,a.jsxs)(b,{onSelect:()=>D(e.value),disabled:e.disabled,className:(0,o.cn)("cursor-pointer",e.disabled&&"cursor-not-allowed opacity-50"),children:[E&&(0,a.jsx)("div",{className:(0,o.cn)("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",s.includes(e.value)?"bg-primary text-primary-foreground":"opacity-50"),children:s.includes(e.value)&&(0,a.jsx)(i.A,{className:"h-3 w-3"})}),(0,a.jsx)("span",{children:e.label})]},e.value))}),M.map(e=>$[e]&&0!==$[e].length?(0,a.jsx)(g,{heading:e,children:$[e].map(e=>(0,a.jsxs)(b,{onSelect:()=>D(e.value),disabled:e.disabled,className:(0,o.cn)("cursor-pointer",e.disabled&&"cursor-not-allowed opacity-50"),children:[E&&(0,a.jsx)("div",{className:(0,o.cn)("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",s.includes(e.value)?"bg-primary text-primary-foreground":"opacity-50"),children:s.includes(e.value)&&(0,a.jsx)(i.A,{className:"h-3 w-3"})}),(0,a.jsx)("span",{children:e.label})]},e.value))},e):null)]}),k&&(0,a.jsxs)("div",{className:"p-2 text-xs text-muted-foreground border-t",children:["已选择 ",s.length,"/",k," 项"]})]})})]})}var w=s(85057),k=s(74651),C=s(70639),B=s(48436);let _=e=>{let{open:l,onOpenChange:s,data:n}=e,[d,i]=(0,t.useState)([]),{data:o}=(0,k.zo)({}),{data:u,isLoading:m}=(0,C.Qq)(n.id),[p,{isLoading:f}]=(0,C.RC)();(0,t.useEffect)(()=>{l&&u&&i(u.map(e=>e.courseId))},[l,u]);let x=async()=>{try{await p({productId:n.id,coursesData:{courseIds:d}}),B.l.success("课程绑定成功"),s(!1)}catch(e){B.l.error("课程绑定失败"),console.error("绑定课程失败:",e)}},h=(null==o?void 0:o.map(e=>({label:e.name,value:e.id})))||[];return(0,a.jsx)(r.lG,{open:l,onOpenChange:s,children:(0,a.jsxs)(r.Cf,{className:"sm:max-w-[425px] p-0 overflow-hidden",children:[(0,a.jsx)(r.c7,{className:"px-6 pt-6 pb-2",children:(0,a.jsx)(r.L3,{className:"text-xl font-semibold",children:"绑定课程"})}),(0,a.jsx)("div",{className:"px-6",children:(0,a.jsx)("div",{className:"grid gap-4 py-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(w.J,{htmlFor:"courses",className:"text-sm font-medium",children:"选择课程"}),(0,a.jsx)(v,{selectedItemsDisplay:"count",className:"w-full",placeholder:"请选择要绑定的课程",options:h||[],onChange:e=>i(e),selected:d,disabled:m},"courses"),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"可以选择多个课程进行绑定"})]})})}),(0,a.jsxs)(r.Es,{className:"px-6 py-4 border-t",children:[(0,a.jsx)(c.$,{variant:"outline",onClick:()=>s(!1),className:"mr-2",disabled:f,children:"取消"}),(0,a.jsx)(c.$,{onClick:x,disabled:f,children:f?"绑定中...":"确认绑定"})]})]})})}}}]);