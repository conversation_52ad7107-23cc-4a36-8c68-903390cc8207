(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6366],{26126:(e,a,t)=>{"use strict";t.d(a,{E:()=>i});var s=t(95155);t(12115);var r=t(74466),n=t(59434);let l=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:a,variant:t,...r}=e;return(0,s.jsx)("div",{className:(0,n.cn)(l({variant:t}),a),...r})}},30285:(e,a,t)=>{"use strict";t.d(a,{$:()=>o,r:()=>c});var s=t(95155),r=t(12115),n=t(99708),l=t(74466),i=t(59434);let c=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,a)=>{let{className:t,variant:r,size:l,asChild:o=!1,...d}=e,u=o?n.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(c({variant:r,size:l,className:t})),ref:a,...d})});o.displayName="Button"},41970:(e,a,t)=>{"use strict";t.d(a,{default:()=>b});var s=t(95155),r=t(9110),n=t(12115),l=t(26126),i=t(73168),c=t(24122),o=t(6874),d=t.n(o);let u=[{accessorKey:"title",header:"信息标题",cell:e=>{let{row:a}=e,t="unread"===a.getValue("status");return(0,s.jsx)(d(),{href:"/notifications/".concat(a.original.id),className:"hover:underline",children:(0,s.jsx)("div",{className:"font-medium ".concat(t?"text-slate-900":"text-slate-600"),children:a.getValue("title")})})}},{accessorKey:"createdAt",header:"发布时间",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"text-sm",children:(0,i.GP)(a.getValue("createdAt"),"yyyy-MM-dd",{locale:c.g})})}},{accessorKey:"type",header:"消息类型",cell:e=>{let{row:a}=e,t=a.getValue("type");return(0,s.jsx)(l.E,{variant:"system"===t?"secondary":"outline",children:"system"===t?"系统消息":"机构消息"})}},{accessorKey:"creatorName",header:"创建人",cell:e=>{let{row:a}=e;return(0,s.jsx)("div",{className:"text-sm",children:a.getValue("creatorName")})}},{accessorKey:"status",header:"状态",cell:e=>{let{row:a}=e,t=a.getValue("status");return(0,s.jsx)(l.E,{variant:"unread"===t?"destructive":"outline",className:"opacity-80",children:"read"===t?"已读":"未读"})}},{id:"rowStyle",cell:e=>{let{row:a}=e;return null},meta:{getRowClassName:e=>"unread"===e.getValue("status")?"bg-blue-50 dark:bg-blue-950/20":"bg-white dark:bg-slate-950"}}];var x=t(59409),m=t(30285),h=t(62525),g=t(5196),f=t(90437),p=t(48436),v=t(48432),j=t(57786);let b=function(){let[e,a]=(0,n.useState)([]),[t,l]=(0,n.useState)(1),[i,c]=(0,n.useState)(10),[o,d]=(0,n.useState)("all"),{data:b,isLoading:y}=(0,j.D3)({page:t,pageSize:i,status:o}),[N]=(0,j._R)(),[w]=(0,j.Fj)(),[k]=(0,j.eT)(),C=(0,n.useCallback)(e=>{d(e),l(1)},[]),P=(0,n.useCallback)(async()=>{if(0!==e.length)try{let a=e.map(e=>e.id);await N({ids:a}).unwrap(),p.l.success("标记成功")}catch(e){console.error("标记已读失败:",e),p.l.error("标记失败")}},[e,N]),A=(0,n.useCallback)(async()=>{try{await w().unwrap(),p.l.success("全部标记成功")}catch(e){console.error("标记全部已读失败:",e),p.l.error("标记失败")}},[w]),V=(0,n.useCallback)(async()=>{if(0!==e.length)try{let a=e.map(e=>e.id);await k({ids:a}).unwrap(),p.l.success("删除成功")}catch(e){console.error("删除通知失败:",e),p.l.error("删除失败")}},[e,k]);return(0,s.jsxs)("div",{className:"space-y-6 p-4",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4 mb-4",children:[(0,s.jsxs)(x.l6,{value:o,onValueChange:C,children:[(0,s.jsx)(x.bq,{className:"w-full sm:w-[80px]",children:(0,s.jsx)(x.yv,{placeholder:"筛选通知"})}),(0,s.jsxs)(x.gC,{children:[(0,s.jsx)(x.eb,{value:"all",children:"全部"}),(0,s.jsx)(x.eb,{value:"read",children:"已读"}),(0,s.jsx)(x.eb,{value:"unread",children:"未读"})]})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,s.jsxs)(m.$,{onClick:V,disabled:0===e.length,variant:"outline",size:"sm",className:"flex items-center gap-1",children:[(0,s.jsx)(h.A,{className:"h-4 w-4"})," 删除"]}),(0,s.jsxs)(m.$,{disabled:0===e.length,onClick:P,variant:"outline",size:"sm",className:"flex items-center gap-1",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"})," 标记为已读"]}),(0,s.jsxs)(m.$,{onClick:A,variant:"default",size:"sm",className:"flex items-center gap-1",children:[(0,s.jsx)(f.A,{className:"h-4 w-4"})," 全部标为已读"]})]})]}),(0,s.jsx)(r.b,{columns:u,data:(null==b?void 0:b.list)||[],selectable:!0,pagination:!1,onSelectedRowsChange:a,loading:y}),(null==b?void 0:b.total)&&b.total>9&&(0,s.jsx)(v.default,{pageSize:i,currentPage:t,onPageChange:l,onPageSizeChange:c,totalItems:b.total})]})}},48432:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>v});var s=t(95155),r=t(12115),n=t(42355),l=t(13052),i=t(5623),c=t(59434),o=t(30285);let d=e=>{let{className:a,...t}=e;return(0,s.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,c.cn)("mx-auto flex w-full justify-center",a),...t})};d.displayName="Pagination";let u=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("ul",{ref:a,className:(0,c.cn)("flex flex-row items-center gap-1",t),...r})});u.displayName="PaginationContent";let x=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("li",{ref:a,className:(0,c.cn)("",t),...r})});x.displayName="PaginationItem";let m=e=>{let{className:a,isActive:t,size:r="icon",...n}=e;return(0,s.jsx)("a",{"aria-current":t?"page":void 0,className:(0,c.cn)((0,o.r)({variant:t?"outline":"ghost",size:r}),a),...n})};m.displayName="PaginationLink";let h=e=>{let{className:a,...t}=e;return(0,s.jsxs)(m,{"aria-label":"Go to previous page",size:"default",className:(0,c.cn)("gap-1 pl-2.5",a),...t,children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"上一页"})]})};h.displayName="PaginationPrevious";let g=e=>{let{className:a,...t}=e;return(0,s.jsxs)(m,{"aria-label":"Go to next page",size:"default",className:(0,c.cn)("gap-1 pr-2.5",a),...t,children:[(0,s.jsx)("span",{children:"下一页"}),(0,s.jsx)(l.A,{className:"h-4 w-4"})]})};g.displayName="PaginationNext";let f=e=>{let{className:a,...t}=e;return(0,s.jsxs)("span",{"aria-hidden":!0,className:(0,c.cn)("flex h-9 w-9 items-center justify-center",a),...t,children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"更多页"})]})};f.displayName="PaginationEllipsis";var p=t(59409);function v(e){let{currentPage:a,pageSize:t,totalItems:r,onPageChange:i,onPageSizeChange:c}=e,o=Math.ceil(r/t),v=(()=>{let e=[];if(o<=5){for(let a=1;a<=o;a++)e.push(a);return e}e.push(1);let t=Math.max(2,a-1),s=Math.min(a+1,o-1);2===t&&(s=Math.min(t+2,o-1)),s===o-1&&(t=Math.max(s-2,2)),t>2&&e.push("ellipsis-start");for(let a=t;a<=s;a++)e.push(a);return s<o-1&&e.push("ellipsis-end"),o>1&&e.push(o),e})(),j=0===r?0:(a-1)*t+1,b=Math.min(a*t,r);return(0,s.jsxs)("div",{className:"flex flex-col gap-4 px-2 py-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"显示"}),(0,s.jsxs)(p.l6,{value:t.toString(),onValueChange:e=>{c(Number(e))},children:[(0,s.jsx)(p.bq,{className:"w-[4.5rem] h-7 text-xs border-border/60 hover:bg-accent/30 transition-colors",children:(0,s.jsx)(p.yv,{})}),(0,s.jsx)(p.gC,{children:[10,20,30,50].map(e=>(0,s.jsx)(p.eb,{value:e.toString(),className:"text-xs",children:e},e))})]}),(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"条"}),(0,s.jsx)("span",{className:"text-muted-foreground/40 mx-1.5",children:"|"}),r>0?(0,s.jsxs)("span",{className:"text-muted-foreground/80",children:[j,"-",b," / ",r," 条记录"]}):(0,s.jsx)("span",{className:"text-muted-foreground/80",children:"0 条记录"})]}),(0,s.jsx)(d,{children:(0,s.jsxs)(u,{className:"gap-1",children:[(0,s.jsx)(x,{children:(0,s.jsx)(h,{onClick:()=>i(Math.max(1,a-1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(1===a?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,s.jsx)(n.A,{className:"h-4 w-4 mr-1"})})}),v.map((e,t)=>"ellipsis-start"===e||"ellipsis-end"===e?(0,s.jsx)(x,{children:(0,s.jsx)(f,{className:"h-8 w-8 flex items-center justify-center text-xs"})},"ellipsis-".concat(t)):(0,s.jsx)(x,{children:(0,s.jsx)(m,{onClick:()=>i(e),isActive:a===e,className:"h-8 w-8 text-xs font-medium rounded-md transition-colors data-[active]:bg-primary data-[active]:text-primary-foreground hover:bg-accent/50 hover:text-accent-foreground/90","aria-label":"前往第 ".concat(e," 页"),children:e})},e)),(0,s.jsx)(x,{children:(0,s.jsx)(g,{onClick:()=>i(Math.min(o,a+1)),className:"h-8 px-2.5 text-xs font-medium rounded-md transition-colors ".concat(a===o||0===o?"opacity-50 cursor-not-allowed":"hover:bg-accent/50 hover:text-accent-foreground/90"),children:(0,s.jsx)(l.A,{className:"h-4 w-4 ml-1"})})})]})})]})}},50430:(e,a,t)=>{Promise.resolve().then(t.bind(t,41970))},59434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>n});var s=t(52596),r=t(39688);function n(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,8687,4201,8737,4540,4582,5620,3168,9613,7945,6874,2207,9624,9110,6315,7358],()=>a(50430)),_N_E=e.O()}]);