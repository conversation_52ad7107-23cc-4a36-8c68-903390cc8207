#!/bin/bash
set -e

# Configuration
DOCKER_REGISTRY=${DOCKER_REGISTRY:-"your-registry.com"}
IMAGE_NAME=${IMAGE_NAME:-"cardmees-api"}
IMAGE_TAG=${IMAGE_TAG:-$(git rev-parse --short HEAD)}
NAMESPACE="cardmees"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting deployment process for Cardmees API...${NC}"

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}kubectl is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}docker is not installed. Please install it first.${NC}"
    exit 1
fi

# Build Docker image
echo -e "${YELLOW}Building Docker image...${NC}"
docker build -t ${DOCKER_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG} .
docker tag ${DOCKER_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG} ${DOCKER_REGISTRY}/${IMAGE_NAME}:latest

# Push Docker image
echo -e "${YELLOW}Pushing Docker image to registry...${NC}"
docker push ${DOCKER_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}
docker push ${DOCKER_REGISTRY}/${IMAGE_NAME}:latest

# Create namespace if it doesn't exist
echo -e "${YELLOW}Creating namespace if it doesn't exist...${NC}"
kubectl apply -f k8s/namespace.yaml

# Apply ConfigMap and Secrets
echo -e "${YELLOW}Applying ConfigMap and Secrets...${NC}"
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml

# Apply database and cache deployments
echo -e "${YELLOW}Deploying PostgreSQL and Redis...${NC}"
kubectl apply -f k8s/postgres-deployment.yaml
kubectl apply -f k8s/postgres-service.yaml
kubectl apply -f k8s/redis-deployment.yaml
kubectl apply -f k8s/redis-service.yaml

# Wait for database and cache to be ready
echo -e "${YELLOW}Waiting for PostgreSQL and Redis to be ready...${NC}"
kubectl wait --for=condition=ready pod -l app=postgres --timeout=300s -n ${NAMESPACE}
kubectl wait --for=condition=ready pod -l app=redis --timeout=300s -n ${NAMESPACE}

# Apply API and queue deployments
echo -e "${YELLOW}Deploying API and Image Queue...${NC}"
# Replace the image tag in the deployment files
sed -i "s|\${DOCKER_REGISTRY}|${DOCKER_REGISTRY}|g" k8s/api-deployment.yaml
sed -i "s|\${DOCKER_REGISTRY}|${DOCKER_REGISTRY}|g" k8s/image-queue-deployment.yaml

kubectl apply -f k8s/api-deployment.yaml
kubectl apply -f k8s/api-service.yaml
kubectl apply -f k8s/image-queue-deployment.yaml

# Apply HPA
echo -e "${YELLOW}Applying Horizontal Pod Autoscalers...${NC}"
kubectl apply -f k8s/api-hpa.yaml
kubectl apply -f k8s/image-queue-hpa.yaml

# Apply Network Policy
echo -e "${YELLOW}Applying Network Policy...${NC}"
kubectl apply -f k8s/network-policy.yaml

# Apply Ingress
echo -e "${YELLOW}Applying Ingress...${NC}"
kubectl apply -f k8s/ingress.yaml

# Wait for API to be ready
echo -e "${YELLOW}Waiting for API to be ready...${NC}"
kubectl wait --for=condition=ready pod -l app=api --timeout=300s -n ${NAMESPACE}

echo -e "${GREEN}Deployment completed successfully!${NC}"
echo -e "${GREEN}API is accessible at: https://api.cardmees.com${NC}"

# Show pods status
echo -e "${YELLOW}Current pods status:${NC}"
kubectl get pods -n ${NAMESPACE}

exit 0
