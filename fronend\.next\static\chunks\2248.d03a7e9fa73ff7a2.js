"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2248],{46308:(e,t,n)=>{n.d(t,{A:()=>s});let s=(0,n(19946).A)("Receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z",key:"q3az6g"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17.5v-11",key:"1jc1ny"}]])},57001:(e,t,n)=>{n.d(t,{p:()=>d});var s=n(95155),a=n(30285),l=n(46102);function d(e){let{icon:t,tooltipText:n,tooltipSide:d="top",tooltipAlign:i="center",delayDuration:r=300,variant:c="ghost",size:h="icon",className:o="h-8 w-8 hover:bg-muted",...p}=e;return(0,s.jsx)(l.Bc,{delayDuration:r,children:(0,s.jsxs)(l.m_,{children:[(0,s.jsx)(l.k$,{asChild:!0,children:(0,s.jsx)(a.$,{variant:c,size:h,className:o,...p,children:(0,s.jsx)(t,{className:"h-4 w-4 text-muted-foreground"})})}),(0,s.jsx)(l.ZI,{side:d,align:i,className:"font-medium text-xs px-3 py-1.5",children:(0,s.jsx)("p",{children:n})})]})})}},82248:(e,t,n)=>{n.r(t),n.d(t,{default:()=>c});var s=n(95155),a=n(12115),l=n(55028),d=n(46308),i=n(57001);let r=(0,l.default)(()=>Promise.all([n.e(81),n.e(110),n.e(3101)]).then(n.bind(n,53101)),{loadableGenerated:{webpack:()=>[53101]},ssr:!1}),c=function(e){let{student:t}=e,[n,l]=a.useState(!1);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.p,{icon:d.A,tooltipText:"缴费",onClick:()=>l(!0)}),n&&(0,s.jsx)(r,{open:n,onOpenChange:l,student:t})]})}}}]);