"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5799],{15452:(e,t,a)=>{a.d(t,{G$:()=>H,Hs:()=>v,UC:()=>et,VY:()=>en,ZL:()=>Q,bL:()=>X,bm:()=>er,hE:()=>ea,hJ:()=>ee,l9:()=>K});var n=a(12115),r=a(85185),o=a(6101),s=a(46081),l=a(61285),d=a(5845),i=a(19178),c=a(25519),u=a(34378),p=a(28905),f=a(63655),m=a(92293),g=a(93795),x=a(38168),h=a(99708),y=a(95155),j="Dialog",[b,v]=(0,s.A)(j),[N,w]=b(j),C=e=>{let{__scopeDialog:t,children:a,open:r,defaultOpen:o,onOpenChange:s,modal:i=!0}=e,c=n.useRef(null),u=n.useRef(null),[p=!1,f]=(0,d.i)({prop:r,defaultProp:o,onChange:s});return(0,y.jsx)(N,{scope:t,triggerRef:c,contentRef:u,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:i,children:a})};C.displayName=j;var k="DialogTrigger",D=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,s=w(k,a),l=(0,o.s)(t,s.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":Z(s.open),...n,ref:l,onClick:(0,r.m)(e.onClick,s.onOpenToggle)})});D.displayName=k;var R="DialogPortal",[I,E]=b(R,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:a,children:r,container:o}=e,s=w(R,t);return(0,y.jsx)(I,{scope:t,forceMount:a,children:n.Children.map(r,e=>(0,y.jsx)(p.C,{present:a||s.open,children:(0,y.jsx)(u.Z,{asChild:!0,container:o,children:e})}))})};O.displayName=R;var _="DialogOverlay",F=n.forwardRef((e,t)=>{let a=E(_,e.__scopeDialog),{forceMount:n=a.forceMount,...r}=e,o=w(_,e.__scopeDialog);return o.modal?(0,y.jsx)(p.C,{present:n||o.open,children:(0,y.jsx)(A,{...r,ref:t})}):null});F.displayName=_;var A=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=w(_,a);return(0,y.jsx)(g.A,{as:h.DX,allowPinchZoom:!0,shards:[r.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":Z(r.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),z="DialogContent",M=n.forwardRef((e,t)=>{let a=E(z,e.__scopeDialog),{forceMount:n=a.forceMount,...r}=e,o=w(z,e.__scopeDialog);return(0,y.jsx)(p.C,{present:n||o.open,children:o.modal?(0,y.jsx)(P,{...r,ref:t}):(0,y.jsx)(G,{...r,ref:t})})});M.displayName=z;var P=n.forwardRef((e,t)=>{let a=w(z,e.__scopeDialog),s=n.useRef(null),l=(0,o.s)(t,a.contentRef,s);return n.useEffect(()=>{let e=s.current;if(e)return(0,x.Eq)(e)},[]),(0,y.jsx)(T,{...e,ref:l,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=a.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),G=n.forwardRef((e,t)=>{let a=w(z,e.__scopeDialog),r=n.useRef(!1),o=n.useRef(!1);return(0,y.jsx)(T,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,s;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(r.current||null===(s=a.triggerRef.current)||void 0===s||s.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var n,s;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let l=t.target;(null===(s=a.triggerRef.current)||void 0===s?void 0:s.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),T=n.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:l,...d}=e,u=w(z,a),p=n.useRef(null),f=(0,o.s)(t,p);return(0,m.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:l,children:(0,y.jsx)(i.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Z(u.open),...d,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(J,{titleId:u.titleId}),(0,y.jsx)(Y,{contentRef:p,descriptionId:u.descriptionId})]})]})}),q="DialogTitle",B=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=w(q,a);return(0,y.jsx)(f.sG.h2,{id:r.titleId,...n,ref:t})});B.displayName=q;var L="DialogDescription",S=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=w(L,a);return(0,y.jsx)(f.sG.p,{id:r.descriptionId,...n,ref:t})});S.displayName=L;var V="DialogClose",U=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,o=w(V,a);return(0,y.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,r.m)(e.onClick,()=>o.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}U.displayName=V;var $="DialogTitleWarning",[H,W]=(0,s.q)($,{contentName:z,titleName:q,docsSlug:"dialog"}),J=e=>{let{titleId:t}=e,a=W($),r="`".concat(a.contentName,"` requires a `").concat(a.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(a.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(a.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},Y=e=>{let{contentRef:t,descriptionId:a}=e,r=W("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");a&&n&&!document.getElementById(a)&&console.warn(o)},[o,t,a]),null},X=C,K=D,Q=O,ee=F,et=M,ea=B,en=S,er=U},54165:(e,t,a)=>{a.d(t,{Cf:()=>f,Es:()=>g,HM:()=>u,L3:()=>x,c7:()=>m,lG:()=>d,rr:()=>h,zM:()=>i});var n=a(95155),r=a(12115),o=a(15452),s=a(54416),l=a(59434);let d=o.bL,i=o.l9,c=o.ZL,u=o.bm,p=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(o.hJ,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r})});p.displayName=o.hJ.displayName;let f=r.forwardRef((e,t)=>{let{className:a,children:r,...d}=e;return(0,n.jsxs)(c,{children:[(0,n.jsx)(p,{}),(0,n.jsxs)(o.UC,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...d,children:[r,(0,n.jsxs)(o.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(s.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});f.displayName=o.UC.displayName;let m=e=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};m.displayName="DialogHeader";let g=e=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};g.displayName="DialogFooter";let x=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(o.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});x.displayName=o.hE.displayName;let h=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(o.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",a),...r})});h.displayName=o.VY.displayName},54416:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},55799:(e,t,a)=>{a.r(t),a.d(t,{default:()=>m});var n=a(95155),r=a(12115);let o=(0,a(19946).A)("UserRoundCog",[["path",{d:"M2 21a8 8 0 0 1 10.434-7.62",key:"1yezr2"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["circle",{cx:"18",cy:"18",r:"3",key:"1xkwt0"}],["path",{d:"m19.5 14.3-.4.9",key:"1eb35c"}],["path",{d:"m16.9 20.8-.4.9",key:"dfjc4z"}],["path",{d:"m21.7 19.5-.9-.4",key:"q4dx6b"}],["path",{d:"m15.2 16.9-.9-.4",key:"1r0w5f"}],["path",{d:"m21.7 16.5-.9.4",key:"1knoei"}],["path",{d:"m15.2 19.1-.9.4",key:"j188fs"}],["path",{d:"m19.5 21.7-.4-.9",key:"1tonu5"}],["path",{d:"m16.9 15.2-.4-.9",key:"699xu"}]]);var s=a(30285),l=a(54165),d=a(59409),i=a(57141);let c=function(e){let{open:t,onOpenChange:a,studentType:o,handleSubmit:c}=e,[u,p]=r.useState("");return t?(0,n.jsx)(l.lG,{open:t,onOpenChange:a,children:(0,n.jsxs)(l.Cf,{className:"sm:max-w-[425px] rounded-lg border shadow-lg",children:[(0,n.jsx)(l.c7,{className:"pb-4 border-b",children:(0,n.jsx)(l.L3,{className:"text-xl font-medium",children:"修改学员类型"})}),(0,n.jsxs)("div",{className:"py-6",children:[(0,n.jsx)("label",{className:"text-sm text-gray-500 mb-2 block",children:"请选择新的学员类型"}),(0,n.jsxs)(d.l6,{defaultValue:o,onValueChange:e=>p(e),children:[(0,n.jsx)(d.bq,{className:"w-full h-10 rounded-md border border-gray-300 focus:ring-2 focus:ring-primary/20",children:(0,n.jsx)(d.yv,{placeholder:"请选择学员类型"})}),(0,n.jsx)(d.gC,{className:"max-h-60 overflow-auto rounded-md shadow-md",children:Object.entries(i.IC).map(e=>{let[t,a]=e;return(0,n.jsx)(d.eb,{value:t,className:"cursor-pointer hover:bg-gray-100",children:a.label},t)})})]})]}),(0,n.jsxs)(l.Es,{className:"pt-4 border-t flex justify-end gap-3",children:[(0,n.jsx)(s.$,{variant:"outline",onClick:()=>a(!1),className:"px-5 rounded-md hover:bg-gray-100",children:"取消"}),(0,n.jsx)(s.$,{onClick:()=>{if(""===o){alert("请选择学员类型");return}c&&c({selectStudentType:u}),p("")},className:"px-5 rounded-md shadow-sm",children:"确定"})]})]})}):null};var u=a(48436),p=a(95728),f=a(57001);let m=function(e){let{studentId:t,studentType:a}=e,[s]=(0,p.zy)(),[l,d]=r.useState(!1);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(f.p,{icon:o,tooltipText:"修改学员状态",onClick:()=>d(!0)}),l&&(0,n.jsx)(c,{open:l,onOpenChange:d,studentType:a,handleSubmit:e=>{s({studentId:t,data:{type:e.selectStudentType}}).unwrap().then(e=>{u.l.success("更新学员成功."),d(!1)}).catch(e=>{u.l.error("更新学员失败!")})}})]})}},57001:(e,t,a)=>{a.d(t,{p:()=>s});var n=a(95155),r=a(30285),o=a(46102);function s(e){let{icon:t,tooltipText:a,tooltipSide:s="top",tooltipAlign:l="center",delayDuration:d=300,variant:i="ghost",size:c="icon",className:u="h-8 w-8 hover:bg-muted",...p}=e;return(0,n.jsx)(o.Bc,{delayDuration:d,children:(0,n.jsxs)(o.m_,{children:[(0,n.jsx)(o.k$,{asChild:!0,children:(0,n.jsx)(r.$,{variant:i,size:c,className:u,...p,children:(0,n.jsx)(t,{className:"h-4 w-4 text-muted-foreground"})})}),(0,n.jsx)(o.ZI,{side:s,align:l,className:"font-medium text-xs px-3 py-1.5",children:(0,n.jsx)("p",{children:a})})]})})}}}]);