"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9624],{5959:(e,t,r)=>{r.d(t,{Lu:()=>s,YI:()=>a,bx:()=>n,gk:()=>o});let s=(0,r(29624).w)("billsApi",["Bills"]).injectEndpoints({endpoints:e=>({createBill:e.mutation({query:e=>({url:"/bills",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"创建账单失败")},invalidatesTags:["Bills"]}),getBills:e.query({query:e=>({url:"/bills",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Bills"]}),getBillChartData:e.query({query:e=>({url:"/bills/annual-data",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:null,providesTags:["Bills"]})})}),{useCreateBillMutation:a,useGetBillsQuery:o,useGetBillChartDataQuery:n}=s},6658:(e,t,r)=>{r.d(t,{X:()=>a,Y:()=>s});let s=(0,r(29624).w)("getTeacherIdAndNameApi",["getTeacherIdAndName"]).injectEndpoints({endpoints:e=>({getTeacherIdAndName:e.query({query:()=>"/institutions/teachers/select",transformResponse:e=>200===e.code&&e.data?e.data:[],providesTags:["getTeacherIdAndName"]})})}),{useGetTeacherIdAndNameQuery:a}=s},7986:(e,t,r)=>{r.d(t,{q:()=>$,M:()=>W});var s=r(52),a=r(5710),o=r(40694),n=r(61328),d=r(18091),u=r(6658);let i=(0,a.Z0)({name:"auth",initialState:{token:"",isAuthenticated:!0},reducers:{setCredentials:(e,t)=>{e.token=t.payload.token,e.isAuthenticated=!0},clearCredentials:e=>{e.token=null,e.isAuthenticated=!1}}}),{setCredentials:l,clearCredentials:c}=i.actions,m=i.reducer;var p=r(52991);let g=(0,a.Z0)({name:"userMenus",initialState:{menus:[],loading:!1,error:null},reducers:{setUserMenus:(e,t)=>{e.menus=t.payload,e.loading=!1,e.error=null},setLoading:(e,t)=>{e.loading=t.payload},setError:(e,t)=>{e.error=t.payload,e.loading=!1},addMenuItem:(e,t)=>{e.menus.push(t.payload)},updateMenuItem:(e,t)=>{let r=e.menus.findIndex(e=>e.id===t.payload.id);-1!==r&&(e.menus[r]=t.payload)},removeMenuItem:(e,t)=>{e.menus=e.menus.filter(e=>e.id!==t.payload)},clearMenus:e=>{e.menus=[]}}}),{setUserMenus:f,setLoading:h,setError:y,addMenuItem:T,updateMenuItem:S,removeMenuItem:q,clearMenus:v}=g.actions,E=g.reducer;var R=r(32422),w=r(35035),P=r(10407);let C=(0,a.Z0)({name:"notificationsUnreadCount",initialState:{count:0},reducers:{setUnreadCount:(e,t)=>{e.count=t.payload.count}}}),{setUnreadCount:A}=C.actions,I=C.reducer,b=(0,a.zD)("teacher/fetchTeachers",async(e,t)=>{let{dispatch:r,rejectWithValue:s}=t;try{console.log("开始获取教师列表...");let e=await r(u.Y.endpoints.getTeacherIdAndName.initiate());if("error"in e)return console.error("获取教师列表失败:",e.error),s("获取教师列表失败");let t=e.data;if(console.log("获取到的教师列表:",t),!t||!Array.isArray(t))return console.error("教师数据格式不正确:",t),s("教师数据格式不正确");return t.map(e=>({id:String(e.id),name:String(e.name)}))}catch(e){return console.error("获取教师列表失败:",e),s(e.message||"获取教师列表失败")}}),G=(0,a.Z0)({name:"teacher",initialState:{teachers:[],isLoading:!1,error:null},reducers:{clearTeachers:e=>{e.teachers=[],e.error=null}},extraReducers:e=>{e.addCase(b.pending,e=>{e.isLoading=!0,e.error=null}).addCase(b.fulfilled,(e,t)=>{e.isLoading=!1,e.teachers=t.payload,console.log("Redux state 更新后的教师列表:",e.teachers)}).addCase(b.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload||"获取教师列表失败",console.error("Redux state 更新失败:",e.error)})}}),{clearTeachers:k}=G.actions,O=G.reducer;var U=r(95728),L=r(27893),N=r(74651),M=r(12519),Z=r(78504),F=r(70639),j=r(71159),z=r(5959),D=r(13515),B=r(75521),x=r(98553),_=r(57786);let Q={key:"root",storage:d.A,whitelist:["user","userMenus","userPermissions"]},Y=(0,s.HY)({[U.W4.reducerPath]:U.W4.reducer,[u.Y.reducerPath]:u.Y.reducer,[P.v.reducerPath]:P.v.reducer,[L.ZM.reducerPath]:L.ZM.reducer,[N.hh.reducerPath]:N.hh.reducer,[M.ZZ.reducerPath]:M.ZZ.reducer,[Z.O.reducerPath]:Z.O.reducer,[F.ZN.reducerPath]:F.ZN.reducer,[j.rA.reducerPath]:j.rA.reducer,[z.Lu.reducerPath]:z.Lu.reducer,[x.d.reducerPath]:x.d.reducer,[D.$I.reducerPath]:D.$I.reducer,[B.pQ.reducerPath]:B.pQ.reducer,[_.OT.reducerPath]:_.OT.reducer,auth:m,user:p.Ay,userMenus:E,userPermissions:R.Ay,currentStudent:w.Ay,notificationsUnreadCount:I,teacher:O}),H=(0,n.rL)(Q,(e,t)=>"user/clearUserInfo"===t.type?(localStorage.removeItem("persist:root"),Y(void 0,t)):Y(e,t)),W=(0,a.U1)({reducer:H,middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE"]}}).concat(U.W4.middleware,u.Y.middleware,P.v.middleware,L.ZM.middleware,N.hh.middleware,M.ZZ.middleware,Z.O.middleware,F.ZN.middleware,j.rA.middleware,z.Lu.middleware,D.$I.middleware,B.pQ.middleware,x.d.middleware,_.OT.middleware),devTools:!1}),$=(0,n.GM)(W);(0,o.$k)(W.dispatch)},10407:(e,t,r)=>{r.d(t,{K:()=>d,v:()=>n});var s=r(70404),a=r(40694),o=r(69043);let n=(0,s.xP)({reducerPath:"notificationsApi",baseQuery:(0,a.cw)({baseUrl:"/api",prepareHeaders:(e,t)=>{var r;let{getState:s}=t,a=null===(r=s().user)||void 0===r?void 0:r.token;return a&&e.set("authorization","Bearer ".concat(a)),e}}),endpoints:e=>({getUnreadCount:e.query({query:()=>"/notifications/unreadCount",transformResponse:e=>(console.log("Unread count response:",e),200===e.code&&"number"==typeof e.data)?e.data:0,transformErrorResponse:async(e,t,r)=>{console.error("Error response:",e);let{status:s,data:a}=e;return 401===s&&await (0,o.B)(),0}})})}),{useGetUnreadCountQuery:d}=n},12519:(e,t,r)=>{r.d(t,{KN:()=>l,Xt:()=>g,YC:()=>m,YL:()=>u,ZZ:()=>s,Zx:()=>p,aP:()=>o,en:()=>i,hP:()=>c,lN:()=>f,qd:()=>n,r$:()=>d,uL:()=>a});let s=(0,r(29624).w)("institutionApi",["Institution","InstitutionAddress","InstitutionClassroom"]).injectEndpoints({endpoints:e=>({getInstitutionInfo:e.query({query:()=>({url:"/institutions/info",method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取机构信息失败")},providesTags:["Institution"]}),updateInstitutionInfo:e.mutation({query:e=>({url:"/institutions/info",method:"PUT",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"更新机构信息失败")},invalidatesTags:["Institution"]}),getInstitutionAddress:e.query({query:()=>({url:"/institutions/address",method:"GET"}),transformResponse:e=>200===e.code&&e.data?e.data:[],providesTags:["InstitutionAddress"]}),createInstitutionAddress:e.mutation({query:e=>({url:"/institutions/address",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"创建机构地址失败")},invalidatesTags:["InstitutionAddress"]}),updateInstitutionAddress:e.mutation({query:e=>{let{addressId:t,data:r}=e;return{url:"/institutions/address/".concat(t),method:"PUT",body:r}},transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"更新机构地址失败")},invalidatesTags:["InstitutionAddress"]}),deleteInstitutionAddress:e.mutation({query:e=>({url:"/institutions/address/".concat(e),method:"DELETE"}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"删除机构地址失败")},invalidatesTags:["InstitutionAddress"]}),getInstitutionClassrooms:e.query({query:e=>{let{search:t="",page:r=1,pageSize:s=10}=e;return{url:"/institutions/classrooms",method:"GET",params:{search:t,page:r,pageSize:s}}},transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["InstitutionClassroom"]}),getInstitutionClassroomOptions:e.query({query:()=>({url:"/institutions/classrooms/select",method:"GET"}),transformResponse:e=>200===e.code&&e.data?e.data:[],providesTags:["InstitutionClassroom"]}),createInstitutionClassroom:e.mutation({query:e=>({url:"/institutions/classrooms",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"创建机构教室失败")},invalidatesTags:["InstitutionClassroom"]}),updateInstitutionClassroom:e.mutation({query:e=>{let{classroomId:t,data:r}=e;return{url:"/institutions/classrooms/".concat(t),method:"PUT",body:r}},transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"更新机构教室失败")},invalidatesTags:["InstitutionClassroom"]}),deleteInstitutionClassroom:e.mutation({query:e=>({url:"/institutions/classrooms/".concat(e),method:"DELETE"}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"删除机构教室失败")},invalidatesTags:["InstitutionClassroom"]}),getInstitutionLogs:e.query({query:e=>({url:"/institutions/logs",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10}})})}),{useGetInstitutionInfoQuery:a,useUpdateInstitutionInfoMutation:o,useGetInstitutionAddressQuery:n,useCreateInstitutionAddressMutation:d,useUpdateInstitutionAddressMutation:u,useDeleteInstitutionAddressMutation:i,useGetInstitutionClassroomsQuery:l,useGetInstitutionClassroomOptionsQuery:c,useCreateInstitutionClassroomMutation:m,useUpdateInstitutionClassroomMutation:p,useDeleteInstitutionClassroomMutation:g,useGetInstitutionLogsQuery:f}=s},13515:(e,t,r)=>{r.d(t,{$I:()=>s,A$:()=>c,Ry:()=>p,T7:()=>n,US:()=>a,Wt:()=>m,dU:()=>d,iJ:()=>u,iz:()=>l,pX:()=>g,vV:()=>o,vx:()=>i});let s=(0,r(29624).w)("staffApi",["Staff","StaffRoles"]).injectEndpoints({endpoints:e=>({getStaff:e.query({query:e=>({url:"/staff",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Staff"]}),resetStaffPassword:e.mutation({query:e=>({url:"/staff/".concat(e,"/resetPassword"),method:"POST"}),invalidatesTags:["Staff"]}),updateStaffWorkingStatus:e.mutation({query:e=>({url:"/staff/".concat(e,"/workingStatus"),method:"PUT"}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"操作失败")},invalidatesTags:["Staff"]}),createStaff:e.mutation({query:e=>({url:"/staff",method:"POST",body:e}),invalidatesTags:["Staff"]}),getStaffRoleList:e.query({query:e=>({url:"/roles",method:"GET",params:e}),transformResponse:e=>(console.log(e,"11111111111111111111"),200===e.code&&e.data)?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["StaffRoles"]}),createStaffRole:e.mutation({query:e=>({url:"/roles",method:"POST",body:e}),invalidatesTags:["StaffRoles"]}),updateStaffRole:e.mutation({query:e=>{let{roleId:t,data:r}=e;return{url:"/roles/".concat(t),method:"PUT",body:r}},invalidatesTags:["StaffRoles"]}),deleteStaffRole:e.mutation({query:e=>({url:"/roles/".concat(e),method:"DELETE"}),invalidatesTags:["StaffRoles"]}),updateStaffInfo:e.mutation({query:e=>{let{staffId:t,data:r}=e;return{url:"/staff/".concat(t),method:"PUT",body:r}},invalidatesTags:["Staff"]}),updateStaffRolePermission:e.mutation({query:e=>{let{roleId:t,data:r}=e;return{url:"/roles/".concat(t,"/permissions"),method:"PUT",body:r}},invalidatesTags:["Staff"]}),uploadStaffAvatar:e.mutation({query:e=>({url:"/staff/avatar",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"上传头像失败")},invalidatesTags:["Staff"]})})}),{useGetStaffQuery:a,useResetStaffPasswordMutation:o,useUpdateStaffWorkingStatusMutation:n,useCreateStaffMutation:d,useGetStaffRoleListQuery:u,useCreateStaffRoleMutation:i,useUpdateStaffRoleMutation:l,useDeleteStaffRoleMutation:c,useUpdateStaffInfoMutation:m,useUpdateStaffRolePermissionMutation:p,useUploadStaffAvatarMutation:g}=s},27893:(e,t,r)=>{r.d(t,{BU:()=>q,D_:()=>u,E4:()=>i,EV:()=>f,GY:()=>c,Kp:()=>n,OU:()=>T,Qr:()=>d,Xd:()=>l,ZM:()=>s,Zj:()=>a,b:()=>y,e6:()=>o,eW:()=>g,eZ:()=>h,oW:()=>S,rM:()=>m,rr:()=>p});let s=(0,r(29624).w)("classesApi",["Classes","ClassSchedule","ClassesDetail"]).injectEndpoints({endpoints:e=>({createClasses:e.mutation({query:e=>({url:"/classes",method:"POST",body:e}),invalidatesTags:["Classes"]}),getClassesList:e.query({query:e=>{let{page:t=1,teacherId:r="",pageSize:s=10,name:a=""}=e;return{url:"/classes",method:"GET",params:{page:t,pageSize:s,name:a,teacherId:r}}},transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Classes"]}),getTeacherFreeTime:e.mutation({query:e=>({url:"/classes/check-teacher-free",method:"POST",body:e}),transformResponse:e=>200===e.code&&e.data?e.data:[]}),postAppendOrCopyClassesSchedule:e.mutation({query:e=>{let{classesId:t,data:r}=e;return{url:"/classes/append-or-copy-schedule/".concat(t),method:"POST",body:r}},transformResponse:e=>200===e.code&&e.data?e.data:[]}),getClassesSelect:e.query({query:e=>{let{page:t=1,pageSize:r=10,name:s=""}=e;return{url:"/classes/select",method:"GET",params:{page:t,pageSize:r,name:s}}},providesTags:["Classes"]}),getClassesByIdDetail:e.query({query:e=>({url:"/classes/".concat(e),method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取班级详情失败")},providesTags:["ClassesDetail"]}),addStudentToClass:e.mutation({query:e=>{let{classId:t,studentIds:r}=e;return{url:"/classes/".concat(t,"/students"),method:"POST",body:{studentIds:r}}},transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"添加学生到班级失败")},invalidatesTags:["ClassesDetail"]}),deleteStudentFromClass:e.mutation({query:e=>{let{classId:t,studentId:r}=e;return{url:"/classes/".concat(t,"/students/").concat(r),method:"DELETE"}},transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"从班级删除学生失败")},invalidatesTags:["ClassesDetail"]}),updateClasses:e.mutation({query:e=>{let{classesId:t,data:r}=e;return{url:"/classes/".concat(t),method:"PUT",body:r}},invalidatesTags:["ClassesDetail"]}),getClassesSchedule:e.query({query:e=>({url:"/classes/all/schedules",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取班级课表失败")},providesTags:["ClassSchedule"]}),getClassesSchedulesDetail:e.query({query:e=>({url:"/classes/schedules/".concat(e),method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取班级计划详情失败")},providesTags:["ClassSchedule"]}),updateClassesSchedules:e.mutation({query:e=>{let{scheduleId:t,data:r}=e;return{url:"/classes/schedules/".concat(t),method:"PUT",body:r}},transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"更新班级计划失败")},invalidatesTags:["ClassSchedule"]}),updateClassesStudentAttendance:e.mutation({query:e=>{let{id:t,data:r}=e;return{url:"/classes/schedules/".concat(t,"/studentAttendance"),method:"PUT",body:r}},transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"更新班级学生考勤失败")},invalidatesTags:["ClassSchedule"]}),addStudentToClassSchedule:e.mutation({query:e=>{let{scheduleId:t,data:r}=e;return{url:"/classes/schedules/".concat(t,"/students"),method:"POST",body:r}},transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"添加学生到班级计划失败")},invalidatesTags:["ClassSchedule"]}),deleteStudentFromClassSchedule:e.mutation({query:e=>{let{scheduleId:t,studentIds:r}=e;return{url:"/classes/schedules/".concat(t,"/students"),method:"DELETE",body:{studentIds:r}}},transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"从班级计划删除学生失败")},invalidatesTags:["ClassSchedule"]}),createClassesSchedules:e.mutation({query:e=>({url:"/classes/temp-schedule",method:"POST",body:e}),invalidatesTags:["ClassSchedule"]}),deleteClasses:e.mutation({query:e=>({url:"/classes/".concat(e),method:"delete"}),invalidatesTags:["Classes"]})})}),{useCreateClassesMutation:a,useGetClassesListQuery:o,useGetTeacherFreeTimeMutation:n,useGetClassesSelectQuery:d,useGetClassesByIdDetailQuery:u,useAddStudentToClassMutation:i,useDeleteStudentFromClassMutation:l,useUpdateClassesMutation:c,useGetClassesScheduleQuery:m,useGetClassesSchedulesDetailQuery:p,useUpdateClassesSchedulesMutation:g,usePostAppendOrCopyClassesScheduleMutation:f,useUpdateClassesStudentAttendanceMutation:h,useAddStudentToClassScheduleMutation:y,useDeleteStudentFromClassScheduleMutation:T,useDeleteClassesMutation:S,useCreateClassesSchedulesMutation:q}=s},29624:(e,t,r)=>{r.d(t,{w:()=>m});var s=r(40694),a=r(70404),o=r(69043),n=r(52991),d=r(7986),u=r(48436);let i={count:0,maxRetries:3,reset(){this.count=0},increment(){return this.count+=1,this.count},exceedsMaxRetries(){return this.count>=this.maxRetries}},l=(0,s.cw)({baseUrl:"".concat("http://127.0.0.1:3001/api/"),prepareHeaders:(e,t)=>{var r;let{getState:s}=t,a=null===(r=s().user)||void 0===r?void 0:r.token;return a&&e.set("authorization","Bearer ".concat(a)),e}}),c=async(e,t,r)=>{let s=await l(e,t,r);if(console.log(s,"result base api."),s.error){let a=s.error.status,c=s.error.data;if(401===a||(null==c?void 0:c.code)===401){console.log("Token 已过期，尝试刷新...");let a=i.increment();if(console.log("Token 错误计数: ".concat(a,"/").concat(i.maxRetries)),i.exceedsMaxRetries())return console.log("Token 错误次数超过最大限制，退出登录"),u.l.error("登录已过期","请重新登录"),d.M.dispatch((0,n.Hf)()),localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("refreshToken"),setTimeout(()=>{window.location.href="/login"},1500),s;try{await (0,o.B)()?(console.log("Token 刷新成功，重试请求"),i.reset(),s=await l(e,t,r)):(console.error("Token 刷新失败，无法获取新token"),u.l.error("登录已过期","请重新登录"),d.M.dispatch((0,n.Hf)()),setTimeout(()=>{window.location.href="/login"},1500))}catch(e){console.error("Token 刷新过程中发生错误:",e),u.l.error("登录已过期","请重新登录"),d.M.dispatch((0,n.Hf)()),setTimeout(()=>{window.location.href="/login"},1500)}}else i.reset()}else i.reset();return s},m=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return(0,a.xP)({reducerPath:e,keepUnusedDataFor:60,baseQuery:c,tagTypes:t,endpoints:()=>({})})}},32422:(e,t,r)=>{r.d(t,{Ay:()=>n,jb:()=>a});let s=(0,r(5710).Z0)({name:"userPermissions",initialState:{permissions:[]},reducers:{setPermissions:(e,t)=>{e.permissions=t.payload.permissions},clearPermissions:e=>{e.permissions=[]}}}),{setPermissions:a,clearPermissions:o}=s.actions,n=s.reducer},35035:(e,t,r)=>{r.d(t,{Ay:()=>o,ZZ:()=>a});let s=(0,r(5710).Z0)({name:"currentStudent",initialState:{studentId:""},reducers:{setCurrentStudent:(e,t)=>{e.studentId=t.payload.studentId}}}),{setCurrentStudent:a}=s.actions,o=s.reducer},48436:(e,t,r)=>{r.d(t,{l:()=>a});var s=r(56671);let a={success:(e,t)=>{(0,s.oR)(e,{description:t,icon:"✅"})},error:(e,t)=>{(0,s.oR)(e,{description:t,icon:"❌"})},info:(e,t)=>{(0,s.oR)(e,{description:t,icon:"ℹ️"})},warning:(e,t)=>{(0,s.oR)(e,{description:t,icon:"⚠️"})}}},52991:(e,t,r)=>{r.d(t,{Ay:()=>n,Hf:()=>o,iA:()=>a});let s=(0,r(5710).Z0)({name:"user",initialState:{id:"",name:"",account:"",avatar:void 0,institutionName:"",institutionLogo:"",institutionSubjectName:"",token:"",refresh_token:"",isAuthenticated:!1},reducers:{setUserInfo:(e,t)=>{e.id=t.payload.id,e.name=t.payload.name,e.account=t.payload.account,e.institutionName=t.payload.institutionName,e.institutionLogo=t.payload.institutionLogo,e.institutionSubjectName=t.payload.institutionSubjectName,e.token=t.payload.token,e.refresh_token=t.payload.refresh_token,e.isAuthenticated=!0},clearUserInfo:e=>{e.id="",e.name="",e.account="",e.institutionName="",e.institutionLogo="",e.institutionSubjectName="",e.token="",e.refresh_token="",e.isAuthenticated=!1}}}),{setUserInfo:a,clearUserInfo:o}=s.actions,n=s.reducer},57786:(e,t,r)=>{r.d(t,{D3:()=>a,Fj:()=>u,Gw:()=>n,OT:()=>s,_R:()=>d,eT:()=>i,gG:()=>o});let s=(0,r(29624).w)("notificationApi",["Notification"]).injectEndpoints({endpoints:e=>({getNotificationList:e.query({query:e=>({url:"/notifications",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:(console.log(e,"11111111111111111111"),{list:[],total:0,page:1,pageSize:10}),transformErrorResponse:(e,t,r)=>(console.log(e,t,r),e.message||"获取通知列表失败"),providesTags:["Notification"]}),getNotificationDetail:e.query({query:e=>"/notifications/".concat(e),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"获取通知详情失败")},providesTags:(e,t,r)=>[{type:"Notification",id:r}]}),createNotification:e.mutation({query:e=>({url:"/notifications",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"创建通知失败")},invalidatesTags:["Notification"]}),markAsRead:e.mutation({query:e=>({url:"/notifications/read",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"标记为已读失败")},invalidatesTags:["Notification"]}),markAllAsRead:e.mutation({query:()=>({url:"/notifications/readAll",method:"POST"}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"标记为全部已读失败")},invalidatesTags:["Notification"]}),deleteNotifications:e.mutation({query:e=>({url:"/notifications/delete",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"批量删除失败")},invalidatesTags:["Notification"]})})}),{useGetNotificationListQuery:a,useGetNotificationDetailQuery:o,useCreateNotificationMutation:n,useMarkAsReadMutation:d,useMarkAllAsReadMutation:u,useDeleteNotificationsMutation:i}=s},69043:(e,t,r)=>{r.d(t,{B:()=>c});var s=r(23464),a=r(7986),o=r(52991);let n=s.A.create({baseURL:"http://127.0.0.1:3001",timeout:1e4,headers:{"Content-Type":"application/json"}}),d=!1,u=[],i=e=>{u.forEach(t=>t(e)),u=[]},l=e=>{u.push(e)},c=async()=>{try{if(d)return new Promise(e=>{l(t=>{e(t)})});d=!0;let e=localStorage.getItem("refresh_token")||localStorage.getItem("refreshToken");if(!e)return console.error("刷新令牌不存在"),d=!1,null;let{accessToken:t,refreshToken:r}=(await n.post("/api/auth/refresh-token",null,{headers:{Authorization:"Bearer ".concat(e)}})).data;localStorage.setItem("token",t),localStorage.setItem("refresh_token",r),localStorage.setItem("refreshToken",r);let s=a.M.getState().user;return a.M.dispatch((0,o.iA)({...s,token:t,refresh_token:r,isAuthenticated:!0})),i(t),d=!1,t}catch(e){return console.error("刷新令牌失败:",e),d=!1,localStorage.removeItem("token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("refreshToken"),null}}},70639:(e,t,r)=>{r.d(t,{Kc:()=>a,Q$:()=>n,Qq:()=>i,RC:()=>l,ZN:()=>s,lY:()=>d,r3:()=>o,vM:()=>u});let s=(0,r(29624).w)("productsApi",["Products","ProductCourses"]).injectEndpoints({endpoints:e=>({getProducts:e.query({query:e=>({url:"/products",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Products"]}),getAllProducts:e.query({query:()=>({url:"/products/all",method:"GET"}),transformResponse:e=>200===e.code&&e.data?e.data:[],providesTags:["Products"]}),createProduct:e.mutation({query:e=>({url:"/products",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"创建产品失败")},invalidatesTags:["Products"]}),deleteProduct:e.mutation({query:e=>({url:"/products/".concat(e),method:"DELETE"}),transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"删除产品失败")},invalidatesTags:["Products"]}),updateProduct:e.mutation({query:e=>{let{id:t,product:r}=e;return{url:"/products/".concat(t),method:"PUT",body:r}},transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"更新产品失败")},invalidatesTags:["Products"]}),getProductsCoursesById:e.query({query:e=>({url:"/products/".concat(e,"/courses"),method:"GET"}),transformResponse:e=>200===e.code&&e.data?e.data:[],providesTags:["ProductCourses"]}),updateCoursesByProductId:e.mutation({query:e=>{let{productId:t,coursesData:r}=e;return{url:"/products/".concat(t,"/courses"),method:"PUT",body:r}},transformResponse:e=>{if(200===e.code)return e.data;throw Error(e.message||"更新产品课程失败")},invalidatesTags:["ProductCourses"]})})}),{useGetProductsQuery:a,useGetAllProductsQuery:o,useCreateProductMutation:n,useDeleteProductMutation:d,useUpdateProductMutation:u,useGetProductsCoursesByIdQuery:i,useUpdateCoursesByProductIdMutation:l}=s},71159:(e,t,r)=>{r.d(t,{Bv:()=>i,H4:()=>a,J8:()=>n,Jw:()=>m,N6:()=>c,Q_:()=>l,YK:()=>o,mA:()=>d,mk:()=>u,rA:()=>s});let s=(0,r(29624).w)("financeApi",["Finance"]).injectEndpoints({endpoints:e=>({getBuyingRecord:e.query({query:e=>({url:"/finance/buying-record",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Finance"]}),getTeachingRecord:e.query({query:e=>({url:"/finance/teaching-record",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Finance"]}),getSalesOverview:e.query({query:e=>({url:"/finance/sales-overview",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:null,providesTags:["Finance"]}),getSalesRepOverview:e.query({query:e=>({url:"/finance/salesrep-overview",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:null,providesTags:["Finance"]}),getStudentOverview:e.query({query:e=>({url:"/finance/student-overview",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:null,providesTags:["Finance"]}),getEducationOverview:e.query({query:e=>({url:"/finance/education-overview",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:null,providesTags:["Finance"]}),getStudentFees:e.query({query:e=>({url:"/finance/student-fees",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Finance"]}),getStudentClassSummary:e.query({query:e=>({url:"/finance/student-class-summary",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Finance"]}),getRefundRecord:e.query({query:e=>({url:"/finance/refund-records",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Finance"]})})}),{useGetBuyingRecordQuery:a,useGetTeachingRecordQuery:o,useGetSalesOverviewQuery:n,useGetSalesRepOverviewQuery:d,useGetStudentOverviewQuery:u,useGetEducationOverviewQuery:i,useGetStudentFeesQuery:l,useGetStudentClassSummaryQuery:c,useGetRefundRecordQuery:m}=s},74651:(e,t,r)=>{r.d(t,{AA:()=>n,VD:()=>u,hh:()=>s,p5:()=>a,v5:()=>d,zo:()=>o});let s=(0,r(29624).w)("coursesApi",["Courses"]).injectEndpoints({endpoints:e=>({getCourseList:e.query({query:e=>({url:"/courses",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Courses"]}),getCourseSelectList:e.query({query:()=>({url:"/courses/select",method:"GET"}),transformResponse:e=>200===e.code&&e.data?e.data:[],providesTags:["Courses"]}),createCourse:e.mutation({query:e=>({url:"/courses",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"创建课程失败")},invalidatesTags:["Courses"]}),deleteCourse:e.mutation({query:e=>({url:"/courses/".concat(e),method:"DELETE"}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"删除课程失败")},invalidatesTags:["Courses"]}),updateCourse:e.mutation({query:e=>{let{courseId:t,course:r}=e;return{url:"/courses/".concat(t),method:"PUT",body:r}},transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"更新课程失败")},invalidatesTags:["Courses"]})})}),{useGetCourseListQuery:a,useGetCourseSelectListQuery:o,useCreateCourseMutation:n,useDeleteCourseMutation:d,useUpdateCourseMutation:u}=s},75521:(e,t,r)=>{r.d(t,{DQ:()=>o,T6:()=>a,pQ:()=>s,wZ:()=>n});let s=(0,r(29624).w)("menusApi",["Menus"]).injectEndpoints({endpoints:e=>({getAllMenus:e.query({query:()=>({url:"/menus",method:"GET"}),transformResponse:e=>200===e.code?e.data:[],providesTags:["Menus"]}),getUserMenus:e.query({query:()=>({url:"/menus/user-menus",method:"GET"}),transformResponse:e=>200===e.code?e.data:[],providesTags:["Menus"]}),getRoleMenus:e.query({query:e=>({url:"/menus/role-menus/".concat(e),method:"GET"}),transformResponse:e=>200===e.code?e.data:[],providesTags:["Menus"]})})}),{useGetAllMenusQuery:a,useGetUserMenusQuery:o,useGetRoleMenusQuery:n}=s},78504:(e,t,r)=>{r.d(t,{O:()=>s,d:()=>a});let s=(0,r(29624).w)("publicApi",["Public"]).injectEndpoints({endpoints:e=>({uploadImage:e.mutation({query:e=>({url:"/public/upload/image",method:"POST",body:e}),transformResponse:e=>{if(200===e.code)return{message:e.message,data:e.data};throw Error(e.message||"上传图片失败")}})})}),{useUploadImageMutation:a}=s},95728:(e,t,r)=>{r.d(t,{Er:()=>A,F3:()=>w,FQ:()=>a,F_:()=>S,GZ:()=>u,I5:()=>E,Iq:()=>h,JV:()=>p,Jb:()=>P,L5:()=>i,Mk:()=>y,Nq:()=>l,PO:()=>C,RC:()=>q,W4:()=>s,dx:()=>g,eq:()=>o,mo:()=>d,nu:()=>n,og:()=>f,qc:()=>m,sq:()=>R,zJ:()=>c,zy:()=>v});let s=(0,r(29624).w)("studentApi",["Student","StudentProduct","FollowRecord","Attendance"]).injectEndpoints({endpoints:e=>({getStudentList:e.query({query:e=>({url:"/students",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Student"]}),deleteStudents:e.mutation({query:e=>({url:"/students",method:"DELETE",body:{studentIds:e}}),invalidatesTags:["Student"]}),addStudentFollower:e.mutation({query:e=>({url:"/students/follow-up",method:"POST",body:e}),invalidatesTags:["Student"]}),createStudent:e.mutation({query:e=>({url:"/students/add",method:"POST",body:e}),invalidatesTags:["Student"]}),createProductForStudent:e.mutation({query:e=>{let{studentId:t,productData:r}=e;return{url:"/students/".concat(t,"/products"),method:"POST",body:r}},invalidatesTags:["StudentProduct"]}),getStudentSelectList:e.query({query:e=>{let{page:t=1,pageSize:r=10,search:s=""}=e;return{url:"/students/simple",method:"GET",params:{page:t,pageSize:r,search:s}}},transformResponse:e=>200===e.code&&e.data?e.data:{list:[],total:0,page:1,pageSize:10},providesTags:["Student"]}),getStudentInfo:e.query({query:e=>({url:"/students/".concat(e),method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员信息失败")},providesTags:(e,t,r)=>[{type:"Student",id:r}]}),getStudentProducts:e.query({query:e=>({url:"/students/".concat(e,"/products"),method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["StudentProduct"]}),getStudentProductPurchaseRecord:e.query({query:e=>({url:"/students/".concat(e,"/products/records"),method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["StudentProduct"]}),getStudentAttendanceRecord:e.query({query:e=>{let{studentId:t,...r}=e;return{url:"/students/".concat(t,"/attendance"),method:"GET",params:r}},transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["Attendance"]}),getStudentClasses:e.query({query:e=>({url:"/students/".concat(e,"/classes"),method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["Student"]}),getStudentProduct:e.query({query:e=>({url:"/students/products",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["StudentProduct"]}),getStudentProductDownload:e.query({query:e=>({url:"/students/products/download",method:"GET",params:e})}),adjustStudentProduct:e.mutation({query:e=>{let{studentId:t,studentProductId:r,data:s}=e;return{url:"/students/".concat(t,"/products/").concat(r),method:"PUT",body:s}},invalidatesTags:["StudentProduct"]}),getIntentionStudent:e.query({query:e=>({url:"/students/intent",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["Student"]}),addStudentFollowRecord:e.mutation({query:e=>{let{studentId:t,data:r}=e;return{url:"/students/".concat(t,"/followRecords"),method:"POST",body:r}},invalidatesTags:["FollowRecord"]}),getStudentFollowRecord:e.query({query:e=>({url:"/students/".concat(e,"/followRecords"),method:"GET"}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["FollowRecord"]}),updateStudentInfo:e.mutation({query:e=>{let{studentId:t,data:r}=e;return{url:"/students/".concat(t),method:"PUT",body:r}},invalidatesTags:["Student"]}),getStudentClassRecord:e.query({query:e=>{let{studentId:t,...r}=e;return{url:"/students/".concat(t,"/classesHistory"),method:"GET",params:r}},transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["Student"]}),getAttendanceRecord:e.query({query:e=>({url:"/students/attendanceRecords",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["Attendance"]}),getFollowRecords:e.query({query:e=>({url:"/students/followRecords",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["FollowRecord"]}),getProductAdjustRecord:e.query({query:e=>({url:"/students/products-adjustments",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["StudentProduct"]}),refundStudentProduct:e.mutation({query:e=>{let{studentId:t,studentProductId:r,data:s}=e;return{url:"/students/".concat(t,"/products/").concat(r,"/refund"),method:"POST",body:s}},invalidatesTags:["StudentProduct"]}),getStudentClassRecordQuery:e.query({query:e=>({url:"/students/classes-query",method:"GET",params:e}),transformResponse:e=>{if(200===e.code&&e.data)return e.data;throw Error(e.message||"获取学员产品失败")},providesTags:["Student"]})})}),{useGetStudentListQuery:a,useDeleteStudentsMutation:o,useAddStudentFollowerMutation:n,useCreateStudentMutation:d,useCreateProductForStudentMutation:u,useGetStudentSelectListQuery:i,useGetStudentInfoQuery:l,useGetStudentProductsQuery:c,useGetStudentProductPurchaseRecordQuery:m,useGetStudentAttendanceRecordQuery:p,useGetStudentClassesQuery:g,useGetStudentProductQuery:f,useGetStudentProductDownloadQuery:h,useAdjustStudentProductMutation:y,useGetIntentionStudentQuery:T,useAddStudentFollowRecordMutation:S,useGetStudentFollowRecordQuery:q,useUpdateStudentInfoMutation:v,useGetStudentClassRecordQuery:E,useGetAttendanceRecordQuery:R,useGetFollowRecordsQuery:w,useGetProductAdjustRecordQuery:P,useRefundStudentProductMutation:C,useGetStudentClassRecordQueryQuery:A}=s},98553:(e,t,r)=>{r.d(t,{H:()=>a,d:()=>s});let s=(0,r(29624).w)("usersApi",["Users"]).injectEndpoints({endpoints:e=>({getCurrentPermissions:e.query({query:e=>({url:"/user/current-permissions",method:"GET",params:e}),transformResponse:e=>200===e.code&&e.data?e.data.permissionCodes:[],providesTags:["Users"]})})}),{useGetCurrentPermissionsQuery:a}=s}}]);